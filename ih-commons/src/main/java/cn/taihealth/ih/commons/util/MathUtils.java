package cn.taihealth.ih.commons.util;

import java.text.DecimalFormat;

/**
 * @Author: Moon
 * @Date: 2021/3/15 下午4:53
 */
public class MathUtils {


    /**
     * (a-b) / b 结果百分比 b为0时返回100%
     * @param a 被除数
     * @param b 除数
     * @return 结果
     */
    public static String subtractAndDivision(long a, long b) {
        if (b == 0) {
            return a == 0 ? "0%" : "100%";
        }
        float num = (float) (a - b) / b;
        if (num == 0.0) {
            return "0%";
        }
        DecimalFormat df = new DecimalFormat("0.0");
        return df.format(num * 100) + "%";

    }

    /**
     * (a-b) / b 结果百分比 b为0时返回100%
     * @param a 被除数
     * @param b 除数
     * @return 结果
     */
    public static String subtractAndDivision(String a, String b) {
        if ("0.0".equals(b)) {
            return "0.0".equals(a) ? "0%" : "100%";
        }
        float a1 = Float.parseFloat(a);
        float b1 = Float.parseFloat(b);
        float num = (a1 - b1) / b1;
        if (num == 0.0) {
            return "0%";
        }
        DecimalFormat df = new DecimalFormat("0.0");
        return df.format(num * 100) + "%";
    }

    /**
     * a / b 保留1位小数
     * @param a 被除数
     * @param b 除数
     * @return 结果
     */
    public static float division(int a, int b) {
        DecimalFormat df = new DecimalFormat("0.0");
        float result = ((float) a / b);
        return Float.parseFloat(df.format(result));
    }

    /**
     * a / b 保留2位小数
     * @param a 被除数
     * @param b 除数
     * @return 结果
     */
    public static String division2(int a, int b) {
        DecimalFormat df = new DecimalFormat("0.00");
        float result = ((float) a / b);
        return df.format(result);
    }

    /**
     * a / b 保留2位小数
     * @param a 被除数
     * @param b 除数
     * @return 结果
     */
    public static String division2(long a, long b) {
        DecimalFormat df = new DecimalFormat("0.00");
        float result = ((float) a / b);
        return df.format(result);
    }


    /**
     * (a-b) / b 结果百分比 b为0时返回100%
     * @param a 被除数
     * @param b 除数
     * @return 结果
     */
    public static String subtractAndDivisionWithSign(int a, int b) {
        if (b == 0) {
            return a == 0 ? "0.00" : "100.00";
        }
        float num = (float) (a - b) / b;
        if (num == 0.0) {
            return "0.00";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(num * 100.00);
    }

    public static String subtractAndDivisionWithSign(float a, float b) {
        if (b == 0) {
            return a == 0 ? "0.00" : "100.00";
        }
        float num = (a - b) / b;
        if (num == 0.0) {
            return "0.00";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(num * 100.00);
    }


    /**
     * a / b 结果百分比
     * @param a 被除数
     * @param b 除数
     * @return 结果
     */
    public static String divisionWithSign(int a, int b) {
        if (b == 0) {
            return a == 0 ? "0.00" : "100.00";
        }
        float num = (float) a / b;
        if (num == 0.0) {
            return "0.00";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(num * 100.00);
    }

    /**
     * 把传入的数字转换为百分比
     * @param value
     * @return
     */
    public static String getRateByDouble(Double value) {
        if (value == null) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return Float.parseFloat(df.format(value * 100)) + "%";
    }

    public static String getDoubleString(Double d) {
        if (d == null) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return ""  + Float.parseFloat(df.format(d));
    }

    public static String getDoubleIntString(Double d) {
        if (d == null) {
            return "0";
        }
        return ""  + d.intValue();
    }
    public static double doubleDivision(int a, int b) {
        if (b == 0) {
            return a == 0 ? 0.0 : 1.0;
        }
        double result = (double) a / b;
        return Math.round(result * 10) / 10.0;
    }

    /**
     * 将分转换成元
     */
    public static String formatAmount(long amountInCent) {
        double amountInYuan = (double) amountInCent / 100;
        return String.format("%.2f", amountInYuan);
    }
}
