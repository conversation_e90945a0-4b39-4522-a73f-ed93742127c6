package cn.taihealth.ih.commons.util;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.error.ErrorType;
import com.gitq.jedi.common.datatype.DataTypes;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Queues;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import reactor.util.function.Tuple2;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TimeUtils {

    @Getter
    public enum Shift {
        UNKNOWN("未知", "-1"),
        MORNING("上午", "0"),
        AFTERNOON("下午", "1"),
        NIGHT("晚上", "2"),
        ALL_DAY("全天", "3"),
        DAYTIME("白天", "4"),
        LATE_NIGHT("后夜", "5"),
        MIDNIGHT("夜间", "6"),
        ;

        private final String name;

        private final String code;

        Shift(String name, String code) {
            this.name = name;
            this.code = code;
        }

        public static Shift getByCode(String code) {
            for (Shift shift : Shift.values()) {
                if (shift.getCode().equals(code)) {
                    return shift;
                }
            }
            return UNKNOWN;
        }

    }

    private static final String[] DATE_FORMATS = new String[] {
            "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ssX", "yyyy-MM-dd'T'HH:mm:ssXX", "yyyy-MM-dd'T'HH:mm:ssZZ",
            "yyyy-MM-dd HH:mm:ss.SSS", "yyyy-MM-dd'T'HH:mm:ss.SSSX", "yyyy-MM-dd'T'HH:mm:ss.SSSXX", "yyyy-MM-dd'T'HH:mm:ss.SSSZZ",
            "yyyy-MM-dd", "yyyy-MM-ddX", "yyyy-MM-ddXX", "yyyy-MM-ddZZ", "yyyyMMddX", "yyyyMMddXX", "yyyyMMddZZ",
            "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd'T'HH:mm:ssX", "yyyy/MM/dd'T'HH:mm:ssXX", "yyyy/MM/dd'T'HH:mm:ssZZ",
            "yyyy/MM/dd HH:mm:ss.SSS", "yyyy/MM/dd'T'HH:mm:ss.SSSX", "yyyy/MM/dd'T'HH:mm:ss.SSSXX", "yyyy/MM/dd'T'HH:mm:ss.SSSZZ",
            "yyyy/MM/dd", "yyyy/MM/ddX", "yyyy/MM/ddXX", "yyyy/MM/ddZZ", "yyyyMMddHHmmss", "yyyy-MM", "yyyy", "yyyyMMdd", "yyyyMMddHHmm",
            "yyyy-MM-dd hh:mmaa", "yyyy-MM-dd hh:mm:ssaa", "MM dd yyyy hh:mmaa"};
    private static final String TIME_STAMP_FORMAT = "^\\d+$";

    private static final String formatter1 = "yyyyMMddHHmmss";
    private static final String formatter2 = "yyyy-MM-dd HH:mm:ss";

    private static final ShiftTime[] TIMES = new ShiftTime[]{
        new ShiftTime(0, 12 * 3600000, Shift.MORNING),
        new ShiftTime(12 * 3600000, 17 * 3600000, Shift.AFTERNOON),
        new ShiftTime(17 * 3600000, 24 * 3600000, Shift.NIGHT)
    };

    @Getter
    public enum TimeUnit {
        DAY(86400000),
        HOUR(3600000),
        MINUTE(60000),
        SECOND(1000);

        private final long millisecond;

        TimeUnit(long millisecond) {
            this.millisecond = millisecond;
        }

    }

    private TimeUtils() {
    }

    /**
     * 格式化时间 yyyy-MM-dd HH:mm:ss
     *
     * @param date
     * @return
     */
    public static String dateToString(Date date) {
        return DataTypes.DATE.asString(date, formatter2, Locale.getDefault());
    }

    public static String dateToString(Date date, String format) {
        return DataTypes.DATE.asString(date, format, Locale.getDefault());
    }

    /**
     * 计算年龄 (年)
     * @param birthDay
     * @return
     */
    public static int age(Date birthDay) {
        LocalDateTime beforeLT = date2LocalDateTime(birthDay);
        LocalDateTime afterLT = getNowLocalDateTime();
        return age(beforeLT, afterLT);
    }

    /**
     * 时间戳转换成时间
     * @param time
     * @return
     */
    public static String timeToString(Long time) {
        DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return ftf
            .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault()));
    }

    /**
     * 计算年龄 (年)
     * @param birthDay
     * @return
     */
    public static int age(LocalDateTime birthDay) {
        LocalDateTime afterLT = getNowLocalDateTime();
        return age(birthDay, afterLT);
    }

    /**
     * 计算年龄 (年)
     * @param beforeDate
     * @param afterDate
     * @return
     */
    public static int age(Date beforeDate, Date afterDate) {
        LocalDateTime beforeLT = date2LocalDateTime(beforeDate);
        LocalDateTime afterLT = date2LocalDateTime(afterDate);
        return age(beforeLT, afterLT);
    }

    /**
     * 计算年龄 (年)
     * @param beforeDate
     * @param afterDate
     * @return
     */
    public static int age(LocalDateTime beforeDate, LocalDateTime afterDate) {
        if (beforeDate.isAfter(afterDate)) {
            return age(afterDate, beforeDate);
        }

        int age = afterDate.getYear() - beforeDate.getYear() - 1;
        Queue<Integer> before = Queues.newLinkedBlockingQueue();
        before.add(beforeDate.getMonthValue());
        before.add(beforeDate.getDayOfMonth());
        before.add(beforeDate.getHour());
        before.add(beforeDate.getMinute());
        before.add(beforeDate.getSecond());

        Queue<Integer> after = Queues.newLinkedBlockingQueue();
        after.add(afterDate.getMonthValue());
        after.add(afterDate.getDayOfMonth());
        after.add(afterDate.getHour());
        after.add(afterDate.getMinute());
        after.add(afterDate.getSecond());

        age += one(before, after);
        return age;
    }

    /**
     * 计算时间差
     * @param beforeDate
     * @param afterDate
     * @param unit
     * @return
     */
    public static int intervalTime(Date beforeDate, Date afterDate, TimeUnit unit) {
        if (beforeDate.compareTo(afterDate) > 0) {
            return intervalTime(afterDate, beforeDate, unit);
        }

        return (int) ((afterDate.getTime() - beforeDate.getTime()) / unit.getMillisecond());
    }

    private static int one(Queue<Integer> before, Queue<Integer> after) {
        if (before.size() != after.size()) {
            return 0;
        }
        if (before.isEmpty()) {
            return 1;
        }
        int a = after.poll();
        int b = before.poll();
        if (a > b) {
            return 1;
        } else if (a == b) {
            return one(before, after);
        } else {
            return 0;
        }
    }

    public static LocalDateTime date2LocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault())
            .toLocalDateTime();
    }

    public static LocalDate date2LocalDate(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault())
            .toLocalDate();
    }

    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime getNowLocalDateTime() {
        return LocalDateTime.now();
    }


    /**
     * 当前时间，增加几天
     * @return
     */
    public static LocalDateTime getAddDay(int day) {
        LocalDateTime startTime = LocalDateTime.now().plusDays(day);
        return startTime;

    }


    /**
     * 计算班次
     * @return shift
     */
    public static Shift getShift(Date startTime, Date endTime) {
        Shift start = Shift.ALL_DAY;
        Shift end = Shift.ALL_DAY;
        Date date = DateUtils.truncate(startTime, Calendar.DAY_OF_MONTH);

        int[] s = new int[2];
        long startL = startTime.getTime() - date.getTime();
        long endL = endTime.getTime() - date.getTime();
        if (startL >= TIMES[TIMES.length - 1].getEndTime()) {
//            start = Shift.ALL_DAY;
            s[0] = 4;
        } else {
            for (int i = 0; i < TIMES.length; i++) {
                if (startL < TIMES[i].getEndTime()) {
                    start = TIMES[i].getShift();
                    s[0] = i;
                    break;
                }
            }
        }

        if (endL > TIMES[TIMES.length - 1].getEndTime()) {
//            end = Shift.ALL_DAY;
            s[1] = 4;
        } else {
            for (int i = 0; i < TIMES.length; i++) {
                if (endL <= TIMES[i].getEndTime()) {
                    end = TIMES[i].getShift();
                    s[1] = i;
                    break;
                }
            }
        }
        if (start == end && s[0] == s[1]) {
            return start;
        } else {
            return Shift.ALL_DAY;
        }
    }

    /**
     * 计算班次 跨时间段 ALL_DAY
     *
     * @return
     */
    public static Shift getShift(String start, String end) {
        Date startTime = (Date) DataTypes.DATE.fromString(start, "HH:mm");
        Date endTime = (Date) DataTypes.DATE.fromString(end, "HH:mm");
        if (startTime == null || endTime == null) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("时间格式不正确");
        }
        return getShift(startTime, endTime);
    }

    /**
     * 将HH:MM 赋值给date
     * @param date
     * @param time
     * @return
     */
    public static Date combineDateAndTime(Date date, String time) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalTime localTime = LocalTime.parse(time);
        LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 判断两个时间段是否有交集, e1 > s2 && s1 < e2
     *
     * @param s1  第一段事件的开始时间
     * @param e1  第一段事件的结束时间
     * @param ses 第二段事件的开始和结束时间
     * @return
     */
    public static boolean timeIsIntersection(Date s1, Date e1, List<Tuple2<Date, Date>> ses) {
        for (Tuple2<Date, Date> se : ses) {
            if (e1.getTime() > se.getT1().getTime() && s1.getTime() < se.getT2().getTime()) {
                return true;
            }
        }
        return false;
    }

    public static boolean timeIsIntersection(Date s1, Date e1, Date s2, Date e2) {
        return e1.getTime() > s2.getTime() && s1.getTime() < e2.getTime();
    }

    /**
     * 判断当前时间是否在开始和结束时间所在天范围内
     * start = 2020-12-12 08:00:00, end = 2020-12-12 09:00:00,
     * 2020-12-12 02:00:00 = true,
     * 2020-12-12 18:00:00 = true
     * @param start
     * @param end
     * @return
     */
    public static boolean isInDays(Date start, Date end) {
        long startTime = DateUtils.truncate(start, Calendar.DAY_OF_MONTH).getTime();
        long endTime = DateUtils.truncate(end, Calendar.DAY_OF_MONTH).getTime() + Constants.ONE_DAY_MILLIONS;
        long now = System.currentTimeMillis();
        return now >= startTime && now <= endTime;
    }

    public static List<String> getDays(Date start, Date end) {
        List<String> days = Lists.newArrayList();
        long dayTime = start.getTime();
        long endTime = end.getTime();
        while (dayTime < endTime) {
            days.add(DataTypes.DATE.asString(new Date(dayTime), "yyyy-MM-dd"));
            dayTime += Constants.ONE_DAY_MILLIONS;
        }
        return days;
    }

    /**
     * 获取周几
     * @param date
     * @return
     */
    public static String getWeek(Date date) {
        String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    /**
     * 获取 2014-07-21 周一 上午 10:00-11:00 格式时间
     * @param startTime
     * @param endTime
     * @return string
     */
    public static String getAppointmentTime(Date startTime, Date endTime) {
        String s = DataTypes.DATE.asString(startTime, "HH:mm");
        String e = DataTypes.DATE.asString(endTime, "HH:mm");
        String ymd = DataTypes.DATE.asString(startTime, "yyyy-MM-dd");
        String week = TimeUtils.getWeek(startTime);
        String shift = TimeUtils.getShift(startTime, endTime).getName();
        return ymd + " " + week + " " + shift + " " + s + "~" + e;
    }

    /**
     * 获取 2014-07-21 周一 上午 10:00 格式时间
     * @param startTime
     * @return string
     */
    public static String getAppointmentTime(Date startTime) {
        String s = DataTypes.DATE.asString(startTime, "HH:mm");
        String ymd = DataTypes.DATE.asString(startTime, "yyyy-MM-dd");
        String week = TimeUtils.getWeek(startTime);
        String shift = TimeUtils.getShift(startTime, startTime).getName();
        return ymd + " " + week + " " + shift + " " + s;
    }

    /**
     * 获取 2024-05-27 09:00~10:00
     * @param startTime
     * @param endTime
     * @return string
     */
    public static String getAppointmentTimeSlot(Date startTime, Date endTime) {
        String s = DataTypes.DATE.asString(startTime, "HH:mm");
        String e = DataTypes.DATE.asString(endTime, "HH:mm");
        String ymd = DataTypes.DATE.asString(startTime, "yyyy-MM-dd");
        return ymd + " " + s + "~" + e;
    }

    /**
     * 获取 2024年06月13日 09:00~10:00
     * @param startTime
     * @param endTime
     * @return
     */
    public static String getAppointmentTimeSlotTemplate(Date startTime, Date endTime) {
        String s = DataTypes.DATE.asString(startTime, "HH:mm");
        String e = DataTypes.DATE.asString(endTime, "HH:mm");
        String ymd = DataTypes.DATE.asString(startTime, "yyyy年MM月dd日");
        return ymd + " " + s + "~" + e;
    }

    /**
     * 格式转化
     * @param input  202411180000-202411182359
     * @return 2024-11-18 00:00~23:59
     */
    public static String getAppointmentTime(String input) {
        if (null == input) return null;
        // 定义输入和输出的时间格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

        // 解析起始和结束时间
        LocalDateTime startTime = LocalDateTime.parse(input.substring(0, 12), inputFormatter);
        LocalDateTime endTime = LocalDateTime.parse(input.substring(13), inputFormatter);

        // 格式化日期和时间范围
        String formattedDate = startTime.format(dateFormatter);
        String formattedTimeRange = startTime.format(timeFormatter) + "~" + endTime.format(timeFormatter);
        return formattedDate + " " + formattedTimeRange;
    }

    // 获得某天最大时间 2020-02-19 xx to 2020-02-20 00:00:00
    public static Date getEndOfDay(Date date) {
        return DateUtils.addDays(getStartOfDay(date), 1);
    }

    // 获得某天最小时间 2020-02-17 00:00:00
    public static Date getStartOfDay(Date date) {
        return DateUtils.truncate(date, Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取某天所在周的最大时间
     * @param date
     * @return
     */
    public static Date getEndOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int w = calendar.get(Calendar.DAY_OF_WEEK);
        if (w != 1) {
            calendar.setTime(new Date(date.getTime() + 604800000));
        }
        calendar.set(Calendar.DAY_OF_WEEK, 1);
        return getEndOfDay(calendar.getTime());
    }

    /**
     * 获取某天所在周的最小时间
     * @param date
     * @return
     */
    public static Date getStartOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int w = calendar.get(Calendar.DAY_OF_WEEK);
        if (w == 1) {
            calendar.setTime(new Date(date.getTime() - 604800000));
        }
        calendar.set(Calendar.DAY_OF_WEEK, 2);
        return getStartOfDay(calendar.getTime());
    }


    /**
     * 获取某天所在月的最大时间
     * @param date
     * @return
     */
    public static Date getEndOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
        return getStartOfDay(calendar.getTime());
    }

    /**
     * 获取某天所在月的最小时间
     * @param date
     * @return
     */
    public static Date getStartOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return getStartOfDay(calendar.getTime());
    }


    /**
     * 获取某天所在年的最大时间
     * @param date
     * @return
     */
    public static Date getEndOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + 1);
        return getStartOfDay(calendar.getTime());
    }

    /**
     * 获取某天所在年的最小时间
     * @param date
     * @return
     */
    public static Date getStartOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        return getStartOfDay(calendar.getTime());
    }

    public static int dateDiff(LocalDateTime dt1, LocalDateTime dt2) {
        long t1 = dt1.toEpochSecond(ZoneOffset.ofHours(0));
        long day1 = t1 / (60 * 60 * 24);
        long t2 = dt2.toEpochSecond(ZoneOffset.ofHours(0));
        long day2 = t2 / (60 * 60 * 24);
        return (int) (day2 - day1);
    }

    /**
     * 获取当前时间所在一周/月/年/过去一年(截止到今天23：59：59)的时间范围内所有时间点
     * @param dateUnit week/month/year/oldYear
     * @return dateList
     */
    public static List<Date> getDateListByDateUnit(String dateUnit) {
        List<Date> param = Lists.newArrayList();
        LocalDateTime endOfDay = LocalDateTime.now().with(LocalTime.MAX);
        Date endTime = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        Date startTime = getStartDateByDateUnit(dateUnit);
        switch (dateUnit) {
            case "week":
            case "month":
                while (startTime.getTime() < endTime.getTime()) {
                    param.add(startTime);
                    startTime = DateUtils.addDays(startTime, 1);
                }
                break;
            case "year":
            case "oldYear":
                while (startTime.getTime() < endTime.getTime()) {
                    param.add(startTime);
                    startTime = DateUtils.addMonths(startTime, 1);
                }
                break;
            default:
                throw new IllegalArgumentException("Unknown date");
        }
        return param;
    }

    /**
     * 获取当前时间所在一周/月/年/过去一年(截止到今天23：59：59)的时间范围内所有时间点
     * @param dateUnit week/month/year/oldYear
     * @return dateList
     */
    public static Date getStartDateByDateUnit(String dateUnit) {
        LocalDate date = LocalDate.now();

        Date startTime;
        switch (dateUnit) {
            case "week":
                LocalDateTime monday = LocalDateTime.of(date, LocalTime.MIN).with(DayOfWeek.MONDAY);
                startTime = Date.from(monday.atZone(ZoneId.systemDefault()).toInstant());
                break;
            case "month":
                startTime = DateUtils.truncate(new Date(), Calendar.MONTH);
                break;
            case "year":
                startTime = DateUtils.truncate(new Date(), Calendar.YEAR);
                break;
            case "oldYear":
                startTime = DateUtils.addMonths(DateUtils.truncate(new Date(), Calendar.MONTH), -12);
                break;
            case "tillNow":
                startTime = null;
                break;
            default:
                throw new IllegalArgumentException("Unknown date");
        }
        return startTime;
    }

    /**
     * 获取给定时间范围内的所有日期 （日/月）
     * @param startTime 时间1
     * @param endTime 时间2
     * @return 时间字符串集合
     */
    public static List<Date> getDateListCustom(Date startTime, Date endTime) {
        List<Date> param = Lists.newArrayList();
        if (DateUtils.addDays(startTime, 31).getTime() > endTime.getTime()) {
            while (startTime.getTime() < endTime.getTime()) {
                param.add(startTime);
                startTime = DateUtils.addDays(startTime, 1);
            }
        } else {
            Date truncate = DateUtils.truncate(startTime, Calendar.MONTH);
            while (truncate.getTime() < endTime.getTime()) {
                param.add(truncate);
                truncate = DateUtils.addMonths(truncate, 1);
            }
        }
        return param;
    }

    public static Date getTomorrowDate(LocalDate date) {
        ZonedDateTime zonedDateTime = date.atStartOfDay(ZoneId.systemDefault());
        Instant instant = zonedDateTime.toInstant();
        Date from = Date.from(instant);
        return DateUtils.addDays(from, 1);
    }

    public static Date getYesterdayDate(LocalDate date) {
        ZonedDateTime zonedDateTime = date.atStartOfDay(ZoneId.systemDefault());
        Instant instant = zonedDateTime.toInstant();
        Date from = Date.from(instant);
        return DateUtils.addDays(from, -1);
    }

    public static List<LocalDate> getDatesBetween(Date startDate, Date endDate) {
        LocalDate start = new java.sql.Date(startDate.getTime()).toLocalDate();
        LocalDate end = new java.sql.Date(endDate.getTime()).toLocalDate();
        long days = ChronoUnit.DAYS.between(start, end);
        List<LocalDate> dates = Lists.newArrayList();
        for (int i = 0; i <= days; i++) {
            dates.add(start.plusDays(i));
        }
        dates.remove(LocalDate.now());
        return dates;
    }

    public static Date getStartDate(Date date, String dateUnit) {
        if (date == null || StringUtils.isBlank(dateUnit)) {
            return date;
        }
        LocalDateTime dateTime = date2LocalDateTime(date);
        LocalDateTime startDate;

        switch (dateUnit) {
            case "day":
                startDate = dateTime.with(LocalTime.MIN);
                break;
            case "week":
                startDate = dateTime.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).with(LocalTime.MIN);
                break;
            case "month":
                startDate = dateTime.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
                break;
            default:
                throw new IllegalArgumentException("Unknown date unit");
        }
        return localDateTime2Date(startDate);
    }

    public static Date getEndDate(Date date, String dateUnit) {

        if (date == null || StringUtils.isBlank(dateUnit)) {
            return date;
        }
        LocalDateTime dateTime = date2LocalDateTime(date);
        LocalDateTime endDate;

        switch (dateUnit) {
            case "day":
                endDate = dateTime.with(LocalTime.MAX);
                break;
            case "week":
                endDate = dateTime.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
                    .with(LocalTime.MAX);
                break;
            case "month":
                endDate = dateTime.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
                break;
            default:
                throw new IllegalArgumentException("Unknown date unit");
        }
        return localDateTime2Date(endDate);
    }

    public static Date getFirstDayOfLastYear() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date getLastDayOfLastYear() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        calendar.set(Calendar.MONTH, 11);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        return calendar.getTime();
    }

    public static Map<String, Date> getDateByTime(String startTime, String endTime, String dateUnit){
        Date startTimeDate;
        Date endTimeDate;
        if ("custom".equals(dateUnit)) {
            if (startTime == null || endTime == null) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("自定义时间必填");
            } else {
                startTimeDate = (Date) DataTypes.DATE.fromString(startTime, "yyyy-MM-dd");
                endTimeDate = (Date) DataTypes.DATE.fromString(endTime, "yyyy-MM-dd");
                endTimeDate = DateUtils.addDays(endTimeDate, 1);
            }
        } else if ("lastYear".equals(dateUnit)) {
            //上一年的第一天
            startTimeDate = getFirstDayOfLastYear();
            //上一年的最后一天
            endTimeDate = getLastDayOfLastYear();
        } else {
            startTimeDate = TimeUtils.getStartDateByDateUnit(dateUnit);
            endTimeDate = TimeUtils.getEndOfDay(new Date());
        }
        Map<String, Date> dateMap = Maps.newHashMap();
        dateMap.put("startTimeDate", startTimeDate);
        dateMap.put("endTimeDate", endTimeDate);
        return dateMap;
    }

    /**
     * 将字符串转换成日期
     * @param dateTimeStr 支持的格式: 1689159935091, "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ssX", "yyyy-MM-dd'T'HH:mm:ssXX", "yyyy-MM-dd'T'HH:mm:ssZZ",
     *         "yyyy-MM-dd HH:mm:ss.SSS", "yyyy-MM-dd'T'HH:mm:ss.SSSX", "yyyy-MM-dd'T'HH:mm:ss.SSSXX", "yyyy-MM-dd'T'HH:mm:ss.SSSZZ",
     *         "yyyy-MM-dd", "yyyy-MM-ddX", "yyyy-MM-ddXX", "yyyy-MM-ddZZ"，"yyyyMMddHHmmss", "yyyyMMddHHmm", yyyy-MM-dd hh:mm:ssaa
     * @return
     */
    public static Date convert(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        dateTimeStr = dateTimeStr.trim();
        if (dateTimeStr.length() == 13 && dateTimeStr.matches(TIME_STAMP_FORMAT)) {
            try {
                return new Date(Long.parseLong(dateTimeStr));
            } catch (Exception e) {
                throw new RuntimeException(String.format("parser %s to Date fail", dateTimeStr));
            }
        }

        try {
            return convert(dateTimeStr, DATE_FORMATS);
        } catch (ParseException e) {
            throw new RuntimeException(String.format("parser %s to Date fail", dateTimeStr));
        }
    }

    /**
     * 将字符串转换成日期
     * @param dateTimeStr
     * @param parsePatterns 提供转换的时间格式
     * @return
     * @throws ParseException
     */
    public static Date convert(String dateTimeStr, final String... parsePatterns) throws ParseException {
        for (String dateFormat : parsePatterns) {
            try {
                Date date;
                if (dateTimeStr.length() == 12) {
                    date = (Date) DataTypes.DATE.fromString(dateTimeStr, dateFormat);
                } else {
                    date = DateUtils.parseDate(dateTimeStr, Locale.ENGLISH, dateFormat);
                }
                if (date != null) {
                    return date;
                }
            } catch (Exception ignored) {
                // 这里不处理，失败进行下一个尝试
            }
        }
        throw new ParseException("Unable to parse the date: " + dateTimeStr, -1);
    }

    /**
     * 获取当前日期的yyyyMMdd的模式
     */
    public static String currentDateToString() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 创建一个日期时间格式化器，指定要格式化的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        // 使用格式化器将日期转换为字符串
        return currentDate.format(formatter);
    }


    /**
     * 解析字符串中的日期yyyyMMdd的模式
     * @param query
     * @return
     */
    public static String[] extractDatesToString(String query) {
        String[] dates = new String[2];
        Pattern pattern = Pattern.compile("\\d{4}-\\d{2}-\\d{2}");
        Matcher matcher = pattern.matcher(query);

        int dateIndex = 0;
        while (matcher.find() && dateIndex < 2) {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
            try {
                Date date = inputFormat.parse(matcher.group());
                dates[dateIndex] = outputFormat.format(date);
                dateIndex++;
            } catch (ParseException e) {
                // 处理日期解析异常
                e.printStackTrace();
            }
        }
        return dates;
    }

    public static Date getDateBeforeHours(int hours) {
        // 获取当前时间
        Date currentTime = new Date();
        // 创建Calendar对象并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentTime);
        // 将时间向前
        calendar.add(Calendar.HOUR_OF_DAY, -1 * hours);
        // 获取推前两小时后的时间
        return calendar.getTime();
    }

    public static Date getDateAfterHours(Date start, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);
        // 将时间向前
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        // 获取推前两小时后的时间
        return calendar.getTime();
    }

    public static Long dateDiff(Date dt1, Date dt2) {
        if (dt1 == null || dt2 == null) {
            return 0L;
        }
        long diffInSeconds = (dt1.getTime() - dt2.getTime()) / 1000;
        return Math.abs(diffInSeconds);
    }

    /**
     * 获取将yyyyMMddHHmm类型的两个时间段转换成HH:mm-HH:mm的格式
     * 现有两家医院,本钢传的是yyyyMMddHHmm 阜新传的是yyyyMMddHHmmss 需要兼容
     * @param beginTime
     * @param endTime
     * @return
     */
    public static String createVisitTimeSpan(String beginTime, String endTime) {
        try {
            SimpleDateFormat outputFormat = new SimpleDateFormat("HH:mm");
            return outputFormat.format(convert(beginTime)) + "-" + outputFormat.format(convert(endTime));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取筛选月份所在的最小时间和最大时间
     * @param monthString
     * @return
     */
    public static Date[] extractMonth(String monthString) {
        int year = Integer.parseInt(monthString.split("-")[0]);
        int month = Integer.parseInt(monthString.split("-")[1]);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // 月份从0开始，所以需要减1
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为该月第一天
        Date firstDayOfMonth = getStartOfMonth(calendar.getTime()); // 月的最小时间
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date lastDayOfMonth = getEndOfMonth(calendar.getTime()); // 月的最大时间
        if (year == Calendar.getInstance().get(Calendar.YEAR) && month == Calendar.getInstance().get(Calendar.MONTH) + 1) {
            lastDayOfMonth = new Date();
        }
        return new Date[]{firstDayOfMonth, lastDayOfMonth};
    }

    /**
     * 获取筛选年份的最小时间和最大时间
     * @param year
     * @return
     */
    public static Date[] extractYear(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, Calendar.JANUARY); // 将月份设置为一月，即最小月
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstMonth = getStartOfMonth(calendar.getTime());
        calendar.set(Calendar.MONTH, Calendar.DECEMBER); // 将月份设置为十二月，即最大月
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date lastMonth = getEndOfMonth(calendar.getTime());
        if (year == Calendar.getInstance().get(Calendar.YEAR)) {
            // 如果年份与当前年份相同，使用当前时间作为最大月
            lastMonth = new Date();
        }
        return new Date[]{firstMonth, lastMonth};
    }

    public static List<String> getDatetoStringList(Date startTime, Date endTime, boolean dayFly) {
        List<String> param = Lists.newArrayList();
        if (DateUtils.addDays(startTime, 31).getTime() >= endTime.getTime()) {
            while (startTime.getTime() < endTime.getTime()) {
                if (dayFly) {
                    param.add(dateToString(startTime, "YYYY-MM-dd"));
                } else {
                    param.add(dateToString(startTime, "YYYY-MM"));
                }
                startTime = DateUtils.addDays(startTime, 1);
            }
        } else {
            Date truncate = DateUtils.truncate(startTime, Calendar.MONTH);
            while (truncate.getTime() < endTime.getTime()) {
                if (dayFly) {
                    param.add(dateToString(truncate, "YYYY-MM-DD"));
                } else {
                    param.add(dateToString(truncate, "YYYY-MM"));
                }
                truncate = DateUtils.addMonths(truncate, 1);
            }
        }
        return param;
    }

    public static Date getPreWeekToday() {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        Date today = calendar.getTime();

        // 获取上个月的今天
        calendar.add(Calendar.MONTH, -1);
        return calendar.getTime();
    }

    /**
     * 兼容两家医院,将预约时间段以YYYY-MM-DD HH:mm-HH:mm格式输出
     * @param appointmentTimeSlot
     * @return
     */
    public static String appointmentTimeSlotSpan(String appointmentTimeSlot) {
        String[] appointmentTimeSlots = appointmentTimeSlot.split("-");
        return dateToString(convert(appointmentTimeSlots[0]), "yyyy-MM-dd") + " " +
                createVisitTimeSpan(appointmentTimeSlots[0], appointmentTimeSlots[1]);
    }

    /**
     * 获取 2014-07-21 10:00-11:00 格式时间
     * @param startTime
     * @param endTime
     * @return string
     */
    public static String getVisitTime(Date startTime, Date endTime) {
        String s = DataTypes.DATE.asString(startTime, "HH:mm");
        String e = DataTypes.DATE.asString(endTime, "HH:mm");
        String ymd = DataTypes.DATE.asString(startTime, "yyyy-MM-dd");
        return ymd + " " + " " + " " + s + "~" + e;
    }

    /**
     * 获取 yyyyMMddHHmmss 格式当前时间
     * @return string
     */
    public static String getTimeStr(Date date) {
        return DataTypes.DATE.asString(date, "yyyyMMddHHmmss");
    }

    /**
     * 查询两个日期之间的每一天的日期数据
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<Date> getBetweenDates(Date startTime, Date endTime) {
        List<Date> dates = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        while (calendar.getTime().before(endTime) || calendar.getTime().equals(endTime)) {
            dates.add(calendar.getTime());
            calendar.add(Calendar.DATE, 1);
        }
        return dates;
    }

    /**
     * 获取一个时间段内的map集合，
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<Date, Date> getTimeSegments(Date startTime, Date endTime) {
        Map<Date, Date> segments = new LinkedHashMap<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        while (calendar.getTime().before(endTime)) {
            Date currentDay = calendar.getTime();
            Date[] periods = getDailyPeriods(currentDay);
            for (int i = 0; i < periods.length - 1; i++) {
                Date periodStart = periods[i];
                Date periodEnd = periods[i + 1];
                if (periodEnd.after(endTime)) {
                    periodEnd = endTime;
                }
                if (periodStart.before(startTime)) {
                    periodStart = startTime;
                }
                if (!periodStart.equals(periodEnd)) {
                    segments.put(periodStart, periodEnd);
                }
                if (periodEnd.equals(endTime)) {
                    break;
                }
            }
            calendar.add(Calendar.DATE, 1);
        }
        return segments;
    }

    private static Date[] getDailyPeriods(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        Calendar startOfDay = (Calendar) calendar.clone();
        startOfDay.set(Calendar.HOUR_OF_DAY, 0);
        startOfDay.set(Calendar.MINUTE, 0);
        startOfDay.set(Calendar.SECOND, 0);
        startOfDay.set(Calendar.MILLISECOND, 0);
        Calendar noon = (Calendar) startOfDay.clone();
        noon.set(Calendar.HOUR_OF_DAY, 12);
        Calendar afternoon = (Calendar) startOfDay.clone();
        afternoon.set(Calendar.HOUR_OF_DAY, 17);
        Calendar endOfDay = (Calendar) startOfDay.clone();
        endOfDay.set(Calendar.HOUR_OF_DAY, 23);
        endOfDay.set(Calendar.MINUTE, 59);
        endOfDay.set(Calendar.SECOND, 59);
        return new Date[] {
                startOfDay.getTime(),
                noon.getTime(),
                afternoon.getTime(),
                endOfDay.getTime()
        };
    }

    /**
     * 判断两个日期是否同一天
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * 根据年月日创建时间
     * @param year
     * @param month
     * @param day
     * @return
     */
    public static Date createDate(int year, int month, int day) {
        LocalDate localDate = LocalDate.of(year, month, day);
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

}
