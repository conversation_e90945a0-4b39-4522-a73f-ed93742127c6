package cn.taihealth.ih.commons.error;

import java.net.URI;

/**
 */
public class ErrorConstants {

    public static final String PROBLEM_BASE_URL = "https://xxx.cn/problem";

    public static final URI DEFAULT_TYPE = URI.create(PROBLEM_BASE_URL + "/problem-with-message");
//
//    public static final URI CONSTRAINT_VIOLATION_TYPE = URI.create(PROBLEM_BASE_URL + "/contraint-violation");
//
//    public static final String ERR_CONCURRENCY_FAILURE = "error.concurrencyFailure";
//
//    public static final String ERR_VALIDATION = "error.validation";
//
//    public static final String ERR_INVALID_ID_CARD_IMAGE = "error.invalid.idcard.image";
//
//    public static final String ERR_IM_FAILURE = "error.im.failure";
//
//    public static final String ERR_BAD_CREDENTIALS = "error.bad.credentials";
}
