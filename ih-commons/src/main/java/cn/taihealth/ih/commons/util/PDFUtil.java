package cn.taihealth.ih.commons.util;

import com.google.common.collect.Lists;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Entities;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * Created by Moon on 2022/4/26 2:36 PM
 */
@Slf4j
public class PDFUtil {

    public static String writeStringToOutputStreamAsPDF(String html, OutputStream os) throws UnsupportedEncodingException {
        String s = formatHtml(html);
        writeToOutputStreamAsPDF(new ByteArrayInputStream(s.getBytes(StandardCharsets.UTF_8)), os);
        return s;
    }

    public static void writeToOutputStreamAsPDF(InputStream html, OutputStream os) {
        try {
            Document document = new Document(PageSize.A4);
            PdfWriter pdfWriter = PdfWriter.getInstance(document, os);
            document.open();
            try {
                XMLWorkerHelper worker = XMLWorkerHelper.getInstance();
                worker.parseXHtml(pdfWriter, document, html, null, StandardCharsets.UTF_8, new AsianFontProvider());
            } catch (Exception e) {
                log.error("PDF写入异常", e);
                document.close();
            }
            document.close();
        } catch (Exception e) {
            log.error("生成处方PDF失败", e);
        }
    }

    private static String formatHtml(String html) {
        log.info("格式化前HTML：{}", html);
        org.jsoup.nodes.Document doc = Jsoup.parse(html);

        // jsoup标准化标签，生成闭合标签
        doc.outputSettings().syntax(org.jsoup.nodes.Document.OutputSettings.Syntax.xml);
        doc.outputSettings().escapeMode(Entities.EscapeMode.xhtml);
        return doc.html();
    }

    /***
     * PDF文件转PNG图片
     *
     * @param pdfFilePath pdf完整路径
     * @param imgFolder 图片存放的文件夹
     * @param dpi dpi越大转换后越清晰，相对转换速度越慢
     * @param pages 页数，若不传则为全部页数
     * @return
     */
    public static List<File> pdf2Image(String pdfFilePath, String imgFolder, int dpi, Integer pages) {
        List<File> files = Lists.newArrayList();
        File file = new File(pdfFilePath);
        PDDocument pdDocument;
        try {
            String imgPDFPath = file.getParent();
            int dot = file.getName().lastIndexOf('.');
            String imagePDFName = file.getName().substring(0, dot); // 获取图片文件名
            String imgFolderPath;
            if (StringUtils.isBlank(imgFolder)) {
                imgFolderPath = imgPDFPath + File.separator + imagePDFName;// 获取图片存放的文件夹路径
            } else {
                imgFolderPath = imgFolder + File.separator + imagePDFName;
            }
            if (createDirectory(imgFolderPath)) {
                pdDocument = PDDocument.load(file);
                try {
                    PDFRenderer renderer = new PDFRenderer(pdDocument);
                    /* dpi越大转换后越清晰，相对转换速度越慢 */
                    PdfReader reader = new PdfReader(pdfFilePath);
                    int realPages = pages == null ? reader.getNumberOfPages() : pages;
                    StringBuilder imgFilePath;
                    for (int i = 0; i < realPages; i++) {
                        String imgFilePathPrefix = imgFolderPath + File.separator + imagePDFName;
                        imgFilePath = new StringBuilder();
                        imgFilePath.append(imgFilePathPrefix);
                        imgFilePath.append("_");
                        imgFilePath.append(i + 1);
                        imgFilePath.append(".png");
                        File dstFile = new File(imgFilePath.toString());
                        BufferedImage image = renderer.renderImageWithDPI(i, dpi);
                        ImageIO.write(image, "png", dstFile);
                        files.add(dstFile);
                    }
                    log.info("PDF文档转PNG图片成功！");
                } catch (IOException e) {
                    log.error("PDF文档转PNG图片失败！", e);
                    pdDocument.close();
                }
                pdDocument.close();
            } else {
                log.error("PDF文档转PNG图片失败：" + "创建" + imgFolderPath + "失败");
            }

        } catch (IOException e) {
            log.error("PDF转png失败", e);
        }
        return files;
    }


    private static boolean createDirectory(String folder) {
        File dir = new File(folder);
        if (dir.exists()) {
            return true;
        } else {
            return dir.mkdirs();
        }
    }

    /**
     * 在pdf指定位置添加图片
     * @param sourcePath
     * @param targetPath
     * @param picturePath
     * @param pageIndex 页码，当页码为-1时，表示最后一页
     * @param x
     * @param y
     * @throws IOException
     */
    public static void addPicture(String sourcePath, String targetPath, String picturePath, int pageIndex, float x, float y) throws IOException {
        try (PDDocument document = PDDocument.load(new File(sourcePath))) {
            PDPageTree pages = document.getPages();
            PDPage page;
            if (pageIndex == -1) {
                page = pages.get(pages.getCount() - 1);
            } else {
                page = pages.get(pageIndex);
            }
            // 加载图像
            PDImageXObject image = PDImageXObject.createFromFile(picturePath, document);
            // 创建页面内容流
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true)) {
                // 将图像添加到页面内容流中
                contentStream.drawImage(image, x, y); // 指定图像的位置
                // 关闭页面内容流
                contentStream.close();
                // 保存修改后的PDF文档
                document.save(targetPath);
            }
        }
    }

    /**
     * 在pdf指定位置添加图片, 并在图片下方添加日期
     * @param sourceFile
     * @param targetPath
     * @param picture
     * @param pageIndex 页码，当页码为-1时，表示最后一页
     * @param x
     * @param y
     * @throws IOException
     */
    public static void addSign(File sourceFile, String targetPath, File picture, int pageIndex, float x, float y) throws IOException {
        try (PDDocument document = PDDocument.load(sourceFile)) {
            PDPageTree pages = document.getPages();
            PDPage page;
            if (pageIndex == -1) {
                page = pages.get(pages.getCount() - 1);
            } else {
                page = pages.get(pageIndex);
            }

            double height = 60;
            double width = 120;
            BufferedImage bi = ImageIO.read(picture);
            if (bi != null) {
                height = bi.getHeight() / 1.33;
                width = bi.getWidth() / 1.33;
            }
            if (width > 120) {
                double scale = width / 120;
                width = 120;
                height = height / scale;
            }

            // 加载图像
            PDImageXObject image = PDImageXObject.createFromFileByExtension(picture, document);
            // 创建页面内容流
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true)) {
                // 将图像添加到页面内容流中
                contentStream.drawImage(image, x, y, (float) width, (float) height); // 指定图像的位置

                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.beginText();
                contentStream.newLineAtOffset(x + 5, y - 20);
                contentStream.showText(TimeUtils.dateToString(new Date()));
                contentStream.endText();

                // 关闭页面内容流
                contentStream.close();
                // 保存修改后的PDF文档
                document.save(targetPath);
            }
        }



    }


    /**
     * 在pdf指定位置添加签名时间
     * @param sourceFile
     * @param targetPath
     * @param pageIndex 页码，当页码为-1时，表示最后一页
     * @param x
     * @param y
     * @throws IOException
     */
    public static void addSignTime(File sourceFile, String targetPath, int pageIndex, float x, float y) throws IOException {
        try (PDDocument document = PDDocument.load(sourceFile)) {
            PDPageTree pages = document.getPages();
            PDPage page;
            if (pageIndex == -1) {
                page = pages.get(pages.getCount() - 1);
            } else {
                page = pages.get(pageIndex);
            }

            // 创建页面内容流
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true)) {
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.beginText();
                contentStream.newLineAtOffset(x + 5, y - 20);
                contentStream.showText(TimeUtils.dateToString(new Date()));
                contentStream.endText();

                // 关闭页面内容流
                contentStream.close();
                // 保存修改后的PDF文档
                document.save(targetPath);
            }
        }
    }

    /**
     * 将图片转换为PDF
     * @param imageFile 图片文件
     * @param pdfFile 输出的PDF文件
     * @throws IOException
     */
    public static void convertImageToPDF(File imageFile, File pdfFile) throws IOException {
        try (PDDocument document = new PDDocument()) {
            // 使用默认构造函数创建标准的A4页面
            PDPage page = new PDPage();
            document.addPage(page);

            PDImageXObject image = PDImageXObject.createFromFileByExtension(imageFile, document);
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // 计算图片在PDF中的位置和大小（保持原始比例）
                float scale = 1.0f;
                if (image.getHeight() > page.getMediaBox().getHeight() || image.getWidth() > page.getMediaBox().getWidth()) {
                    // 如果图片太大，按比例缩小
                    float widthScale = page.getMediaBox().getWidth() / image.getWidth();
                    float heightScale = page.getMediaBox().getHeight() / image.getHeight();
                    scale = Math.min(widthScale, heightScale);
                }

                float x = (page.getMediaBox().getWidth() - (image.getWidth() * scale)) / 2;
                float y = (page.getMediaBox().getHeight() - (image.getHeight() * scale)) / 2;

                contentStream.drawImage(image, x, y, image.getWidth() * scale, image.getHeight() * scale);
            }

            document.save(pdfFile);
        }
    }

    public static void main(String[] args) throws IOException {
        convertImageToPDF(new File("C:\\Users\\<USER>\\Downloads\\就医服务Banner.png"), new File("C:\\Users\\<USER>\\Downloads\\1.pdf"));
    }
    /**
     * 获取最后一样位置示例
     */
//    public static void main(String[] args) {
//        try {
//            // 加载PDF文档
//            PDDocument document = PDDocument.load(new File("your_document.pdf"));
//
//            // 创建自定义的PDFTextStripper子类
//            CustomPDFTextStripper textStripper = new CustomPDFTextStripper();
//
//            // 提取文本内容并获取最后一行的坐标
//            textStripper.extractText(document);
//            Rectangle2D.Float lastLineCoordinates = textStripper.getLastLineCoordinates();
//
//            // 打印最后一行的坐标
//            System.out.println("最后一行的坐标：x=" + lastLineCoordinates.x + ", y=" + lastLineCoordinates.y);
//
//            // 关闭文档
//            document.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//
//    // 自定义的PDFTextStripper子类
//    private static class CustomPDFTextStripper extends PDFTextStripper {
//        private Rectangle2D.Float lastLineCoordinates;
//
//        @Override
//        protected void processTextPosition(TextPosition text) {
//            super.processTextPosition(text);
//
//            // 检查当前文本位置是否在新的行中
//            if (text.getIndividualWidth() == 0 && !text.getUnicode().equals(" ")) {
//                // 获取最后一行的坐标
//                lastLineCoordinates = new Rectangle2D.Float(
//                        text.getTextMatrix().getTranslateX(),
//                        text.getTextMatrix().getTranslateY() - text.getHeight(),
//                        text.getWidth(),
//                        text.getHeight()
//                );
//            }
//        }
//
//        public Rectangle2D.Float getLastLineCoordinates() {
//            return lastLineCoordinates;
//        }
//    }

}


