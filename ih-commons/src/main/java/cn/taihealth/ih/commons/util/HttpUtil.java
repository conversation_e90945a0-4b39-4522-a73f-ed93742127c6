package cn.taihealth.ih.commons.util;


import cn.taihealth.ih.commons.Constants;
import com.google.common.base.Strings;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;

/**
 * Utility class for HTTP creation.
 */
public final class HttpUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    private HttpUtil() {
    }

    /**
     * http下载
     *
     * @param httpUrl
     * @param destFile
     * @param fileMd5
     * @throws Exception
     */
    public static void httpDownload(String httpUrl, String destFile, String fileMd5) throws IOException {
        IOException ex = null;
        // 如果文件已存在，直接返回
        if (new File(destFile).exists()) {
            try (InputStream in = new FileInputStream(destFile)) {
                if (fileMd5.equalsIgnoreCase(DigestUtils.md5Hex(in))) {
                    return;
                }
            }
        }
        // 尝试下载3次
        for (int i = 0; i < 3; i++) {
            URL url = new URL(httpUrl);
            URLConnection connection = url.openConnection();
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(30000);
            try (InputStream inStream = connection.getInputStream();
                 FileOutputStream fs = new FileOutputStream(destFile);) {
                IOUtils.copy(inStream, fs);
            } catch (IOException e) {
                ex = e;
            }
            try (InputStream in = new FileInputStream(destFile)) {
                if (fileMd5.equalsIgnoreCase(DigestUtils.md5Hex(in))) {
                    return;
                }
            } catch (IOException e) {
                ex = e;
            }
        }
        throw ex;
    }


    public static void copyStream(String httpUrl, HttpServletResponse response) throws IOException {
        URL url = new URL(httpUrl);
        URLConnection connection = url.openConnection();
        connection.setConnectTimeout(3000);
        connection.setReadTimeout(30000);
        try (InputStream inStream = connection.getInputStream();
            OutputStream fs = response.getOutputStream()) {
            IOUtils.copy(inStream, fs);
        }
    }

    /**
     * 给url添加参数
     * @param url 支持xxx/xxxx, xxx/xxxx?a=b&c=d, xxx/xxxx?a=b&c=d#/a/b, xxx/xxxx#/a/b
     * @param params {"a": "1", "b": "b"}
     * @return
     */
    public static String addParams(String url, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return url;
        }

        StringBuilder newUrl = new StringBuilder(url);
        Matcher matcher = Constants.URL_MATCH_PATTERN.matcher(newUrl.toString());
        if (matcher.matches()) {
            newUrl = new StringBuilder(matcher.group("url"));
            String param = matcher.group("param");
            String hash = matcher.group("hash");
            int i = 0;
            for (Entry<String, String> entry : params.entrySet()) {
                String k = entry.getKey();
                String v = entry.getValue();
                String p = "";
                if (i == 0) {
                    p = "?" + k + "=" + URLEncoder.encode(v, StandardCharsets.UTF_8);
                } else {
                    p = "&" + k + "=" + URLEncoder.encode(v, StandardCharsets.UTF_8);
                }
                newUrl.append(p);
                i++;
            }
            if (!Strings.isNullOrEmpty(param)) {
                newUrl.append("&").append(param);
            }
            if (!Strings.isNullOrEmpty(hash)) {
                newUrl.append(hash);
            }
        }
        return newUrl.toString();
    }

    /**
     * {"a": "1", "b": "2"} -> a=1&b=2
     * @param params
     * @return
     */
    public static String toHttpParams(Map<String, String> params) {
        List<String> strings = new ArrayList<>();
        for (Entry<String, String> entry : params.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            strings.add(k + "=" + v);
        }
        return StringUtils.join(strings, "&");
    }

    /**
     * {"a": ["1"], "b": ["2", "3"]} -> a=1&b=2&b=3
     * @param params
     * @return
     */
    public static String toListHttpParams(Map<String, String[]> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        List<String> strings = new ArrayList<>();
        for (Entry<String, String[]> entry : params.entrySet()) {
            String k = entry.getKey();
            String[] v = entry.getValue();
            for (String s : v) {
                strings.add(k + "=" + s);
            }
        }
        return StringUtils.join(strings, "&");
    }

}

