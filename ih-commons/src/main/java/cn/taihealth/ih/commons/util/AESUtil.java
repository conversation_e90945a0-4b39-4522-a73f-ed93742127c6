package cn.taihealth.ih.commons.util;

import okhttp3.Headers;
import okhttp3.Response;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * AES加密工具类
 *
 */
public class AESUtil {
    /**
     * 编码
     */
    private static final String ENCODING = "UTF-8";
    /**
     * 算法定义
     */
    private static final String AES_ALGORITHM = "AES";
    /**
     * 指定填充方式
     */
    private static final String CIPHER_PADDING = "AES/ECB/PKCS5Padding";
    private static final String CIPHER_CBC_PADDING = "AES/CBC/PKCS5Padding";

    /**
     * AES加密
     *
     * @param content 待加密内容
     * @param aesKey  密码
     * @return
     */
    public static String encrypt(String content, String aesKey) {
        if (content == null) {
            return null;
        }
        //判断秘钥是否为16位
        if (aesKey != null && aesKey.length() == 16) {
            try {
                //对密码进行编码
                byte[] bytes = aesKey.getBytes(ENCODING);
                //设置加密算法，生成秘钥
                SecretKeySpec skeySpec = new SecretKeySpec(bytes, AES_ALGORITHM);
                // "算法/模式/补码方式"
                Cipher cipher = Cipher.getInstance(CIPHER_PADDING);
                //选择加密
                cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
                //根据待加密内容生成字节数组
                byte[] encrypted = cipher.doFinal(content.getBytes(ENCODING));
                //返回base64字符串
                return Base64.getEncoder().encodeToString(encrypted);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } else {
            return null;
        }
    }

    /**
     * 解密
     *
     * @param content 待解密内容
     * @param aesKey  密码
     * @return
     */
    public static String decrypt(String content, String aesKey) {
        if (content == null) {
            return null;
        }
        //判断秘钥是否为16位
        if (aesKey != null && aesKey.length() == 16) {
            try {
                //对密码进行编码
                byte[] bytes = aesKey.getBytes(ENCODING);
                //设置解密算法，生成秘钥
                SecretKeySpec skeySpec = new SecretKeySpec(bytes, AES_ALGORITHM);
                // "算法/模式/补码方式"
                Cipher cipher = Cipher.getInstance(CIPHER_PADDING);
                //选择解密
                cipher.init(Cipher.DECRYPT_MODE, skeySpec);

                //先进行Base64解码
                byte[] decodeBase64 = Base64.getDecoder().decode(content);

                //根据待解密内容进行解密
                byte[] decrypted = cipher.doFinal(decodeBase64);
                //将字节数组转成字符串
                return new String(decrypted, ENCODING);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } else {
            return null;
        }
    }

    /**
     * AES_CBC加密
     *
     * @param content 待加密内容
     * @param aesKey  密码
     * @return
     */
    public static String encryptCBC(String content, String aesKey, String ivSeed) {
        if (content == null) {
            return null;
        }
        //判断秘钥是否为16位
        if (aesKey != null && aesKey.length() == 16) {
            try {
                //对密码进行编码
                byte[] bytes = aesKey.getBytes(ENCODING);
                //设置加密算法，生成秘钥
                SecretKeySpec skeySpec = new SecretKeySpec(bytes, AES_ALGORITHM);
                // "算法/模式/补码方式"
                Cipher cipher = Cipher.getInstance(CIPHER_CBC_PADDING);
                //偏移
                IvParameterSpec iv = new IvParameterSpec(ivSeed.getBytes(ENCODING));
                //选择加密
                cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
                //根据待加密内容生成字节数组
                byte[] encrypted = cipher.doFinal(content.getBytes(ENCODING));
                //返回base64字符串
                return Base64.getEncoder().encodeToString(encrypted);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } else {
            return null;
        }
    }

    /**
     * AES_CBC解密
     *
     * @param content 待解密内容
     * @param aesKey  密码
     * @return
     */
    public static String decryptCBC(String content, String aesKey, String ivSeed) {
        if (content == null) {
            return null;
        }
        //判断秘钥是否为16位
        if (aesKey != null && aesKey.length() == 16) {
            try {
                //对密码进行编码
                byte[] bytes = aesKey.getBytes(ENCODING);
                //设置解密算法，生成秘钥
                SecretKeySpec skeySpec = new SecretKeySpec(bytes, AES_ALGORITHM);
                //偏移
                IvParameterSpec iv = new IvParameterSpec(ivSeed.getBytes(ENCODING));
                // "算法/模式/补码方式"
                Cipher cipher = Cipher.getInstance(CIPHER_CBC_PADDING);
                //选择解密
                cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);

                //先进行Base64解码
                byte[] decodeBase64 = Base64.getDecoder().decode(content);

                //根据待解密内容进行解密
                byte[] decrypted = cipher.doFinal(decodeBase64);
                //将字节数组转成字符串
                return new String(decrypted, ENCODING);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } else {
            return null;
        }
    }


    /**
     * AES_CBC解密
     *
     * @param content 待解密内容
     * @param aesKey  密码
     * @return
     */
    public static String decryptCBC(byte[] content, String aesKey, String ivSeed) {
        //判断秘钥是否为16位
        if (aesKey != null && aesKey.length() == 16) {
            try {
                //对密码进行编码
                byte[] bytes = aesKey.getBytes(ENCODING);
                //设置解密算法，生成秘钥
                SecretKeySpec skeySpec = new SecretKeySpec(bytes, AES_ALGORITHM);
                //偏移
                IvParameterSpec iv = new IvParameterSpec(ivSeed.getBytes(ENCODING));
                // "算法/模式/补码方式"
                Cipher cipher = Cipher.getInstance(CIPHER_CBC_PADDING);
                //选择解密
                cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);

                //先进行Base64解码
                byte[] decodeBase64 = content;

                //根据待解密内容进行解密
                byte[] decrypted = cipher.doFinal(decodeBase64);
                //将字节数组转成字符串
                return new String(decrypted, ENCODING);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } else {
            return null;
        }
    }

    public static String getWeixinAuth() {
        String us = "taiyi" + RandomUtils.generateRandomNumbers(5) + "/" + "taiyi" + RandomUtils.generateRandomNumbers(10) + "/" + System.currentTimeMillis();
        return AESUtil.encryptCBC(us, "NJCdWFky5v6wBSO1", "khzZvvZ1gChBPX2e");
    }

//    public static String urlEncoderCBC(String content) {
//        return URLEncoder.encode(encryptCBC(content, Constants.AES_KEY, Constants.AES_IV_SEED), Charsets.UTF_8);
//    }
//
//    public static String urlDecoderCBC(String content) {
//        return decryptCBC(URLDecoder.decode(content, Charsets.UTF_8), Constants.AES_KEY, Constants.AES_IV_SEED);
//    }


    public static void main(String[] args) {
        System.out.println("--------AES_CBC加密解密---------");
        String cbcResult = AESUtil.encryptCBC("55u;5Q,5U?7b&6z,", "0123456789ASDFGH", "ASDFGH0123456789");
        System.out.println("aes_cbc加密结果:" + cbcResult);
        System.out.println();

        System.out.println("---------解密CBC---------");
        String cbcDecrypt = AESUtil.decryptCBC("xaz6PTbk6zbuShfGE0BAH+F5LRkG7tJ2UfrK1ju3yqA=", "0123456789ASDFGH", "ASDFGH0123456789");
        System.out.println("aes解密结果:" + cbcDecrypt);
        System.out.println();

        String us = "taiyi" + RandomUtils.generateRandomNumbers(5) + "/" + "taiyi" + RandomUtils.generateRandomNumbers(10);
        String inOut = AESUtil.encryptCBC(us, "NJCdWFky5v6wBSO1", "khzZvvZ1gChBPX2e");
        try {
            System.out.println(inOut);
            Map<String, String> header = new HashMap<>();
            header.put("in-out", inOut);
            Headers headers = Headers.of(header);
            Response response = OkHttpUtils.get("http://localhost:8082/wxapi/api/admin/log", null, headers);
            System.out.println(OkHttpUtils.getResponseBody(response));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}