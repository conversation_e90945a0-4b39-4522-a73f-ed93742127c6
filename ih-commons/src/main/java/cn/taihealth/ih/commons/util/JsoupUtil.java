package cn.taihealth.ih.commons.util;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Whitelist;

/**
 * xss非法标签过滤工具类
 * 过滤html中的xss字符
 * <AUTHOR>
 */
public class JsoupUtil {

    private static final Whitelist whitelist = Whitelist.relaxed();
    private static final Document.OutputSettings outputSettings = new Document.OutputSettings().prettyPrint(false);

    static {
        whitelist.addAttributes(":all", "style", "class");
        // 允许 iframe，并添加安全的属性
        whitelist.addTags("iframe");
        whitelist.addAttributes("iframe", "src", "frameborder", "allowfullscreen", "class", "data-blot-formatter-unclickable-bound");
        // 仅允许 HTTPS 或相对路径 iframe 源地址，防止 XSS 注入
        whitelist.addProtocols("iframe", "src", "https", "http");
        // 允许 video 标签， 并添加安全的属性
        whitelist.addTags("video");
        whitelist.addAttributes("video", "src", "controls", "autoplay", "loop", "muted", "preload", "poster", "width", "height", "class");
        whitelist.addProtocols("video", "src", "https", "http");
    }

    public static String clean(String content) {
        if (StringUtils.isNotBlank(content)) {
            content = content.trim();
        }
        return Jsoup.clean(content, "", whitelist, outputSettings);
    }

    public static boolean isValid(String content) {
        if (StringUtils.isBlank(content)) {
            return true;
        }
        return Jsoup.isValid(content, whitelist);
    }
}