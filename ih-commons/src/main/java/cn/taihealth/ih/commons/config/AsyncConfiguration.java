package cn.taihealth.ih.commons.config;

import cn.taihealth.ih.commons.async.ExceptionHandlingAsyncTaskExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import javax.inject.Inject;
import java.util.concurrent.Callable;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;

@Configuration
@EnableAsync
@EnableRetry
@EnableScheduling
public class AsyncConfiguration implements AsyncConfigurer {

    private static final Logger log = LoggerFactory.getLogger(AsyncConfiguration.class);

    private final ApplicationProperties applicationProperties;

    @Inject
    AsyncConfiguration(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @Override
    public Executor getAsyncExecutor() {
        return exceptionHandlingAsyncTaskExecutor();
    }

    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(applicationProperties.getAsync().getCorePoolSize());
        taskScheduler.setThreadNamePrefix("ih-task-scheduler-");
        return taskScheduler;
    }

    @Bean
    public ExceptionHandlingAsyncTaskExecutor exceptionHandlingAsyncTaskExecutor() {
        return new ExceptionHandlingAsyncTaskExecutor(asyncTaskExecutor("ih-async-executor"));
    }

    @Bean("sendMessageExecutor")
    public ExceptionHandlingAsyncTaskExecutor exceptionSendMiniMessageAsyncTaskExecutor() {
        return new ExceptionHandlingAsyncTaskExecutor(asyncTaskExecutor("ih-async-send-message-executor"));
    }

    @Bean("loginExecutor")
    public ExceptionHandlingAsyncTaskExecutor loginTaskExecutor() {
        return new ExceptionHandlingAsyncTaskExecutor(asyncTaskExecutor("ih-login-executor"));
    }

    @Bean("hisSyncExecutor")
    public ExceptionHandlingAsyncTaskExecutor hisSyncExecutor() {
        return new ExceptionHandlingAsyncTaskExecutor(asyncTaskExecutor("ih-his-sync-executor"));
    }

    @Bean("hisRecordSyncExecutor")
    public ExceptionHandlingAsyncTaskExecutor hisRecordSyncExecutor() {
        return new ExceptionHandlingAsyncTaskExecutor(asyncTaskExecutor("ih-his-record-sync-executor"));
    }

    private AsyncTaskExecutor asyncTaskExecutor(String prefix) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor() {
            @Override
            public void execute(Runnable task) {
                log.debug("{} active threads: {}", getThreadNamePrefix(), getActiveCount());
                super.execute(task);
            }

            @Override
            public void execute(Runnable task, long startTimeout) {
                log.debug("{} active threads: {}", getThreadNamePrefix(), getActiveCount());
                super.execute(task, startTimeout);
            }

            @Override
            public Future<?> submit(Runnable task) {
                log.debug("{} active threads: {}", getThreadNamePrefix(), getActiveCount());
                return super.submit(task);
            }

            @Override
            public <T> Future<T> submit(Callable<T> task) {
                log.debug("{} active threads: {}", getThreadNamePrefix(), getActiveCount());
                return super.submit(task);
            }
        };

        executor.setCorePoolSize(applicationProperties.getAsync().getCorePoolSize());
        executor.setMaxPoolSize(applicationProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(applicationProperties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix(prefix);
        executor.initialize();
        return executor;
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }
}
