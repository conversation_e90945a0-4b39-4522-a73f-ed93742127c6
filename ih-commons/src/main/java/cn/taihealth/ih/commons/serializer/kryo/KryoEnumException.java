package cn.taihealth.ih.commons.serializer.kryo;

public class KryoEnumException extends RuntimeException {

    private Class<? extends Enum> key;
    private String value;


    public KryoEnumException(Class<? extends Enum> key, String value, String message) {
        super(message);
        this.key = key;
        this.value = value;
    }

    public KryoEnumException(Class<? extends Enum> key, String value, String message, Throwable cause) {
        super(message, cause);
        this.key = key;
        this.value = value;
    }

    public Class<? extends Enum> getKey() {
        return key;
    }

    public void setKey(Class<? extends Enum> key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
