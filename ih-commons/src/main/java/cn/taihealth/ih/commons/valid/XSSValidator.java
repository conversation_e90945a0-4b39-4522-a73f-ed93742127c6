package cn.taihealth.ih.commons.valid;

import cn.taihealth.ih.commons.util.JsoupUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class XSSValidator implements ConstraintValidator<XSSValid, String> {

    public XSSValidator() {
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return JsoupUtil.isValid(value);
    }
}