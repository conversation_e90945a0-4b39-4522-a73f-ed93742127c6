package cn.taihealth.ih.commons.util;

import cn.taihealth.ih.commons.Constants;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.HtmlUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;

/**
 */
public class StringUtil {

    public static String getDefault(Object v) {
        if (v == null) {
            return "";
        }
        return v.toString();
    }

    public static String joinWithDoubleSeparator(final Object[] array, final String separator) {
        if (array == null || array.length == 0) {
            return null;
        }
        String string = org.apache.commons.lang3.StringUtils.join(array, separator);
        if (org.apache.commons.lang3.StringUtils.isBlank(string)) {
            return string;
        } else {
            return separator + string + separator;
        }
    }

    public static String[] split(final String string, final String separator) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(string)) {
            return new String[]{};
        }
        return Arrays.stream(string.split(separator))
            .filter(StringUtils::isNotEmpty).toArray(String[]::new);
    }

    /**
     * <pre>
     *     "1" -> true
     *     "true" -> true 不区分大小写
     *     others -> false
     * </pre>
     * @param string
     * @return
     */
    public static boolean stringToBoolean(String string) {
        return "1".equals(string) || "true".equalsIgnoreCase(string);
    }

    public static String excelStrToHtmlStr(String excelStr) {
        if (StringUtils.isEmpty(excelStr)) {
            return "<p></p>";
        }
        return "<p>" + StringUtils.join(HtmlUtils.htmlEscapeDecimal(
                                            excelStr.replaceAll("\r\n", "\n")).split("\n"),
                                        "</p><p>") + "</p>";
    }

    public static String replaceSqlSlash(String pattern) {
        return pattern.replaceAll("\\\\", "\\\\\\\\\\\\\\\\");
    }

    public static String formatHtml(String format, String... args) {
        return String.format(format, Arrays.stream(args).map(HtmlUtils::htmlEscapeDecimal).toArray());
    }

    /**
     * 解析一个字符串，获取所有连续的数字，用集合接收
     */
    public static List<String> parse(String str) {
        // 解析数字
        ArrayList<String> list = new ArrayList<>();
        int start = 0, end, len = str.length();
        for (int i = 0; i < len; i++) {
            char cTop = 0;
            char cRear = 0;
            char c = str.charAt(i);
            if (i > 0) {
                cTop = str.charAt(i - 1);
            }
            if (i < len - 1) {
                cRear = str.charAt(i + 1);
            }
            // 如果 c 是数字 且 它前一个数不是数字 或者 它是第 0 个字符时，获得 start
            if (isNumber(c) && (!isNumber(cTop) || i == 0)) {
                start = i;
            }
            // 如果 c 是数字 且 它后一个数不是数字 或者 它是最后一个字符时，获得 end
            if (isNumber(c) && (!isNumber(cRear) || i == len - 1)) {
                end = i + 1;
                list.add(str.substring(start, end));
            }
        }
        return list;
    }

    /**
     * 判断是否是数字
     */
    public static boolean isNumber(char c) {
        // ascii 编码
        return c >= 48 && c <= 57;
    }

    /**
     * 将字符串转换成数字，无法转换时返回0
     * @param str
     * @return
     */
    public static int parseIntOrZero(String str) {
        try {
            return new BigDecimal(str).intValue();
        } catch (Exception ignored) {
            return 0;
        }
    }

    /**
     * 提取字符串中的全部数字
     * @param str
     * @return
     */
    public static String getNumbers(String str) {
        // 将非数字字符全部删除
        Matcher m = Constants.NUMBER_PATTERN.matcher(str);
        StringBuilder numbers = new StringBuilder();
        while (m.find()) {
            numbers.append(m.group());
        }
        return numbers.toString();
    }

    /**
     * 【推荐】使用 SHA-256 哈希函数将任意字符串转换为 16 字节的数组。
     * 这个方法是确定性的、抗碰撞的且安全的。
     *
     * @param inputString 输入的字符串 (如 "my-secret-pass")
     * @return 16 字节的数组
     */
    public static byte[] stringTo16ByteKey(String inputString) {
        if (inputString == null) {
            throw new IllegalArgumentException("Input string cannot be null.");
        }
        try {
            // 1. 获取 SHA-256 哈希算法实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            // 2. 对输入字符串进行哈希计算，得到 32 字节的哈希值
            byte[] hash = digest.digest(inputString.getBytes(StandardCharsets.UTF_8));
            // 3. 截取哈希值的前 16 个字节作为结果
            return Arrays.copyOf(hash, 16);
        } catch (NoSuchAlgorithmException e) {
            // 在现代 Java 环境中，SHA-256 总是可用的，所以这个异常几乎不会发生
            throw new RuntimeException("SHA-256 algorithm not found", e);
        }
    }
}
