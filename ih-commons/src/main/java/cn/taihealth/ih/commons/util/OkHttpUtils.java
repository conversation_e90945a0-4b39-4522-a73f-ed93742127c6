package cn.taihealth.ih.commons.util;

import cn.taihealth.ih.commons.handler.LogRequestIdConverter;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 */
@Slf4j
public class OkHttpUtils {
    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final MediaType FILE_MEDIA_TYPE = MediaType.parse("multipart/form-data");

    public static enum OkHttpHolder {
        INSTANCE;

        private final OkHttpClient client;
        OkHttpHolder () {
            this.client = new OkHttpClient();
        }

        public static OkHttpClient getClient() {
            return INSTANCE.client;
        }
    }

    public static class FilePart {
        private final String filename;
        private final byte[] content;
        private final String contentType;

        public FilePart(String filename, byte[] content, String contentType) {
            this.filename = filename;
            this.content = content;
            this.contentType = contentType;
        }
    }

    public static Response get(String url) throws IOException {
        return get(url, Collections.emptyMap());
    }

    public static Response get(String url, Map<String, String> params) throws IOException {
        return get(url, params, Headers.of());
    }

    public static Response get(String url, Map<String, String> queryParams, Headers headers) throws IOException {
        HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();

        if (queryParams != null) {
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    builder.addQueryParameter(entry.getKey(), entry.getValue());
                }
            }
        }

        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers = headers.newBuilder().add(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID)).build();
        }

        String fullUrl = builder.build().toString();
        Request request = new Request.Builder()
            .url(fullUrl)
            .headers(headers)
            .build();

        return call(request);
    }

    private static Response call(Request request) throws IOException {

        OkHttpClient.Builder bu = OkHttpHolder.getClient().newBuilder();

        // 设置连接超时时间
        bu.connectTimeout(60, TimeUnit.SECONDS);
        bu.readTimeout(120, TimeUnit.SECONDS);
        bu.callTimeout(120, TimeUnit.SECONDS);
//        bu.writeTimeout(1, TimeUnit.MINUTES);
//        bu.pingInterval(1, TimeUnit.MINUTES);

        return bu.build().newCall(request).execute();
    }

    public static Response post(String url, Map<String, Object> data) throws IOException {
        return post(url, StandardObjectMapper.stringify(data));
    }

    public static Response post(String url, String json) throws IOException {
        return post(url, json, Maps.<String, String>newHashMap());
    }

    public static Response post(String url, String json, Map<String, String> headers) throws IOException {
        RequestBody body = RequestBody.create(JSON, json);
        if (headers == null) {
            headers = new HashMap<>();
        }
        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers.put(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID));
        }

        Request request = new Request.Builder()
            .url(url)
            .headers(Headers.of(headers))
            .post(body)
            .build();
        return call(request);
    }

    public static Response post(String url, Headers headers, Map<String, String> queryParams, String json) throws IOException {
        HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
        if (queryParams != null) {
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    builder.addQueryParameter(entry.getKey(), entry.getValue());
                }
            }
        }
        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers = headers.newBuilder().add(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID)).build();
        }
        RequestBody body;
        String contentType = headers.get("Content-Type");
        if (contentType == null) {
            body = RequestBody.create(JSON, json);
        } else {
            body = RequestBody.create(MediaType.parse(contentType), json);
        }

        Request request = new Request.Builder()
            .url(builder.build().toString())
            .headers(headers)
            .post(body)
            .build();
        return call(request);
    }

    /**
     * 发送 multipart/form-data 请求的通用方法
     */
    public static Response postMultipart(String url, Headers headers, Map<String, String> formParts, Map<String, FilePart> fileParts) throws IOException {
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);

        if (formParts != null && !formParts.isEmpty()) {
            for (Map.Entry<String, String> entry : formParts.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue());
            }
        }

        // 添加文件参数
        if (fileParts != null && !fileParts.isEmpty()) {
            for (Map.Entry<String, FilePart> entry : fileParts.entrySet()) {
                FilePart filePart = entry.getValue();
                RequestBody fileBody = RequestBody.create(MediaType.parse(filePart.contentType), filePart.content);
                builder.addFormDataPart(entry.getKey(), filePart.filename, fileBody);
            }
        }

        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers = headers.newBuilder().add(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID)).build();
        }

        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(builder.build())
                .build();

        return call(request);
    }

    public static Response delete(String url, String json) throws IOException {
        RequestBody body = RequestBody.create(JSON, json);
        Headers headers = Headers.of(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID));

        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .delete(body)
                .build();
        return call(request);
    }

    public static Response delete(String url, Map<String, String> queryParams, Headers headers) throws IOException {
        HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();

        if (queryParams != null) {
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    builder.addQueryParameter(entry.getKey(), entry.getValue());
                }
            }
        }

        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers = headers.newBuilder().add(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID)).build();
        }

        Request request = new  Request.Builder()
            .url(builder.build().toString())
            .headers(headers)
            .delete()
            .build();

        return call(request);
    }


    public static Response delete(String url, Map<String, String> queryParams, Headers headers, String json) throws IOException {
        HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();

        if (queryParams != null) {
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    builder.addQueryParameter(entry.getKey(), entry.getValue());
                }
            }
        }

        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers = headers.newBuilder().add(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID)).build();
        }
        Request request;
        if (StringUtils.isBlank(json)) {
            request = new Request.Builder()
                    .url(builder.build().toString())
                    .headers(headers)
                    .delete()
                    .build();
        } else {
            RequestBody body = RequestBody.create(JSON, json);
            request = new Request.Builder()
                    .url(builder.build().toString())
                    .headers(headers)
                    .delete(body)
                    .build();
        }
        return call(request);
    }


    public static Response put(String url, String json) throws IOException {
        return put(url,Headers.of(Maps.newHashMap()),  null, json);
    }

    public static Response put(String url, Headers headers, Map<String, String> queryParams, String json) throws IOException {
        HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
        if (queryParams != null) {
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    builder.addQueryParameter(entry.getKey(), entry.getValue());
                }
            }
        }

        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers = headers.newBuilder().add(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID)).build();
        }

        RequestBody body = RequestBody.create(JSON, json);
        Request request = new Request.Builder()
            .url(builder.build().toString())
            .headers(headers)
            .put(body)
            .build();
        return call(request);
    }

    public static Response patch(String url, Headers headers, Map<String, String> queryParams, String json) throws IOException {
        HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
        if (queryParams != null) {
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    builder.addQueryParameter(entry.getKey(), entry.getValue());
                }
            }
        }

        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers = headers.newBuilder().add(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID)).build();
        }

        RequestBody body = RequestBody.create(JSON, json);
        Request request = new Request.Builder()
            .url(builder.build().toString())
            .headers(headers)
            .patch(body)
            .build();
        return call(request);
    }

    public static Response send(String url, String method, Headers headers, Map<String, String> queryParams, String json) throws IOException {
        HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
        if (queryParams != null) {
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    builder.addQueryParameter(entry.getKey(), entry.getValue());
                }
            }
        }

        if (StringUtils.isBlank(headers.get(LogRequestIdConverter.REQUEST_ID))
                && StringUtils.isNotBlank(MDC.get(LogRequestIdConverter.REQUEST_ID))) {
            headers = headers.newBuilder().add(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID)).build();
        }

        RequestBody body = json == null ? null : RequestBody.create(JSON, json);
        Request request = new Request.Builder()
            .url(builder.build().toString())
            .headers(headers)
            .method(method, body)
            .build();
        return call(request);
    }

    /**
     * 上传文件
     * @param url
     * @param file
     * @param nameKey
     * @return
     * @throws IOException
     */
    public static Response upload(String url, File file, String nameKey) throws IOException {
        RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), file);
        MultipartBody body = new MultipartBody.Builder()
                .setType(FILE_MEDIA_TYPE)
                .addFormDataPart(nameKey, URLEncoder.encode(file.getName(), StandardCharsets.UTF_8), fileBody)
                .build();

        Headers headers = Headers.of(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID));

        Request request = new Request.Builder()
                .post(body)
                .headers(headers)
                .url(url)
                .build();

        return call(request);
    }

    /**
     * 上传文件
     * @param url
     * @param file
     * @param nameKey
     * @param params
     * @return
     * @throws IOException
     */
    public static Response upload(String url, File file, String nameKey, Map<String, String> params, Headers headers) throws IOException {
        RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), file);
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(FILE_MEDIA_TYPE)
                .addFormDataPart(nameKey, URLEncoder.encode(file.getName(), StandardCharsets.UTF_8), fileBody);
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue());
            }
        }

        MultipartBody body = builder.build();
        Request request = new Request.Builder()
                .post(body)
                .headers(headers)
                .url(url)
                .build();

        return call(request);
    }

    /**
     * 上传数据
     * @param url 上传地址
     * @param data 数据
     * @param nameKey 数据文件名称key
     * @param name 数据文件名称
     * @param params 参数
     * @param headers headers
     * @return
     * @throws IOException
     */
    public static Response upload(String url, byte[] data, String nameKey, String name, Map<String, String> params, Headers headers) throws IOException {
        RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), data);
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(FILE_MEDIA_TYPE)
                .addFormDataPart(nameKey, URLEncoder.encode(name, StandardCharsets.UTF_8), fileBody);
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue());
            }
        }

        MultipartBody body = builder.build();
        Request request = new Request.Builder()
                .post(body)
                .headers(headers)
                .url(url)
                .build();

        return call(request);
    }

    /**
     * 上传文件
     * @param url
     * @param file
     * @param nameKey
     * @param params
     * @return
     * @throws IOException
     */
    public static Response upload(String url, File file, String nameKey, Map<String, String> params) throws IOException {
        RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), file);
        MultipartBody.Builder builder = new MultipartBody.Builder()
            .setType(FILE_MEDIA_TYPE)
            .addFormDataPart(nameKey, URLEncoder.encode(file.getName(), StandardCharsets.UTF_8), fileBody);
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue());
            }
        }
        Headers headers = Headers.of(LogRequestIdConverter.REQUEST_ID, MDC.get(LogRequestIdConverter.REQUEST_ID));
        MultipartBody body = builder.build();
        Request request = new Request.Builder()
            .post(body)
            .headers(headers)
            .url(url)
            .build();

        return call(request);
    }

    public static void download(String url, File file) throws IOException {
        FileUtils.forceMkdir(file.getParentFile());
        String errorString = null;
        IOException ex = null;
        for (int i = 0; i < 3; i++) {
            Response response = get(url);
            if (!response.isSuccessful()) {
                errorString = getResponseBody(response).orElse(null);
                if (response.code() == 401 || response.code() == 403) {
                    break;
                }
                continue;
            }
            try (InputStream in = response.body().byteStream();
                OutputStream fs = new FileOutputStream(file)) {
                IOUtils.copy(in, fs);
                return;
            } catch (IOException e) {
                ex = e;
            }
        }
        if (ex != null) {
            throw ex;
        }
        throw new IOException(errorString);
    }

    public static Optional<String> getResponseBody(Response response) {
        try (ResponseBody body = response.body()) {
            return body == null ? Optional.empty() : Optional.of(body.string());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static Optional<byte[]> getBinaryResponseBody(Response response) {
        try (ResponseBody body = response.body()) {
            return body == null ? Optional.empty() : Optional.of(body.bytes());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
