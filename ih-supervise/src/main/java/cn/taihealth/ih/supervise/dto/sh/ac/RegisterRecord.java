package cn.taihealth.ih.supervise.dto.sh.ac;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 挂号
 */
@Data
public class RegisterRecord {

    private String serialNumber; //	复诊流水号 	互联网医院复诊诊疗系统中产生的唯一复诊编码 	是 	 varchar(64) 	需要调取《文件上传及验证接口》，上传问诊聊天记录音视频文件以及电子病历PDF文件。
    private String hosOrgCode; //	医院代码 	医院国家医保代码 12位 	是 	varchar(12)
    private String orgCode; //	登记号 	医疗许可证上的登记号 	是 	varchar(22) 	医疗许可证登记号
    private String hosName; //	医院名称 	机构名称 	是 	varchar(60) 	与医疗许可证商名称保持一致，不能用简称
    private String areaCode; //	机构区划代码 	上海区域代码（以全国地区代码表为准） 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String areaName; //	机构区划名称 	上海区域名称（以全国地区代码表为准）注：不要带“上海市” 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String numSourceId; //	号源ID 	和bookinginformation的numSourceId关联 	是 	varchar(64)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime; //	复诊申请时间 	患者申请复诊时间。 格式'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signInTime; //	患者签到时间 	格式'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
    private String duration; //	诊疗时长 	单位为"分钟" 	是 	varchar(3)
    private String organizationName; //	所属机构 	 	否 	varchar(50)
    private String klx; //	卡类型 	 	是 	varchar(50) 	0 ：社保卡 1 ：医保卡 2 ：统一自费就诊卡 9 ：其他卡
    private String kh; //	卡号 	 	是 	varchar(50)
    private String idType; //	身份证件类别代码 	按照卫标 CV02.01.101 身份证件类别代码执行编码。 	是 	varchar(2) 	01：居民身份证 02：居民户口本 03：护照 04：军官证 05：驾驶证 06：港澳居民来往内地通行证 07：台湾居民来往内地通行证 19：母亲身份证 99：其他法定有效证件
    private String idCard; //	身份证号 	 	是 	varchar(18)
    private String patientName; //	患者姓名 	 	是 	varchar(56)
    private int age; //	年龄 	 	是 	integer
    private String medicalInsuranceNumber; //	医保号 	患者医保号 	否 	varchar(50)
    private String phone; //	患者联系电话 	 	是 	varchar(11)
    private String deptCode; //	二级科室代码 	按照本院实际科室代码上传 	是 	varchar(15)
    private String deptName; //	二级科室名称 	按照本院实际科室名称上传 	是 	varchar(56)
    private String diagnoseSubject; //	诊疗科目 	 	是 	varchar(50) 	与资源上报中名称一致，并且在上报范围中。
    private String docName; //	医生姓名 	 	是 	varchar(56)
    private String doctorId; //	医生国家编码 	同医生信息上报的doctorId 	是 	varchar(50)
    private String diagnosisType; //	诊断编码类型 	目前监管是验证国家码，需要填写02，该字段作为保留字段 	是 	 varchar(2) 	01：上海市统一编码《疾病分类与代码》; 02：国标-95
    private String tcmDiagnosis; //	中医诊断 	如有多条，使用“|”进行分隔 	否 	varchar(255)
    private String icd; //	诊断ICD编码 	如有多条，使用“|”进行分隔 	是 	varchar(255) 	参见诊断编码类型
    private String diagnosis; //	诊断名称 	西医:按照上海市统一编码《疾病分类与代码》规范填写;中医:按国标-95 执行。若有多条，填写主要诊断。 	是 	varchar(100)
    private String lastDiagnosIsName; //	上次就诊诊断名称 	复诊患者在实体医院的诊断名称，如有多条，使用“|”进行分隔 	是 	varchar(255)
    private String diagnosticName; //	初步诊断名称 	如有多条，使用“|”进行分隔 	是 	varchar(255)
    private String chiefComplaint; //	主诉 	需要和生成的病历文件保持一致 	是 	varchar(5000)
    private String medicalHistory; //	现病史 	需要和生成的病历文件保持一致 	否 	varchar(5000)
    private String allergyHistory; //	过敏史 	需要和生成的病历文件保持一致 	否 	varchar(5000)
    private String pastDiseaseHistory; //	既往史 	需要和生成的病历文件保持一致 	否 	varchar(5000)
    private String physicalExamination; //	体格检查 	需要和生成的病历文件保持一致 	否 	varchar(5000)
    private String method; //	处理办法 	治疗意见，需要和生成的病历文件保持一致 	是 	varchar(5000)
    private String notes; //	备注 	需要和生成的病历文件保持一致 	否 	varchar(3000)
    private String companionName; //	陪诊人姓名 	 	否 	varchar(50)
    private String companionId; //	陪诊人身份证号 	 	否 	varchar(18)
    private String companionPhone; //	陪诊人联系电话 	 	否 	varchar(11)
    private int status; //	复诊状态 	记录状态，枚举值 	是 	Int(10) 	1：待接诊 2:复诊中 3：已完成 4：拒绝接诊 5：未接诊
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime; //	提交时间（提交预约时间） 	提交时间:格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiptTime; //	接单时间（医生接诊时间） 	提交时间:格式 'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	status=2（复诊中）、3（已完成）为必填。
    private String explanation; //	说明 	说明，拒绝接诊原因，拒接接诊需要退费。 	否 	varchar(200) 	status=4（拒绝接诊）为必填。
    private Integer evaluate; //	评价等级 	评价，枚举值 	否 	Int(2) 	5：非常满意 4：满意 3：一般 2：不满意 1：非常满意 status=3（已完成）为必填。
    private String evaluateContent; //	评价内容 	评价详细说明 	否 	varchar(1000) 	status=3（已完成）为必填。
    private String evaluateLabel; //	评价标签 	其它自定义标签 	否 	varchar(50) 	根据实际上传
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate; //	结束时间 	格式'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	status=3（已完成）为必填。
    private String isComplaint; //	是否产生投诉 	 	否 	varchar(1) 	0：否  1：是 status=3（已完成）为必填。
    private String imageVideoFileNumber; //	图文音视频档案号 	医院自编码，确保在本院内始终唯一。互联网医院复诊全程的图文音视频资料的留痕 	否  	  varchar(64) 	通过非结构化文件上传接口上传，若未上传，需在本院存储以备检查。 status=3（已完成）为必填。
    private String imageVideoFileUrl; //	图文音视频URL 	互联网医院复诊全程的图文音视频资料存储地址 	否 	longvarchar 	status=3（已完成）为必填，如是图文，传图文地址，如是音视频传音视频地址。未上传音视频文件，必须上传,上传格式json字符串 硬性要求：json为list<Object> 对象属性: time-视频发生时间 sort-视频顺序 url-视频地址 client-1患者端 2医生端  如：[{"time":"2024-01-01 19:00:01", "sort":1, url:"", client:1}, {"time":"2024-01-01 19:00:01", "sort":2, url : "", client:2}]
    private String qmyw; //	签名原文 	 	否 	text
    private String sjywqmz; //	数据原文签名值 	复诊病历CA签名，于上传文件保持一致，用于签名验证。 	否 	varchar(1024) 	status=3（已完成）为必填。
    private String yhryzsxlh; //	医护人员证书序列号 	接诊医生的CA证书序列号 	否 	 varchar(32) 	status=3（已完成）为必填。
    private String upDataType; //	上报数据类型 	 	否 	varchar(10) 	1. 实时上报 2.批量补传
    private String businessBeQuartered = "0"; //	是否为入驻业务 	如果和第三方联合办医，则为是，其余为否 	是 	varchar(1) 	0：否  1：是
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date upDate; //	上报时间 	尽量与时间戳一致，格式 'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	2019/12/10 16:34

}
