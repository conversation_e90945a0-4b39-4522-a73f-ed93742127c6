package cn.taihealth.ih.supervise.dto.sh.ba;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class HospitalAnnualInspectionInfo {
    /**
     * 医院代码 (医院国家医保代码 12 位)
     * 说明: 医院国家医保代码 12 位
     */
    private String hosOrgCode;

    /**
     * 登记号 (医疗许可证上的登记号)
     * 说明: 医疗许可证上的登记号
     */
    private String orgCode;

    /**
     * 医院名称 (机构名称)
     * 说明: 与医疗许可证上名称保持一致，不能用简称
     */
    private String hosName;

    /**
     * 机构区划代码
     * 说明: 上海区域代码（以全国地区代码表为准）
     * 备注: 见字典规范 /行政区划代码.pdf
     */
    private String areaCode;

    /**
     * 机构区划名称
     * 说明: 上海区域名称（以全国地区代码表为准）注：不要带“上海市”
     * 备注: 见字典规范 /行政区划代码.pdf
     */
    private String areaName;

    /**
     * 机构名称
     * 说明: 按医院名称上传
     */
    private String orgName;

    /**
     * 登记时间
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registrationDate;

    /**
     * 上传时间
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10 16:34:32
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadedDate;

    /**
     * 来源
     * 说明: 不一致以卫监所信息为准：0：卫监所, 1：医院填报
     */
    private String resource;

    /**
     * 三级等保是否更新
     * 说明: 0：否, 1：是
     */
    private String updateThreelevelEqualprotection;

    /**
     * 派 出（分支）机构数量
     */
    private Integer branchNumber;

    /**
     * 职工总数
     */
    private Integer workForce;

    /**
     * 客户服务人数总数
     */
    private Integer serviceCustomerSum;

    /**
     * 日均坐诊医生数
     */
    private Integer averageDoctorsNumber;

    /**
     * 许可证号码
     * 说明: 需要调取《文件上传及验证接口》，上传年检校验记录PDF 文件。
     */
    private String licenceNumber;

    /**
     * 许可证有效期
     * 说明: 格式 'yyyy-MM-dd'
     * 备注: 例如 2019-12-10
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenceExpiration;

    /**
     * 业务用房面积
     */
    private BigDecimal premisesArea;

    /**
     * 总收入（万元）
     */
    private BigDecimal totalIncome;

    /**
     * 总支出（万元）
     */
    private BigDecimal totalExpenditure;

    /**
     * 总资产（万元）
     */
    private BigDecimal totalAssets;

    /**
     * 流动资产（万元）
     */
    private BigDecimal flowAssets;

    /**
     * 对外资产（万元）
     */
    private BigDecimal externalAssets;

    /**
     * 固定资产（万元）
     */
    private BigDecimal fixedAssets;

    /**
     * 无形资产及开办费（万元）
     */
    private BigDecimal intangibleAssets;

    /**
     * 负债（万元）
     */
    private BigDecimal liabilities;

    /**
     * 净资产（万元）
     */
    private BigDecimal netAssets;

    /**
     * 三级等保编码
     * 说明: 需要调取《文件上传及验证接口》，上传信息安全等级保护证书。
     */
    private String threelevelEqualprotectionCode;
}
