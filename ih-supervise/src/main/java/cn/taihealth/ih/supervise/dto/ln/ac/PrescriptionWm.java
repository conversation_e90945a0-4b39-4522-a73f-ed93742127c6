package cn.taihealth.ih.supervise.dto.ln.ac;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 西药处方
 */
@Data
public class PrescriptionWm {

    // 患者就诊卡证类型
    private String idcardTypeCode; // 字符，长度2

    // 患者就诊卡证号码
    private String idcardNo; // 字符，长度24

    // 姓名
    private String name; // 字符，长度40

    // 性别代码
    private String genderCode; // 字符，长度1

    // 出生日期
    private String birthdate; // 日期，长度8

    // 门诊编号
    private String visitNo; // 字符，长度36

    // 处方编码
    private String hosRxCode; // 字符，长度36

    // 处方分类代码
    private String rxTypeCode; // 字符，长度2

    // 处方药品总金额(元)
    private String rxAmount; // 数值，长度10，精度2

    // 处方备注说明
    private String rxDescription; // 字符，长度400

    // 开处方医疗机构编码
    private String unifiedOrgCode; // 字符，长度18

    // 开处方医疗机构名称
    private String orgName; // 字符，长度80

    // 开处方科室编码
    private String deptCode; // 字符，长度50

    // 开处方科室名称
    private String deptName; // 字符，长度40

    // 开处方科室对应诊疗科目 编码
    private String deptClassCode; // 字符，长度9

    // 开处方科室对应诊疗科目 名称
    private String deptClassName; // 字符，长度40

    // 开处方医师身份证号
    private String doctIdcardNo; // 字符，长度18

    // 开处方医师姓名
    private String doctName; // 字符，长度40

    // 开处方日期时间
    private String rxDatetime; // 日期时间，长度15

    // 开处方医师电子签名
    private String doctCaSign; // 字符，长度4000

    // 处方流向类型
    private String rotateTypeCode; // 字符，长度1

    // 处方明细
//    private PrescriptionWMDetail[] items; // 是，该处方所包含项目，处方明细数组。参见“西药处方明细”
    private PrescriptionWMDetail[] items;
}
