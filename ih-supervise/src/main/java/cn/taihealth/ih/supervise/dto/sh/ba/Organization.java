package cn.taihealth.ih.supervise.dto.sh.ba;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 医疗机构
 */
@Data
public class Organization {

    private String hosOrgCode; //	医 院 代码 	医院国家医保代码12位，如没有医保代码请咨询监管平台。 	是 	varchar(12) 	需要调取《文件上传及验证接口》，上传医疗许可证PDF文件。
    private String orgCode; //	登记号 	医疗许可证上的登记号 	是 	varchar(22) 	登记号
    private String hosName; //	医院名称 	机构名称 	是 	varchar(60) 	与医疗许可证商名称保持一致，不能用简称
    private String areaCode; //	机构区划代码 	上海区域代码（以全国地区代码表为准） 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String areaName; //	机构区划名称 	上海区域名称（以全国地区代码表为准）注：不要带“上海市” 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String hospitalAdd; //	医院地址 	 	是 	varchar(200)
    private String hospitalRule; //	预约挂号须知 	 	否 	varchar(200)
    private String hospitalWeb; //	医院网址 	 	否 	varchar(50)
    private String trafficGuide; //	交通指南 	 	否 	varchar(200)
    private String hospitalDesc; //	医院简介 	 	否 	varchar(500)
    private String hospitalTel; //	联系电话 	 	否 	varchar(11)
    private String hospitalGrade; //	医 院 级别 	参考《上海市卫生资源与  医疗服务调查制度》   	是 	varchar(1) 	0：社区卫生服务中心 1：一级医院  2：二级医院  3：三级医院 9：未评级
    private String hospitalLevel; //	医 院 等级 	参考《上海市卫生资源与  医疗服务调查制度》  	是 	varchar(1) 	0:特 1:甲 2:乙 3:丙 4:未定级
    private String hospitalType; //	医 院 类别 	编码。按照“卫生机构(组  织 ) 分类与代码 (WS218-  2002）”编码执行。  	是 	varchar(1) 	1：综合性医院 2：专科医院
    private String payMode; //	支 付 方式 	多选，用分号；分隔格式（1;2;3;）  	是 	varchar(6) 	1：第三方支付 2：诊疗卡支付 3：到院支付
    private String orderMode; //	预 约 方式 	 	是 	varchar(1) 	1：有卡预约 2：无卡预约
    private String isRegister; //	是 否 开展预约 	 	是 	varchar(1) 	0：否  1：是
    private String isSpTime; //	是 否 支持 分 时段 	 	是 	varchar(1) 	0：不支持   1：支持
    private String shortName; //	医 院 简称 	 	是 	varchar(100)
    private String orgSort; //	医 院 属性 	 	是 	varchar(1) 	1：市属 2：区属 3：社会办医疗机构
    private String hospitalImage; //	医 院 图片 	图片小于 256K，格式Base64 	否 	varchar(max)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate; //	创 建 日期 	格式 'yyyy - MM - dd ' 	是 	timestamp 	43809
    private String orgSecondName; //	机 构 第二名称 	 	否 	varchar(100)
    private String hostName; //	法 定 代表人 	 	是 	varchar(50)
    private String keyWorker; //	主 要 负责人 	 	是 	varchar(50)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expirationStart; //	许 可 证有 效 期（起） 	格式 'yyyy - MM - dd' 	是 	timestamp 	43809
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expirationEnd; //	许 可 证有 效 期（止） 	格式 'yyyy - MM - dd' 	是 	timestamp 	43809
    private String orgMark; //	机 构 标识 	卫统22位编码 	否 	varchar(22)
    private String subSidiary; //	单 位 隶属关系 	 	是 	varchar(1) 	1：市属 2：区属 3：社会办医疗机构
    private String platform; //	部 署 平台 	互联网医院部署平台 	是 	varchar(50) 	部署平台名称，自建和互联网医院名称一致，入驻填写入驻平台。
    private String medicalSubjects; //	诊 疗 科目 	在备案的诊疗科目范围，如有多个用“，”分隔 	是 	varchar(1000)
    private String ethnicDistrict; //	机 构 所在 地 民族 自 治地 方 标志 	 	否 	varchar(1) 	0：否  1：是
    private String infoSecurityLevelProtect; //	信 息 安全 等 级保护 	默认三级 	是 	varchar(1) 	1：一级 2：二级 3：三级
    private String infoSecuritLevelProtectNumber; //	信 息 安全 等 级保 护 证书编号 	 	是 	varchar(50)
    private String supplyPowerEquipment = "1"; //	是 否 具备 双 路供 电 或紧 急 发电设备 	默认是 	是 	varchar(1) 	0：否  1：是
    private BigDecimal initialFunds; //	开 办 资金 额 数（万元） 	 	是 	decimal(18,2)
    private String branch; //	是 否 分支机构 	 	否 	varchar(1) 	0：否  1：是
    private String postCode; //	邮 政 编码 	 	否 	varchar(50)
    private BigDecimal machInearea; //	机 房 面积（m²） 	 	否 	decimal(18,2)
    private String unifiedInfoCode; //	统 一 社会 信 息代码 	 	是 	varchar(50)
    private String thirdInstitutions; //	是 否 有第 三 方机 构 参与 	 	否 	varchar(1) 	0：否  1：是
    private String thirdInstitutionsName; //	第 三 方机 构 名称 	如有多点第三方机构，需填写多个机构名称，分号分隔 	否 	varchar(100)
    private String thirdOgrCreditCode; //	第 三 方机 构 统一 信 用代码 	 	否 	varchar(50)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date argrementStartDate; //	协 议 有 效 期（起） 	格式 'yyyy - MM - dd' 	否 	timestamp 	43809
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date argrementEndDate; //	协 议 有 效 期（止） 	格式 'yyyy - MM - dd' 	否 	timestamp 	43809
    private String registrationNo; //	证 书 登记号 	 	是 	varchar(50)
    private String issuingAuthority; //	发 证 机关 	 	是 	varchar(50)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date establishmentDate; //	机 构 成立日期 	格式 'yyyy - MM - dd' 	是 	date
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDate; //	发 证 日期 	格式 'yyyy - MM - dd ' 	是 	date
    private String selypayMode; //	自 费 方式 	就医费用中自费部分的支付方式（支付宝；微信；银联  三位数字 1 代表支持，0 代表不支持，如都不支持000，都支持111）  	是 	varchar(6) 	示例： 111：三个都支持 100：只支持支付宝 010：只支持微信
    private String furtherConsultation; //	复 诊 判断依据 	 	是 	varchar(1) 	1:诊断代码 2:科室代码 3：诊断代码与科室代码
    private String furtherConsultationResource; //	复 诊 判断 数 据来源 	 	是 	varchar(1) 	1：院内就诊数据 2：市级健康档案 3：医联健康档案 4：区级健康档案
    private String furtherConsultationTime; //	复 诊 判断时效 	单位为月 	是 	varchar(50) 	不限制为0
    private String comeSource; //	来源 	 	否 	varchar(1) 	0：卫监所 1：医院填报
    private Integer thirdOrgNum; //	第 三 方机构数 	 	否 	Int(10)
    private String orgManageType; //	机 构 分类 管 理类别 	 	是 	varchar(1) 	1：公益性医院     2：经营性医院
    private String entityHosOrgCode; //	实 体 医院 医 疗组 织 机构代码 	 	是 	varchar(50)
    private String hospitalTelTotal; //	联 系 电话（总机/ 查 询台） 	 	否 	varchar(20)
    private String email; //	单 位 电子 信 箱 （ E-mail） 	 	否 	varchar(20)
    private String orgDescribe; //	机 构 描述 	 	否 	varchar(500)
    private String frameDescribe; //	架 构 描述 	 	否 	varchar(500)
    private BigDecimal lng; //	经度 	 	否 	decimal(9,6)
    private BigDecimal lat; //	纬度 	 	否 	decimal(9,6)
    private String provinceCode; //	省编码 	 	否 	varchar(20)
    private String province; //	省名称 	 	否 	varchar(50)
    private String cityCode; //	市编码 	 	否 	varchar(20)
    private String city; //	市名称 	 	否 	varchar(50)
    private String countyCode; //	区编码 	 	否 	varchar(20)
    private String county; //	区名称 	 	否 	varchar(50)
    private String houseNumber; //	门牌号 	 	否 	varchar(200)
    private String medicalSubjectsBak; //	备 案 诊疗科目 	 	是 	varchar(1000) 	多个用“，”间隔
    private String levelTestResult; //	等 保 测试结果 	 	否 	varchar(1) 	1：优 2：良 3：差 4：无
    private String userLicenseDocumentCode; //	第 三 方协 议 文档编码 	 	否 	varchar(500) 	需要通过文件上传接口上传对应协议
    private String formsOfOwnership; //	所 有 制形式 	 	是 	varchar(1) 	1：私有制 2：公有制 3：混合所有制

}
