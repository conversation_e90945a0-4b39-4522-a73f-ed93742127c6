package cn.taihealth.ih.supervise.dto.sh.ac;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 图片/视频文件 URL 数据传输对象 (DTO)
 * 用于封装从前端接收或向前端返回的包含 URL、排序、客户端类型等信息的数据。
 */
@Data // 自动生成 Getters, Setters, toString(), equals(), hashCode()
@NoArgsConstructor // 自动生成无参构造函数
@AllArgsConstructor // 自动生成全参构造函数
public class ImageVideoFileUrlDto {

    /**
     * 文件 URL 地址
     * 对应 JSON 中的: "url": ""
     */
    private String url;

    /**
     * 时间戳 (例如上传时间或创建时间)
     * 对应 JSON 中的: "time": "2024-01-01 19:00:01"
     * @JsonFormat 注解确保了字符串和 LocalDateTime 类型的正确转换。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;

    /**
     * 排序序号
     * 对应 JSON 中的: "sort": 2
     */
    private Integer sort;

    /**
     * 客户端类型
     * (例如: 1-PC端, 2-移动端)
     * 对应 JSON 中的: "client": 2
     */
    private Integer client;
}
