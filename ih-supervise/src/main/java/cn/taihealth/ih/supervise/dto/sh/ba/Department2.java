package cn.taihealth.ih.supervise.dto.sh.ba;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 互联网二级科室
 */
@Getter
@Setter
@NoArgsConstructor
public class Department2 {

    private String hosOrgCode; //	医院代码 	医院国家医保代码12位 	是 	varchar(12)
    private String orgCode; //	登记号 	医疗许可证上的登记号 	是 	varchar(22) 	登记号
    private String hosName; //	医院名称 	机构名称 	是 	varchar(60) 	与医疗许可证商名称保持一致，不能用简称
    private String areaCode; //	机构区划代码 	上海区域代码（以全国地区代码表为准） 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String areaName; //	机构区划名称 	上海区域名称（以全国地区代码表为准）注：不要带“上海市” 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String oneDeptCode; //	一级科室代码 	按照本院实际一级科室代码上传，和 firstdepartmen 表dept_code一致 	是 	varchar(15)
    private String oneDeptName; //	一级科室名称 	按照本院实际一级科室名称上传，和firstdepartment 表dept_name一致 	是 	varchar(30)
    private String deptCode; //	二级科室代码 	按照本院实际二级科室代码上传 	是 	varchar(15)
    private String deptName; //	二级科室名称 	按照本院实际二级科室名称上传 	是 	varchar(30)
    private String normDeptCode; //	标准二级科室代码 	按照字典规范要求的科室编码填写  	是 	varchar(15) 	参见 字典规范/标准科室字典表.pdf
    private String normDeptName; //	标准二级科室名称 	按照字典规范要求的科室名称填写  	是 	varchar(30) 	参见 字典规范/标准科室字典表.pdf
    private String deptDesc; //	科室简介 	 	是 	varchar(500)
    private String isRegister; //	是否开展预约 	 	是 	varchar(1) 	需要和对应的一级科室保持一致： 0:否   1:是
    private String isDelete; //	删除标志 	 	是 	varchar(1) 	0:否   1:是
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate; //	创建日期 	格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
    private String status; //	停用标志 	 	是 	varchar(1) 	0:否   1:是
    private String medicalSubjectsBak; // 备案诊疗科目 是 varchar(50) 与医疗许可证文件诊疗科目中选择。

}
