package cn.taihealth.ih.supervise.service.ln;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.domain.MedicalCase;
import cn.taihealth.ih.domain.MedicalCaseDisease;
import cn.taihealth.ih.domain.Ticket;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.dict.SuggestTypeConvert;
import cn.taihealth.ih.domain.enums.ClinicType;
import cn.taihealth.ih.domain.enums.SettingKey;
import cn.taihealth.ih.domain.enums.ThirdOrderType;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.service.LinkService;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.hospital.order.DiagnosisCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionRepository;
import cn.taihealth.ih.repo.live.TencentVideoRecordingRepository;
import cn.taihealth.ih.repo.order.OrderHistoryRecordRepository;
import cn.taihealth.ih.repo.order.OrderImageRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.repo.order.OrderWorkerRepository;
import cn.taihealth.ih.supervise.constants.ln.DeptClassEnum;
import cn.taihealth.ih.supervise.dto.ln.ac.*;
import cn.taihealth.ih.supervise.dto.ln.ba.Department;
import cn.taihealth.ih.supervise.dto.ln.ba.DepartmentClass;
import cn.taihealth.ih.supervise.dto.ln.ba.Doctor;
import cn.taihealth.ih.supervise.dto.ln.ba.Organization;
import cn.taihealth.ih.supervise.utils.DataUtils;
import com.beust.jcommander.internal.Lists;
import com.gitq.jedi.common.web.UrlUtils;
import com.gitq.jedi.context.AppContext;
import com.xikang.medical.sdk.bean.supervise.UrlInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class LiaoningDataCreateComponent {

    private final HospitalRepository hospitalRepository;

    private final TicketRepository ticketRepository;

    private final OrderWorkerRepository orderWorkerRepository;

    private final OrderRepository orderRepository;

    private final DeptRepository deptRepository;

    private final MedicalWorkerRepository medicalWorkerRepository;

    private final MedicalCredentialsRepository medicalCredentialsRepository;

    private final PrescriptionOrderRepository prescriptionOrderRepository;

    private final PrescriptionRepository prescriptionRepository;

    private final MedicalCaseRepository medicalCaseRepository;

    private final OrderHistoryRecordRepository orderHistoryRecordRepository;

    private final OfflineHospitalRepository offlineHospitalRepository;

    private final UserRepository userRepository;

    private final PatientRepository patientRepository;

    private final UploadRepository uploadRepository;

    private final TencentMessageRecordingRepository messageRecordingRepository;

    private final VideoRoomRepository videoRoomRepository;

    private final TencentVideoRecordingRepository videoRecordingRepository;

    private final WechatOrderRepository wechatOrderRepository;

    private final WechatOrderRefundRepository wechatOrderRefundRepository;

    private final SystemSettingRepository settingRepository;

    private final ApplicationProperties applicationProperties;

    private final PrescriptionOrderCaRepository prescriptionOrderCaRepository;

    private final DiagnosisCaRepository diagnosisCaRepository;

    private final MedicalCaseDiseaseRepository medicalCaseDiseaseRepository;
    private final HospitalDictionaryRepository hospitalDictionaryRepository;
    private final OrderImageRepository orderImageRepository;

    @Transactional
    public Organization getOrganization(Hospital hospital) {
        Organization organization = new Organization();
        organization.setUnifiedOrgCode(hospital.getLicenseNumber());

        OfflineHospital offlineHospital = offlineHospitalRepository.getById(hospital.getOfflineHospitals().get(0).getId());

        organization.setOrgTypeCode(offlineHospital.getCategory());
        organization.setHosClassCode(DataUtils.rankToCode(offlineHospital.getRank()));
        organization.setHosDegreeCode(DataUtils.gradeToCode(offlineHospital.getGrade()));
        organization.setAddressCode(offlineHospital.getAddressCode());
        organization.setOpenDatetime(DataUtils.toDateStr8(offlineHospital.getOpenDatetime()));
        organization.setAddress(offlineHospital.getAddress());
        organization.setWebsiteUrl(offlineHospital.getWebsite());
        organization.setLegalRepresentative(offlineHospital.getLegalRepresentative());
        organization.setPrincipal(hospital.getContact());
        organization.setTelNo(hospital.getPhone());
        return organization;
    }

    @Transactional
    public Department getDepartment(Long deptId) {
        Dept dept = deptRepository.findById(deptId).orElseThrow();
        Department department = new Department();
        department.setUnifiedOrgCode(dept.getHospital().getLicenseNumber());
        department.setDeptClassCode(dept.getSubjectCode());
        department.setDeptClassName(DeptClassEnum.getDescByCode(dept.getSubjectCode()));
        department.setDeptCode(dept.getDeptCode());
        department.setDeptName(dept.getDeptName());
        String ifAccepted = (dept.getEnabled() != null && dept.getEnabled()) ? "1" : "0";
        department.setIfAccepted(ifAccepted);//写死1
        return department;
    }

    @Transactional
    public DepartmentClass getDepartmentClass(Long deptId) {
        DepartmentClass departmentClass = new DepartmentClass();
        Dept dept = deptRepository.findById(deptId).orElseThrow();
        Hospital hospital = hospitalRepository.getById(dept.getHospital().getId());
        departmentClass.setUnifiedOrgCode(hospital.getLicenseNumber());
        departmentClass.setDeptClassCode(dept.getSubjectCode());
        departmentClass.setDeptClassName(DeptClassEnum.getDescByCode(dept.getSubjectCode()));
        departmentClass.setAuthorizeDate(DataUtils.toDateStr8(hospital.getLicenseCreateDate()));
        departmentClass.setEffectiveDate(DataUtils.toDateStr8(hospital.getLicenseStartDate()));
        departmentClass.setExpireDate(DataUtils.toDateStr8(hospital.getLicenseEndDate()));
        List<HospitalDictionary> ds = hospitalDictionaryRepository.findAllByHospitalAndParentCode(hospital, "ZHENLIAOKEMUDAIMA");
        HospitalDictionary di = ds.stream().filter(u -> Objects.equals(dept.getSubjectCode(), u.getCode()))
                .findFirst().orElse(null);
        if (di == null) {
            departmentClass.setIfAccepted("0");
        } else {
            String ifAccepted = (di.getEnabled() != null && di.getEnabled()) ? "1" : "0";
            departmentClass.setIfAccepted(ifAccepted);
        }
        return departmentClass;
    }

    @Transactional
    public Doctor getDoctor(MedicalWorker medicalWorker) {
        Doctor doctor = new Doctor();
        Hospital hospital = hospitalRepository.getById(medicalWorker.getHospital().getId());
        doctor.setUnifiedOrgCode(hospital.getLicenseNumber());
        doctor.setIdcardTypeCode("01");
        doctor.setIdcardNo(medicalWorker.getIdentity());

        User user = userRepository.findById(medicalWorker.getUser().getId()).orElseThrow();

        doctor.setName(user.getFullName());
        doctor.setGenderCode(DataUtils.genderToCode(medicalWorker.getGender()));
        doctor.setBirthdate(DataUtils.extractBirthdayFromIDCard(medicalWorker.getIdentity()));


        medicalCredentialsRepository.findOneByMedicalWorker(medicalWorker).ifPresent(a -> doctor.setPractisingCertCode(a.getPractisingNumber()));
        doctor.setPractisingTypeCode(medicalWorker.getPracticeCategory());
        doctor.setPractisingScopeCode(medicalWorker.getPracticeScope());
        doctor.setDoctTitleCode(medicalWorker.getTitle());

        if (medicalWorker.getOfflineHospital() != null) {
            OfflineHospital offlineHospital = offlineHospitalRepository.getById(medicalWorker.getOfflineHospital().getId());
            doctor.setPractisingOrgCode1(offlineHospital.getOrgTypeCode());
        }
        doctor.setIfAccepted(medicalWorker.isIfAccepted()? "1" : "0");
        return doctor;
    }

    @Transactional
    public RegisterRecord getRegisterRecord(Long orderId) {
        RegisterRecord registerRecord = new RegisterRecord();
        Order order = orderRepository.findById(orderId).orElseThrow();
        if (order.getOrderType() != ClinicType.OUT && order.getOrderType() != ClinicType.CONSULT) {
            return null;
        }

        // 患者
        Patient patient = patientRepository.findById(order.getPatient().getId()).orElseThrow();


        registerRecord.setIdcardTypeCode(patient.getCardType().getCode());
        registerRecord.setIdcardNo(patient.getIdCardNum());
        registerRecord.setName(patient.getName());
        registerRecord.setGenderCode(DataUtils.genderToCode(patient.getGender()));
        registerRecord.setBirthdate(DataUtils.toDateStr8(patient.getBirthday()));

        // 医院
        Hospital hospital = hospitalRepository
                .findById(order.getHospital().getId()).orElseThrow();

        registerRecord.setUnifiedOrgCode(hospital.getLicenseNumber());
        registerRecord.setOrgName(hospital.getOrgName());
        // 科室
        Dept dept = order.getDept();
        registerRecord.setDeptCode(dept.getDeptCode());
        registerRecord.setDeptName(dept.getDeptName());
        registerRecord.setDeptClassCode(dept.getSubjectCode());
        registerRecord.setDeptClassName(DeptClassEnum.getDescByCode(dept.getSubjectCode()));

        // 专家号
        if (order.getDoctor() == null) {
            registerRecord.setIfExpert("0");
        } else {
            // 医生
            registerRecord.setIfExpert("1");
            MedicalWorker medicalWorker = medicalWorkerRepository
                    .findById(order.getDoctor().getId()).orElseThrow();
            User medicalWorkerUser = userRepository
                    .findById(medicalWorker.getUser().getId()).orElseThrow();
            registerRecord.setDoctIdcardNo(medicalWorker.getIdentity());
            registerRecord.setDoctName(medicalWorkerUser.getFullName());

        }


        // 时间
        Date visitDate = order.getAdmissionDate() == null ? new Date() : order.getAdmissionDate();
        registerRecord.setVisitDate(DataUtils.toDateStr8(visitDate));
        registerRecord.setVisitTime(DataUtils.toTimeStr6(visitDate));
        registerRecord.setVisitNo(orderId.toString());
        if (order.getOrderType() == ClinicType.OUT) {
            registerRecord.setVisitType("1");
        } else if (order.getOrderType() == ClinicType.CONSULT) {
            registerRecord.setVisitType("2");
        }
        registerRecord.setExaminFee(DataUtils.divideToFloat2(order.getRegistrationFee(), 100));
        registerRecord.setRecordDateTime(DataUtils.toDateStr15(order.getCreatedDate()));
        registerRecord.setVisitTypeCode(DataUtils.visitTypeToCode(order.getVisitType()));


        return registerRecord;
    }

    @Transactional
    public CancelRegister getCancelRegister(Long orderId) {
        CancelRegister cancelRegister = new CancelRegister();
        Order order = orderRepository.findById(orderId).orElseThrow();
        Hospital hospital = hospitalRepository
                .findById(order.getHospital().getId()).orElseThrow();

        cancelRegister.setVisitNo(orderId.toString());
        cancelRegister.setUnifiedOrgCode(hospital.getLicenseNumber());
        cancelRegister.setCancelReason(order.getEndedReason());
        cancelRegister.setCancelDateTime(DataUtils.toDateStr15(order.getEndedDate()));

        return cancelRegister;
    }

    @Transactional
    public MedicalRecord getMedicalRecord(Long orderId) {
        MedicalRecord medicalRecord = new MedicalRecord();
        Order order = orderRepository.findById(orderId).orElseThrow();
        Patient patient = patientRepository
                .findById(order.getPatient().getId()).orElseThrow();

        // 患者
        medicalRecord.setIdcardTypeCode(patient.getCardType().getCode());
        medicalRecord.setIdcardNo(patient.getIdCardNum());
        medicalRecord.setName(patient.getName());
        medicalRecord.setGenderCode(DataUtils.genderToCode(patient.getGender()));
        medicalRecord.setBirthdate(DataUtils.toDateStr8(patient.getBirthday()));
        medicalRecord.setVisitNo(orderId.toString());
        // 主诉
        medicalRecord.setSubjComplaint(order.getDescription());

        MedicalCase medicalcase = medicalCaseRepository.findByOrderId(order.getId()).orElseThrow();
        List<MedicalCaseDisease> caseDiseases = medicalCaseDiseaseRepository.findByMedicalCaseId(medicalcase.getId());
        if (CollectionUtil.isNotEmpty(caseDiseases)) {
            medicalRecord.setMdDisCode(caseDiseases.get(0).getDiseaseCode());
            medicalRecord.setMdDisName(caseDiseases.get(0).getDiseaseName());
        }

        medicalRecord.setTreatMeas(Optional.ofNullable(medicalcase.getSummary()).orElse("无"));

        // 医疗机构，科室，医生相关信息
        Hospital hospital = hospitalRepository
                .findById(order.getHospital().getId()).orElseThrow();
        medicalRecord.setUnifiedOrgCode(hospital.getLicenseNumber());
        medicalRecord.setOrgName(hospital.getOrgName());
        // 科室
        Dept dept = deptRepository
                .findById(order.getDept().getId()).orElseThrow();
        medicalRecord.setDeptCode(dept.getDeptCode());
        medicalRecord.setDeptName(dept.getDeptName());
        medicalRecord.setDeptClassCode(dept.getSubjectCode());
        medicalRecord.setDeptClassName(DeptClassEnum.getDescByCode(dept.getSubjectCode()));
        // 医生
        MedicalWorker medicalWorker = medicalWorkerRepository
                .findById(order.getDoctor().getId()).orElseThrow();

        User medicalWorkerUser = medicalWorker.getUser();
        medicalRecord.setDoctIdcardNo(medicalWorker.getIdentity());
        medicalRecord.setDoctName(medicalWorkerUser.getFullName());
        medicalRecord.setDoctCaSign(order.getDiagnosisCa());

        medicalRecord.setVisitTimeStart(DataUtils.toDateStr15(order.getCreatedDate()));
        medicalRecord.setVisitTimeEnd(DataUtils.toDateStr15(order.getUpdatedDate()));

        SystemSetting urlPrefix = settingRepository.findOneByKey(SettingKey.NOTIFY_USR_URL_PREFIX).orElseThrow();

        String visitImRecordUrl = UrlUtils.concatSegments(urlPrefix.getValue(), "/hospcode-" + hospital.getCode()) + "/out/visitimrecord/" + orderId;
        String firstVisitRecordUrl = UrlUtils.concatSegments(urlPrefix.getValue(), "/hospcode-" + hospital.getCode()) + "/out/firstvisit/" + orderId;
        medicalRecord.setReviewUrl(visitImRecordUrl);


        // 初诊记录
        FirstVisitRecord firstVisitRecord = new FirstVisitRecord();
        List<UrlInfo> attachments = Lists.newArrayList();
        UrlInfo urlInfo = new UrlInfo();
        urlInfo.setUrl(firstVisitRecordUrl);
        attachments.add(urlInfo);
        firstVisitRecord.setAttachment(ArrayUtil.toArray(attachments, UrlInfo.class));
        medicalRecord.setFirstVisitRecord(firstVisitRecord);

        return medicalRecord;
    }

//    public static void main(String[] args) {
//        String urlPrefix1 = "https://test.taihealth.cn/user/";
//        String urlPrefix2 = "https://test.taihealth.cn/user";
//        String hospitalCode = "fxkzyy";
//        String orderId = "123456";
//
//        System.out.println(UrlUtils.concatSegments(urlPrefix1,  "/hospcode-" + hospitalCode + "/out/visitimrecord/" + orderId));
//        System.out.println(UrlUtils.concatSegments(urlPrefix2,  "/hospcode-" + hospitalCode + "/out/visitimrecord/" + orderId)); ;
//    }

    /**
     * 诊疗完成上报
     * @param orderId
     * @return
     */
    @Transactional
    public ConsultRecord getConsultRecord(Long orderId) {
        Optional<Order> optionalOrder = orderRepository.findById(orderId);
        if (optionalOrder.isPresent()) {
            Order order = optionalOrder.get();
            ConsultRecord consultRecord = new ConsultRecord();
            consultRecord.setVisitNo(orderId.toString());

            Hospital hospital = hospitalRepository
                    .findById(order.getHospital().getId()).orElseThrow();
            consultRecord.setUnifiedOrgCode(hospital.getLicenseNumber());
            consultRecord.setOrgName(hospital.getOrgName());

            Dept dept = deptRepository
                    .findById(order.getDept().getId()).orElseThrow();
            consultRecord.setDeptCode(dept.getDeptCode());
            consultRecord.setDeptName(dept.getDeptName());
            consultRecord.setDeptClassCode(dept.getSubjectCode());
            consultRecord.setDeptClassName(DeptClassEnum.getDescByCode(dept.getSubjectCode()));
//            consultRecord.setDeptClassName(dept.getSubjectName());

            MedicalWorker medicalWorker = medicalWorkerRepository
                    .findById(order.getDoctor().getId()).orElseThrow();

            User user = userRepository
                    .findById(medicalWorker.getUser().getId()).orElseThrow();

            consultRecord.setDoctIdcardNo(medicalWorker.getIdentity());
            consultRecord.setDoctName(user.getFullName());
            consultRecord.setDoctCaSign(order.getDiagnosisCa());

            Patient patient = patientRepository
                    .findById(order.getPatient().getId()).orElseThrow();


            consultRecord.setName(patient.getName());
            consultRecord.setGenderCode(DataUtils.genderToCode(patient.getGender()));
            consultRecord.setAge(DataUtils.getAgeByIdCard(patient.getIdCardNum()));// 根据birthday转成生日
            consultRecord.setTelNum(patient.getMobile());


            consultRecord.setConsultationType(DataUtils.visitTypeToCode(order.getVisitType()));//

            consultRecord.setOnsultationAttribute("1");
            consultRecord.setContent(order.getDescription());
            // 咨询内容附件
            List<OrderImage> orderImagesDB = orderImageRepository.findAllByOrder(order);
            List<OrderImage> orderImageList = orderImagesDB.stream().filter(u -> u.getType() == OrderImage.Type.ORDER)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderImageList)) {
                UrlInfo[] attachment = orderImageList.stream()
                        .map(orderImage -> {
                            UrlInfo urlInfo = new UrlInfo();
                            String url = AppContext.getInstance(LinkService.class).urlOfUpload(orderImage.getImage());
                            if (url != null) {
                                // 监管平台访问文件的时候不需要权限
                                urlInfo.setUrl(url.replace("/raw/", "/rawu/"));
                                return urlInfo;
                            } else {
                                log.warn("URL 为 null，跳过该 orderImage: " + orderImage.getId());
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .toArray(UrlInfo[]::new);
                consultRecord.setAttachment(attachment);
            }
            consultRecord.setApplyTime(DataUtils.toDateStr15(order.getRegisteredDate()));
            consultRecord.setVisitTimeStart(DataUtils.toDateStr15(order.getAdmissionDate()));
            consultRecord.setVisitTimeEnd(DataUtils.toDateStr15(order.getEndedDate()));
            consultRecord.setStatus("2");

            MedicalCase medicalcase = medicalCaseRepository.findByOrderId(order.getId()).orElse(null);
            if (medicalcase != null) {
                consultRecord.setInstruction(Optional.ofNullable(medicalcase.getDiagnosis()).orElse("无"));
                // 1、正常结束的：用medical_record中的diagnosis;2、被拒绝或者取消时：取order中的endedReason
            } else {
                consultRecord.setInstruction("无");
            }
            String payType;
            switch (order.getPaymentMethod()) {
                case WECHAT:
                    payType = "4";
                    break;
                case ALI_PAY:
                    payType = "3";
                    break;
                default:
                    payType = "9";
            }
            consultRecord.setPayType(payType);
            consultRecord.setVisitPrice(DataUtils.divideToFloat2(order.getRegistrationFee(), 100));
//            consultRecord.setRefuseTime(DataUtils.toDateStr15(order.getEndedDate()));
//            consultRecord.setRefuseType("1");//todo 拒绝情况必填

            SystemSetting urlPrefix = settingRepository.findOneByKey(SettingKey.NOTIFY_USR_URL_PREFIX).orElseThrow();
            String visitImRecordUrl = UrlUtils.concatSegments(urlPrefix.getValue(), "/hospcode-" + hospital.getCode()) + "/out/visitimrecord/" + orderId;
            UrlInfo urlInfo = new UrlInfo();
            urlInfo.setUrl(visitImRecordUrl);
            UrlInfo[] reviewUrls = new UrlInfo[1];
            reviewUrls[0] = urlInfo;
            consultRecord.setReviewUrls(reviewUrls);
            return consultRecord;
        }
        return new ConsultRecord();
    }

    /**
     * 取消或拒绝上报
     * @param orderId
     * @return
     */
    @Transactional
    public ConsultRecord getCancelConsultRecord(Long orderId, String reportStatus) {
        Optional<Order> optionalOrder = orderRepository.findById(orderId);
        if (optionalOrder.isPresent()) {
            Order order = optionalOrder.get();
            ConsultRecord consultRecord = new ConsultRecord();
            consultRecord.setVisitNo(orderId.toString());

            Hospital hospital = hospitalRepository
                    .findById(order.getHospital().getId()).orElseThrow();
            consultRecord.setUnifiedOrgCode(hospital.getLicenseNumber());
            consultRecord.setOrgName(hospital.getOrgName());

            Dept dept = deptRepository
                    .findById(order.getDept().getId()).orElseThrow();
            consultRecord.setDeptCode(dept.getDeptCode());
            consultRecord.setDeptName(dept.getDeptName());
            consultRecord.setDeptClassCode(dept.getSubjectCode());
            consultRecord.setDeptClassName(DeptClassEnum.getDescByCode(dept.getSubjectCode()));

            MedicalWorker medicalWorker = medicalWorkerRepository
                    .findById(order.getDoctor().getId()).orElseThrow();

            User user = userRepository
                    .findById(medicalWorker.getUser().getId()).orElseThrow();

            consultRecord.setDoctIdcardNo(medicalWorker.getIdentity());
            consultRecord.setDoctName(user.getFullName());

            Optional<DiagnosisCa> optionalDiagnosisCa = diagnosisCaRepository.findOneByOrderId(orderId);
            optionalDiagnosisCa.ifPresent(diagnosisCa -> consultRecord.setDoctCaSign(diagnosisCa.getDiagnosisDigestValue()));

            Patient patient = patientRepository
                    .findById(order.getPatient().getId()).orElseThrow();


            consultRecord.setName(patient.getName());
            consultRecord.setGenderCode(DataUtils.genderToCode(patient.getGender()));
            consultRecord.setAge(DataUtils.getAgeByIdCard(patient.getIdCardNum()));// 根据birthday转成生日
            consultRecord.setTelNum(patient.getMobile());


            consultRecord.setConsultationType(DataUtils.visitTypeToCode(order.getVisitType()));//

            consultRecord.setOnsultationAttribute("1");
            consultRecord.setContent(order.getDescription());
            consultRecord.setApplyTime(DataUtils.toDateStr15(order.getRegisteredDate()));
            consultRecord.setVisitTimeStart(DataUtils.toDateStr15(order.getRegisteredDate()));
            consultRecord.setVisitTimeEnd(DataUtils.toDateStr15(order.getEndedDate()));
            consultRecord.setStatus(reportStatus);
            consultRecord.setInstruction("0".equals(reportStatus) ? "" : order.getEndedReason());

            String payType;
            switch (order.getPaymentMethod()) {
                case WECHAT:
                    payType = "4";
                    break;
                case ALI_PAY:
                    payType = "3";
                    break;
                default:
                    payType = "9";
            }
            consultRecord.setPayType(payType);
            consultRecord.setVisitPrice(DataUtils.divideToFloat2(order.getRegistrationFee(), 100));
            consultRecord.setRefuseTime(DataUtils.toDateStr15(order.getEndedDate()));
            consultRecord.setRefuseReason(order.getEndedReason());
            consultRecord.setRefuseType("1");

            SystemSetting urlPrefix = settingRepository.findOneByKey(SettingKey.NOTIFY_USR_URL_PREFIX).orElseThrow();
            String visitImRecordUrl = UrlUtils.concatSegments(urlPrefix.getValue(), "/hospcode-" + hospital.getCode()) + "/out/visitimrecord/" + orderId;
            UrlInfo urlInfo = new UrlInfo();
            urlInfo.setUrl(visitImRecordUrl);
            UrlInfo[] reviewUrls = new UrlInfo[1];
            reviewUrls[0] = urlInfo;
            consultRecord.setReviewUrls(reviewUrls);
            return consultRecord;
        }
        return new ConsultRecord();
    }

    @Transactional
    public PrescriptionWm getPrescriptionWm(Long prescriptionOrderId) {
        PrescriptionWm prescriptionWm = new PrescriptionWm();
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(prescriptionOrderId);
        // 患者相关内容
        Patient patient = patientRepository
                .findById(prescriptionOrder.getPatient().getId()).orElseThrow();
        // 患者
        prescriptionWm.setRxDatetime(DataUtils.toDateStr15(prescriptionOrder.getCreatedDate()));
        prescriptionWm.setIdcardTypeCode(patient.getCardType().getCode());
        prescriptionWm.setIdcardNo(patient.getIdCardNum());
        prescriptionWm.setName(patient.getName());
        prescriptionWm.setGenderCode(DataUtils.genderToCode(patient.getGender()));
        prescriptionWm.setBirthdate(DataUtils.extractBirthdayFromIDCard(patient.getIdCardNum()));

        // 咨询订单相关
        Order order = orderRepository
                .findById(prescriptionOrder.getOrder().getId()).orElseThrow();
        prescriptionWm.setVisitNo(order.getId().toString());


        // 医疗机构，科室，医生相关信息
        Hospital hospital = hospitalRepository
                .findById(order.getHospital().getId()).orElseThrow();
        prescriptionWm.setUnifiedOrgCode(hospital.getLicenseNumber());
        prescriptionWm.setOrgName(hospital.getOrgName());
        // 科室
        Dept dept = deptRepository
                .findById(order.getDept().getId()).orElseThrow();
        prescriptionWm.setDeptCode(dept.getDeptCode());
        prescriptionWm.setDeptName(dept.getDeptName());
        prescriptionWm.setDeptClassCode(dept.getSubjectCode());
        prescriptionWm.setDeptClassName(DeptClassEnum.getDescByCode(dept.getSubjectCode()));
        // 医生
        MedicalWorker medicalWorker = medicalWorkerRepository
                .findById(order.getDoctor().getId()).orElseThrow();

        User medicalWorkerUser = userRepository
                .findById(medicalWorker.getUser().getId()).orElseThrow();
        prescriptionWm.setDoctIdcardNo(medicalWorker.getIdentity());
        prescriptionWm.setDoctName(medicalWorkerUser.getFullName());

        PrescriptionOrderCa orderCa = prescriptionOrderCaRepository.findAllByPrescriptionOrderIdAndEnabled(prescriptionOrderId,true)
                .stream().findFirst().orElseThrow();
        prescriptionWm.setDoctCaSign(orderCa.getDoctorDigestValue());


//        Upload upload = uploadRepository.getById(medicalWorker.getSignature().getId());
//        if (upload != null) {
//            prescriptionWm.setDoctCaSign(getUrlOfUpload(upload));
//        }

        prescriptionWm.setRotateTypeCode("0");

        // 处方相关
        List<Prescription> prescriptionList = prescriptionOrder.getPrescription();

        prescriptionWm.setHosRxCode(prescriptionOrderId.toString());
        prescriptionWm.setRxTypeCode("1"); //处方分类
        Integer totalPrice = prescriptionList.stream().map(a -> a.getPrice() * a.getQuantity()).reduce((a, b) -> a + b).orElse(0);
        prescriptionWm.setRxAmount(DataUtils.divideToFloat2(totalPrice, 100));

        // 处方明细
        PrescriptionWMDetail[] wmDetails = prescriptionList.stream().map(prescription -> {
            PrescriptionWMDetail wmDetail = new PrescriptionWMDetail();
            wmDetail.setDrugCode(prescription.getDrugCode());
//            wmDetail.setHosDrugCode(prescription.getDrugId().toString());
            wmDetail.setHosDrugName(prescription.getDrugName());
            wmDetail.setWmOnceDosage(prescription.getSingle());
            wmDetail.setDosageUnit(prescription.getSingleUnit());
//            wmDetail.setWmFrequencyCode(WmFrequencyEnum.getCodeByDesc(prescription.getUseFrequency())); // 需要满足规范
            wmDetail.setWmFrequencyCode(prescription.getUseFrequencyCode()); // 需要满足规范
            wmDetail.setWmFrequencyDes(prescription.getUseFrequency());
//            wmDetail.setWmUsewayCode(WmUsewayEnum.getCodeByDesc(prescription.getUseage())); // 需要满足规范
            wmDetail.setWmUsewayCode(prescription.getRouteCode());
            wmDetail.setWmUsewayDes(prescription.getUseage());
            wmDetail.setDurationDays(DataUtils.getDurationDays(prescription.getTimes(), prescription.getTreatmentUnit())); //  times * treatmentUnit
            wmDetail.setPackageUnit(prescription.getUnit()); // 包装单位
            wmDetail.setDrugPrice(DataUtils.divideToFloat2(prescription.getPrice(), 100));
            wmDetail.setPackageCnt(prescription.getQuantity());
            wmDetail.setDrugAmount(DataUtils.divideToFloat2(prescription.getPrice() * prescription.getQuantity(), 100));

            // 药品总剂量显示的是0，需要增加计算总剂量逻辑=[药品使用频率说明]，一次[药品使用次剂量][药品使用剂量单位]，用药[持续用药天数]天
            String totalDosage = prescription.getUseFrequency() +
                    ",一次" + prescription.getSingle() + prescription.getSingleUnit() +
                    ",用药" + DataUtils.getDurationDays(prescription.getTimes(), prescription.getTreatmentUnit()) + "天";
            wmDetail.setTotalDosage(totalDosage);
            return wmDetail;
        }).toArray(PrescriptionWMDetail[]::new);


        prescriptionWm.setItems(wmDetails);
        return prescriptionWm;
    }

    @Transactional
    public CancelPrescription getCancelPrescription(Long prescriptionOrderId) {
        CancelPrescription cancelPrescription = new CancelPrescription();
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(prescriptionOrderId);
        Hospital hospital = hospitalRepository
                .findById(prescriptionOrder.getHospital().getId()).orElseThrow();

        cancelPrescription.setUnifiedOrgCode(hospital.getLicenseNumber());
        cancelPrescription.setHosRxCode(prescriptionOrderId.toString());
        cancelPrescription.setCancelDatetime(DataUtils.toDateStr15(prescriptionOrder.getUpdatedDate()));
        return cancelPrescription;
    }

    @Transactional
    public CancelPrescription getCancelPrescriptionByOrder(Long orderId) {
        // 处方
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.findByOrderIdIn(Lists.newArrayList(orderId)).stream().findFirst().orElseThrow();
        CancelPrescription cancelPrescription = new CancelPrescription();
        Hospital hospital = hospitalRepository
                .findById(prescriptionOrder.getHospital().getId()).orElseThrow();

        cancelPrescription.setUnifiedOrgCode(hospital.getLicenseNumber());
        cancelPrescription.setHosRxCode(prescriptionOrder.getId().toString());
        cancelPrescription.setCancelDatetime(DataUtils.toDateStr15(prescriptionOrder.getUpdatedDate()));
        return cancelPrescription;
    }


    @Transactional
    public PrescriptionExamine getPrescriptionExamine(Long prescriptionId, Boolean pass, PrescriptionOrderCa orderCa) {
        PrescriptionExamine prescriptionExamine = new PrescriptionExamine();
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.findById(prescriptionId).orElseThrow();
        Order order = orderRepository
                .findById(prescriptionOrder.getOrder().getId()).orElseThrow();

        Hospital hospital = hospitalRepository
                .findById(order.getHospital().getId()).orElseThrow();

        prescriptionExamine.setUnifiedOrgCode(hospital.getLicenseNumber());

        // 审核的人
        MedicalWorker doctorReview = medicalWorkerRepository
                .findById(prescriptionOrder.getDoctorReview().getId()).orElseThrow();

        User doctorReviewUser = userRepository
                .findById(doctorReview.getUser().getId()).orElseThrow();
        prescriptionExamine.setSeniorPharName(doctorReviewUser.getFullName());
        prescriptionExamine.setSeniorPharIdcardNo(doctorReview.getIdentity());
        if (pass && orderCa == null) {
            orderCa = prescriptionOrderCaRepository.findAllByPrescriptionOrderIdAndEnabled(prescriptionId, true)
                    .stream().findFirst().orElseThrow();
        }
        prescriptionExamine.setSeniorPharCaSign(orderCa.getDoctorReviewDigestValue());
        prescriptionExamine.setExamineDatetime(DataUtils.toDateStr15(prescriptionOrder.getReviewTime()));
        prescriptionExamine.setHosRxCode(prescriptionId.toString());
        prescriptionExamine.setExamineStatus(pass ? "1" : "2");
        return prescriptionExamine;
    }

    @Transactional
    public ChargeOrRefund getChargeOrRefund(ChargeAndRefundReq req) {
        ChargeOrRefund chargeOrRefund = new ChargeOrRefund();

        Order order = orderRepository.findById(req.getOrderId()).orElseThrow();
        // 获取患者,患者相关字段
        Patient patient = patientRepository
                .findById(order.getPatient().getId()).orElseThrow();
        chargeOrRefund.setIdcardTypeCode(patient.getCardType().getCode());
        chargeOrRefund.setIdcardNo(patient.getIdCardNum());
        chargeOrRefund.setName(patient.getName());
        chargeOrRefund.setGenderCode(DataUtils.genderToCode(patient.getGender()));
        chargeOrRefund.setBirthdate(DataUtils.extractBirthdayFromIDCard(patient.getIdCardNum()));

        // 医疗机构相关
        Hospital hospital = hospitalRepository
                .findById(order.getHospital().getId()).orElseThrow();
        chargeOrRefund.setUnifiedOrgCode(hospital.getLicenseNumber());
        chargeOrRefund.setOrgName(hospital.getOrgName());

        // 科室相关
        Dept dept = deptRepository
                .findById(order.getDept().getId()).orElseThrow();
        chargeOrRefund.setDeptCode(dept.getDeptCode());
        chargeOrRefund.setDeptName(dept.getDeptName());
        chargeOrRefund.setDeptClassCode(dept.getSubjectCode());
        chargeOrRefund.setDeptClassName(DeptClassEnum.getDescByCode(dept.getSubjectCode()));

        // 订单相关
        chargeOrRefund.setVisitNo(order.getId().toString());
        chargeOrRefund.setAccountNo(req.getAccountNo());
        chargeOrRefund.setOrderNo(req.getTransactionId());

        WechatOrder wechatOrder = wechatOrderRepository.getById(req.getWechatOrderId());
        // 退费
        if (Objects.equals("2", req.getChargeRefundCode())) {

//            WechatOrderRefund refund = wechatOrderRefundRepository.findOneByOutTradeNo(outTradeNo).get();

            // 0908 修改，退费的结算号取微信的退款单号，原结算序号取支付时的单号
//            chargeOrRefund.setChargeRefundCode(req.getAccountNo());
            chargeOrRefund.setOriginalAccountNo(wechatOrder.getOutTradeNo());
        }

        // 挂号
        if (req.getWechatOrderType() == ThirdOrderType.REGISTER) {
            // 收费/退费
            chargeOrRefund.setChargeRefundCode(req.getChargeRefundCode());
//            chargeOrRefund.setOrderNo(wechatOrder.getTransactionId());
            chargeOrRefund.setTotalFee(DataUtils.divideToFloat2(order.getRegistrationFee(), 100));
            chargeOrRefund.setIndividualPay(DataUtils.divideToFloat2(order.getRegistrationFee(), 100));
            chargeOrRefund.setRcdDatetime(DataUtils.toDateStr15(new Date()));
            chargeOrRefund.setPayTypeCode("07");


            // 收费/退费明细
            CostDetail costDetail = new CostDetail();
            costDetail.setChargeRefundCode(req.getChargeRefundCode());
            // 项目名称
            costDetail.setProjName("挂号费");
            costDetail.setProjDeno(order.getId().toString());
            costDetail.setPinCatCode("10");
            // 单价 数量 总价
            costDetail.setProjUnitPrice(DataUtils.divideToFloat2(order.getRegistrationFee(), 100));
            costDetail.setProjCnt(1);
            costDetail.setProjAmount(DataUtils.divideToFloat2(order.getRegistrationFee(), 100));
            costDetail.setIndividualPay(DataUtils.divideToFloat2(order.getRegistrationFee(), 100));

            chargeOrRefund.setItems(Lists.newArrayList(costDetail).toArray(new CostDetail[0]));
        } else {
            // 处方
            PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.findByOrderIdIn(Lists.newArrayList(req.getOrderId())).stream().findFirst().orElseThrow();

            List<Prescription> prescriptionList = prescriptionOrder.getPrescription();

            // 收费/退费
            chargeOrRefund.setChargeRefundCode(req.getChargeRefundCode());
//            chargeOrRefund.setOrderNo(wechatOrder.getTransactionId());

            chargeOrRefund.setRcdDatetime(DataUtils.toDateStr15(new Date()));
            chargeOrRefund.setPayTypeCode("07");

            AtomicInteger totalPrice = new AtomicInteger();
            // 处方明细
            CostDetail[] costDetails = prescriptionList.stream().map(prescription -> {
                CostDetail costDetail = new CostDetail();
                costDetail.setChargeRefundCode(req.getChargeRefundCode());
                // 项目名称
                costDetail.setProjName(prescription.getDrugName());
                costDetail.setProjDeno(prescription.getDrugId().toString());
                costDetail.setPinCatCode("01");
                // 单价 数量 总结
                costDetail.setProjUnitPrice(DataUtils.divideToFloat2(prescription.getPrice(), 100));
                costDetail.setProjCnt(prescription.getQuantity());
                int amount = prescription.getPrice() * prescription.getQuantity();
                costDetail.setProjAmount(DataUtils.divideToFloat2(amount, 100));
                totalPrice.addAndGet(amount);

                return costDetail;
            }).toArray(CostDetail[]::new);
            chargeOrRefund.setItems(costDetails);
            chargeOrRefund.setIndividualPay(DataUtils.divideToFloat2(totalPrice.get(), 100));
            chargeOrRefund.setTotalFee(DataUtils.divideToFloat2(totalPrice.get(), 100));
        }

        return chargeOrRefund;
    }

    @Transactional
    public MedicineDeliver getMedicineDeliver(Long drugOrderId) {
        return new MedicineDeliver();
    }

    @Transactional
    public ServiceEvaluation getServiceEvaluation(Long evaluationId) {
        Optional<OrderWorker> optionalOrderWorker = orderWorkerRepository.findById(evaluationId);
        if (optionalOrderWorker.isPresent()) {
            ServiceEvaluation serviceEvaluation = new ServiceEvaluation();
            OrderWorker orderWorker = optionalOrderWorker.get();
            Order order = orderRepository
                    .findById(orderWorker.getOrder().getId()).orElseThrow();

            String orgId = order.getHospital().getLicenseNumber();
            serviceEvaluation.setUnifiedOrgCode(orgId);
            serviceEvaluation.setBusinessTypeCode("1");
            serviceEvaluation.setBusinessCode(order.getId().toString());
            serviceEvaluation.setBusinessCode2("0");
            serviceEvaluation.setEvaluationDatetime(DataUtils.toDateStr15(orderWorker.getEvaluateDate()));

            serviceEvaluation.setScoring(orderWorker.getRating() * 2);
            return serviceEvaluation;
        }
        return new ServiceEvaluation();
    }

    @Transactional
    public Complaint getComplaint(Hospital hospital, Long complaintId) {
        Ticket ticket = ticketRepository.findById(complaintId).orElse(null);
        if (ticket != null && ticket.getType() == Ticket.Type.FEEDBACK && !Objects.equals(ticket.getResponseType(), 1)) {
            Complaint complaint = new Complaint();
            complaint.setComplaintedUnifiedOrgCode(hospital.getLicenseNumber());

            complaint.setComplaintNo(complaintId.toString());
            complaint.setComplaintDatetime(DataUtils.toDateStr15(ticket.getCreatedDate()));

            complaint.setDemandTypeCode("1");//投诉
            List<String> list = SuggestTypeConvert.toList(ticket.getSuggestTypes());
            complaint.setComplaintTypeCode(CollectionUtil.isEmpty(list) ? "0" : list.get(0));
            complaint.setComplaintText(ticket.getContent());
            complaint.setTelNum(ticket.getTelephone());
            return complaint;
        }
        return new Complaint();
    }
}
