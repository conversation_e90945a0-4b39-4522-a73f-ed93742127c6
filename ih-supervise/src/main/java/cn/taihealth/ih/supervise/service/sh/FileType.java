package cn.taihealth.ih.supervise.service.sh;

import lombok.Getter;

@Getter
public enum FileType {
    ORG_INFO("机构信息 （需要上传医疗许可证 PDF 文件） "), //uniqueId 唯一校验标识——hosOrgCode 医院编码; 
    AGREEMENT_INFO("机构信息 （合作协议，安全协议，平台方等保文档合并 PDF 文件） "), //uniqueId 唯一校验标识——userLicenseDocumentCode 合作协议，安全协议，平台方等保文档合并 PDF 文件 
    YEAR_CHECK_INFO("年检信息 （上传年检校验记录 PDF 文件） "), //uniqueId 唯一校验标识——licenceNumber 许可证号码; 
    THREE_GRADE_INFO("年检信息 （上传信息安全等级证书 PDF 文件） "), //uniqueId 唯一校验标识——threelevelEqualprotectionCode 三级等保编码; 
    DOCTOR_INFO("医生信息 （上传资格证书PDF 文件） "), //uniqueId 唯一校验标识——personId 证件号; 
    DOCTOR_CERT_INFO("医生证件照 （面部识别清晰JPG 文件） "), //uniqueId 唯一校验标识——personId 证件号; 
    PHARMACIST_INFO("药师信息 （上传资格证书PDF 文件） "), //uniqueId 唯一校验标识——idNumber 证件号; 
    NURSE_STAFF_INFO("护理人员信息 （上传资格证书PDF 文件） "), //uniqueId 唯一校验标识——idNumber 证件号; 
    FOLLOW_UP_INFO("复诊信息 （上传问诊聊天记录或音视频文件） "), //uniqueId 唯一校验标识——serialNumber 复诊流水号; 
    MEDICAL_RECORD("病历信息 （上传带电子签名的 PDF 文件） "), //uniqueId 唯一校验标识——serialNumber 复诊流水号; 
    PRESCRIPTION_INFO("处方信息 （上传带电子签名的 PDF 文件） "), //uniqueId 唯一校验标识——cfh 处方号;  
    IM_CHAT_INFO("复诊聊天记录文件 "), //uniqueId 唯一校验标识——serialNumber 复诊流水号; 
    ;
    private final String desc;

    FileType(String desc) {
        this.desc = desc;
    }

}
