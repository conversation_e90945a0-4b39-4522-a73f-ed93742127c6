package cn.taihealth.ih.supervise.service;


import cn.taihealth.ih.domain.enums.SuperviseEnum;
import com.gitq.jedi.context.AppContext;
import org.apache.commons.lang3.StringUtils;


public class SuperviseFactory {

    // 根据医院监管平台的配置，获取不同的监管上报服务
    public static SuperviseService getSuperviseService(SuperviseEnum superviseEnum) {
        if (StringUtils.isBlank(superviseEnum.getServiceName())) {
            return null;
        } else {
            return AppContext.getInstance(superviseEnum.getServiceName(), SuperviseService.class);
        }
    }
}
