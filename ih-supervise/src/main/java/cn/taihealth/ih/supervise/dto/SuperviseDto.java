package cn.taihealth.ih.supervise.dto;

import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.enums.SuperviseEnum;
import lombok.Data;

@Data
public class SuperviseDto {

    private SuperviseEnum superviseEnum;

    private String code;

    private String secret;

    private Boolean debugMode;

    private String host;

    private String hospitalCode;

    private OfflineHospital offlineHospital;
    /**
     * 医院国家医保代码
     * 12 位，如没有医保
     * 请咨询监管平台
     */
    private String hosOrgCode;

}
