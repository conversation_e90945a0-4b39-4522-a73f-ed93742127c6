package cn.taihealth.ih.supervise.dto.sh.ba;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 互联网医生
 */
@Data
public class Doctor {

    private String hosOrgCode; //	医院代码 	医院国家医保代码12位。 	是 	varchar(12)
    private String orgCode; //	登记号 	医疗许可证上的登记号 	是 	varchar(22) 	登记号
    private String hosName; //	医院名称 	机构名称 	是 	varchar(60) 	与医疗许可证商名称保持一致，不能用简称
    private String areaCode; //	机构区划代码 	上海区域代码（以全国地区代码表为准） 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String areaName; //	机构区划名称 	上海区域名称（以全国地区代码表为准）注：不要带“上海市” 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String oneDeptCode; //	一级科室代码 	按照本院实际一级科室代码上传 	是 	varchar(15)
    private String primaryOffice; //	一级科室名称 	按照本院实际一级科室名称上传 	是 	varchar(30)
    private String deptCode; //	二级科室代码 	按照本院实际二级科室代码上传 	是 	varchar(15)
    private String secondOffice; //	二级科室名称 	按照本院实际二级科室名称上传 	是 	varchar(30)
    private String resourceName; //	医生姓名 	 	是 	varchar(30)
    private String doctTile; //	医生职称 	按国标 GB/T 8561-2001  专业技术职务代码执行编码。 	是 	varchar(3) 	230：卫生技术人员（医师） 231：主任医师 232：副主任医师 233：主治医师 234：医师  235：医士
    private String personType; //	证件类型 	按照卫标 CV02.01.101 身份证件类别代码执行编码。 	是 	varchar(2) 	01：居民身份证 02：居民户口本 03：护照 04：军官证 05：驾驶证 06：港澳居民来往内地通行证 07：台湾居民来往内地通行证 19：母亲身份证 99：其他法定有效证件
    private String personId; //	证件号 	 	是 	varchar(18) 	需要调取《文件上传及验证接口》，上传医生信息文件以及医生资格证书文件。
    private String resourceCode; //	医生代码 	 	是 	varchar(300)
    private String doctorId; //	医生国家编码 	 	是 	varchar(50)
    private String doctImg; //	医生头像 	图片小于256K，格式Base64 	否 	varchar(max)
    private String doctSex; //	医生性别 	按国标 GB/T2261.1-2003 执行编码。 	是 	varchar(1) 	0：未知的性别 1：男性 2：女性 9：未说明的性别
    private String doctInfo; //	医生简介 	 	是 	text
    private String doctSpecialty; //	医生特长 	 	是 	varchar(500)
    private String isRegister; //	是否开展预约 	 	是 	varchar(1) 	0:否 1:是
    private String isDelete; //	删除标志 	 	是 	varchar(1) 	0:否 1:是
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate; //	创建日期 	格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date participationDate; //	参加工作日期 	格式 'yyyy - MM - dd' 	是 	date 	2019/12/10
    private String clinicalYear; //	临床工作年限 	 	是 	varchar(2)
    private String certificateNo; //	执业证书编号 	 	是 	varchar(50)
    private String organzationName; //	执业机构 	如有多个执业机构，“，”分割 	是 	varchar(60)
    private String place; //	执业地点 	如有多点执业，需填写多个执业地点,“，”分割 	是 	varchar(60)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDate; //	执业发证日期 	格式 'yyyy - MM - dd' 	是 	date 	2019/12/10
    private String practiceScope; //	执业范围 	 	是 	varchar(100)
    private String idPhoto; //	证件照片 	 	否 	varchar(max)
    private String qualificationCategory; //	资质类别 	如有多个需在医务人员资质上报表补充多条。 	否 	varchar(1) 	1：临床 2：中医 3：口腔 4：公共卫生 5：其他
    private String qualificationNo; //	资质证书编号 	如有多个需在医务人员资质上报表补充多条。 	否 	varchar(3000)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date qualificationDate; //	资质获取时间 	格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    private String qualificationPhoto; //	资质证件照片 	 	否 	varchar(max)
    private String duty; //	职务代码 	按国标  GB/T12407-2008  职务级别代码执行编码。 	是 	varchar(3) 	参见  字典规范/GBT 12407-2008 职 务 级 别 代码.PDF
    private String personnelType; //	人员类型 	 	是 	varchar(3) 	1：普通医务人员  2：港澳台医务人员   3：外籍医务人员
    private String professionalJobSort; //	专业技术职务类别 	按照： WS364.15卫生信息数据源值域代码 第15部分： 卫 生 人 员CV08.30.005 执行编码。 	是 	varchar(1) 	1：正高 2：副高 3：中级 4：师级/助理 5：士级 6：待聘
    private String resource; //	来源 	 	否 	varchar(1) 	0：卫监所 1：医院填报
    private String orgMark; //	机构标识 	卫统22位编码 	是 	varchar(50)
    private String organzaionName; //	所属机构 	 	是 	varchar(50)
    private String recipeRight; //	处方权 	处方权（有：1；无：0；） 	否 	varchar(3) 	示例： 000：三个都没有 100：有心血管药品处方权 010：有精神药品处方权 001：有麻醉药品处方权 111：三个都有
    private String phoneNumber; //	手机号码 	 	否 	varchar(11)
    private String nation; //	民族 	按照 GB3304-1991  中国各民族名称的罗马字母拼写法和代码执行编码。 	否 	varchar(20) 	参见  字典规范/GB 3304-1991 民族代码.pdf
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthDate; //	出生日期 	格式 'yyyy - MM - dd' 	否 	date 	2019/12/10
    private String education; //	学历 	按照 GB/T 4658-2006《学历代码》 执行编码。 	是 	varchar(20) 	参见 字典规范/GBT 46582006 学历代码.pdf
    private String openState; //	开通状态 	 	否 	varchar(1) 	0：未开通  1：已开通
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date openTime; //	开通时间 	格式 'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	2019/12/10 16:34
    private String doctAgreementCode; //	用户授权协议 	如是入驻平台，并签署协议需要提供 	否 	varchar(50) 	需要文件上传接口对应协议。
    private String doctPenaltyTimeStart; //	医师行政处罚开始时间+原因说明 	只填写最近一条，格式 'yyyy - MM - dd：处罚原因' 	否 	varchar(2000) 	2019-12-10：因...被处罚
    private String doctPenaltyTimeEnd; //	医师行政处罚结束时间+原因说明 	只填写最近一条，格式 'yyyy - MM - dd：解除/撤销处罚说明' 	否 	varchar(2000) 	2019-12-10：经...解除/撤销处罚
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date doctTrainingTimeStart; //	医师离岗培训开始时间 	格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date doctTrainingTimeEnd; //	医师离岗培训结束时间 	格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date shortLicenseTimeStart; //	短期行医许可开始时间 	格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date shortLicenseTimeEnd; //	短期行医许可结束时间 	格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    private String medicareState; //	医师参加医疗保险状态 	开通状态 	否 	varchar(1) 	0：未开通 1：已开通
    private String medicareCode; //	医疗保险码 	 	否 	varchar(22)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date medicareTimeStart; //	医师参加医疗保险开始时间 	只填写最近一次，格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date medicareTimeEnd; //	医师参加医疗保险结束时间 	只填写最近一次，格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    private String regularAssessmentResult; //	医师定期考核结果 	0：未通过 1：通过 	否 	varchar(100) 	例如：2020.12.25|1 2018.12.25|0

}
