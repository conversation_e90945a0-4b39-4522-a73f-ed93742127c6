package cn.taihealth.ih.supervise.dto.ln.ac;

import lombok.Data;

/**
 * 西药处方明细
 */
@Data
public class PrescriptionWMDetail {
    // 药品标准编码
    private String drugCode; // 字符，长度16

    // 医院药品编码
    private String hosDrugCode; // 字符，长度36

    // 医院药品名称
    private String hosDrugName; // 字符，长度80

    // 药品生产企业名称
    private String factoryName; // 字符，长度80

    // 药品生产批号
    private String productPatch; // 字符，长度32

    // 药品规格编码
    private String drugSpecCode; // 字符，长度4

    // 药品规格描述
    private String drugSpecDes; // 字符，长度80

    // 药品剂型代码
    private String drugFormCode; // 字符，长度4

    // 药品剂型描述
    private String drugFormDes; // 字符，长度80

    // 药品使用次剂量
    private double wmOnceDosage; // 数值，长度10，精度2

    // 药品使用剂量单位
    private String dosageUnit; // 字符，长度8

    // 药品使用频率代码
    private String wmFrequencyCode; // 字符，长度8

    // 药品使用频率说明
    private String wmFrequencyDes; // 字符，长度40

    // 药品使用途径代码
    private String wmUsewayCode; // 字符，长度3

    // 药品使用途径说明
    private String wmUsewayDes; // 字符，长度40

    // 持续用药天数
    private int durationDays; // 数值，长度5

    // 药品包装单位
    private String packageUnit; // 字符，长度20

    // 药品单价(元)
    private String drugPrice; // 数值，长度10，精度4

    // 药品包装数量
    private int packageCnt; // 数值，长度5

    // 药品总价(元)
    private String drugAmount; // 数值，长度10，精度2

    // 药品使用总剂量
    private String totalDosage; // 字符，长度40

    // 是否属于基药
    private String ifBaseDrug; // 字符，长度1

    // 是否属于抗生素
    private String ifAntibiotics; // 字符，长度1

    // 用药备注说明信息
    private String useDescription; // 字符，长度400
}
