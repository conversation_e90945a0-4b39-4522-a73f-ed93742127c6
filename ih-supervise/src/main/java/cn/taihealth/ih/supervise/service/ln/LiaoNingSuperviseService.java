package cn.taihealth.ih.supervise.service.ln;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.taihealth.ih.commons.mail.CommonMailService;
import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.ThirdOrderType;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.OrderOperation;
import cn.taihealth.ih.domain.hospital.PrescriptionOrderCa;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.supervise.constants.ln.ServCodeConstants;
import cn.taihealth.ih.supervise.dto.ln.ac.ChargeAndRefundReq;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.dto.ln.ac.*;
import cn.taihealth.ih.supervise.dto.ln.ba.Department;
import cn.taihealth.ih.supervise.dto.ln.ba.DepartmentClass;
import cn.taihealth.ih.supervise.dto.ln.ba.Doctor;
import cn.taihealth.ih.supervise.dto.ln.ba.Organization;
import cn.taihealth.ih.supervise.service.SuperviseService;
import cn.taihealth.ih.supervise.utils.ThreadPoolUtils;
import com.alibaba.fastjson.JSON;
import com.xikang.medical.sdk.ISDKClient;
import com.xikang.medical.sdk.SDKClientFactory;
import com.xikang.medical.sdk.entity.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service("liaoNingSuperviseService")
@Slf4j
@AllArgsConstructor
public class LiaoNingSuperviseService implements SuperviseService {

    private final LiaoningDataCreateComponent dataCreateComponent;
    private final CommonMailService commonMailService;
    private final DeptRepository deptRepository;


    private boolean clientInit(ISDKClient sdk, SuperviseDto dto) {
        String initResStr = sdk.doClientInit(dto.getCode(), dto.getSecret(), dto.getDebugMode());
        Response initRes = JSON.parseObject(initResStr, Response.class);
        log.info("辽宁监管平台初始化 参数{} {} {}  返回值 {}", dto.getCode(), dto.getSecret(), dto.getDebugMode(), initRes);
        return Objects.equals(0, initRes.getCode());
    }


    @Override
    public void organizationBa(Hospital hospital, SuperviseDto dto) {
        ThreadPoolUtils.getBaThreadPool().execute(() -> {
            // 获取医院相关的实体
            Organization organization = dataCreateComponent.getOrganization(hospital);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 医院备案初始化失败，医院code:{}", hospital.getCode());
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 医院备案调用开始，参数{}", JSONUtil.toJsonStr(organization));
            String resStr = sdk.doClientServ(ServCodeConstants.BA_HOSPITAL, JSONUtil.toJsonStr(organization));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 医院备案调用结束，返回值{}", resStr);
            sendMail(hospital.getCode(), JSONUtil.toJsonStr(organization), "医院备案异常结束", resStr);
        });
    }

    @Override
    public void departmentBa(Hospital hospital, SuperviseDto dto) {
        List<Dept> deptList = deptRepository.findByHospital(hospital);
        // 线上科室才需要备案
        for (Dept dept : deptList) {
//                    if (dept.getChannelType() == ONLINE) {
            // 科室备案
            departmentBa(dept.getId(), dto);
            // 科目备案
            departmentClassBa(dept.getId(), dto);
//                    }
        }
    }

    private void departmentBa(Long deptId, SuperviseDto dto) {
        // 准入使用科室表的enabled字段
        ThreadPoolUtils.getBaThreadPool().execute(() -> {
            // 获取科目相关的实体
            Department department = dataCreateComponent.getDepartment(deptId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 科室备案初始化失败，科室id:{}", deptId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 科室备案调用开始，参数{}", JSONUtil.toJsonStr(department));
            String resStr = sdk.doClientServ(ServCodeConstants.BA_DEPT, JSONUtil.toJsonStr(department));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 科室备案调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(department), "科室备案异常结束", resStr);
        });
    }

    private void departmentClassBa(Long deptId, SuperviseDto dto) {
        // 准入使用字典表的enabled字段
        ThreadPoolUtils.getBaThreadPool().execute(() -> {

            // 获取科目相关的实体
            DepartmentClass departmentClass = dataCreateComponent.getDepartmentClass(deptId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 科目备案初始化失败，科室id:{}", deptId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 科目备案调用开始，参数{}", JSONUtil.toJsonStr(departmentClass));
            String resStr = sdk.doClientServ(ServCodeConstants.BA_SUBJECT, JSONUtil.toJsonStr(departmentClass));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 科目备案调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(departmentClass), "科目备案异常结束", resStr);
        });
    }

    @Override
    public void doctorBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto) {
        ThreadPoolUtils.getBaThreadPool().execute(() -> {
            // 获取医生相关的实体
            Doctor doctor = dataCreateComponent.getDoctor(medicalWorker);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 医生备案初始化失败，医生id:{}", medicalWorker.getId());
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 医生备案调用开始，参数{}", JSONUtil.toJsonStr(doctor));
            String resStr = sdk.doClientServ(ServCodeConstants.BA_MEDICAL_WORK, JSONUtil.toJsonStr(doctor));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 医生备案调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(doctor), "医生备案异常结束", resStr);
        });
    }

    @Override
    public void pharmacistBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto) {

    }

    @Override
    // 挂号
    public void register(Hospital hospital, Long orderId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取挂号相关的实体
            RegisterRecord registerRecord = dataCreateComponent.getRegisterRecord(orderId);
            if (registerRecord == null) {
                log.info("不是复诊咨询订单，不需要上报");
                return;
            }

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 挂号初始化失败，挂号id:{}", orderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 挂号调用开始，参数{}", JSONUtil.toJsonStr(registerRecord));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_REGISTRATION, JSONUtil.toJsonStr(registerRecord));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 挂号调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(registerRecord), " 挂号调用异常结束", resStr);
        });
    }

    @Override
    public void rejectRegistration(Hospital hospital, Long orderId, SuperviseDto dto) {

    }

    // 退号
    @Override
    public void cancelRegistration(Long orderId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取退号相关的实体
            CancelRegister cancelRegister = dataCreateComponent.getCancelRegister(orderId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 退号初始化失败，挂号id:{}", orderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 退号调用开始，参数{}", JSONUtil.toJsonStr(cancelRegister));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_CANCEL_REG, JSONUtil.toJsonStr(cancelRegister));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 退号调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(cancelRegister), "退号调用异常结束", resStr);
        });
    }

    // 就诊记录
    @Override
    public void createVisitRecord(Hospital hospital, Long orderId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取就诊记录相关的实体
            MedicalRecord medicalRecord = dataCreateComponent.getMedicalRecord(orderId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 就诊记录初始化失败，就诊记录id:{}", orderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 就诊记录调用开始，参数{}", JSONUtil.toJsonStr(medicalRecord));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_MEDICAL_RECORD, JSONUtil.toJsonStr(medicalRecord));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 就诊记录调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(medicalRecord), "就诊记录异常结束", resStr);
        });
    }

    // 咨询记录
    @Override
    public void createConsultationRecord(Long orderId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取咨询记录相关的实体
            ConsultRecord consultRecord = dataCreateComponent.getConsultRecord(orderId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 咨询记录初始化失败，挂号id:{}", orderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 咨询记录调用开始，参数{}", JSONUtil.toJsonStr(consultRecord));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_CONSULTATION_RECORD, JSONUtil.toJsonStr(consultRecord));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 咨询记录调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(consultRecord), "咨询记录异常结束", resStr);
        });
    }

    // 咨询-取消接诊
    @Override
    public void createCancelConsultationRecord(Long orderId, SuperviseDto dto, OrderOperation.Step step) {
        // -1 医生拒绝-对应id医生拒绝接诊、退款  0 患者取消-对应ih患者“退款”，退款status是 ONTIME_CONFIRMED_REFUNDED
        String reportStatus;
        switch (step) {
            case ONTIME_CONFIRMED_REFUNDED:
                // 患者申请退款
                reportStatus = "0";
                break;
            case REJECT_RETURN:
            case STARTED_REFUNDED:
            case REFUND:
            case ONTIME_CONFIRMED_DOCTOR_REFUNDED:
                reportStatus = "-1";
                break;
            default:
                return;
        }
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取咨询记录相关的实体
            ConsultRecord consultRecord = dataCreateComponent.getCancelConsultRecord(orderId, reportStatus);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 咨询记录初始化失败，挂号id:{}", orderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 咨询记录调用开始，参数{}", JSONUtil.toJsonStr(consultRecord));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_CONSULTATION_RECORD, JSONUtil.toJsonStr(consultRecord));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 咨询记录调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(consultRecord), "咨询记录异常结束", resStr);
        });
    }

    // 处方开立（西药）
    public void prescribeWest(Long prescriptionOrderId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取处方相关的实体
            PrescriptionWm prescriptionWm = dataCreateComponent.getPrescriptionWm(prescriptionOrderId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 西药处方初始化失败，西药处方id:{}", prescriptionOrderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 西药处方开立调用开始，参数{}", JSONUtil.toJsonStr(prescriptionWm));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_PRESCRIPTION, JSONUtil.toJsonStr(prescriptionWm));
            log.info("-------- 辽宁监管平台， 西药处方开立调用结束，返回值1{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(prescriptionWm), "西药处方开立异常结束", resStr);
        });

    }

    // 处方变更
    public void changePrescription() {
        // 方法体
        // 暂时不对接
    }

    // 处方撤消
    public void cancelPrescription(Long prescriptionOrderId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取挂号相关的实体
            CancelPrescription cancelPrescription = dataCreateComponent.getCancelPrescription(prescriptionOrderId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 处方撤消初始化失败，处方code:{}", prescriptionOrderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 处方撤消调用开始，参数{}", JSONUtil.toJsonStr(cancelPrescription));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_PRESCRIPTION_RETURN, JSONUtil.toJsonStr(cancelPrescription));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 处方撤消调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(cancelPrescription), "处方撤消异常结束", resStr);
        });
    }

    // 处方审核
    @Override
    public void auditPrescription(Hospital hospital, Long prescriptionOrderId, SuperviseDto dto, Boolean pass, PrescriptionOrderCa orderCa) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取挂号相关的实体
            PrescriptionExamine prescriptionExamine = dataCreateComponent.getPrescriptionExamine(prescriptionOrderId, pass, orderCa);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 处方审核初始化失败，处方id:{}", prescriptionOrderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 处方审核调用开始，参数{}", JSONUtil.toJsonStr(prescriptionExamine));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_PRESCRIPTION_REVIEW, JSONUtil.toJsonStr(prescriptionExamine));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 处方审核调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(prescriptionExamine), "处方审核异常结束", resStr);
        });

    }

    // 处方流转
    public void transferPrescription() {
        // 方法体
        // 暂时不对接
    }

    // 药品配送
    public void deliverMedicine(Long drugOrderId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            // todo 调整为事务执行后运行
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 获取挂号相关的实体
            MedicineDeliver medicineDeliver = dataCreateComponent.getMedicineDeliver(drugOrderId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 药品配送初始化失败，药品订单id:{}", drugOrderId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 药品配送调用开始，参数{}", JSONUtil.toJsonStr(medicineDeliver));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_MEDICINE_DELIVERY, JSONUtil.toJsonStr(medicineDeliver));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 药品配送调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(medicineDeliver), "药品配送异常结束", resStr);
        });
    }

    public void deliverMedicine(MedicineDeliver medicineDeliver, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            // todo 调整为事务执行后运行
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 获取挂号相关的实体
//            MedicineDeliver medicineDeliver = dataCreateComponent.getMedicineDeliver(drugOrderId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 药品配送初始化失败，药品订单id:{}", medicineDeliver.getHosRxCode());
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 药品配送调用开始，参数{}", JSONUtil.toJsonStr(medicineDeliver));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_MEDICINE_DELIVERY, JSONUtil.toJsonStr(medicineDeliver));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 药品配送调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(medicineDeliver), "药品配送异常结束", resStr);
        });
    }


    // 收费/退费
    public void chargeAndRefund(ChargeAndRefundReq req, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {

            // todo 调整为事务执行后运行
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 获取挂号相关的实体
            ChargeOrRefund chargeOrRefund = dataCreateComponent.getChargeOrRefund(req);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 收费/退费初始化失败，参数:{}", JSONUtil.toJsonStr(req));
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 收费/退费调用开始，参数{}", JSONUtil.toJsonStr(chargeOrRefund));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_PAYMENT, JSONUtil.toJsonStr(chargeOrRefund));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 收费/退费调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(chargeOrRefund), "收费/退费异常结束", resStr);

            // 如果是处方退款，同时调用处方撤销
            if (req.getWechatOrderType() == ThirdOrderType.RECIPE && "2".equals(req.getChargeRefundCode())) {

                // 获取撤销处方相关的实体
                CancelPrescription cancelPrescription = dataCreateComponent.getCancelPrescriptionByOrder(req.getOrderId());
                // 开始调用
                log.info("-------- 辽宁监管平台， 处方撤消调用开始，参数{}", JSONUtil.toJsonStr(cancelPrescription));
                String resStr2 = sdk.doClientServ(ServCodeConstants.AC_PRESCRIPTION_RETURN, JSONUtil.toJsonStr(cancelPrescription));
                // 解析返回值
                log.info("-------- 辽宁监管平台， 处方撤消调用结束，返回值{}", resStr2);
                sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(cancelPrescription), "处方撤消异常结束", resStr2);
            }

        });

    }

    // 服务评价
    public void evaluateService(Long evaluationId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            // todo 调整为事务执行后运行
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 获取挂号相关的实体
            ServiceEvaluation serviceEvaluation = dataCreateComponent.getServiceEvaluation(evaluationId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 服务评价初始化失败，挂号id:{}", evaluationId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 服务评价调用开始，参数{}", JSONUtil.toJsonStr(serviceEvaluation));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_SERVICE_EVALUATION, JSONUtil.toJsonStr(serviceEvaluation));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 服务评价调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(serviceEvaluation), "服务评价异常结束", resStr);
        });

    }

    // 投诉举报
    @Override
    public void reportComplaint(Hospital hospital, Long complaintId, SuperviseDto dto) {
        ThreadPoolUtils.getAcThreadPool().execute(() -> {
            // todo 调整为事务执行后运行
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            // 获取挂号相关的实体
            Complaint complaint = dataCreateComponent.getComplaint(hospital, complaintId);

            // sdk初始化
            ISDKClient sdk = SDKClientFactory.getClient();
            boolean initSuccess = clientInit(sdk, dto);
            if (!initSuccess) {
                // 记录日志
                log.error("辽宁监管平台， 投诉举报初始化失败，投诉举报id:{}", complaintId);
                // 失败记录，重试
                return;
            }

            // 开始调用
            log.info("-------- 辽宁监管平台， 投诉举报调用开始，参数{}", JSONUtil.toJsonStr(complaint));
            String resStr = sdk.doClientServ(ServCodeConstants.AC_COMPLAINT, JSONUtil.toJsonStr(complaint));
            // 解析返回值
            log.info("-------- 辽宁监管平台， 投诉举报调用结束，返回值{}", resStr);
            sendMail(dto.getHospitalCode(), JSONUtil.toJsonStr(complaint), "投诉举报异常结束", resStr);
        });

    }

    @Override
    public void annualInspectionInfoBa(Hospital hospital, SuperviseDto dto) {

    }

    private void sendMail(String hospitalCode, String param, String title, String msg) {

        JSONObject jsonObject = JSONUtil.parseObj(msg);

        String code = jsonObject.get("code", String.class);
        // 有错误
        if (!Objects.equals(code, "0")) {
            String content = StringUtil.formatHtml(title + "参数：" + param + "，失败原因：<font color=\"red\">%s</font>，请联系开发人员处理",
                    msg);
            commonMailService.sendHtmlMail(hospitalCode, title, content);
        }

    }


    @Override
    public void appointmentInfoUpload(Hospital hospital, Long orderId, SuperviseDto dto) {

    }

    @Override
    public void selectUploadData(Hospital hospital, int type, Integer page, Integer size, SuperviseDto dto) {

    }
}
