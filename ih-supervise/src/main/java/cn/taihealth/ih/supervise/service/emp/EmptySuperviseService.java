package cn.taihealth.ih.supervise.service.emp;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.OrderOperation;
import cn.taihealth.ih.domain.hospital.PrescriptionOrderCa;
import cn.taihealth.ih.supervise.dto.ln.ac.ChargeAndRefundReq;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.dto.ln.ac.MedicineDeliver;
import cn.taihealth.ih.supervise.service.SuperviseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("emptySuperviseService")
@Slf4j
public class EmptySuperviseService implements SuperviseService {

    @Override
    public void organizationBa(Hospital hospital, SuperviseDto dto) {

    }

    @Override
    public void departmentBa(Hospital hospital, SuperviseDto dto) {

    }

    @Override
    public void doctorBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto) {

    }

    @Override
    public void pharmacistBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto) {

    }

    @Override
    public void register(Hospital hospital, Long orderId, SuperviseDto dto) {

    }

    @Override
    public void cancelRegistration(Long orderId, SuperviseDto dto) {

    }

    @Override
    public void rejectRegistration(Hospital hospital, Long orderId, SuperviseDto dto) {

    }

    @Override
    public void createVisitRecord(Hospital hospital, Long orderId, SuperviseDto dto) {

    }

    @Override
    public void createConsultationRecord(Long orderId, SuperviseDto dto) {

    }

    @Override
    public void createCancelConsultationRecord(Long orderId, SuperviseDto dto, OrderOperation.Step step) {

    }

    @Override
    public void prescribeWest(Long prescriptionOrderId, SuperviseDto dto) {

    }

    @Override
    public void changePrescription() {

    }

    @Override
    public void cancelPrescription(Long prescriptionOrderId, SuperviseDto dto) {

    }

    @Override
    public void auditPrescription(Hospital hospital, Long prescriptionOrderId, SuperviseDto dto, Boolean pass, PrescriptionOrderCa orderCa) {

    }

    @Override
    public void transferPrescription() {

    }

    @Override
    public void deliverMedicine(Long drugOrderId, SuperviseDto dto) {

    }

    @Override
    public void deliverMedicine(MedicineDeliver medicineDeliver, SuperviseDto dto) {

    }

    @Override
    public void chargeAndRefund(ChargeAndRefundReq req, SuperviseDto dto) {

    }

    @Override
    public void evaluateService(Long evaluationId, SuperviseDto dto) {

    }

    @Override
    public void reportComplaint(Hospital hospital, Long complaintId, SuperviseDto dto) {

    }

    @Override
    public void annualInspectionInfoBa(Hospital hospital, SuperviseDto dto) {

    }

    @Override
    public void appointmentInfoUpload(Hospital hospital, Long orderId, SuperviseDto dto) {

    }

    @Override
    public void selectUploadData(Hospital hospital, int type, Integer page, Integer size, SuperviseDto dto) {

    }
}
