package cn.taihealth.ih.supervise.service.sh;

import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.mail.CommonMailService;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import cn.taihealth.ih.commons.util.PDFUtil;
import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.MedicalCredentials;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.cloud.User.AvatarType;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.utils.UploadUtil;
import cn.taihealth.ih.realname.SM2Utils;
import cn.taihealth.ih.realname.SM4Utils;
import cn.taihealth.ih.repo.MedicalCredentialsRepository;
import cn.taihealth.ih.repo.OfflineHospitalAnnualInspectionRepository;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.repo.UserRepository;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.hospital.order.DiagnosisCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderCaRepository;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.dto.ln.ac.ChargeAndRefundReq;
import cn.taihealth.ih.supervise.dto.ln.ac.MedicineDeliver;
import cn.taihealth.ih.supervise.dto.sh.ac.*;
import cn.taihealth.ih.supervise.dto.sh.ba.*;
import cn.taihealth.ih.supervise.service.SuperviseService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.common.web.UrlUtils;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.util.*;

@Service("shangHaiSuperviseService")
@Slf4j
@RequiredArgsConstructor
public class ShangHaiSuperviseService implements SuperviseService {

    private final ShangHaiDataCreateComponent dataCreateComponent;
    private final CommonMailService commonMailService;
    private final UploadUtil uploadUtil;
    private final ApplicationProperties properties;
    private final DeptRepository deptRepository;
    private final MedicalCredentialsRepository medicalCredentialsRepository;
    private final UploadRepository uploadRepository;
    private final DiagnosisCaRepository diagnosisCaRepository;
    private boolean needEnc = true;

    private String getFullPath(String host, String path) {
//        host = "http://127.0.0.1:8815/api";
        String url = UrlUtils.concatSegments(host, path);
        log.info("上海监管平台 请求url：{}", url);
        return url;
    }

    /**
     * 上传文件
     * @param upload
     * @param fileType
     * @param dto
     * @throws Exception
     */
    private UploadResponse fileUpload(Upload upload, FileType fileType, SuperviseDto dto, String uniqueId,
                                      boolean deleteFile) throws Exception {
        File file = new File(properties.getHome(), "temp/" + upload.getId() + "/" + upload.getOriginalName());
        uploadUtil.download(upload, file);
        File uploadFile = file;
        if (!upload.getOriginalName().endsWith(".pdf") && !(fileType == FileType.DOCTOR_CERT_INFO || fileType == FileType.IM_CHAT_INFO
                || fileType == FileType.FOLLOW_UP_INFO)) {
            uploadFile = new File(properties.getHome(), "temp/" + upload.getId() + "/" + upload.getOriginalName() + ".pdf");
            PDFUtil.convertImageToPDF(file, uploadFile);
            FileUtils.forceDelete(file);
        }
        return fileUpload(uploadFile, fileType, dto, uniqueId, deleteFile);
    }

    private UploadResponse fileVerify(Upload upload, FileType fileType, SuperviseDto dto, String uniqueId,
                                      Date businessCreateTime, String patName, String certNo, String certType) throws Exception {
        File file = new File(properties.getHome(), "temp/" + upload.getId() + "/" + upload.getOriginalName());
        uploadUtil.download(upload, file);
        File uploadFile = file;
        if (!upload.getOriginalName().endsWith(".pdf") && !(fileType == FileType.DOCTOR_CERT_INFO || fileType == FileType.IM_CHAT_INFO
            || fileType == FileType.FOLLOW_UP_INFO)) {
            uploadFile = new File(properties.getHome(), "temp/" + upload.getId() + "/" + upload.getOriginalName() + ".pdf");
            PDFUtil.convertImageToPDF(file, uploadFile);
            FileUtils.forceDelete(file);
        }
        return fileVerify(uploadFile, fileType, dto, uniqueId, businessCreateTime, patName, certNo, certType);
    }

    /**
     * 上传文件
     * @param uploadFile
     * @param fileType
     * @param dto
     * @throws Exception
     */
    private UploadResponse fileUpload(File uploadFile, FileType fileType, SuperviseDto dto, String uniqueId,
                                      boolean deleteFile) throws Exception {
        try {
            String pubKey = dto.getCode();
            OfflineHospital offlineHospital = dto.getOfflineHospital();
            Map<String, String> params = new HashMap<>();
            params.put("secretFlag", "N");
            params.put("fileType", fileType.name());
            params.put("hosOrgCode", SM2Utils.encryptHex(pubKey, dto.getHosOrgCode())); // 加密
            params.put("countyAreaCode", SM2Utils.encryptHex(pubKey, offlineHospital.getAddressCode())); // 加密
            OfflineHospital.Rank rank = offlineHospital.getRank();
            params.put("hospitalLevel", ((rank == null || rank == OfflineHospital.Rank.UNKNOWN) ? 9 : rank.ordinal()) + "");
            params.put("hospitalName", SM2Utils.encryptHex(pubKey, offlineHospital.getName())); // 加密
            params.put("uniqueId", SM2Utils.encryptHex(pubKey, uniqueId)); // 加密
            // 2025年06月18日09:34 监管平台接口更新，上传文件要求加密
//            byte[] keys = new byte[16];
//            byte[] iv = new byte[16];
//            SecureRandom random = new SecureRandom();
//            random.nextBytes(iv);
//            random.nextBytes(keys);
            String keys = pubKey.substring(0, 16);
            String iv = keys;
            params.put("key", SM2Utils.encryptHex(dto.getCode(), keys)); // 加密
//            params.put("key",  keys); // 加密
            params.put("iv", SM2Utils.encryptHex(dto.getCode(), iv)); // 加密
//            params.put("iv", iv); // 加密
            String fileName = uploadFile.getName();
            byte[] fileBytes = Files.readAllBytes(uploadFile.toPath());
            byte[] encryptFileBytes = SM4Utils.encrypt_CBC_Padding(keys.getBytes(), iv.getBytes(), fileBytes);
            log.info("上海监管平台文件上传入参 params：" + StandardObjectMapper.stringify(params));
            Headers headers = Headers.of("hosOrgCode", dto.getHosOrgCode());
            Response response = OkHttpUtils.upload(getFullPath(dto.getHost(), "/public/hospital/file/upload"),
                                                   encryptFileBytes, "file", fileName, params, headers);
            String body = OkHttpUtils.getResponseBody(response).get();
            log.info("上海监管平台文件上传出参 " + body);
            ResponseData<UploadResponse> responseData = StandardObjectMapper.readValue(body, new TypeReference<>() {});
            if (responseData.isSuccess()) {
                return responseData.getData();
            } else {
                throw new RuntimeException("监管平台文件上传失败");
            }
        } finally {
            if (deleteFile) {
                FileUtils.forceDelete(uploadFile);
            }
        }
    }

    /**
     * 文件验签
     * @param uploadFile
     * @param fileType
     * @param dto
     * @throws Exception
     */
    private UploadResponse fileVerify(File uploadFile, FileType fileType, SuperviseDto dto, String uniqueId,
                                      Date businessCreateTime, String patName, String certNo, String certType) throws Exception {
        try {
            String pubKey = dto.getCode();
            Map<String, String> params = new HashMap<>();
            // 验签标记【1-验证文件，2-验证文本】， 验证文件时文件不为空
            params.put("signFlag", "1");
            params.put("businessType", fileType.name());
            params.put("businessCreateTime", TimeUtils.dateToString(businessCreateTime, "yyyy-MM-dd HH:mm:ss"));
            params.put("patientName", patName);
            params.put("credentialId", certNo);
            params.put("credentialType", certType);
            params.put("uniqueId", uniqueId); // 加密
            // 2025年06月18日09:34 监管平台接口更新，上传文件要求加密
//            byte[] keys = StringUtil.stringTo16ByteKey(dto.getHospitalCode());
            String keys = pubKey.substring(0, 16);
            String iv = keys;
            params.put("key", SM2Utils.encryptHex(dto.getCode(), keys)); // 加密
//            params.put("key",  keys); // 加密
            params.put("iv", SM2Utils.encryptHex(dto.getCode(), iv)); // 加密
            String fileName = uploadFile.getName();
            byte[] fileBytes = Files.readAllBytes(uploadFile.toPath());
            byte[] encryptFileBytes = SM4Utils.encrypt_CBC_Padding(keys.getBytes(), iv.getBytes(), fileBytes);
            log.info("上海监管平台文件验签入参 params：" + StandardObjectMapper.stringify(params));
            Headers headers = Headers.of("hosOrgCode", dto.getHosOrgCode());
            Response response = OkHttpUtils.upload(getFullPath(dto.getHost(), "/public/hospital/file/upload"),
                                                   encryptFileBytes, "file", fileName, params, headers);
            String body = OkHttpUtils.getResponseBody(response).get();
            log.info("上海监管平台文件验签出参 " + body);
            ResponseData<UploadResponse> responseData = StandardObjectMapper.readValue(body, new TypeReference<>() {});
            if (responseData.isSuccess()) {
                return responseData.getData();
            } else {
                throw new RuntimeException("监管平台文件验签失败");
            }
        } finally {
            FileUtils.forceDelete(uploadFile);
        }
    }

    /**
     * 上报数据
     * @param path test后的路径
     * @param data 上报的未加密数据
     */
    private <T> ResponseData<T> reportData(SuperviseDto dto, String path, Object data) throws Exception {
        ResponseData<T> responseData = null;
        if (needEnc) {
            Map<String, String> headers = new HashMap<>();
            headers.put("hosOrgCode", dto.getHosOrgCode());
            Map<String, String> postData = new HashMap<>();

            String d = data instanceof String ? (String) data : StandardObjectMapper.stringify(data);
            postData.put("ciphertext", SM2Utils.encryptHex(dto.getCode(), "[" + d + "]"));
            log.info("上海监管平台上报入参原文 " + d);
            String pd = StandardObjectMapper.stringify(postData);
            log.info("上海监管平台上报入参密文 " + pd);
            Response response = OkHttpUtils.post(getFullPath(dto.getHost(), path), pd, headers);
            String body = OkHttpUtils.getResponseBody(response).get();
            log.info("上海监管平台上报出参 " + body);
            responseData = StandardObjectMapper.readValue(body, new TypeReference<>() {});
            responseData.setRawData(body);
        } else {
            Map<String, String> headers = new HashMap<>();
            headers.put("hosOrgCode", dto.getHosOrgCode());
            String d = data instanceof String ? (String) data : StandardObjectMapper.stringify(data);
            log.info("上海监管平台上报入参 " + d);
            Response response = OkHttpUtils.post(getFullPath(dto.getHost(), path), d, headers);
            String body = OkHttpUtils.getResponseBody(response).get();
            log.info("上海监管平台上报出参 " + body);
            responseData = StandardObjectMapper.readValue(body, new TypeReference<>() {});
            responseData.setRawData(body);
        }
        return responseData;
    }

    @Override
    @Async
    public void organizationBa(Hospital hospital, SuperviseDto dto) {
        Organization organization = null;

        try {
            organization = dataCreateComponent.getOrganization(dto, hospital);
            // 开始调用
            ResponseData<Object> response = reportData(dto, "/outRequest/hospitalInformation", organization);
            try {
                log.error("上传医疗许可证 {}", hospital.getCode());
                fileUpload(hospital.getPracticingLicense(), FileType.ORG_INFO, dto, dto.getHosOrgCode(), true);
            } catch (Exception e) {
                log.error("上传医疗许可证失败");
                throw new RuntimeException(e);
            }

            sendMail(hospital.getCode(), StandardObjectMapper.stringify(organization), "医院备案正常结束", response);
        } catch (Exception e) {
            log.error("医院备案异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(organization), "医院备案异常结束", e.getMessage());
        }
    }

    @Override
    @Async
    public void annualInspectionInfoBa(Hospital hospital, SuperviseDto dto) {
        HospitalAnnualInspectionInfo annualInspectionInfo = null;

        try {
            OfflineHospital offlineHospital = dto.getOfflineHospital();
            List<OfflineHospitalAnnualInspection> offlineHospitalAnnualInspections = AppContext.getInstance(
                OfflineHospitalAnnualInspectionRepository.class).findAllByOfflineHospitalOrderByYearDesc(offlineHospital);
            if (CollectionUtils.isEmpty(offlineHospitalAnnualInspections)) return;
            annualInspectionInfo = dataCreateComponent.getHospitalAnnualInspectionInfo(hospital,
                                                                                       offlineHospitalAnnualInspections.get(0),dto);
            // 开始调用
            ResponseData<Object> response = reportData(dto, "/outRequest/annualInspectionInfo", annualInspectionInfo);
            try {
                log.error("上传年检记录PDF {}", hospital.getCode());
                fileUpload(offlineHospitalAnnualInspections.get(0).getAnnualInspectionFile(), FileType.YEAR_CHECK_INFO, dto,
                           annualInspectionInfo.getLicenceNumber(), true);
                if (offlineHospitalAnnualInspections.get(0).getThreelevelEqualprotectionFile() != null) {
                    log.error("上传三级等保证书PDF {}", hospital.getCode());
                    fileUpload(offlineHospitalAnnualInspections.get(0).getThreelevelEqualprotectionFile(), FileType.THREE_GRADE_INFO, dto,
                               annualInspectionInfo.getThreelevelEqualprotectionCode(), true);
                }
            } catch (Exception e) {
                log.error("上传年检记录PDF");
                throw new RuntimeException(e);
            }

            sendMail(hospital.getCode(), StandardObjectMapper.stringify(annualInspectionInfo), "年检备案正常结束", response);
        } catch (Exception e) {
            log.error("年检记录上传异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(annualInspectionInfo), "年检备案异常结束", e.getMessage());
        }
    }


    @Override
    @Async
    public void departmentBa(Hospital hospital, SuperviseDto dto) {
//        List<Dept> deptList = deptRepository.findByHospital(hospital);
        List<Dept> deptList = deptRepository.findByHospitalAndEnabled(hospital, true);
        // 线上科室才需要备案
        for (Dept dept : deptList) {
            if (dept.getParentDeptId() == null) {
                // 一级科室备案
                departmentBa1(hospital, dept, dto);
//                departmentClassBa(hospital, dept, dto);
            } else {
                // 二级科室备案
                deptList.stream()
                        .filter(d -> Objects.equals(d.getId(), dept.getParentDeptId()))
                        .findFirst()
                        .ifPresent(firstDept -> {
                            departmentBa2(hospital, firstDept, dept, dto);
                            departmentClassBa(hospital, dept, dto);
                        });
            }
        }
    }

    /**
     * 一级科室信息上报
     * @param hospital
     * @param dept
     * @param dto
     */
    public void departmentBa1(Hospital hospital, Dept dept, SuperviseDto dto) {
        Department1 department = new Department1();
        try {
            // 获取科目相关的实体
            OfflineHospital offlineHospital = dto.getOfflineHospital();
            department.setHosOrgCode(dto.getHosOrgCode());
            department.setOrgCode(offlineHospital.getOrgCode());
            department.setHosName(hospital.getOrganizationName());
            department.setAreaCode(offlineHospital.getAddressCode());
            department.setAreaName(offlineHospital.getDistrict());
            department.setDeptCode(dept.getDeptCode());
            department.setDeptName(dept.getDeptName());
            department.setNormDeptCode(dept.getSubjectCode());
            department.setNormDeptName(dept.getSubjectName());
            department.setDeptDesc(dept.getIntroduction());
            department.setIsRegister("1");
            department.setIsDelete("0");
            department.setCreateDate(dept.getCreatedDate());
            department.setStatus(dept.getEnabled() ? "0" : "1");

            // 开始调用
            ResponseData<Object> response = reportData(dto, "/outRequest/firstDepartment", department);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(department), "一级科室信息上报异常结束", response);
        } catch (Exception e) {
            log.error("一级科室信息上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(department), "一级科室信息上报异常结束", e.getMessage());
        }
    }

    /**
     * 二级科室信息上报
     * @param hospital
     * @param firstDept
     * @param dept
     * @param dto
     */
    public void departmentBa2(Hospital hospital, Dept firstDept, Dept dept, SuperviseDto dto) {
        Department2 department = new Department2();
        try {
            // 获取科目相关的实体
            OfflineHospital offlineHospital = dto.getOfflineHospital();
            department.setHosOrgCode(dto.getHosOrgCode());
            department.setOrgCode(offlineHospital.getOrgCode());
            department.setHosName(hospital.getOrganizationName());
            department.setAreaCode(offlineHospital.getAddressCode());
            department.setAreaName(offlineHospital.getDistrict());
            department.setOneDeptCode(firstDept.getDeptCode());
            department.setOneDeptName(firstDept.getDeptName());
            department.setNormDeptCode(dept.getSubjectCode());
            department.setNormDeptName(dept.getSubjectName());
            department.setDeptDesc(dept.getIntroduction());
            department.setIsRegister("1");
            department.setIsDelete("0");
            department.setCreateDate(dept.getCreatedDate());
            department.setStatus(dept.getEnabled() ? "0" : "1");
            department.setMedicalSubjectsBak("内科");

            department.setDeptCode(dept.getDeptCode());
            department.setDeptName(dept.getDeptName());

            // 开始调用
            ResponseData<Object> response = reportData(dto, "/outRequest/secondaryDepartment", department);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(department), "二级科室信息上报异常结束", response);
        } catch (Exception e) {
            log.error("二级科室信息上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(department), "二级科室信息上报异常结束", e.getMessage());
        }
    }

    /**
     * 诊疗科室信息上报
     * @param hospital
     * @param dept
     * @param dto
     */
    public void departmentClassBa(Hospital hospital, Dept dept, SuperviseDto dto) {
        DepartmentClass department = new DepartmentClass();
        try {
            OfflineHospital offlineHospital = dto.getOfflineHospital();
            department.setHosOrgCode(dto.getHosOrgCode());
            department.setOrgCode(offlineHospital.getOrgCode());
            department.setHosName(hospital.getOrganizationName());
            department.setAreaCode(offlineHospital.getAddressCode());
            department.setAreaName(offlineHospital.getDistrict());

            department.setDepartCode(dept.getDeptCode());
            department.setDepartName(dept.getDeptName());

            // 开始调用
            ResponseData<Object> response = reportData(dto, "/outRequest/diagnosisTreatDepart", department);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(department), "诊疗科室信息上报异常结束", response);
        } catch (Exception e) {
            log.error("诊疗科室信息上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(department), "诊疗科室信息上报异常结束", e.getMessage());
        }
    }

    @Override
    @Async
    public void doctorBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto) {
        // 获取医生相关的实体
        Doctor doctor = null;
        try {
            doctor = dataCreateComponent.getDoctor(hospital, medicalWorker, dto);
            if (doctor == null) {
                return;
            }
            // 先上报信息
            ResponseData<Object> response = reportData(dto, "/outRequest/doctorInformation", doctor);

            // 后上传文件
            // personId 	; //	证件号 	 	是 	varchar(18) 	需要调取《文件上传及验证接口》，上传医生信息文件以及医生资格证书文件。
            // 上传医生资格证书和医生照片
            MedicalCredentials medicalCredential = medicalCredentialsRepository
                .findOneByMedicalWorker(medicalWorker).orElse(null);
            if (medicalCredential != null) {
                String[] certificationImages = medicalCredential.getCertificationImage().split(",");
                for (String imagesId : certificationImages) {
                    Upload cert = uploadRepository.getById(Long.valueOf(imagesId));
                    fileUpload(cert, FileType.DOCTOR_CERT_INFO, dto, doctor.getPersonId(), true);
                }
            }

            User user =
                AppContext.getInstance(UserRepository.class).findById(medicalWorker.getUser().getId()).orElseThrow();
            Upload picture =
                medicalWorker.getUser().getAvatarType() == AvatarType.UPLOAD && user.getUploadedAvatar() != null ?
                   uploadRepository.getById(user.getUploadedAvatar().getId()) : null;
            if (picture != null) {
                fileUpload(picture, FileType.DOCTOR_CERT_INFO, dto, doctor.getPersonId(), true);
            }

            sendMail(hospital.getCode(), StandardObjectMapper.stringify(doctor), "医生信息上报异常结束", response);
        } catch (Exception e) {
            log.error("医生信息上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(doctor), "医生信息上报异常结束", e.getMessage());
        }
    }

    @Async
    @Override
    public void pharmacistBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto) {
        // 获取医生相关的实体
        Pharmacist pharmacist = null;
        try {
            pharmacist = dataCreateComponent.getPharmacistBa(hospital, medicalWorker, dto);
            if (pharmacist == null) {
                return;
            }

            // 先上报信息
            ResponseData<Object> response = reportData(dto, "/outRequest/pharmacistInformation", pharmacist);
            // 再上报文件
            MedicalCredentials credentials = medicalCredentialsRepository.findOneByMedicalWorker(medicalWorker).orElse(null);
            if (credentials == null || StringUtils.isBlank(credentials.getCertificationImage())) {
                throw new RuntimeException("未找到药师证件信息 doctorName: " + pharmacist.getPharmacistName());
            }
            Upload upload = uploadRepository.getById(Long.parseLong(credentials.getCertificationImage().split(",")[0]));
//            private	String	idNumber 	; //	证件号 	 	是 	varchar(18) 	需要调取《文件上传及验证接口》，上传药师资格证书文件。
            fileUpload(upload, FileType.PHARMACIST_INFO, dto, pharmacist.getIdNumber(), true);

            sendMail(hospital.getCode(), StandardObjectMapper.stringify(pharmacist), "药师信息上报异常结束", response);
        } catch (Exception e) {
            log.error("药师信息上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(pharmacist), "药师信息上报异常结束", e.getMessage());
        }
    }

    // 挂号
    @Async
    @Override
    public void register(Hospital hospital, Long orderId, SuperviseDto dto) {
        RegisterRecord registerRecord = null;
        try {
            Thread.sleep(5000);
            log.info("上海监管平台复诊订单{}已挂号，开始上报------------------------", orderId);
            // 获取挂号相关的实体
            registerRecord = dataCreateComponent.getRegisterRecord(hospital, orderId, dto);
            if (registerRecord == null) {
                log.info("不是复诊订单，不需要上报");
                return;
            } else {
                registerRecord.setStatus(1);
            }
            // 上报信息
            ResponseData<Object> response = reportData(dto, "/outRequest/onlineReview", registerRecord);


            sendMail(hospital.getCode(), StandardObjectMapper.stringify(registerRecord), "在线复诊-未接诊上报正常结束", response);
        } catch (Exception e) {
            log.error("在线复诊-未接诊上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(registerRecord), "在线复诊-未接诊上报异常结束",
                     e.getMessage());
        }
    }

    @Override
    public void cancelRegistration(Long orderId, SuperviseDto dto) {
    }

    // 拒绝
    @Override
    @Async
    public void rejectRegistration(Hospital hospital, Long orderId, SuperviseDto dto) {
        RegisterRecord registerRecord = null;
        try {
            Thread.sleep(2000);
            log.info("上海监管平台复诊订单{}医生拒绝接诊，开始上报------------------------", orderId);

            // 获取挂号相关的实体
            registerRecord = dataCreateComponent.getRejectRegister(hospital, orderId, dto);
            if (registerRecord == null) {
                log.info("不是复诊订单，不需要上报");
                return;
            }
            ResponseData<Object> response = reportData(dto, "/outRequest/onlineReview", registerRecord);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(registerRecord), "在线复诊-医生拒绝接诊上报正常结束", response);
        } catch (Exception e) {
            log.error("在线复诊-医生拒绝接诊上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(registerRecord), "在线复诊-医生拒绝接诊上报异常结束", e.getMessage());
        }
    }

    // 就诊记录
    @Override
    @Async
    public void createVisitRecord(Hospital hospital, Long orderId, SuperviseDto dto) {
        RegisterRecord registerRecord = null;
        try {
            Thread.sleep(2000);
            log.info("上海监管平台复诊订单{}开始上报------------------------", orderId);
            // 获取挂号相关的实体
            registerRecord = dataCreateComponent.getRegisterRecord(hospital, orderId, dto);
            if (registerRecord == null) {
                log.info("不是复诊订单，不需要上报");
                return;
            } else {
                registerRecord.setStatus(2);
            }
            String m1 = registerRecord.getSjywqmz();
            String m2 = registerRecord.getYhryzsxlh();
            registerRecord.setSjywqmz(null);
            registerRecord.setYhryzsxlh(null);
            // 先上报信息后上传文件
            ResponseData<Object> response = reportData(dto, "/outRequest/onlineReview", registerRecord);


            //private String serialNumber; //	复诊流水号 	互联网医院复诊诊疗系统中产生的唯一复诊编码 	是 	 varchar(64)
            // 需要调取《文件上传及验证接口》，上传问诊聊天记录音视频文件以及电子病历PDF文件。
            File message = dataCreateComponent.getImMessageFile(orderId);
            if (message != null) {
                fileUpload(message, FileType.FOLLOW_UP_INFO, dto, registerRecord.getSerialNumber(), false);
            }
            DiagnosisCa diagnosisCa = diagnosisCaRepository.findOneByOrderId(orderId).orElse(null);
            if (diagnosisCa != null && diagnosisCa.getDiagnosisPdfId() != null) {
                Long pdfId = diagnosisCa.getDiagnosisPdfId();
                Upload upload = uploadRepository.getById(pdfId);
//                fileVerify(upload, FileType.MEDICAL_RECORD, dto,registerRecord.getSerialNumber(),
//                           registerRecord.getEndDate(), registerRecord.getPatientName(), registerRecord.getIdCard(),
//                           registerRecord.getIdType());
                fileUpload(upload, FileType.MEDICAL_RECORD, dto, registerRecord.getSerialNumber(), true);
            }
            Thread.sleep(5000);
            // 结束就诊
            registerRecord.setStatus(3);
            registerRecord.setImageVideoFileNumber(orderId + "");
            ImageVideoFileUrlDto imageVideoFileUrlDto = null;
            if (message != null) {
                imageVideoFileUrlDto = new ImageVideoFileUrlDto(message.getAbsolutePath(),
                                                               registerRecord.getReceiptTime(), 1,  1);
                registerRecord.setImageVideoFileUrl(StandardObjectMapper.stringify(Lists.newArrayList((imageVideoFileUrlDto))));

            } else {
                registerRecord.setImageVideoFileUrl("[]");
            }
            registerRecord.setSjywqmz(m1);
            registerRecord.setYhryzsxlh(m2);
            ResponseData<Object> response1 = reportData(dto, "/outRequest/onlineReview", registerRecord);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(registerRecord), "在线复诊上报正常结束", response);
        } catch (Exception e) {
            log.error("在线复诊上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(registerRecord), "在线复诊上报异常结束", e.getMessage());
        }
    }

    // 咨询记录
    @Override
    public void createConsultationRecord(Long orderId, SuperviseDto dto) {
    }

    // 咨询-取消接诊
    @Override
    public void createCancelConsultationRecord(Long orderId, SuperviseDto dto, OrderOperation.Step step) {
    }

    // 处方开立（西药）
    @Override
    public void prescribeWest(Long prescriptionOrderId, SuperviseDto dto) {
    }

    // 处方变更
    @Override
    public void changePrescription() {
        // 方法体
        // 暂时不对接
    }

    // 处方撤消
    @Override
    public void cancelPrescription(Long prescriptionOrderId, SuperviseDto dto) {
    }

    // 处方审核
    @Async
    @Override
    public void auditPrescription(Hospital hospital, Long prescriptionOrderId, SuperviseDto dto, Boolean pass, PrescriptionOrderCa orderCa) {
        List<PrescriptionWm> prescriptions = null;
        try {
            Thread.sleep(2000);
            // 获取挂号相关的实体
            prescriptions = dataCreateComponent.getPrescriptionExamine(hospital, prescriptionOrderId, pass, dto);

            // 先上传处方信息，再上传pdf
            ResponseData<Object> response = null;
            for (PrescriptionWm p : prescriptions) {
                response = reportData(dto, "/outRequest/prescriptionInfo", p);
            }

            if (pass) {
                if (orderCa == null) {
                    orderCa =
                        AppContext.getInstance(
                            PrescriptionOrderCaRepository.class).findOneByPrescriptionOrderIdAndEnabled(prescriptionOrderId, true).orElse(null);
                }
                Long pdfId = orderCa.getFinalPdfId();
                Upload upload = uploadRepository.getById(pdfId);
//                PrescriptionWm prescriptionWm = prescriptions.get(0);
//                fileVerify(upload, FileType.PRESCRIPTION_INFO, dto, prescriptionWm.getCfh(),
//                           prescriptionWm.getPrescriptionTime(), prescriptionWm.getHzxm(), prescriptionWm.getIdCard()
//                    , "01");
                fileUpload(upload, FileType.PRESCRIPTION_INFO, dto, prescriptions.get(0).getCfh(), true);
            }

            sendMail(hospital.getCode(), StandardObjectMapper.stringify(prescriptions), "处方信息上报异常结束", response);
        } catch (Exception e) {
            log.error("处方信息上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(prescriptions), "处方信息上报异常结束", e.getMessage());
        }

    }

    // 处方流转
    @Override
    public void transferPrescription() {
        // 方法体
        // 暂时不对接
    }

    // 药品配送
    @Override
    public void deliverMedicine(Long drugOrderId, SuperviseDto dto) {
    }

    @Override
    public void deliverMedicine(MedicineDeliver medicineDeliver, SuperviseDto dto) {
    }


    // 收费/退费
    @Override
    public void chargeAndRefund(ChargeAndRefundReq req, SuperviseDto dto) {
    }

    // 服务评价
    @Override
    public void evaluateService(Long evaluationId, SuperviseDto dto) {
    }

    // 投诉举报
    @Override
    @Async
    public void reportComplaint(Hospital hospital, Long ticketId, SuperviseDto dto) {
        Complaint complaint = null;
        try {
            Thread.sleep(2000);
            complaint = dataCreateComponent.getComplaint(hospital, dto, ticketId);

            ResponseData<Object> response = reportData(dto, "/outRequest/problemInformation", complaint);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(complaint), "问题信息上报异常结束", response);
        } catch (Exception e) {
            log.error("问题信息上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(complaint), "问题信息上报异常结束", e.getMessage());
        }
    }
    // 预约单上传
    @Override
    @Async
    public void appointmentInfoUpload(Hospital hospital, Long orderId, SuperviseDto dto) {
        AppointmentOrderInfo appointmentOrderInfo = null;
        try {
            Thread.sleep(2000);
            log.info("上海监管平台预约单信息{}开始上报------------------------", orderId);
            // 获取挂号相关的实体
            appointmentOrderInfo = dataCreateComponent.getAppointmentOrderInfo(hospital, orderId, dto);
            if (appointmentOrderInfo == null) {
                log.info("不是有效预约单，不需要上报");
                return;
            }
            // 先上报信息后上传文件
            ResponseData<Object> response = reportData(dto, "/outRequest/bookingInformation", appointmentOrderInfo);

            sendMail(hospital.getCode(), StandardObjectMapper.stringify(appointmentOrderInfo), "预约单信息上报正常结束", response);
        } catch (Exception e) {
            log.error("预约单信息上报异常结束", e);
            sendMail(hospital.getCode(), StandardObjectMapper.stringify(appointmentOrderInfo), "预约单信息上报异常结束",
                     e.getMessage());
        }
    }

    private void sendMail(String hospitalCode, String param, String title, ResponseData<Object> msg) {
        // 有错误
        if (msg != null && !msg.isSuccess()) {
            String content = StringUtil.formatHtml(title + "参数：" + param + "，失败原因：<font color=\"red\">%s</font>，请联系开发人员处理",
                    Objects.toString(msg.getRawData()));
            commonMailService.sendHtmlMail(hospitalCode, title, content);
        }
    }


    private void sendMail(String hospitalCode, String param, String title, String msg) {
        // 有错误
        String content = StringUtil.formatHtml(title + "参数：" + param + "，失败原因：<font color=\"red\">%s</font>，请联系开发人员处理", Objects.toString(msg));
        commonMailService.sendHtmlMail(hospitalCode, title, content);
    }

}
