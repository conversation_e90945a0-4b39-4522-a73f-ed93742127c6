package cn.taihealth.ih.supervise.dto.sh.ba;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 预约订单信息实体类
 */
@Data
public class AppointmentOrderInfo {

    /**
     * 预约单 ID
     * 是否必须: 是
     * 数据类型: varchar(64)
     */
    private String orderId;

    /**
     * 医院代码 (医院国家医保代码 12 位)
     * 是否必须: 是
     * 数据类型: varchar(12)
     */
    private String hosOrgCode;

    /**
     * 登记号 (医疗许可证上的登记号)
     * 说明: 医疗许可证上的登记号
     * 是否必须: 是
     * 数据类型: varchar(22)
     */
    private String orgCode;

    /**
     * 医院名称 (机构名称)
     * 说明: 与医疗许可证上名称保持一致，不能用简称
     * 是否必须: 是
     * 数据类型: varchar(60)
     */
    private String hosName;

    /**
     * 机构区划代码
     * 说明: 上海区域代码（以全国地区代码表为准）
     * 备注: 见字典规范/行政区划代码.pdf
     * 是否必须: 是
     * 数据类型: varchar(20)
     */
    private String areaCode;

    /**
     * 机构区划名称
     * 说明: 上海区域名称（以全国地区代码表为准）注：不要带“上海市”
     * 备注: 见字典规范/行政区划代码.pdf
     * 是否必须: 是
     * 数据类型: varchar(20)
     */
    private String areaName;

    /**
     * 预约单状态
     * 说明: 1：已预约, 2：已撤销, 3：已履约, 4：爽约, 5：停诊, 6：替诊
     * 是否必须: 是
     * 数据类型: varchar(1)
     */
    private String orderStatus;

    /**
     * 一级科室代码
     * 说明: 按照本院实际科室代码上传
     * 是否必须: 是
     * 数据类型: varchar(15)
     */
    private String oneDeptCode;

    /**
     * 一级科室名称
     * 说明: 按照本院实际科室名称上传
     * 是否必须: 是
     * 数据类型: varchar(30)
     */
    private String oneDeptName;

    /**
     * 二级科室代码
     * 说明: 按照本院实际科室代码上传
     * 是否必须: 是
     * 数据类型: varchar(15)
     */
    private String deptCode;

    /**
     * 二级科室名称
     * 说明: 按照本院实际科室名称上传
     * 是否必须: 是
     * 数据类型: varchar(30)
     */
    private String deptName;

    /**
     * 医生 | 门诊名称
     * 是否必须: 是
     * 数据类型: varchar(30)
     */
    private String resourceName;

    /**
     * 医生 | 门诊代码
     * 说明: 医生国家编码
     * 是否必须: 是
     * 数据类型: varchar(300)
     */
    private String resourceCode;

    /**
     * 预约类型
     * 说明: 1:医生, 2:门诊
     * 是否必须: 是
     * 数据类型: varchar(1)
     */
    private String orderType;

    /**
     * 预约渠道方式
     * 说明: 1：官网, 2：微信, 3：APP, 4：支付宝, 5：电话, 6：转诊预约, 7：1+1+1 转诊预约, 8：现场预约, 0：其他
     * 是否必须: 是
     * 数据类型: varchar(1)
     */
    private String channelCode;

    /**
     * 预约渠道描述
     * 说明: 如：上海市健康云 APP
     * 是否必须: 否
     * 数据类型: varchar(30)
     */
    private String channelName;

    /**
     * 号源 ID
     * 说明: 和复诊保持一致
     * 备注: 如果有复诊，此字段必填。
     * 是否必须: 否
     * 数据类型: varchar(64)
     */
    private String numSourceId;

    /**
     * 费用 (单位元)
     * 是否必须: 是
     * 数据类型: varchar(10)
     *
     */
    private String visitCost;

    /**
     * 申请日期
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10 16:34:32
     * 是否必须: 是
     * 数据类型: timestamp
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyDate;

    /**
     * 预约日期
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10 16:34:32
     * 是否必须: 是
     * 数据类型: timestamp
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;

    /**
     * 出诊时间
     * 说明: 1：上午, 2：下午, 3：晚上
     * 是否必须: 是
     * 数据类型: varchar(1)
     */
    private String timeRange;

    /**
     * 就诊时段_开始时间
     * 备注: 例：09:00
     * 是否必须: 是
     * 数据类型: varchar(5)
     */
    private String startTime;

    /**
     * 就诊时段_结束时间
     * 备注: 例：09:30
     * 是否必须: 是
     * 数据类型: varchar(5)
     */
    private String endTime;

    /**
     * 支付方式
     * 说明: 默认线上支付. 1：线上支付, 2：到院支付
     * 是否必须: 否
     * 数据类型: varchar(1)
     */
    private String payMode;

    /**
     * 支付状态
     * 说明: 1: 已付费, 2：未付费, 3：已退费
     * 是否必须: 是
     * 数据类型: varchar(1)
     */
    private String payState;

    /**
     * 预约患者类型
     * 说明: 默认普通预约. 1：普通预约, 2：1+1+1 转诊预约
     * 是否必须: 否
     * 数据类型: varchar(1)
     */
    private String patientType;

    /**
     * 1+1+1 签约编号
     * 说明: 1+1+1 转诊预约必填
     * 是否必须: 否
     * 数据类型: varchar(32)
     */
    private String patientId;

    /**
     * 就诊人卡号
     * 说明: 医院不支持无卡预约时，必填
     * 是否必须: 否
     * 数据类型: varchar(50)
     */
    private String mediCardId;

    /**
     * 就诊人卡类型
     * 说明: 0：社保卡, 1：医保卡, 2：统一自费就诊卡, 9：其他卡
     * 是否必须: 否
     * 数据类型: varchar(1)
     */
    private String mediCardType;

    /**
     * 就诊人证件类型
     * 说明: 按照卫标 CV02.01.101 身份证件类别代码执行编码。
     *       01：居民身份证, 02：居民户口本, 03：护照, 04：军官证, 05：驾驶证,
     *       06：港澳居民来往内地通行证, 07：台湾居民来往内地通行证, 19：母亲身份证, 99：其他法定有效证件
     * 是否必须: 是
     * 数据类型: varchar(2)
     */
    private String userCardType;

    /**
     * 就诊人证件号码
     * 是否必须: 是
     * 数据类型: varchar(32)
     */
    private String userCardId;

    /**
     * 就诊人姓名
     * 是否必须: 是
     * 数据类型: varchar(50)
     */
    private String userName;

    /**
     * 手机号码
     * 是否必须: 是
     * 数据类型: varchar(11)
     */
    private String userPhone;

    /**
     * 就诊人性别
     * 说明: 按国标 GB/T2261.1-2003 执行编码。
     *       0：未知的性别, 1：男性, 2：女性, 9：未说明的性别
     * 是否必须: 否
     * 数据类型: varchar(1)
     */
    private String userSex;

    /**
     * 就诊人出生日期
     * 说明: 格式：'yyyy-MM-dd'
     * 备注: 例如 2019-12-10
     * 是否必须: 否
     * 数据类型: date
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date userBd;

    /**
     * 就诊人联系地址
     * 是否必须: 否
     * 数据类型: varchar(200)
     */
    private String userContAdd;

    /**
     * 就诊人来源省
     * 说明: 省行政区划码
     * 备注: 《 行政区 划代码 - 明细》
     * 是否必须: 否
     * 数据类型: varchar(50)
     */
    private String sourceProvince;

    /**
     * 就诊人来源市
     * 说明: 市行政区划码
     * 备注: 《 行政区 划代码 - 明细》
     * 是否必须: 否
     * 数据类型: varchar(50)
     */
    private String sourceCity;

    /**
     * 就诊人来源区县
     * 说明: 区县行政区划码
     * 备注: 《 行政区 划代码 - 明细》
     * 是否必须: 否
     * 数据类型: varchar(50)
     */
    private String sourceArea;

    /**
     * 就诊人来源乡镇
     * 说明: 乡镇行政区划码
     * 备注: 《 行政区 划代码 - 明细》
     * 是否必须: 否
     * 数据类型: varchar(50)
     */
    private String sourceTownship;

    /**
     * 代预约人证件类型
     * 说明: 按照卫标 CV02.01.101 身份证件类别代码执行编码。
     *       01：居民身份证, 02：居民户口本, 03：护照, 04：军官证, 05：驾驶证,
     *       06：港澳居民来往内地通行证, 07：台湾居民来往内地通行证, 19：母亲身份证, 99：其他法定有效证件
     * 是否必须: 否
     * 数据类型: varchar(2)
     */
    private String replaceUserCardType;

    /**
     * 代预约人证件号码
     * 是否必须: 否
     * 数据类型: varchar(32)
     */
    private String replaceUserCardId;

    /**
     * 代预约人用户姓名
     * 是否必须: 否
     * 数据类型: varchar(50)
     */
    private String replaceUserName;

    /**
     * 加号标识
     * 说明: 0:否, 1:是
     * 是否必须: 是
     * 数据类型: varchar(1)
     */
    private String isAdd;

    /**
     * 加号原因
     * 说明: 加号标识为 1 时，加号途径必填
     * 是否必须: 否
     * 数据类型: varchar(300)
     */
    private String addReason;

    /**
     * 加号途径
     * 说明: 加号标识为 1 时，加号途径必填。1:医生, 2:护士, 9:其它
     * 是否必须: 否
     * 数据类型: varchar(1)
     */
    private String addOrderType;

    /**
     * 其它加号途径描述
     * 说明: 加号途径为 9 时必填
     * 是否必须: 否
     * 数据类型: varchar(100)
     */
    private String addOrderDesc;

    /**
     * Ip 地址
     * 是否必须: 否
     * 数据类型: varchar(15)
     */
    private String ipAddr;

    /**
     * 取号密码
     * 是否必须: 否
     * 数据类型: varchar(32)
     */
    private String numPassword;

    /**
     * 创建日期
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10 16:34:32
     * 是否必须: 是
     * 数据类型: timestamp
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 医保标志
     * 说明: 0：否, 1：是
     * 是否必须: 否
     * 数据类型: varchar(1)
     */
    private String medicalInsuranceMark;

    /**
     * 申请人姓名
     * 是否必须: 是
     * 数据类型: varchar(50)
     */
    private String applyUserName;

    /**
     * 申请人电话
     * 是否必须: 是
     * 数据类型: varchar(11)
     */
    private String applyUserPhone;

    /**
     * 预约时段开始时间
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10 16:34:32
     * 是否必须: 是
     * 数据类型: timestamp
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderStartDate;

    /**
     * 预约时段结束时间
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10 16:34:32
     * 是否必须: 是
     * 数据类型: timestamp
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderEndDate;

    /**
     * 审核时间
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10 16:34:32
     * 是否必须: 否
     * 数据类型: timestamp
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditDate;

    /**
     * 是否为入驻业务
     * 说明: 如果和第三方联合办医，则为是，其余为否. 0：否, 1：是
     * 是否必须: 是
     * 数据类型: varchar(1)
     */
    private String businessBeQuartered;


}
