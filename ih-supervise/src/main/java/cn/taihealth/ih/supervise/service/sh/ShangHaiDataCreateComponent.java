package cn.taihealth.ih.supervise.service.sh;

import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.*;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.enums.ClinicType;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.domain.enums.TrueFalseEnum;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.hospital.DiseaseRepository;
import cn.taihealth.ih.repo.hospital.order.DiagnosisCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.order.OrderExtraInfoRepository;
import cn.taihealth.ih.repo.order.OrderOperationRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.repo.order.OrderWorkerRepository;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.dto.sh.ac.Complaint;
import cn.taihealth.ih.supervise.dto.sh.ac.Message;
import cn.taihealth.ih.supervise.dto.sh.ac.PrescriptionWm;
import cn.taihealth.ih.supervise.dto.sh.ac.RegisterRecord;
import cn.taihealth.ih.supervise.dto.sh.ba.*;
import cn.taihealth.ih.supervise.utils.DataUtils;
import cn.taihealth.ih.wechat.service.api.DrugStoreDictService;
import cn.taihealth.ih.wechat.service.vm.drug.DicMedInfoDTO;
import com.alibaba.fastjson.JSONArray;
import com.beust.jcommander.internal.Lists;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import jodd.io.FileUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class ShangHaiDataCreateComponent {

    private final TicketRepository ticketRepository;
    private final OrderWorkerRepository orderWorkerRepository;
    private final OrderRepository orderRepository;
    private final DeptRepository deptRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final MedicalCredentialsRepository medicalCredentialsRepository;
    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final MedicalCaseRepository medicalCaseRepository;
    private final UserRepository userRepository;
    private final PatientRepository patientRepository;
    private final PrescriptionOrderCaRepository prescriptionOrderCaRepository;
    private final MedicalCaseDiseaseRepository medicalCaseDiseaseRepository;
    private final OrderExtraInfoRepository orderExtraInfoRepository;
    private final ElectronicMedicCardRepository electronicMedicCardRepository;
    private final TIMMessageRepository timMessageRepository;
    private final ApplicationProperties properties;
    private final OrderOperationRepository orderOperationRepository;
    private final DrugStoreDictService drugStoreDictService;
    private final DiagnosisCaRepository diagnosisCaRepository;

    public Organization getOrganization(SuperviseDto dto, Hospital hospital) {
        OfflineHospital offlineHospital = dto.getOfflineHospital();

        Organization organization = new Organization();
        organization.setHosOrgCode(dto.getHosOrgCode());
//         orgCode 医疗许可证登记号：42503915431010711A5201
//         orgSort 医院属性 	 	是 	varchar(1) 	1：市属 2：区属 3：社会办医疗机构
//         keyWorker; //	主 要 负责人 	 	是 	varchar(50)
//         subSidiary; //	单位隶属关系 	 	是 	varchar(1) 	1：市属 2：区属 3：社会办医疗机构
//         medicalSubjects; //	诊 疗 科目 	在备案的诊疗科目范围，如有多个用“，”分隔 	是 	varchar(1000)
//         infoSecurityLevelProtect; //	信 息 安全 等 级保护 	默认三级 	是 	varchar(1) 	1：一级 2：二级 3：三级
//         infoSecuritLevelProtectNumber; //	信 息 安全 等 级保 护 证书编号 	 	是 	varchar(50)
//         initialFunds; //	开 办 资金 额 数（万元） 	 	是 	decimal(18,2)
//         registrationNo; //	证 书 登记号 	 	是 	varchar(50)
//         issuingAuthority; //	发 证 机关 	 	是 	varchar(50)
//         issuanceDate; //	发 证 日期 	格式 'yyyy - MM - dd ' 	是 	date
//         medicalSubjectsBak; //	备 案 诊疗科目 	 	是 	varchar(1000) 	多个用“，”间隔
//         formsOfOwnership; //	所 有 制形式 	 	是 	varchar(1) 	1：私有制 2：公有制 3：混合所有制
        organization.setOrgCode(offlineHospital.getOrgCode());
        organization.setOrgSort(offlineHospital.getOrgSort().getValue() + "");
        organization.setKeyWorker(offlineHospital.getKeyWorker());
        organization.setSubSidiary(organization.getOrgSort());
        organization.setInfoSecurityLevelProtect(offlineHospital.getInfoSecurityLevelProtect().getValue() + "");
        organization.setInfoSecuritLevelProtectNumber(offlineHospital.getInfoSecuritLevelProtectNumber());
        organization.setInitialFunds(offlineHospital.getInitialFunds());
        organization.setRegistrationNo(offlineHospital.getRegistrationNo());
        organization.setIssuingAuthority(offlineHospital.getIssuingAuthority());
        organization.setIssuanceDate(offlineHospital.getIssuanceDate());
        organization.setMedicalSubjectsBak(offlineHospital.getMedicalSubjects());
        organization.setMedicalSubjects(offlineHospital.getMedicalSubjects());
        organization.setFormsOfOwnership(offlineHospital.getFormsOfOwnership().getValue() + "");
        organization.setHosName(hospital.getOrganizationName());
        organization.setAreaCode(offlineHospital.getAddressCode());
        organization.setAreaName(offlineHospital.getDistrict());
        organization.setHospitalAdd(offlineHospital.getAddress());
        organization.setHospitalGrade(offlineHospital.getRank() ==  OfflineHospital.Rank.UNKNOWN ? "9" : offlineHospital.getGrade().ordinal() + "");
        String level;
        switch (offlineHospital.getGrade()) {
            case SPECIFIC:
                level = "0";
                break;
            case BING:
                level = "3";
                break;
            case YI:
                level = "2";
                break;
            case JIA:
                level = "1";
                break;
            default:
                level = "4";
        }
        organization.setHospitalLevel(level);
        organization.setHospitalType(offlineHospital.getCategory() != null && offlineHospital.getCategory().contains("专科") ? "2" : "1");
        organization.setPayMode("1");
        organization.setOrderMode("1");
        organization.setIsRegister("0");
        organization.setIsSpTime("0");
        organization.setShortName(offlineHospital.getName());
        organization.setCreateDate(offlineHospital.getOpenDatetime());
        organization.setHostName(offlineHospital.getLegalRepresentative());
        organization.setExpirationStart(hospital.getLicenseStartDate());
        organization.setExpirationEnd(hospital.getLicenseEndDate());
        // TODO 临时添加了platform名称，后续要纠正
        organization.setPlatform(hospital.getName() + "线上服务平台");
        organization.setUnifiedInfoCode(hospital.getLicenseNumber());
        organization.setEstablishmentDate(offlineHospital.getOpenDatetime());
        organization.setSelypayMode("010");
        // 复诊判断依据 	 	是 	varchar(1) 	1:诊断代码 2:科室代码 3：诊断代码与科室代码
        organization.setFurtherConsultation("2");
        // 复诊判断数据来源 	 	是 	varchar(1) 	1：院内就诊数据 2：市级健康档案 3：医联健康档案 4：区级健康档案
        organization.setFurtherConsultationResource("1");
        organization.setFurtherConsultationTime("0");
        organization.setOrgManageType("2");
        organization.setEntityHosOrgCode(offlineHospital.getOrgTypeCode());

        return organization;
    }

    public HospitalAnnualInspectionInfo getHospitalAnnualInspectionInfo(Hospital hospital,
                                                                        OfflineHospitalAnnualInspection entityInfo,
                                                                        SuperviseDto dto) {
        OfflineHospital offlineHospital = dto.getOfflineHospital();
        List<OfflineHospitalAnnualInspection> offlineHospitalAnnualInspections = AppContext.getInstance(
            OfflineHospitalAnnualInspectionRepository.class).findAllByOfflineHospitalOrderByYearDesc(offlineHospital);
        if (CollectionUtils.isEmpty(offlineHospitalAnnualInspections)) return null;
        HospitalAnnualInspectionInfo info = new HospitalAnnualInspectionInfo();

        BeanUtils.copyProperties(entityInfo, info);

        info.setHosOrgCode(dto.getHosOrgCode());
        info.setOrgCode(offlineHospital.getOrgCode());
        info.setHosName(hospital.getOrganizationName());
        info.setAreaCode(offlineHospital.getAddressCode());
        info.setAreaName(offlineHospital.getDistrict());
        info.setOrgName(offlineHospital.getName());


        return info;
    }


    public Doctor getDoctor(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto) {
        OfflineHospital offlineHospital = dto.getOfflineHospital();
        User medicalWorkerUser = userRepository.findById(medicalWorker.getUser().getId()).orElseThrow();
        Dept dept = deptRepository.getById(medicalWorker.getDeptMedicalWorkers().get(0).getDept().getId());
        Dept firstDept = dept.getParentDeptId() == null ? dept : deptRepository.getById(dept.getParentDeptId());
        MedicalCredentials credentials = medicalCredentialsRepository.findOneByMedicalWorker(medicalWorker).orElse(null);
        log.info("构建医生：{} 上报信息-------", medicalWorkerUser.getFullName());
        Doctor doctor = new Doctor();
        doctor.setHosOrgCode(dto.getHosOrgCode());
        doctor.setOrgCode(offlineHospital.getOrgCode());
        doctor.setHosName(hospital.getOrganizationName());
        doctor.setAreaCode(offlineHospital.getAddressCode());
        doctor.setAreaName(offlineHospital.getDistrict());
        doctor.setOneDeptCode(firstDept.getDeptCode());
        doctor.setPrimaryOffice(firstDept.getDeptName());
        doctor.setDeptCode(dept.getDeptCode());
        doctor.setSecondOffice(dept.getDeptName());

        doctor.setResourceName(medicalWorkerUser.getFullName());
        doctor.setResourceCode(medicalWorker.getNationCode());
        doctor.setDoctorId(medicalWorker.getNationCode());

        doctor.setDoctTile(medicalWorker.getTitle());
        doctor.setPersonType("01");
        doctor.setPersonId(medicalWorker.getIdentity());
        doctor.setDoctSex(medicalWorker.getGender() == Gender.MALE ? "1" : (medicalWorker.getGender() ==  Gender.FEMALE ? "2" : "9"));
        doctor.setDoctInfo(medicalWorker.getIntroduction());
        doctor.setDoctSpecialty(medicalWorker.getAreasOfExpertise());
        doctor.setIsRegister("1");
        doctor.setIsDelete("0");
        doctor.setCreateDate(medicalWorker.getCreatedDate());
        doctor.setParticipationDate(medicalWorker.getParticipationDate());
        doctor.setClinicalYear(TimeUtils.age(medicalWorker.getParticipationDate()) + "");
        if (credentials != null) {
            doctor.setCertificateNo(credentials.getPractisingNumber());
        }
        doctor.setOrganzationName(hospital.getOrganizationName());
        doctor.setPlace(hospital.getOrganizationName());
        doctor.setIssuanceDate(medicalWorker.getIssuanceDate());
        doctor.setPracticeScope(medicalWorker.getPracticeScope());
        doctor.setDuty(medicalWorker.getPosition());
        doctor.setPersonnelType("1");
        doctor.setProfessionalJobSort(medicalWorker.getProfessionalJobSort());
        doctor.setOrgMark(offlineHospital.getOrgCode());
        doctor.setOrganzaionName(hospital.getOrganizationName());
        doctor.setEducation(medicalWorker.getEducation());
        return doctor;
    }

    public Pharmacist getPharmacistBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto) {
        OfflineHospital offlineHospital = dto.getOfflineHospital();
        User user = userRepository.findById(medicalWorker.getUser().getId()).orElseThrow();
//        if (medicalWorker.getDeptMedicalWorkers().isEmpty()) {
//            log.error("药师备案,未找到科室信息, " + user.getFullName());
//            return null;
//        }
        Dept dept = deptRepository.getById(medicalWorker.getDeptMedicalWorkers().get(0).getDept().getId());
        Dept firstDept = dept.getParentDeptId() == null ? dept : deptRepository.getById(dept.getParentDeptId());
        MedicalCredentials credentials = medicalCredentialsRepository.findOneByMedicalWorker(medicalWorker).orElse(null);
        log.info("构建药师：{} 上报信息-------", user.getFullName());
        Pharmacist pharmacist = new Pharmacist();
        pharmacist.setHosOrgCode(dto.getHosOrgCode());
        pharmacist.setOrgCode(offlineHospital.getOrgCode());
        pharmacist.setHosName(hospital.getOrganizationName());
        pharmacist.setAreaCode(offlineHospital.getAddressCode());
        pharmacist.setAreaName(offlineHospital.getDistrict());
        pharmacist.setOneDeptCode(firstDept.getDeptCode());
        pharmacist.setPrimaryOffice(firstDept.getDeptName());
        pharmacist.setDeptCode(dept.getDeptCode());
        pharmacist.setSecondOffice(dept.getDeptName());
        pharmacist.setParticipationDate(medicalWorker.getParticipationDate());
        pharmacist.setClinicalYear(TimeUtils.age(medicalWorker.getParticipationDate()) + "");
        if (credentials != null) {
            pharmacist.setCertificateNo(credentials.getPractisingNumber());
        }
        pharmacist.setPlace(hospital.getOrganizationName());
        pharmacist.setIssuanceDate(medicalWorker.getIssuanceDate());
        pharmacist.setPracticeScope(medicalWorker.getPracticeScope());
        pharmacist.setProfessionalJobSort(medicalWorker.getProfessionalJobSort());
        pharmacist.setOrgMark(offlineHospital.getOrgCode());
        pharmacist.setOrganzaionName(hospital.getOrganizationName());
        pharmacist.setEducation(medicalWorker.getEducation());
        pharmacist.setPharmacistId(medicalWorker.getNationCode());
        pharmacist.setPharmacistName(user.getFullName());
        pharmacist.setIdType("01");
        pharmacist.setIdNumber(medicalWorker.getIdentity());
        pharmacist.setGender(DataUtils.genderToCode(medicalWorker.getGender()));
        pharmacist.setIntroduction(medicalWorker.getIntroduction());
        pharmacist.setQualificationNo(medicalWorker.getCertificate());
        pharmacist.setTitle(medicalWorker.getTitle());
        pharmacist.setOrganzationName(hospital.getOrganizationName());

        return pharmacist;
    }

    public RegisterRecord getRegisterRecord(Hospital hospital, Long orderId, SuperviseDto dto) {
        RegisterRecord registerRecord = new RegisterRecord();
        Order order = orderRepository.findById(orderId).orElseThrow();
        if (order.getOrderType() != ClinicType.OUT) {
            return null;
        }
        OfflineHospital offlineHospital = dto.getOfflineHospital();
        Patient patient = patientRepository.findById(order.getPatient().getId()).orElseThrow();
        ElectronicMedicCard card = electronicMedicCardRepository.findById(order.getElectronicMedicCard().getId()).orElseThrow();
        MedicalCase medicalCase = medicalCaseRepository.findByOrderId(orderId).orElse(null);
        List<MedicalCaseDisease> diseases = Lists.newArrayList();
        if (medicalCase != null) {
            diseases = medicalCaseDiseaseRepository.findByMedicalCaseId(medicalCase.getId());
        }

        MedicalWorker medicalWorker = medicalWorkerRepository.findById(order.getDoctor().getId()).orElseThrow();
        User medicalWorkerUser = userRepository.findById(medicalWorker.getUser().getId()).orElseThrow();
        OrderWorker orderWorker = orderWorkerRepository.findOneByUserAndOrder(medicalWorkerUser, order).orElse(null);
        DiagnosisCa diagnosisCa = diagnosisCaRepository.findOneByOrderId(orderId).orElse(null);

        registerRecord.setSerialNumber(order.getId() + "");
        registerRecord.setHosOrgCode(dto.getHosOrgCode());
        registerRecord.setOrgCode(offlineHospital.getOrgCode());
        registerRecord.setHosName(hospital.getOrganizationName());
        registerRecord.setAreaCode(offlineHospital.getAddressCode());
        registerRecord.setAreaName(offlineHospital.getDistrict());
        registerRecord.setNumSourceId(order.getId() + "");
        registerRecord.setApplyTime(order.getRegisteredDate());
        registerRecord.setSignInTime(DateUtils.addMinutes(order.getRegisteredDate(), 3));
        long duration = ((order.getEndedDate() == null ? new Date() : order.getEndedDate()).getTime() - order.getRegisteredDate().getTime()) / 60_000;
        if (duration > 999) {
            duration = 999;
        }
        registerRecord.setDuration(duration + "");
        registerRecord.setKlx(card.getCardType() == ElectronicMedicCard.CardType.MEDICARE ? "1" : "2");
        registerRecord.setKh(card.getNumber());
        Patient.CardType cardType = patient.getCardType();
        String cd = cardType.getCode();
        if (cardType == Patient.CardType.FOREIGNERS_RESIDENCE_PERMIT || cardType == Patient.CardType.BIRTH_MEDICAL_CERTIFICATE
                || cardType == Patient.CardType.ENTRY_EXIT_PASS || cardType == Patient.CardType.TRAVEL_CERTIFICATE) {
            cd = "99";
        }
        registerRecord.setIdType(cd);
        registerRecord.setIdCard(patient.getIdCardNum());
        registerRecord.setPatientName(patient.getName());
        registerRecord.setAge(TimeUtils.age(patient.getBirthday()));
        registerRecord.setPhone(patient.getMobile());
        Dept dept = deptRepository.getById(order.getDept().getId()) ;
        registerRecord.setDeptCode(dept.getDeptCode());
        registerRecord.setDeptName(dept.getDeptName());
        registerRecord.setDiagnoseSubject(dept.getSubjectCode());
        registerRecord.setDocName(medicalWorkerUser.getFullName());
        registerRecord.setDoctorId(medicalWorker.getNationCode());
        registerRecord.setDiagnosisType("02");
        DiseaseRepository diseaseRepository = AppContext.getInstance(DiseaseRepository.class);
        // TODO 挂号即上报未接诊状态的复诊信息，此时默认诊断为睡眠障碍，正式上报时需修改
        String icd = "780.501";
        String chiefComplaint = "病情稳定，要求配药";
        String diagnosis = "睡眠障碍";
        String diagnosisName = "睡眠障碍";
        String method = "续方";
        if (CollectionUtils.isNotEmpty(diseases)) {
            icd = diseases.stream().map(u -> diseaseRepository.getById(u.getDisease().getId()).getGb95Code()).collect(Collectors.joining(
                "|"));
            diagnosisName = diseases.stream().map(MedicalCaseDisease::getDiseaseName).collect(Collectors.joining(
                "|"));
            diagnosis = diseases.stream().findFirst().map(MedicalCaseDisease::getDiseaseName).orElse("");
            chiefComplaint = StringUtils.isBlank(medicalCase.getSelfSpeak()) ? medicalCase.getSummary() :
                medicalCase.getSelfSpeak();
            method = medicalCase.getSummary();
        }
        registerRecord.setIcd(icd);
        registerRecord.setDiagnosis(diagnosis);
        registerRecord.setChiefComplaint(chiefComplaint);
        registerRecord.setMethod(method);

        String lastDiagnosisName = order.getDisease();
        String initDiagnosisName = diagnosisName;
        if (StringUtils.isNotBlank(lastDiagnosisName)) {
            lastDiagnosisName = StringUtils.join(StringUtil.split(order.getDisease(),"\\|"), "|");
        } else {
            lastDiagnosisName = initDiagnosisName;
        }
        registerRecord.setLastDiagnosIsName(lastDiagnosisName);
        registerRecord.setDiagnosticName(initDiagnosisName);
        registerRecord.setStatus(3);
        registerRecord.setSubmitTime(order.getRegisteredDate());
        registerRecord.setReceiptTime(order.getAdmissionDate());
        registerRecord.setEndDate(order.getEndedDate());
        registerRecord.setIsComplaint("0");
        registerRecord.setUpDataType("1");
        registerRecord.setUpDate(new Date());
        if (diagnosisCa != null) {
            registerRecord.setQmyw(diagnosisCa.getSignRaw()); //	签名原文 	 	否 	text
            registerRecord.setSjywqmz(diagnosisCa.getSignData()); //	数据原文签名值 	复诊病历CA签名，于上传文件保持一致，用于签名验证。 	否 	varchar(1024) 	status=3（已完成）为必填。
            registerRecord.setYhryzsxlh(diagnosisCa.getSignSn()); //	医护人员证书序列号 	接诊医生的CA证书序列号 	否 	 varchar(32) 	status=3（已完成）为必填。
            // CA不为空基本代表已完成就诊，可以有评价
            //        private int evaluate; //	评价等级 	评价，枚举值 	否 	Int(2) 	5：非常满意 4：满意 3：一般 2：不满意 1：非常满意 status=3（已完成）为必填。
            if (orderWorker == null || orderWorker.getRating() < 1) {
                registerRecord.setEvaluate(5);
                registerRecord.setEvaluateContent("无");
            } else {
                registerRecord.setEvaluate(6 - orderWorker.getRating());
                registerRecord.setEvaluateContent(orderWorker.getReview());
            }
        }
        return registerRecord;
    }

    public AppointmentOrderInfo getAppointmentOrderInfo(Hospital hospital, Long orderId, SuperviseDto dto) {
        AppointmentOrderInfo info = new AppointmentOrderInfo();
        Order order = orderRepository.findById(orderId).orElseThrow();
        if (order.getOrderType() != ClinicType.OUT) {
            return null;
        }
        OfflineHospital offlineHospital = dto.getOfflineHospital();
        Patient patient = patientRepository.findById(order.getPatient().getId()).orElseThrow();
        MedicalWorker medicalWorker = medicalWorkerRepository.findById(order.getDoctor().getId()).orElseThrow();
        User medicalWorkerUser = userRepository.findById(medicalWorker.getUser().getId()).orElseThrow();
        ElectronicMedicCard card = electronicMedicCardRepository.findById(order.getElectronicMedicCard().getId()).orElseThrow();

        info.setMediCardId(card.getNumber());
        info.setMediCardType("2");
        // TODO 线上暂不支持医保，固定传0
        info.setMedicalInsuranceMark("0");
        info.setOrderId(order.getId() + "");
        info.setHosOrgCode(dto.getHosOrgCode());
        info.setOrgCode(offlineHospital.getOrgCode());
        info.setHosName(hospital.getOrganizationName());
        info.setAreaCode(offlineHospital.getAddressCode());
        info.setAreaName(offlineHospital.getDistrict());
        info.setOrderStatus("1");
        Dept dept = deptRepository.getById(order.getDept().getId());
        Dept parentDept = deptRepository.getById(dept.getParentDeptId());
        info.setOneDeptCode(parentDept.getDeptCode());
        info.setOneDeptName(parentDept.getDeptName());
        info.setDeptCode(dept.getDeptCode());
        info.setDeptName(dept.getDeptName());

        info.setResourceName(medicalWorkerUser.getFullName());
        info.setResourceCode(medicalWorker.getNationCode());
        info.setOrderType("1");

        info.setChannelCode("2");
        info.setNumSourceId(order.getId()+"");
        info.setVisitCost(order.getRegistrationFee()/100 + "");
        info.setApplyDate(order.getCreatedDate());
        info.setOrderTime(order.getRegisteredDate());

        info.setTimeRange("1");
        info.setStartTime("00:00");
        info.setEndTime("23:59");
        info.setPayMode("1");
        info.setPayState("1");
        info.setPatientType("1");
        info.setUserCardType("01");
        info.setUserCardId(patient.getIdCardNum());
        info.setUserName(patient.getName());
        info.setUserPhone(patient.getMobile());
        info.setIsAdd("0");
        info.setCreateDate(order.getRegisteredDate());
        info.setApplyUserName(patient.getName());
        info.setApplyUserPhone(patient.getMobile());
        info.setOrderStartDate(TimeUtils.getStartOfDay(order.getRegisteredDate()));
        info.setOrderEndDate(TimeUtils.getEndOfDay(order.getRegisteredDate()));
        info.setBusinessBeQuartered("0");
        return info;
    }


    public File getImMessageFile(Long orderId) {
        Order order = orderRepository.findById(orderId).orElseThrow();
        List<TIMMessage> messages = timMessageRepository.findAllByGroupIdOrderBySequenceDesc(order.getImGroupId());
        if (messages.isEmpty()) {
            return null;
        }
        UserRepository userRepository = AppContext.getInstance(UserRepository.class);
        String patUser = userRepository.getById(order.getUser().getId()).getUsername();
        String doctorUser = userRepository.getById(medicalWorkerRepository.getById(order.getDoctor().getId()).getUser().getId()).getUsername();
        List<Message> uploadMessages = messages.stream().map(message -> {
            String msg = null;
            String msgType = null;
            JSONArray jsonArray;
            switch (message.getType()) {
                case TIMTextElem:
                    msgType = "1";
                    jsonArray = JSONArray.parseArray(message.getBody());
                    msg = jsonArray.getJSONObject(0).getJSONObject("MsgContent").getString("Text");
                    break;
                case TIMCustomElem:
                    msgType = "9";
                    jsonArray = JSONArray.parseArray(message.getBody());
                    msg = jsonArray.getJSONObject(0).getJSONObject("MsgContent").getString("Data");
                    break;
            }
            if (msg == null) {
                return null;
            }
            String direction;
            if (Objects.equals(message.getFromAccount(), patUser)) {
                direction = "1";
            } else if (Objects.equals(message.getFromAccount(), doctorUser)) {
                direction = "2";
            } else {
                return null;
            }
            Message m = new Message();
            m.setTime(new Date(message.getTime() * 1000));
            m.setSort(message.getSequence());
            m.setMsg(msg);
            m.setDirection(direction);
            m.setMsgType(msgType);
            return m;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (uploadMessages.isEmpty()) {
            return null;
        }
        File uploadFile = new File(properties.getHome(), "imFiles/msg_" + order.getId() + ".pdf");
        try {
            FileUtil.mkdirs(uploadFile.getParentFile());
            StandardObjectMapper.getInstance().writeValue(uploadFile, uploadMessages);
            return uploadFile;
        } catch (IOException e) {
            log.error("ca上报聊天文件写入异常", e);
            return null;
        }
    }

    @Transactional
    public RegisterRecord getRejectRegister(Hospital hospital, Long orderId, SuperviseDto dto) {
        Order order = orderRepository.findById(orderId).orElseThrow();
        if (order.getOrderType() != ClinicType.OUT) {
            return null;
        }
        RegisterRecord registerRecord = getRegisterRecord(hospital, orderId, dto);
        List<OrderOperation> operations = orderOperationRepository.findAllByOrder(order);
        registerRecord.setExplanation(operations.get(operations.size() - 1).getRemark());
        registerRecord.setEndDate(order.getEndedDate());
        registerRecord.setUpDataType("1");
        registerRecord.setUpDate(new Date());
        registerRecord.setStatus(4);
        return registerRecord;
    }

    @Transactional
    public List<PrescriptionWm> getPrescriptionWm(Hospital hospital, Long prescriptionOrderId, SuperviseDto suv) {
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(prescriptionOrderId);
        OfflineHospital offlineHospital = suv.getOfflineHospital();
        // 患者相关内容
        Patient patient = patientRepository.findById(prescriptionOrder.getPatient().getId()).orElseThrow();
        Order order = orderRepository.findById(prescriptionOrder.getOrder().getId()).orElseThrow();
        OrderExtraInfo extraInfo = orderExtraInfoRepository.findByOrderId(order.getId()).orElseThrow();
        ElectronicMedicCard card = electronicMedicCardRepository.findById(order.getElectronicMedicCard().getId()).orElseThrow();
        // 医生
        MedicalWorker medicalWorker = medicalWorkerRepository.findById(order.getDoctor().getId()).orElseThrow();
        PrescriptionOrderCa orderCa = prescriptionOrderCaRepository.findAllByPrescriptionOrderIdAndEnabled(prescriptionOrderId,true)
                .stream().findFirst().orElseThrow();
        List<Prescription> prescriptions = prescriptionOrder.getPrescription();

        MedicalWorker reviewer = medicalWorkerRepository.findById(prescriptionOrder.getDoctorReview().getId()).orElse(null);

        PrescriptionWm prescriptionWm = new PrescriptionWm();
        prescriptionWm.setCfh(prescriptionOrder.getHisRecipeNo()); //	处方号 	本次复诊若包含多条药品信息，开具处方号应保持一致 	是 	varchar(50) 	需要调取《文件上传及验证接口》，上传处方笺pdf。
        prescriptionWm.setOrderNumber(prescriptionOrder.getOrder().getId() + ""); //	复诊号 	复诊记录唯一号 	是 	varchar(50)
        prescriptionWm.setHosOrgCode(suv.getHosOrgCode()); //	医院编码 	医院国家医保代码12位 	是 	varchar(12)
        prescriptionWm.setOrgCode(offlineHospital.getOrgCode()); //	登记号 	医疗许可证上的登记号 	是 	varchar(22) 	登记号
        prescriptionWm.setHosName(hospital.getOrganizationName()); //	医院名称 	机构名称 	是 	varchar(60) 	与医疗许可证商名称保持一致，不能用简称
        prescriptionWm.setAreaCode(offlineHospital.getAddressCode()); //	机构区划代码 	上海区域代码（以全国地区代码表为准） 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
        prescriptionWm.setAreaName(offlineHospital.getDistrict()); //	机构区划名称 	上海区域名称（以全国地区代码表为准）注：不要带“上海市” 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
        prescriptionWm.setPrescriptionUniqueNumber(prescriptionOrder.getHisRecipeNo()); //	互联网处方唯一号 	处方外流使用，可与互联网医院处方号一致，在流转过程中不可改变。 	是 	varchar(50)
        prescriptionWm.setPrescriptionResource("1"); //	处方来源 	目前只对复诊进行监管上报，请填写1. 	是 	varchar(1) 	1：复诊 2：家庭医生 3：分诊
        prescriptionWm.setSerialNumber(prescriptionOrder.getOrder().getId() + ""); //	关联复诊订单编号 	和onlinereview表的SERIAL_NUMBER关联 	 是 	 varchar(64)
        prescriptionWm.setPrescriptionMedicalInsuranceName(patient.getIdCardNum()); //	处方医保备案号 	处方医保备案号是指医疗保险参保人员在医保系统内的唯一身份识别编码，用于方便医保部门对医疗费用进行管理，同时也方便参保人员进行医保报销。可用就诊人身份证号 	是 	varchar(50)
        prescriptionWm.setFkdq(offlineHospital.getAddressCode()); //	发卡地区 	上海区域代码（以全国地区代码表为准），外地按国家行政区划码填写 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
        prescriptionWm.setKh(card.getNumber()); //	卡号 	同onlinereview 的KH 	是 	varchar(50)
        prescriptionWm.setKlx("2"); //	卡类型 	同onlinereview 的KLX 	是 	varchar(1) 	0 ：社保卡 1 ：医保卡 2 ：统一自费就诊卡 9 ：其他卡
        prescriptionWm.setKfrq(prescriptionOrder.getCommitedDate()); //	开方日期 	格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
        prescriptionWm.setHzxm(patient.getName()); //	患者姓名 	 	是 	varchar(20)
        prescriptionWm.setXb(DataUtils.genderToCode(patient.getGender())); //	性别 	按国标 GB/T2261.1-2003 执行编码。  	是 	varchar(1) 	0：未知的性别 1：男性 2：女性 9：未说明的性别
        prescriptionWm.setHzsjh(patient.getMobile()); //	患者手机号 	 	是 	varchar(30)
        prescriptionWm.setIdCard(patient.getIdCardNum()); //	患者身份证号 	 	是 	varchar(18)
        prescriptionWm.setZsksfbz("0"); //	审方标志 	 	是 	varchar(1) 	0：否  1：是
        prescriptionWm.setSfbz("0"); //	收费标志 	处方收费 	是 	varchar(1) 	0：否  1：是
        prescriptionWm.setHzbz("0"); //	核账标志 	 	是 	varchar(1) 	0：否  1：是
        prescriptionWm.setQmyw(prescriptionOrder.getSignRaw()); // TODO:	签名原文 	与处方号和处方笺保持一致，多条明细应合并到一张处方笺 	否 	text
        prescriptionWm.setGrzsxlh(medicalWorker.getNationCode());
        prescriptionWm.setGrdzqm(orderCa.getDoctorSign()); // TODO:	医生人员电子签名 	开立处方医生CA签名 	是 	text
//        prescriptionWm.setDwdzqm(orderCa.getDoctorDigestValue()); // TODO:	单位电子签名 	 	否 	varchar(512)
        prescriptionWm.setCcf("0"); //	是否是长处方 	 	是 	varchar(1) 	0：否  1：是
        prescriptionWm.setShtg("1"); //	是否审核通过 	 	是 	varchar(1) 	0：否  1：是
        prescriptionWm.setRationalFlag("0"); //	是否经过合理用药判断标志 	 	是 	varchar(1) 	0：否  1：是
        prescriptionWm.setNumSourceId(prescriptionOrder.getOrder().getId() + ""); //	号源ID 	和bookinginformation
        // 表的NUM_SOURCE_ID关联 	是
        // varchar(64)
        if (prescriptionOrder.getStatus() != PrescriptionOrder.Status.UNREVIEWED
                && prescriptionOrder.getStatus() != PrescriptionOrder.Status.REVIEWING && reviewer != null) {
            User reviewerUser = userRepository.findById(reviewer.getUser().getId()).orElseThrow();
            prescriptionWm.setReviewPharmacistName(reviewerUser.getFullName()); //	审核药师姓名 	 	是 	varchar(50)
            prescriptionWm.setReviewPharmacistId(reviewer.getNationCode()); //	审核药师国家编码 	同药师信息上报pharmacistId 	是 	varchar(50)
            prescriptionWm.setReviewDate(prescriptionOrder.getReviewTime()); //	审核时间 	格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
        }
        prescriptionWm.setPrescriptionType("X"); //	处方类型 	 	是 	varchar(1) 	X ：西药 Z ：中成药 C ：中草药
        prescriptionWm.setPrescriptionTime(prescriptionOrder.getCommitedDate()); //	开方时间 	格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
        prescriptionWm.setPrescriptionStartDate(prescriptionOrder.getCommitedDate()); //	处方开始日期 	格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
        prescriptionWm.setPrescriptionEndDate(prescriptionOrder.getExpirationDate()); //	处方开始日期 	格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
        prescriptionWm.setPrescriptionValidity(prescriptionOrder.getExpirationDate()); //	处方有效期 	格式 'yyyy - MM - dd HH24:mi:ss' 	是 	timestamp 	2019/12/10 16:34
        prescriptionWm.setDispensingPharmacistName(null); //	发药药师姓名 	 	否 	varchar(50) 	prescriptionStatus为5（处方发药）时候质控必填，其它情况按照实际填写。
        prescriptionWm.setDispensingPharmacistId(null); //	发药药师国家编码 	同药师信息上报pharmacistId 	否 	varchar(50) 	prescriptionStatus为5（处方发药）时候质控必填，其它情况按照实际填写。
        prescriptionWm.setDispensingDate(null); //	发药时间 	格式 'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	prescriptionStatus为5（处方发药）时候质控必填，其它情况按照实际填写。
        prescriptionWm.setPrescriptionStatus("2"); // //	处方状态 	 	是 	varchar(1) 	1:处方开具 2:处方审核 3:处方复核 4:处方调配和配药 5:处方发药 6:处方存档 7:处方作废
        prescriptionWm.setPrescriptionWriteOffStatus("2"); // //	处方核销状态 	处方核销要根据处方状态来。如是线下医院取药，已经发药的算核销，如是物流配送，配送到家算核销，如果是药店取药用不到这个核销，只要是在处方有效期内就是使用中。 	是 	varchar(1) 	1：待审核 2：已审核 3：使用中 4：已核销 5：已作废 6：已失效
        prescriptionWm.setWriteOffWay(null);//	处方核销方式 	 	否 	varchar(1) 	prescriptionWriteOffStatus为4：已核销必填 1：人工核销 2：电子核销
        prescriptionWm.setWriteOffDate(null); //	处方核销时间 	格式 'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	prescriptionWriteOffStatus为4：已核销必填 2019-12-10 16:34:32
        prescriptionWm.setWriteOffOrg(null); //	处方核销单位 	 	否 	varchar(50) 	prescriptionWriteOffStatus为4：已核销必填
        prescriptionWm.setPrescriptionWriteOffEmployee(null); //	处方核销人 	 	否 	varchar(50) 	prescriptionWriteOffStatus为4：已核销必填
        prescriptionWm.setDeliveryDate(null); //	处方配送日期 	格式 'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	配送方式shfs=1为物流配送必填。
        prescriptionWm.setDeliveryFinishDate(null); //	处方完成配送日期 	格式 'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	配送方式shfs=1为物流配送必填。
        prescriptionWm.setPayment(null); //	配送费用是否支付 	配送支付情况 	否 	varchar(1) 	配送方式shfs=1为物流配送必填。 0：否  1：是
        prescriptionWm.setThirdPaymentTransactionNo(null); //	第三方支付交易流水号 	payment 配送费用是否支付 为：1时，必填 	否 	varchar(50)
        prescriptionWm.setDeliveryCost(null); //	配送金额 	 	否 	decimal(18,3) 	配送方式shfs=1为物流配送必填。
        prescriptionWm.setSqpsddm(null); //	配送点代码 	 	否 	varchar(50) 	配送方式shfs=1为物流配送必填。 按照医院自己的实际设立的配送点填写
        prescriptionWm.setSqpsdmc(null); //	配送点名称 	 	否 	varchar(250) 	配送方式shfs=1为物流配送必填。 按照医院自己的实际设立的配送点填写
        prescriptionWm.setShfs(null); //	送货方式 	 	否 	varchar(1) 	0：医院取药 1：物流配送 2：药店取药
        prescriptionWm.setHzshdz(null); //	患者送货地址 	送货方式为1: 物流配送,此字段必填。 	否 	varchar(200) 	配送方式为物流配送，则必填。
        prescriptionWm.setDeliveryAbnormal(null); //	配送异常状态 	 	否 	varchar(1) 	1 ：拒付件 2 ：拒收件 3 ：发错件 4 ：无快件
        prescriptionWm.setBusinessBeQuartered("0"); //	是否为入驻业务 	如果和第三方联合办医，则为是，其余为否 	是 	varchar(1) 	0：否  1：是
        List<String> drugCodes = prescriptions.stream().map(Prescription::getDrugCode).collect(Collectors.toList());
        List<DicMedInfoDTO> drugs = drugStoreDictService.getDrugDictsByCodes(hospital.getCode(), drugCodes);
        Map<String, DicMedInfoDTO> drugsMap = drugs.stream().collect(Collectors.toMap(DicMedInfoDTO::getCode,
                t -> t, (t1, t2) -> t1));

        int totalPrice = 0;
        List<PrescriptionWm> ps = new ArrayList<>();
        for (Prescription p : prescriptions) {
            try {
                DicMedInfoDTO drug = drugsMap.get(p.getDrugCode());
                PrescriptionWm wm = prescriptionWm.clone();
                wm.setCfmxh(p.getId() + ""); //	处方明细号 	 	是 	varchar(50)
                wm.setYpbm(p.getDrugCode()); //	药品编码 	 	是 	varchar(100)
                wm.setYpmc(p.getDrugName());//	药品名称 	 	是 	varchar(200)
                wm.setTym(p.getDrugName()); //	通用名 	国家食药监批文的注册通 用名，或者是本院自行界 定的通用名。 	否 	varchar(100)
                wm.setGg(p.getDosageSpec()); //	规格 	 	是 	varchar(100)
                wm.setBzdw(p.getUnit()); //	包装单位 	 	是 	varchar(50)
                wm.setSccj(drug.getManufacturer()); // //	生产厂家 	 	是 	varchar(400)
                wm.setYpsl(BigDecimal.valueOf(p.getQuantity())); //	药品数量 	开药总数 	是 	decimal(18,3)
                wm.setCgdj(drug.getSourcingPrice()); // //	采购单价 	药品采购单价 	是 	decimal(18,3)
                wm.setSfje(BigDecimal.valueOf(p.getPrice())); //	收费金额 	收费金额 	是 	decimal(18,3)
                totalPrice += p.getPrice();
                wm.setItemNormCode(drug.getCode()); //	项目标准代码 	医保统一要求的收费编码 	是 	varchar(100)
                wm.setYppc(null); //	药品批次 	 	否 	varchar(50)
                wm.setDrugVariety(null); //	药品品种 	 	否 	varchar(50)
                wm.setAntibiotics(drug.getIsAntimicrobial() == TrueFalseEnum.TRUE ? "1" : "0"); //	抗菌药物 	 	是 	varchar(1) 	0：否  1：是
                wm.setDosageCode(drug.getDicDosageForm().getSupervisionCode()); //	剂型代码 	按照 CV08.50.002 药物剂型代码编码 	是 	varchar(2) 	参见 字典规范/CV 08.50.002 药物剂型代码.pdf
                wm.setMedicalWay(p.getRouteCode()); //	用药途径代码 	按照 CV06.00.102 用药途径代码编码 	是 	varchar(3) 	参见 字典规范/CV06.00.102 用药途径代码.pdf
                wm.setMedicalDay(p.getTimes() * p.getTreatmentUnit().getDay()); //	用药天数 	 	是 	Int(10)
                wm.setYf(p.getUseage()); //	用法 	用药说明 	是 	varchar(50)
                wm.setBz(p.getRemark()); //	备注 	 	否 	varchar(255)
                wm.setInjectionsNumber(0); //	注射剂数 	 	是 	Int(10)
                wm.setPlaster(0); //	贴数 	 	否 	Int(10) 	中药处方使用
                wm.setDispensingNumberUnit(p.getUnit()); //	发药数量单位 	发药包装单位 	是 	varchar(6)
                wm.setFrequencyCode(StringUtils.upperCase(p.getUseFrequencyCode()));
                wm.setJl(BigDecimal.valueOf(p.getSingle())); //	每次使用剂量数量 	数字 	是 	decimal(18,2)
                wm.setDw(p.getSingleUnit()); //	每次使用剂量(数量)单位 	例如克(g)、毫克(mg)、毫升(ml);U.、I.U.、片、粒，瓶、小时、日、个、次等。对应不同药品使用不同单位 	是 	varchar(50)
                wm.setJydm(null); //	中药煎煮法代码 	中 药 必 填 , 当prescriptionType 处方类型为：“C”,必填，否则为空。 	否 	varchar(1) 	1：包煎 2：冲服 3：后煎 4：后下 5：另包 6：先煎 7：烊化 9：其他
                ps.add(wm);
            } catch (CloneNotSupportedException e) {
                log.error("处方上报对象克隆失败", e);
                return null;
            }

        };
        int finalTotalPrice = totalPrice;
        ps.forEach(p -> p.setPrescriptionCost(BigDecimal.valueOf(finalTotalPrice)));
        return ps;
    }


    @Transactional
    public List<PrescriptionWm> getPrescriptionExamine(Hospital hospital, Long prescriptionId, Boolean pass, SuperviseDto suv) {
        List<PrescriptionWm> ps = getPrescriptionWm(hospital, prescriptionId, suv);
        ps.forEach(p -> {
            if (pass) {
                p.setShtg("1");
            } else {
                p.setShtg("0");
            }
            p.setZsksfbz("1");
            p.setSfbz("0");
            p.setPrescriptionStatus("2");
        });
        return ps;
    }

    public Complaint getComplaint(Hospital hospital, SuperviseDto suv, Long ticketId) {
        Ticket ticket = ticketRepository.findById(ticketId).orElse(null);
//        if (ticket != null) {
        if (ticket != null && ticket.getType() == Ticket.Type.COMPLAINT && Objects.equals(ticket.getResponseType(), 1)) {
            OfflineHospital offlineHospital = suv.getOfflineHospital();
            Complaint complaint = new Complaint();
            complaint.setHosOrgCode(suv.getHosOrgCode());
            complaint.setOrgCode(offlineHospital.getOrgCode());
            complaint.setHosName(hospital.getOrganizationName());
            complaint.setAreaCode(offlineHospital.getAddressCode());
            complaint.setAreaName(offlineHospital.getDistrict());
            complaint.setInstitutionalStatus("0");
            complaint.setInstitutionalCode(offlineHospital.getOrgCode());
            complaint.setInstitutionName(hospital.getOrganizationName());
            String content = ticket.getContent();
            complaint.setIssueNames(content.length() > 50 ? content.substring(0, 50) : content);
            complaint.setQuestionContent(content.length() > 300 ? content.substring(0, 300) : content);
            complaint.setProblemStatus(ticket.getStatus().name());
            complaint.setEventOccurred(ticket.getSuggestTypes());
            complaint.setProcessingStatus(ticket.getResponseType() + "");
            complaint.setOccurrenceTime(ticket.getCreatedDate());
            complaint.setTransactor(ticket.getTransactor());
            complaint.setProcessingTime(ticket.getUpdatedDate());
            String responseContent = ticket.getResponseContent();
            complaint.setHandlingOpinions(responseContent.length() > 300 ? responseContent.substring(0, 300) : responseContent);
            return complaint;
        }
        return null;
    }
}
