package cn.taihealth.ih.supervise.dto.ln.ac;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 挂号
 */
@Data
public class RegisterRecord {

    // 患者就诊卡证类型
    private String idcardTypeCode; // 字符，长度2

    // 患者就诊卡证号码
    private String idcardNo; // 字符，长度24

    // 姓名
    private String name; // 字符，长度40

    // 性别代码
    private String genderCode; // 字符，长度1

    // 出生日期
    private String birthdate; // 日期，长度8

    // 就诊医疗机构编码
    private String unifiedOrgCode; // 字符，长度18

    // 就诊医疗机构名称
    private String orgName; // 字符，长度80

    // 就诊科室编码
    private String deptCode; // 字符，长度50

    // 就诊科室名称
    private String deptName; // 字符，长度40

    // 就诊科室对应诊疗科目编码
    private String deptClassCode; // 字符，长度9

    // 就诊科室对应诊疗科目名称
    private String deptClassName; // 字符，长度40

    // 是否挂专家号
    private String ifExpert; // 字符，长度1

    // 医师身份证号
    private String doctIdcardNo; // 字符，长度18

    // 医师姓名
    private String doctName; // 字符，长度40

    // 预约就诊日期
    private String visitDate; // 日期，长度8

    // 预约就诊时间
    private String visitTime; // 时间，长度6

    // 门诊编号
    private String visitNo; // 字符，长度36

    // 挂号类别
    private String visitType; // 字符，长度1

    // 挂号费用
    private String examinFee; // 数值，长度10,2

    // 挂号时间
    private String recordDateTime; // 日期时间，长度15

    // 看诊方式
    private String visitTypeCode; // 字符，长度1
}
