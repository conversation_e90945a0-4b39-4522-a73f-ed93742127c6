package cn.taihealth.ih.supervise.dto.sh.ba;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 互联网医生
 */
@Data
public class Pharmacist {

    private String hosOrgCode; //	医院代码 	医院国家医保代码12位 	是 	varchar(12)
    private String orgCode; //	登记号 	医疗许可证上的登记号 	是 	varchar(22) 	登记号
    private String hosName; //	医院名称 	机构名称 	是 	varchar(60) 	与医疗许可证商名称保持一致，不能用简称
    private String areaCode; //	机构区划代码 	上海区域代码（以全国地区代码表为准） 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String areaName; //	机构区划名称 	上海区域名称（以全国地区代码表为准）注：不要带“上海市” 	是 	varchar(20) 	见字典规范/行政区划代码.pdf
    private String pharmacistId; //	药师国家编码 	 	是 	varchar(50)
    private String resource; //	来源 	 	否 	varchar(1) 	0：卫监所 1：医院填报
    private String pharmacistName; //	药师姓名 	 	是 	varchar(30)
    private String oneDeptCode; //	一级科室代码 	按照本院实际科室代码上传 	否 	varchar(15)
    private String primaryOffice; //	一级科室名称 	按照本院实际科室名称上传 	否 	varchar(30)
    private String deptCode; //	二级科室代码 	按照本院实际科室代码上传 	是 	varchar(15)
    private String secondOffice; //	二级科室名称 	按照本院实际科室名称上传 	是 	varchar(30)
    private String idType; //	证件类型 	按照卫标 CV02.01.101 身份证件类别代码执行编码。 	是 	varchar(2) 	01：居民身份证 02：居民户口本 03：护照 04：军官证 05：驾驶证 06：港澳居民来往内地通行证 07：台湾居民来往内地通行证 19：母亲身份证 99：其他法定有效证件
    private String idNumber; //	证件号 	 	是 	varchar(18) 	需要调取《文件上传及验证接口》，上传药师资格证书文件。
    private String gender; //	性别 	按国标 GB/T2261.1-2003 执行编码。  	是 	varchar(1) 	0：未知的性别 1：男性 2：女性 9：未说明的性别
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthDate; //	出生日期 	格式 'yyyy - MM - dd' 	否 	date 	2019/12/10
    private String phoneNumber; //	联系电话 	 	否 	varchar(11)
    private String education; //	学历 	按照 GB/T 4658-2006《学历代码》 执行编码。 	否 	varchar(20) 	参见 字典规范/GBT 4658-2006 学历代码.pdf
    private String nation; //	民族 	按照 GB3304-1991  中国各民族名称的罗马字母拼写法和代码执行编码。 	否 	varchar(20) 	参见  字典规范/GB 33041991 民族代码.pdf
    private String speciality; //	专长 	 	否 	varchar(200)
    private String title; //	职称 	编码。按国标 GB/T8561-2001  专业技术职务代码执行 	是 	varchar(3) 	240：卫生技术人员（药剂） 241：主任药师 242：副主任药师 243：主管药师 244：药师 245：药士
    private String professionalJobSort; //	专业技术职务类别 	按照： WS364.15卫生信息数据源值域代码 第15部分： 卫 生 人 员CV08.30.005 执行编码。 	否 	varchar(1) 	1：正高 2：副高 3：中级 4：师级/助理 5：士级 6：待聘
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date participationDate; //	参加工作日期 	格式 'yyyy - MM - dd' 	否 	date 	2019/12/10
    private String clinicalYear; //	临床工作年限 	 	否 	varchar(2)
    private String introduction; //	简介 	 	是 	varchar(500)
    private String organzaionName; //	所属机构 	可同HOS_NAME一致 	是 	varchar(50)
    private String orgMark; //	机构标识 	可同ORG_CODE一致 	是 	varchar(22)
    private String organzationName; //	执业机构 	如有多个执业机构填写多个，“，”分隔 	是 	varchar(60)
    private String place; //	执业地点 	如有多点执业，需填写多个执业地点，“，”分隔 	是 	varchar(60)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDate; //	发证日期 	格式 'yyyy - MM - dd ' 	否 	timestamp 	2019/12/10
    private String practiceScope; //	执业范围 	 	否 	varchar(50)
    private String idPhoto; //	证件照片 	 	否 	varchar(max)
    private String qualificationCategory; //	资质类别 	如有多个需在医务人员资质上报表补充多条。 	否 	varchar(1) 	1：药学类 2：中药学类 3：药学与中药学类 4：其他
    private String qualificationNo; //	资质证书编号 	如有多个需在医务人员资质上报表补充多条。 	是 	varchar(3000)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date qualificationDate; //	资质获取时间 	格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    private String qualificationPhoto; //	资质证件照片 	 	否 	varchar(max)
    private String recipeRight; //	处方权 	 	否 	varchar(20)
    private String certificateNo; //	证书编号 	 	否 	varchar(50)
    private String openState; //	开通状态 	是否开通业务 	否 	varchar(1) 	0：未开通  1：已开通
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date openTime; //	开通时间 	格式 'yyyy - MM - dd HH24:mi:ss' 	否 	timestamp 	2019/12/10 16:34
    private String medicareState; //	药师参加医疗保险状态 	 	否 	varchar(1) 	0：未开通  1：已开通
    private String medicareCode; //	医疗保险码 	 	否 	varchar(22)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date medicareTimeStart; //	药师参加医疗保险开始时间 	只填写最近一次，格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date medicareTimeEnd; //	药师参加医疗保险结束时间 	只填写最近一次，格式 'yyyy - MM - dd' 	否 	timestamp 	2019/12/10

}
