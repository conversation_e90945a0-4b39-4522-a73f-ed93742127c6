package cn.taihealth.ih.supervise.service;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.OrderOperation;
import cn.taihealth.ih.domain.hospital.PrescriptionOrderCa;
import cn.taihealth.ih.supervise.dto.ln.ac.ChargeAndRefundReq;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.dto.ln.ac.MedicineDeliver;
import cn.taihealth.ih.supervise.dto.sh.ac.ResponseData;
import cn.taihealth.ih.supervise.dto.sh.ac.SelectResponse;
import org.springframework.scheduling.annotation.Async;

/**
 * 监管上报接口，不同的监管平台，有不同的实现
 */
public interface SuperviseService {

    // 机构备案 已对接
    void organizationBa(Hospital hospital, SuperviseDto dto);
    // 年检信息上报
    void annualInspectionInfoBa(Hospital hospital, SuperviseDto dto);

    /**
     * 科室备案
     * 科目备案
     * @param hospital
     * @param dto
     */
    void departmentBa(Hospital hospital, SuperviseDto dto);

    // 医生备案 已对接
    void doctorBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto);

    /**
     * 药师备案
     * @param hospital
     * @param medicalWorker
     * @param dto
     */
    void pharmacistBa(Hospital hospital, MedicalWorker medicalWorker, SuperviseDto dto);

    // 挂号成功
    void register(Hospital hospital, Long orderId, SuperviseDto dto);

    // 退号成功
    void cancelRegistration(Long orderId, SuperviseDto dto);

    // 拒绝
    void rejectRegistration(Hospital hospital, Long orderId, SuperviseDto dto);

    // 就诊记录开具
    void createVisitRecord(Hospital hospital, Long orderId, SuperviseDto dto);

    // 咨询记录开具
    void createConsultationRecord(Long orderId, SuperviseDto dto);

    // 咨询-取消接诊
    void createCancelConsultationRecord(Long orderId, SuperviseDto dto, OrderOperation.Step step);

    // 处方开立（西药）
    void prescribeWest(Long prescriptionOrderId, SuperviseDto dto);

    // 处方变更（本次不用对接）
    void changePrescription();

    // 处方撤消  
    void cancelPrescription(Long prescriptionOrderId, SuperviseDto dto);

    /**
     * 处方审核
     * @param prescriptionOrderId
     * @param pass
     * @param dto
     * @param orderCa 通过时，可以传null，由方法内部自动查询，不通过时，必须传值
     */
    void auditPrescription(Hospital hospital, Long prescriptionOrderId, SuperviseDto dto, Boolean pass, PrescriptionOrderCa orderCa);

    // 处方流转（本次不用对接）
    void transferPrescription();

    // 药品配送（定时任务）已对接
    void deliverMedicine(Long drugOrderId, SuperviseDto dto);

    // 药品配送（定时任务）
    void deliverMedicine(MedicineDeliver medicineDeliver, SuperviseDto dto);

    // 收费/退费
    void chargeAndRefund(ChargeAndRefundReq req, SuperviseDto dto);

    // 服务评价 已对接
    void evaluateService(Long evaluationId, SuperviseDto dto);

    // 投诉举报 已对接
    void reportComplaint(Hospital hospital, Long ticketId, SuperviseDto dto);

    // 预约单上传
    void appointmentInfoUpload(Hospital hospital, Long orderId, SuperviseDto dto);

    void selectUploadData(Hospital hospital, int type, Integer page, Integer size, SuperviseDto dto);
}
