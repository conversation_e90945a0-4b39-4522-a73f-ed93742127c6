package cn.taihealth.ih.supervise.utils;

import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.domain.enums.TreatmentUnit;
import cn.taihealth.ih.domain.enums.VisitType;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DataUtils {

    public static String toTimeStr6(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat beFormat = new SimpleDateFormat("HHmmss");
        return beFormat.format(date);
    }

    public static String toDateStr8(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat beFormat = new SimpleDateFormat("yyyyMMdd");
        return beFormat.format(date);
    }

    public static String toDateStr15(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat beFormat = new SimpleDateFormat("yyyyMMdd HHmmss");
        return beFormat.format(date);
    }

    /**
     * 除法，保留2位小数
     */
    public static String divideToFloat2(int registrationFee, int i) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format((float) registrationFee / i);
    }


    /**
     * 除法，保留2位小数
     */
    public static Integer getDurationDays(int times, TreatmentUnit treatmentUnit) {
        switch (treatmentUnit) {
            case DAY:
                return times;
            case WEEK:
                return 7 * times;
            case MONTH:
                return 30 * times;
            default:
                return times;
        }
    }

    public static String rankToCode(OfflineHospital.Rank rank) {
        switch (rank) {
            case ONE:
                return "1";
            case TWO:
                return "2";
            case THREE:
                return "3";
            case UNKNOWN:
                return "9";
            default:
                return "9";
        }
    }

    public static String gradeToCode(OfflineHospital.Grade grade) {
        switch (grade) {
            case SPECIFIC:
                return "1";
            case JIA:
                return "2";
            case YI:
                return "3";
            case BING:
                return "4";
            case UNKNOWN:
                return "9";
            default:
                return "9";
        }
    }

    public static String genderToCode(Gender gender) {
        switch (gender) {
            case MALE:
                return "1";
            case FEMALE:
                return "2";
            case UNKNOWN:
                return "0";
            default:
                return "0";
        }
    }


    public static String extractBirthdayFromIDCard(String idCardNumber) {
        // 身份证号码的正则表达式
        String regex = "^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])\\d{3}[\\dxX]$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(idCardNumber);

        // 检查身份证号码格式是否正确
        if (!matcher.matches()) {
            throw new IllegalArgumentException("身份证号码格式不正确");
        }

        // 提取出生日期并格式化为字符串
        int year = Integer.parseInt(idCardNumber.substring(6, 10));
        int month = Integer.parseInt(idCardNumber.substring(10, 12));
        int day = Integer.parseInt(idCardNumber.substring(12, 14));
        return String.format("%04d%02d%02d", year, month, day);
    }

    public static Integer getAgeByIdCard(String idCard) {
        String birthYear = idCard.substring(6, 10); // 身份证号的出生年份
        String birthMonth = idCard.substring(10, 12); // 身份证号的出生月份
        String birthDay = idCard.substring(12, 14); // 身份证号的出生日

        // 创建一个表示出生日期的LocalDate对象
        LocalDate birthdate = LocalDate.of(Integer.parseInt(birthYear), Integer.parseInt(birthMonth), Integer.parseInt(birthDay));

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算年龄
        Period period = Period.between(birthdate, currentDate);
        int years = period.getYears();

        return years;
    }

    public static String toDateStr15(LocalDateTime createdDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd HHmmss");
        return createdDate.format(formatter);
    }

    public static String visitTypeToCode(VisitType visitType) {
        switch (visitType) {
            case VIDEO:
                return "3";
            case GRAPHIC:
                return "1";
            case PHONE:
                return "2";
            default:
                return "9";
        }
    }


}
