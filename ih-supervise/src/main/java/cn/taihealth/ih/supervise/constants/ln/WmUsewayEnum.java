package cn.taihealth.ih.supervise.constants.ln;

public enum WmUsewayEnum {

    U_1("1", "口服"),
    U_2("2", "直肠用药"),
    U_3("3", "舌下用药"),
    U_4("4", "注射用药"),
    U_401("401", "皮下注射"),
    U_402("402", "皮内注射"),
    U_403("403", "肌肉注射"),
    U_404("404", "静脉注射或静脉滴注"),
    U_5("5", "吸入用药"),
    U_6("6", "局部用药"),
    U_601("601", "椎管内用药"),
    U_602("602", "关节腔内用药"),
    U_603("603", "胸腔膜用药"),
    U_604("604", "腹腔用药"),
    U_605("605", "阴道用药"),
    U_606("606", "气管内用药"),
    U_607("607", "滴眼"),
    U_608("608", "滴鼻"),
    U_609("609", "喷喉"),
    U_610("610", "含化"),
    U_611("611", "敷伤口"),
    U_612("612", "擦皮肤"),
    U_699("699", "其他局部用药途径"),
    U_9("9", "其他用药途径");

    /**
     * 编码
     */
    public String code;

    /**
     * 描述
     */
    public String desc;


    WmUsewayEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据描述获取编码
     *
     * @param desc 编码
     * @return
     */
    public static String getCodeByDesc(String desc) {
        //判空
        if (desc == null) {
            return null;
        }
        //循环处理
        WmUsewayEnum[] values = WmUsewayEnum.values();
        for (WmUsewayEnum value : values) {
            if (value.desc.equals(desc)) {
                return value.code;
            }
        }
        return desc;
    }
}
