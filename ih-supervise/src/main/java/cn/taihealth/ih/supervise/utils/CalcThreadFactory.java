package cn.taihealth.ih.supervise.utils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR>
 */
public class CalcThreadFactory implements ThreadFactory {

    private int counter;
    private String name;
    private List<String> stats;
    private Thread.UncaughtExceptionHandler exceptionHandler;

    public CalcThreadFactory(String name, Thread.UncaughtExceptionHandler exceptionHandler) {
        counter = 1;
        this.name = name;
        stats = new ArrayList<String>();
        this.exceptionHandler = exceptionHandler;
    }


    /**
     * Constructs a new {@code Thread}.  Implementations may also initialize
     * priority, name, daemon status, {@code ThreadGroup}, etc.
     *
     * @param r a runnable to be executed by new thread instance
     * @return constructed thread, or {@code null} if the request to
     * create a thread is rejected
     */
    @Override
    public Thread newThread(Runnable r) {
        Thread t = new Thread(r, name + "-work-" + counter);
        t.setUncaughtExceptionHandler(exceptionHandler);
        counter++;
        return t;
    }

    public String getStats() {
        StringBuffer buffer = new StringBuffer();
        Iterator<String> it = stats.iterator();
        while (it.hasNext()) {
            buffer.append(it.next());
        }
        return buffer.toString();
    }
}
