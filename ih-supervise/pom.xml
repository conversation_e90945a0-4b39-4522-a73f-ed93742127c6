<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.taihealth.ih</groupId>
        <artifactId>ih-parent</artifactId>
        <version>2.0.4878</version>
    </parent>

    <artifactId>ih-supervise</artifactId>

    <dependencies>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <!-- 引用SDK Jar包，根据实际路径配置 systemPath -->
        <dependency>
            <groupId>com.xikang</groupId>
            <artifactId>supervise-sdk-client</artifactId>
            <version>0.1.8</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.xikang</groupId>-->
        <!--            <artifactId>supervise-sdk-client</artifactId>-->
        <!--            <version>0.1.7</version>-->
        <!--            <scope>system</scope>-->
        <!--            <systemPath>${project.basedir}/lib/supervise-sdk-lite-0.1.8.jar-->
        <!--            </systemPath>-->
        <!--        </dependency>-->

        <!-- hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.20</version>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-repo</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.gitq.jedi</groupId>
            <artifactId>jedi-context</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gitq.jedi</groupId>
            <artifactId>jedi-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-wechat-service</artifactId>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>