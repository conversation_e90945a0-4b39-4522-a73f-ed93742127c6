package cn.taihealth.ih.spring.security.jwt;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

/**
 */
public class JWTFaceToken extends JWTToken {

    @ApiModelProperty("验证码是否正确")
    @JsonIgnore
    private boolean tokenIsTrue;
    @ApiModelProperty("是否成功登录")
    private boolean loginSuccess;
    @ApiModelProperty("需要重定向的地址")
    private String redirectUrl;
    @ApiModelProperty("后续操作的唯一标识")
    private String uuid;

    public JWTFaceToken() {
    }



    public JWTFaceToken(boolean tokenIsTrue) {
        this.tokenIsTrue = tokenIsTrue;
    }

    public JWTFaceToken(boolean tokenIsTrue, boolean loginSuccess) {
        this.tokenIsTrue = tokenIsTrue;
        this.loginSuccess = loginSuccess;
    }

    public JWTFaceToken(String idToken, String refreshToken, int expiresIn) {
        super(idToken, refreshToken, expiresIn);
        this.loginSuccess = true;
        this.tokenIsTrue = true;
    }

    public JWTFaceToken(String redirectUrl, String uuid) {
        this.redirectUrl = redirectUrl;
        this.uuid = uuid;
        this.loginSuccess = false;
        this.tokenIsTrue = true;
    }

    public boolean isTokenIsTrue() {
        return tokenIsTrue;
    }

    public void setTokenIsTrue(boolean tokenIsTrue) {
        this.tokenIsTrue = tokenIsTrue;
    }

    public boolean isLoginSuccess() {
        return loginSuccess;
    }

    public void setLoginSuccess(boolean loginSuccess) {
        this.loginSuccess = loginSuccess;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @JsonIgnore
    public void setToken(JWTToken token) {
        this.setExpiresIn(token.getExpiresIn());
        this.setIdToken(token.getIdToken());
        this.setRefreshToken(token.getRefreshToken());
    }
}
