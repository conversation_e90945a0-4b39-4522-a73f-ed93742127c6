package cn.taihealth.ih.spring.security.database;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.spring.security.UserDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;
import java.util.Optional;

/**
 * Authenticate a user from the database.
 */
@Component
public class DomainUserDetailsService implements UserDetailsService {
    private final Logger log = LoggerFactory.getLogger(DomainUserDetailsService.class);

    private final UserCacheFindService userCacheFindService;
    @Autowired
    private HttpServletRequest request;

    public DomainUserDetailsService(UserCacheFindService userCacheFindService) {
        this.userCacheFindService = userCacheFindService;
    }

    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(final String username) {
        log.debug("Authenticating {}", username);
//        request.getSession().setAttribute("name", username);
        String lowercaseLogin = username.toLowerCase(Locale.ENGLISH);
        Optional<User> userFromDatabase = userCacheFindService.findOneByUsername(lowercaseLogin);
        UserDetail user = userFromDatabase.map(UserDetail::new)
            .orElseThrow(() -> new UsernameNotFoundException("用户名: " + username + "不存在"));
//        String hospitalCode = request.getHeader("IH_HOSPITAL");
        String cloudType = request.getHeader("ITEM");
        if ("PC".equalsIgnoreCase(cloudType)) {
            user.setCloudType(1);
        } else {
            user.setCloudType(0);
        }
        log.info(user.getPassword());
        return user;
    }
}
