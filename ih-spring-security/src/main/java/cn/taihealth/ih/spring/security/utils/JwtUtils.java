package cn.taihealth.ih.spring.security.utils;

import cn.taihealth.ih.spring.security.jwt.JWTConfigurer;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

public class JwtUtils {

    /**
     * 获取token
     * @param request
     * @return
     */
    public static String resolveToken(HttpServletRequest request) {
        String bearerToken = request.getHeader(JWTConfigurer.AUTHORIZATION_HEADER);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }

        bearerToken = request.getParameter("idToken");
        return bearerToken;
    }

}
