package cn.taihealth.ih.spring.security;

import java.io.Serializable;

/**
 */
public class UserRoleCodeName implements Serializable {

    private static final long serialVersionUID = 1L;

    private String code;

    private String name;

    private boolean admin;

    private boolean headNurse;

    private boolean nurse;

    private boolean doctor;
    private boolean doctorAssistant;

    private boolean pharmacist;

    private boolean out;

    private boolean emergency;

    private String hospitalCode;

    public UserRoleCodeName() {
    }

    public UserRoleCodeName(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public UserRoleCodeName(
        String code, String name, boolean headNurse, boolean nurse,
        boolean doctor, boolean doctorAssistant, boolean pharmacist, boolean out, boolean emergency, boolean admin,
        String hospitalCode) {
        this.code = code;
        this.name = name;
        this.headNurse = headNurse;
        this.nurse = nurse;
        this.doctor = doctor;
        this.doctorAssistant = doctorAssistant;
        this.pharmacist = pharmacist;
        this.out = out;
        this.emergency = emergency;
        this.admin = admin;
        this.hospitalCode = hospitalCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String toRole() {
        return "ROLE_" + code;
    }

    public boolean isHeadNurse() {
        return headNurse;
    }

    public void setHeadNurse(boolean headNurse) {
        this.headNurse = headNurse;
    }

    public boolean isNurse() {
        return nurse;
    }

    public void setNurse(boolean nurse) {
        this.nurse = nurse;
    }

    public boolean isDoctor() {
        return doctor;
    }

    public void setDoctor(boolean doctor) {
        this.doctor = doctor;
    }

    public boolean isPharmacist() {
        return pharmacist;
    }

    public void setPharmacist(boolean pharmacist) {
        this.pharmacist = pharmacist;
    }

    public boolean isAdmin() {
        return admin;
    }

    public void setAdmin(boolean admin) {
        this.admin = admin;
    }

    public boolean isOut() {
        return out;
    }

    public void setOut(boolean out) {
        this.out = out;
    }

    public boolean isEmergency() {
        return emergency;
    }

    public void setEmergency(boolean emergency) {
        this.emergency = emergency;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public boolean isDoctorAssistant() {
        return doctorAssistant;
    }

    public void setDoctorAssistant(boolean doctorAssistant) {
        this.doctorAssistant = doctorAssistant;
    }
}
