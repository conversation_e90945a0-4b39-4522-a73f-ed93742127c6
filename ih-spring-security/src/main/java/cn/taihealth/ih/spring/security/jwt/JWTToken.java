package cn.taihealth.ih.spring.security.jwt;

import java.io.Serializable;

/**
 */
public class JWTToken implements Serializable {

    private static final long serialVersionUID = 1L;

    private String idToken;

    private String refreshToken;

    private int expiresIn;

    private Long userPlatform;

    public JWTToken() {
    }

    public JWTToken(String idToken, String refreshToken, int expiresIn) {
        this.idToken = idToken;
        this.refreshToken = refreshToken;
        this.expiresIn = expiresIn;
    }

    public void setIdToken(String idToken) {
        this.idToken = idToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public void setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getIdToken() {
        return idToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public int getExpiresIn() {
        return expiresIn;
    }

    public Long getUserPlatform() {
        return userPlatform;
    }

    public void setUserPlatform(Long userPlatform) {
        this.userPlatform = userPlatform;
    }
}
