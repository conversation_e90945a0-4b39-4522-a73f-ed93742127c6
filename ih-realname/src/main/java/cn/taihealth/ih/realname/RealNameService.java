package cn.taihealth.ih.realname;

import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.common.web.UrlUtils;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 */
@Service
public class RealNameService {

    private static final String HOST = "https://restapi.realnameonline.cn:24200/smz-resapi/restservice/";

    private static final String SOURCE_ID = "210607";
    private static final String USER = "wfjWSH";
    private static final String PASSWORD = "hKtd#QkBm";
    private static final String SECRET_KEY = "uyGbijE";
    private static final String PRIVATE_KEY = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCj0Ywsilv1QbpWOyzN6Gr1GwqaKQnEU1KBLfK3+gUCQe/+7WM2pWjUIMg5wkKZ0caLCacqvzckdQkj043ydm0bCusRKCFvFZKaHbFPto/Udmp1YcYPTcDv4CCQH5J58M63dIe5l4ZBVqgjbKdv+BhjPPXcYKcr8qyk7uCTMToevNuCCRbJ8Rn/3H1WDo5MjXtkutGKExzmdBfrG6Q/jlapBXg/Vn8416ZBQUk7bC6C6U8NHMC9BwjmxNJcBhLZ4zvIz71nLB3I5OzLts2Zp2slkOkbTPYrsFc9lNmqyNfYGlcND8n37k1eV0v5nwN2BEUfkE1k0IBMzM0KygcNCaoNAgMBAAECggEADYsDVaNSmuIGL57Qw20VepHzvG7sGcjxpzBPQppu05Lz7lVdwGTIV9El/HBs5R6MlW4E3enU+zSZvtZ8g7D6sCOk71Qr/2z/I7KUuAxVs7DeeDnPD/DTpIEY8BFMILyhulTt64/1T22ElPkkloMiMQIXKVLsPxve3fvJoSGOtn+whEHbiiqIUzGhL0bPYF2VWD4WxT7LMnIrJBhKu+4zCv52GmUgjQDmXE6KH/YBeZKGDv/tYUbpzQhRvvP+Z2Grds3IopP644/q0WjoHMLjji6XvNGOiaf0KNk8h7Hhz1OA5LFNqaGXWSvMQl2sgd1B2vhVP/K8ENb/O1UE9V7/2QKBgQDeZLAKUfHdXVqF01I8KWTU1HORdC2OdljZxufhtV3pDlKk+lNhVeb8MEl5K3Hn7mnwbozPnQfLLDhZ3OhMYHSXtu8mMwjw/bbcsUjrVdshqkI91c6U/DL/9gB6028REtz1MVZBcRanGKxqdoT+TLcM3IhYH21XlHUwUGkylN0SJwKBgQC8kuNPwGFmIhWm9DrrdHpP6l0ILFBR/nP/YY8yUpREuVcDnO7xj1VuYUAVr2SFKKxec/GK0bzBv1zOVjXPAkOyseaqRokqW7n/yVyEucfs0j15TxJNYLNnToGwWb2vp8nMceAqvR7bJ/C+QYWdO71/sWXhaa0OqIKzZHdOo6VmqwKBgQDZDHfrepKNs8DXf6lw4g1w0nCAc1zK/6Iz2bcSSIqBHON39IBQLSCk3RO4WLhw3fUzHRH8xouGtX/B5KIreLASKg45v/FoEpbXyYcbd83Ii3zfEL7wFTxJJ/TL63MovLo964r9j7vGFYSg209tSb4VpV7EcteqdE/zeWdSYItqXQKBgQCl9G9wJFKTW7PKdm3UAXITGqI9KyKUxma3fdhT5TDvOl1FpLr0g7wfpB6N9wlz1tO7XDZagTKpIAxA2sYIJGoHvWDOwby9NnR+k2uEsysrU+sd82ScDFpWtFSWKqQxDm0eK6mocz/Qh1nrViaMtaKT2j9bHXFb0RkVX3lQD9zkeQKBgQCdWkSXaQ0/oK+oqIFPPLy+jbyT+rI2YcSSTGnBJo9SqqiJIrOrexO1nBCO8JU7HLMRECZUW2pJjCAIVc4vR7C5aFysWCOpBq02Fw3RfdjtNIdUo9Iww4ODRcIlOIvUEgaLB8SMhQfMfO5DkuAGpk7zf4hVOjv+Iw/sLN4dPOMApg==";

    public ResOutObject identify(File file, boolean isFace) {
        return identify(readImage(file), isFace);
    }

    public ResOutObject identify(String imageData, boolean isFace) {
        String strTransId = getTransactionId();

        ResInObject in = new ResInObject(); // 业务参数
        Map<String, String> params = new HashMap<>();

        params.put("userId", USER);
        params.put("userPwd", PASSWORD);
        params.put("picType", isFace ? "Z" : "F");
        String sn = EncryptUtil.getSn();
        params.put("sn", sn);
        String reqJson = StandardObjectMapper.stringify(params);
        reqJson = new RealNameMsDesPlus(SECRET_KEY).encrypt(reqJson);// TLSPL 分配报文加密密钥
        in.setParamsJson(reqJson);
        in.setBusiCode("custPicIdentify");
        in.setRequestSource(SOURCE_ID);
        in.setTransactionID(strTransId);//流水号
        in.getImages().put("picPathName", imageData);

        String signature = EncryptUtil.getSignature(PRIVATE_KEY, SOURCE_ID, strTransId, USER, PASSWORD, sn);
        in.setSignature(signature);// 验签数据
        HttpsUtil util = new HttpsUtil();
        String json = util.request(UrlUtils.concatSegments(HOST, "/custPicIdentify"), in);
        return StandardObjectMapper.readValue(json, new TypeReference<ResOutObject>() {});
    }

    public ResOutObject verify(File front, File back, String custName, String custCertNo) {
        String strTransId = getTransactionId();

        ResInObject in = new ResInObject(); // 业务参数
        Map<String, String> params = new HashMap<>();

        params.put("userId", USER);
        params.put("userPwd", PASSWORD);
        params.put("custName", custName);
        params.put("custCertNo", custCertNo);
        String sn = EncryptUtil.getSn();
        params.put("sn", sn);
        String reqJson = StandardObjectMapper.stringify(params);
        reqJson = new RealNameMsDesPlus(SECRET_KEY).encrypt(reqJson);// TLSPL 分配报文加密密钥
        in.setParamsJson(reqJson);
        in.setBusiCode("custInfoPicVerify");
        in.setRequestSource(SOURCE_ID);
        in.setTransactionID(strTransId);//流水号
        in.setImages(getImages(front, back));

        String signature = EncryptUtil.getSignature(PRIVATE_KEY, SOURCE_ID, strTransId, USER, PASSWORD, sn);
        in.setSignature(signature);// 验签数据
        HttpsUtil util = new HttpsUtil();
        String json = util.request(UrlUtils.concatSegments(HOST, "/custInfoPicVerify"), in);
        return StandardObjectMapper.readValue(json, new TypeReference<ResOutObject>() {});
    }

    private Map<String, String> getImages(File front, File back) {
        return ImmutableMap.<String, String>builder()
                .put("picNameZ", readImage(front))
                .put("picNameF", readImage(back))
                .build();
    }

    private String readImage(File file) {
        try (FileInputStream in = new FileInputStream(file)) {
            return IOUtils.toString(in, Charsets.ISO_8859_1);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private String getTransactionId() {
        return String.format("%s%s%s",
                SOURCE_ID,
                DataTypes.DATE.asString(new Date(), "yyyyMMddHHmmss"),
                RandomStringUtils.randomNumeric(6)
        );
    }

    public static class WhitelistHostnameVerifier implements HostnameVerifier {
        private static final HostnameVerifier defaultHostnameVerifier = HttpsURLConnection.getDefaultHostnameVerifier();
        private Set<String> trustedHosts;

        public WhitelistHostnameVerifier(Set<String> trustedHosts) {
            this.trustedHosts = trustedHosts;
        }

        @Override
        public boolean verify(String hostname, SSLSession session) {
            if (trustedHosts.contains(hostname)) {
                return true;
            } else {
                return defaultHostnameVerifier.verify(hostname, session);
            }
        }
    }
}
