package cn.taihealth.ih.realname;

import cn.taihealth.ih.commons.util.Rsa;
import org.apache.commons.lang3.RandomStringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;


public class EncryptUtil {
    private static final String DEFAULT_PATTERN = "yyyyMMddHHmmssSSS";

    /**
     * @param pirKey        私钥
     *
     * @return
     */
    public static String getSignature(String pirKey, String requestSource, String transactionID, String userId, String userPwd, String sn) {
        String source = requestSource + transactionID + userId + userPwd + sn;
        return new Rsa.Encoder(pir<PERSON><PERSON>, 256).encode(source);
    }

    /**
     * @param key
     * @param json
     * @return
     * @throws Exception
     */
    public static String getEncryptParams(String key, String json) throws Exception {
        return new RealNameMsDesPlus(key).encrypt(json);
    }

    public static String getSn() {
        String date = new SimpleDateFormat(DEFAULT_PATTERN).format(new Date());
        String random = generateRandomCode();// 生成6位随机数
        return date + random;
    }

    /**
     * 生成6位随机数
     */
    public static String generateRandomCode() {
        return RandomStringUtils.randomNumeric(6);
//        return String.valueOf(new Random().nextInt(899999) + 100000);
    }
}

