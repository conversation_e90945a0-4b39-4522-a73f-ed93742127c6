package cn.taihealth.ih.wechat.service.vm.wechat;

import java.io.Serializable;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;

public class IhWxMpOAuth2AccessToken implements Serializable {

    private String accessToken;
    private int expiresIn = -1;
    private String refreshToken;
    private String openId;
    private String scope;
    private String unionId;

    public IhWxMpOAuth2AccessToken() {
    }

    public IhWxMpOAuth2AccessToken(WxOAuth2AccessToken auth2AccessToken) {
        this.accessToken = auth2AccessToken.getAccessToken();
        this.expiresIn = auth2AccessToken.getExpiresIn();
        this.openId = auth2AccessToken.getOpenId();
        this.refreshToken = auth2AccessToken.getRefreshToken();
        this.scope = auth2AccessToken.getScope();
        this.unionId = auth2AccessToken.getUnionId();
    }

    public WxOAuth2AccessToken toToken() {
        WxOAuth2AccessToken token = new WxOAuth2AccessToken();
        token.setAccessToken(this.accessToken);
        token.setExpiresIn(this.expiresIn);
        token.setOpenId(this.openId);
        token.setRefreshToken(this.refreshToken);
        token.setScope(this.scope);
        token.setUnionId(this.unionId);
        return token;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public int getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }
}
