package cn.taihealth.ih.wechat.service.vm.wechat;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.w3c.dom.Document;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@XStreamAlias("xml")
public class WxInsurancePayRefundQueryResult extends BaseWxInstrancePayResult implements Serializable {

    /**
     * <pre>
     *     微信支付生成的医疗订单号
     * </pre>
     */
    @XStreamAlias("med_trans_id")
    private String medTransId;
    /**
     * <pre>
     *     微信生成的退款医疗单号
     * </pre>
     */
    @XStreamAlias("med_refund_id")
    private String medRefundId;
    /**
     * <pre>
     *     第三方服务商订单号
     * </pre>
     */
    @XStreamAlias("hosp_out_trade_no")
    private String hospOutTradeNo;
    /**
     * <pre>
     *     医院退款订单号
     * </pre>
     */
    @XStreamAlias("hosp_out_refund_no")
    private String hospOutRefundNo;
    /**
     * <pre>
     *     医保退款金额（目前只能全额退）,以分为单位
     * </pre>
     */
    @XStreamAlias("insurance_refund_fee")
    private Integer insuranceRefundFee;
    /**
     * <pre>
     *     现金退款金额,以分为单位
     * </pre>
     */
    @XStreamAlias("cash_refund_fee")
    private Integer cashRefundFee;
    /**
     * <pre>
     *     现金退款状态
     *     SUCCESS:退款成功
     *     REFUNDING:退款处理中
     *     CHANGE:转入代发，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，资金回流到商户的现金帐号，需要商户人工干预，通过线下或者财付通转账的方式进行退款。
     * </pre>
     */
    @XStreamAlias("cash_refund_stauts")
    private String cashRefundStauts;
    /**
     * <pre>
     *     医保退款状态
     *     SUCCESS:退款成功
     *     FAIL:退款失败
     *     REFUNDING:退款处理中
     * </pre>
     */
    @XStreamAlias("insurance_refund_status")
    private String insuranceRefundStatus;
    /**
     * <pre>
     *     退款单总状态
     *     SUCCESS:退款成功
     *     REFUNDING:退款处理中
     * </pre>
     */
    @XStreamAlias("med_refund_state")
    private String medRefundState;
    /**
     * <pre>
     *     退款成功时间
     * </pre>
     */
    @XStreamAlias("refund_end_time")
    private String refundEndTime;
    /**
     * <pre>
     *     退款完成后医保局返回内容串
     * </pre>
     */
    @XStreamAlias("response_content")
    private String responseContent;
    /**
     * <pre>
     *     微信医保支付生成的微信支付退款订单号
     *     混合支付或纯现金支付时返回
     * </pre>
     */
    @XStreamAlias("cash_refund_id")
    private String cashRefundId;
    /**
     * <pre>
     *     微信医保支付生成的医保退款单号
     *     混合支付或纯医保支付时返回
     * </pre>
     */
    @XStreamAlias("insurance_refund_id")
    private String insuranceRefundId;

    @Override
    protected void loadXml(Document d) {
        this.medTransId = readXmlString(d, "med_trans_id");
        this.medRefundId = readXmlString(d, "med_refund_id");
        this.hospOutTradeNo = readXmlString(d, "hosp_out_trade_no");
        this.hospOutRefundNo = readXmlString(d, "hosp_out_refund_no");
        this.insuranceRefundFee = readXmlInteger(d, "insurance_refund_fee");
        this.cashRefundFee = readXmlInteger(d, "cash_refund_fee");
        this.cashRefundStauts = readXmlString(d, "cash_refund_stauts");
        this.insuranceRefundStatus = readXmlString(d, "insurance_refund_status");
        this.medRefundState = readXmlString(d, "med_refund_state");
        this.refundEndTime = readXmlString(d, "refund_end_time");
        this.responseContent = readXmlString(d, "response_content");
        this.cashRefundId = readXmlString(d, "cash_refund_id");
        this.insuranceRefundId = readXmlString(d, "insurance_refund_id");
    }

}

