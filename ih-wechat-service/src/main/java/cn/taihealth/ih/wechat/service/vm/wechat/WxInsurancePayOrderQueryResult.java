package cn.taihealth.ih.wechat.service.vm.wechat;

import com.github.binarywang.wxpay.bean.result.BaseWxPayResult;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.w3c.dom.Document;

import java.io.Serializable;

/**
 * <a href="https://yb.qq.com/yibao-payment/doc?nodeId=83680041366614016">3.5支付结果通知接口</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@XStreamAlias("xml")
public class WxInsurancePayOrderQueryResult extends BaseWxInstrancePayResult implements Serializable {

    /**
     * 用户标识
     */
    @XStreamAlias("openid")
    private String openid;
    /**
     * 用户子标识
     */
    @XStreamAlias("sub_openid")
    private String subOpenid;
    /**
     * 微信支付生成的医疗订单号
     */
    @XStreamAlias("med_trans_id")
    private String medTransId;
    /**
     * 第三方服务商订单号, 第三方服务商平台自动生成的一个订单号
     */
    @XStreamAlias("hosp_out_trade_no")
    private String hospOutTradeNo;
    /**
     * 订单支付时间，格式为yyyyMMddHHmmss，如2009年12月25日9点10分10秒表示为20091225091010
     */
    @XStreamAlias("time_end")
    private String timeEnd;
    /**
     * 支付类型, 2:医保 3:现金+医保
     */
    @XStreamAlias("pay_type")
    private String payType;
    /**
     * 总共需要支付的现金金额, 以分为单位
     */
    @XStreamAlias("total_fee")
    private int totalFee;
    /**
     * 现金支付金额, 以分为单位
     */
    @XStreamAlias("cash_fee")
    private int cashFee;
    /**
     * 医保支付金额, 以分为单位
     */
    @XStreamAlias("insurance_fee")
    private int insuranceFee;
    /**
     * 医保个账部分. 以分为单位
     */
    @XStreamAlias("insurance_self_fee")
    private int insuranceSelfFee;
    /**
     * 医保统筹部分
     */
    @XStreamAlias("insurance_fund_fee")
    private int insuranceFundFee;
    /**
     * 医保其他部分
     */
    @XStreamAlias("insurance_other_fee")
    private int insuranceOtherFee;
    /**
     * 医保局支付成功后返回内容
     */
    @XStreamAlias("response_content")
    private String responseContent;
    /**
     * 医保单据号
     */
    @XStreamAlias("bill_no")
    private String billNo;
    /**
     * 医院HIS系统订单号
     */
    @XStreamAlias("serial_no")
    private String serialNo;
    /**
     * 医保子单号, 混合或纯医保支付必传
     */
    @XStreamAlias("insurance_order_id")
    private String insuranceOrderId;
    /**
     * 微信支付子单号, 可用于在微信支付商户平台查询对应的微信支付单，混合或纯现金支付必传
     */
    @XStreamAlias("cash_order_id")
    private String cashOrderId;
    /**
     * 附加数据, 附加数据，原样返回
     */
    @XStreamAlias("attach")
    private String attach;

    @Override
    protected void loadXml(Document d) {
        this.openid = readXmlString(d, "openid");
        this.subOpenid = readXmlString(d, "sub_openid");
        this.medTransId = readXmlString(d, "med_trans_id");
        this.hospOutTradeNo = readXmlString(d, "hosp_out_trade_no");
        this.timeEnd = readXmlString(d, "time_end");
        this.payType = readXmlString(d, "pay_type");
        this.totalFee = readXmlInteger(d, "total_fee");
        this.cashFee = readXmlInteger(d, "cash_fee");
        this.insuranceFee = readXmlInteger(d, "insurance_fee");
        this.insuranceSelfFee = readXmlInteger(d, "insurance_self_fee");
        this.insuranceFundFee = readXmlInteger(d, "insurance_fund_fee");
        this.insuranceOtherFee = readXmlInteger(d, "insurance_other_fee");
        this.responseContent = readXmlString(d, "response_content");
        this.billNo = readXmlString(d, "bill_no");
        this.serialNo = readXmlString(d, "serial_no");
        this.insuranceOrderId = readXmlString(d, "insurance_order_id");
        this.cashOrderId = readXmlString(d, "cash_order_id");
        this.attach = readXmlString(d, "attach");
    }

}
