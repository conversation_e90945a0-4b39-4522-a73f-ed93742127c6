package cn.taihealth.ih.wechat.service.cache;

import java.util.concurrent.TimeUnit;
import javax.annotation.Nullable;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

@Component
public class WechatMiniCache {

    private final RMapCache<String, String> userPhone;

    public WechatMiniCache(RedissonClient redisson) {
        this.userPhone = redisson.getMapCache(WechatMiniCache.class.getName());
    }

    public void putPhone(String appId, String code, String sessionKey, String phone) {
        userPhone.fastPutIfAbsent(appId + "." + code + "." + sessionKey, phone, 10, TimeUnit.MINUTES);
    }

    public @Nullable String getPhone(String appId, String code, String sessionKey) {
        return userPhone.get(appId + "." + code + "." + sessionKey);
    }

}
