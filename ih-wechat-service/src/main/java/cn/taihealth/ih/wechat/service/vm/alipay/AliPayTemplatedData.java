package cn.taihealth.ih.wechat.service.vm.alipay;

import com.beust.jcommander.internal.Maps;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class AliPayTemplatedData implements Serializable {

    private String appId;
    private List<String> userIds;
    private String templateId;
    private String url;
    private Map<String, AliPayTemplateKeyword> keywords = Maps.newHashMap();
    @JsonIgnore
    public void addKeywords(String value) {
        int index = keywords.keySet().size() + 1;
        if (StringUtils.isEmpty(value)) {
            keywords.put("keyword" + index, new AliPayTemplateKeyword("-"));
        } else {
            keywords.put("keyword" + index, new AliPayTemplateKeyword(value));
        }
    }
}
