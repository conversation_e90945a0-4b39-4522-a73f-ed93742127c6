package cn.taihealth.ih.wechat.service.vm.wechat;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.w3c.dom.Document;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@XStreamAlias("xml")
public class WxInsuranceBillDownloadResult extends BaseWxInstrancePayResult implements Serializable {

    /**
     * <pre>
     *     账单地址
     *     账单URL，可直接获取账单内容
     *     为了保证数据安全，账单所在的腾讯云CDN有鉴权机制，鉴权有效的URL方可从腾讯云CDN下载账单内容。目前接口返回的账单URL经过了预授权，授权有效时间5分钟，5分钟后授权失效需要重新调用billdownload接口生成URL，方可重新下载。
     * </pre>
     */
    @XStreamAlias("download_url")
    private String downloadUrl;

    /**
     * <pre>
     *     校验码值
     *     账单内容校验值，用于下载账单后接入方校验账单内容的完整性。推荐接入方做文件的完整性校验。
     * </pre>
     */
    @XStreamAlias("checksum_value")
    private String checksumValue;

    /**
     * <pre>
     *     校验算法
     *     目前仅支持CRC64 ECMA
     * </pre>
     */
    @XStreamAlias("checksum_type")
    private String checksumType;

    @Override
    protected void loadXml(Document d) {
        this.downloadUrl = readXmlString(d, "download_url");
        this.checksumValue = readXmlString(d, "checksum_value");
        this.checksumType = readXmlString(d, "checksum_type");
    }

}

