package cn.taihealth.ih.wechat.service.vm.wxapi;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class AliPayMiniAppConfigVM {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("小程序名称")
    @NotEmpty
    private String name;

    @ApiModelProperty("appId")
    @NotEmpty
    private String appId;

    @ApiModelProperty("应用私钥")
    @NotEmpty
    private String privateKey;

    @ApiModelProperty("应用公钥")
    @NotEmpty
    private String publicKey;

    @ApiModelProperty("支付宝公钥")
    @NotEmpty
    private String alipayPublicKey;

    @ApiModelProperty("支付宝网关地址")
    @NotEmpty
    private String serverUrl;

    @ApiModelProperty("接口内容加密方式的key")
    @NotEmpty
    private String decryptKey;

    @ApiModelProperty("支付宝的openId")
//    @NotEmpty
    private String openId;

}
