package cn.taihealth.ih.wechat.service.vm.wechat;

import java.io.Serializable;

public class IhWxPayRefundRequest implements Serializable {

    /**
     * 字段名：设备号.
     */
    private String deviceInfo;

    /**
     * 字段名：微信订单号.
     */
    private String transactionId;

    /**
     * 字段名：商户订单号.
     */
    private String outTradeNo;

    /**
     * 字段名：商户退款单号.
     */
    private String outRefundNo;

    /**
     * 字段名：订单金额.
     */
    private Integer totalFee;

    /**
     * 字段名：退款金额.
     */
    private Integer refundFee;

    /**
     * 字段名：货币种类.
     */
    private String refundFeeType = "CNY";

    /**
     * 字段名：操作员.
     */
    private String opUserId;

    /**
     * 字段名：退款资金来源.
     */
    private String refundAccount = "REFUND_SOURCE_RECHARGE_FUNDS";

    /**
     * 字段名：退款原因.
     */
    private String refundDesc;

    /**
     * 字段名：退款结果通知url.
     */
    private String notifyUrl;

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public Integer getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Integer totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(Integer refundFee) {
        this.refundFee = refundFee;
    }

    public String getRefundFeeType() {
        return refundFeeType;
    }

    public void setRefundFeeType(String refundFeeType) {
        this.refundFeeType = refundFeeType;
    }

    public String getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(String opUserId) {
        this.opUserId = opUserId;
    }

    public String getRefundAccount() {
        return refundAccount;
    }

    public void setRefundAccount(String refundAccount) {
        this.refundAccount = refundAccount;
    }

    public String getRefundDesc() {
        return refundDesc;
    }

    public void setRefundDesc(String refundDesc) {
        this.refundDesc = refundDesc;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
}
