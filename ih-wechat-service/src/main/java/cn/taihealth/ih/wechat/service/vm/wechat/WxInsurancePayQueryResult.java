package cn.taihealth.ih.wechat.service.vm.wechat;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.w3c.dom.Document;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@XStreamAlias("xml")
public class WxInsurancePayQueryResult extends BaseWxInstrancePayResult implements Serializable {

    /**
     * <pre>
     *     交易状态
     *     SUCCESS:支付成功
     *     REFUND:转入退款
     *     PAYING:支付中
     *     SYS_REFUNDED:支付失败
     *     SYS_REFUNDING:支付失败，异常处理中
     *     NOTPAY:未支付
     *     CLOSED:已关闭
     *     INITIAL:未绑卡
     * </pre>
     */
    @XStreamAlias("med_trade_state")
    private String medTradeState;
    /**
     * <pre>
     *     现金支付状态
     *     SUCCESS:支付成功
     *     REFUND:转入退款
     *     NOTPAY:未支付
     *     CLOSED:已关闭
     * </pre>
     */
    @XStreamAlias("cash_trade_status")
    private String cashTradeStatus;
    /**
     * <pre>
     *     医保支付状态
     *     PAYED:已支付
     *     REFUND:转入退款
     *     NOTPAY:未支付
     *     CLOSED:已关闭
     * </pre>
     */
    @XStreamAlias("insurance_trade_status")
    private String insuranceTradeStatus;
    /**
     * <pre>
     *     对当前查询订单状态的描述和下一步操作的指引
     * </pre>
     */
    @XStreamAlias("trade_status_desc")
    private String tradeStatusDesc;
    /**
     * <pre>
     *     订单支付时间，格式为yyyyMMddHHmmss， 20091225091010
     * </pre>
     */
    @XStreamAlias("time_end")
    private String timeEnd;
    /**
     * <pre>
     *     订单类型
     *     RegPay=挂号支付
     *     MedPay=药费支付
     *     DiagPay=诊间支付
     *     InHospPay=住院费支付
     *     PharmacyPay=药店支付
     *     InsurancePay=保险费支付
     *     IntRegPay=互联网医院挂号支付
     *     IntReDiagPay=互联网医院复诊支付
     *     IntPscPay=互联网医院处方支付
     *     CovidExamPay=新冠检测费用
     * </pre>
     */
    @XStreamAlias("order_type")
    private String orderType;
    /**
     * <pre>
     *     支付类型
     *     2:医保
     *     3:现金+医保
     * </pre>
     */
    @XStreamAlias("pay_type")
    private String payType;
    /**
     * <pre>
     *     用户标识
     * </pre>
     */
    @XStreamAlias("openid")
    private String openid;
    /**
     * <pre>
     *     用户子标识
     * </pre>
     */
    @XStreamAlias("sub_openid")
    private String subOpenid;
    /**
     * <pre>
     *     总共需要支付金额,单位为分>0
     * </pre>
     */
    @XStreamAlias("total_fee")
    private Integer totalFee;
    /**
     * <pre>
     *     现金需要支付的金额,单位为分>=0
     * </pre>
     */
    @XStreamAlias("cash_fee")
    private Integer cashFee;
    /**
     * <pre>
     *     是否允许预结算费用发生变化
     *     0：不允许
     *     1：允许
     * </pre>
     */
    @XStreamAlias("allow_fee_change")
    private String allowFeeChange;
    /**
     * <pre>
     *     用户端ip
     * </pre>
     */
    @XStreamAlias("spbill_create_ip")
    private String spbillCreateIp;
    /**
     * <pre>
     *     回调url
     * </pre>
     */
    @XStreamAlias("notify_url")
    private String notifyUrl;
    /**
     * <pre>
     *     医保支付金额,单位为分>=0
     * </pre>
     */
    @XStreamAlias("insurance_fee")
    private Integer insuranceFee;
    /**
     * <pre>
     *     医保个账部分,单位为分>=0
     * </pre>
     */
    @XStreamAlias("insurance_self_fee")
    private Integer insuranceSelfFee;
    /**
     * <pre>
     *     医保统筹部分
     * </pre>
     */
    @XStreamAlias("insurance_fund_fee")
    private Integer insuranceFundFee;
    /**
     * <pre>
     *     医保其他部分,相关字段为医保分账明细，部分地区会返回。
     * </pre>
     */
    @XStreamAlias("insurance_other_fee")
    private Integer insuranceOtherFee;
    /**
     * <pre>
     *     第三方服务商订单号,第三方服务商平台自动生成的一个订单号
     * </pre>
     */
    @XStreamAlias("hosp_out_trade_no")
    private String hospOutTradeNo;
    /**
     * <pre>
     *     医院HIS系统订单号
     * </pre>
     */
    @XStreamAlias("serial_no")
    private String serialNo;
    /**
     * <pre>
     *     医疗机构编码（医保局分配给机构）
     * </pre>
     */
    @XStreamAlias("org_no")
    private String orgNo;
    /**
     * <pre>
     *     医院下单时间,格式为yyyyMMddHHmmss 如20160501163102
     * </pre>
     */
    @XStreamAlias("gmt_out_create")
    private String gmtOutCreate;
    /**
     * <pre>
     *     参考医保结构体（医疗机构透传医保）
     *     1 挂号支付必传
     *     2 诊间支付分两种情况
     *       ①线上预结算模式必填
     *       ②线下预结算模式非必填
     * </pre>
     */
    @XStreamAlias("request_content")
    private String requestContent;
    /**
     * <pre>
     *     业务单据号
     *     1 挂号支付非必传
     *     2 诊间支付分两种情况
     *       ①线上预结算模式非必填
     *       ②线下预结算模式必填
     * </pre>
     */
    @XStreamAlias("bill_no")
    private String billNo;
    /**
     * <pre>
     *     no_credit--指定不能使用信用卡支付
     * </pre>
     */
    @XStreamAlias("limit_pay")
    private String limitPay;
    /**
     * <pre>
     *     医院名称
     * </pre>
     */
    @XStreamAlias("hospital_name")
    private String hospitalName;
    /**
     * <pre>
     *     支付成功之后回跳的页面
     * </pre>
     */
    @XStreamAlias("return_url")
    private String returnUrl;
    /**
     * <pre>
     *     支付完成后医保局返回内容串
     * </pre>
     */
    @XStreamAlias("response_content")
    private String responseContent;
    /**
     * <pre>
     *     商品描述
     * </pre>
     */
    @XStreamAlias("body")
    private String body;
    /**
     * <pre>
     *     扩展字段
     * </pre>
     */
    @XStreamAlias("extends")
    private String extend;
    /**
     * <pre>
     *     微信生成的医疗订单号
     * </pre>
     */
    @XStreamAlias("med_trans_id")
    private String medTransId;
    /**
     * <pre>
     *     医保子单号
     * </pre>
     */
    @XStreamAlias("insurance_order_id")
    private String insuranceOrderId;
    /**
     * <pre>
     *     微信支付子单号,可用于在微信支付商户平台查询对应的微信支付单，混合或纯现金支付必传
     * </pre>
     */
    @XStreamAlias("cash_order_id")
    private String cashOrderId;
    /**
     * <pre>
     *     附加数据,附加数据原样返回
     * </pre>
     */
    @XStreamAlias("attach")
    private String attach;

    @Override
    protected void loadXml(Document d) {
        this.medTradeState = readXmlString(d, "med_trade_state");
        this.cashTradeStatus = readXmlString(d, "cash_trade_status");
        this.insuranceTradeStatus = readXmlString(d, "insurance_trade_status");
        this.tradeStatusDesc = readXmlString(d, "trade_status_desc");
        this.timeEnd = readXmlString(d, "time_end");
        this.orderType = readXmlString(d, "order_type");
        this.payType = readXmlString(d, "pay_type");
        this.openid = readXmlString(d, "openid");
        this.subOpenid = readXmlString(d, "sub_openid");
        this.totalFee = readXmlInteger(d, "total_fee");
        this.cashFee = readXmlInteger(d, "cash_fee");
        this.allowFeeChange = readXmlString(d, "allow_fee_change");
        this.spbillCreateIp = readXmlString(d, "spbill_create_ip");
        this.notifyUrl = readXmlString(d, "notify_url");
        this.insuranceFee = readXmlInteger(d, "insurance_fee");
        this.insuranceSelfFee = readXmlInteger(d, "insurance_self_fee");
        this.insuranceFundFee = readXmlInteger(d, "insurance_fund_fee");
        this.insuranceOtherFee = readXmlInteger(d, "insurance_other_fee");
        this.hospOutTradeNo = readXmlString(d, "hosp_out_trade_no");
        this.serialNo = readXmlString(d, "serial_no");
        this.orgNo = readXmlString(d, "org_no");
        this.gmtOutCreate = readXmlString(d, "gmt_out_create");
        this.requestContent = readXmlString(d, "request_content");
        this.billNo = readXmlString(d, "bill_no");
        this.limitPay = readXmlString(d, "limit_pay");
        this.hospitalName = readXmlString(d, "hospital_name");
        this.returnUrl = readXmlString(d, "return_url");
        this.responseContent = readXmlString(d, "response_content");
        this.body = readXmlString(d, "body");
        this.extend = readXmlString(d, "extends");
        this.medTransId = readXmlString(d, "med_trans_id");
        this.insuranceOrderId = readXmlString(d, "insurance_order_id");
        this.cashOrderId = readXmlString(d, "cash_order_id");
        this.attach = readXmlString(d, "attach");
    }

}

