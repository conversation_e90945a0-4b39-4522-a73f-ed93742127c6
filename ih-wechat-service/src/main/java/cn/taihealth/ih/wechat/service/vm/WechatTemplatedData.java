package cn.taihealth.ih.wechat.service.vm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import java.io.Serializable;
import java.util.List;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage.MiniProgram;

public class WechatTemplatedData implements Serializable {

    private List<OpenIdApp> openIdApps;
    private List<String> openIds;
    private KeywordColor first;
    private KeywordColor remark;
    private List<KeywordColor> keywords = Lists.newArrayList();
    private String templateId;
    private String url;

    private String appId;

    private MiniProgram miniProgram;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public WechatTemplatedData() {
    }

    public List<OpenIdApp> getOpenIdApps() {
        return openIdApps;
    }

    public void setOpenIdApps(List<OpenIdApp> openIdApps) {
        this.openIdApps = openIdApps;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public KeywordColor getFirst() {
        return first;
    }

    public void setFirst(KeywordColor first) {
        this.first = first;
    }

    public KeywordColor getRemark() {
        return remark;
    }

    public void setRemark(KeywordColor remark) {
        this.remark = remark;
    }

    public List<KeywordColor> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<KeywordColor> keywords) {
        this.keywords = keywords;
    }

    @JsonIgnore
    public void setFirstStr(String value) {
        first = new KeywordColor(value);
    }

    @JsonIgnore
    public void setFirstStrColor(String value, String color) {
        first = new KeywordColor(value, color);
    }

    @JsonIgnore
    public void setRemarkStr(String value) {
        remark = new KeywordColor(value);
    }

    @JsonIgnore
    public void setRemarkStrColor(String value, String color) {
        remark = new KeywordColor(value, color);
    }

    @JsonIgnore
    public void addKeywordsStr(String value) {
        keywords.add(new KeywordColor(value));
    }

    @JsonIgnore
    public void addKeywordsStrColor(String value, String color) {
        keywords.add(new KeywordColor(value, color));
    }

    public List<String> getOpenIds() {
        return openIds;
    }

    public void setOpenIds(List<String> openIds) {
        this.openIds = openIds;
    }

    public MiniProgram getMiniProgram() {
        return miniProgram;
    }

    public void setMiniProgram(MiniProgram miniProgram) {
        this.miniProgram = miniProgram;
    }
}
