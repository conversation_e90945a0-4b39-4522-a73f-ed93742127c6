package cn.taihealth.ih.wechat.service.vm;


import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.enums.ThirdOrderType;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;

public class WechatOrderInVM implements Serializable {

    @ApiModelProperty("类型(REGISTER: 挂号)")
    @NotNull(message = "类型不能为空")
    private ThirdOrderType type = ThirdOrderType.REGISTER;

    @ApiModelProperty("商品描述")
    @NotNull
    private String body = "";

    @ApiModelProperty("商品详情")
    @NotNull
    private String detail = "";

    @ApiModelProperty("设备号")
    @NotNull
    private String deviceInfo = "";

    @ApiModelProperty("标价金额")
    @NotNull
    private int totalFee = 0;

    @ApiModelProperty("交易类型")
    private WechatOrder.TradeType tradeType = WechatOrder.TradeType.JSAPI;

    @ApiModelProperty("商品ID")
    private String productId = "";

    @ApiModelProperty("业务订单id")
    private Long orderId;

    public ThirdOrderType getType() {
        return type;
    }

    public void setType(ThirdOrderType type) {
        this.type = type;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public int getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(int totalFee) {
        this.totalFee = totalFee;
    }

    public WechatOrder.TradeType getTradeType() {
        return tradeType;
    }

    public void setTradeType(WechatOrder.TradeType tradeType) {
        this.tradeType = tradeType;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
}
