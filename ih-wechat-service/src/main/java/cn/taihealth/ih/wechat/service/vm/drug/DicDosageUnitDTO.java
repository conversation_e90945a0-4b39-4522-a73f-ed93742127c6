package cn.taihealth.ih.wechat.service.vm.drug;

import cn.taihealth.ih.domain.hospital.meddic.DicDosageUnit;
import cn.taihealth.ih.wechat.service.vm.AbstractEntityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

/**
 * <p>
 * 剂量单位
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-09
 */
@Data
@NoArgsConstructor
public class DicDosageUnitDTO extends AbstractEntityDTO {

    /**
     * 计量单位名字(英文)
     */
    @ApiModelProperty("计量单位名字(英文)")
    @Size(max = 100)
    private String unitName;

    private String hospitalCode;

    public DicDosageUnitDTO(DicDosageUnit dicDosageUnit){
        super(dicDosageUnit);
        this.unitName = dicDosageUnit.getUnitName();
    }

}
