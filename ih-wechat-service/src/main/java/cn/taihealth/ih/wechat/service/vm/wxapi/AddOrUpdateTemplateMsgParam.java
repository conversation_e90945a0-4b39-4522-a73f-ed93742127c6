package cn.taihealth.ih.wechat.service.vm.wxapi;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AddOrUpdateTemplateMsgParam {

    @ApiModelProperty("appId")
    @NotBlank(message = "appId不能为空")
    private String appId;

    @ApiModelProperty("key")
    @NotBlank(message = "key不能为空")
    private String key;

    @ApiModelProperty("模版消息Id")
    private String templateId;

    @ApiModelProperty("消息描述")
    private String describe;
}
