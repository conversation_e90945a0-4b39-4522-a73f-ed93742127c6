package cn.taihealth.ih.wechat.service.vm.wechat;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.w3c.dom.Document;

import java.io.Serializable;

/**
 * <a href="https://yb.qq.com/yibao-payment/doc?nodeId=83680041366614016">3.6申请退款接口</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@XStreamAlias("xml")
public class WxInsurancePayRefundResult extends BaseWxInstrancePayResult implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 订单总金额, 以分为单位
     */
    @XStreamAlias("total_fee")
    private Integer totalFee;
    /**
     * 现金退款金额, 以分为单位
     */
    @XStreamAlias("cash_refund_fee")
    private Integer cashRefundFee;
    /**
     * 医保退款金额（目前只能全额退）, 以分为单位
     */
    @XStreamAlias("insurance_refund_fee")
    private Integer insuranceRefundFee;
    /**
     * 微信生成的退款医疗订单号, 后续可以根据此退款订单号查询退款单
     */
    @XStreamAlias("med_refund_id")
    private String medRefundId;
    /**
     * 微信医保支付生成的微信支付退款订单号, 混合支付或纯现金支付时返回
     */
    @XStreamAlias("cash_refund_id")
    private String cashRefundId;
    /**
     * 微信医保支付生成的医保退款单号, 混合支付或纯医保支付时返回
     */
    @XStreamAlias("insurance_refund_id")
    private String insuranceRefundId;

    @Override
    protected void loadXml(Document d) {
        this.totalFee = readXmlInteger(d, "total_fee");
        this.cashRefundFee = readXmlInteger(d, "cash_refund_fee");
        this.insuranceRefundFee = readXmlInteger(d, "insurance_refund_fee");
        this.medRefundId = readXmlString(d, "med_refund_id");
        this.cashRefundId = readXmlString(d, "cash_refund_id");
        this.insuranceRefundId = readXmlString(d, "insurance_refund_id");
    }

}
