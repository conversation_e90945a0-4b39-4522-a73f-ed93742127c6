<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <include file="./config/liquibase/mysql/changelog1/000000314-init-schema.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000315-alter-table-wechatorder-patient-mediccard.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000316-alter-table-hospital_app_setting-add-column.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000317-alter-table-medical_cards-index.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000318-create-table-ih_risk_report.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000319-create-table-ih_pedigree.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000320-alter-table-ih_offline_depts.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000321-alter-table-ih_offline_medical_workers.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000322-create-table-ih_bill_balance_records.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000323-alter-table-ih_charges.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000324-create-table-ih_case_report_form.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000325-alter-table-ih_case_report_form.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000326-alter-table-ih_case_report_form.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000327-alter-table-Insurance.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000328-alter-table-ih_offline_medical_workers.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000329-add-table-ih_wandpay_orders_refunds.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000330-add-table-ih_pre_consultation.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000331-alert-table-ih_jianguan.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000332-add-table-ih_hospital_annual_inspection.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000333-alter-table-ih_hospital_annual_inspection.xml" relativeToChangelogFile="false"/>
    <include file="./config/liquibase/mysql/changelog1/000000334-add-table-ih_dify_app_config.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>

