<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="zhengfei (generated)" id="1748920998497-852">
        <createTable tableName="ih_offline_hospital_annual_inspection">
            <column name="id" remarks="主键" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_offline_hospital_annual_inspectionPK"/>
            </column>
            <column name="created_date" type="datetime(6)">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime(6)">
                <constraints nullable="false"/>
            </column>
            <column name="average_doctors_number" remarks="日均坐诊医生数" type="INT"/>
            <column name="branch_number" remarks="派 出（分支）机构数量" type="INT"/>
            <column name="external_assets" remarks="对外资产（万元）" type="DECIMAL(19, 2)"/>
            <column name="fixed_assets" remarks="固定资产（万元）" type="DECIMAL(19, 2)"/>
            <column name="flow_assets" remarks="流动资产（万元）" type="DECIMAL(19, 2)"/>
            <column name="intangible_Assets" remarks="无形资产及开办费（万元）" type="DECIMAL(19, 2)"/>
            <column name="liabilities" remarks="负债（万元）" type="DECIMAL(19, 2)"/>
            <column name="licence_expiration" remarks="许可证有效期" type="datetime(6)"/>
            <column name="licence_number" remarks="许可证号码" type="VARCHAR(255)"/>
            <column name="net_assets" remarks="净资产（万元" type="DECIMAL(19, 2)"/>
            <column name="premises_area" remarks="业务用房面积" type="DECIMAL(19, 2)"/>
            <column name="registration_date" remarks="登记时间" type="datetime(6)"/>
            <column name="resource" remarks="来源 0：卫监所, 1：医院填报" type="VARCHAR(255)"/>
            <column name="service_customer_sum" remarks="客户服务人数总数" type="INT"/>
            <column name="three_level_equal_protection_code" remarks="三级等保编码" type="VARCHAR(255)"/>
            <column name="total_assets" remarks="总资产（万元）" type="DECIMAL(19, 2)"/>
            <column name="total_expenditure" remarks="总支出（万元）" type="DECIMAL(19, 2)"/>
            <column name="total_income" remarks="总收入（万元）" type="DECIMAL(19, 2)"/>
            <column name="update_three_level_equal_protection" remarks="三级等保是否更新 0：否, 1：是" type="VARCHAR(255)"/>
            <column name="uploaded_date" remarks="上传时间" type="datetime(6)"/>
            <column name="work_force" remarks="职工总数" type="INT"/>
            <column name="year_str" remarks="年份" type="VARCHAR(255)"/>
            <column name="offline_hospital_id" remarks="线下医院ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1748920998497-853">
        <addForeignKeyConstraint baseColumnNames="offline_hospital_id" baseTableName="ih_offline_hospital_annual_inspection" constraintName="FK_ANNUAL_INSPECTION_OFFLINE_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_offline_hospitals"
            validate="true"/>
    </changeSet>

    <changeSet author="zhengfei (generated)" id="1748920998497-854">
        <addColumn tableName="ih_offline_hospital_annual_inspection">
            <column name="annual_inspection_file_id" remarks="年检证明文件id" type="BIGINT"/>
        </addColumn>
    </changeSet>

    <changeSet author="zhengfei (generated)" id="1748920998497-855">
        <addForeignKeyConstraint baseColumnNames="annual_inspection_file_id" baseTableName="ih_offline_hospital_annual_inspection" constraintName="FK_ANNUAL_INSPECTION_FILE_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_uploads" validate="true"/>
    </changeSet>
</databaseChangeLog>
