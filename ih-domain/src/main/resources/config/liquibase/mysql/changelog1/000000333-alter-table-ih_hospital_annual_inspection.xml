<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="zhengfei (generated)" id="1709920998497-666">
        <addColumn tableName="ih_offline_hospital_annual_inspection">
            <column name="three_level_equal_protection_file_id" remarks="年检证明文件id" type="BIGINT"/>
        </addColumn>
    </changeSet>

    <changeSet author="zhengfei (generated)" id="1709920998497-667">
        <addForeignKeyConstraint baseColumnNames="three_level_equal_protection_file_id" baseTableName="ih_offline_hospital_annual_inspection" constraintName="FK_THREE_LEVEL_EQUAL_PROTECTION_FILE_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_uploads" validate="true"/>
    </changeSet>
</databaseChangeLog>
