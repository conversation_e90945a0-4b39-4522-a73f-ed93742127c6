<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="60902 (generated)" id="1747279995766-851">
        <addColumn tableName="ih_depts">
            <column name="parent_dept_id" remarks="上级科室" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-851">
        <addColumn tableName="ih_offline_hospitals">
            <column defaultValue="PUBLIC" name="forms_of_ownership" remarks="所有制形式 1：私有制 2：公有制 3：混合所有制" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-852">
        <addColumn tableName="ih_offline_hospitals">
            <column name="info_securit_level_protect_number" remarks="信息安全等级保护证书编号" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-853">
        <addColumn tableName="ih_offline_hospitals">
            <column defaultValue="THREE" name="info_security_level_protect" remarks="信息安全等级保护（1：一级 2：二级 3：三级，默认三级）" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-854">
        <addColumn tableName="ih_offline_hospitals">
            <column name="initial_funds" remarks="开办资金额数（万元）" type="decimal(19, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-855">
        <addColumn tableName="ih_offline_hospitals">
            <column name="issuance_date" remarks="发证日期" type="datetime(6)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-856">
        <addColumn tableName="ih_offline_hospitals">
            <column name="issuing_authority" remarks="发证机关" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-857">
        <addColumn tableName="ih_offline_hospitals">
            <column name="key_worker" remarks="主要负责人" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-858">
        <addColumn tableName="ih_offline_hospitals">
            <column name="medical_subjects" remarks="诊疗科目，在备案的诊疗科目范围，如有多个用“,”分隔" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-859">
        <addColumn tableName="ih_offline_hospitals">
            <column name="org_code" remarks="医疗许可证登记号" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-860">
        <addColumn tableName="ih_offline_hospitals">
            <column defaultValue="CITY" name="org_sort" remarks="医院属性（1：市属 2：区属 3：社会办医疗机构）" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747360837234-861">
        <addColumn tableName="ih_offline_hospitals">
            <column name="registration_no" remarks="证书登记号" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747366889183-871">
        <dropColumn columnName="is_chest_pain_center" tableName="ih_offline_hospitals"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747366889183-872">
        <dropColumn columnName="is_stroke_base" tableName="ih_offline_hospitals"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747366889183-873">
        <dropColumn columnName="stroke_grade" tableName="ih_offline_hospitals"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747381948899-851">
        <addColumn tableName="ih_medical_workers">
            <column name="issuance_date" remarks="执业发证日期" type="datetime(6)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747381948899-852">
        <addColumn tableName="ih_medical_workers">
            <column name="nation_code" remarks="医生国家编码" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747381948899-853">
        <addColumn tableName="ih_medical_workers">
            <column name="participation_date" remarks="参加工作日期" type="datetime(6)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747381948899-876">
        <dropColumn columnName="specialist_type" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-851">
        <addColumn tableName="ih_medical_workers">
            <column name="professional_job_sort" remarks="专业技术职务类别" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-871">
        <dropForeignKeyConstraint baseTableName="ih_medical_workers" constraintName="FK_MEDICAL_WORKERS_AGENCY_USER_ID"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-872">
        <dropForeignKeyConstraint baseTableName="ih_medical_workers" constraintName="FK_MEDICAL_WORKERS_AUDIT_USER_ID"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-874">
        <dropColumn columnName="agency_user_id" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-875">
        <dropColumn columnName="audit_date" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-876">
        <dropColumn columnName="audit_for" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-877">
        <dropColumn columnName="audit_status" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-878">
        <dropColumn columnName="audit_user_id" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-879">
        <dropColumn columnName="refuse_reason" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-880">
        <dropColumn columnName="register_type" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747388029924-881">
        <dropColumn columnName="submit_date" tableName="ih_medical_workers"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1747813336805-851">
        <addColumn tableName="ih_tickets">
            <column name="transactor" remarks="处理人" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748311628566-852">
        <addColumn tableName="ih_diagnosis_cas">
            <column name="sign_raw" remarks="签名原文" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748399277395-857">
        <createIndex indexName="IDX_DIAGNOSIS_CA_DIAGNOSIS_PDF_UID" tableName="ih_diagnosis_cas">
            <column name="diagnosis_pdf_uid"/>
        </createIndex>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748490214160-852">
        <addColumn tableName="ih_prescription_orders">
            <column name="sign_raw" remarks="签名原文" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748922776381-852">
        <addColumn tableName="ih_prescription_order_cas">
            <column name="doctor_sign" remarks="医生签名" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748922776381-853">
        <addColumn tableName="ih_diagnosis_cas">
            <column name="sign_data" remarks="签名值" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748922776381-854">
        <addColumn tableName="ih_diagnosis_cas">
            <column name="sign_sn" remarks="医生证书序列号" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748922776381-855">
        <dropColumn columnName="sign_raw" tableName="ih_diagnosis_cas"/>
        <dropColumn columnName="sign_raw" tableName="ih_prescription_orders"/>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748927739234-853">
        <addColumn tableName="ih_diagnosis_cas">
            <column name="sign_raw" remarks="签名原文" type="longtext"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748927739234-854">
        <addColumn tableName="ih_prescription_orders">
            <column name="sign_raw" remarks="签名原文" type="longtext"/>
        </addColumn>
    </changeSet>
    <changeSet author="60902 (generated)" id="1748927739234-855">
        <addColumn tableName="ih_prescription_order_cas">
            <column name="final_pdf_uid" remarks="医院签章pdf文件标识" type="varchar(255)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
