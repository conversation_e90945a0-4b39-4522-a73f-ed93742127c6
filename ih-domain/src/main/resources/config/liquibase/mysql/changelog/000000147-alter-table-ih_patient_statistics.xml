<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="zhengfei (generated)" id="1685092524972-62">
        <createTable tableName="ih_patient_statistics">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_patient_statisticsPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="ckd_count_mom" type="VARCHAR(255)"/>
            <column name="count_mom" type="VARCHAR(255)"/>
            <column name="date_unit" type="INT"/>
            <column name="ft_count_mom" type="VARCHAR(255)"/>
            <column name="statistics_date" type="date"/>
            <column name="hospital_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1685092524972-65">
        <createIndex indexName="INDEX_PATIENT_STATISTICS_DATE_UNIT" tableName="ih_patient_statistics">
            <column name="date_unit"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1685092524972-66">
        <createIndex indexName="INDEX_PATIENT_STATISTICS_HOSPITAL_ID" tableName="ih_patient_statistics">
            <column name="hospital_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1685092524972-67">
        <createIndex indexName="INDEX_PATIENT_STATISTICS_STATISTICS_DATE" tableName="ih_patient_statistics">
            <column name="statistics_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1685092524972-68">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_patient_statistics" constraintName="FK_PATIENT_STATISTICS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
