<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xwj (generated)" id="1682301844343-61">
        <dropTable tableName="ih_article_send"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1682301844343-62">
        <createTable tableName="ih_article_send">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_article_sendPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="is_send" type="BIT">
                <constraints nullable="false"/>
            </column>
            <column name="article_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="medical_Worker_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="patient_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="xwj (generated)" id="1682301844343-63">
        <addForeignKeyConstraint baseColumnNames="article_id" baseTableName="ih_article_send" constraintName="FK_ARTICLE_SEND_ARTICLE_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_articles" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1682301844343-64">
        <addForeignKeyConstraint baseColumnNames="medical_Worker_id" baseTableName="ih_article_send" constraintName="FK_ARTICLE_SEND_MEDICAL_WORKER_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_medical_workers" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1682301844343-65">
        <addForeignKeyConstraint baseColumnNames="patient_id" baseTableName="ih_article_send" constraintName="FK_ARTICLE_SEND_PATIENT_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_patients" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1682301844343-66">
        <addUniqueConstraint columnNames="is_send, patient_id, medical_Worker_id, article_id" constraintName="U_CLINIC_RECORD_IMAGES_RECORD_IMAGE" tableName="ih_article_send"/>
    </changeSet>
</databaseChangeLog>
