<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="zhengfei (generated)" id="1714103338856-887">
        <createTable tableName="ih_retry_refund_records">
            <column name="id" remarks="主键" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_retry_refund_recordsPK"/>
            </column>
            <column name="created_date" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" remarks="最后一次更新时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="retry_count" remarks="第几次退款" type="INT"/>
            <column name="failed_reason" remarks="最后一次退款失败的原因" type="VARCHAR(255)"/>
            <column name="hospital_id" remarks="医院id" type="BIGINT"/>
            <column name="status" remarks="退款订单状态" type="VARCHAR(255)"/>
            <column name="success" remarks="退款重试是否成功" type="BIT"/>
            <column name="success_time" remarks="退款成功时间" type="VARCHAR(255)"/>
            <column name="retry_time" remarks="重试时间" type="datetime"/>
            <column name="transaction_id" remarks="支付订单号" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1714103338856-903">
        <createIndex indexName="IDX_RETRY_REFUND_RECORD_TRANSACTION_ID" tableName="ih_retry_refund_records">
            <column name="transaction_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
