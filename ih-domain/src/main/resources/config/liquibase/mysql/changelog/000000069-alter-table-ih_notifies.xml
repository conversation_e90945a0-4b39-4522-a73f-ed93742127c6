<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="Xwj (generated)" id="1669620945128-53">
        <addColumn tableName="ih_notifies">
            <column defaultValue="IH_SYSTEM" name="app" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="xwj (generated)" id="1669620945128-54">
        <addColumn tableName="ih_notifies">
            <column name="patient_id" type="BIGINT" remarks="对应的就诊人"/>
        </addColumn>
    </changeSet>
    <changeSet author="xwj (generated)" id="1669620945128-55">
        <addForeignKeyConstraint baseColumnNames="patient_id" baseTableName="ih_notifies" constraintName="FK_NOTIFIES_PATIENT_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_patients" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1669620945128-56">
        <addColumn tableName="ih_notifies">
            <column name="dept_name" type="VARCHAR(255)" remarks="科室名字"/>
        </addColumn>
    </changeSet>
    <changeSet author="xwj (generated)" id="1669620945128-57">
        <addColumn tableName="ih_notifies">
            <column name="product_id" type="BIGINT" remarks="业务的id，比如订单详情就代表的是订单的id，如果是列表页则可能是null"/>
        </addColumn>
    </changeSet>
    <changeSet author="xwj (generated)" id="1669620945128-58">
        <addColumn tableName="ih_notifies">
            <column name="patient_name" type="VARCHAR(255)" remarks="对应的就诊人的名字"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
