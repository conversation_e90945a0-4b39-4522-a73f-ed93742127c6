<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="xuechun (generated)" id="1709000423006-180">
        <addColumn tableName="ih_crm_questionnaires">
            <column defaultValueComputed="0" name="is_need_visit_hospital" remarks="答题时是否需要填写就诊科室" type="bit"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1709000423006-181">
        <addColumn tableName="ih_crm_task_detail_results">
            <column name="visit_hospital_id" remarks="线下就诊医院id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1709100148357-173">
        <createTable remarks="问题关联显示表" tableName="ih_crm_questionnaire_link_shows">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_crm_questionnaire_link_showsPK"/>
            </column>
            <column name="linked_question_id" remarks="显示的问题id" type="BIGINT"/>
            <column name="linked_questionnaire_question_id" remarks="显示的关联问题id" type="BIGINT"/>
            <column name="option_id" remarks="选项id" type="BIGINT"/>
            <column name="question_id" remarks="问题id" type="BIGINT"/>
            <column name="questionnaire_question_id" remarks="问卷关联问题id" type="BIGINT"/>
            <column name="hospital_id" remarks="互联网医院id" type="BIGINT"/>
            <column name="questionnaire_id" remarks="问卷id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1709100148357-182">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_crm_questionnaire_link_shows" constraintName="FK_CRM_QUESTIONNAIRE_LINK_SHOWS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1709100148357-183">
        <addForeignKeyConstraint baseColumnNames="questionnaire_id" baseTableName="ih_crm_questionnaire_link_shows" constraintName="FK_CRM_QUESTIONNAIRE_LINK_SHOWS_QUESTIONNAIRE_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_crm_questionnaires" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1709100148357-200">
        <createTable remarks="问题对应渠道表" tableName="ih_crm_questionnaire_channels">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_crm_questionnaire_channelsPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="questionnaire_id" remarks="问卷id" type="BIGINT"/>
            <column name="channel_name" remarks="渠道名称" type="VARCHAR(255)"/>
            <column name="is_delete" defaultValueComputed="0" remarks="是否被删除" type="bit"/>
            <column name="hospital_id" remarks="互联网医院id" type="BIGINT"/>
            <column name="qr_code_id" remarks="二维码对应的文件id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="xwj (generated)" id="1709100148357-201">
        <addForeignKeyConstraint baseColumnNames="questionnaire_id" baseTableName="ih_crm_questionnaire_channels" constraintName="FK_CRM_QUESTIONNAIRE_CHANNELS_QUESTIONNAIRE_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_crm_questionnaires" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1709100148357-202">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_crm_questionnaire_channels" constraintName="FK_CRM_QUESTIONNAIRE_CHANNELS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1709100148357-203">
        <addForeignKeyConstraint baseColumnNames="qr_code_id" baseTableName="ih_crm_questionnaire_channels" constraintName="FK_CRM_QUESTIONNAIRE_CHANNELS_UPLOAD_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_uploads" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1709000423006-204">
        <addColumn tableName="ih_crm_task_detail_results">
            <column name="channel_id" remarks="线下渠道id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1709707493969-174">
        <addColumn tableName="ih_bills">
            <column defaultValue="UNKNOWN" name="reconciliation_result" remarks="对账结果" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1709876850402-174">
        <addColumn tableName="ih_user_medical_record_copy_appointment">
            <column defaultValue="REFUND" name="refund_type" remarks="退款方式" type="varchar(255)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
