<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xwj (generated)" id="1689135333866-57">
        <createTable tableName="ih_hospital_dictionary">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_hospital_dictionaryPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="parent_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="xwj (generated)" id="1689135333866-58">
        <addUniqueConstraint columnNames="hospital_id, parent_id, name" constraintName="U_HOSPITAL_DICTIONARY_HOSPITAL_PARENT_NAME" tableName="ih_hospital_dictionary"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1689135333866-59">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_hospital_dictionary" constraintName="FK_HOSPITAL_DICTIONARY_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1689135333866-60">
        <addForeignKeyConstraint baseColumnNames="parent_id" baseTableName="ih_hospital_dictionary" constraintName="FK_HOSPITAL_DICTIONARY_PARENT_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospital_dictionary" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1689135333866-61">
        <createIndex indexName="IDX_HOSPITAL_DICTIONARY_CODE" tableName="ih_hospital_dictionary">
            <column name="code"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
