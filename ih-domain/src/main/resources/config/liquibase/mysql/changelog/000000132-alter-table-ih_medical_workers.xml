<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="alex (generated)" id="1681184837883-61">
        <addColumn tableName="ih_medical_workers">
            <column defaultValue="SELF_REGISTER" name="register_type" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="alex (generated)" id="1681184837883-62">
        <addColumn tableName="ih_medical_workers">
            <column name="agency_user_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="alex (generated)" id="1681184837883-69">
        <addForeignKeyConstraint baseColumnNames="agency_user_id" baseTableName="ih_medical_workers" constraintName="FK_MEDICAL_WORKERS_AGENCY_USER_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_users" validate="true"/>
    </changeSet>
</databaseChangeLog>
