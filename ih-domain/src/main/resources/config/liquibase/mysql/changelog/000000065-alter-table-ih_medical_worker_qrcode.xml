<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

  <changeSet author="kangjing (generated)" id="1669018404926-55">
    <addColumn tableName="ih_medical_worker_qrcode">
      <column name="upload_id" type="bigint"/>
    </addColumn>
  </changeSet>
  <changeSet author="kangjing (generated)" id="1669018404926-56">
    <addForeignKeyConstraint baseColumnNames="upload_id" baseTableName="ih_medical_worker_qrcode"
      constraintName="FK_MEDICAL_WORKER_QRCODE_UPLOAD_ID" deferrable="false"
      initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_uploads"
      validate="true"/>
  </changeSet>
</databaseChangeLog>
