<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xwj (generated)" id="1685524209910-62">
        <createTable tableName="ih_special_disease_doctor_of_day">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_special_disease_doctor_of_dayPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="statistics_date" type="date"/>
            <column name="today_ckd_doctor_count" type="INT"/>
            <column name="today_ft_doctor_count" type="INT"/>
            <column name="hospital_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="xwj (generated)" id="1685524209910-63">
        <createIndex indexName="FK_SPECIAL_DISEASE_DOCTOR_OF_DAY_HOSPITAL_ID" tableName="ih_special_disease_doctor_of_day">
            <column name="hospital_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="xwj (generated)" id="1685524209910-64">
        <createIndex indexName="INDEX_STATISTICS_DATE" tableName="ih_special_disease_doctor_of_day">
            <column name="statistics_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="xwj (generated)" id="1685524209910-65">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_special_disease_doctor_of_day" constraintName="FK_SPECIAL_DISEASE_DOCTOR_OF_DAY_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
