<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">
    <changeSet author="xuechun (generated)" id="1621837137632-136">
        <addColumn tableName="ih_dic_med_adm_freqs">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-137">
        <addColumn tableName="ih_dic_med_adm_routes">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-138">
        <addColumn tableName="ih_dic_med_categorys">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-139">
        <addColumn tableName="ih_dic_med_dosage_forms">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-140">
        <addColumn tableName="ih_dic_med_dosage_units">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-141">
        <addColumn tableName="ih_dic_med_infos">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-142">
        <addColumn tableName="ih_dic_med_package_units">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-143">
        <addUniqueConstraint columnNames="category_name, hospital_id" constraintName="U_CATEGORY_NAME_HOSPITAL_ID" tableName="ih_dic_med_categorys"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-144">
        <addUniqueConstraint columnNames="nmpa_number, channel, hospital_id" constraintName="U_DIC_MEDINFO_CHANNEL_HOSPITAL_ID" tableName="ih_dic_med_infos"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-145">
        <addUniqueConstraint columnNames="form_name, hospital_id" constraintName="U_FORM_NAME_HOSPITAL_ID" tableName="ih_dic_med_dosage_forms"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-146">
        <addUniqueConstraint columnNames="frequency_code, hospital_id" constraintName="U_FREQUENCY_CODE_HOSPITAL_ID" tableName="ih_dic_med_adm_freqs"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-147">
        <addUniqueConstraint columnNames="route_code, hospital_id" constraintName="U_ROUTE_CODE_HOSPITAL_ID" tableName="ih_dic_med_adm_routes"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-148">
        <addUniqueConstraint columnNames="unit_name, hospital_id" constraintName="U_UNIT_NAME_HOSPITAL_ID" tableName="ih_dic_med_dosage_units"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-149">
        <addUniqueConstraint columnNames="unit_name, hospital_id" constraintName="U_UNIT_NAME_HOSPITAL_ID" tableName="ih_dic_med_package_units"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-150">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_dic_med_adm_freqs" constraintName="FK_DIC_MED_ADM_FREQS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-151">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_dic_med_adm_routes" constraintName="FK_DIC_MED_ADM_ROUTES_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-152">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_dic_med_categorys" constraintName="FK_DIC_MED_CATEGORYS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-153">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_dic_med_dosage_forms" constraintName="FK_DIC_MED_DOSAGE_FORMS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-154">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_dic_med_dosage_units" constraintName="FK_DIC_MED_DOSAGE_UNITS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-155">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_dic_med_infos" constraintName="FK_DIC_MED_INFOS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-156">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_dic_med_package_units" constraintName="FK_DIC_MED_PACKAGE_UNITS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-157">
        <dropUniqueConstraint constraintName="U_CATEGORY_NAME" tableName="ih_dic_med_categorys"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-158">
        <dropUniqueConstraint constraintName="U_DIC_MEDINFO" tableName="ih_dic_med_infos"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-159">
        <dropUniqueConstraint constraintName="U_FORM_NAME" tableName="ih_dic_med_dosage_forms"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-160">
        <dropUniqueConstraint constraintName="U_FREQUENCY_CODE" tableName="ih_dic_med_adm_freqs"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-161">
        <dropUniqueConstraint constraintName="U_ROUTE_CODE" tableName="ih_dic_med_adm_routes"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-162">
        <dropUniqueConstraint constraintName="U_UNIT_NAME" tableName="ih_dic_med_dosage_units"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-163">
        <dropUniqueConstraint constraintName="U_UNIT_NAME" tableName="ih_dic_med_package_units"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621837137632-164">
        <addColumn tableName="ih_docs">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621937857242-143">
        <createTable tableName="ih_hospital_files">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" primaryKeyName="ih_hospital_filesPK"/>
            </column>
            <column name="file_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621937857242-144">
        <addUniqueConstraint columnNames="hospital_id, file_id" constraintName="U_HOSPITAL_FILES_HOSPITAL_FILE" tableName="ih_hospital_files"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621937857242-145">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_docs" constraintName="FK_DOCS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621937857242-146">
        <addForeignKeyConstraint baseColumnNames="file_id" baseTableName="ih_hospital_files" constraintName="FK_HOSPITAL_FILES_FILE_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_uploads" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1621937857242-147">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_hospital_files" constraintName="FK_HOSPITAL_FILES_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1622193152143-144">
        <addUniqueConstraint columnNames="hospital_id, name" constraintName="U_DOCS_HOSPITAL_NAME" tableName="ih_docs"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1622193152143-146">
        <dropUniqueConstraint constraintName="U_DOCS_NAME" tableName="ih_docs"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1622195027777-145">
        <addColumn tableName="ih_health_exam">
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1622195027777-147">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_health_exam" constraintName="FK_HEALTH_EXAMS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1622796014328-145">
        <addUniqueConstraint columnNames="code" constraintName="U_HOSPITALS_CODE" tableName="ih_hospitals"/>
    </changeSet>
</databaseChangeLog>
