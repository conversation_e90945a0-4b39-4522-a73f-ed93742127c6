<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="wyj (generated)" id="1713857427806-884">
        <addColumn tableName="ih_his_bills">
            <column name="hospital_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713857427806-885">
        <addColumn tableName="ih_his_bills">
            <column defaultValueNumeric="0" name="order_type" remarks="自费或医保订单类型，0表示自费订单，1表示医保订单" type="integer"/>
        </addColumn>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713857427806-886">
        <addColumn tableName="ih_his_bills">
            <column defaultValueNumeric="1" name="pay_type" remarks="支付方式，1表示微信支付，2表示支付宝支付" type="integer"/>
        </addColumn>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713857427806-887">
        <addColumn tableName="ih_his_bills">
            <column name="settle_id" remarks="HIS收据号, 收款时传入收款收据号，退款状态传退款收据号" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713857427806-901">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_his_bills" constraintName="FK_HIS_BILL_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
