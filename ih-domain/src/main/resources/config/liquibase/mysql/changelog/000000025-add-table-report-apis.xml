<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="zhengfei (generated)" id="1649644962249-50">
        <createTable tableName="ih_report_apis">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_report_apisPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="is_report" type="BIT">
                <constraints nullable="false"/>
            </column>
            <column name="service_Url" type="VARCHAR(255)"/>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1649644962249-51">
        <addUniqueConstraint columnNames="code, hospital_id" constraintName="U_REPORT_APIS_CODE_HOSPITAL_ID" tableName="ih_report_apis"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1649644962249-53">
        <createIndex indexName="IDX_REPORT_APIS_IS_REPORT" tableName="ih_report_apis">
            <column name="is_report"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
