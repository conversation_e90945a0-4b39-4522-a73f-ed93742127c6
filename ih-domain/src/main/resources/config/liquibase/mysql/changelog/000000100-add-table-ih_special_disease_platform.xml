<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="zhengfei (generated)" id="1673841931223-54">
        <createTable tableName="ih_hospital_special_disease_platform_rel">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_hospital_special_disease_platform_relPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="special_disease_platform_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-55">
        <createTable tableName="ih_medical_worker_special_disease_platform_rel">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_medical_worker_special_disease_platform_relPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="medical_worker_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="special_disease_platform_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-56">
        <createTable tableName="ih_patient_special_disease_platform_rel">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_patient_special_disease_platform_relPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="patient_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="special_disease_platform_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-57">
        <createTable tableName="ih_special_disease_platforms">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_special_disease_platformsPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-59">
        <addUniqueConstraint columnNames="code" constraintName="U_SPECIAL_DISEASE_PLATFORMS_CODE" tableName="ih_special_disease_platforms"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-60">
        <addUniqueConstraint columnNames="name" constraintName="U_SPECIAL_DISEASE_PLATFORMS_NAME" tableName="ih_special_disease_platforms"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-61">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_hospital_special_disease_platform_rel" constraintName="FK_IH_S_D_P_R_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-62">
        <addForeignKeyConstraint baseColumnNames="special_disease_platform_id" baseTableName="ih_hospital_special_disease_platform_rel" constraintName="FK_IH_S_D_P_R_SPECIAL_DISEASE_PLATFORM_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id"
            referencedTableName="ih_special_disease_platforms" validate="true"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-63">
        <addForeignKeyConstraint baseColumnNames="medical_worker_id" baseTableName="ih_medical_worker_special_disease_platform_rel" constraintName="FK_M_W_S_D_P_REL_MEDICAL_WORKER_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_medical_workers"
            validate="true"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-64">
        <addForeignKeyConstraint baseColumnNames="special_disease_platform_id" baseTableName="ih_medical_worker_special_disease_platform_rel" constraintName="FK_M_W_S_D_P_REL_SPECIAL_DISEASE_PLATFORM_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id"
            referencedTableName="ih_special_disease_platforms" validate="true"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-65">
        <addForeignKeyConstraint baseColumnNames="patient_id" baseTableName="ih_patient_special_disease_platform_rel" constraintName="FK_P_S_D_P_REL_PATIENT_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_patients" validate="true"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1673841931223-66">
        <addForeignKeyConstraint baseColumnNames="special_disease_platform_id" baseTableName="ih_patient_special_disease_platform_rel" constraintName="FK_P_S_D_P_REL_SPECIAL_DISEASE_PLATFORM_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id"
            referencedTableName="ih_special_disease_platforms" validate="true"/>
    </changeSet>
</databaseChangeLog>
