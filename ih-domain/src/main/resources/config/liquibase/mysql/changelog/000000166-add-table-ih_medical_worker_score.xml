<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xwj (generated)" id="1689215908082-57">
        <createTable tableName="ih_medical_worker_score">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_medical_worker_scorePK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="default_score" type="DOUBLE"/>
            <column name="good_feedback_score" type="DOUBLE"/>
            <column name="patient_total_score" type="INT"/>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="medical_worker_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="xwj (generated)" id="1689215908082-58">
        <addUniqueConstraint columnNames="medical_worker_id" constraintName="U_MEDICAL_WORKER_SCORE_DOCTOR_ID" tableName="ih_medical_worker_score"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1689215908082-60">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_medical_worker_score" constraintName="FK_MEDICAL_WORKER_SCORE_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1689215908082-61">
        <addForeignKeyConstraint baseColumnNames="medical_worker_id" baseTableName="ih_medical_worker_score" constraintName="FK_MEDICAL_WORKER_SCORE_MEDICAL_WORKER_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_medical_workers" validate="true"/>
    </changeSet>
</databaseChangeLog>
