<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="wyj (generated)" id="1715419199509-1259">
        <addUniqueConstraint columnNames="hospital_id, user_id, open_id, ali_user_id" constraintName="U_USER_PLATFORM_HOSPITAL_USER_OPEN_ID_ALI_USER_ID" tableName="ih_user_platform_info"/>
    </changeSet>
    <changeSet author="wyj (generated)" id="1715419199509-1258">
        <dropUniqueConstraint constraintName="U_USER_PLATFORM_HOSPITAL_USER_OPEN_ID" tableName="ih_user_platform_info"/>
    </changeSet>
</databaseChangeLog>
