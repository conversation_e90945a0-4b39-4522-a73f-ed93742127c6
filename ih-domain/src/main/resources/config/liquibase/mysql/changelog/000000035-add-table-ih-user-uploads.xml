<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xuechun (generated)" id="1651599369346-53">
        <createTable tableName="ih_user_uploads">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_user_uploadsPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="signature_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1651599369346-55">
        <addForeignKeyConstraint baseColumnNames="signature_id" baseTableName="ih_user_uploads" constraintName="FK_USER_UPLOAD_SIGNATURE_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_uploads" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1651599369346-56">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="ih_user_uploads" constraintName="FK_USER_UPLOAD_USER_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_users" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1651599369346-57">
        <dropColumn columnName="identity_card" tableName="ih_users"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1651599369346-58">
        <dropColumn columnName="national_emblem" tableName="ih_users"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1651599369346-59">
        <dropColumn columnName="portrait" tableName="ih_users"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1651599369346-60">
        <dropColumn columnName="signature_png" tableName="ih_users"/>
    </changeSet>
</databaseChangeLog>
