<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="lizhexuan (generated)" id="1668568693775-53">
        <addColumn tableName="ih_articles">
            <column name="modifier" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1668568693775-54">
        <addForeignKeyConstraint baseColumnNames="modifier" baseTableName="ih_articles" constraintName="FK_ARTICLES_MODIFIER" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_users" validate="true"/>
    </changeSet>
</databaseChangeLog>
