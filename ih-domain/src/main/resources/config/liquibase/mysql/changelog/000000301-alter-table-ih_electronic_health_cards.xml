<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="jiangzhengshan (generated)" id="1718768617778-857">
        <addColumn tableName="ih_electronic_health_cards">
            <column name="diagnosis_code" remarks="电子健康卡二维码" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="jiangzhengshan (generated)" id="1718768617778-870">
        <addForeignKeyConstraint baseColumnNames="diagnosis_code" baseTableName="ih_electronic_health_cards" constraintName="FK_ELECTRONIC_HEALTH_CARDS_UPLOAD_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_uploads" validate="true"/>
    </changeSet>
    <changeSet author="jiangzhengshan (generated)" id="1718768617778-460">
        <addDefaultValue columnDataType="boolean" columnName="is_bind" defaultValueComputed="0" tableName="ih_electronic_health_cards"/>
    </changeSet>
    <changeSet author="jiangzhengshan (generated)" id="1718768617778-461">
        <addDefaultValue columnDataType="boolean" columnName="is_bound" defaultValueComputed="0" tableName="ih_electronic_health_cards"/>
    </changeSet>
</databaseChangeLog>
