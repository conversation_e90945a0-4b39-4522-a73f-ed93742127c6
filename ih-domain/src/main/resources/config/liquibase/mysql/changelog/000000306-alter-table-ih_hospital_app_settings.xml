<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="xuechun (generated)" id="1721905462441-859">
        <addColumn tableName="ih_hospital_app_settings">
            <column defaultValueComputed="1" name="need_auth" remarks="是否需要登录才能跳转" type="bit"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1721906454378-860">
        <addColumn tableName="ih_hospital_service_settings">
            <column defaultValueComputed="1" name="need_auth" remarks="是否需要登录才能跳转" type="bit"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1721906454378-861">
        <setColumnRemarks columnName="introduction" remarks="简介" tableName="ih_medical_workers" columnDataType="text"/>
        <setColumnRemarks columnName="introduction" remarks="简介" tableName="ih_offline_medical_workers" columnDataType="text"/>
        <setColumnRemarks columnName="areas_of_expertise" remarks="擅长领域" tableName="ih_medical_workers" columnDataType="text"/>
        <setColumnRemarks columnName="areas_of_expertise" remarks="擅长领域" tableName="ih_offline_medical_workers" columnDataType="text"/>
        <setColumnRemarks columnName="introduction" remarks="信息简介" tableName="ih_offline_depts" columnDataType="text"/>
    </changeSet>
</databaseChangeLog>
