<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="zhengfei (generated)" id="1679982202831-60">
        <addColumn tableName="ih_followup_record">
            <column name="hospital_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1679982202831-61">
        <addColumn tableName="ih_followup_record">
            <column defaultValueComputed="0" name="noticed" type="bit"/>
        </addColumn>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1679982202831-62">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_followup_record" constraintName="IH_FOLLOWUP_RECORD_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>

</databaseChangeLog>
