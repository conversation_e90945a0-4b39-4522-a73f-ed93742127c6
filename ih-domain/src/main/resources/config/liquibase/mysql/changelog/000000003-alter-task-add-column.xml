<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">
    <changeSet author="zhengfei (generated)" id="1621849388188-143">
        <createTable tableName="ih_hospital_create_tasks">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" primaryKeyName="ih_hospital_create_tasksPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="password" type="VARCHAR(60)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="SUBMIT_FORM" name="stage" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="username" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1621849388188-144">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_hospital_create_tasks" constraintName="FK_HOSPITAL_CREATE_TASK_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
