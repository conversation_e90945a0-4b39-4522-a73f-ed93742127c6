<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="20240615000000-1" author="taiyi">
        <createTable tableName="dify_app_config">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="hospital_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="app_type" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="app_id" type="varchar(100)">
                <constraints nullable="false"/>
            </column>
            <column name="api_key" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="dify_url" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="app_name" type="varchar(100)"/>
            <column name="app_icon_url" type="varchar(255)"/>
            <column name="enabled" type="boolean" defaultValueBoolean="true"/>
            <column name="created_date" type="datetime"/>
            <column name="last_modified_date" type="datetime"/>
        </createTable>
        
        <createIndex indexName="idx_dify_app_config_hospital_id"
                     tableName="dify_app_config">
            <column name="hospital_id"/>
        </createIndex>
        
        <createIndex indexName="idx_dify_app_config_app_type"
                     tableName="dify_app_config">
            <column name="app_type"/>
        </createIndex>
        
        <createIndex indexName="idx_dify_app_config_hospital_app_type"
                     tableName="dify_app_config" unique="true">
            <column name="hospital_id"/>
            <column name="app_type"/>
        </createIndex>
        
        <addForeignKeyConstraint baseColumnNames="hospital_id"
                                 baseTableName="dify_app_config"
                                 constraintName="fk_dify_app_config_hospital_id"
                                 referencedColumnNames="id"
                                 referencedTableName="ih_hospital"/>
    </changeSet>

    <!-- 迁移默认配置到新表 -->
    <changeSet id="20240615000000-2" author="taiyi">
        <sql>
            INSERT INTO dify_app_config (hospital_id, app_type, app_id, api_key, dify_url, app_name, app_icon_url, enabled, created_date, last_modified_date)
            SELECT 
                h.id as hospital_id,
                'default' as app_type,
                s1.value as app_id,
                s2.value as api_key,
                'https://api.dify.ai/v1' as dify_url,
                s3.value as app_name,
                s4.value as app_icon_url,
                1 as enabled,
                NOW() as created_date,
                NOW() as last_modified_date
            FROM 
                ih_hospital h
                LEFT JOIN ih_hospital_setting s1 ON h.id = s1.hospital_id AND s1.key_name = 'smart_medical_consult_app_id'
                LEFT JOIN ih_hospital_setting s2 ON h.id = s2.hospital_id AND s2.key_name = 'smart_medical_consult_api_key'
                LEFT JOIN ih_hospital_setting s3 ON h.id = s3.hospital_id AND s3.key_name = 'smart_medical_consult_app_name'
                LEFT JOIN ih_hospital_setting s4 ON h.id = s4.hospital_id AND s4.key_name = 'smart_medical_consult_app_icon'
            WHERE
                s2.value IS NOT NULL AND s2.value != '';
        </sql>
    </changeSet>
</databaseChangeLog> 