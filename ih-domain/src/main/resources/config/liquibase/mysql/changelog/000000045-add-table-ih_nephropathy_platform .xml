<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="zhengfei (generated)" id="1666150503885-53">
        <createTable tableName="ih_nephropathy_platform">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_nephropathy_platformPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="nephropathy_platform" type="VARCHAR(1000)"/>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1666150503885-54">
        <addColumn tableName="ih_medical_workers">
            <column defaultValueComputed="0" name="is_abdominal_dialysis" type="bit"/>
        </addColumn>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1666150503885-55">
        <addColumn tableName="ih_medical_workers">
            <column defaultValueComputed="0" name="is_blood_dialysis" type="bit"/>
        </addColumn>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1666150503885-56">
        <addColumn tableName="ih_medical_workers">
            <column defaultValueComputed="0" name="is_gkd" type="bit"/>
        </addColumn>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1666150503885-57">
        <addColumn tableName="ih_medical_workers">
            <column name="offline_hospital_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1666150503885-59">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_nephropathy_platform" constraintName="FK_IH_NEPHROPATHY_PLATFORM_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1666150503885-60">
        <addForeignKeyConstraint baseColumnNames="offline_hospital_id" baseTableName="ih_medical_workers" constraintName="FK_MEDICAL_WORKERS_OFFLINE_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_offline_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
