<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">
    <changeSet author="zhengfei (generated)" id="1624604902112-9">
        <dropNotNullConstraint columnDataType="char(100)" columnName="generic_name_english" tableName="ih_dic_med_infos"/>
        <dropDefaultValue columnDataType="char(100)" columnName="generic_name_english" tableName="ih_dic_med_infos"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1624604902112-10">
        <dropNotNullConstraint columnDataType="char(100)" columnName="generic_name_input_code" tableName="ih_dic_med_infos"/>
        <dropDefaultValue columnDataType="char(100)" columnName="generic_name_input_code" tableName="ih_dic_med_infos"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1624604902112-35">
        <dropDefaultValue columnDataType="bigint COMMENT '零售价单位为分'" columnName="retail_price" tableName="ih_dic_med_infos"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1624604902112-36">
        <dropNotNullConstraint columnDataType="char(100)" columnName="trade_name" tableName="ih_dic_med_infos"/>
        <dropDefaultValue columnDataType="char(100)" columnName="trade_name" tableName="ih_dic_med_infos"/>
    </changeSet>
    <changeSet author="zhengfei (generated)" id="1624604902112-37">
        <dropNotNullConstraint columnDataType="char(100)" columnName="packaging_spec" tableName="ih_dic_med_infos"/>
        <dropDefaultValue columnDataType="char(100)" columnName="packaging_spec" tableName="ih_dic_med_infos"/>
    </changeSet>
</databaseChangeLog>
