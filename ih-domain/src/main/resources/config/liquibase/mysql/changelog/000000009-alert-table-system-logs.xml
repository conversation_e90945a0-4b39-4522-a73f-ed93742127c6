<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">
    <changeSet author="xuechun (generated)" id="1623221955064-146">
        <addColumn tableName="ih_system_logs">
            <column name="hospital_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1623221955064-148">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_system_logs" constraintName="FK_SYSTEM_LOGS_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1623395028687-146">
        <addColumn tableName="ih_medical_workers">
            <column defaultValueComputed="0" name="is_admin" type="bit"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
