<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="wyj (generated)" id="1713871116969-885">
        <createTable tableName="ih_bills_new">
            <column name="id" remarks="主键" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_bills_newPK"/>
            </column>
            <column name="created_date" remarks="创建时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" remarks="最后一次更新时间" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="bill_channel" type="INT"/>
            <column name="bill_operate_type" type="INT"/>
            <column name="bill_pay_status" type="INT"/>
            <column name="bill_service_type" type="INT"/>
            <column name="bill_source" type="INT"/>
            <column name="dept_code" remarks="科室code" type="VARCHAR(255)"/>
            <column name="dept_id" remarks="科室id" type="BIGINT"/>
            <column name="dept_name" remarks="科室名称" type="VARCHAR(255)"/>
            <column name="dept_online_type" remarks="线上/线下科室" type="VARCHAR(255)"/>
            <column name="his_amount" remarks="医保his总金额" type="INT"/>
            <column name="his_medicare_amount" remarks="医保his医保部分" type="INT"/>
            <column name="his_self_amount" remarks="医保his自付部分" type="INT"/>
            <column name="insurance_flag" type="BIT"/>
            <column name="medicare_payment_amount" remarks="医保支付金额" type="INT"/>
            <column name="medicare_refund_amount" remarks="医保退款金额" type="INT"/>
            <column name="medicare_refunded_amount" remarks="医保已退款金额" type="INT"/>
            <column name="merchant_order_number" type="VARCHAR(255)"/>
            <column name="order_no" type="VARCHAR(255)"/>
            <column name="order_operate_time" type="datetime"/>
            <column name="order_time" type="datetime"/>
            <column name="out_refund_no" remarks="商户退款单号" type="VARCHAR(255)"/>
            <column name="patient_id" type="BIGINT"/>
            <column name="patient_mobile" type="VARCHAR(255)"/>
            <column name="patient_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="pay_order_id" remarks="医保支付订单号" type="VARCHAR(255)"/>
            <column name="payment_amount" type="INT"/>
            <column name="payment_time" type="datetime"/>
            <column name="reconciliation_doubt" type="BIT"/>
            <column defaultValue="UNKNOWN" name="reconciliation_result" remarks="对账结果" type="VARCHAR(255)"/>
            <column name="refund_amount" type="INT"/>
            <column name="refund_initiation_time" type="datetime"/>
            <column name="refund_type" remarks="退款类型" type="VARCHAR(255)"/>
            <column name="refunded_amount" remarks="已退款金额" type="INT"/>
            <column name="remark" remarks="备注" type="VARCHAR(255)"/>
            <column name="self_payment_amount" remarks="个人支付金额" type="INT"/>
            <column name="self_refund_amount" remarks="个人退款金额" type="INT"/>
            <column name="self_refunded_amount" remarks="个人已退款金额" type="INT"/>
            <column name="settle_id" remarks="HIS收据号, 收款时传入收款收据号，退款状态传退款收据号" type="VARCHAR(255)"/>
            <column name="solve_flag" type="BIT"/>
            <column name="transaction_id" type="VARCHAR(255)"/>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713871116969-886">
        <createIndex indexName="IDX_BILL_CHANNEL" tableName="ih_bills">
            <column name="bill_channel"/>
        </createIndex>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713871116969-887">
        <createIndex indexName="IDX_BILL_NEW_CHANNEL" tableName="ih_bills_new">
            <column name="bill_channel"/>
        </createIndex>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713871116969-888">
        <createIndex indexName="IDX_BILL_NEW_OPERATE_TYPE" tableName="ih_bills_new">
            <column name="bill_operate_type"/>
        </createIndex>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713871116969-889">
        <createIndex indexName="IDX_BILL_NEW_PAY_STATUS" tableName="ih_bills_new">
            <column name="bill_pay_status"/>
        </createIndex>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713871116969-890">
        <createIndex indexName="IDX_BILL_NEW_SERVICE_TYPE" tableName="ih_bills_new">
            <column name="bill_service_type"/>
        </createIndex>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713871116969-891">
        <createIndex indexName="IDX_BILL_NEW_SOURCE" tableName="ih_bills_new">
            <column name="bill_source"/>
        </createIndex>
    </changeSet>
    <changeSet author="wyj (generated)" id="1713871116969-899">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_bills_new" constraintName="FK_BILLS_NEW_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
