<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="云冰 (generated)" id="1697477624712-121">
        <createTable tableName="ih_phy_exam">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_phy_examPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="INT"/>
            <column name="appointment_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="attribute" type="INT"/>
            <column name="exam_no" type="VARCHAR(255)"/>
            <column name="pay_time" type="datetime"/>
            <column name="status" type="INT"/>
            <column name="hospital_id" type="BIGINT"/>
            <column name="patient_id" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet author="云冰 (generated)" id="1697477624712-138">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_phy_exam" constraintName="FK_PHY_EXAM_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="云冰 (generated)" id="1697477624712-139">
        <addForeignKeyConstraint baseColumnNames="patient_id" baseTableName="ih_phy_exam" constraintName="FK_PHY_EXAM_PATIENT_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_patients" validate="true"/>
    </changeSet>

</databaseChangeLog>
