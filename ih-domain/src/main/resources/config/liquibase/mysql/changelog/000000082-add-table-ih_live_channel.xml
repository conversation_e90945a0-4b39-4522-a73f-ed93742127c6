<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xwj (generated)" id="1670395267865-54">
        <createTable tableName="ih_live_channel">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_live_channelPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="channel_status" type="BIT"/>
            <column name="client_type" type="VARCHAR(255)"/>
            <column name="enable_sing_up" type="BIT"/>
            <column name="live_channel_audience_link" type="VARCHAR(255)"/>
            <column name="live_channel_cover" type="VARCHAR(255)"/>
            <column name="live_channel_host_link" type="VARCHAR(255)"/>
            <column name="live_channel_host_password" type="VARCHAR(255)"/>
            <column name="live_channel_id" type="VARCHAR(255)"/>
            <column name="live_channel_name" type="VARCHAR(255)"/>
            <column name="live_platform_type" type="VARCHAR(255)"/>
            <column name="live_start_time" type="VARCHAR(255)"/>
            <column name="live_status" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="xwj (generated)" id="1670395267865-55">
        <createTable tableName="ih_live_platform_hospital_rels">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_live_platform_hospital_relsPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="appid" type="VARCHAR(255)"/>
            <column name="live_platform_type" type="VARCHAR(255)"/>
            <column name="secret" type="VARCHAR(255)"/>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="xwj (generated)" id="1670395267865-56">
        <createIndex indexName="IDX_LIVE_CHANNEL_ID" tableName="ih_live_channel">
            <column name="live_channel_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="xwj (generated)" id="1670395267865-57">
        <createIndex indexName="IDX_LIVE_CHANNEL_NAME" tableName="ih_live_channel">
            <column name="live_channel_name"/>
        </createIndex>
    </changeSet>
    <changeSet author="xwj (generated)" id="1670395267865-58">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_live_platform_hospital_rels" constraintName="FK_LIVE_PLATFORM_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
