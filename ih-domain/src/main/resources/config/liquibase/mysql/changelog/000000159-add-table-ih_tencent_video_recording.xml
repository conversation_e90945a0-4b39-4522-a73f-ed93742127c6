<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xwj (generated)" id="1688707202802-57">
        <createTable tableName="ih_tencent_video_recording">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_tencent_video_recordingPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="cache_file" type="VARCHAR(255)"/>
            <column name="end_time" type="datetime"/>
            <column name="file_id" type="VARCHAR(255)"/>
            <column name="media_id" type="VARCHAR(255)"/>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="room_id" type="VARCHAR(255)"/>
            <column name="start_time" type="datetime"/>
            <column name="status" type="INT"/>
            <column name="task_id" type="VARCHAR(255)"/>
            <column name="track_type" type="VARCHAR(255)"/>
            <column name="user_id" type="VARCHAR(255)"/>
            <column name="video_oss_url" type="VARCHAR(255)"/>
            <column name="video_url" type="VARCHAR(255)"/>
            <column name="his_file_id" type="VARCHAR(255)"/>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="xwj (generated)" id="1688707202802-58">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_tencent_video_recording" constraintName="FK_TENCENT_VIDEO_RECORDING_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
