<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xuechun (generated)" id="1652928552689-53">
        <createTable tableName="ih_channel">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_channelPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="drug_channels" type="VARCHAR(255)"/>
            <column name="hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1652928552689-55">
        <addForeignKeyConstraint baseColumnNames="hospital_id" baseTableName="ih_channel" constraintName="FK_CHANNEL_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_hospitals" validate="true"/>
    </changeSet>
</databaseChangeLog>
