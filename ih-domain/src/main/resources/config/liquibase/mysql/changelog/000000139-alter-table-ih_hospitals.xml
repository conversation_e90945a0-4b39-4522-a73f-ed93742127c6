<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xwj (generated)" id="1683526941442-63">
        <addColumn tableName="ih_hospitals">
            <column defaultValue="JUNZIQIAN" name="hospital_ca" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun" id="1683526941442-64">
        <sql>
            ALTER TABLE `ih_hospitals` MODIFY COLUMN `hospital_ca` varchar(255) NULL DEFAULT 'UNKNOWN';
        </sql>
    </changeSet>
    <changeSet author="xuechun" id="1683526941442-65">
        <sql>
            update ih_hospitals set hospital_ca = 'UNKNOWN';
        </sql>
    </changeSet>
</databaseChangeLog>
