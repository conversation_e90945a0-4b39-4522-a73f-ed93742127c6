<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="lizhexuan (generated)" id="1669795774004-54">
        <createTable tableName="ih_doctor_stop_diagnosis">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_doctor_stop_diagnosisPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="date_time" type="datetime"/>
            <column name="time_zone" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="creator" type="BIGINT"/>
            <column name="doctor_visit_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1669795774004-55">
        <createTable tableName="ih_doctor_visit">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_doctor_visitPK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="begin_date" type="datetime"/>
            <column name="dept" type="VARCHAR(255)"/>
            <column name="end_date" type="datetime"/>
            <column name="creator" type="BIGINT"/>
            <column name="medical_worker_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="offline_hospital_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1669795774004-56">
        <createTable tableName="ih_doctor_visit_time">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ih_doctor_visit_timePK"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="updated_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="day" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="timeZone" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="doctor_visit_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1669795774004-57">
        <addForeignKeyConstraint baseColumnNames="creator" baseTableName="ih_doctor_stop_diagnosis" constraintName="FK_DOCTOR_STOP_DIAGNOSIS_CREATOR_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_users" validate="true"/>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1669795774004-58">
        <addForeignKeyConstraint baseColumnNames="doctor_visit_id" baseTableName="ih_doctor_stop_diagnosis" constraintName="FK_DOCTOR_STOP_DIAGNOSIS_DOCTOR_VISIT_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_doctor_visit" validate="true"/>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1669795774004-59">
        <addForeignKeyConstraint baseColumnNames="creator" baseTableName="ih_doctor_visit" constraintName="FK_DOCTOR_VISIT_CREATOR_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_users" validate="true"/>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1669795774004-60">
        <addForeignKeyConstraint baseColumnNames="medical_worker_id" baseTableName="ih_doctor_visit" constraintName="FK_DOCTOR_VISIT_MEDICAL_WORKER_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_medical_workers" validate="true"/>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1669795774004-61">
        <addForeignKeyConstraint baseColumnNames="offline_hospital_id" baseTableName="ih_doctor_visit" constraintName="FK_DOCTOR_VISIT_OFFLINE_HOSPITAL_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_offline_hospitals" validate="true"/>
    </changeSet>
    <changeSet author="lizhexuan (generated)" id="1669795774004-62">
        <addForeignKeyConstraint baseColumnNames="doctor_visit_id" baseTableName="ih_doctor_visit_time" constraintName="FK_DOCTOR_VISIT_TIME_DOCTOR_VISIT_ID" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="ih_doctor_visit" validate="true"/>
    </changeSet>
</databaseChangeLog>
