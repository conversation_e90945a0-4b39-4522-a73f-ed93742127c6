<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="xuechun (generated)" id="1703141412188-169">
        <addColumn tableName="ih_his_charge_records">
            <column name="transaction_id" remarks="支付平台流水号" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1703141412188-176">
        <createIndex indexName="IDX_HIS_CHARGE_RECORDS_TRANSACTION_ID" tableName="ih_his_charge_records">
            <column name="transaction_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1703646354200-206">
        <dropTable tableName="ih_sessionkey_temporary"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1703675104861-178">
        <addColumn tableName="ih_wechat_orders">
            <column name="app_id" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1703675104861-179">
        <addColumn tableName="ih_wechat_orders">
            <column name="hospital_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1703678379613-205">
        <dropColumn columnName="order_id" tableName="ih_wechat_orders"/>
    </changeSet>
    <changeSet author="xuechun (generated)" id="1703646354200-207">
        <modifyDataType tableName="ih_users" columnName="password_hash" newDataType="VARCHAR(255)"/>
    </changeSet>
</databaseChangeLog>
