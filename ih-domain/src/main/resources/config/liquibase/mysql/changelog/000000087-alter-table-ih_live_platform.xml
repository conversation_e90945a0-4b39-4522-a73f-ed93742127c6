<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="xwj (generated)" id="1670469425977-55">
        <addUniqueConstraint columnNames="live_platform_type, hospital_id" constraintName="U_LIVE_PLATFORM_TYPE_HOSPITAL" tableName="ih_live_platform_hospital_rels"/>
    </changeSet>
    <changeSet author="xwj (generated)" id="1670469425977-56">
        <dropColumn tableName="ih_live_channel">
            <column name="live_status" />
        </dropColumn>
    </changeSet>
    <changeSet author="xwj (generated)" id="1670469425977-57">
        <addColumn tableName="ih_live_channel">
            <column name="live_status" type="VARCHAR(255)" />
        </addColumn>
    </changeSet>
    <changeSet author="xwj (generated)" id="1670469425977-59">
        <addUniqueConstraint tableName="ih_live_channel" columnNames="live_channel_id" constraintName="U_LIVE_CHANNEL_ID"/>
    </changeSet>
</databaseChangeLog>
