package cn.taihealth.ih.domain;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.PaymentStatus;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

/**
 * 流调筛选
 */
@Entity(name = SelfBilling.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class SelfBilling extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_self_billing";

    @Comment("医院")
    @JoinColumn(name = "hospital_id", nullable = false, foreignKey = @ForeignKey(name = "FK_SELF_BILLING_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Comment("就诊卡")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "medic_card_id", nullable = false, foreignKey = @ForeignKey(name = "FK_SELF_BILLING_USER_ID"))
    private ElectronicMedicCard medicCard;

    /**
     * 服务项目
     */
    @Comment("服务项目")
    @Column(name = "service_item")
    private String serviceItem;

    /**
     * 执行科室
     */
    @Comment("执行科室")
    @Column(name = "execute_dept")
    private String executeDept;

    /**
     * 检查金额
     */
    @Comment("检查金额")
    @Column(name = "total_amount")
    private int totalAmount;

    /**
     * 实缴金额
     */
    @Comment("实缴金额")
    @Column(name = "paid_amount")
    private int paidAmount;

    /**
     * 检查项目
     */
    @Comment("检查项目")
    @Column(name = "check_item")
    private String checkItem;

    /**
     * 检查明细
     */
    @Comment("检查明细")
    @Column(name = "check_detail")
    private String checkDetail;

    /**
     * 支付状态
     */
    @Comment("支付状态")
    @Column(name = "payment_status")
    @Enumerated(EnumType.STRING)
    private PaymentStatus paymentStatus = PaymentStatus.UNPAID;

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public ElectronicMedicCard getMedicCard() {
        return medicCard;
    }

    public void setMedicCard(ElectronicMedicCard medicCard) {
        this.medicCard = medicCard;
    }

    public String getServiceItem() {
        return serviceItem;
    }

    public void setServiceItem(String serviceItem) {
        this.serviceItem = serviceItem;
    }

    public String getExecuteDept() {
        return executeDept;
    }

    public void setExecuteDept(String executeDept) {
        this.executeDept = executeDept;
    }

    public int getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(int totalAmount) {
        this.totalAmount = totalAmount;
    }

    public int getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(int paidAmount) {
        this.paidAmount = paidAmount;
    }

    public String getCheckItem() {
        return checkItem;
    }

    public void setCheckItem(String checkItem) {
        this.checkItem = checkItem;
    }

    public String getCheckDetail() {
        return checkDetail;
    }

    public void setCheckDetail(String checkDetail) {
        this.checkDetail = checkDetail;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }
}
