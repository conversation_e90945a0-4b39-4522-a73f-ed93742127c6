package cn.taihealth.ih.domain.crm;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.CrmStatus;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;

/**
 * 随访任务详情
 */
@Entity
@Table(name = CrmTaskDetail.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class CrmTaskDetail extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_crm_task_details";

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id",
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAILS_HOSPITAL_ID"))
    private Hospital hospital;

    @Comment("就诊人")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id",
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAILS_PATIENT_ID"))
    private Patient patient;

    @Comment("随诊计划")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plan_id",
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAILS_PLAN_ID"))
    private CrmPlan plan;

    @Comment("随诊问卷")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "questionnaire_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAILS_QUESTIONNAIRE_ID"))
    private CrmQuestionnaire questionnaire;

    @Comment("随诊计划详情")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plan_detail_id",
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAILS_PLAN_DETAIL_ID"))
    private CrmPlanDetail planDetail;

    @Comment("随访任务")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id",
    foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAILS_TASK_ID"))
    private CrmTask task;

    @Comment("任务状态 STARTED(进行中)-重新约定/暂存-黑色, FINISHED(已结束)-完成随访-绿色, TERMINATED(已终止)-终止随访-红色;")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    @ColumnDefault("UNKNOWN")
    private CrmStatus status = CrmStatus.UNKNOWN;

    @Comment("上一个状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "previous_status")
    @ColumnDefault("UNKNOWN")
    private CrmStatus previousStatus = CrmStatus.UNKNOWN;

    @Comment("计划日期")
    @Column(name = "plan_time")
    private Date planTime;

    @Comment("开始时间")
    @Column(name = "start_time")
    private Date startTime;

    @Comment("结束时间")
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 随访人, 由A打电话给B进行随访, 这个值就是A
     */
    @Comment("随访人, 由A打电话给B进行随访, 这个值就是A")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "follow_up_user_id",
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAILS_FOLLOW_UP_USER_ID"))
    private User followUpUser;

    /**
     * 顺序
     */
    @Comment("顺序")
    @Column(name = "detail_order")
    private int detailOrder;

    @Comment("是否已回答问题")
    @Column(name = "is_answered")
    private boolean answered;

    @Comment("存档文件")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "file_id",
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAILS_FILE_ID"))
    private Upload file;

    @Comment("暂存数据")
    @Column(name = "temp_data_json")
    private String tempDataJson;

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public CrmPlan getPlan() {
        return plan;
    }

    public void setPlan(CrmPlan plan) {
        this.plan = plan;
    }

    public CrmQuestionnaire getQuestionnaire() {
        return questionnaire;
    }

    public void setQuestionnaire(CrmQuestionnaire questionnaire) {
        this.questionnaire = questionnaire;
    }

    public CrmPlanDetail getPlanDetail() {
        return planDetail;
    }

    public void setPlanDetail(CrmPlanDetail planDetail) {
        this.planDetail = planDetail;
    }

    public CrmStatus getStatus() {
        return status;
    }

    public void setStatus(CrmStatus status) {
        this.status = status;
    }

    public CrmStatus getPreviousStatus() {
        return previousStatus;
    }

    public void setPreviousStatus(CrmStatus previousStatus) {
        this.previousStatus = previousStatus;
    }

    public Date getPlanTime() {
        return planTime;
    }

    public void setPlanTime(Date planTime) {
        this.planTime = planTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public User getFollowUpUser() {
        return followUpUser;
    }

    public void setFollowUpUser(User followUpUser) {
        this.followUpUser = followUpUser;
    }

    public int getDetailOrder() {
        return detailOrder;
    }

    public void setDetailOrder(int detailOrder) {
        this.detailOrder = detailOrder;
    }

    public boolean isAnswered() {
        return answered;
    }

    public void setAnswered(boolean answered) {
        this.answered = answered;
    }

    public Upload getFile() {
        return file;
    }

    public void setFile(Upload file) {
        this.file = file;
    }

    public CrmTask getTask() {
        return task;
    }

    public void setTask(CrmTask task) {
        this.task = task;
    }

    public String getTempDataJson() {
        return tempDataJson;
    }

    public void setTempDataJson(String tempDataJson) {
        this.tempDataJson = tempDataJson;
    }
}
