package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.BillChannelEnum;
import cn.taihealth.ih.domain.enums.BillOperateTypeEnum;
import cn.taihealth.ih.domain.enums.BillPayStatusEnum;
import cn.taihealth.ih.domain.enums.BillRefundTypeEnum;
import cn.taihealth.ih.domain.enums.BillSourceEnum;
import cn.taihealth.ih.domain.enums.OnlineType;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.enums.ReconciliationResultEnum;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;

/**
 * 对账单-2024-04新的对账表
 * <AUTHOR>
 */
@Entity
@Table(name = BillNew.TABLE_NAME, indexes = {
        @Index(name = "IDX_BILL_NEW_SERVICE_TYPE", columnList = "bill_service_type"),
        @Index(name = "IDX_BILL_NEW_SOURCE", columnList = "bill_source"),
        @Index(name = "IDX_BILL_NEW_PAY_STATUS", columnList = "bill_pay_status"),
        @Index(name = "IDX_BILL_NEW_CHANNEL", columnList = "bill_channel"),
        @Index(name = "IDX_BILL_NEW_OPERATE_TYPE", columnList = "bill_operate_type"),

})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class BillNew extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_bills_new";

    @JoinColumn(name = "hospital_id", nullable = false, foreignKey = @ForeignKey(name = "FK_BILLS_NEW_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    /**
     * 科室id
     */
    @Column(name = "dept_id")
    @org.hibernate.annotations.Comment(value = "科室id")
    private Long deptId;

    /**
     * 科室code
     */
    @Column(name = "dept_code")
    @org.hibernate.annotations.Comment(value = "科室code")
    private String deptCode;

    /**
     * 科室名称
     */
    @Column(name = "dept_name")
    @org.hibernate.annotations.Comment(value = "科室名称")
    private String deptName;

    /**
     * 线上/线下科室
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "dept_online_type")
    @org.hibernate.annotations.Comment(value = "线上/线下科室")
    private OnlineType deptOnlineType;


    // 患者id
    @Column(name = "patient_id")
    private Long patientId;

    // 患者姓名
    @Column(name = "patient_name", nullable = false)
    private String patientName;

    // 患者手机号
    @Column(name = "patient_mobile")
    private String patientMobile;


    // ----- 订单相关start
    // 订单编号--product_id
    @Column(name = "order_no")
    private String orderNo;

    // 商户订单号--out_trade_no
    @Column(name = "merchant_order_number")
    private String merchantOrderNumber;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "pay_order_id")
    @org.hibernate.annotations.Comment(value = "医保支付订单号")
    private String payOrderId;


// ----- 订单相关end


    // ----- 院内金额相关start
    // 支付金额 分
    @Column(name = "payment_amount")
    private Integer paymentAmount;

    @Column(name = "self_payment_amount")
    @org.hibernate.annotations.Comment("个人支付金额")
    private Integer selfPaymentAmount;

    @Column(name = "medicare_payment_amount")
    @org.hibernate.annotations.Comment("医保支付金额")
    private Integer medicarePaymentAmount;

    // 退款金额 分
    @Column(name = "refund_amount")
    private Integer refundAmount;

    @Column(name = "self_refund_amount")
    @org.hibernate.annotations.Comment("个人退款金额")
    private Integer selfRefundAmount;

    @Column(name = "medicare_refund_amount")
    @org.hibernate.annotations.Comment("医保退款金额")
    private Integer medicareRefundAmount;
    // ----- 院内金额相关end

    @org.hibernate.annotations.Comment("已退款金额")
    @Column(name = "refunded_amount")
    private Integer refundedAmount;

    @Column(name = "self_refunded_amount")
    @org.hibernate.annotations.Comment("个人已退款金额")
    private Integer selfRefundedAmount;

    @Column(name = "medicare_refunded_amount")
    @org.hibernate.annotations.Comment("医保已退款金额")
    private Integer medicareRefundedAmount;

    // ----- 枚举相关start
    // 服务类型
    @Column(name = "bill_service_type")
    private ProjectTypeEnum billServiceType;

    // 支付来源
    @Column(name = "bill_source")
    private BillSourceEnum billSource;

    // 支付状态
    @Column(name = "bill_pay_status")
    private BillPayStatusEnum billPayStatus;

    // 支付渠道
    @Column(name = "bill_channel")
    private BillChannelEnum billChannel;

    // 交易操作类型
    @Column(name = "bill_operate_type")
    private BillOperateTypeEnum billOperateType;
    // ----- 枚举相关end


    // ----- 时间相关start
    // 下单时间
    @Column(name = "order_time")
    private Date orderTime;

    // 支付时间
    @Column(name = "payment_time")
    private Date paymentTime;

    // 发起退款时间
    @Column(name = "refund_initiation_time")
    private Date refundInitiationTime;

    // 交易操作时间
    @Column(name = "order_operate_time")
    private Date orderOperateTime;
    // ----- 时间相关end


    // ----- 对账相关start
    // 对账结果 (true:存疑)
    @Column(name = "reconciliation_doubt")
    private Boolean reconciliationDoubt;

    @org.hibernate.annotations.Comment("对账结果")
    @Column(name = "reconciliation_result")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("UNKNOWN")
    private ReconciliationResultEnum reconciliationResult = ReconciliationResultEnum.UNKNOWN;

    // 处理结果 (是否处理)
    @Column(name = "solve_flag")
    private Boolean solveFlag;
    // ----- 对账相关end

    // ----- his相关start
    // 是否医保支付
    @Column(name = "insurance_flag")
    private Boolean insuranceFlag;

    // his金额 分 暂时没有用
    @Column(name = "his_amount")
    @org.hibernate.annotations.Comment("医保his总金额")
    private Integer hisAmount;

    @Column(name = "his_self_amount")
    @org.hibernate.annotations.Comment("医保his自付部分")
    private Integer hisSelfAmount;

    @Column(name = "his_medicare_amount")
    @org.hibernate.annotations.Comment("医保his医保部分")
    private Integer hisMedicareAmount;
    // ----- his相关end

    @Column(name = "remark")
    @org.hibernate.annotations.Comment("备注")
    private String remark;

    @Column(name = "refund_type")
    @Enumerated(EnumType.STRING)
    @org.hibernate.annotations.Comment("退款类型")
    private BillRefundTypeEnum refundType;

    @Column(name = "settle_id")
    @org.hibernate.annotations.Comment("HIS收据号, 收款时传入收款收据号，退款状态传退款收据号")
    private String settleId;

    /**
     * 商户退款单号
     */
    @Column(name = "out_refund_no")
    @org.hibernate.annotations.Comment("商户退款单号")
    private String outRefundNo;


    public String getSettleId() {
        return settleId;
    }

    public void setSettleId(String settleId) {
        this.settleId = settleId;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BillRefundTypeEnum getRefundType() {
        return refundType;
    }

    public void setRefundType(BillRefundTypeEnum refundType) {
        this.refundType = refundType;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientMobile() {
        return patientMobile;
    }

    public void setPatientMobile(String patientMobile) {
        this.patientMobile = patientMobile;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getMerchantOrderNumber() {
        return merchantOrderNumber;
    }

    public void setMerchantOrderNumber(String merchantOrderNumber) {
        this.merchantOrderNumber = merchantOrderNumber;
    }

    public Integer getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Integer paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public Integer getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Integer refundAmount) {
        this.refundAmount = refundAmount;
    }

    public ProjectTypeEnum getBillServiceType() {
        return billServiceType;
    }

    public void setBillServiceType(ProjectTypeEnum billServiceType) {
        this.billServiceType = billServiceType;
    }

    public BillSourceEnum getBillSource() {
        return billSource;
    }

    public void setBillSource(BillSourceEnum billSource) {
        this.billSource = billSource;
    }

    public BillPayStatusEnum getBillPayStatus() {
        return billPayStatus;
    }

    public void setBillPayStatus(BillPayStatusEnum billPayStatus) {
        this.billPayStatus = billPayStatus;
    }

    public BillChannelEnum getBillChannel() {
        return billChannel;
    }

    public void setBillChannel(BillChannelEnum billChannel) {
        this.billChannel = billChannel;
    }

    public BillOperateTypeEnum getBillOperateType() {
        return billOperateType;
    }

    public void setBillOperateType(BillOperateTypeEnum billOperateType) {
        this.billOperateType = billOperateType;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(Date paymentTime) {
        this.paymentTime = paymentTime;
    }

    public Date getRefundInitiationTime() {
        return refundInitiationTime;
    }

    public void setRefundInitiationTime(Date refundInitiationTime) {
        this.refundInitiationTime = refundInitiationTime;
    }

    public Date getOrderOperateTime() {
        return orderOperateTime;
    }

    public void setOrderOperateTime(Date orderOperateTime) {
        this.orderOperateTime = orderOperateTime;
    }

    public Boolean getReconciliationDoubt() {
        return reconciliationDoubt;
    }

    public void setReconciliationDoubt(Boolean reconciliationDoubt) {
        this.reconciliationDoubt = reconciliationDoubt;
    }

    public Boolean getSolveFlag() {
        return solveFlag;
    }

    public void setSolveFlag(Boolean solveFlag) {
        this.solveFlag = solveFlag;
    }

    public Boolean getInsuranceFlag() {
        return insuranceFlag;
    }

    public void setInsuranceFlag(Boolean insuranceFlag) {
        this.insuranceFlag = insuranceFlag;
    }

    public Integer getHisAmount() {
        return hisAmount;
    }

    public void setHisAmount(Integer hisAmount) {
        this.hisAmount = hisAmount;
    }

    public Integer getRefundedAmount() {
        return refundedAmount;
    }

    public void setRefundedAmount(Integer refundedAmount) {
        this.refundedAmount = refundedAmount;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public OnlineType getDeptOnlineType() {
        return deptOnlineType;
    }

    public void setDeptOnlineType(OnlineType deptOnlineType) {
        this.deptOnlineType = deptOnlineType;
    }

    public ReconciliationResultEnum getReconciliationResult() {
        return reconciliationResult;
    }

    public void setReconciliationResult(ReconciliationResultEnum reconciliationResult) {
        this.reconciliationResult = reconciliationResult;
    }

    public String getPayOrderId() {
        return payOrderId;
    }

    public void setPayOrderId(String payOrderId) {
        this.payOrderId = payOrderId;
    }

    public Integer getSelfPaymentAmount() {
        return selfPaymentAmount;
    }

    public void setSelfPaymentAmount(Integer selfPaymentAmount) {
        this.selfPaymentAmount = selfPaymentAmount;
    }

    public Integer getMedicarePaymentAmount() {
        return medicarePaymentAmount;
    }

    public void setMedicarePaymentAmount(Integer medicarePaymentAmount) {
        this.medicarePaymentAmount = medicarePaymentAmount;
    }

    public Integer getSelfRefundAmount() {
        return selfRefundAmount;
    }

    public void setSelfRefundAmount(Integer selfRefundAmount) {
        this.selfRefundAmount = selfRefundAmount;
    }

    public Integer getMedicareRefundAmount() {
        return medicareRefundAmount;
    }

    public void setMedicareRefundAmount(Integer medicareRefundAmount) {
        this.medicareRefundAmount = medicareRefundAmount;
    }

    public Integer getHisSelfAmount() {
        return hisSelfAmount;
    }

    public void setHisSelfAmount(Integer hisSelfAmount) {
        this.hisSelfAmount = hisSelfAmount;
    }

    public Integer getHisMedicareAmount() {
        return hisMedicareAmount;
    }

    public void setHisMedicareAmount(Integer hisMedicareAmount) {
        this.hisMedicareAmount = hisMedicareAmount;
    }

    public Integer getSelfRefundedAmount() {
        return selfRefundedAmount;
    }

    public void setSelfRefundedAmount(Integer selfRefundedAmount) {
        this.selfRefundedAmount = selfRefundedAmount;
    }

    public Integer getMedicareRefundedAmount() {
        return medicareRefundedAmount;
    }

    public void setMedicareRefundedAmount(Integer medicareRefundedAmount) {
        this.medicareRefundedAmount = medicareRefundedAmount;
    }
}
