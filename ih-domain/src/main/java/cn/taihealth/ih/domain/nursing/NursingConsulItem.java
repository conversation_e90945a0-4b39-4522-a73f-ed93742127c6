package cn.taihealth.ih.domain.nursing;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

@Entity
@Table(name = NursingConsulItem.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@org.hibernate.annotations.Table(appliesTo = NursingConsulItem.TABLE_NAME, comment = "护理咨询项目")
public class NursingConsulItem extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_nursing_consul_items";

    @Comment("医院")
    @JoinColumn(name = "hospital_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_NURSING_CONSUL_ITEMS_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Column(name = "name")
    @Comment("护理咨询项目名称")
    private String name;

    @Column(name = "order_no")
    @Comment("排序序号")
    @ColumnDefault("0")
    private int orderNo;

    @Comment("护理咨询项目图标")
    @JoinColumn(name = "icon_id",
            foreignKey = @ForeignKey(name = "FK_NURSING_CONSUL_ITEMS_ICON_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Upload icon;

    @Column(name = "is_enabled")
    @Comment("是否可用")
    @ColumnDefault("1")
    private boolean enabled = true;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public int getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(int orderNo) {
        this.orderNo = orderNo;
    }

    public Upload getIcon() {
        return icon;
    }

    public void setIcon(Upload icon) {
        this.icon = icon;
    }
}
