package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.BillBalanceTypeEnum;
import cn.taihealth.ih.domain.enums.BillChannelEnum;
import cn.taihealth.ih.domain.enums.BillOperateTypeEnum;
import cn.taihealth.ih.domain.enums.BillPayStatusEnum;
import cn.taihealth.ih.domain.enums.BillRefundTypeEnum;
import cn.taihealth.ih.domain.enums.BillSourceEnum;
import cn.taihealth.ih.domain.enums.OnlineType;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.enums.ReconciliationResultEnum;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;

/**
 * 对账单平账记录
 */
@Entity
@Table(name = BillBalanceRecord.TABLE_NAME, indexes = {
        @Index(name = "IDX_BILL_BALANCE_RECORDS_TRANSACTION_ID", columnList = "transaction_id"),
        @Index(name = "IDX_BILL_BALANCE_RECORDS_BALANCE_TYPE", columnList = "balance_type"),

})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class BillBalanceRecord extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_bill_balance_records";


    @Column(name = "transaction_id", nullable = false)
    @org.hibernate.annotations.Comment(value = "交易ID")
    private String transactionId;

    @Column(name = "out_refund_no")
    @org.hibernate.annotations.Comment(value = "退款商户号")
    private String outRefundNo;

    @org.hibernate.annotations.Comment("对账结果")
    @Column(name = "balance_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private BillBalanceTypeEnum balanceType;

    @Column(name = "remark")
    @org.hibernate.annotations.Comment(value = "备注")
    private String remark;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BillBalanceTypeEnum getBalanceType() {
        return balanceType;
    }

    public void setBalanceType(BillBalanceTypeEnum balanceType) {
        this.balanceType = balanceType;
    }
}
