package cn.taihealth.ih.domain.ai;

import cn.taihealth.ih.domain.UpdatableEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 风险报告
 */
@Entity
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Table(name = DifyAppConfig.TABLE_NAME)
public class DifyAppConfig extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_dify_app_config";

    @Comment("医院")
    @Column(name = "hospital_id")
    private Long hospitalId;

    @Comment("dify应用id")
    @Column(name = "app_id")
    private String appId;

    @Comment("dify api key")
    @Column(name = "api_key")
    private String apiKey;

    @Comment("dify应用名称")
    @Column(name = "app_name")
    private String appName;

    @Comment("是否启用")
    @Column(name = "enabled")
    @ColumnDefault("1")
    private boolean enabled = true;

    @Comment("应用描述")
    @Column(name = "description")
    private String description;

    public Long getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
