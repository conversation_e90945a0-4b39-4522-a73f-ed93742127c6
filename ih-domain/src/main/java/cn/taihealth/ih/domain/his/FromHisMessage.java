package cn.taihealth.ih.domain.his;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.io.Serializable;
import javax.persistence.*;

@Entity
@Table(name = FromHisMessage.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class FromHisMessage  extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_from_his_message";

    /**
     * 身份证号
     */
    @Column(name = "certificate_no", columnDefinition = "VARCHAR(255) COMMENT '身份证号'")
    private String certificateNo;

    /**
     * 患者姓名
     */
    @Column(name = "patient_name", columnDefinition = "VARCHAR(255) COMMENT '患者姓名'")
    private String patientName;

    /**
     * 患者唯一号
     */
    @Column(name = "patient_id", columnDefinition = "VARCHAR(255) COMMENT '患者唯一号'")
    private String patientId;

    /**
     * 卡号
     */
    @Column(name = "card_no", columnDefinition = "VARCHAR(255) COMMENT '卡号'")
    private String cardNo;

    /**
     * 医院代码
     */
    @Column(name = "organ_code", columnDefinition = "VARCHAR(255) COMMENT '医院代码'")
    private String organCode;

    /**
     * 医院名称
     */
    @Column(name = "organ_name", columnDefinition = "VARCHAR(255) COMMENT '医院名称'")
    private String organName;

    /**
     * 就诊流水号
     */
    @Column(name = "regno", columnDefinition = "VARCHAR(255) COMMENT '就诊流水号'")
    private String regno;

    /**
     * 就诊来源 1门诊 2住院
     */
    @Column(name = "user_source", columnDefinition = "VARCHAR(255) COMMENT '就诊来源 1门诊 2住院'")
    private String userSource;

    /**
     * 消息类型
     */
    @Column(name = "msg_type", columnDefinition = "VARCHAR(255) COMMENT '消息类型'")
    private String msgType;

    /**
     * 业务流水号
     */
    @Column(name = "operation_sn", columnDefinition = "VARCHAR(255) COMMENT '业务流水号'")
    private String operationSn;

    @Column(name = "msg_details", columnDefinition = "LONGTEXT COMMENT '消息详情'")
    private String msgDetails;

    @Column(name = "request_id", columnDefinition = "VARCHAR(255) COMMENT '请求ID'")
    private String requestId;

    @Column(name = "timestamp", columnDefinition = "VARCHAR(255) COMMENT '时间戳'")
    private String timestamp;

    @Column(name = "request_path", columnDefinition = "VARCHAR(255) COMMENT '请求路径'")
    private String requestPath;

    @Column(name = "return_message", columnDefinition = "LONGTEXT COMMENT 'ih返回的参数'")
    private String returnMessage;

    @Column(name = "is_success")
    private Boolean success;

    @JoinColumn(name = "hospital_id",
            foreignKey = @ForeignKey(name = "FK_FROM_HIS_MESSAGE_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public String getRegno() {
        return regno;
    }

    public void setRegno(String regno) {
        this.regno = regno;
    }

    public String getUserSource() {
        return userSource;
    }

    public void setUserSource(String userSource) {
        this.userSource = userSource;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getOperationSn() {
        return operationSn;
    }

    public void setOperationSn(String operationSn) {
        this.operationSn = operationSn;
    }

    public String getMsgDetails() {
        return msgDetails;
    }

    public void setMsgDetails(String msgDetails) {
        this.msgDetails = msgDetails;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getRequestPath() {
        return requestPath;
    }

    public void setRequestPath(String requestPath) {
        this.requestPath = requestPath;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}