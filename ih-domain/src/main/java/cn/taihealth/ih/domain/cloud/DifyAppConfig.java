package cn.taihealth.ih.domain.cloud;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.ZonedDateTime;

@Entity
@Table(name = "dify_app_config")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DifyAppConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 医院ID
     */
    @Column(name = "hospital_id")
    private Long hospitalId;

    /**
     * 应用类型
     */
    @Column(name = "app_type", nullable = false)
    private String appType;

    /**
     * 应用ID
     */
    @Column(name = "app_id", nullable = false)
    private String appId;

    /**
     * API密钥
     */
    @Column(name = "api_key", nullable = false)
    private String apiKey;

    /**
     * Dify服务URL
     */
    @Column(name = "dify_url", nullable = false)
    private String difyUrl;

    /**
     * 应用名称
     */
    @Column(name = "app_name")
    private String appName;

    /**
     * 应用图标URL
     */
    @Column(name = "app_icon_url")
    private String appIconUrl;

    /**
     * 是否启用
     */
    @Column(name = "enabled")
    private Boolean enabled;

    /**
     * 创建时间
     */
    @Column(name = "created_date")
    private ZonedDateTime createdDate;

    /**
     * 最后修改时间
     */
    @Column(name = "last_modified_date")
    private ZonedDateTime lastModifiedDate;
} 