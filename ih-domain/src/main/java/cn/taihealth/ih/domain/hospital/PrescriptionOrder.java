package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.ChannelType;
import com.google.common.collect.Lists;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 处方表关联订单表
 * <AUTHOR>
 */
@Entity
@Table(name = PrescriptionOrder.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class PrescriptionOrder extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_prescription_orders";

    public enum Status {
        UNREVIEWED("未审核", "0"),
        REJECTED("已拒绝", "2"),
        REVIEWING("审核中", "3"),
        UNSENT("未发送", "4"),
        SENT("已发送", "5"),
        USED("已使用", "6"),
        REVIEN("已审核", "1"),

        ;

        private final String name;
        private final String code;

        Status(String name, String code) {
            this.name = name;
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public String getCode() {
            return code;
        }
    }

    public enum DrugOrderStatus {
        CANCELED("已作废"),
        VALID("有效"),
        USED("已使用");

        /**
         * 已使用: 处方对应的药品订单被支付了 已作废:处方对应的药品订单未支付的情况下到期，药品订单被退款 有效:处方对应的药品订单未支付且处方未到期
         */

        private final String name;

        DrugOrderStatus(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    public enum PrescriptionType {
        // 1:西药处方,2:中药处方,3:草药处方,4:治疗处方,5:检查处方,6体检处方,7:自动挂号产生挂号收费信息，8:检验处方
        UNIVERSAL("通用", "-99"),
        WESTERN_MEDICINE("西药处方", "1"),
        HERBAL_MEDICINE("草药处方", "3"),
        PATENT_MEDICINE("中药处方", "2"),

        TREATMENT("治疗处方", "4"),
        DIAGNOSTIC("检查处方", "5"),
        MEDICAL_EXAMINATION("体检处方", "6"),
        AUTOMATIC_REGISTRATION("自动挂号产生挂号收费信息", "7"),
        LAB_TEST("检验处方", "8");


        private final String name;
        private final String code;

        // 根据code获取处方类型
        public static PrescriptionType getPrescriptionTypeByCode(String code) {
            for (PrescriptionType prescriptionType : PrescriptionType.values()) {
                if (prescriptionType.getCode().equals(code)) {
                    return prescriptionType;
                }
            }
            return UNIVERSAL;
        }

        PrescriptionType(String name, String code) {
            this.name = name;
            this.code = code;
        }
        public String getName() {
            return name;
        }

        public String getCode() {
            return code;
        }

    }

    @Comment("医院ID")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_PRESCRIPTION_ORDERS_HOSPITAL_ID"))
    private Hospital hospital;

    /* 急诊订单表关联 */
    @Comment("急诊订单表关联")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_MIDDLE_ORDER_ID"))
    private Order order;

    /* 患者表关联 */
    @Comment("患者表关联")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id", foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_MIDDLE_PATIENT_ID"))
    private Patient patient;

    /*医生*/
    @Comment("医生")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "doctor_id", nullable = false, foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_MIDDLE_DOCTOR_ID"))
    private MedicalWorker doctor;

    @Comment("用户ID")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_MIDDLE_USER_ID"))
    private User user;

    /* 状态 已审核 ，未审核 */
    @Comment("状态 已审核/未审核")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PrescriptionOrder.Status status = Status.UNREVIEWED;

    /* 审核人 */
    @Comment("审核人")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "doctor_review_id", foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_REVIEW_MIDDLE_DOCTOR_ID"))
    private MedicalWorker doctorReview;

    /* 审核时间 */
    @Comment("审核时间")
    @Column(name = "review_time")
    private Date reviewTime;

    @Comment("处方图片（原生）")
    @ManyToOne
    @JoinColumn(name = "upload_id", foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_MIDDLE_AUDIO_ID"))
    private Upload upload;

    @Comment("处方图片（ca）")
    @ManyToOne
    @JoinColumn(name = "ca_picture_id", foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_MIDDLE_CA_PICTURE_ID"))
    private Upload caPicture;

    @ManyToOne
    @JoinColumn(name = "no_ca_pdf_id", foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_MIDDLE_NO_CA_PDF_ID"))
    private Upload noCaPdf;

    @Comment("处方pdf文件")
    @ManyToOne
    @JoinColumn(name = "ca_pdf_id", foreignKey = @ForeignKey(name = "FK_PRESCRIPTIONS_MIDDLE_CA_PDF_ID"))
    private Upload caPdf;

    /* 拒绝原因 */
    @Comment("拒绝原因")
    @Column(name = "reject_reason")
    private String rejectReason;

    /* 是否需要发送消息给患者 */
    @Comment("是否需要发送消息给患者")
    @Column(name = "send_user", nullable = false)
    private boolean sendUser = false;

    /* 增加处方音类型 */
    @Comment("增加处方的类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "prescription_type")
    @ColumnDefault("UNIVERSAL")
    private PrescriptionOrder.PrescriptionType type = PrescriptionType.UNIVERSAL;

    @Comment("医生提交处方审核时间")
    @Column(name = "commited_date")
    private Date commitedDate;

    @OneToMany(mappedBy = "prescriptionOrder", fetch = FetchType.LAZY)
    private List<Prescription> prescription = Lists.newArrayList();

    //发送处方通知给用户的时间，处方过期使用
    @Comment("发送处方通知给用户的时间，处方过期使用")
    @Column(name = "send_user_date")
    private Date sendUserDate;

    /**
     * 过期时间
     */
    @Comment("过期时间")
    @Column(name = "expiration_date")
    private Date expirationDate;

    @Comment("是否无需CA")
    @Column(name = "apply_no_ca")
    private String applyNoCA;

    /**
     * 是否需要获取ca签章
     */
    @Comment("是否需要获取ca签章")
    @Column(name = "need_get_ca_sign")
    @ColumnDefault("0")
    private boolean needGetCASign;


    /* 是否逻辑删除，0删除，1未删 */
    @Comment("是否逻辑删除")
    @Column(name = "is_enabled", nullable = false)
    @ColumnDefault("1")
    private boolean enabled = true;


    /* 备注 */
    @Comment("备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 医嘱
     */
    @Comment("医嘱")
    @Column(name = "medical_order")
    @Type(type = "org.hibernate.type.TextType")
    private String medicalOrder;
    /**
     * 绑定渠道
     */
    @Comment("绑定渠道")
    @Column(name = "channel_code")
    private String channel;
    /**
     * 医院处方号
     */
    @Comment("医院处方号")
    @Column(name = "his_recipe_no")
    private String hisRecipeNo;

    @Comment("就诊渠道 线上就诊/线下就诊")
    @Column(name = "channel_type")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("ONLINE")
    private ChannelType channelType;

    @Comment("是否支付")
    @Column(name = "is_paid")
    @ColumnDefault("0")
    private boolean paid;

    @Comment("药品订单状态")
    @Column(name = "drug_order_status")
    @Enumerated(EnumType.STRING)
    private DrugOrderStatus drugOrderStatus;

    @Comment("签名原文")
    @Column(name = "sign_raw")
    @Type(type = "org.hibernate.type.TextType")
    private String signRaw;

    public DrugOrderStatus getDrugOrderStatus() {
        return drugOrderStatus;
    }

    public void setDrugOrderStatus(DrugOrderStatus drugOrderStatus) {
        this.drugOrderStatus = drugOrderStatus;
    }

    public ChannelType getChannelType() {
        return channelType;
    }

    public void setChannelType(ChannelType channelType) {
        this.channelType = channelType;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public MedicalWorker getDoctor() {
        return doctor;
    }

    public void setDoctor(MedicalWorker doctor) {
        this.doctor = doctor;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public MedicalWorker getDoctorReview() {
        return doctorReview;
    }

    public void setDoctorReview(MedicalWorker doctorReview) {
        this.doctorReview = doctorReview;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public Upload getUpload() {
        return upload;
    }

    public void setUpload(Upload upload) {
        this.upload = upload;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public boolean isSendUser() {
        return sendUser;
    }

    public void setSendUser(boolean sendUser) {
        this.sendUser = sendUser;
    }

    public PrescriptionType getType() {
        return type;
    }

    public void setType(PrescriptionType type) {
        this.type = type;
    }

    public Date getCommitedDate() {
        return commitedDate;
    }

    public void setCommitedDate(Date commitedDate) {
        this.commitedDate = commitedDate;
    }

    public List<Prescription> getPrescription() {
        return prescription;
    }

    public void setPrescription(List<Prescription> prescription) {
        this.prescription = prescription;
    }

    public Date getSendUserDate() {
        return sendUserDate;
    }

    public void setSendUserDate(Date sendUserDate) {
        this.sendUserDate = sendUserDate;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getMedicalOrder() {
        return medicalOrder;
    }

    public void setMedicalOrder(String medicalOrder) {
        this.medicalOrder = medicalOrder;
    }

    public Upload getCaPicture() {
        return caPicture;
    }

    public void setCaPicture(Upload caPicture) {
        this.caPicture = caPicture;
    }

    public Upload getNoCaPdf() {
        return noCaPdf;
    }

    public void setNoCaPdf(Upload noCaPdf) {
        this.noCaPdf = noCaPdf;
    }

    public Upload getCaPdf() {
        return caPdf;
    }

    public void setCaPdf(Upload caPdf) {
        this.caPdf = caPdf;
    }

    public boolean isNeedGetCASign() {
        return needGetCASign;
    }

    public void setNeedGetCASign(boolean needGetCASign) {
        this.needGetCASign = needGetCASign;
    }

    public String getApplyNoCA() {
        return applyNoCA;
    }

    public void setApplyNoCA(String applyNoCA) {
        this.applyNoCA = applyNoCA;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getHisRecipeNo() {
        return hisRecipeNo;
    }

    public void setHisRecipeNo(String hisRecipeNo) {
        this.hisRecipeNo = hisRecipeNo;
    }

    public boolean isPaid() {
        return paid;
    }

    public void setPaid(boolean paid) {
        this.paid = paid;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getSignRaw() {
        return signRaw;
    }

    public void setSignRaw(String signRaw) {
        this.signRaw = signRaw;
    }
}
