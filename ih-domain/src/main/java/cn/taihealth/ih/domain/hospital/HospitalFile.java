package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.AbstractEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = HospitalFile.TABLE_NAME, uniqueConstraints = {
    @UniqueConstraint(name = "U_HOSPITAL_FILES_HOSPITAL_FILE", columnNames = {"hospital_id", "file_id"})})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class HospitalFile extends AbstractEntity {

    public static final String TABLE_NAME = "ih_hospital_files";

    @Comment("医院")
    @JoinColumn(name = "hospital_id", nullable = false, foreignKey = @ForeignKey(name = "FK_HOSPITAL_FILES_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Comment("文件")
    @JoinColumn(name = "file_id", nullable = false, foreignKey = @ForeignKey(name = "FK_HOSPITAL_FILES_FILE_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Upload file;

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Upload getFile() {
        return file;
    }

    public void setFile(Upload file) {
        this.file = file;
    }
}