package cn.taihealth.ih.domain;


import cn.taihealth.ih.domain.cloud.Hospital;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

@Entity
@Table(name = UserPlatformTemporaryInfo.TABLE_NAME,
        uniqueConstraints = {
                @UniqueConstraint(name = "U_USER_PLATFORM_TEMPORARY_OPEN_ID", columnNames = {"open_id"})
        },
        indexes = {
                @Index(name = "IDX_USER_PLATFORM_TEMPORARY_UNION_ID", columnList = "union_id")
        }
)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class UserPlatformTemporaryInfo extends UpdatableEntity {

    /**
     * 用户unionid，id,openid关联表
     */
    public static final String TABLE_NAME = "ih_user_platform_temporary_info";


    @Comment("手机号")
    @Column(name = "mobile")
    private String mobile;

    @Comment("微信unionId")
    @Column(name = "union_id", nullable = false)
    private String unionId;

    @Comment("医院")
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_USER_PLATFORM_TEMPORARY_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Comment("微信openId")
    @Column(name = "open_id", nullable = false)
    private String openId;

    @Comment("微信appId")
    @Column(name = "app_id", nullable = false)
    private String appId;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }
}
