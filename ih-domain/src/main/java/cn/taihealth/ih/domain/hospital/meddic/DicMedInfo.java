package cn.taihealth.ih.domain.hospital.meddic;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.TrueFalseEnum;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;

/**
 * <p>
 * 医药信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-09
 */
@Entity
@Table(name = DicMedInfo.TABLE_NAME, uniqueConstraints = {@UniqueConstraint(name = "U_DIC_MEDINFO_CHANNEL_HOSPITAL_ID",
    columnNames = {"nmpa_number", "channel", "hospital_id"})},
    indexes = {
        @Index(name = "IDX_DOSAGEFORM", columnList = "dosage_form_id"),
        @Index(name = "IDX_DOSAGEUNITID", columnList = "dosage_unit_id"),
        @Index(name = "IDX_PACKAGEUNITID", columnList = "package_unit_id"),
        @Index(name = "IDX_GENERIC_NAME_INPUT_CODE", columnList = "generic_name_input_code"),
        @Index(name = "IDX_GENERIC_NAME_ENGLISH", columnList = "generic_name_english"),
        @Index(name = "IDX_TRADE_NAME_INPUT_CODE", columnList = "trade_name_input_code"),
        @Index(name = "IDX_TRADE_NAME_ENGLISH", columnList = "trade_name_english"),
        @Index(name = "IDX_MANUFACTURER", columnList = "manufacturer"),
        @Index(name = "IDX_NMPA_NUMBER", columnList = "nmpa_number"),
    })
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class DicMedInfo extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_dic_med_infos";

    @JoinColumn(name = "hospital_id", nullable = false, foreignKey = @ForeignKey(name = "FK_DIC_MED_INFOS_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    /**
     * 类别ID
     */
//    @Column(name = "category_id", columnDefinition = "bigint COMMENT '类别ID'" )
//    @ColumnDefault("-1")
//    private Long categoryId = -1L;

    /**
     * 剂量规格ID
     * 关联 ih_dic_med_dosage_forms
     */
    @Column(name = "dosage_form_id", columnDefinition = "bigint COMMENT '剂量规格ID 关联 ih_dic_med_dosage_forms'")
//    @ManyToOne(fetch = FetchType.LAZY)
    @ColumnDefault("-1")
//    @Column(columnDefinition = "COMMENT '剂量规格ID'")
    private Long dosageFormId = -1L;

    /**
     * 剂量单位ID
     * 关联 ih_dic_med_dosage_units
     */
    @Column(name = "dosage_unit_id", columnDefinition = "bigint COMMENT '剂量单位ID 关联 ih_dic_med_dosage_units'")
//    @ManyToOne(fetch = FetchType.LAZY)
    @ColumnDefault("-1")
//    @Column(columnDefinition = "COMMENT '剂量单位ID'")

    private Long dicDosageUnitId = -1L;

//    /**
//     * 医药渠道ID
//     */
//    @Column(name = "med_channel_id", columnDefinition = "bigint COMMENT '医药渠道ID'" )
////    @ManyToOne(fetch = FetchType.LAZY)
//    @ColumnDefault("-1")
////    @Column(columnDefinition = "COMMENT '医药渠道ID'")
//    private Long dicMedChannelId = -1L;

    /**
     * 包装单位ID
     * 关联 ih_dic_med_package_units
     */
    @Column(name = "package_unit_id", columnDefinition = "bigint COMMENT '包装单位ID 关联 ih_dic_med_package_units'")
//    @ManyToOne(fetch = FetchType.LAZY)
    @ColumnDefault("-1")
//    @Column(columnDefinition = "COMMENT '包装单位ID'")
    private Long dicPackageUnitId = -1L;


    /**
     * 代码
     */
    @Column(name = "code", columnDefinition = "CHAR(100) COMMENT '代码'", nullable = false)
    @ColumnDefault("-1")
    private String code = "-1";

    /**
     * 通用名
     */
    @Column(name = "generic_name", columnDefinition = "CHAR(100) COMMENT '通用名'", nullable = false)
    @ColumnDefault("-1")
    private String genericName = "-1";

    /**
     * 通用名首拼
     */
    @Column(name = "generic_name_input_code", columnDefinition = "CHAR(100) COMMENT '通用名首拼'")
    private String genericNameInputCode;

    /**
     * 通用名英文
     */
    @Column(name = "generic_name_english", columnDefinition = "CHAR(100) COMMENT '通用名英文'")
    private String genericNameEnglish;

    /**
     * 商品名
     */
    @Column(name = "trade_name", columnDefinition = "CHAR(100) COMMENT '商品名'")
    private String tradeName = "";

    /**
     * 商品名首拼
     */
    @Column(name = "trade_name_input_code", columnDefinition = "CHAR(100)  COMMENT '商品名首拼'", nullable = false)
    @ColumnDefault("-1")
    private String tradeNameInputCode = "-1";

    /**
     * 商品名英文
     */
    @Column(name = "trade_name_english", columnDefinition = "CHAR(100) COMMENT '商品名英文'", nullable = false)
    @ColumnDefault("-1")
    private String tradeNameEnglish = "-1";

    /**
     * 批准文号
     */
    @Column(name = "nmpa_number", columnDefinition = "CHAR(100) COMMENT '批准文号'", nullable = false)
    @ColumnDefault("-1")

    private String nmpaNumber = "-1";

    /**
     * 剂量规格
     */
    @Column(name = "dosage_spec", columnDefinition = "CHAR(100) COMMENT '剂量规格'", nullable = false)
    @ColumnDefault("-1")

    private String dosageSpec = "-1";

    /**
     * 包装规格
     */
    @Column(name = "packaging_spec", columnDefinition = "CHAR(100) COMMENT '包装规格'")
    private String packagingSpec;

    /**
     * 生产厂家
     */
    @Column(name = "manufacturer", columnDefinition = "CHAR(100) COMMENT '生产厂家'", nullable = false)
    @ColumnDefault("-1")

    private String manufacturer = "-1";

    /**
     * 是否抗菌药物
     */
    @Enumerated
    @Column(name = "is_antimicrobial", columnDefinition = "smallint COMMENT '是否抗菌药物'", nullable = false)
    @ColumnDefault("-1")

    private TrueFalseEnum isAntimicrobial = TrueFalseEnum.OTHER;

    /**
     * 抗菌药物分级
     */
    @Column(name = "antimicrobial_grade", columnDefinition = "CHAR(30) COMMENT '抗菌药物分级'", nullable = false)
    @ColumnDefault("-1")
    private String antimicrobialGrade = "-1";

    /**
     * 是否医保
     */
    @Enumerated
    @Column(name = "is_insurance", columnDefinition = "smallint COMMENT '是否医保'", nullable = false)

    private TrueFalseEnum isInsurance = TrueFalseEnum.OTHER;

    /**
     * 是否处方
     */
    @Enumerated
    @Column(name = "is_prescription", columnDefinition = "smallint COMMENT '是否处方'", nullable = false)

    private TrueFalseEnum isPrescription = TrueFalseEnum.OTHER;


    /**
     * 是否精麻毒放
     */
    @Enumerated
    @Column(name = "is_special", columnDefinition = "smallint COMMENT '是否精麻毒放'", nullable = false)
    private TrueFalseEnum isSpecial = TrueFalseEnum.OTHER;

    /**
     * 条形码
     */
    @ColumnDefault("-1")

    @Column(name = "bar_code", columnDefinition = "CHAR(100) COMMENT '条形码'", nullable = false)
    private String barCode = "-1";

    /**
     * 零售价(单位为分)
     */
    @Column(name = "retail_price", columnDefinition = "bigint COMMENT '零售价单位为分'", nullable = false)
    private Integer retailPrice = 0;

    /**
     * 采购价(单位为分)
     */
    @Column(name = "sourcing_price", columnDefinition = "bigint COMMENT '采购价单位为分'", nullable = false)
    @ColumnDefault("-1")
    private Integer sourcingPrice = -1;

    /**
     * 销售价(单位为分)
     */
    @Column(name = "sale_price", columnDefinition = "bigint COMMENT '销售价单位为分'", nullable = false)
    @ColumnDefault("-1")
    private Integer salePrice = -1;

    /**
     * 会员价(单位为分)
     */
    @Column(name = "membership_price", columnDefinition = "bigint COMMENT '会员价单位为分'", nullable = false)
    @ColumnDefault("-1")
    private Integer membershipPrice = -1;

    /**
     * 库存下限
     */
    @Column(name = "stock_lower_limit", columnDefinition = "bigint COMMENT '库存下限'", nullable = false)
    @ColumnDefault("-1")
    private Integer stockLowerLimit = -1;

    /**
     * 库存上限
     */
    @Column(name = "stock_upper_limit", columnDefinition = "bigint COMMENT '库存上限'", nullable = false)
    @ColumnDefault("-1")
    private Integer stockUpperLimit = -1;

    /**
     * 是否特价
     */
    @Enumerated
    @Column(name = "is_special_price", columnDefinition = "smallint COMMENT '库存上限'", nullable = false)
    @ColumnDefault("-1")
    private TrueFalseEnum isSpecialPrice = TrueFalseEnum.OTHER;

    /**
     * 渠道来源
     */
    @Column(name = "channel", columnDefinition = "CHAR(100) COMMENT '渠道来源'", nullable = false)
    @ColumnDefault("-1")
    private String channel = "";

    /**
     * 默认用药途径
     * 关联 ih_dic_med_adm_routes
     */
    @Column(name = "routecode_default", columnDefinition = "bigint COMMENT '默认用药途径 关联 ih_dic_med_adm_routes'")
    @ColumnDefault("-1")
    private Long routecodeDefault = -1L;

    /**
     * 默认用药频次
     * 关联 ih_dic_med_adm_freqs
     */
    @Column(name = "frequencycode_default", columnDefinition = "bigint COMMENT '默认用药频次 关联 ih_dic_med_adm_freqs'")
    @ColumnDefault("-1")
    private Long frequencycodeDefault = -1L;

    public Long getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(Long dosageFormId) {
        this.dosageFormId = dosageFormId;
    }

    public Long getDicDosageUnitId() {
        return dicDosageUnitId;
    }

    public void setDicDosageUnitId(Long dicDosageUnitId) {
        this.dicDosageUnitId = dicDosageUnitId;
    }

    public Long getDicPackageUnitId() {
        return dicPackageUnitId;
    }

    public void setDicPackageUnitId(Long dicPackageUnitId) {
        this.dicPackageUnitId = dicPackageUnitId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getGenericName() {
        return genericName;
    }

    public void setGenericName(String genericName) {
        this.genericName = genericName;
    }

    public String getGenericNameInputCode() {
        return genericNameInputCode;
    }

    public void setGenericNameInputCode(String genericNameInputCode) {
        this.genericNameInputCode = genericNameInputCode;
    }

    public String getGenericNameEnglish() {
        return genericNameEnglish;
    }

    public void setGenericNameEnglish(String genericNameEnglish) {
        this.genericNameEnglish = genericNameEnglish;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getTradeNameInputCode() {
        return tradeNameInputCode;
    }

    public void setTradeNameInputCode(String tradeNameInputCode) {
        this.tradeNameInputCode = tradeNameInputCode;
    }

    public String getTradeNameEnglish() {
        return tradeNameEnglish;
    }

    public void setTradeNameEnglish(String tradeNameEnglish) {
        this.tradeNameEnglish = tradeNameEnglish;
    }

    public String getNmpaNumber() {
        return nmpaNumber;
    }

    public void setNmpaNumber(String nmpaNumber) {
        this.nmpaNumber = nmpaNumber;
    }

    public String getDosageSpec() {
        return dosageSpec;
    }

    public void setDosageSpec(String dosageSpec) {
        this.dosageSpec = dosageSpec;
    }

    public String getPackagingSpec() {
        return packagingSpec;
    }

    public void setPackagingSpec(String packagingSpec) {
        this.packagingSpec = packagingSpec;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public TrueFalseEnum getIsAntimicrobial() {
        return isAntimicrobial;
    }

    public void setIsAntimicrobial(TrueFalseEnum isAntimicrobial) {
        this.isAntimicrobial = isAntimicrobial;
    }

    public String getAntimicrobialGrade() {
        return antimicrobialGrade;
    }

    public void setAntimicrobialGrade(String antimicrobialGrade) {
        this.antimicrobialGrade = antimicrobialGrade;
    }

    public TrueFalseEnum getIsInsurance() {
        return isInsurance;
    }

    public void setIsInsurance(TrueFalseEnum isInsurance) {
        this.isInsurance = isInsurance;
    }

    public TrueFalseEnum getIsPrescription() {
        return isPrescription;
    }

    public void setIsPrescription(TrueFalseEnum isPrescription) {
        this.isPrescription = isPrescription;
    }

    public TrueFalseEnum getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(TrueFalseEnum isSpecial) {
        this.isSpecial = isSpecial;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Integer getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(Integer retailPrice) {
        this.retailPrice = retailPrice;
    }

    public Integer getSourcingPrice() {
        return sourcingPrice;
    }

    public void setSourcingPrice(Integer sourcingPrice) {
        this.sourcingPrice = sourcingPrice;
    }

    public Integer getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(Integer salePrice) {
        this.salePrice = salePrice;
    }

    public Integer getMembershipPrice() {
        return membershipPrice;
    }

    public void setMembershipPrice(Integer membershipPrice) {
        this.membershipPrice = membershipPrice;
    }

    public Integer getStockLowerLimit() {
        return stockLowerLimit;
    }

    public void setStockLowerLimit(Integer stockLowerLimit) {
        this.stockLowerLimit = stockLowerLimit;
    }

    public Integer getStockUpperLimit() {
        return stockUpperLimit;
    }

    public void setStockUpperLimit(Integer stockUpperLimit) {
        this.stockUpperLimit = stockUpperLimit;
    }

    public TrueFalseEnum getIsSpecialPrice() {
        return isSpecialPrice;
    }

    public void setIsSpecialPrice(TrueFalseEnum isSpecialPrice) {
        this.isSpecialPrice = isSpecialPrice;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Long getRoutecodeDefault() {
        return routecodeDefault;
    }

    public void setRoutecodeDefault(Long routecodeDefault) {
        this.routecodeDefault = routecodeDefault;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Long getFrequencycodeDefault() {
        return frequencycodeDefault;
    }

    public void setFrequencycodeDefault(Long frequencycodeDefault) {
        this.frequencycodeDefault = frequencycodeDefault;
    }
}
