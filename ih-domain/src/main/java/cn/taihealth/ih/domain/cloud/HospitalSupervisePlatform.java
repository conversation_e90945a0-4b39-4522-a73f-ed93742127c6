package cn.taihealth.ih.domain.cloud;

import cn.taihealth.ih.domain.AbstractEntity;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = HospitalSupervisePlatform.TABLE_NAME, uniqueConstraints = {
    @UniqueConstraint(name = "U_HOSPITALS_SUPERVISE_PLATFORMS_HOSPITAL_ID", columnNames = {"hospital_id"})}
)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class HospitalSupervisePlatform extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_hospitals_supervise_platforms";

    @Comment("医院ID")
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_HOSPITALS_SUPERVISE_PLATFORMS_HOSPITAL_ID"))
    @OneToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Comment("监管平台ID")
    @JoinColumn(name = "supervise_platform_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_HOSPITALS_SUPERVISE_PLATFORMS_SUPERVISE_PLATFORM_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private SupervisePlatform supervisePlatform;
    /**
     * 接口地址
     */
    @Comment("接口地址")
    @Column(name = "api_url")
    private String apiUrl;

    @Comment("客户端ID")
    @Column(name = "client_id")
    private String clientId;

    @Comment("应用密钥")
    @Column(name = "app_secret")
    private String appSecret;

    /**
     * 是否进行上报
     */
    @Comment("是否进行上报")
    @Column(name = "is_report", nullable = false)
    private Boolean report = false;

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public SupervisePlatform getSupervisePlatform() {
        return supervisePlatform;
    }

    public void setSupervisePlatform(SupervisePlatform supervisePlatform) {
        this.supervisePlatform = supervisePlatform;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public Boolean getReport() {
        return report;
    }

    public void setReport(Boolean report) {
        this.report = report;
    }
}
