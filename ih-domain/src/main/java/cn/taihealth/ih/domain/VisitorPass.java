package cn.taihealth.ih.domain;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;

/**
 */
@Entity
@Table(name = VisitorPass.TABLE_NAME,
        indexes = {
                @Index(name = "IDX_PATIENT_VISITOR_PASS", columnList = "patient_id")
        })
public class VisitorPass extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_visitor_pass";

    @Comment("患者")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id",
            foreignKey = @ForeignKey(name = "FK_VISITOR_PASS_PATIENT_ID"))
    private Patient patient;

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", foreignKey = @ForeignKey(name = "FK_VISITOR_PASS_HOSPITAL_ID"))
    private Hospital hospital;

    // 陪护证证书图片
    @Comment("陪护证证书图片")
    @OneToOne
    @JoinColumn(name = "visitor_pass_cert_picture_id", foreignKey = @ForeignKey(name = "FK_VISITOR_PASS_CERT_PICTURE_ID"))
    private Upload visitorPassCertPicture;

    // 住院号
    @Comment("住院号")
    @Column(name = "regno")
    private String regno;

    // 入院日期
    @Comment("入院日期")
    @Column(name = "in_time")
    private Date in_time;

    // 科室名称
    @Comment("科室名称")
    @Column(name = "dept_name")
    private String dept_name;

    // 病区名称
    @Comment("病区名称")
    @Column(name = "ward_name")
    private String ward_name;

    // 床位号
    @Comment("床位号")
    @Column(name = "bed_no")
    private String bed_no;

    // 陪护证号
    @Comment("陪护证号")
    @Column(name = "visitor_pass_no")
    private String visitor_pass_no;

    // 患者姓名
    @Comment("患者姓名")
    @Column(name = "patname")
    private String patname;

    // 患者性别
    @Comment("患者性别")
    @Column(name = "patsex")
    private String patsex;

    // 患者证件号
    @Comment("患者证件号")
    @Column(name = "pat_certificate_no")
    private String pat_certificate_no;

    // 陪护人证件号
    @Comment("陪护人证件号")
    @Column(name = "attendant_certificate_no")
    private String attendant_certificate_no;

    // 陪护人姓名
    @Comment("陪护人姓名")
    @Column(name = "attendant_name")
    private String attendant_name;

    // 陪护人性别
    @Comment("陪护人性别")
    @Column(name = "attendant_sex")
    private String attendant_sex;


    // 陪护人关系
    @Comment("陪护人关系")
    @Enumerated(EnumType.STRING)
    @Column(name = "relationship")
    private Patient.Relationship relationship;

    // 陪护人关系 for his
    @Comment("陪护人关系 for his")
    @Column(name = "attendant_relationship")
    private String attendant_relationship;

    // 陪护人电话
    @Comment("陪护人电话")
    @Column(name = "attendant_telephone")
    private String attendant_telephone;

    // 陪护人照片
    @Comment("陪护人照片")
    @OneToOne
    @JoinColumn(name = "attendant_picture_id", foreignKey = @ForeignKey(name = "FK_VISITOR_PASS_PICTURE_ID"))
    private Upload attendantPicture;

    // 陪护人照片base64
    @Comment("陪护人照片base64")
    @Column(name = "attendant_picture")
    private String attendant_picture;

    // 核酸检测结果
    @Comment("核酸检测结果")
    @Column(name = "nucleic_acid_test_result")
    private Integer nucleic_acid_test_result;

    // 核酸检测报告图片
    @Comment("核酸检测报告图片")
    @OneToOne
    @JoinColumn(name = "test_report_picture_id", foreignKey = @ForeignKey(name = "FK_VISITOR_PASS_TEST_REPORT_PICTURE_ID"))
    private Upload testReportPicture;

    // 核酸检测报告
    @Comment("核酸检测报告")
    @Column(name = "nucleic_acid_test_report")
    private String nucleic_acid_test_report;

    // 办理日期
    @Comment("办理日期")
    @Column(name = "start_attendant_time")
    private Date start_attendant_time;

    // 核酸检测日期
    @Comment("核酸检测日期")
    @Column(name = "nucleic_acid_test_time")
    private Date nucleic_acid_test_time;

    // 失效日期
    @Comment("失效日期")
    @Column(name = "end_attendant_time")
    private Date end_attendant_time;

    // 状态
    @Comment("状态")
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private VisitorPassStatusEnum status;


    public enum VisitorPassStatusEnum {

        //待审核
        UN_AUDIT("待审核"),
        //已通过

        REJECT("驳回"),

        VALID("有效"),
        //已拒绝
        INVALID("已失效");

        private final String value;

        VisitorPassStatusEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

    }

    // 拒绝理由
    @Comment("拒绝理由")
    @Column(name = "reject_reason")
    private String rejectReason;

    // 有效天数
    @Comment("有效天数")
    @Column(name = "affect_days")
    private Integer affectDays;

    @Comment("是否删除")
    @Column(name = "is_deleted")
    @ColumnDefault("0")
    private boolean deleted = false;

    public String getPatsex() {
        return patsex;
    }

    public void setPatsex(String patsex) {
        this.patsex = patsex;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getRegno() {
        return regno;
    }

    public void setRegno(String regno) {
        this.regno = regno;
    }

    public Date getIn_time() {
        return in_time;
    }

    public void setIn_time(Date in_time) {
        this.in_time = in_time;
    }

    public String getDept_name() {
        return dept_name;
    }

    public void setDept_name(String dept_name) {
        this.dept_name = dept_name;
    }

    public String getWard_name() {
        return ward_name;
    }

    public void setWard_name(String ward_name) {
        this.ward_name = ward_name;
    }

    public String getBed_no() {
        return bed_no;
    }

    public void setBed_no(String bed_no) {
        this.bed_no = bed_no;
    }

    public String getVisitor_pass_no() {
        return visitor_pass_no;
    }

    public void setVisitor_pass_no(String visitor_pass_no) {
        this.visitor_pass_no = visitor_pass_no;
    }

    public String getPatname() {
        return patname;
    }

    public void setPatname(String patname) {
        this.patname = patname;
    }

    public String getPat_certificate_no() {
        return pat_certificate_no;
    }

    public void setPat_certificate_no(String pat_certificate_no) {
        this.pat_certificate_no = pat_certificate_no;
    }

    public String getAttendant_certificate_no() {
        return attendant_certificate_no;
    }

    public void setAttendant_certificate_no(String attendant_certificate_no) {
        this.attendant_certificate_no = attendant_certificate_no;
    }

    public String getAttendant_name() {
        return attendant_name;
    }

    public void setAttendant_name(String attendant_name) {
        this.attendant_name = attendant_name;
    }

    public String getAttendant_sex() {
        return attendant_sex;
    }

    public void setAttendant_sex(String attendant_sex) {
        this.attendant_sex = attendant_sex;
    }

    public Patient.Relationship getRelationship() {
        return relationship;
    }

    public void setRelationship(Patient.Relationship relationship) {
        this.relationship = relationship;
    }

    public String getAttendant_relationship() {
        return attendant_relationship;
    }

    public void setAttendant_relationship(String attendant_relationship) {
        this.attendant_relationship = attendant_relationship;
    }

    public String getAttendant_telephone() {
        return attendant_telephone;
    }

    public void setAttendant_telephone(String attendant_telephone) {
        this.attendant_telephone = attendant_telephone;
    }

    public Upload getAttendantPicture() {
        return attendantPicture;
    }

    public void setAttendantPicture(Upload attendantPicture) {
        this.attendantPicture = attendantPicture;
    }

    public String getAttendant_picture() {
        return attendant_picture;
    }

    public void setAttendant_picture(String attendant_picture) {
        this.attendant_picture = attendant_picture;
    }

    public Integer getNucleic_acid_test_result() {
        return nucleic_acid_test_result;
    }

    public void setNucleic_acid_test_result(Integer nucleic_acid_test_result) {
        this.nucleic_acid_test_result = nucleic_acid_test_result;
    }

    public Upload getTestReportPicture() {
        return testReportPicture;
    }

    public void setTestReportPicture(Upload testReportPicture) {
        this.testReportPicture = testReportPicture;
    }

    public String getNucleic_acid_test_report() {
        return nucleic_acid_test_report;
    }

    public void setNucleic_acid_test_report(String nucleic_acid_test_report) {
        this.nucleic_acid_test_report = nucleic_acid_test_report;
    }

    public Date getStart_attendant_time() {
        return start_attendant_time;
    }

    public void setStart_attendant_time(Date start_attendant_time) {
        this.start_attendant_time = start_attendant_time;
    }

    public Date getNucleic_acid_test_time() {
        return nucleic_acid_test_time;
    }

    public void setNucleic_acid_test_time(Date nucleic_acid_test_time) {
        this.nucleic_acid_test_time = nucleic_acid_test_time;
    }

    public Date getEnd_attendant_time() {
        return end_attendant_time;
    }

    public void setEnd_attendant_time(Date end_attendant_time) {
        this.end_attendant_time = end_attendant_time;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public Integer getAffectDays() {
        return affectDays;
    }

    public void setAffectDays(Integer affectDays) {
        this.affectDays = affectDays;
    }

    public VisitorPassStatusEnum getStatus() {
        return status;
    }

    public void setStatus(VisitorPassStatusEnum status) {
        this.status = status;
    }

    public Upload getVisitorPassCertPicture() {
        return visitorPassCertPicture;
    }

    public void setVisitorPassCertPicture(Upload visitorPassCertPicture) {
        this.visitorPassCertPicture = visitorPassCertPicture;
    }
}
