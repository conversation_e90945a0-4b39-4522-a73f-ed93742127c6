package cn.taihealth.ih.domain.crm;

import cn.taihealth.ih.domain.AbstractEntity;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;

/**
 */
@Entity
@Table(name = CrmTaskDetailAnswerOption.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class CrmTaskDetailAnswerOption extends AbstractEntity {

    public static final String TABLE_NAME = "ih_crm_task_detail_answer_options";

    @Comment("创建时间")
    @Column(name = "created_date", nullable = false)
    private Date createdDate = new Date();

    @Comment("修改时间")
    @Column(name = "updated_date", nullable = false)
    private Date updatedDate = new Date();

    @Comment("问题")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAIL_ANSWER_OPTIONS_QUESTION_ID"))
    private CrmQuestion question;

    /**
     * 选择题选线
     */
    @Comment("选择题选项")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "option_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAIL_ANSWER_OPTIONS_OPTION_ID"))
    private CrmOption option;

    @Comment("回答")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_detail_answer_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAIL_ANSWER_OPTIONS_ANSWER_ID"))
    private CrmTaskDetailAnswer taskDetailAnswer;

    /**
     * 满意度问题分值
     */
    @Comment("满意度问题分值")
    @Column(name = "score")
    private Integer score;

    public CrmQuestion getQuestion() {
        return question;
    }

    public void setQuestion(CrmQuestion question) {
        this.question = question;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public CrmOption getOption() {
        return option;
    }

    public void setOption(CrmOption option) {
        this.option = option;
    }

    public CrmTaskDetailAnswer getTaskDetailAnswer() {
        return taskDetailAnswer;
    }

    public void setTaskDetailAnswer(CrmTaskDetailAnswer taskDetailAnswer) {
        this.taskDetailAnswer = taskDetailAnswer;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }
}
