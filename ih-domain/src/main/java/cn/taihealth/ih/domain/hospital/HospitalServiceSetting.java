package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.HospitalAppCategory;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;

@Entity
@Table(name = HospitalServiceSetting.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class HospitalServiceSetting extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_hospital_service_settings";

    @org.hibernate.annotations.Comment("医院id")
    @ManyToOne
    @JoinColumn(name = "hospital_id",
        foreignKey = @ForeignKey(name = "FK_HOSPITAL_SERVICE_SETTINGS_HOSPITAL_ID"))
    private Hospital hospital;

    @org.hibernate.annotations.Comment("应用编码")
    @Column(name = "code")
    private String code;

    @org.hibernate.annotations.Comment("服务分类")
    @Column(name = "service_category")
    @Enumerated(EnumType.STRING)
    private HospitalAppCategory serviceCategory;

    @org.hibernate.annotations.Comment("排序")
    @Column(name = "order_number")
    private int orderNumber;

    @org.hibernate.annotations.Comment("是否显示")
    @Column(name = "is_show")
    private boolean show = true;

    @org.hibernate.annotations.Comment("是否需要登录才能跳转")
    @Column(name = "need_auth")
    @ColumnDefault("1")
    private boolean needAuth = true;

    public HospitalServiceSetting() {
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public HospitalAppCategory getServiceCategory() {
        return serviceCategory;
    }

    public void setServiceCategory(HospitalAppCategory serviceCategory) {
        this.serviceCategory = serviceCategory;
    }

    public int getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(int orderNumber) {
        this.orderNumber = orderNumber;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public boolean isNeedAuth() {
        return needAuth;
    }

    public void setNeedAuth(boolean needAuth) {
        this.needAuth = needAuth;
    }
}
