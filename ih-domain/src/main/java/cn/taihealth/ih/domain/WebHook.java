package cn.taihealth.ih.domain;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.springframework.util.MimeTypeUtils;

/**
 *
 */
@Entity
@Table(name = WebHook.TABLE_NAME)
public class WebHook extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_webhooks";

    @Column(name = "url", unique = true, nullable = false)
    private String url;

    @Column(name = "content_type", nullable = false)
    private String contentType = MimeTypeUtils.APPLICATION_JSON_VALUE;

    @Column(name = "secret")
    private String secret;

    @Column(name = "is_active", nullable = false)
    private boolean active = true;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }
}
