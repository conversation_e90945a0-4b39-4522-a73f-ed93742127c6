package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Comment;

/**
 * 病历模板
 * <AUTHOR>
 */
@Entity
@Table(name = MedicalCaseContent.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class MedicalCaseContent extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_medical_case_contents";

    @Comment("病历模板")
    @ManyToOne
    @JoinColumn(name = "template_id",
        foreignKey = @ForeignKey(name = "FK_MEDICAL_CASE_CONTENTS_TEMPLATE_ID"))
    private MedicalCaseTemplate template;

    /** 主诉 */
    @Comment("主诉")
    @Column(name = "self_speak", nullable = false)
    @Type(type = "org.hibernate.type.TextType")
    private String selfSpeak;

    /**  现病史 */
    @Comment("现病史")
    @Column(name = "now_medical_history")
    @Type(type = "org.hibernate.type.TextType")
    private String nowMedicalHistory;

    /**  既往史 */
    @Comment("既往史")
    @Column(name = "old_medical_history")
    @Type(type = "org.hibernate.type.TextType")
    private String oldMedicalHistory;

    /**  过敏史 */
    @Comment("过敏史")
    @Column(name = "allergies_history")
    @Type(type = "org.hibernate.type.TextType")
    private String allergiesHistory;

    /**  体格检查 */
    @Comment("体格检查")
    @Column(name = "checking")
    @Type(type = "org.hibernate.type.TextType")
    private String checking;

    /** 诊断 */
    @Comment("诊断")
    @Column(name = "diagnosis")
    @Type(type = "org.hibernate.type.TextType")
    private String diagnosis;

    /** 病历内容 */
    @Comment("病历内容")
    @Column(name = "content")
    @Type(type = "org.hibernate.type.TextType")
    private String content;

    public String getSelfSpeak() {
        return selfSpeak;
    }

    public void setSelfSpeak(String selfSpeak) {
        this.selfSpeak = selfSpeak;
    }

    public String getNowMedicalHistory() {
        return nowMedicalHistory;
    }

    public void setNowMedicalHistory(String nowMedicalHistory) {
        this.nowMedicalHistory = nowMedicalHistory;
    }

    public String getOldMedicalHistory() {
        return oldMedicalHistory;
    }

    public void setOldMedicalHistory(String oldMedicalHistory) {
        this.oldMedicalHistory = oldMedicalHistory;
    }

    public String getAllergiesHistory() {
        return allergiesHistory;
    }

    public void setAllergiesHistory(String allergiesHistory) {
        this.allergiesHistory = allergiesHistory;
    }

    public String getChecking() {
        return checking;
    }

    public void setChecking(String checking) {
        this.checking = checking;
    }

    public String getDiagnosis() {
        return diagnosis;
    }

    public void setDiagnosis(String diagnosis) {
        this.diagnosis = diagnosis;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public MedicalCaseTemplate getTemplate() {
        return template;
    }

    public void setTemplate(MedicalCaseTemplate template) {
        this.template = template;
    }
}
