package cn.taihealth.ih.domain.convert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
public class IntegerListConvert implements AttributeConverter<List<Integer>, String> {

    @Override
    public String convertToDatabaseColumn(List<Integer> attribute) {
        return StandardObjectMapper.stringify(attribute);
    }

    @Override
    public List<Integer> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return Lists.newArrayList();
        }
        if (dbData.startsWith("[")) {
            return StandardObjectMapper.readValue(dbData, new TypeReference<ArrayList<Integer>>() {});
        } else {
            return Arrays.stream(dbData.split(",")).map(u -> Integer.parseInt(u.trim())).collect(Collectors.toList());
        }
    }

}
