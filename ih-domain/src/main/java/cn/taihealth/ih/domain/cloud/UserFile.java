package cn.taihealth.ih.domain.cloud;

import cn.taihealth.ih.domain.UpdatableEntity;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

/**
 */
@Entity
@Table(name = UserFile.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class UserFile extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_user_uploads";

    @Comment("用户")
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_USER_UPLOAD_USER_ID"))
    private User user;

    @Comment("文件签名")
    @ManyToOne
    @JoinColumn(name = "signature_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_USER_UPLOAD_SIGNATURE_ID"))
    private Upload signature;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Upload getSignature() {
        return signature;
    }

    public void setSignature(Upload signature) {
        this.signature = signature;
    }

}
