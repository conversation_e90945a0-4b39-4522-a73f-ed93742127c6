package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.User;
import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

/**
 */
@Entity
@Table(name = ArticleAuthor.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ArticleAuthor extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_article_authors";

    @Comment("文章")
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "article_id",
        foreignKey = @ForeignKey(name = "FK_ARTICLE_AUTHORS_ARTICLE_ID"))
    private Article article;

    @Comment("用户")
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id",
        foreignKey = @ForeignKey(name = "FK_ARTICLE_AUTHORS_CATEGORY_ID"))
    private User author;

    @Comment("是否置顶")
    @Column(name = "is_top", nullable = false)
    private Boolean top;

    public Article getArticle() {
        return article;
    }

    public void setArticle(Article article) {
        this.article = article;
    }

    public User getAuthor() {
        return author;
    }

    public void setAuthor(User author) {
        this.author = author;
    }

    public Boolean getTop() {
        return top;
    }

    public void setTop(Boolean top) {
        this.top = top;
    }
}
