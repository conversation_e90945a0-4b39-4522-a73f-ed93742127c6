package cn.taihealth.ih.domain;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;

/**
 */
@Entity
@Table(name = SystemLog.TABLE_NAME)
public class SystemLog extends AbstractEntity {

    public static final String TABLE_NAME = "ih_system_logs";

    @Comment("")
    @JoinColumn(name = "hospital_id", foreignKey = @ForeignKey(name = "FK_SYSTEM_LOGS_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Comment("用户")
    @ManyToOne
    @JoinColumn(name = "user_id",
        foreignKey = @ForeignKey(name = "FK_SYSTEM_LOGS_USER_ID"))
    private User user;

    @Comment("事件时间")
    @Column(name = "date")
    private Date date = new Date();

    @Comment("用户ip")
    @Column(name = "ip")
    private String ip;

    @Comment("用户操作")
    @Column(name = "action")
    private String action;

    @Comment("操作是否成功")
    @Column(name = "is_success")
    private boolean success;

    /**
     * 访问url
     */
    @Comment("访问url")
    @Column(name = "path")
    private String path;

    /**
     * HTTP method GET/POST/PUT/DELETE等
     */
    @Comment("HTTP method GET/POST/PUT/DELETE等")
    @Column(name = "method")
    private String method;

    /**
     * 小程序appId
     */
    @Comment("小程序appId")
    @Column(name = "app_id")
    private String appId;

    /**
     * httpHeader中的item
     */
    @Comment("httpHeader中的item")
    @Column(name = "item")
    private String item;

    public SystemLog() {
    }

    public SystemLog(Hospital hospital, User user, String ip, String action, boolean success, String appId,
                     String method, String path, String item) {
        this.hospital = hospital;
        this.user = user;
        this.ip = ip;
        this.action = action;
        this.success = success;
        this.appId = appId;
        this.method = method;
        this.path = path;
        this.item = item;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }
}
