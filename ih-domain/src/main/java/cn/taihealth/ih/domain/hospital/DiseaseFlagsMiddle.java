package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = DiseaseFlagsMiddle.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class DiseaseFlagsMiddle extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_diseases_flags_middle";

    @Comment("疾病")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "disease_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_DISEASES_FLAGS_MIDDLE_DISEASE_ID"))
    private Disease disease;

    @Comment("疾病分组标签")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "disease_flags_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_DISEASES_FLAGS_MIDDLE_DISEASE_FLAGS_ID"))
    private DiseaseFlags diseaseFlags;

    public DiseaseFlagsMiddle() {
    }

    public DiseaseFlagsMiddle(Disease disease, DiseaseFlags diseaseFlags) {
        this.disease = disease;
        this.diseaseFlags = diseaseFlags;
    }

    public Disease getDisease() {
        return disease;
    }

    public void setDisease(Disease disease) {
        this.disease = disease;
    }

    public DiseaseFlags getDiseaseFlags() {
        return diseaseFlags;
    }

    public void setDiseaseFlags(DiseaseFlags diseaseFlags) {
        this.diseaseFlags = diseaseFlags;
    }
}
