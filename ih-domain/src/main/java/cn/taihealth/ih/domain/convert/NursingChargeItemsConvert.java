package cn.taihealth.ih.domain.convert;

import cn.taihealth.ih.domain.nursing.NursingHomeChargeItemPrice;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.AttributeConverter;
import java.util.List;

/**
 *
 */
public class NursingChargeItemsConvert implements AttributeConverter<List<NursingHomeChargeItemPrice>, String> {

    private static final Logger log = LoggerFactory.getLogger(NursingChargeItemsConvert.class);

    @Override
    public String convertToDatabaseColumn(List<NursingHomeChargeItemPrice> attribute) {
        return StandardObjectMapper.stringify(attribute);
    }

    @Override
    public List<NursingHomeChargeItemPrice> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return Lists.newArrayList();
        }
        try {
            return StandardObjectMapper.readValue(dbData, new TypeReference<>() {});
        } catch (Exception e) {
            log.error("预交金价格项目格式错误: {}", dbData);
            return Lists.newArrayList();
        }
    }

}
