package cn.taihealth.ih.domain.cloud;

import cn.taihealth.ih.domain.AbstractEntity;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;

/**
 */
@Entity
@Table(name = UserOnline.TABLE_NAME, uniqueConstraints = {
        @UniqueConstraint(name = "U_USER_ONLINE_HOSPITAL_ID_USER_ID", columnNames = {"hospital_id", "user_id"})
})
public class UserOnline extends AbstractEntity {

    public static final String TABLE_NAME = "ih_user_onlines";

    @Comment("医院")
    @JoinColumn(name = "hospital_id", foreignKey = @ForeignKey(name = "FK_USER_ONLINES_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Comment("用户")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Comment("上线时间")
    @Column(name = "date")
    private Date date = new Date();

    public UserOnline() {
    }

    public UserOnline(Hospital hospital, Long userId) {
        this.hospital = hospital;
        this.userId = userId;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
