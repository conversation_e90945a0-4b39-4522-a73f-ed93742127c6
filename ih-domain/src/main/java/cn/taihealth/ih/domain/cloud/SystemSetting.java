package cn.taihealth.ih.domain.cloud;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.enums.SettingKey;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicUpdate;

@Entity
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = SystemSetting.TABLE_NAME,
    uniqueConstraints = {
        @UniqueConstraint(name = "U_SYSTEM_SETTINGS_KEY", columnNames = { "setting_key" })
    }
)
@DynamicUpdate
public class SystemSetting extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_system_settings";

    @Comment("系统设置KEY")
    @Column(name = "setting_key", length = 128)
    @Enumerated(EnumType.STRING)
    private SettingKey key;

    @Comment("系统设置VALUE")
    @Column(name = "setting_value", length = 2000)
    private String value;

    protected SystemSetting() {}

    public SystemSetting(SettingKey key, String value) {
        this.key = key;
        this.value = value;
    }

    public SettingKey getKey() {
        return key;
    }

    public void setKey(SettingKey key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
