package cn.taihealth.ih.domain.dict;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import java.util.Arrays;
import java.util.List;

/**
 *
 */
//@Converter
public class SuggestTypeConvert implements AttributeConverter<List<String>, String> {

    @Override
    public String convertToDatabaseColumn(List<String> enums) {
        return listToDb(enums);
    }

    @Override
    public List<String> convertToEntityAttribute(String dbData) {
        return toList(dbData);
    }

    public static String listToDb(List<String> enums) {
        if (CollectionUtils.isNotEmpty(enums)) {
            return Joiner.on(",").join(enums);
        }
        return null;
    }

    public static List<String> toList(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return Lists.newArrayList();
        }
        return Arrays.asList(dbData.split(","));
    }
}
