package cn.taihealth.ih.domain.copyappointment;

import cn.taihealth.ih.domain.AbstractEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

/**
 * @Author: jzs
 * @Date: 2023-10-18
 * 复印用途-范围关联表
 */
@Entity
@Table(name = MedicalRecordCopyPurposeScopeRel.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class MedicalRecordCopyPurposeScopeRel extends AbstractEntity {

    public static final String TABLE_NAME = "ih_medical_record_copy_purpose_scope_rels";

    @Comment("用途ID")
    @Column(name = "copy_purpose_id")
    private long copyPurposeId;

    @Comment("范围ID")
    @Column(name = "copy_scope_id")
    private long copyScopeId;

    public long getCopyPurposeId() {
        return copyPurposeId;
    }

    public void setCopyPurposeId(long copyPurposeId) {
        this.copyPurposeId = copyPurposeId;
    }

    public long getCopyScopeId() {
        return copyScopeId;
    }

    public void setCopyScopeId(long copyScopeId) {
        this.copyScopeId = copyScopeId;
    }
}
