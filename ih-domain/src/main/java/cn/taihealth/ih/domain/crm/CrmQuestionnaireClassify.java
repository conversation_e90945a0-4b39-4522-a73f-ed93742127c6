package cn.taihealth.ih.domain.crm;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;


@Entity
@Table(name = CrmQuestionnaireClassify.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class CrmQuestionnaireClassify extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_crm_questionnaire_classify";

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", foreignKey = @ForeignKey(name = "FK_CRM_QUESTIONNAIRE_CLASSIFY_HOSPITAL_ID"))
    private Hospital hospital;

    @Comment("问题分类名称")
    @Column
    private String name;

    @Comment("问题分类编码")
    @Column
    private String code;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }
}
