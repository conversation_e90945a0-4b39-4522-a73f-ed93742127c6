package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.ExamOrder.ExamOrderStatus;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ForeignKey;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = ExamOrderOperation.TABLE_NAME,
    indexes = {
        @Index(name = "IDX_EXAM_ORDER_OPERATIONS_STEP", columnList = "step")
    })
@org.hibernate.annotations.Table(appliesTo = ExamOrderOperation.TABLE_NAME, comment = "预约记录操作日志表")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ExamOrderOperation extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_exam_order_operations";

    public enum Step {
        CREATE("开单"),
        UPDATE("预约"),
        PAY("支付"),
        SIGN_IN("签到"),
        START("开始"),
        FINISH("结束"),
        REPORT("报告出具"),
        CANCEL("取消检查"),
        REFUND("退款"),
        RATING("评分"),
        CHECK("检查"),
        MAINTENANCE("取消预约（设备维护/停用）"),
        STOP("停诊");

        private Step(String name) {
            this.name = name;
        }

        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    @Comment("预约记录")
    @ManyToOne
    @JoinColumn(name = "exam_order_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_EXAM_ORDER_OPERATIONS_ORDER_ID"))
    private ExamOrder examOrder;

    @Comment("操作人")
    @ManyToOne
    @JoinColumn(name = "operator_id", foreignKey = @ForeignKey(name = "FK_EXAM_ORDER_OPERATIONS_OPERATOR_ID"))
    private User operator;

    @Comment("操作人姓名")
    @Column(name = "operator_name")
    private String operatorName;

    @Comment("操作人唯一标识")
    @Column(name = "unique_mark")
    private String unique;

    @Comment("当前步骤")
    @Column(name = "step", nullable = false)
    @Enumerated(EnumType.STRING)
    private Step step;

    @Comment("操作前状态")
    @Column(name = "previous_status")
    @Enumerated(EnumType.STRING)
    private ExamOrderStatus previousStatus = ExamOrderStatus.UNKNOWN;

    @Comment("操作后状态")
    @Column(name = "next_status")
    @Enumerated(EnumType.STRING)
    private ExamOrderStatus nextStatus = ExamOrderStatus.UNKNOWN;

    @Comment("备注")
    @Column(name = "remark")
    @Type(type = "org.hibernate.type.TextType")
    private String remark;

    public ExamOrderOperation() {
    }

    public ExamOrder getExamOrder() {
        return examOrder;
    }

    public void setExamOrder(ExamOrder examOrder) {
        this.examOrder = examOrder;
    }

    public User getOperator() {
        return operator;
    }

    public void setOperator(User operator) {
        this.operator = operator;
    }

    public Step getStep() {
        return step;
    }

    public void setStep(Step step) {
        this.step = step;
    }

    public ExamOrderStatus getPreviousStatus() {
        return previousStatus;
    }

    public void setPreviousStatus(ExamOrderStatus previousStatus) {
        this.previousStatus = previousStatus;
    }

    public ExamOrderStatus getNextStatus() {
        return nextStatus;
    }

    public void setNextStatus(ExamOrderStatus nextStatus) {
        this.nextStatus = nextStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getUnique() {
        return unique;
    }

    public void setUnique(String unique) {
        this.unique = unique;
    }
}
