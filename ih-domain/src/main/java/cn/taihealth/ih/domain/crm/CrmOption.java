package cn.taihealth.ih.domain.crm;

import cn.taihealth.ih.domain.UpdatableEntity;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

/**
 */
@Entity
@Table(name = CrmOption.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class CrmOption extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_crm_options";

    @Comment("问题")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_CRM_OPTIONS_QUESTION_ID"))
    private CrmQuestion question;

    @Comment("选项内容")
    @Column(name = "content", length = 1000)
    private String content;

    @Comment("排序")
    @Column(name = "option_order")
    private int optionOrder;

    @Comment("分值")
    @Column(name = "score")
    private Integer score;

    public CrmQuestion getQuestion() {
        return question;
    }

    public void setQuestion(CrmQuestion question) {
        this.question = question;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getOptionOrder() {
        return optionOrder;
    }

    public void setOptionOrder(int optionOrder) {
        this.optionOrder = optionOrder;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }
}
