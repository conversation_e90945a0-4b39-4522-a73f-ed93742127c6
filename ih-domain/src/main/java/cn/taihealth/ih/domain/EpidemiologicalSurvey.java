package cn.taihealth.ih.domain;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

/**
 * 流调筛选
 */
@Entity(name = EpidemiologicalSurvey.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class EpidemiologicalSurvey extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_epidemiological_survey";

    @Column(name = "card_type", columnDefinition = "VARCHAR(255) COMMENT '证件类型'")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("ID_CARD")
    private Patient.CardType cardType = Patient.CardType.ID_CARD;

    @Column(name = "card_num", columnDefinition = "VARCHAR(255) COMMENT '证件号码'")
    private String cardNum;

    @Column(name = "mobile", columnDefinition = "VARCHAR(255) COMMENT '手机号码'")
    private String mobile;

    @Column(name = "address", columnDefinition = "VARCHAR(255) COMMENT '地址'")
    private String address;

    @Column(name = "question", columnDefinition = "TEXT COMMENT '问题，存的是JSON字符串'")
    private String question;

    @Column(name = "normal", columnDefinition = "bit COMMENT '本次流调问卷是否有问题：true：没问题 | false：有问题'")
    private boolean normal = true;

    @Comment("用户")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "FK_EPIDEMIOLOGICAL_SURVEY_USER_ID"))
    private User user;

    @Comment("医院")
    @JoinColumn(name = "hospital_id", nullable = false, foreignKey = @ForeignKey(name = "FK_EPIDEMIOLOGICAL_SURVEY_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Comment("对应的二维码")
    @JoinColumn(name = "survey_code", nullable = false, foreignKey = @ForeignKey(name = "FK_EPIDEMIOLOGICAL_SURVEY_SURVEY_CODE"))
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private Upload surveyCode;

    public Patient.CardType getCardType() {
        return cardType;
    }

    public void setCardType(Patient.CardType cardType) {
        this.cardType = cardType;
    }

    public String getCardNum() {
        return cardNum;
    }

    public void setCardNum(String cardNum) {
        this.cardNum = cardNum;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public boolean isNormal() {
        return normal;
    }

    public void setNormal(boolean normal) {
        this.normal = normal;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Upload getSurveyCode() {
        return surveyCode;
    }

    public void setSurveyCode(Upload surveyCode) {
        this.surveyCode = surveyCode;
    }
}
