package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 处方签章表
 */
@Entity
@Table(name = PrescriptionOrderCa.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class PrescriptionOrderCa extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_prescription_order_cas";

    @Comment("医院ID")
    @Column(name = "hospital_id")
    private Long hospitalId;

    /* 订单表id */
    @Comment("订单表id")
    @Column(name = "order_id", nullable = false)
    private Long orderId;

    /**
     * 处方
     */
    @Comment("处方")
    @Column(name = "prescription_order_id")
    private Long prescriptionOrderId;

    /**
     * 医生id
     */
    @Comment("医生id")
    @Column(name = "doctor_id")
    private Long doctorId;

    /**
     * 医生在ca系统用户名
     */
    @Comment("医生在ca系统用户名")
    @Column(name = "doctor_ca_username")
    private String doctorCaUsername;

    /**
     * 药师id
     */
    @Comment("药师id")
    @Column(name = "doctor_review_id")
    private Long doctorReviewId;

    /**
     * 药师在ca系统用户名
     */
    @Comment("药师在ca系统用户名")
    @Column(name = "doctor_review_ca_username")
    private String doctorReviewCaUsername;

    /**
     * 医生签章pdf文件标识， 信安世纪使用random
     */
    @Comment("医生签名")
    @Column(name = "doctor_sign")
    private String doctorSign;

    /**
     * 医生签章pdf文件标识， 信安世纪使用random
     */
    @Comment("医生签章pdf文件标识")
    @Column(name = "doctor_pdf_uid")
    private String doctorPdfUid;

    /**
     * 医生未签章pdf文件
     */
    @Comment("医生未签章pdf文件")
    @Column(name = "doctor_pdf_no_ca_id")
    private Long doctorPdfNoCaId;

    /**
     * 医生签章pdf文件hash
     */
    @Comment("医生签章pdf文件hash")
    @Column(name = "doctor_pdf_file_hash")
    private String doctorPdfFileHash;

    /**
     * 医生签章后的pdf
     */
    @Comment("医生签章后的pdf")
    @Column(name = "doctor_pdf_id")
    private Long doctorPdfId;

    /**
     * 药师签章pdf文件标识， 信安世纪使用random
     */
    @Comment("药师签章pdf文件标识")
    @Column(name = "doctor_review_pdf_uid")
    private String doctorReviewPdfUid;

    /**
     * 医生签章pdf摘要值
     */
    @Comment("医生签章pdf摘要值")
    @Column(name = "doctor_digest_value")
    private String doctorDigestValue;

    /**
     * 药师签章pdf文件hash
     */
    @Comment("药师签章pdf文件hash")
    @Column(name = "doctor_review_pdf_file_hash")
    private String doctorReviewPdfFileHash;

    /**
     * 药师未签章pdf文件
     */
    @Comment("药师未签章pdf文件")
    @Column(name = "doctor_review_no_ca_id")
    private Long doctorReviewNoCaId;

    /**
     * 药师签章pdf摘要值
     */
    @Comment("药师签章pdf摘要值")
    @Column(name = "doctor_review_digest_value")
    private String doctorReviewDigestValue;

    /**
     * 药师签章后的pdf
     */
    @Comment("药师签章后的pdf")
    @Column(name = "doctor_review_pdf_id")
    private Long doctorReviewPdfId;

    @Comment("医院签章pdf文件标识")
    @Column(name = "final_pdf_uid")
    private String finalPdfUid;

    /**
     * 医院签章后的pdf
     */
    @Comment("医院签章后的pdf")
    @Column(name = "final_pdf_id")
    private Long finalPdfId;

    /**
     * 医院签章后的pdf摘要值
     */
    @Comment("医院签章后的pdf摘要值")
    @Column(name = "final_digest_value")
    private String finalDigestValue;

    /**
     * 对应的处方是否被拒绝了
     */
    /* 是否逻辑删除，0删除，1未删 */
    @Comment("是否逻辑删除，0删除，1未删")
    @Column(name = "is_enabled", nullable = false)
    @ColumnDefault("1")
    private boolean enabled = true;

    public PrescriptionOrderCa() {
    }

    public PrescriptionOrderCa(Long hospitalId, Long orderId, Long prescriptionOrderId) {
        this.hospitalId = hospitalId;
        this.orderId = orderId;
        this.prescriptionOrderId = prescriptionOrderId;
    }

    public Long getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getPrescriptionOrderId() {
        return prescriptionOrderId;
    }

    public void setPrescriptionOrderId(Long prescriptionOrderId) {
        this.prescriptionOrderId = prescriptionOrderId;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public String getDoctorCaUsername() {
        return doctorCaUsername;
    }

    public void setDoctorCaUsername(String doctorCaUsername) {
        this.doctorCaUsername = doctorCaUsername;
    }

    public Long getDoctorReviewId() {
        return doctorReviewId;
    }

    public void setDoctorReviewId(Long doctorReviewId) {
        this.doctorReviewId = doctorReviewId;
    }


    public Long getDoctorPdfId() {
        return doctorPdfId;
    }

    public void setDoctorPdfId(Long doctorPdfId) {
        this.doctorPdfId = doctorPdfId;
    }

    public Long getDoctorReviewPdfId() {
        return doctorReviewPdfId;
    }

    public void setDoctorReviewPdfId(Long doctorReviewPdfId) {
        this.doctorReviewPdfId = doctorReviewPdfId;
    }

    public Long getFinalPdfId() {
        return finalPdfId;
    }

    public void setFinalPdfId(Long finalPdfId) {
        this.finalPdfId = finalPdfId;
    }

    public String getDoctorPdfUid() {
        return doctorPdfUid;
    }

    public void setDoctorPdfUid(String doctorPdfUid) {
        this.doctorPdfUid = doctorPdfUid;
    }

    public String getDoctorPdfFileHash() {
        return doctorPdfFileHash;
    }

    public void setDoctorPdfFileHash(String doctorPdfFileHash) {
        this.doctorPdfFileHash = doctorPdfFileHash;
    }

    public String getDoctorReviewPdfUid() {
        return doctorReviewPdfUid;
    }

    public void setDoctorReviewPdfUid(String doctorReviewPdfUid) {
        this.doctorReviewPdfUid = doctorReviewPdfUid;
    }

    public String getDoctorReviewPdfFileHash() {
        return doctorReviewPdfFileHash;
    }

    public void setDoctorReviewPdfFileHash(String doctorReviewPdfFileHash) {
        this.doctorReviewPdfFileHash = doctorReviewPdfFileHash;
    }

    public String getDoctorReviewCaUsername() {
        return doctorReviewCaUsername;
    }

    public void setDoctorReviewCaUsername(String doctorReviewCaUsername) {
        this.doctorReviewCaUsername = doctorReviewCaUsername;
    }

    public Long getDoctorPdfNoCaId() {
        return doctorPdfNoCaId;
    }

    public void setDoctorPdfNoCaId(Long doctorPdfNoCaId) {
        this.doctorPdfNoCaId = doctorPdfNoCaId;
    }

    public Long getDoctorReviewNoCaId() {
        return doctorReviewNoCaId;
    }

    public void setDoctorReviewNoCaId(Long doctorReviewNoCaId) {
        this.doctorReviewNoCaId = doctorReviewNoCaId;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDoctorDigestValue() {
        return doctorDigestValue;
    }

    public void setDoctorDigestValue(String doctorDigestValue) {
        this.doctorDigestValue = doctorDigestValue;
    }

    public String getDoctorReviewDigestValue() {
        return doctorReviewDigestValue;
    }

    public void setDoctorReviewDigestValue(String doctorReviewDigestValue) {
        this.doctorReviewDigestValue = doctorReviewDigestValue;
    }

    public String getFinalDigestValue() {
        return finalDigestValue;
    }

    public void setFinalDigestValue(String finalDigestValue) {
        this.finalDigestValue = finalDigestValue;
    }

    public String getDoctorSign() {
        return doctorSign;
    }

    public void setDoctorSign(String doctorSign) {
        this.doctorSign = doctorSign;
    }

    public String getFinalPdfUid() {
        return finalPdfUid;
    }

    public void setFinalPdfUid(String finalPdfUid) {
        this.finalPdfUid = finalPdfUid;
    }
}
