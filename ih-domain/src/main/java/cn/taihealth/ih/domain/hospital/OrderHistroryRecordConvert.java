package cn.taihealth.ih.domain.hospital;

import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import javax.persistence.AttributeConverter;
import org.apache.commons.lang3.StringUtils;

/**
 * 订单 归档快照 转换数据表对象存储
 *
 */
public class OrderHistroryRecordConvert implements AttributeConverter<Object, String> {

    @Override
    public String convertToDatabaseColumn(Object attribute) {
        return StandardObjectMapper.stringify(attribute);
    }

    @Override
    public Object convertToEntityAttribute(String dbData) {
        if (StringUtils.isBlank(dbData)) {
            return null;
        }
        return StandardObjectMapper.readValue(dbData, new TypeReference<Object>() {
        });
    }


}
