package cn.taihealth.ih.domain;

import cn.taihealth.ih.domain.enums.GuidenceQuestionTypeEnum;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Type;

/**
 * <AUTHOR>
 * @Date 2020/9/27
 */
@Entity
@Table(name = GuidanceRecord.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class GuidanceRecord extends UpdatableEntity{

  public static final String TABLE_NAME = "ih_guidance_records";

  @Column(name = "chat_id", nullable = false, columnDefinition = "bigint COMMENT '关联的chatId'")
  @Type(type = "long")
  @ColumnDefault("-1")
  Long chatId = -1L;

  @Column(name = "order_id", nullable = false, columnDefinition = "bigint COMMENT '关联的orderId'")
  @Type(type = "long")
  @ColumnDefault("-1")
  Long orderId = -1L;

  @Column(name = "question_type", length = 4, nullable = false, columnDefinition = "INT(4) COMMENT '问题类型'")
  @ColumnDefault("-1")
  private GuidenceQuestionTypeEnum questionType;

  @Column(name = "question", length = 200, nullable = false,  columnDefinition = "varchar(200) COMMENT '问题'")
  @ColumnDefault("-1")
  private String question;

  @Column(name = "answer", length = 200, nullable = false, columnDefinition = "varchar(200)　COMMENT '回答'")
  @Type(type = "string")
  @ColumnDefault("-1")
  private String answer;

  @Column(name = "sequence", length = 4, nullable = false, columnDefinition = "int(4) COMMENT '问答序号'")
  @Type(type = "integer")
  @ColumnDefault("-1")
  private Integer sequence;

  public Long getChatId() {
    return chatId;
  }

  public void setChatId(Long chatId) {
    this.chatId = chatId;
  }

  public Long getOrderId() {
    return orderId;
  }

  public void setOrderId(Long orderId) {
    this.orderId = orderId;
  }

  public GuidenceQuestionTypeEnum getQuestionType() {
    return questionType;
  }

  public void setQuestionType(GuidenceQuestionTypeEnum questionType) {
    this.questionType = questionType;
  }

  public String getQuestion() {
    return question;
  }

  public void setQuestion(String question) {
    this.question = question;
  }

  public String getAnswer() {
    return answer;
  }

  public void setAnswer(String answer) {
    this.answer = answer;
  }

  public Integer getSequence() {
    return sequence;
  }

  public void setSequence(Integer sequence) {
    this.sequence = sequence;
  }
}

