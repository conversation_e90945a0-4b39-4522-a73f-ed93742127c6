package cn.taihealth.ih.domain.crm;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 */
public enum Province {

    beijing("京", "北京市"),
    shanghai("沪", "上海市"),
    jiangsu("苏", "江苏省"),
    zhejiang("浙", "浙江省"),
    anhui("徽", "安徽省"),
    fujian("闽", "福建省"),
    jiangxi("赣", "江西省"),
    shandong("鲁", "山东省"),
    tianjin("津", "天津市"),
    hebei("冀", "河北省"),
    shanxi("晋", "山西省"),
    neimenggu("蒙", "内蒙古自治区"),
    hubei("鄂", "湖北省"),
    hunan("湘", "湖南省"),
    henan("豫", "河南省"),
    guangdong("粤", "广东省"),
    guangxi("桂", "广西壮族自治区"),
    hainan("琼", "海南省"),
    liaoning("辽", "辽宁省"),
    jilin("吉", "吉林省"),
    heilongjiang("黑", "黑龙江省"),
    chongqing("渝", "重庆市"),
    sichuan("川", "四川省"),
    yunnan("云", "云南省"),
    guizhou("贵", "贵州省"),
    xizang("藏", "西藏自治区"),
    shanxisheng("陕", "陕西省"),
    gansu("甘", "甘肃省"),
    qinghai("青", "青海省"),
    ningxia("宁", "宁夏回族自治区"),
    xinjiang("新", "新疆维吾尔自治区");

    private final String shortName;
    private final String fullName;

    Province(String shortName, String fullName) {
        this.shortName = shortName;
        this.fullName = fullName;
    }

    public String getShortName() {
        return shortName;
    }

    public String getFullName() {
        return fullName;
    }

    public static Set<String> shortNames() {
        Set<String> set = Sets.newHashSet();
        Arrays.stream(Province.values())
            .forEach(p -> {
                set.add(p.getShortName());
            });

        return set;
    }

    public static Set<String> fullNames() {
        return Sets.newHashSet(Province.getNames());
    }

    public static List<String> getNames() {
        List<String> names = Lists.newArrayList();
        for (Province each : Province.values()) {
            names.add(each.getFullName());
        }

        return names;
    }

    public static Province fromShortName(String shortname) {
        for (Province each : Province.values()) {
            if (shortname.equalsIgnoreCase(each.getShortName())) {
                return each;
            }
        }

        throw new IllegalArgumentException("Unknown province " + shortname);
    }

    public static boolean isShortName(String shortname) {
        for (Province each : Province.values()) {
            if (shortname.equalsIgnoreCase(each.getShortName())) {
                return true;
            }
        }
        return false;
    }

    public static Province fromFullName(String fullName) {
        for (Province each : Province.values()) {
            if (fullName.equalsIgnoreCase(each.fullName)) {
                return each;
            }
        }

        throw new IllegalArgumentException("Unknown province " + fullName);
    }
}
