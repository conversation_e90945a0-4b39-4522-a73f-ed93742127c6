package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.AbstractEntity;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
@Table(name = DeptMedicalWorker.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class DeptMedicalWorker extends AbstractEntity {

    public static final String TABLE_NAME = "ih_depts_medical_workers";

    @Comment("医生")
    @JoinColumn(name = "medical_worker_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_DEPTS_MEDICAL_WORKERS_MEDICAL_WORKER_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private MedicalWorker medicalWorker;

    @Comment("科室")
    @JoinColumn(name = "dept_id",
            foreignKey = @ForeignKey(name = "FK_DEPTS_MEDICAL_WORKERS_DEPT_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Dept dept;

    public MedicalWorker getMedicalWorker() {
        return medicalWorker;
    }

    public void setMedicalWorker(MedicalWorker medicalWorker) {
        this.medicalWorker = medicalWorker;
    }

    public Dept getDept() {
        return dept;
    }

    public void setDept(Dept dept) {
        this.dept = dept;
    }
}
