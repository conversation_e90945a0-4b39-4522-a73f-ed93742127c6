package cn.taihealth.ih.domain.copyappointment;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.UserAddress;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.enums.RefundTypeEnum;
import com.google.common.collect.Lists;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Entity
@Table(name = UserMedicalRecordCopyAppointment.TABLE_NAME, indexes = {
        @Index(name = "IDX_copy_appointment_status", columnList = "status")
})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class UserMedicalRecordCopyAppointment extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_user_medical_record_copy_appointment";

    @Comment("患者姓名")
    @Column(name = "patient_name", length = 64)
    private String patientName;

    @Comment("患者证件类型")
    @Enumerated(EnumType.STRING)
    @ColumnDefault(value = "ID_CARD")
    @Column(name = "card_type", length = 64)
    private Patient.CardType cardType = Patient.CardType.ID_CARD;

    @Comment("患者身份证号")
    @Column(name = "patient_id_card_num")
    private String patientIdCardNum;

    @Comment("患者住院号")
    @Column(name = "reg_no")
    private String regNo;

    @Comment("出院时间")
    @Column(name = "discharge_time")
    private Date dischargeTime;

    @Comment("科室")
    @Column(name = "dept")
    private String dept;

    // 申请人类型
    @Comment("申请人类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "applicant_type")
    private ApplicantType applicantType;

    @Comment("申请人姓名")
    @Column(name = "name", length = 64)
    private String name;

    @Comment("申请人身份证号")
    @Column(name = "id_card_num")
    private String idCardNum;

    @Comment("申请人联系方式")
    @Column(name = "mobile")
    private String mobile;

    // 复印用途
    @Comment("复印用途")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "copy_purpose_id", foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_COPY_PURPOSE_ID"))
    private MedicalRecordCopyPurpose medicalRecordCopyPurpose;

    // 复印范围
    @Comment("复印范围")
    @Column(name = "copy_scope_id", length = 1000)
    @Convert(converter = MedicalRecordCopyScopeCover.class)
    private List<Long> copyScopeIds = Lists.newArrayList();

    @Comment("补充说明")
    @Column(name = "explanation")
    private String explanation;

    @Comment("复印数量")
    @Column(name = "copy_count")
    private int copyCount;

    @Comment("收费标准:份 页")
    @Column(name = "unit")
    private String unit;

    @Comment("d端核算时必填 复印页数")
    @Column(name = "page_total")
    private int pageTotal;

    // 单价：分
    @Comment("复印单价：分")
    @Column(name = "unit_price")
    private int unitPrice;

    // 总价：分
    @Comment("总价：分")
    @Column(name = "total_price")
    private int totalPrice;

    // 预交金额：分
    @Comment("预交金额：分")
    @Column(name = "paid_price")
    private int paidPrice;

    // 补缴金额:分
    @Comment("补缴金额:分")
    @Column(name = "append_price")
    private int appendPrice;

    @Column(name = "refund_type")
    @Comment("退款方式")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("REFUND")
    private RefundTypeEnum refundType = RefundTypeEnum.REFUND;

    // 退款金额:分
    @Comment("退款金额:分")
    @Column(name = "refund_price")
    private int refundPrice;

    // 拒绝退款原因
    @Comment("拒绝退款原因")
    @Column(name = "refund_reject_reason")
    private String refundRejectReason;

    // 配送方式
    @Comment("配送方式")
    @Enumerated(EnumType.STRING)
    @Column(name = "delivery_method")
    private DeliveryMethod deliveryMethod;

    // 收件人地址
    @Comment("收件人地址")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "addresses_id", foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_ADDRESSES_ID"))
    private UserAddress deliveryInfo;

    @Comment("医院信息")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "offline_hospital_id", nullable = false, foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_OFFLINE_HOSPITAL_ID"))
    private OfflineHospital offlineHospital;

    @Comment("用户")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_USER_ID"))
    private User user;

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false, foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_HOSPITAL_ID"))
    private Hospital hospital;

    // 订单状态
    @Comment("订单状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    @ColumnDefault("UNKNOWN")
    private CopyAppointmentOrderStatus status = CopyAppointmentOrderStatus.UNKNOWN;

    @OneToMany(mappedBy = "userMedicalRecordCopyAppointment", fetch = FetchType.LAZY)
    private List<UserMedicalRecordCopyAppointmentOperation> operations = Lists.newArrayList();

    // 物流信息
    @Comment("物流信息")
    @Column(name = "logistics_data")
    private String logisticsData;

    // 驳回原因
    @Comment("驳回原因")
    @Column(name = "reject_reason")
    private String rejectReason;

    /**
     * 身份证人像面
     */
    @Comment("身份证人像面")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "portrait", foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_PORTRAIT"))
    private Upload portrait;
    /**
     * 身份证国徽面
     */
    @Comment("身份证国徽面")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "national_emblem", foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_NATIONAL_EMBLEM"))
    private Upload nationalEmblem;
    /**
     * 手持自身身份证照片
     */
    @Comment("手持自身身份证照片")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "holding_id_card", foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_HOLDING_ID_CARD"))
    private Upload holdingIdCard;

    // 审核人
    @Comment("审核人")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audit_user_id", foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_AUDIT_USER_ID"))
    private User auditUser;

    // 审核时间
    @Comment("审核时间")
    @Column(name = "audit_date")
    private Date auditDate;

    // 付款时间
    @Comment("付款时间")
    @Column(name = "pay_time")
    private Date payTime;

    // 退款时间
    @Comment("退款时间")
    @Column(name = "refund_time")
    private Date refundTime;

    // 订单预支付金额
    @Comment("订单预支付金额")
    @Column(name = "pre_pay_price")
    @ColumnDefault("0")
    private int prePayPrice;

    // 微信订单号
    @Comment("微信订单号")
    @Column(name = "out_trade_no")
    private String outTradeNo;

    public enum ApplicantType {
        SELF, // 本人
        AGENT; // 代办
    }

    public enum DeliveryMethod {
        POSTAL_DELIVERY("邮寄"), // 邮寄
        SELF_PICKUP("自提"); // 自取

        private final String name;

        DeliveryMethod(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    public enum CopyAppointmentOrderStatus {

        // 0-9  订单创建
        // 10-19  订单支付
        // 20-29  订单审核
        // 30-39  订单发货
        // 40-49  订单结束
        // 90-99  订单异常

        UNKNOWN(-1, "缺省值，没有任何业务意义"),
        CREATE(0, "订单创建"),

        WAIT_PAY(10, "订单待支付"),
        PAYING(11, "订单支付中"),
        PAY_SUCCESS(12, "订单支付成功"),
        WAITING_APPEND(13, "订单待补缴"),
        PAY_APPEND_SUCCESS(14, "订单补缴成功"),

        WAITING_REVIEW(20, "待审核"),
        WAITING_COPY(21, "待复印"),
        WAITING_ACCOUNTING(22, "待核算"),

        WAIT_SEND(35, "待邮寄"),
        WAIT_PICK_UP(36, "待自提"),
        WAIT_SIGN(37, "待签收"),
        PICK_UP(38, "已自提"),
        DELIVER_SIGN(39, "已签收"),

        COMPLETE(40, "已完成"),

        CANCELLED(90, "已取消"),
        APPLY_REFUNDING(93, "申请退款中"),
        REFUNDING(94, "退款中"),
        SURPLUS_REFUNDING(95, "差额退款中"),
        REFUNDED(96, "已退款"),
        SURPLUS_REFUNDED(97, "差额退款"),
        CLOSE(99, "已关闭");

        private final Integer code;

        private final String label;
        CopyAppointmentOrderStatus(Integer code, String label) {
            this.code = code;
            this.label = label;
        }

        public Integer getCode() {
            return code;
        }

        public String getLabel() {
            return label;
        }

    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public Patient.CardType getCardType() {
        return cardType;
    }

    public void setCardType(Patient.CardType cardType) {
        this.cardType = cardType;
    }

    public String getPatientIdCardNum() {
        return patientIdCardNum;
    }

    public void setPatientIdCardNum(String patientIdCardNum) {
        this.patientIdCardNum = patientIdCardNum;
    }

    public String getRegNo() {
        return regNo;
    }

    public void setRegNo(String regNo) {
        this.regNo = regNo;
    }

    public Date getDischargeTime() {
        return dischargeTime;
    }

    public void setDischargeTime(Date dischargeTime) {
        this.dischargeTime = dischargeTime;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public ApplicantType getApplicantType() {
        return applicantType;
    }

    public void setApplicantType(ApplicantType applicantType) {
        this.applicantType = applicantType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCardNum() {
        return idCardNum;
    }

    public void setIdCardNum(String idCardNum) {
        this.idCardNum = idCardNum;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public MedicalRecordCopyPurpose getMedicalRecordCopyPurpose() {
        return medicalRecordCopyPurpose;
    }

    public void setMedicalRecordCopyPurpose(MedicalRecordCopyPurpose medicalRecordCopyPurpose) {
        this.medicalRecordCopyPurpose = medicalRecordCopyPurpose;
    }

    public List<Long> getCopyScopeIds() {
        return copyScopeIds;
    }

    public void setCopyScopeIds(List<Long> copyScopeIds) {
        this.copyScopeIds = copyScopeIds;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public int getCopyCount() {
        return copyCount;
    }

    public void setCopyCount(int copyCount) {
        this.copyCount = copyCount;
    }

    public DeliveryMethod getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(DeliveryMethod deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public UserAddress getDeliveryInfo() {
        return deliveryInfo;
    }

    public void setDeliveryInfo(UserAddress deliveryInfo) {
        this.deliveryInfo = deliveryInfo;
    }

    public OfflineHospital getOfflineHospital() {
        return offlineHospital;
    }

    public void setOfflineHospital(OfflineHospital offlineHospital) {
        this.offlineHospital = offlineHospital;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public CopyAppointmentOrderStatus getStatus() {
        return status;
    }

    public void setStatus(CopyAppointmentOrderStatus status) {
        this.status = status;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public int getPageTotal() {
        return pageTotal;
    }

    public void setPageTotal(int pageTotal) {
        this.pageTotal = pageTotal;
    }

    public int getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(int unitPrice) {
        this.unitPrice = unitPrice;
    }

    public int getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(int totalPrice) {
        this.totalPrice = totalPrice;
    }

    public int getPaidPrice() {
        return paidPrice;
    }

    public void setPaidPrice(int paidPrice) {
        this.paidPrice = paidPrice;
    }

    public int getAppendPrice() {
        return appendPrice;
    }

    public void setAppendPrice(int appendPrice) {
        this.appendPrice = appendPrice;
    }

    public int getRefundPrice() {
        return refundPrice;
    }

    public void setRefundPrice(int refundPrice) {
        this.refundPrice = refundPrice;
    }

    public String getRefundRejectReason() {
        return refundRejectReason;
    }

    public void setRefundRejectReason(String refundRejectReason) {
        this.refundRejectReason = refundRejectReason;
    }

    public List<UserMedicalRecordCopyAppointmentOperation> getOperations() {
        return operations;
    }

    public void setOperations(List<UserMedicalRecordCopyAppointmentOperation> operations) {
        this.operations = operations;
    }

    public String getLogisticsData() {
        return logisticsData;
    }

    public void setLogisticsData(String logisticsData) {
        this.logisticsData = logisticsData;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public Upload getPortrait() {
        return portrait;
    }

    public void setPortrait(Upload portrait) {
        this.portrait = portrait;
    }

    public Upload getNationalEmblem() {
        return nationalEmblem;
    }

    public void setNationalEmblem(Upload nationalEmblem) {
        this.nationalEmblem = nationalEmblem;
    }

    public Upload getHoldingIdCard() {
        return holdingIdCard;
    }

    public void setHoldingIdCard(Upload holdingIdCard) {
        this.holdingIdCard = holdingIdCard;
    }

    public User getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(User auditUser) {
        this.auditUser = auditUser;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    public int getPrePayPrice() {
        return prePayPrice;
    }

    public void setPrePayPrice(int prePayPrice) {
        this.prePayPrice = prePayPrice;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public RefundTypeEnum getRefundType() {
        return refundType;
    }

    public void setRefundType(RefundTypeEnum refundType) {
        this.refundType = refundType;
    }
}
