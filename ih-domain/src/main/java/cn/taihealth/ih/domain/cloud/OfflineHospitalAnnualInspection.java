package cn.taihealth.ih.domain.cloud;


import cn.taihealth.ih.domain.UpdatableEntity;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

/**
 * 线下医院年检信息表
 */
@Entity
@Table(name = OfflineHospitalAnnualInspection.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class OfflineHospitalAnnualInspection extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_offline_hospital_annual_inspection";

    @Comment("线下医院ID")
    @JoinColumn(name = "offline_hospital_id",
            foreignKey = @ForeignKey(name = "FK_OFFLINE_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private OfflineHospital offlineHospital;

    @Comment("年份")
    @Column(name = "year_str")
    private String year;

    /**
     * 登记时间
     * 说明: 格式 'yyyy-MM-dd'
     * 备注: 例如 2019-12-10
     */
    @Comment("登记时间")
    @Column(name = "registration_date")
    private Date registrationDate;

    /**
     * 上传时间
     * 说明: 格式 'yyyy-MM-dd HH24:mi:ss'
     * 备注: 例如 2019-12-10 16:34:32
     */
    @Comment("上传时间")
    @Column(name = "uploaded_date")
    private Date uploadedDate;

    /**
     * 来源
     * 说明: 不一致以卫监所信息为准：0：卫监所, 1：医院填报
     */
    @Comment("来源 0：卫监所, 1：医院填报")
    @Column(name = "resource")
    private String resource;

    /**
     * 三级等保是否更新
     * 说明: 0：否, 1：是
     */
    @Comment("三级等保是否更新 0：否, 1：是")
    @Column(name = "update_three_level_equal_protection")
    private String updateThreelevelEqualprotection;

    /**
     * 派 出（分支）机构数量
     */
    @Comment("派 出（分支）机构数量")
    @Column(name = "branch_number")
    private Integer branchNumber;

    /**
     * 职工总数
     */
    @Comment("职工总数")
    @Column(name = "work_force")
    private Integer workForce;

    /**
     * 客户服务人数总数
     */
    @Comment("客户服务人数总数")
    @Column(name = "service_customer_sum")
    private Integer serviceCustomerSum;

    /**
     * 日均坐诊医生数
     */
    @Comment("日均坐诊医生数")
    @Column(name = "average_doctors_number")
    private Integer averageDoctorsNumber;

    /**
     * 许可证号码
     * 说明: 需要调取《文件上传及验证接口》，上传年检校验记录PDF 文件。
     */
    @Comment("许可证号码")
    @Column(name = "licence_number")
    private String licenceNumber;

    /**
     * 许可证有效期
     * 说明: 格式 'yyyy-MM-dd'
     * 备注: 例如 2019-12-10
     */
    @Comment("许可证有效期")
    @Column(name = "licence_expiration")
    private Date licenceExpiration;

    /**
     * 业务用房面积
     */
    @Comment("业务用房面积")
    @Column(name = "premises_area")
    private BigDecimal premisesArea;

    /**
     * 总收入（万元）
     */
    @Comment("总收入（万元）")
    @Column(name = "total_income")
    private BigDecimal totalIncome;

    /**
     * 总支出（万元）
     */
    @Comment("总支出（万元）")
    @Column(name = "total_expenditure")
    private BigDecimal totalExpenditure;

    /**
     * 总资产（万元）
     */
    @Comment("总资产（万元）")
    @Column(name = "total_assets")
    private BigDecimal totalAssets;

    /**
     * 流动资产（万元）
     */
    @Comment("流动资产（万元）")
    @Column(name = "flow_assets")
    private BigDecimal flowAssets;

    /**
     * 对外资产（万元）
     */
    @Comment("对外资产（万元）")
    @Column(name = "external_assets")
    private BigDecimal externalAssets;

    /**
     * 固定资产（万元）
     */
    @Comment("固定资产（万元）")
    @Column(name = "fixed_assets")
    private BigDecimal fixedAssets;

    /**
     * 无形资产及开办费（万元）
     */
    @Comment("无形资产及开办费（万元）")
    @Column(name = "intangible_Assets")
    private BigDecimal intangibleAssets;

    /**
     * 负债（万元）
     */
    @Comment("负债（万元）")
    @Column(name = "liabilities")
    private BigDecimal liabilities;

    /**
     * 净资产（万元）
     */
    @Comment("净资产（万元")
    @Column(name = "net_assets")
    private BigDecimal netAssets;

    /**
     * 三级等保编码
     * 说明: 需要调取《文件上传及验证接口》，上传信息安全等级保护证书。
     */
    @Comment("三级等保编码")
    @Column(name = "three_level_equal_protection_code")
    private String threelevelEqualprotectionCode;

    @Comment("年检证明文件")
    @ManyToOne
    @JoinColumn(name = "annual_inspection_file_id",
        foreignKey = @ForeignKey(name = "FK_ANNUAL_INSPECTION_FILE_ID"))
    private Upload annualInspectionFile;

    @Comment("三级等保文件")
    @ManyToOne
    @JoinColumn(name = "three_level_equal_protection_file_id",
        foreignKey = @ForeignKey(name = "FK_THREE_LEVEL_EQUAL_PROTECTION_FILE_ID"))
    private Upload threelevelEqualprotectionFile;

    public OfflineHospital getOfflineHospital() {
        return offlineHospital;
    }

    public void setOfflineHospital(OfflineHospital offlineHospital) {
        this.offlineHospital = offlineHospital;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }


    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getUpdateThreelevelEqualprotection() {
        return updateThreelevelEqualprotection;
    }

    public void setUpdateThreelevelEqualprotection(String updateThreelevelEqualprotection) {
        this.updateThreelevelEqualprotection = updateThreelevelEqualprotection;
    }

    public Integer getBranchNumber() {
        return branchNumber;
    }

    public void setBranchNumber(Integer branchNumber) {
        this.branchNumber = branchNumber;
    }

    public Integer getWorkForce() {
        return workForce;
    }

    public void setWorkForce(Integer workForce) {
        this.workForce = workForce;
    }

    public Integer getServiceCustomerSum() {
        return serviceCustomerSum;
    }

    public void setServiceCustomerSum(Integer serviceCustomerSum) {
        this.serviceCustomerSum = serviceCustomerSum;
    }

    public Integer getAverageDoctorsNumber() {
        return averageDoctorsNumber;
    }

    public void setAverageDoctorsNumber(Integer averageDoctorsNumber) {
        this.averageDoctorsNumber = averageDoctorsNumber;
    }

    public String getLicenceNumber() {
        return licenceNumber;
    }

    public void setLicenceNumber(String licenceNumber) {
        this.licenceNumber = licenceNumber;
    }



    public BigDecimal getPremisesArea() {
        return premisesArea;
    }

    public void setPremisesArea(BigDecimal premisesArea) {
        this.premisesArea = premisesArea;
    }

    public BigDecimal getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(BigDecimal totalIncome) {
        this.totalIncome = totalIncome;
    }

    public BigDecimal getTotalExpenditure() {
        return totalExpenditure;
    }

    public void setTotalExpenditure(BigDecimal totalExpenditure) {
        this.totalExpenditure = totalExpenditure;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getFlowAssets() {
        return flowAssets;
    }

    public void setFlowAssets(BigDecimal flowAssets) {
        this.flowAssets = flowAssets;
    }

    public BigDecimal getExternalAssets() {
        return externalAssets;
    }

    public void setExternalAssets(BigDecimal externalAssets) {
        this.externalAssets = externalAssets;
    }

    public BigDecimal getFixedAssets() {
        return fixedAssets;
    }

    public void setFixedAssets(BigDecimal fixedAssets) {
        this.fixedAssets = fixedAssets;
    }

    public BigDecimal getIntangibleAssets() {
        return intangibleAssets;
    }

    public void setIntangibleAssets(BigDecimal intangibleAssets) {
        this.intangibleAssets = intangibleAssets;
    }

    public BigDecimal getLiabilities() {
        return liabilities;
    }

    public void setLiabilities(BigDecimal liabilities) {
        this.liabilities = liabilities;
    }

    public BigDecimal getNetAssets() {
        return netAssets;
    }

    public void setNetAssets(BigDecimal netAssets) {
        this.netAssets = netAssets;
    }

    public String getThreelevelEqualprotectionCode() {
        return threelevelEqualprotectionCode;
    }

    public void setThreelevelEqualprotectionCode(String threelevelEqualprotectionCode) {
        this.threelevelEqualprotectionCode = threelevelEqualprotectionCode;
    }

    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }

    public Date getUploadedDate() {
        return uploadedDate;
    }

    public void setUploadedDate(Date uploadedDate) {
        this.uploadedDate = uploadedDate;
    }

    public Date getLicenceExpiration() {
        return licenceExpiration;
    }

    public Upload getAnnualInspectionFile() {
        return annualInspectionFile;
    }

    public void setAnnualInspectionFile(Upload annualInspectionFile) {
        this.annualInspectionFile = annualInspectionFile;
    }

    public void setLicenceExpiration(Date licenceExpiration) {
        this.licenceExpiration = licenceExpiration;
    }

    public Upload getThreelevelEqualprotectionFile() {
        return threelevelEqualprotectionFile;
    }

    public void setThreelevelEqualprotectionFile(Upload threelevelEqualprotectionFile) {
        this.threelevelEqualprotectionFile = threelevelEqualprotectionFile;
    }
}
