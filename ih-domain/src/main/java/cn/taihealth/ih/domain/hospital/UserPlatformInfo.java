package cn.taihealth.ih.domain.hospital;


import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

@Entity
@Table(name = UserPlatformInfo.TABLE_NAME,
        uniqueConstraints = {
                @UniqueConstraint(name = "U_USER_PLATFORM_HOSPITAL_USER_OPEN_ID_ALI_USER_ID", columnNames = {"hospital_id", "user_id", "open_id", "ali_user_id"})
        },
        indexes = {
                @Index(name = "IDX_USER_PLATFORM_UNION_ID", columnList = "union_id")
        }
)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class UserPlatformInfo extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_user_platform_info";

    @Comment("用户")
    @JoinColumn(name = "user_id", nullable = false
            , foreignKey = @ForeignKey(name = "FK_USER_PLATFORM_USER_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private User user;

    @Comment("unionId")
    @Column(name = "union_id")
    private String unionId;

    @Comment("医院")
    @JoinColumn(name = "hospital_id",
        foreignKey = @ForeignKey(name = "FK_USER_PLATFORM_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Comment("openId")
    @Column(name = "open_id")
    private String openId;

    @Comment("支付宝用户userId")
    @Column(name = "ali_user_id")
    private String aliUserId;

    @Comment("appId")
    @Column(name = "app_id", nullable = false)
    private String appId;

    @Column(name = "platform_type", nullable = false)
    @Enumerated(EnumType.STRING)
    @Comment("公众平台类型")
    @ColumnDefault("MINI")
    private PlatformTypeEnum platformType;

    public String getAliUserId() {
        return aliUserId;
    }

    public void setAliUserId(String aliUserId) {
        this.aliUserId = aliUserId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public PlatformTypeEnum getPlatformType() {
        return platformType;
    }

    public void setPlatformType(PlatformTypeEnum platformType) {
        this.platformType = platformType;
    }
}
