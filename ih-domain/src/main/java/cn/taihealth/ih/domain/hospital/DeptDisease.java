package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.AbstractEntity;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
@Table(name = DeptDisease.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class DeptDisease extends AbstractEntity {

    public static final String TABLE_NAME = "ih_depts_diseases";

    @Comment("科室")
    @JoinColumn(name = "dept_id",
            foreignKey = @ForeignKey(name = "FK_DEPTS_DISEASES_DEPT_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Dept dept;

    @Comment("疾病")
    @JoinColumn(name = "disease_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_DEPTS_DISEASES_DISEASE_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Disease disease;

    public Dept getDept() {
        return dept;
    }

    public void setDept(Dept dept) {
        this.dept = dept;
    }

    public Disease getDisease() {
        return disease;
    }

    public void setDisease(Disease disease) {
        this.disease = disease;
    }
}
