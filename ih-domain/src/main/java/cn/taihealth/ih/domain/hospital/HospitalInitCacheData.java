package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.dict.ExamCategory;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import com.google.common.collect.Maps;
import java.util.Map;

/**
 * @Author: Moon
 * @Date: 2021/5/24 下午3:11
 */
public class HospitalInitCacheData {

    private  Map<Long, ExamCategory> categoryMap = Maps.newHashMap();
    private  Map<Long, Dept> deptMap = Maps.newHashMap();
    private  Map<Long, OfflineDept> offlineDeptMap = Maps.newHashMap();
    private  Map<Long, Disease> diseaseMap = Maps.newHashMap();
    private  Map<Long, DiseaseFlags> diseaseFlagsMap = Maps.newHashMap();
    private  Map<Long, Long> dicDosageFormIdMap = Maps.newHashMap();
    private  Map<Long, Long> dicCategoryIdMap = Maps.newHashMap();
    private  Map<Long, Long> dicDosageUnitIdMap = Maps.newHashMap();
    private  Map<Long, Long> dicPackageUnitIdMap = Maps.newHashMap();
    private  Map<Long, Long> DicMedAdmRouteIdMap = Maps.newHashMap();
    private  Map<Long, Long> dicMedAdmFreqIdMap = Maps.newHashMap();
    private  Map<Long, HospitalDictionary> dictionaryMap = Maps.newHashMap();
    private  Map<Long, MessageTemplate> commonMsgMap = Maps.newHashMap();
    private Hospital initHospital;

    public void setCategoryMap(Map<Long, ExamCategory> categoryMap) {
        this.categoryMap = categoryMap;
    }

    public void setDeptMap(Map<Long, Dept> deptMap) {
        this.deptMap = deptMap;
    }
    public void setOfflineDeptMap(Map<Long, OfflineDept> offlineDeptMap) {
        this.offlineDeptMap = offlineDeptMap;
    }

    public void setDiseaseMap(Map<Long, Disease> diseaseMap) {
        this.diseaseMap = diseaseMap;
    }

    public void setDiseaseFlagsMap(Map<Long, DiseaseFlags> diseaseFlagsMap) {
        this.diseaseFlagsMap = diseaseFlagsMap;
    }

    public void setDicDosageFormIdMap(Map<Long, Long> dicDosageFormIdMap) {
        this.dicDosageFormIdMap = dicDosageFormIdMap;
    }

    public void setDicCategoryIdMap(Map<Long, Long> dicCategoryIdMap) {
        this.dicCategoryIdMap = dicCategoryIdMap;
    }

    public void setDicDosageUnitIdMap(Map<Long, Long> dicDosageUnitIdMap) {
        this.dicDosageUnitIdMap = dicDosageUnitIdMap;
    }

    public void setDicPackageUnitIdMap(Map<Long, Long> dicPackageUnitIdMap) {
        this.dicPackageUnitIdMap = dicPackageUnitIdMap;
    }

    public void setDicMedAdmRouteIdMap(Map<Long, Long> dicMedAdmRouteIdMap) {
        DicMedAdmRouteIdMap = dicMedAdmRouteIdMap;
    }

    public void setDicMedAdmFreqIdMap(Map<Long, Long> dicMedAdmFreqIdMap) {
        this.dicMedAdmFreqIdMap = dicMedAdmFreqIdMap;
    }

    public Map<Long, ExamCategory> getCategoryMap() {
        return categoryMap;
    }

    public Map<Long, Dept> getDeptMap() {
        return deptMap;
    }

    public Map<Long, OfflineDept> getOfflineDeptMap() {
        return offlineDeptMap;
    }

    public Map<Long, Disease> getDiseaseMap() {
        return diseaseMap;
    }

    public Map<Long, DiseaseFlags> getDiseaseFlagsMap() {
        return diseaseFlagsMap;
    }

    public Map<Long, Long> getDicDosageFormIdMap() {
        return dicDosageFormIdMap;
    }

    public Map<Long, Long> getDicCategoryIdMap() {
        return dicCategoryIdMap;
    }

    public Map<Long, Long> getDicDosageUnitIdMap() {
        return dicDosageUnitIdMap;
    }

    public Map<Long, Long> getDicPackageUnitIdMap() {
        return dicPackageUnitIdMap;
    }

    public Map<Long, Long> getDicMedAdmRouteIdMap() {
        return DicMedAdmRouteIdMap;
    }

    public Map<Long, Long> getDicMedAdmFreqIdMap() {
        return dicMedAdmFreqIdMap;
    }

    public Hospital getInitHospital() {
        return initHospital;
    }

    public void setInitHospital(Hospital initHospital) {
        this.initHospital = initHospital;
    }

    public Map<Long, HospitalDictionary> getDictionaryMap() {
        return dictionaryMap;
    }

    public void setDictionaryMap(Map<Long, HospitalDictionary> dictionaryMap) {
        this.dictionaryMap = dictionaryMap;
    }

    public Map<Long, MessageTemplate> getCommonMsgMap() {
        return commonMsgMap;
    }

    public void setCommonMsgMap(Map<Long, MessageTemplate> commonMsgMap) {
        this.commonMsgMap = commonMsgMap;
    }
}
