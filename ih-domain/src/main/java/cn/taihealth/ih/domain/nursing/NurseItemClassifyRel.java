package cn.taihealth.ih.domain.nursing;

import cn.taihealth.ih.domain.UpdatableEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

@Entity
@Table(name = NurseItemClassifyRel.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@org.hibernate.annotations.Table(appliesTo = NurseItemClassifyRel.TABLE_NAME, comment = "护理组项目分类关联")
public class NurseItemClassifyRel extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_nurse_item_classify_rel";

    @Comment("护理组")
    @JoinColumn(name = "nurse_item_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_NURSE_ITEM_CLASSIFY_REL_NURSE_ITEM_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private NurseItem nurseItem;

    @Comment("项目分类")
    @JoinColumn(name = "item_classify_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_NURSE_ITEM_CLASSIFY_REL_ITEM_CLASSIFY_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private NursingItemClassify itemClassify;

    @Column(name = "order_no")
    @Comment("排序序号")
    @ColumnDefault("0")
    private int orderNo;

    public NurseItem getNurseItem() {
        return nurseItem;
    }

    public void setNurseItem(NurseItem nurseItem) {
        this.nurseItem = nurseItem;
    }

    public NursingItemClassify getItemClassify() {
        return itemClassify;
    }

    public void setItemClassify(NursingItemClassify itemClassify) {
        this.itemClassify = itemClassify;
    }

    public int getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(int orderNo) {
        this.orderNo = orderNo;
    }
}
