package cn.taihealth.ih.domain.crm;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;

/**
 */
@Entity
@Table(name = CrmTaskDetailResult.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class CrmTaskDetailResult extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_crm_task_detail_results";


    // TODO
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id",
            foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAIL_RESULTS_HOSPITAL_ID"))
    private Hospital hospital;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_detail_id",
        foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAIL_RESULTS_TASK_DETAIL_ID"))
    private CrmTaskDetail taskDetail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "questionnaire_id",
            foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAIL_RESULTS_QUESTIONNAIRE_ID"))
    private CrmQuestionnaire questionnaire;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "answerer_id",
            foreignKey = @ForeignKey(name = "FK_CRM_TASK_DETAIL_RESULTS_ANSWERER_ID"))
    private User answerer;

    @Column(name = "result_code")
    private String resultCode;

    @Column(name = "remark")
    private String remark;

    @Column(name = "score")
    private Integer score;

    @Column(name = "score_rate")
    private Double scoreRate;

    @Column(name = "dept_id")
    private Long deptId;

    @Comment("线下就诊医院id")
    @Column(name = "visit_hospital_id")
    private Long visitHospitalId;

    @Comment("线下渠道id")
    @Column(name = "channel_id")
    private Long channelId;

    @Column(name = "name")
    private String name;

    @Column(name = "terminate_reason_id")
    private Long terminateReasonId;

    @Column(name = "terminate_reason")
    private String terminateReason;

    @Column(name = "result_time")
    private Date resultTime;

    public CrmTaskDetail getTaskDetail() {
        return taskDetail;
    }

    public void setTaskDetail(CrmTaskDetail taskDetail) {
        this.taskDetail = taskDetail;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Long getTerminateReasonId() {
        return terminateReasonId;
    }

    public void setTerminateReasonId(Long terminateReasonId) {
        this.terminateReasonId = terminateReasonId;
    }

    public String getTerminateReason() {
        return terminateReason;
    }

    public void setTerminateReason(String terminateReason) {
        this.terminateReason = terminateReason;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public User getAnswerer() {
        return answerer;
    }

    public void setAnswerer(User answerer) {
        this.answerer = answerer;
    }

    public CrmQuestionnaire getQuestionnaire() {
        return questionnaire;
    }

    public void setQuestionnaire(CrmQuestionnaire questionnaire) {
        this.questionnaire = questionnaire;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public Date getResultTime() {
        return resultTime;
    }

    public void setResultTime(Date resultTime) {
        this.resultTime = resultTime;
    }

    public Double getScoreRate() {
        return scoreRate;
    }

    public void setScoreRate(Double scoreRate) {
        this.scoreRate = scoreRate;
    }

    public Long getVisitHospitalId() {
        return visitHospitalId;
    }

    public void setVisitHospitalId(Long visitHospitalId) {
        this.visitHospitalId = visitHospitalId;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }
}
