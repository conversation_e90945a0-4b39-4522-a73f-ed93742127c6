package cn.taihealth.ih.domain.tim;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = TencentMessageRecording.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class TencentMessageRecording extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_tencent_message_recording";

    @Comment("")
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_TENCENT_MESSAGE_RECORDING_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;


    /**
     * groupId
     */
    @Comment("groupId")
    @Column(name = "group_id")
    private String groupId;

    /**
     * orderId
     */
    @Comment("orderId")
    @Column(name = "order_id")
    private long orderId;

    /**
     * 本录制文件在ih的upload
     */
    @Comment("本录制文件在ih的upload")
    @ManyToOne
    @JoinColumn(name = "upload_id", foreignKey = @ForeignKey(name = "FK_TENCENT_MESSAGE_RECORDING_UPLOAD_ID"))
    private Upload upload;

    /**
     * 压缩包在His的唯一 ID
     */
    @Comment("压缩包在His的唯一 ID")
    @Column(name = "his_file_id")
    private String hisFileId;


    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public long getOrderId() {
        return orderId;
    }

    public void setOrderId(long orderId) {
        this.orderId = orderId;
    }

    public Upload getUpload() {
        return upload;
    }

    public void setUpload(Upload upload) {
        this.upload = upload;
    }

    public String getHisFileId() {
        return hisFileId;
    }

    public void setHisFileId(String hisFileId) {
        this.hisFileId = hisFileId;
    }
}
