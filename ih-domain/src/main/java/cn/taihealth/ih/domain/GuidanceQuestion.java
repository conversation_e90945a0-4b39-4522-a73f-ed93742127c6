package cn.taihealth.ih.domain;

import cn.taihealth.ih.domain.enums.GuidenceQuestionTypeEnum;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Type;

/**
 * <AUTHOR>
 * @Date 2020/9/27
 */
@Entity
@Table(name = GuidanceQuestion.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class GuidanceQuestion extends UpdatableEntity {
  public static final String TABLE_NAME = "ih_guidance_questions";

  @Column(name = "question", length = 200, nullable = false, columnDefinition = "VARCHAR(200) COMMENT '问题'")
  @ColumnDefault("-1")
  @Type(type = "string")
  private String question;

  @Column(name = "question_type", length = 4, nullable = false, columnDefinition = "INT(4) COMMENT '问题类型'")
  @ColumnDefault("-1")
  private GuidenceQuestionTypeEnum questionType;

  @Column(name = "question_seq", length = 4, nullable = false, columnDefinition = "INT(4) COMMENT '问题序号'")
  @ColumnDefault("-1")
  private Integer questionSeq;

  public String getQuestion() {
    return question;
  }

  public void setQuestion(String question) {
    this.question = question;
  }

  public GuidenceQuestionTypeEnum getQuestionType() {
    return questionType;
  }

  public void setQuestionType(GuidenceQuestionTypeEnum questionType) {
    this.questionType = questionType;
  }

  public Integer getQuestionSeq() {
    return questionSeq;
  }

  public void setQuestionSeq(Integer questionSeq) {
    this.questionSeq = questionSeq;
  }
}
