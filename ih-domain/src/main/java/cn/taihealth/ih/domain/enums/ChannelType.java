package cn.taihealth.ih.domain.enums;

public enum ChannelType {
    /**
     * 线上就诊
     */
    ONLINE("线上就诊", "1"),
    /**
     * 线下就诊
     */
    OFFLINE("线下就诊", "0"),
    /**
     * 全部
     */
    WHOLE("全部", "-1");

    private final String name;
    private final String code;

    private ChannelType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static ChannelType getByCode(String code) {
        switch (code) {
            case "1":
                return ONLINE;
            case "0":
                return OFFLINE;
            case "-1":
                return WHOLE;
            default:
                return null;
        }
    }

}
