package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.commons.util.TimeUtils.Shift;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.dict.ExamDevice;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = ExamSchedule.TABLE_NAME, indexes = {
        @Index(name = "IDX_EXAM_SCHEDULES_STATUS", columnList = "status"),
        @Index(name = "IDX_EXAM_SCHEDULES_START_TIME", columnList = "start_time"),
        @Index(name = "IDX_EXAM_SCHEDULES_END_TIME", columnList = "end_time")
})
@org.hibernate.annotations.Table(appliesTo = ExamSchedule.TABLE_NAME, comment = "设备排班表")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ExamSchedule extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_exam_schedules";

    public enum NumberSourceStatus {
        LOCKED("已锁定"),
        CLOSED("停诊");

        private NumberSourceStatus(String name) {
            this.name = name;
        }

        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_EXAM_SCHEDULES_HOSPITAL_ID"))
    private Hospital hospital;

    @Comment("排班计划")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "schedule_group_id",
        foreignKey = @ForeignKey(name = "FK_EXAM_SCHEDULES_SCHEDULE_GROUP_ID"))
    private ExamScheduleGroup scheduleGroup;

    @Comment("设备")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "device_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_EXAM_SCHEDULES_DEVICE_ID"))
    private ExamDevice device;

    @Comment("开始时间")
    @Column(name = "start_time", nullable = false)
    private Date startTime;

    @Comment("结束时间")
    @Column(name = "end_time", nullable = false)
    private Date endTime;

    @Comment("创建人ID")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_EXAM_SCHEDULES_CREATOR_ID"))
    private User creator;

    @Comment("修改人ID")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "editor_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_EXAM_SCHEDULES_EDITOR_ID"))
    private User editor;

    /**
     * 根据时间段确定的号源状态
     */
    @Comment("号源状态")
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private NumberSourceStatus status;

    /**
     * 排班设备渠道号源分配信息
     */
    @OneToMany(mappedBy = "examSchedule", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<ExamScheduleNumberSource> numberSourceList;

    public ExamSchedule() {
    }

    public ExamSchedule(User user) {
        this.creator = user;
        this.editor = user;
    }

    @Transient
    @JsonIgnore
    public Shift getShift() {
        return TimeUtils.getShift(startTime, endTime);
    }

    public ExamDevice getDevice() {
        return device;
    }

    public void setDevice(ExamDevice examDevice) {
        this.device = examDevice;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public User getCreator() {
        return creator;
    }

    public void setCreator(User creator) {
        this.creator = creator;
    }

    public User getEditor() {
        return editor;
    }

    public void setEditor(User editor) {
        this.editor = editor;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public ExamScheduleGroup getScheduleGroup() {
        return scheduleGroup;
    }

    public void setScheduleGroup(ExamScheduleGroup scheduleGroup) {
        this.scheduleGroup = scheduleGroup;
    }

    public List<ExamScheduleNumberSource> getNumberSourceList() {
        return numberSourceList;
    }

    public void setNumberSourceList(List<ExamScheduleNumberSource> numberSourceList) {
        this.numberSourceList = numberSourceList;
    }

    public NumberSourceStatus getStatus() {
        return status;
    }

    public void setStatus(NumberSourceStatus status) {
        this.status = status;
    }
}
