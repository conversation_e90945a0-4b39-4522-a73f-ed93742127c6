package cn.taihealth.ih.domain.cloud;

import cn.taihealth.ih.domain.AbstractEntity;
import org.hibernate.annotations.Comment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 */
@Entity
@Table(name = "ih_node_info")
public class NodeInfo extends AbstractEntity {

    public enum ServerType {
        API,
        ADMIN,
        H5,
        WEBSOCKET
    }

    @Comment("实例ID")
    @Column(name = "instance_id", nullable = false, unique = true)
    private String instanceId;

    @Comment("服务类型")
    @Column(name = "server_type")
    private ServerType serverType = ServerType.API;

    @Comment("数据版本")
    @Column(name = "data_version")
    private String dataVersion;

    @Comment("启动时间")
    @Column(name = "start_time")
    private Date startTime = new Date();

    @Comment("mac地址")
    @Column(name = "mac_address")
    private String macAddress;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public ServerType getServerType() {
        return serverType;
    }

    public void setServerType(ServerType serverType) {
        this.serverType = serverType;
    }

    public String getDataVersion() {
        return dataVersion;
    }

    public void setDataVersion(String dataVersion) {
        this.dataVersion = dataVersion;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }
}
