package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

/**
 * 热门科室
 * <AUTHOR>
 */
@Entity
@Table(name = RecommendDept.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class RecommendDept extends UpdatableEntity {
    public static final String TABLE_NAME = "ih_recommend_dept";

    @Comment("科室")
    @JoinColumn(name = "dept_id",
            foreignKey = @ForeignKey(name = "FK_RECOMMEND_DEPT_ID"))
    @OneToOne(fetch = FetchType.LAZY)
    private Dept dept;

    /**
     * 排序序号
     */
    @Comment("排序序号")
    @Column(name = "sort_num")
    @ColumnDefault("0")
    private int sortNum;

    /**
     * 图标标签(最多三个字)
     */
    @Comment("图标标签(最多三个字)")
    @Column(name = "icon_tag")
    private String iconTag;


    public Dept getDept() {
        return dept;
    }

    public void setDept(Dept dept) {
        this.dept = dept;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getIconTag() {
        return iconTag;
    }

    public void setIconTag(String iconTag) {
        this.iconTag = iconTag;
    }

}
