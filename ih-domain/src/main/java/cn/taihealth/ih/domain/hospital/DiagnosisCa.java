package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 诊断单签章表
 */
@Entity
@Table(name = DiagnosisCa.TABLE_NAME,
        indexes = {
                @Index(name = "IDX_DIAGNOSIS_CA_DIAGNOSIS_PDF_UID", columnList = "diagnosis_pdf_uid")
        }
)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class DiagnosisCa extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_diagnosis_cas";

    @Comment("医院")
    @Column(name = "hospital_id")
    private Long hospitalId;

    /* 订单表id */
    @Comment("订单")
    @Column(name = "order_id", nullable = false)
    private Long orderId;

    /**
     * 医生id
     */
    @Comment("医生")
    @Column(name = "doctor_id")
    private Long doctorId;

    /**
     * 医生在ca系统用户名
     */
    @Comment("医生在ca系统用户名")
    @Column(name = "doctor_ca_username")
    private String doctorCaUsername;

    /**
     * 医生签章pdf文件标识， 信安世纪使用random
     */
    @Comment("医生签章pdf文件标识")
    @Column(name = "diagnosis_pdf_uid")
    private String diagnosisPdfUid;

    /**
     * 医生未签章pdf文件
     */
    @Comment("医生签章pdf文件")
    @Column(name = "diagnosis_pdf_no_ca_id")
    private Long diagnosisPdfNoCaId;
    /**
     * 医生未签章pdf文件
     */
    @Comment("医生未签章pdf文件")
    @Column(name = "diagnosis_pdf_id")
    private Long diagnosisPdfId;

    /**
     * 医生签章pdf文件hash
     */
    @Comment("医生签章pdf文件hash")
    @Column(name = "diagnosis_pdf_file_hash")
    private String diagnosisPdfFileHash;

    /**
     * 签章后的pdf摘要值
     */
    @Comment("签章后的pdf摘要值")
    @Column(name = "diagnosis_digest_value")
    private String diagnosisDigestValue;

    @Comment("签名原文")
    @Column(name = "sign_raw")
    @Type(type = "org.hibernate.type.TextType")
    private String signRaw;

    @Comment("签名值")
    @Column(name = "sign_data")
    private String signData;

    @Comment("医生证书序列号")
    @Column(name = "sign_sn")
    private String signSn;

    public DiagnosisCa() {
    }

    public DiagnosisCa(Long hospitalId, Long orderId) {
        this.hospitalId = hospitalId;
        this.orderId = orderId;
    }

    public Long getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public String getDoctorCaUsername() {
        return doctorCaUsername;
    }

    public void setDoctorCaUsername(String doctorCaUsername) {
        this.doctorCaUsername = doctorCaUsername;
    }

    public String getDiagnosisPdfUid() {
        return diagnosisPdfUid;
    }

    public void setDiagnosisPdfUid(String diagnosisPdfUid) {
        this.diagnosisPdfUid = diagnosisPdfUid;
    }

    public Long getDiagnosisPdfNoCaId() {
        return diagnosisPdfNoCaId;
    }

    public void setDiagnosisPdfNoCaId(Long diagnosisPdfNoCaId) {
        this.diagnosisPdfNoCaId = diagnosisPdfNoCaId;
    }

    public Long getDiagnosisPdfId() {
        return diagnosisPdfId;
    }

    public void setDiagnosisPdfId(Long diagnosisPdfId) {
        this.diagnosisPdfId = diagnosisPdfId;
    }

    public String getDiagnosisPdfFileHash() {
        return diagnosisPdfFileHash;
    }

    public void setDiagnosisPdfFileHash(String diagnosisPdfFileHash) {
        this.diagnosisPdfFileHash = diagnosisPdfFileHash;
    }

    public String getDiagnosisDigestValue() {
        return diagnosisDigestValue;
    }

    public void setDiagnosisDigestValue(String diagnosisDigestValue) {
        this.diagnosisDigestValue = diagnosisDigestValue;
    }

    public String getSignRaw() {
        return signRaw;
    }

    public void setSignRaw(String signRaw) {
        this.signRaw = signRaw;
    }

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public String getSignSn() {
        return signSn;
    }

    public void setSignSn(String signSn) {
        this.signSn = signSn;
    }
}
