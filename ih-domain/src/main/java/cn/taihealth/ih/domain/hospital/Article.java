package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.MarkupType;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.ArticleStatus;
import com.google.common.collect.Lists;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Entity
@Table(name = Article.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class Article extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_articles";

    public enum ArticleType {
        REGULAR,
        VIDEO
    }

    public enum ArticleClient {
        DOCTOR,
        PATIENT
    }

    @org.hibernate.annotations.Comment("医院ID")
    @JoinColumn(name = "hospital_id",
        foreignKey = @ForeignKey(name = "FK_ARTICLES_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @OneToMany(mappedBy = "article", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<ArticleAuthor> articleAuthors = Lists.newArrayList();

    @org.hibernate.annotations.Comment("标题")
    @Column(name = "title", length = 128, nullable = false)
    private String title;

    @org.hibernate.annotations.Comment("用户端")
    @Column(name = "article_client", nullable = false)
    @Enumerated(EnumType.STRING)
    private ArticleClient articleClient = ArticleClient.PATIENT;

    //    @Column(name = "original_authors")
//    private String originalAuthors;
    @org.hibernate.annotations.Comment("类型")
    @Column(name = "article_type", nullable = false)
    private ArticleType articleType = ArticleType.REGULAR;

    /**
     * 文章状态，如果需要草稿，改这个状态
     */
    @org.hibernate.annotations.Comment("文章状态")
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("PUBLISHED")
    private ArticleStatus status = ArticleStatus.PUBLISHED;

    @org.hibernate.annotations.Comment("构成类型 HTML/MARKDOWN/TEXT")
    @Column(name = "markup_type")
    private MarkupType markupType = MarkupType.HTML;

    @org.hibernate.annotations.Comment("原文")
    @Column(name = "raw")
    @Type(type = "org.hibernate.type.TextType")
    private String raw;

    @Column(name = "html")
    @org.hibernate.annotations.Comment("HTML内容")
    @Type(type = "org.hibernate.type.TextType")
    private String html;

    @org.hibernate.annotations.Comment("封面图片URL")
    @Column(name = "cover_image_url", nullable = false)
    private String coverImageUrl;

    @org.hibernate.annotations.Comment("来源")
    @Column(name = "source", length = 64)
    private String source = "原创";

    @org.hibernate.annotations.Comment("原文链接")
    @Column(name = "original_url")
    private String originalUrl;

    @org.hibernate.annotations.Comment("原文作者")
    @Column(name = "original_author")
    private String originalAuthor;

    @org.hibernate.annotations.Comment("引用")
    @Column(name = "excerpt")
    private String excerpt;

    @org.hibernate.annotations.Comment("阅读量")
    @Column(name = "view_count")
    private int viewCount;

    @org.hibernate.annotations.Comment("假的阅读量")
    @Column(name = "fake_view_count")
    private int fakeViewCount;

    @org.hibernate.annotations.Comment("评论数量")
    @Column(name = "comment_count")
    private int commentCount;

    @org.hibernate.annotations.Comment("收藏数量")
    @Column(name = "bookmark_count")
    private int bookmarkCount;

    @org.hibernate.annotations.Comment("喜欢数量")
    @Column(name = "like_count")
    private int likeCount;

    @org.hibernate.annotations.Comment("是否显示")
    @Column(name = "is_show")
    private boolean show = true;

    @org.hibernate.annotations.Comment("编辑人")
    @JoinColumn(name = "modifier",
        foreignKey = @ForeignKey(name = "FK_ARTICLES_MODIFIER"))
    @ManyToOne(fetch = FetchType.LAZY)
    private User modifier;

    @org.hibernate.annotations.Comment("发布时间")
    @Column(name = "published_date")
    private Date publishedDate;

    @org.hibernate.annotations.Comment("编辑时间")
    @Column(name = "modified_date")
    private Date modifiedDate;

    /**
     * 拒绝原因
     */
    @org.hibernate.annotations.Comment("拒绝原因")
    @Column(name = "reject_reason")
    @Type(type = "org.hibernate.type.TextType")
    private String rejectReason;

    @OneToMany(mappedBy = "article", cascade = CascadeType.REMOVE)
    private List<Comment> comments = Lists.newArrayList();
    @OneToMany(mappedBy = "article", cascade = CascadeType.REMOVE)
    private List<ArticleVisit> articleVisits = Lists.newArrayList();
    @OneToMany(mappedBy = "article", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<ArticleCategory> articleCategories = Lists.newArrayList();

    @org.hibernate.annotations.Comment("音频文件ID")
    @ManyToOne
    @JoinColumn(name = "audio_id",
        foreignKey = @ForeignKey(name = "FK_ARTICLES_AUDIO_ID"))
    private Upload audio;

    @org.hibernate.annotations.Comment("视频文件ID")
    @ManyToOne
    @JoinColumn(name = "video_id",
        foreignKey = @ForeignKey(name = "FK_ARTICLES_VIDEO_ID"))
    private Upload video;

    @org.hibernate.annotations.Comment("视频图片URL")
    @Column(name = "video_image_url")
    private String videoImageUrl;

    public User getModifier() {
        return modifier;
    }

    public void setModifier(User modifier) {
        this.modifier = modifier;
    }

    public List<ArticleAuthor> getArticleAuthors() {
        return articleAuthors;
    }

    public void setArticleAuthors(List<ArticleAuthor> articleAuthors) {
        this.articleAuthors = articleAuthors;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }
    //    public String getOriginalAuthors() {
//        return originalAuthors;
//    }
//
//    public void setOriginalAuthors(String originalAuthors) {
//        this.originalAuthors = originalAuthors;
//    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public ArticleType getArticleType() {
        return articleType;
    }

    public void setArticleType(ArticleType articleType) {
        this.articleType = articleType;
    }

    public MarkupType getMarkupType() {
        return markupType;
    }

    public void setMarkupType(MarkupType markupType) {
        this.markupType = markupType;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public String getRaw() {
        return raw;
    }

    public void setRaw(String raw) {
        this.raw = raw;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public String getExcerpt() {
        return excerpt;
    }

    public void setExcerpt(String excerpt) {
        this.excerpt = excerpt;
    }

    public String getOriginalUrl() {
        return originalUrl;
    }

    public void setOriginalUrl(String originalUrl) {
        this.originalUrl = originalUrl;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getOriginalAuthor() {
        return originalAuthor;
    }

    public void setOriginalAuthor(String originalAuthor) {
        this.originalAuthor = originalAuthor;
    }

    public int getViewCount() {

        return viewCount;
    }

    public void setViewCount(int viewCount) {
        this.viewCount = viewCount;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public int getBookmarkCount() {
        return bookmarkCount;
    }

    public void setBookmarkCount(int bookmarkCount) {
        this.bookmarkCount = bookmarkCount;
    }

    public int getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(int likeCount) {
        this.likeCount = likeCount;
    }

    public Date getPublishedDate() {
        return publishedDate;
    }

    public void setPublishedDate(Date publishedDate) {
        this.publishedDate = publishedDate;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public List<Comment> getComments() {
        return comments;
    }

    public void setComments(List<Comment> comments) {
        this.comments = comments;
    }

    public List<ArticleVisit> getArticleVisits() {
        return articleVisits;
    }

    public void setArticleVisits(List<ArticleVisit> articleVisits) {
        this.articleVisits = articleVisits;
    }

    public Upload getAudio() {
        return audio;
    }

    public void setAudio(Upload audio) {
        this.audio = audio;
    }

    public Upload getVideo() {
        return video;
    }

    public void setVideo(Upload video) {
        this.video = video;
    }

    public List<ArticleCategory> getArticleCategories() {
        return articleCategories;
    }

    public void setArticleCategories(List<ArticleCategory> articleCategories) {
        this.articleCategories = articleCategories;
    }

    public ArticleClient getArticleClient() {
        return articleClient;
    }

    public void setArticleClient(ArticleClient articleClient) {
        this.articleClient = articleClient;
    }

    public int getFakeViewCount() {
        return fakeViewCount;
    }

    public void setFakeViewCount(int fakeViewCount) {
        this.fakeViewCount = fakeViewCount;
    }

    public String getVideoImageUrl() {
        return videoImageUrl;
    }

    public void setVideoImageUrl(String videoImageUrl) {
        this.videoImageUrl = videoImageUrl;
    }

    public ArticleStatus getStatus() {
        return status;
    }

    public void setStatus(ArticleStatus status) {
        this.status = status;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }
}
