package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = MedicalWorkerAssistantRel.TABLE_NAME, uniqueConstraints = {

})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class MedicalWorkerAssistantRel extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_medical_assistant_rel";


    @Comment("医生")
    @JoinColumn(name = "doctor_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_MEDICAL_ASSISTANT_REL_DOCTOR_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private MedicalWorker doctor;

    @Comment("医助")
    @JoinColumn(name = "assistant_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_MEDICAL_ASSISTANT_REL_ASSISTANT_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private MedicalWorker assistant;

    public MedicalWorker getDoctor() {
        return doctor;
    }

    public void setDoctor(MedicalWorker doctor) {
        this.doctor = doctor;
    }

    public MedicalWorker getAssistant() {
        return assistant;
    }

    public void setAssistant(MedicalWorker assistant) {
        this.assistant = assistant;
    }
}
