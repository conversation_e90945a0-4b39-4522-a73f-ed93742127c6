package cn.taihealth.ih.domain.enums;

/**
 */
public enum UploadType {
    PUBLIC(true),              // 通用，公开的
    PRIVATE(false),             // 通用，私有的
    TIM_HISTORY(false),         // 腾讯IM History
    AVATAR(true),              // 头像
    ARTICLE(true),             // 文章
    CERT(false),                // 证件
    MEDICAL(false),             // 病历类
    BACKEND_SPLIT(false),        // 微信文件，由服务器拼接并转码
    FACE(false),
    HOSPITAL(false), // 医院管理员上传的医院图片类型
    SYSTEM(false) ,// 系统文件
    PRESCRIPTION(false), // 处方
    ARCHIVE(true), // 归档文件
    ;

    private final boolean publiclyVisible;

    UploadType(boolean publiclyVisible) {
        this.publiclyVisible = publiclyVisible;
    }

    public boolean isPubliclyVisible() {
        return publiclyVisible;
    }

}
