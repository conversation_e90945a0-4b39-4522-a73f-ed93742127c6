package cn.taihealth.ih.domain.enums;


import cn.taihealth.ih.commons.web.dataType.ExtraDataTypes;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointment;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import com.gitq.jedi.common.datatype.DataType;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.datatype.EnumType;
import com.gitq.jedi.common.datatype.ListType;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR> moon
 */

@SuppressWarnings("rawtypes")
public enum HospitalSettingKey {

//    DEMO_STRING("示例字符串", SettingCategory.GENERAL, "", DataTypes.STRING, true),
//    DEMO_TEXT("示例文本", SettingCategory.GENERAL, "", DataTypes.TEXT, true),
//    DEMO_BOOLEAN("示例布尔值", SettingCategory.GENERAL, false, DataTypes.BOOLEAN, true),
//    DEMO_INTEGER("示例整数", SettingCategory.GENERAL, 1, DataTypes.INTEGER, true),
//    DEMO_LONG("示例长整形", SettingCategory.GENERAL, 1L, DataTypes.LONG, true),
//    DEMO_URL("示例URL", SettingCategory.GENERAL, "www.baidu.com", DataTypes.URL, true),
//    DEMO_DOUBLE("示例双精度小数", SettingCategory.GENERAL, 0.0, DataTypes.DOUBLE, true),
//    DEMO_FLOAT("示例小数", SettingCategory.GENERAL, 0.0f, DataTypes.FLOAT, true),
//    DEMO_PASSWORD("示例密码", SettingCategory.GENERAL, "123456", DataTypes.PASSWORD, true),
//    DEMO_ENUM("示例单选", SettingCategory.GENERAL, "", new EnumType(NotificationMethod.class), true),

    DEMO_LIST_STRING("示例字符组, 医院看不到", SettingCategory.GENERAL, Lists.newArrayList(), DataTypes.LIST_STRING,
            false),
    PAYMENT_INTERVAL_LIMIT("支付间隔时间（单位: 秒），连续两次支付下单的时间间隔限制（门诊缴费立即支付，预约挂号立即支付）",
            SettingCategory.TEST, NotifyCategory.UNKNOWN, 10, DataTypes.LONG, false, false, -1),
    //    DEMO_LIST_ENUM("示例多选", SettingCategory.GENERAL, Lists.newArrayList(),
//        new ListType(new EnumType(NotificationMethod.class)), true),
    HOSPITAL_OPEN_APP_TYPE("医院开通的应用类型", SettingCategory.GENERAL, HospitalSettingKey.NotifyCategory.UNKNOWN,
            Lists.newArrayList(HospitalAppCategory.OUTPATIENT.name()), new ListType(new EnumType(HospitalAppCategory.class)), false,
            new Enum[]{HospitalAppCategory.OUTPATIENT, HospitalAppCategory.INTPATIENT, HospitalAppCategory.ONLINE_CONSULT},
            new ClientType[]{ClientType.PATIENT}, false, -1),
    MUTUAL_RECOGNITION_NOTICE_TYPE("医院开通的互认类型：INSPECT： 预约挂号成功后，提示患者是否同意调阅历史检查检验结果\n"
                                       + "FILM_PAYMENT： 门诊缴费时，提示患者是否打印胶片，若不打印，则不收取胶片费", SettingCategory.GENERAL,
                                   Lists.newArrayList(),
                                   new ListType(new EnumType(HospitalMutualRecognitionCategory.class)), 3),
    ARTICLE_NEED_VERIFY("文章是否需要审核，关闭时不需要审核就会发布", SettingCategory.GENERAL, true, DataTypes.BOOLEAN),
    PC_LOGIN_LEFT_URL("PC端登录页面左侧图片URL", SettingCategory.PAGE_CONFIGURATION, "", DataTypes.URL, 1),
    PC_LOGIN_LOGO_URL("PC端登录页面logo URL", SettingCategory.PAGE_CONFIGURATION, "", DataTypes.URL, 2),
    APP_REPORT_QUERY_TAB("患者端小程序报告查询入口中显示哪些tab页", SettingCategory.PAGE_CONFIGURATION,
                         Lists.newArrayList(ReportQueryTab.EXAM_REPORT.name(),
                         ReportQueryTab.PHYSICAL_REPORT.name(),
                         ReportQueryTab.TEST_REPORT.name()),
                         new ListType(new EnumType(ReportQueryTab.class)), 3),
    ORDER_OLD_PEOPLE_REGISTER_TYPE("老年挂号可用类型", SettingCategory.ORDER,
                         Lists.newArrayList(OldOrderMethodEnum.DEPT.name()),
                         new ListType(new EnumType(OldOrderMethodEnum.class)), 3),
    SHOW_INTRODUCTION("患者端医生主页是否显示医生简介", SettingCategory.PAGE_CONFIGURATION, false, DataTypes.BOOLEAN, 3),

    SHOW_FAVORABLE_SERVICE("患者端医生主页是否显示医生好评率和服务人数（预约挂号）", SettingCategory.PAGE_CONFIGURATION, true, DataTypes.BOOLEAN, 4),

    ROTATE_PICTURES("公众号首页轮播图片", SettingCategory.DEFAULT, Lists.newArrayList(), DataTypes.LIST_STRING, 1),
    /**
     * 患者黑名单规则设置
     */
    PATIENT_BLACKLIST_SETTINGS("患者黑名单规则设置", SettingCategory.GENERAL, NotifyCategory.UNKNOWN, "{}",
            ExtraDataTypes.JSON_TYPE, false, true, -1),
    /**
     * 用户端URL前缀
     */
    NOTIFY_USR_URL_PREFIX("用户端地址", SettingCategory.GENERAL, "https://u.xxx.cn/", DataTypes.URL, 2),

    SMS_ENABLED("是否开启发送短信", SettingCategory.GENERAL, false, DataTypes.BOOLEAN, 4),
    PRESCRIPTION_EXPIRED_TIME("处方过期时间配置(天)", SettingCategory.GENERAL, 3, DataTypes.INTEGER, 5),
    MAIL_USERNAME("邮箱账户", SettingCategory.GENERAL, "", DataTypes.STRING, 6),
    FROM_MAIL("发送邮箱", SettingCategory.GENERAL, "", DataTypes.STRING, 6),
    MAIL_TYPE("邮箱类型", SettingCategory.GENERAL, "", new EnumType(MailType.class), 7),
    MAIL_HOST("邮件服务器地址", SettingCategory.GENERAL, "", DataTypes.STRING, 7),
    MAIL_PASSWORD("邮箱SMTP授权码", SettingCategory.GENERAL, "", DataTypes.STRING, 8),
    PATIENT_QR_CODES("患者就诊码类型", SettingCategory.GENERAL, List.of(QRCodeEnum.QR_CODE_ENUM.name(), QRCodeEnum.BAR_CODE.name()),
            new ListType(new EnumType(QRCodeEnum.class)), true, 0),
    APPOINTMENT_PAYMENT_REGISTRATION("是：预约时需支付挂号费；否：先预约，就诊当天支付", SettingCategory.APPOINTMENT, true, DataTypes.BOOLEAN, 8),
    // 普通号可预约挂号天数（线下预约挂号）
    NORMAL_REGISTER_DAYS("预约挂号的可预约天数", SettingCategory.APPOINTMENT, 15, DataTypes.INTEGER, 9),
    SPECIAL_REGISTER_DAYS("专家号可预约挂号天数（线下预约挂号）", SettingCategory.APPOINTMENT, 15, DataTypes.INTEGER, false, 10),
    ONLINE_REGISTER_DAYS("在线预约可预约挂号天数（线上咨询复诊）", SettingCategory.APPOINTMENT, 15, DataTypes.INTEGER, false, 11),
    /**
     * 没用了
     */
    @Deprecated
    MULTI_RECOMMEND_IN_SAME_TIME("同一时间段内是否允许推荐多次检查", SettingCategory.APPOINTMENT, false,
            DataTypes.BOOLEAN, false),
    EXPERT_APPOINTMENT_TYPE("线上医生的挂号模式\n" +
            "SCHEDULE_MODEL: 线上医生需要排班并且选择在线门诊与咨询\n" +
            "ONLINE_MODEL: 线上医生不需要排班并且自己决定接诊服务类型以及服务开启关闭\n" +
            "此配置先隐藏，默认值为SCHEDULE_MODEL", SettingCategory.APPOINTMENT, ExpertAppointmentTypeEnum.SCHEDULE_MODEL,
            new EnumType(ExpertAppointmentTypeEnum.class), false),

    // key写错了，应该是就诊人能线上退"今天和今天之前"的号
    CAN_REFUND_ORDER_THE_SAME_DAY_OR_LATER("就诊人能线上退\"今天和今天之前\"的号", SettingCategory.ORDER, true, DataTypes.BOOLEAN, 12),
    PAY_ORDER_LIMIT_TIME("订单支付时限(单位: 分)", SettingCategory.ORDER, 20, DataTypes.INTEGER, 13),

    WAITING_ROOM_LIMIT_TIME("候诊室等待时限(单位: 分)", SettingCategory.ORDER, 20, DataTypes.INTEGER, 13),

    WAITING_ROOM_NOTICE_TIME("候诊室提醒时间(单位: 分)", SettingCategory.ORDER, 20, DataTypes.INTEGER, 13),
    CONSULT_TOTAL_LIMIT_TIME("咨询总时限(单位: 分)", SettingCategory.ORDER, 20, DataTypes.INTEGER, 13),

    EVALUATE_TAG("订单评价标签(json格式)", SettingCategory.ORDER, "", DataTypes.STRING, 14),

    DRUG_PAY_ORDER_LIMIT_TIME("药品订单支付时限(单位: 分)", SettingCategory.ORDER, 15, DataTypes.INTEGER, 14),

    AUTO_CONFIRM_RECEIVED_TIME("自动确认收货超时(单位: 天)", SettingCategory.ORDER, 7, DataTypes.INTEGER, 14),
    RECIPE_DISABLED_TIME("处方失效时间(单位: 小时)", SettingCategory.ORDER, 72, DataTypes.INTEGER, 14),
    DRUG_EXPRESS_COST("处方物流费用（单位：人民币分）", SettingCategory.ORDER, 72, DataTypes.INTEGER, 14),
    DRUG_OFFLINE_GET_ADDRESS("线下药品自取地址", SettingCategory.ORDER, 72, DataTypes.STRING, 14),
    USE_OFFLINE_SIGN_IN("是否开启预约挂号签到", SettingCategory.APPOINTMENT, false, DataTypes.BOOLEAN, 14),
    SIGN_IN_RANGE("预约挂号签到感应半径（米）", SettingCategory.APPOINTMENT, 100, DataTypes.INTEGER, 14),

    CHECK_MODIFY_LIMIT_TIME("检查预约修改预约时限(单位: 小时)", SettingCategory.APPOINTMENT, 12, DataTypes.INTEGER, 14),
    CHECK_SIGN_IN_RANGE("检查预约签到感应半径（米）", SettingCategory.APPOINTMENT, 100, DataTypes.INTEGER, 14),
    APPOINTMENT_NEED_PICK_UP("预约挂号是否需要取号", SettingCategory.APPOINTMENT, true, DataTypes.BOOLEAN, 15),
    REGISTER_REMINDER_TIME("预约挂号提醒时间,前一天的几点,小于0和大于23表示不提醒", SettingCategory.APPOINTMENT, -1, DataTypes.INTEGER, 16),
    REG_DEPT_SHOW_LEVEN("预约挂号科室显示几级", SettingCategory.APPOINTMENT, NotifyCategory.ORDER, DeptLevel.SECOND_LEVEL,
            new EnumType(DeptLevel.class), new Enum[] {DeptLevel.SECOND_LEVEL, DeptLevel.THIRD_LEVEL}, 17),

    ORDER_STATUS_ENABLE_TO_DELETE("用户可删除订单的状态", SettingCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(OrderStatus.class)), 15),
    DRUG_ORDER_STATUS_ENABLE_TO_REFUND("用户可退款药品订单的状态", SettingCategory.ORDER, NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(DrugOrderStatus.class)),
            new Enum[]{DrugOrderStatus.PLACE_DRUG,
                    DrugOrderStatus.SEND_DRUG,
                    DrugOrderStatus.DELIVER,
                    DrugOrderStatus.WAIT_PICK_UP,
                    DrugOrderStatus.WAIT_SIGN,
                    DrugOrderStatus.DELIVER_SIGN,
                    DrugOrderStatus.COMPLETE,
                    DrugOrderStatus.PAY_SUCCESS

            }
            , 15),
    DRUG_ORDER_STATUS_ENABLE_TO_DELETE("用户可删除药品订单的状态", SettingCategory.ORDER, NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(DrugOrderStatus.class)),
            new Enum[]{DrugOrderStatus.REFUND,
                    DrugOrderStatus.CANCELLED,
                    DrugOrderStatus.COMPLETE}
            , 15),

    REVISIT_AUTOMATIC_TIME("复诊自动分配时间(单位: 分)", SettingCategory.AUTO, 120, DataTypes.INTEGER, 16),
    NURSE_TRIAGE_ENABLED("是否开启护士自动分诊", SettingCategory.AUTO, false, DataTypes.BOOLEAN, 20),
    NURSE_TRIAGE_USERNAME("请选择开启护士分诊的用户名", SettingCategory.AUTO, "", DataTypes.STRING, 21),
    STOP_DIAGNOSIS_AUTO_REFUND_NUMBER("医生停诊时自动退号", SettingCategory.AUTO, true, DataTypes.BOOLEAN, 22),

    //20230516 用于faceEnable的控制,前端已经改成CA_FACE_RECOGNITION_ENABLED
    /*FACE_RECOGNITION_ENABLED("是否开启人脸识别", SettingCategory.SECURITY, false, DataTypes.BOOLEAN, 22),*/
    ID_CARD_ENABLED("身份证识别是否开启", SettingCategory.SECURITY, false, DataTypes.BOOLEAN, 23),
    LOGOUT_TIME("自动登出时间 (分钟)", SettingCategory.SECURITY, 120, DataTypes.INTEGER, 24),
    LONIN_WEAK_PASSWORD("简易密码和弱密码提示的配置，不能设置此配置密码", SettingCategory.SECURITY, Lists.newArrayList(),
            DataTypes.LIST_STRING, 26),
    ID_CARD_NAME_UNIT("服务商", SettingCategory.CERTIFICATION, IdCardNameUnitEnum.TENCENT, new EnumType(IdCardNameUnitEnum.class), false),
    ID_CARD_SECRET_ID("secretId", SettingCategory.CERTIFICATION, NotifyCategory.TX_CERTIFICATION,"", DataTypes.PASSWORD, true, false, 126),
    ID_CARD_SECRET_KEY("secretKey", SettingCategory.CERTIFICATION, NotifyCategory.TX_CERTIFICATION,"", DataTypes.PASSWORD, true, false, 126),
    JUMP_MEDICAL_INSURANCE_MINI("医保电子凭证小程序短链", SettingCategory.OTHER,"", DataTypes.STRING, false),
    JUMP_HOSPITAL_INNER_NAVIGATION_MINI("院内导航小程序短链", SettingCategory.OTHER,"", DataTypes.STRING, false),
    WX_SERVER_PATH("微信服务地址", SettingCategory.OTHER, "", DataTypes.STRING, false),
    //    SMS_TEST_MOBILE_TOKEN("测试用户固定短信验证码", SettingCategory.TEST, "546434", DataTypes.STRING, 27),
    OPEN_WECHAT_PAY("是否开启支付功能", SettingCategory.TEST, false, DataTypes.BOOLEAN, 28),
    PAY_ONE_FEN("是否支付1分钱", SettingCategory.TEST, false, DataTypes.BOOLEAN, 29),
    EXAM_SIGN_OUT_DAY("检查预约签到是否允许非当天", SettingCategory.TEST, false, DataTypes.BOOLEAN, true, 30),
    APPOINTMENT_OUT_DAY("是否允许非当天的预约/接诊", SettingCategory.TEST, false, DataTypes.BOOLEAN, true, 31),
    CHECK_EXECUTE_MINUTE("是否开启自动生成检查单，时间(分钟)", SettingCategory.TEST, 0, DataTypes.INTEGER, 32),
    EXAMORDER_EXECUTE_REPORT_MINUTE("是否开启签到后自动生成报告，时间(分钟)", SettingCategory.TEST, 0, DataTypes.INTEGER,
            33),
    EXAM_ORDER_REPORT_FILE("检查报告结果url", SettingCategory.TEST, "", DataTypes.URL, 34),
    ELEC_INVOICE_ENABLE("是否开启自动开具电子发票", SettingCategory.TEST, false, DataTypes.BOOLEAN, 35),
    ELEC_INVOICE_FILE("电子票据文档url", SettingCategory.TEST, "", DataTypes.URL, 36),
    ELEC_INVOICE_IMAGE("电子票据图片url", SettingCategory.TEST, "", DataTypes.URL, 37),
    CA_FACE_RECOGNITION_ENABLED("医生通过审核后需要验证本人身份并生成合规的签名", SettingCategory.TEST, true,
            DataTypes.BOOLEAN, 37),

    OUT_CHARGE_PAY_CAN_MERGE("门诊缴费是否能合并支付（包括医保和自费）", SettingCategory.ORDER, true, DataTypes.BOOLEAN, true, 44),
    SYNC_BILL_REALTIME("订单（包括支付和退款）是否实时同步", SettingCategory.ORDER, false, DataTypes.BOOLEAN, true, 44),
    QUERY_HIS_ORDERS("挂号记录是否以his数据为主，若是 则先查询his的已支付挂号记录，加上IH侧的未支付记录。若否，则以IH侧记录为准", SettingCategory.ORDER, false,
                       DataTypes.BOOLEAN,
                       true,
                       44),
    OUT_CHARGE_CONFIRM_CAN_MERGE("门诊缴费是否合并结算（合并：n个处方，1次结算，不合并：n个处方，n次结算）", SettingCategory.ORDER, true,
                                 DataTypes.BOOLEAN, true, 44),
    AUTO_REFUND_AFTER_CHARGE_CONFIRM_FAILED("结算失败是否自动退款（范围：调用了HIS结算接口的订单，包括预约挂号，门诊缴费，住院预交，自费医保自动退款/门诊医保挂号不自动退款）",
                                            SettingCategory.ORDER,
                                            true,
                                            DataTypes.BOOLEAN,
                                            true, 44),
    REFUNDED_BILL_MERGE("门诊缴费和住院预交的退款对账明细是否合并", SettingCategory.ORDER, true, DataTypes.BOOLEAN, 45),
    DAILY_OFFLINE_ORDER_COUNT("同一患者同一天同科室同时存在的有效挂号数量",
            SettingCategory.ORDER, 3, DataTypes.INTEGER, true, 45),

    /**
     * 就诊-患者端-线下已挂号通知
     */
    NOTIFY_VISIT_REMIND("线下已挂号通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 46),
    /**
     * 候诊-患者端-候诊开始通知
     */
    NOTIFY_WAITING_FOR_DOCTOR_START("候诊开始通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.WAIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 47),
    /**
     * 重新候诊通知
     */
    NOTIFY_RE_WAITING_FOR_DOCTOR_START("重新候诊通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.WAIT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 48),
    /**
     * 就诊-患者端-医生已接诊通知
     */
    NOTIFY_CALL_FOR_VISIT("医生已接诊通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.SMS}, 49),
    /**
     * 就诊-患者端-就诊评价提醒
     */
    NOTIFY_TO_EVALUATE("就诊评价提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.SMS}, 50),

    /**
     * 就诊-患者端-护士上门提醒
     */
    NOTIFY_NURSE_START_HOME("护士上门提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MINI_PROGRAM}, 51),

    /**
     * 就诊-患者端-修改上门时间提醒
     */
    NOTIFY_NURSE_UPDATE_VISIT_TIME("修改上门时间提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MINI_PROGRAM}, 52),
    /**
     * 就诊-患者端-护理订单退款提醒（多退少补）
     */
    NOTIFY_NURSE_REFUND("护理订单退款提醒（多退少补）", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MINI_PROGRAM}, 53),
    /**
     * 就诊-医生端-护理上门订单补缴成功通知
     */
    NOTIFY_NURSE_PAY_APPEND_SUCCESS("护理上门订单补缴成功通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM},
            new ClientType[]{ClientType.DOCTOR}, 54),

    /**
     * 就诊-医生端-新护理咨询消息通知
     */
    NOTIFY_NURSE_NEW_MESSAGE("新护理咨询消息通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM},
            new ClientType[]{ClientType.DOCTOR}, 55),

    /**
     * 就诊-医生端-护理上门订单支付成功通知
     */
    NOTIFY_NURSING_ORDERS_PAID("护理上门订单支付成功通知，用户护理上门订单支付成功回调时发送", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
                               Lists.newArrayList(),
                                    new ListType(new EnumType(NotificationMethod.class)), true,
                                    new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.SMS},
                                    new ClientType[]{ClientType.DOCTOR}, 54),

    /**
     * 就诊-医生端-护理上门任务已分配通知
     */
    NOTIFY_NURSING_ORDERS_TASK_ASSIGNED("护理上门任务已分配通知，护士长调用nursing_orders/{orderId}/assign接口派单时发送",
                                        SettingCategory.NOTIFY,
                                 HospitalSettingKey.NotifyCategory.VISIT,
                               Lists.newArrayList(),
                               new ListType(new EnumType(NotificationMethod.class)), true,
                               new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.SMS},
                               new ClientType[]{ClientType.DOCTOR}, 54),

    /**
     * 抢单提醒
     */
    NOTIFY_EMERGENCY_VISIT_ACCEPT("抢单提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.TRIAGE, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.IM}, new ClientType[]{ClientType.DOCTOR}, 56),

    /**
     * 处方-患者端-处方开具成功通知
     */
    NOTIFY_PRESCRIPTION_READY("处方开具成功通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.PRESCRIPTION,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS}, 61),
    /**
     * 处方审核提醒
     */
    NOTIFY_CALL_FOR_CHECK_PRESCRIPTION("处方审核提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.PRESCRIPTION,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.IM}, new ClientType[]{ClientType.DOCTOR}, 62),
    /**
     * 订单-患者端-问诊-订单待支付通知
     */
    NOTIFY_ORDER_PENDING("问诊-订单待支付通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS}, 63),
    /**
     * 订单-患者端-问诊-订单支付成功通知
     */
    NOTIFY_ORDER_CONFIRMED("问诊-订单支付成功通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.SMS}, 64),
    /**
     * 订单-患者端-问诊-订单关闭提醒
     */
    NOTIFY_ORDER_CLOSED("问诊-订单关闭提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS}, 65),
    /**
     * 订单-患者端-问诊-订单取消通知
     */
    NOTIFY_ORDER_CANCELED("问诊-订单取消通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.SMS}, 66),
    /**
     * 订单-患者端-问诊-订单发货通知
     */
    NOTIFY_ORDER_DELIVERED("问诊-订单发货通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS}, 67),
    /**
     * 订单-患者端-问诊-订单收货通知
     */
    NOTIFY_ORDER_RECEIVED("问诊-订单收货通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.SMS}, 68),

    /**
     * 预约-患者端-检查预约到检提醒
     */
    NOTIFY_TO_APPOINTMENT_CHECK("检查预约到检提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.APPOINTMENT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 69),
    /**
     * 预约-患者端-检查预约成功通知
     */
    NOTIFY_APPOINTMENT_CHECK_SUCCESS_TEMPLATE("检查预约成功通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.APPOINTMENT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY,
                    NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 70),
    /**
     * 预约-患者端-检查预约取消提醒
     */
    NOTIFY_APPOINTMENT_CHECK_CANCEL_TEMPLATE("检查预约取消提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.APPOINTMENT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY,
                    NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 71),
    /**
     * 预约-患者端-检查预约变更提醒
     */
    NOTIFY_APPOINTMENT_CHECK_CHANGE("检查预约变更提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.APPOINTMENT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 72),
    /**
     * 预约-患者端-签到成功通知
     */
    NOTIFY_SIGN_SUCCESS("签到成功通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.APPOINTMENT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 73),
    /**
     * 预约-患者端-检查报告完成通知
     */
    NOTIFY_REPORT_CREATED("检查报告完成通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.APPOINTMENT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 74),
    /**
     * 预约-患者端-检查可预约提醒
     */
    NOTIFY_ENABLE_TO_APPOINT("检查可预约提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.APPOINTMENT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 75),

    //医生快速回复模板
    DOCTOR_REPLY_TEMPLATE("医生快捷回复模板(按|分隔)", SettingCategory.GENERAL,
            "您好，请简单描述一下您的症状。|方便上传病历资料吗？以便进一步了解您的病情，谢谢。|抱歉，我现在不方便进行咨询，稍晚回复您。|由于线上问诊条件有限，现在无法准确判断您的症状，建议您去当地正规的医院进行就诊。|您好，请问你是否在线，咨询即将关闭，未能得到您的完整信息，无法为您提供建议。",
            DataTypes.STRING, 77),
    //客服电话
    CUSTOMER_SERVICE_PHONE("客服电话", SettingCategory.GENERAL,
            "",
            DataTypes.STRING, 78),
    CUSTOMER_SERVICE_TIME("人工客服的服务时间", SettingCategory.GENERAL,
            "",
            DataTypes.STRING, 78),
    USER_CLIENT_SLOGAN("用户端slogan", SettingCategory.GENERAL,
            "",
            DataTypes.STRING, 78),
    USER_PATIENT_TOTAL_MAX("用户能绑定就诊人的最大个数", SettingCategory.GENERAL,
            3,
            DataTypes.INTEGER, 78),

    MEDICAL_CARD_ENABLED("是否开启腾讯电子健康卡", SettingCategory.MEDICAL_CARD, false, DataTypes.BOOLEAN, 1),
    MEDICAL_CARD_HOSPITAL_ID("电子健康卡授权的医院id", SettingCategory.MEDICAL_CARD, "", DataTypes.STRING, 2),
    MEDICAL_CARD_USER_ID("电子健康卡分配账号", SettingCategory.MEDICAL_CARD, "", DataTypes.STRING, 3),
    MEDICAL_CARD_API_KEY("电子健康卡分配apiKey", SettingCategory.MEDICAL_CARD, "", DataTypes.STRING, 4),
    MEDICAL_CARD_APPID("电子健康卡微信应用唯一编号", SettingCategory.MEDICAL_CARD, "", DataTypes.STRING, 5),
    MEDICAL_CARD_API_URL("电子健康卡服务端地址", SettingCategory.MEDICAL_CARD, "http://xx", DataTypes.STRING, 6),
    MEDICAL_CARD_LIST_PATH("电子健康卡列表path", SettingCategory.MEDICAL_CARD, "/**", DataTypes.STRING, 7),
    MEDICAL_CARD_BIND_RELATION_PATH("绑定电子健康卡和院内ID关系path", SettingCategory.MEDICAL_CARD, "/**", DataTypes.STRING, 8),
    MEDICAL_CARD_REPORT_HIS_DATA_PATH("电子健康卡用卡数据监测path", SettingCategory.MEDICAL_CARD, "/**", DataTypes.STRING, 9),
    CREDIT_BASED_MEDICAL_TREATMENT_ENABLED("是否开启信用就医", SettingCategory.MEDICAL_CARD, false, DataTypes.BOOLEAN, 10),
    CHANGE_PATIENT_HOME("首页是否可以切换就诊人", SettingCategory.MEDICAL_CARD, false, DataTypes.BOOLEAN, 11),
    FREE_REG_AGE("免费挂号的年龄要求", SettingCategory.ORDER, 150, DataTypes.INTEGER, 11),
    /**
     * 万达电子健康卡参数
     */
    WD_ELECTRONIC_HEALTH_CARD_ENABLE("万达电子健康卡是否启用", SettingCategory.MEDICAL_CARD, false, DataTypes.BOOLEAN, 12),
    WD_ELECTRONIC_HEALTH_CARD_API_PATH("万达电子健康卡服务端地址", SettingCategory.MEDICAL_CARD, "", DataTypes.STRING, 13),
    WD_ELECTRONIC_HEALTH_CARD_TERM_ID("万达电子健康卡终端编号", SettingCategory.MEDICAL_CARD, "", DataTypes.STRING, 14),
    WD_ELECTRONIC_HEALTH_CARD_TERM_SECERT("万达电子健康卡终端密钥", SettingCategory.MEDICAL_CARD, "", DataTypes.STRING, 15),

    /**
     * 患者端公众号-患者报到提醒
     */
    NOTIFY_PATIENT_REPORT("患者报到提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.FOLLOW,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 80),

    /**
     * 医生账号审核结果通知（医生认证）
     */
    NOTIFY_ACCOUNT_RESULTS("医生账号审核结果通知（医生认证）", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.CHECK,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MESSAGE_CENTER,
                    NotificationMethod.SMS},
            new ClientType[]{ClientType.DOCTOR}, 88),

    /**
     * 患者端公众号-添加医生成功通知
     */
    NOTIFY_ADD_DOC_SUCCESS("添加医生成功通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.FOLLOW,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 90),

    /**
     * 就诊-医生端-医生累积过量候诊与咨询通知
     */
    NOTIFY_WAITING("医生累积过量候诊与咨询通知", SettingCategory.NOTIFY, NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.SMS}, new ClientType[]{ClientType.DOCTOR}, 91),

    /**
     * 医生端-就诊评价提醒
     */
    NOTIFY_TO_EVALUATE_DOCTOR("就诊评价提醒-患者进行评价后，提示医生", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.DOCTOR}, 92),

    /**
     * 医生端-用户咨询通知
     */
    NOTIFY_USER_CONSULT("用户咨询通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.DOCTOR}, 92),

    /**
     * 医生端-患者报到提醒
     */
    NOTIFY_PATIENT_REPORT_DOCTOR("患者报到提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.FOLLOW, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.DOCTOR}, 80),

//    GYT_CALL_BACK_URL("国医通回调地址", SettingCategory.GENERAL,
//                      "",
//                      DataTypes.STRING, 96),


    /**
     * 订单退款通知
     */
    NOTIFY_ORDER_REFUND("问诊-订单退款通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 100),

    /**
     * 医生端-订单取消通知
     */
    NOTIFY_ORDER_CANCELED_DOCTOR("问诊-订单取消通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.DOCTOR}, 101),

    /**
     * 订单-医生端-订单退款通知
     */
    NOTIFY_ORDER_REFUND_DOCTOR("问诊-订单退款通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER,
                    NotificationMethod.SMS},
            new ClientType[]{ClientType.DOCTOR}, 102),

    /**
     * 就诊-患者端-医生回复患者通知(医生给患者发送了回复，患者未读此消息5分钟)
     */
    NOTIFY_CALL_FOR_VISIT_TO_MESSAGE_UNREAD("医生回复患者通知", SettingCategory.NOTIFY,
            HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER, NotificationMethod.IM}, 103),

    /**
     * 就诊-患者端-护士回复患者通知(护士给患者发送了回复，患者未读此消息5分钟)
     */
    NOTIFY_CALL_FOR_CONSULT_TO_MESSAGE_UNREAD("护士回复患者通知", SettingCategory.NOTIFY,
            HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER}, 103),

    /**
     * 就诊-患者端-医生接诊咨询
     */
    NOTIFY_CALL_FOR_VISIT_TO_RECEIVED("医生接诊咨询", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER, NotificationMethod.IM},
            new ClientType[]{ClientType.PATIENT}, 104),

    /**
     * 订单-患者端-问诊-患者主动取消未支付咨询
     */
    NOTIFY_ORDER_CANCELED_UNPAID_TO_PATIENT("问诊-未支付咨询订单患者主动取消", SettingCategory.NOTIFY,
            HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 105),

    /**
     * 订单-患者端-问诊-候诊中咨询订单患者取消
     */
    NOTIFY_ORDER_CANCELED_PAY_TO_PATIENT("问诊-候诊中咨询订单患者取消", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 106),

    /**
     * 订单-患者端-问诊-候诊中咨询订单医生退诊
     */
    NOTIFY_ORDER_CANCELED_TO_DOCTOR("问诊-候诊中咨询订单医生退诊", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 107),

    /**
     * 订单-患者端-问诊-候诊中护理咨询订单护士退诊
     */
    NOTIFY_ORDER_CANCELED_TO_NURSE("问诊-候诊中护理咨询订单护士退诊", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 107),

    /**
     * 问诊-候诊中咨询订单医生未接诊退款
     */
    NOTIFY_ORDER_REFUND_TO_TIME_OUT_ONTIME_CONFIRMED("问诊-候诊中咨询订单医生未接诊退款", SettingCategory.NOTIFY,
            HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER,
                    NotificationMethod.SMS}, 108),

    /**
     * 进行中订单退款
     */
    NOTIFY_ORDER_REFUND_TO_STARTED_REFUNDED("问诊-进行中订单退款", SettingCategory.NOTIFY,
            HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER,
                    NotificationMethod.SMS}, 109),

    /**
     * 已完成订单退款
     */
    NOTIFY_ORDER_REFUND_TO_COMPLETED_REFUNDED("问诊-已完成订单退款", SettingCategory.NOTIFY,
            HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER,
                    NotificationMethod.SMS}, 109),

    /**
     * 订单-患者端-退款完成通知
     */
    NOTIFY_ORDER_REFUND_OVER("问诊-退款完成通知", SettingCategory.NOTIFY,
            HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 110),

    /**
     * 候诊-医生端-长时间未接诊通知
     */
    NOTIFY_WAITING_LONG_TIME("医生长时间未接诊通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.WAIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.DOCTOR}, 111),
    /**
     * 患者端-下次复诊日期提醒
     */
    NOTIFY_NEXT_VISIT_DATE("下次复诊日期提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 114),

    /**
     * 患者端-患教文章分享提醒
     */
    PATIENT_ARTICLE_SEND_NOTIFY("患教文章分享提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.FOLLOW,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{}, new ClientType[]{ClientType.PATIENT}, 117),

    /**
     * 患者端-小程序消息-随访提醒
     */
    NOTICE_PATIENT_TO_VISIT("患者随访提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT}, 120),

    /**
     * 患者端-小程序消息-在线问诊评价提醒
     */
    NOTICE_PATIENT_TO_EVALUATE("在线问诊评价提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 120),

    /**
     * 患者端-小程序消息-医生已接诊通知
     */
    NOTICE_PATIENT_DOCTOR_RECEIVED("医生已接诊通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 120),

    /**
     * 患者端-护士已接诊通知
     */
    NOTICE_PATIENT_NURSE_RECEIVED("护士已接诊通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 120),


    /**
     * 患者端-门诊缴费（支付成功）
     */
    NOTICE_OUTPATIENT_CHARGE_SUCCESS("门诊缴费（支付成功）通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端-门诊缴费（支付提醒）
     */
    NOTICE_OUTPATIENT_CHARGE_WAIT_PAY("门诊缴费（支付提醒）通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端-门诊缴费（退款成功）
     */
    NOTICE_OUTPATIENT_CHARGE_REFUNDED("门诊缴费（退款成功）通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT}, 120),

    /**
     * 患者端 - 病案复印预约 - 病案复印邮寄通知
     */
    NOTICE_COPY_APPOINTMENT("病案复印通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.COPY_APPOINTMENT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端-满意度问卷推送
     */
    NOTICE_QUESTIONNAIRE_PUSH("满意度问卷推送通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.APPOINTMENT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 预约挂号 - 就诊提醒通知,挂号评价通知,满意度调查通知,退款成功提醒
     */
    NOTICE_OUT_REGISTER_VISIT("预约挂号就诊提醒通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY, NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 预约挂号 - 挂号评价通知
     */
    NOTICE_OUT_REGISTER_EVALUATE("预约挂号挂号评价通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY, NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 120),

    /**
     * 患者端 - 预约挂号 - 预约挂号待支付通知
     */
    NOTICE_OUT_REGISTER_WAIT_PAY("预约挂号待支付通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM}, new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 预约挂号 - 满意度调查通知
     */
    NOTICE_QUESTIONNAIRE_SATISFACTION("预约挂号满意度调查通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM}, new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 预约挂号 - 退款成功提醒
     */
    NOTICE_OUT_REGISTER_REFUNDED("预约挂号退款成功提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 预约挂号 - 医生停诊通知
     */
    NOTICE_OUT_REGISTER_DOCTOR_SUSPEND("预约挂号医生停诊通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 取药通知
     */
    NOTICE_PATIENT_GET_MEDICINE("取药通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT},
            120),
    /**
     * 患者端 - 入院通知
     */
    NOTICE_IN_PATIENT("入院通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 出院通知
     */
    NOTICE_OUT_PATIENT("出院通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.MESSAGE_CENTER}, new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 报告出具通知
     */
    NOTICE_REPORT_SEND("报告出具通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM, NotificationMethod.WECHAT, NotificationMethod.ALIPAY, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 120),
    /**
     * 患者端 - 排队候诊通知
     */
    NOTICE_WAITING_DIAGNOSIS_SEND("排队候诊通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.VISIT,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM},
            new ClientType[]{ClientType.PATIENT}, 120),

    /**
     * 医生端-小程序消息-等待审方（审方提醒）
     */
    NOTICE_DOCTOR_TO_REVIEW("等待审方（审方提醒）", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.PRESCRIPTION,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM}, new ClientType[]{ClientType.DOCTOR}, 120),

    /**
     * 医生端-小程序消息-等待审方（药品发货提醒）
     */
    NOTICE_DOCTOR_TO_SEND("等待审方（药品发货提醒）", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.PRESCRIPTION,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM}, new ClientType[]{ClientType.DOCTOR}, 120),


    /**
     * 医生端-小程序消息-患者下单提醒（咨询）
     */
    NOTICE_DOCTOR_PATIENT_PAID("患者下单提醒（咨询）", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.PRESCRIPTION,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.MINI_PROGRAM}, new ClientType[]{ClientType.DOCTOR}, 120),

    OPEN_CERTIFICATION("就诊人和医生是否需要实名认证", SettingCategory.TEST, true, DataTypes.BOOLEAN, 119),

    NOTIFY_DISEASE_ACCOMPANY_DOCTOR("医生绑定患者未填写随访记录提醒（医生端）", SettingCategory.NOTIFY,
            NotifyCategory.UNKNOWN, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.DOCTOR}, 120),

    /**
     * CKD - 随访提醒
     */
    NOTIFY_ACCOMPANY("患者未提交首次随访记录提醒（患者端）", SettingCategory.NOTIFY, NotifyCategory.UNKNOWN,
            Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), false,
            new Enum[]{NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 121),

    CA_STARTED_VERIFY_ENABLED("医生接诊包含处方的问诊前需要验证本人身份", SettingCategory.TEST, false,
            DataTypes.BOOLEAN, 122),


    FOLLOW_UP_USE_HOSPITAL("随访是否接入院内系统", SettingCategory.HIS_SWITCH, false, DataTypes.BOOLEAN, false),
    HIS_SYNC_PATIENT_CARD("是否接入HIS就诊人", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false,
            123),
    HIS_SYNC_SCHEDULE("是否接入HIS互联网医疗业务排班（问诊、咨询、续方）", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false,
            123),
    HIS_SYNC_DIAGNOSTICS("是否接入HIS内诊断信息", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false,
            123),
    HIS_SYNC_MEDICAL_DICTIONARY("是否接入HIS互联网医院可开具的药品名录（药品字典）", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false,
            123),
    HIS_SYNC_MEDICAL_STOCK("是否接入医院内线下药房库存", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false,
            123),
    HIS_SYNC_MEDICAL_ORDER_STATUS("是否接入HIS同步药品订单状态", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false,
            123),
    HIS_SYNC_IM_FILE("是否同步音视频归档", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false,
            123),
    HIS_SYNC_DOC_DICT("是否同步HIS医生信息", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN,
            true, false,
            123),
    HIS_SYNC_DEPT_DICT("是否同步HIS科室信息", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN,
            true, false,
            123),
    HULI_APPLY_INVOICE("护理到家his电子发票开具与对账开关", SettingCategory.HIS_SWITCH, NotifyCategory.UNKNOWN, false,
                       DataTypes.BOOLEAN,
                       true, false,
                       123),
    CA_ALL_ENABLED("CA总开关，关闭时，全部CA配置将都不生效", SettingCategory.CA, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false, 124),
    CA_TYPE("CA厂商", SettingCategory.CA, NotifyCategory.UNKNOWN, HospitalCAEnum.UNKNOWN, new EnumType(HospitalCAEnum.class), true, false, 124),
    CA_LOGIN_ENABLED("医务人员端登录时是否使用CA（PC+移动）", SettingCategory.CA, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false, 125),
    CA_PRESCRIPTION_ENABLED("处方上所有的盖章与签名均需合规（走CA）", SettingCategory.CA, NotifyCategory.UNKNOWN, false, DataTypes.BOOLEAN, true, false, 126),

//    XINAN_API_FRONT_URL("信安服务器地址(前端调用)", SettingCategory.CA, NotifyCategory.CA_SH_KEY, "", DataTypes.STRING, true, false, 126),
    SH_UKEY_API_WEBSERVICE_URL("上海UKEY服务器webservice地址", SettingCategory.CA, NotifyCategory.CA_SH_UKEY, "", DataTypes.STRING, true, false, 126),
    SH_UKEY_API_SEAL_URL("上海UKEY服务器签章地址", SettingCategory.CA, NotifyCategory.CA_SH_UKEY, "", DataTypes.STRING, true, false, 126),
    SH_UKEY_APP_CODE("上海UKEY AppCode", SettingCategory.CA, NotifyCategory.CA_SH_UKEY, "", DataTypes.STRING, true, false, 126),
    SH_UKEY_APP_PWD("上海UKEY AppPwd", SettingCategory.CA, NotifyCategory.CA_SH_UKEY, "", DataTypes.STRING, true, false, 126),
    SH_UKEY_API_KEY("上海UKEY apiKey", SettingCategory.CA, NotifyCategory.CA_XINAN, "", DataTypes.STRING, true, false, 126),
    SH_UKEY_API_SECRET("上海UKEY apiSecret", SettingCategory.CA, NotifyCategory.CA_XINAN, "", DataTypes.STRING, true, false, 126),
    SH_UKEY_SEAL_STRATEGY_ID("上海UKEY签章策略标识，由签章系统提供", SettingCategory.CA, NotifyCategory.CA_SH_UKEY, "", DataTypes.STRING, true, false, 126),

    XINAN_API_FRONT_URL("信安服务器地址(前端调用)", SettingCategory.CA, NotifyCategory.CA_XINAN, "", DataTypes.STRING, true, false, 126),
    XINAN_API_BACKEND_URL("信安服务器地址(后端调用)", SettingCategory.CA, NotifyCategory.CA_XINAN, "", DataTypes.STRING, true, false, 126),
    XINAN_APP_ID("信安appId", SettingCategory.CA, NotifyCategory.CA_XINAN, "", DataTypes.STRING, true, false, 126),
    XINAN_SECRET_KEY("信安secretKey", SettingCategory.CA, NotifyCategory.CA_XINAN, "", DataTypes.STRING, true, false, 126),

    IM_TYPE("IM厂商", SettingCategory.IM, NotifyCategory.UNKNOWN, HospitalIMEnum.UNKNOWN, new EnumType(HospitalIMEnum.class), true, false, 124),
    TIM_APP_ID("腾讯IM APPID", SettingCategory.IM, NotifyCategory.IM_TX, 0L, DataTypes.LONG, true, false, 124),
    TIM_SECRET_KEY("腾讯IM SECRET_KEY", SettingCategory.IM, NotifyCategory.IM_TX, "", DataTypes.PASSWORD, true, false, 124),
    TIM_LAST_FETCH_DATE("最后一次同步腾讯DEFAULT数据时间", SettingCategory.IM, null, null, DataTypes.DATE, false, null, null, false, 127),
    TIM_VOD_DATE("最后一次同步腾讯实时音视频时间", SettingCategory.IM, null, null, DataTypes.DATE, false, null, null, false, 128),


    SMS_TYPE("短信平台类型", SettingCategory.SMS, SmsType.ALI, new EnumType(SmsType.class), false),
    SMS_TEMPLATE_TYPE("短信类型", SettingCategory.SMS, SmsTemplateType.TEMPLATE, new EnumType(SmsTemplateType.class), false),

    /**
     * 阿里短信配置
     */
    ALI_ACCESS_KEY_ID("阿里短信ACCESS_KEY_ID", SettingCategory.SMS, NotifyCategory.ALI_SMS, "xxx",
            DataTypes.STRING, true, false, 129),
    ALI_SIGN_NAME("阿里短信签名", SettingCategory.SMS, NotifyCategory.ALI_SMS, "xxx", DataTypes.STRING, true, false, 130),
    ALI_SECRET("阿里短信SECRET", SettingCategory.SMS, NotifyCategory.ALI_SMS, "xxx",
            DataTypes.PASSWORD, true, false, 131),
    /**
     * 移动短信配置
     */

    MAS_SERVER_URL("移动短信接口地址", SettingCategory.SMS, NotifyCategory.MAS_SMS, "http://xxx/sms/tmpsubmit",
            DataTypes.STRING, true, false, 132),
    MAS_SERVER_EC("移动短信企业名称", SettingCategory.SMS, NotifyCategory.MAS_SMS, "", DataTypes.STRING, true, false, 133),
    MAS_SERVER_APID("移动短信接口账号", SettingCategory.SMS, NotifyCategory.MAS_SMS, "", DataTypes.STRING, true, false, 134),
    MAS_SERVER_SIGN("移动短信签名", SettingCategory.SMS, NotifyCategory.MAS_SMS, "", DataTypes.STRING, true, false, 135),
    MAS_SERVER_SECRET("移动短信密钥", SettingCategory.SMS, NotifyCategory.MAS_SMS, "", DataTypes.PASSWORD, true, false, 136),

    /**
     * 联通短信配置
     */
    UMS_SERVER_URL("联通短信接口地址", SettingCategory.SMS, NotifyCategory.UMS_SMS, "http://xxx/sms/tmpsubmit",
            DataTypes.STRING, true, false, 132),
    UMS_SERVER_SPCODE("联通短信企业编号", SettingCategory.SMS, NotifyCategory.UMS_SMS, "", DataTypes.STRING, true, false, 133),
    UMS_SERVER_NAME("联通短信用户名称", SettingCategory.SMS, NotifyCategory.UMS_SMS, "", DataTypes.STRING, true, false, 134),
    UMS_SERVER_PASSWORD("联通短信用户密码", SettingCategory.SMS, NotifyCategory.UMS_SMS, "", DataTypes.STRING, true, false, 135),
    /**
     * 腾讯短信配置
     */
    TX_ENDPOINT("腾讯短信接口域名", SettingCategory.SMS, NotifyCategory.TX_SMS, "sms.tencentcloudapi.com", DataTypes.STRING, true, false, 136),
    TX_SDK_APP_ID("腾讯短信应用ID", SettingCategory.SMS, NotifyCategory.TX_SMS, "", DataTypes.STRING, true, false, 136),
    TX_REGION("腾讯短信地域信息", SettingCategory.SMS, NotifyCategory.TX_SMS, "ap-guangzhou", DataTypes.STRING, true, false, 136),
    TX_SECRET_ID("腾讯短信密钥ID", SettingCategory.SMS, NotifyCategory.TX_SMS, "", DataTypes.STRING, true, false, 136),
    TX_SECRET_KEY("腾讯短信密钥", SettingCategory.SMS, NotifyCategory.TX_SMS, "", DataTypes.PASSWORD, true, false, 136),
    TX_SIGN_NAME("腾讯短信签名", SettingCategory.SMS, NotifyCategory.TX_SMS, "", DataTypes.STRING, true, false, 136),

    /**
     * 验证码${code}，您正在登录，若非本人操作，请勿泄露。
     */
    SMS_LOGIN_TEMPLATE_ID("短信验证码登录接口-登录验证码模板", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 137),
    /**
     * 验证码${code}，您正在注册成为新用户，感谢您的支持！
     */
    SMS_SIGNUP_TEMPLATE_ID("注册验证码模板", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 138),
    /**
     * 验证码${code}，您正在尝试修改登录密码，请妥善保管账户信息。
     */
    SMS_RESET_PASSWORD_TEMPLATE_ID("修改密码验证码模板", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, false, false, 139),
    /**
     * 您正在更换手机号，验证码${code}，请于10分钟内按页面提示提交验证码。若非本人操作，请勿泄露。
     */
    SMS_RESET_MOBILE_TEMPLATE_ID("患者短信-修改手机号验证码", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, false, false, 140),
    /**
     * 您正在被添加为就诊人，验证码${code}，10分钟内有效。若非本人允许，请勿泄露。
     */
    SMS_ADD_PATIENT_TEMPLATE_ID("患者短信-就诊人手机绑定验证码", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 141),
    /**
     * 患者短信-就诊提醒
     */
    SMS_NOTIFY_VISIT_REMIND_TEMPLATE("患者短信-就诊提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 142),
    /**
     * 患者短信-候诊开始通知
     */
    SMS_NOTIFY_WAITING_FOR_DOCTOR_START_TEMPLATE("患者短信-候诊开始通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, false, false, 143),
    /**
     * 短信-重新候诊通知
     */
    SMS_NOTIFY_RE_WAITING_FOR_DOCTOR_START_TEMPLATE("短信-重新候诊通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx", DataTypes.STRING, false, false, 144),
    /**
     * 患者短信-医生已接诊通知
     */
    SMS_NOTIFY_CALL_FOR_VISIT_TEMPLATE("患者短信-医生已接诊通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 145),

    /**
     * 患者短信-护士已接诊通知
     */
    SMS_NOTIFY_CALL_FOR_CONSULT_TEMPLATE("患者短信-护士已接诊通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 145),

    /**
     * 患者短信-无偿献血患者优先排队通知
     */
    SMS_NOTIFY_BLOOD_DONOR_PRIORITY_QUEUE_TEMPLATE("患者短信-无偿献血患者优先排队通知", SettingCategory.SMS,
                                                   NotifyCategory.SMS_TEMPLATE, "xxx",
                                         DataTypes.STRING, true, false, 145),

    /**
     * 患者短信-就诊评价提醒(咨询、问诊结束后10分钟后推送)
     */
    SMS_NOTIFY_CALL_FOR_EVALUATE_TEMPLATE("(线上+患服)患者短信-就诊评价提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 146),
    /**
     * 短信-分诊会话提醒
     */
    SMS_NOTIFY_TRIAGE_VIDEO_TEMPLATE("短信-分诊会话提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, false, false, 147),
    /**
     * 短信-分诊完成通知
     */
    SMS_NOTIFY_TRIAGE_RESULT_TEMPLATE("短信-分诊完成通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, false, false, 148),
    /**
     * 短信-求救通知
     */
    SMS_NOTIFY_RESCUE_CALL_FOR_VOLUNTEER_TEMPLATE("短信-求救通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, false, false, 149),
    /**
     * 患者短信-处方开具成功通知
     */
    SMS_NOTIFY_PRESCRIPTION_READY_TEMPLATE("患者短信-处方开具成功通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx", DataTypes.STRING, true, false, 150),
    /**
     * 患者短信-订单待支付通知 (咨询订单、药品订单，未支付订单在支付时限一半时间通知患者)
     */
    SMS_NOTIFY_ORDER_PENDING_TEMPLATE("患者短信-订单待支付通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 151),
    /**
     * 患者短信-订单支付成功通知
     */
    SMS_NOTIFY_ORDER_CONFIRMED_TEMPLATE("患者短信-订单支付成功通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 152),
    /**
     * 患者短信-订单关闭提醒
     */
    SMS_NOTIFY_ORDER_CLOSED_TEMPLATE("患者短信-订单关闭提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 153),
    /**
     * 患者短信-订单取消通知
     */
    SMS_NOTIFY_ORDER_CANCELED_TEMPLATE("患者短信-订单取消通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 154),
    /**
     * 患者短信-订单发货通知
     */
    SMS_NOTIFY_ORDER_DELIVERED_TEMPLATE("患者短信-订单发货通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 155),
    /**
     * 患者短信-订单收货通知
     */
    SMS_NOTIFY_ORDER_RECEIVED_TEMPLATE("患者短信-订单收货通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 156),
    /**
     * 患者短信-检查预约到检提醒
     */
    SMS_NOTIFY_TO_APPOINTMENT_CHECK_TEMPLATE("患者短信-检查预约到检提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx", DataTypes.STRING, false, false, 157),
    /**
     * 患者短信-检查预约成功通知
     */
    SMS_NOTIFY_APPOINTMENT_CHECK_SUCCESS_TEMPLATE("患者短信-检查预约成功通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx", DataTypes.STRING, false, false, 158),
    /**
     * 患者短信-检查预约取消提醒
     */
    SMS_NOTIFY_APPOINTMENT_CHECK_CANCEL_TEMPLATE("患者短信-检查预约取消提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx", DataTypes.STRING, false, false, 159),
    /**
     * 患者短信-检查预约变更提醒
     */
    SMS_NOTIFY_APPOINTMENT_CHECK_CHANGE_TEMPLATE("患者短信-检查预约变更提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx", DataTypes.STRING, false, false, 160),
    /**
     * 患者短信-签到成功通知
     */
    SMS_NOTIFY_SIGN_SUCCESS_TEMPLATE("患者短信-签到成功通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, false, false, 161),
    /**
     * 患者短信-检查可预约提醒
     */
    SMS_NOTIFY_ENABLE_TO_APPOINTMENT_TEMPLATE("患者短信-检查可预约提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx", DataTypes.STRING, false, false, 162),
    /**
     * 患者短信-检查报告完成提醒
     */
    SMS_NOTIFY_REPORT_CREATED_TEMPLATE("患者短信-检查报告完成提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, false, false, 163),

    /**
     * 短信-医生回复患者
     */
    SMS_NOTIFY_DOCTORS_REPLY_TO_PATIENT("患者短信-医生回复患者", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 164),

    /**
     * 短信-护士回复患者
     */
    SMS_NOTIFY_NURSE_REPLY_TO_PATIENT("患者短信-护士回复患者", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 164),

    /**
     * 短信-医生挂断视频
     */
    SMS_NOTIFY_DOCTOR_HANGS_UP_VIDEO("患者短信-医生挂断视频", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 165),

    /**
     * 短信-已完成订单退款
     */
    SMS_NOTIFY_COMPLETED_REFUNDED("患者短信-已完成订单退款", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE, "xxx",
            DataTypes.STRING, true, false, 166),

    /**
     * 患者短信-医生发起视频咨询
     */
    SMS_NOTIFY_VIDEO_CONSULTATION_INITIATED_BY_DOCTOR("患者短信-医生发起视频咨询", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 167),
    /**
     * 短信-进行中订单退款
     */
    SMS_NOTIFY_STARTED_REFUNDED("患者短信-进行中订单退款", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 168),

    /**
     * 短信-候诊中咨询订单医生未接诊退款
     */
    SMS_NOTIFY_TIME_OUT_ONTIME_CONFIRMED_REFUNDED("患者短信-候诊中咨询订单医生未接诊退款", SettingCategory.SMS,
            NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 169),

    /**
     * 患者短信-问诊-候诊中护理咨询订单护士退诊
     */
    SMS_NOTIFY_ONTIME_CONFIRMED_NURSE_REFUNDED("患者短信-问诊-候诊中护理咨询订单护士退诊", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 170),

    /**
     * 患者短信-问诊-候诊中咨询订单医生退诊
     */
    SMS_NOTIFY_ONTIME_CONFIRMED_DOCTOR_REFUNDED("患者短信-问诊-候诊中咨询订单医生退诊", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 170),

    /**
     * 医生短信-累积了过量的候诊与咨询
     */
    SMS_NOTIFY_MESSAGE_ACCUMULATION("医生短信-累积了过量的候诊与咨询", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 171),

    /**
     * 患者短信-医生已接诊
     */
    SMS_NOTIFY_START("患者短信-医生已接诊", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, false, false, 172),

    /**
     * 短信-医生审核失败
     */
    SMS_NOTIFY_AUDIT_REFUSE("医生短信-医生审核失败", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, false, false, 173),

    /**
     * 短信-医生审核通过
     */
    SMS_NOTIFY_AUDIT_PASS("医生短信-医生审核通过", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, false, false, 174),
    /**
     * 短信-慢病随访提醒
     */
    SMS_NOTIFY_NEXT_VISIT_DATE("患者短信-慢病随访提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, false, false, 175),
    /**
     * 短信-回复意见建议提醒
     */
    SMS_NOTIFY_REPLAY_ADVICE("患者短信-回复意见建议提醒", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 175),

    /**
     * 短信-推送智能随访
     */
    SMS_NOTIFY_SMART_FOLLOW_UP("患者短信-推送智能随访", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
            "xxx",
            DataTypes.STRING, true, false, 177),

    /**
     * 短信-护理上门新的订单待派单通知
     */
    SMS_NOTIFY_NURSING_ORDERS_PAID("护士长短信-护理上门新的订单待派单通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
                               "xxx",
                               DataTypes.STRING, true, false, 177),
    /**
     * 短信-护理上门新的任务已分配通知
     */
    SMS_NOTIFY_NURSING_ORDERS_TASK_ASSIGNED("护士短信-护理上门新的任务已分配通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
                                   "xxx",
                                   DataTypes.STRING, true, false, 177),
    /**
     * 短信-运维监控系统告警通知
     */
    SMS_NOTIFY_MONITORING_SYSTEM_ALERT("运维短信-运维监控系统告警通知", SettingCategory.SMS, NotifyCategory.SMS_TEMPLATE,
                                            "xxx",
                                            DataTypes.STRING, true, false, 177),
    EXPRESS_TYPE("快递类型", SettingCategory.EXPRESS, ExpressType.SHUNFENG, new EnumType(ExpressType.class), false, 177),

    EXPRESS_100_KEY("快递100Key", SettingCategory.EXPRESS, NotifyCategory.EXPRESS100, "xxx",
            DataTypes.STRING, false, true, 178),

    EXPRESS_100_CUSTOMER("快递100Customer", SettingCategory.EXPRESS, NotifyCategory.EXPRESS100,
            "xxx",
            DataTypes.STRING, false, true, 179),

    SHUNFENG_USERNAME("顺丰医寄通后台账号", SettingCategory.EXPRESS, NotifyCategory.SHUNFENG,
            "xxx",
            DataTypes.STRING, true, true, 180),

    SHUNFENG_PASSWORD("顺丰医寄通后台密码", SettingCategory.EXPRESS, NotifyCategory.SHUNFENG,
            "xxx",
            DataTypes.STRING, true, true, 181),

    SHUNFENG_HOST("顺丰医寄通host", SettingCategory.EXPRESS, NotifyCategory.SHUNFENG,
            "http://mrds-admin-ci.sit.sf-express.com:45478/api",
            DataTypes.STRING, true, true, 182),

    SHUNFENG_HOSPITALCODE("顺丰医寄通hospitalCode", SettingCategory.EXPRESS, NotifyCategory.SHUNFENG,
            "xxx",
            DataTypes.STRING, true, true, 183),

    SHUNFENG_SECRETKEY("顺丰医寄通secretKey", SettingCategory.EXPRESS, NotifyCategory.SHUNFENG,
            "xxx",
            DataTypes.STRING, true, true, 184),

    SHUNFENG_MONTHLY_PAY_ORDER("是否使用快速下单", SettingCategory.EXPRESS, NotifyCategory.SHUNFENG,
            false,
            DataTypes.BOOLEAN, true, true, 185),
    MEDICAL_INSURANCE_PLATFORM("开启的移动医保方式", SettingCategory.MEDICAL_INSURANCE, Lists.newArrayList(),
            new ListType(new EnumType(MedicalInsurancePlatform.class)), 1),
    MEDICAL_INSURANCE_CITY_CODE("微信小程序免密授权城市编码", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 2),
    MEDICAL_INSURANCE_ORDER_CITY_CODE("微信小程序统一下单城市编码", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 3),
    MEDICAL_INSURANCE_CHANNEL("微信小程序渠道号（微信医保立项）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 4),
    MEDICAL_INSURANCE_ORG_CHNL_CRTF_CODG("微信小程序机构渠道认证编码（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 5),
    MEDICAL_INSURANCE_ORG_CODG("微信小程序定点医药机构编码（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 6),
    MEDICAL_INSURANCE_BIZ_TYPE("微信小程序线上业务类型（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, Lists.newArrayList(), new ListType(new EnumType(InsuranceBizTypeEnum.class)), 7),
    MEDICAL_INSURANCE_ORG_APP_ID("微信小程序定点医疗机构小程序/H5应用ID（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 8),
    MEDICAL_INSURANCE_PARTNER_ID("微信小程序需联系腾讯工程师获取partnerId(合作方id)", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 9),
    MEDICAL_INSURANCE_PARTNER_SECRET("微信小程序合作方id对应的partnerSecret", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 10),
    MEDICAL_INSURANCE_TOKEN_EXPIRE_TIME("医保token过期时间，不要小于（医保官方token过期时间+定时任务时间间隔），单位分钟", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, 6, DataTypes.INTEGER, 11),
    MEDICAL_INSURANCE_KEY("微信小程序医保接口签名key（微信医保权限申请邮件）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_WX_MINI, "", DataTypes.STRING, 11),

    MP_MEDICAL_INSURANCE_CITY_CODE("微信公众号免密授权城市编码", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 12),
    MP_MEDICAL_INSURANCE_ORDER_CITY_CODE("微信公众号统一下单城市编码", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 13),
    MP_MEDICAL_INSURANCE_CHANNEL("微信公众号渠道号（微信医保立项）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 14),
    MP_MEDICAL_INSURANCE_ORG_CHNL_CRTF_CODG("微信公众号机构渠道认证编码（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 15),
    MP_MEDICAL_INSURANCE_ORG_CODG("微信公众号定点医药机构编码（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 16),
    MP_MEDICAL_INSURANCE_BIZ_TYPE("微信公众号线上业务类型（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, Lists.newArrayList(), new ListType(new EnumType(InsuranceBizTypeEnum.class)), 17),
    MP_MEDICAL_INSURANCE_ORG_APP_ID("微信公众号定点医疗机构小程序/H5应用ID（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 18),
    MP_MEDICAL_INSURANCE_PARTNER_ID("微信公众号需联系腾讯工程师获取partnerId(合作方id)", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 19),
    MP_MEDICAL_INSURANCE_PARTNER_SECRET("微信公众号合作方id对应的partnerSecret", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 20),
    MP_MEDICAL_INSURANCE_KEY("微信公众号医保接口签名key（微信医保权限申请邮件）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 21),

    ALIPAY_ORG_APP_ID("支付宝小程序，定点医疗机构应用Id，支付宝接口-org_app_id，（国家医保反馈单-定点医药机构小程序/H5应用ID）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 22),
    ALIPAY_ORG_CODE("支付宝小程序，定点医药机构编码，（国家医保反馈单）。", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 23),
    ALIPAY_ORG_CHNL_CRTF_CODE("支付宝小程序，机构渠道认证编码，医药机构在渠道端线上业务办理的授权编码，（国家医保反馈单）。", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 24),
    ALIPAY_ORG_CITY_CODE("支付宝小程序，医保结算地城市编码(国标），可填写医院所在地城市编码。", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, "", DataTypes.STRING, 25),
    ALIPAY_MEDICAL_INSURANCE_BIZ_TYPE("支付宝小程，线上业务类型编码（国家医保反馈单）", SettingCategory.MEDICAL_INSURANCE, NotifyCategory.MEDICAL_INSURANCE_MP, Lists.newArrayList(), new ListType(new EnumType(InsuranceBizTypeEnum.class)), 26),

    SUPERVISE_TYPE("监管平台类型", SettingCategory.SUPERVISE, SuperviseEnum.UNKNOWN, new EnumType(SuperviseEnum.class), true, 185),

    LIAONING_KEY("监管平台KEY", SettingCategory.SUPERVISE, NotifyCategory.LIAONING, "xxx",
            DataTypes.STRING, true, false, 186),

    LIAONING_SECRET("监管平台SECRET", SettingCategory.SUPERVISE, NotifyCategory.LIAONING,
            "xxx",
            DataTypes.STRING, true, false, 187),
    LIAONING_DEBUG("监管平台调试模式", SettingCategory.SUPERVISE, NotifyCategory.LIAONING,
            true,
            DataTypes.BOOLEAN, true, false, 188),

    SICHUAN_KEY("监管平台KEY", SettingCategory.SUPERVISE, NotifyCategory.SICHUAN, "xxx",
            DataTypes.STRING, true, false, 189),

    SICHUAN_SECRET("监管平台SECRET", SettingCategory.SUPERVISE, NotifyCategory.SICHUAN,
            "xxx",
            DataTypes.STRING, true, false, 190),
    SICHUAN_URL("监管平台URL", SettingCategory.SUPERVISE, NotifyCategory.SICHUAN,
            "xxx",
            DataTypes.STRING, true, false, 191),


    /**
     * 就诊-患者端-医生发起视频咨询通知
     */
    NOTIFY_VIDEO_CONSULTATION_INITIATED_BY_DOCTOR("医生发起视频咨询通知",
            SettingCategory.NOTIFY, NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.SMS, NotificationMethod.MESSAGE_CENTER},
            new ClientType[]{ClientType.PATIENT}, 193),

    /**
     * 就诊-患者端-医生挂断视频通知
     */
    NOTIFY_DOCTOR_HANGS_UP_VIDEO("医生挂断视频通知",
            SettingCategory.NOTIFY, NotifyCategory.VISIT, Lists.newArrayList(),
            new ListType(new EnumType(NotificationMethod.class)), true,
            new Enum[]{NotificationMethod.SMS},
            new ClientType[]{ClientType.PATIENT}, 194),

    COPY_APPOINTMENT_PAID_PRICE("病案复印预约预交金额(分)", SettingCategory.MEDICAL_RECORD_COPY_APPOINTMENT, 50, DataTypes.INTEGER, 195),

    COPY_APPOINTMENT_DELIVERY_METHOD("病案复印预约邮寄方式", SettingCategory.MEDICAL_RECORD_COPY_APPOINTMENT, NotifyCategory.UNKNOWN, Lists.newArrayList(),
            new ListType(new EnumType(UserMedicalRecordCopyAppointment.DeliveryMethod.class)),
            new Enum[]{UserMedicalRecordCopyAppointment.DeliveryMethod.POSTAL_DELIVERY,
                    UserMedicalRecordCopyAppointment.DeliveryMethod.SELF_PICKUP}, 196),
    ALLOW_MEDICAL_RECORD_COPY_APPOINTMENT_FOR_OTHER("允许病案复印代办", SettingCategory.MEDICAL_RECORD_COPY_APPOINTMENT, true, DataTypes.BOOLEAN, 197),
    COPY_APPOINTMENT_BUSINESS_HOURS("病案室营业时间", SettingCategory.MEDICAL_RECORD_COPY_APPOINTMENT, "", DataTypes.STRING, 198),
    COPY_APPOINTMENT_ADDRESS("自取地址", SettingCategory.MEDICAL_RECORD_COPY_APPOINTMENT, "", DataTypes.STRING, 199),
    COPY_APPOINTMENT_PHONE("病案室联系电话", SettingCategory.MEDICAL_RECORD_COPY_APPOINTMENT, "", DataTypes.STRING, 200),
    /**
     * 订单-患者端-药品-订单待支付通知
     */
    NOTIFY_DRUG_ORDER_PENDING("药品-订单待支付通知", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS}, 201),
    /**
     * 订单-患者端-药品-订单关闭提醒
     */
    NOTIFY_DRUG_ORDER_CLOSED("药品-订单关闭提醒", SettingCategory.NOTIFY, HospitalSettingKey.NotifyCategory.ORDER,
            Lists.newArrayList(), new ListType(new EnumType(NotificationMethod.class)),
            new Enum[]{NotificationMethod.MESSAGE_CENTER, NotificationMethod.SMS}, 202),
    PAGE_SETTING_DATA("医院页面菜单配置", SettingCategory.OTHER, "", ExtraDataTypes.JSON_TYPE,
            false),
    // 2024年08月22日14:49:44  暂时不需要 所以在d端隐藏
//    YIBAO_ORDER_CONSOLIDATED_PAYMENT("门诊缴费-医保订单是否支持合并支付。是：支持，多个处方单号允许合并预算；否：不支持，多个处方单号单独预算", SettingCategory.ORDER,
//                                     false,
//                                     DataTypes.BOOLEAN
//        ,203),
    // 2024年08月22日14:49:44  暂时不需要 所以在d端隐藏
//
//    ORDER_CONSOLIDATED_PAYMENT("门诊缴费-自费订单是否支持合并支付。是：支持，多个处方单号允许合并预算；否：不支持，多个处方单号单独预算", SettingCategory.ORDER, false,
//                               DataTypes.BOOLEAN
//        ,204),
    SMART_MEDICAL_CONSULT_API_KEY ("智能问诊API-KEY", SettingCategory.AI_ASSISTANT, "", DataTypes.STRING, true, 1),
    SMART_MEDICAL_CONSULT_APP_ID ("智能问诊应用ID", SettingCategory.AI_ASSISTANT, "", DataTypes.STRING, true, 2),
    SMART_MEDICAL_CONSULT_APP_NAME ("智能问诊应用名称", SettingCategory.AI_ASSISTANT, "", DataTypes.STRING, true, 3),
    SMART_MEDICAL_CONSULT_APP_ICON ("智能问诊应用图标", SettingCategory.AI_ASSISTANT, "", DataTypes.STRING, true, 4),
    QWEN_API_KEY ("通义千问API-KEY", SettingCategory.AI_ASSISTANT, "", DataTypes.STRING, true, 5),
    SMART_MEDICAL_FOLLOW_UP_CODE("智能问诊随访问卷编码",SettingCategory.AI_ASSISTANT, "", DataTypes.STRING, true, 6),
    SMS_REPLY_ADVICE("回复意见建议是否发送短信", SettingCategory.GENERAL, false, DataTypes.BOOLEAN, 79),
    WANDA_PAY_ENABLED("是否启用万达链支付", SettingCategory.GENERAL, false, DataTypes.BOOLEAN, 80),
    WANDA_HOST("万达,支付平台调用host,http://***********:8080", SettingCategory.GENERAL, "", DataTypes.STRING, 81),
    WANDA_APP_ID("万达,支付平台分配的固定值", SettingCategory.GENERAL, "", DataTypes.STRING, 82),
    WANDA_SUBMERNO("万达,支付平台分配的固定值。主要区别接入商户是否区分子商户", SettingCategory.GENERAL, "", DataTypes.STRING, 83),
    WANDA_APP_SECRET("万达,支付平台分配的固定值（注意与 SDK 支付接口参数中的appsecret 不同）", SettingCategory.GENERAL, "", DataTypes.STRING, 84),
    ;

    /**
     * 描述
     */
    private final String name;
    /**
     * 分类
     */
    private final SettingCategory category;
    /**
     * 二级分类
     */
    private final NotifyCategory notifyCategory;
    /**
     * 数据类型
     */
    private final DataType dataType;
    /**
     * 是否可编辑（影响查询列表接口是否可查询，如果直接调用接口，可以还是可以编辑的）
     */
    private final boolean editable;
    /**
     * 默认值
     */
    private final Object defaultValue;
    /**
     * 如果是单选多选，这个是选线的枚举值
     */
    private final Enum[] enumConstants;
    /**
     * 顺序
     */
    private final int orderNumber;
    /**
     * 客户端类型
     */
    private final ClientType[] clientTypes;
    /**
     * 医院权限是否可编辑
     */
    private final boolean hospitalCanEdit;

    // 医院可编辑
    HospitalSettingKey(String name, SettingCategory category, Object defaultValue,
                       DataType dataType) {
        this(name, category, NotifyCategory.UNKNOWN, defaultValue, dataType, true, new Enum[]{},
                new ClientType[]{ClientType.PATIENT}, true, -1);
    }

    HospitalSettingKey(String name, SettingCategory category, Object defaultValue,
                       DataType dataType, boolean hospitalCanEdit) {
        this(name, category, NotifyCategory.UNKNOWN, defaultValue, dataType, true, new Enum[]{},
                new ClientType[]{ClientType.PATIENT}, hospitalCanEdit, -1);
    }

    HospitalSettingKey(String name, SettingCategory category, Object defaultValue,
                       DataType dataType, Enum[] enums, boolean hospitalCanEdit) {
        this(name, category, NotifyCategory.UNKNOWN, defaultValue, dataType, true, enums,
                new ClientType[]{ClientType.PATIENT}, hospitalCanEdit, -1);
    }

    // 医院可编辑
    HospitalSettingKey(String name, SettingCategory category, Object defaultValue,
                       DataType dataType, int orderNumber) {
        this(name, category, NotifyCategory.UNKNOWN, defaultValue, dataType, true, new Enum[]{},
                new ClientType[]{ClientType.PATIENT}, true, orderNumber);
    }

    // 医院可编辑
    HospitalSettingKey(String name, SettingCategory category, NotifyCategory notifyCategory, Object defaultValue,
                       DataType dataType, int orderNumber) {
        this(name, category, notifyCategory, defaultValue, dataType, true, new Enum[]{},
                new ClientType[]{ClientType.PATIENT}, true, orderNumber);
    }

    // 医院可编辑
    HospitalSettingKey(String name, SettingCategory category, Object defaultValue,
                       DataType dataType, boolean editable, int orderNumber) {
        this(name, category, NotifyCategory.UNKNOWN, defaultValue, dataType, editable, new Enum[]{},
                new ClientType[]{ClientType.PATIENT}, true, orderNumber);
    }

    HospitalSettingKey(String name, SettingCategory category, NotifyCategory notifyCategory, Object defaultValue,
                       DataType dataType, boolean editable, boolean hospitalCanEdit, int orderNumber) {
        this(name, category, notifyCategory, defaultValue, dataType, editable, new Enum[]{},
                new ClientType[]{ClientType.PATIENT}, hospitalCanEdit, orderNumber);
    }

    // 医院可编辑
    HospitalSettingKey(String name, SettingCategory category, NotifyCategory notifyCategory, Object defaultValue,
                       DataType dataType, Enum[] enums, int orderNumber) {
        this(name, category, notifyCategory, defaultValue, dataType, true, enums, new ClientType[]{ClientType.PATIENT},
                true, orderNumber);
    }

    // 医院可编辑
    HospitalSettingKey(String name, SettingCategory category, NotifyCategory notifyCategory, Object defaultValue,
                       DataType dataType, boolean editable, Enum[] enums, ClientType[] clientTypes, int orderNumber) {
        this(name, category, notifyCategory, defaultValue, dataType, editable, enums, clientTypes, true, orderNumber);
    }

    HospitalSettingKey(String name, SettingCategory category, NotifyCategory notifyCategory, Object defaultValue,
                       DataType dataType, boolean editable, Enum[] enums, ClientType[] clientTypes, boolean hospitalCanEdit, int orderNumber) {
        this.name = name;
        this.notifyCategory = notifyCategory;
        this.category = category;
        this.dataType = dataType;
        this.editable = editable;
        this.defaultValue = defaultValue;
        enumConstants = enums;
        this.orderNumber = orderNumber;
        this.clientTypes = clientTypes;
        this.hospitalCanEdit = hospitalCanEdit;
    }

    public SettingCategory getCategory() {
        return category;
    }

    public DataType getDataType() {
        return dataType;
    }

    public boolean isEditable() {
        return editable;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

    public String getId() {
        return name().toLowerCase();
    }

    public Enum[] getEnumConstants() {
        return enumConstants;
    }

    @Override
    public String toString() {
        return getId();
    }

    public String getName() {
        return name;
    }

    public NotifyCategory getNotifyCategory() {
        return notifyCategory;
    }

    public boolean isHospitalCanEdit() {
        return hospitalCanEdit;
    }

    public int getOrderNumber() {
        return orderNumber;
    }

    public ClientType[] getClientTypes() {
        return clientTypes;
    }

    public enum SettingCategory {
        DEMO("示例", false),
        CONTACT("就诊人", false),
        DEFAULT("默认分类", false),
        NOTIFY("通知方式", true),
        GENERAL("常规", true),
        ORDER("订单", true),
        APPOINTMENT("预约", true),
        AUTO("自动设置", true),
        SECURITY("安全", true),
        IM("IM", false),
        CERTIFICATION("实名认证", false),
        CA("CA", true),
        SMS("短信", true),
        EXPRESS("快递", true),
        SUPERVISE("监管平台", true),
        PAGE_CONFIGURATION("页面配置", true),
        HIS_SWITCH("his同步开关", true),
        MEDICAL_RECORD_COPY_APPOINTMENT("病案复印预约", true),
        TEST("测试", true),
        OTHER("其他", false),
        MEDICAL_INSURANCE("医保", true),
        MEDICAL_CARD("就诊卡", true),
        AI_ASSISTANT("智能医助",true);

        private final String name;
        private final boolean show;

        SettingCategory(String name, boolean show) {
            this.name = name;
            this.show = show;
        }

        public String getName() {
            return name;
        }

        public boolean isShow() {
            return show;
        }
    }

    public enum NotifyCategory {
        UNKNOWN("未知", null),
        ORDER("订单", SettingCategory.NOTIFY),
        PRESCRIPTION("处方", SettingCategory.NOTIFY),
        APPOINTMENT("预约", SettingCategory.NOTIFY),
        LOGIN("登录", SettingCategory.NOTIFY),
        TRIAGE("分诊", SettingCategory.NOTIFY),
        WAIT("候诊", SettingCategory.NOTIFY),
        VISIT("就诊", SettingCategory.NOTIFY),
        FOLLOW("关注", SettingCategory.NOTIFY),
        CHECK("审核", SettingCategory.NOTIFY),
        TX_SMS("腾讯短信", SettingCategory.SMS),
        MAS_SMS("移动短信", SettingCategory.SMS),
        UMS_SMS("联通短信", SettingCategory.SMS),
        ALI_SMS("阿里短信", SettingCategory.SMS),
        SMS_TEMPLATE("短信模板", SettingCategory.SMS),
        CA_XINAN("信安CA", SettingCategory.CA),
        CA_SH_UKEY("上海CA", SettingCategory.CA),
        IM_TX("腾讯IM", SettingCategory.IM),
        EXPRESS100("快递100", SettingCategory.EXPRESS),
        SHUNFENG("顺丰", SettingCategory.EXPRESS),
        LIAONING("辽宁", SettingCategory.SUPERVISE),
        SICHUAN("四川", SettingCategory.SUPERVISE),
        TX_CERTIFICATION("腾讯身份认证", SettingCategory.CERTIFICATION),
        COPY_APPOINTMENT("病案复印预约", SettingCategory.NOTIFY),
        MEDICAL_INSURANCE_WX_MINI("微信小程序医保", SettingCategory.MEDICAL_INSURANCE, MedicalInsurancePlatform.WECHAT_MINI),
        MEDICAL_INSURANCE_MP("微信公众号医保", SettingCategory.MEDICAL_INSURANCE, MedicalInsurancePlatform.WECHAT_MP),
        MEDICAL_INSURANCE_ALPAY("支付宝小程序医保", SettingCategory.MEDICAL_INSURANCE, MedicalInsurancePlatform.ALIPAY),

        ;

        private final SettingCategory category;
        private final String name;
        private final Object group;

        NotifyCategory(String name, SettingCategory category) {
            this(name, category, null);
        }

        NotifyCategory(String name, SettingCategory category, Object group) {
            this.category = category;
            this.group = group;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public SettingCategory getCategory() {
            return category;
        }

        public Object getGroup() {
            return group;
        }
    }

    public enum ClientType {
        PATIENT("患者"),
        DOCTOR("医生");

        private final String name;

        ClientType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    public enum NotificationMethod {
        MESSAGE_CENTER("消息中心"),
        WECHAT("微信公众号"),
        MINI_PROGRAM("微信小程序"),
        ALIPAY("支付宝小程序"),
        IM("页面"),
        SMS("短信");

        private final String name;

        NotificationMethod(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }
}

