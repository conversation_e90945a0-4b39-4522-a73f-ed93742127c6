package cn.taihealth.ih.domain;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = UserMedicalWorkerRel.TABLE_NAME, uniqueConstraints = {
    @UniqueConstraint(name = "U_user_medical_worker_rels_user_id_medical_id", columnNames = {"user_id",
        "medical_worker_id"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class UserMedicalWorkerRel extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_user_medical_worker_rels";


    @Comment("用户")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_USER_MEDICAL_WORKER_USER_ID"))
    private User user;

    @Comment("医生")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "medical_worker_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_USER_MEDICAL_WORKER_MEDICAL_WORKER_ID"))
    private MedicalWorker medicalWorker;

    /**
     * 此字段只针对于general字段
     */
    @Comment("来源 扫码关注/普通  此字段只针对于general字段")
    @Column(name = "source")
    @Enumerated(EnumType.STRING)
    private FocusSource source = FocusSource.GENERAL;

    @Comment("isCkd")
    @Column(name = "is_ckd")
    private Boolean ckd = false;

    /**
     * 此字段只针对于ckd字段
     */
    @Comment("CKD来源 扫码关注/普通  此字段只针对于ckd字段")
    @Column(name = "ckd_source")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("QRCODE")
    private FocusSource ckdSource = FocusSource.QRCODE;

    @Comment("是否是腹透医生")
    @Column(name = "is_ft")
    private Boolean ft = false;

    /**
     * 此字段只针对于ft字段
     */
    @Comment("FT来源 扫码关注/普通  此字段只针对于ft字段")
    @Column(name = "ft_source")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("QRCODE")
    private FocusSource ftSource = FocusSource.QRCODE;

    @Comment("isGeneral")
    @Column(name = "is_general")
    private Boolean general = true;

    @Comment("CKD绑定时间")
    @Column(name = "ckd_bind_date")
    private Date ckdBindDate;

    @Comment("FT绑定时间")
    @Column(name = "ft_bind_date")
    private Date ftBindDate;

    public enum FocusSource {
        /**
         * 扫码关注
         */
        QRCODE,
        /**
         * 普通
         */
        GENERAL
    }


    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public MedicalWorker getMedicalWorker() {
        return medicalWorker;
    }

    public void setMedicalWorker(MedicalWorker medicalWorker) {
        this.medicalWorker = medicalWorker;
    }

    public FocusSource getSource() {
        return source;
    }

    public void setSource(FocusSource source) {
        this.source = source;
    }

    public Boolean getCkd() {
        return ckd;
    }

    public void setCkd(Boolean ckd) {
        this.ckd = ckd;
    }

    public Boolean getFt() {
        return ft;
    }

    public void setFt(Boolean ft) {
        this.ft = ft;
    }

    public Boolean getGeneral() {
        return general;
    }

    public void setGeneral(Boolean general) {
        this.general = general;
    }

    public Date getCkdBindDate() {
        return ckdBindDate;
    }

    public void setCkdBindDate(Date ckdBindDate) {
        this.ckdBindDate = ckdBindDate;
    }

    public Date getFtBindDate() {
        return ftBindDate;
    }

    public void setFtBindDate(Date ftBindDate) {
        this.ftBindDate = ftBindDate;
    }

    public FocusSource getCkdSource() {
        return ckdSource;
    }

    public void setCkdSource(FocusSource ckdSource) {
        this.ckdSource = ckdSource;
    }

    public FocusSource getFtSource() {
        return ftSource;
    }

    public void setFtSource(FocusSource ftSource) {
        this.ftSource = ftSource;
    }
}
