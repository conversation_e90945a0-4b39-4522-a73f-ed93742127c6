package cn.taihealth.ih.domain.enums;

import java.util.HashMap;
import java.util.Map;

public enum QuestionType {

    SINGLE("单选题"),
    MULTIPLE("多选题"),
    ANSWER("问答题"),
    SINGLE_ANSWER("单选备注题"),
    MULTIPLE_ANSWER("多选备注题");

    private final String name;

    private static final Map<String, QuestionType> MAP = new HashMap<>();

    static {
        for (QuestionType item : QuestionType.values()) {
            MAP.put(item.name, item);
        }
    }

    public static QuestionType getByCode(String name) {
        return MAP.get(name);
    }

    QuestionType(String name){
        this.name = name;
    }

    public String getName(){
        return this.name;
    }

}
