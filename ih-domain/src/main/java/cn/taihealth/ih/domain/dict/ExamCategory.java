package cn.taihealth.ih.domain.dict;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.UpdatableEntity;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.*;

/**
 * 检查类别
 */
@Entity
@Table(name = ExamCategory.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ExamCategory extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_exam_categories";

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_EXAM_CATEGORIES_HOSPITAL_ID"))
    private Hospital hospital;

    @Comment("检查类别名称")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 类别代码
     */
    @Comment("检查类别代码")
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 检查需知, 使用doc的title
     * 添加类别的时候可以生成一个随机的路径, 编辑类别时使用原有的
     */
    @Comment("检查需知, 使用doc的title")
    @Column(name = "doc_path")
    private String docPath;

    /**
     * 可预约天数, -1表示未设置
     */
    @Comment("可预约天数, -1表示未设置")
    @Column(name = "appointment_days")
    @ColumnDefault("-1")
    private int appointmentDays = 15;

    /**
     * 优先顺序, -1表示不限顺序
     */
    @Comment("优先顺序, -1表示不限顺序")
    @Column(name = "exam_sequence")
    @ColumnDefault("-1")
    private int examSequence = -1;

    /**
     * 检查需知html内容
     */
    @Comment("检查需知html内容")
    @Column(name = "notice")
    @Type(type = "org.hibernate.type.TextType")
    private String notice;

    public ExamCategory() {
    }

    public ExamCategory(Hospital hospital, String name) {
        this.hospital = hospital;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDocPath() {
        return docPath;
    }

    public void setDocPath(String docPath) {
        this.docPath = docPath;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public int getAppointmentDays() {
        return appointmentDays;
    }

    public void setAppointmentDays(int appointmentDays) {
        this.appointmentDays = appointmentDays;
    }

    public int getExamSequence() {
        return examSequence;
    }

    public void setExamSequence(int examSequence) {
        this.examSequence = examSequence;
    }

    public String getNotice() {
        return notice;
    }

    public void setNotice(String notice) {
        this.notice = notice;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
