package cn.taihealth.ih.domain.nursing;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

@Entity
@Table(name = NursingChargeItem.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@org.hibernate.annotations.Table(appliesTo = NursingChargeItem.TABLE_NAME, comment = "护理费用项目")
public class NursingChargeItem extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_nursing_charge_items";

    @Comment("医院")
    @JoinColumn(name = "hospital_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_NURSING_CHARGE_ITEMS_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @Column(name = "code")
    @Comment("费用编码")
    private String code;

    @Column(name = "name")
    @Comment("费用名称")
    private String name;

    @Column(name = "type")
    @Comment("费用类型")
    private String type;

    @Column(name = "unit")
    @Comment("项目单位")
    private String unit;

    @Column(name = "price")
    @Comment("项目价格(分)")
    @ColumnDefault("0")
    private int price;

    @Column(name = "is_enabled")
    @Comment("是否启用")
    @ColumnDefault("1")
    private boolean enabled = true;

    @Column(name = "is_deleted")
    @Comment("是否删除")
    @ColumnDefault("0")
    private boolean deleted;

    @Column(name = "is_change_price")
    @Comment("医护使用时是否可变更价格")
    @ColumnDefault("0")
    private boolean changePrice;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isChangePrice() {
        return changePrice;
    }

    public void setChangePrice(boolean changePrice) {
        this.changePrice = changePrice;
    }
}
