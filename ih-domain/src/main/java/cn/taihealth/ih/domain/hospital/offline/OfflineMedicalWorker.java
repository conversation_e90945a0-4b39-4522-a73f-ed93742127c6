package cn.taihealth.ih.domain.hospital.offline;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import com.google.common.collect.Lists;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;
import org.springframework.boot.context.properties.bind.DefaultValue;

@Entity
@Table(name = OfflineMedicalWorker.TABLE_NAME, uniqueConstraints = {
    @UniqueConstraint(name = "U_OFFLINE_MEDICAL_WORKERS_HOSPITAL_JOB_NUMBER", columnNames = {"hospital_id",
        "job_number"})
},
    indexes = {
        @Index(name = "IDX_MEDICAL_WORKERS_TITLE", columnList = "title"),
    })
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class OfflineMedicalWorker extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_offline_medical_workers";

    /**
     * 医生姓名
     */
    @Column(length = 64)
    @Comment("医生姓名")
    private String name;

    @JoinColumn(name = "avatar_id",
            foreignKey = @ForeignKey(name = "FK_OFFLINE_MEDICAL_WORKERS_AVATAR"))
    @ManyToOne(fetch = FetchType.LAZY)
    @Comment("头像ID")
    private Upload avatar;
    /**
     * 职称
     */
    @Column(length = 64)
    @Comment("职称")
    private String title;
    /**
     * 简介
     */
    @Column(name = "introduction")
    @Comment("简介")
    @Type(type = "org.hibernate.type.TextType")
    private String introduction;
    /**
     * 擅长领域
     */
    @Column(name = "areas_of_expertise")
    @Comment("擅长领域")
    @Type(type = "org.hibernate.type.TextType")
    private String areasOfExpertise;

    /**
     * 工号
     */
    @Column(name = "job_number")
    @Comment("工号")
    private String jobNumber;

    /**
     * 排序序号
     */
    @Column(name = "order_value")
    @Comment("排序序号")
    private int orderValue;

    @Comment("医院")
    @JoinColumn(name = "hospital_id",
        foreignKey = @ForeignKey(name = "FK_MEDICAL_WORKERS_HOSPITAL_ID"))
    @ManyToOne(fetch = FetchType.LAZY)
    private Hospital hospital;

    @OneToMany(mappedBy = "medicalWorker", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<OfflineDeptMedicalWorker> deptMedicalWorkers = Lists.newArrayList();

    @OneToMany(mappedBy = "offlineMedicalWorker", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<OfflineMedicalWorkerRecommend> offlineMedicalWorkerRecommends = Lists.newArrayList();

    @Comment("启用/禁用")
    @Column(name = "is_enabled" , nullable = false)
    private boolean enabled = true;

    @Column(name = "is_introduce")
    @ColumnDefault("1")
    @Comment("是否是介绍医生")
    private boolean introduce = true;

    @Column(name = "tag")
    @Comment("标签")
    private String tag;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Upload getAvatar() {
        return avatar;
    }

    public void setAvatar(Upload avatar) {
        this.avatar = avatar;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getAreasOfExpertise() {
        return areasOfExpertise;
    }

    public void setAreasOfExpertise(String areasOfExpertise) {
        this.areasOfExpertise = areasOfExpertise;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public List<OfflineDeptMedicalWorker> getDeptMedicalWorkers() {
        return deptMedicalWorkers;
    }

    public void setDeptMedicalWorkers(List<OfflineDeptMedicalWorker> deptMedicalWorkers) {
        this.deptMedicalWorkers = deptMedicalWorkers;
    }

    public int getOrderValue() {
        return orderValue;
    }

    public void setOrderValue(int orderValue) {
        this.orderValue = orderValue;
    }

    public List<OfflineMedicalWorkerRecommend> getOfflineMedicalWorkerRecommends() {
        return offlineMedicalWorkerRecommends;
    }

    public void setOfflineMedicalWorkerRecommends(List<OfflineMedicalWorkerRecommend> offlineMedicalWorkerRecommends) {
        this.offlineMedicalWorkerRecommends = offlineMedicalWorkerRecommends;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isIntroduce() {
        return introduce;
    }

    public void setIntroduce(boolean introduce) {
        this.introduce = introduce;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }
}
