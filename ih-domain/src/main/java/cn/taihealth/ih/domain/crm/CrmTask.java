package cn.taihealth.ih.domain.crm;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.enums.CrmTaskStatus;
import cn.taihealth.ih.domain.hospital.Order;
import com.beust.jcommander.internal.Lists;
import java.util.Date;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

/**
 * 随访任务
 */
@Entity
@Table(name = CrmTask.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class CrmTask extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_crm_tasks";

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_CRM_TASKS_HOSPITAL_ID"))
    private Hospital hospital;

    @Comment("就诊人")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_CRM_TASKS_PATIENT_ID"))
    private Patient patient;

    @Comment("随访计划")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plan_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_CRM_TASKS_PLAN_ID"))
    private CrmPlan plan;

    /**
     * 随访任务总状态, 参见https://git.taihealth.cn/hero/management/issues/-/issues/2268
     */
    @Comment("随访任务总状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "task_status")
    @ColumnDefault("IN_PROGRESS")
    private CrmTaskStatus taskStatus = CrmTaskStatus.IN_PROGRESS;

    /**
     * 第一次随访时间, 还未随访时使用第一次计划时间, 第一次已经随访后, 使用第一次随访时间
     */
    @Comment("第一次随访时间, 还未随访时使用第一次计划时间, 第一次已经随访后, 使用第一次随访时间")
    @Column(name = "first_time")
    private Date firstTime;

    @Comment("订单")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id",
        foreignKey = @ForeignKey(name = "FK_CRM_TASKS_ORDER_ID"))
    private Order order;

    @OneToMany(mappedBy = "task", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<CrmTaskDetail> taskDetails = Lists.newArrayList();

    @Comment("是否启用")
    @Column(name = "is_enabled")
    private boolean enabled = true;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public CrmPlan getPlan() {
        return plan;
    }

    public void setPlan(CrmPlan plan) {
        this.plan = plan;
    }

    public CrmTaskStatus getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(CrmTaskStatus taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Date getFirstTime() {
        return firstTime;
    }

    public void setFirstTime(Date firstTime) {
        this.firstTime = firstTime;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public List<CrmTaskDetail> getTaskDetails() {
        return taskDetails;
    }

    public void setTaskDetails(List<CrmTaskDetail> taskDetails) {
        this.taskDetails = taskDetails;
    }
}
