package cn.taihealth.ih.domain.enums;

public enum SmsType {

    UNKNOWN("未配置", null),
    MAS("移动短信平台", "masSMSSender"),
    UMS("联通短信平台", "umsSMSSender"),
    ALI("阿里短信平台", "aliSMSSender"),
    TX("腾讯短信平台", "txSMSSender");

    private final String name;
    private final String type;

    SmsType(String name, String type){
        this.name = name;
        this.type = type;
    }

    public String getName(){
        return this.name;
    }
    public String getType(){
        return this.type;
    }

}
