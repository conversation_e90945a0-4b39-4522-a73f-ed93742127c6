package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = ChargeItem.TABLE_NAME, uniqueConstraints = {
    @UniqueConstraint(name = "U_CHARGE_ITEMS_ITEM_CODE_HOSPITAL_ID", columnNames = {"item_code", "hospital_id"})
}, indexes = {
    @Index(name = "idx_item_code", columnList = "item_code"),
    @Index(name = "idx_item_name", columnList = "item_name"),
    @Index(name = "ids_query_scope", columnList = "query_scope"),
})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ChargeItem extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_charge_items";

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_CHARGE_ITEMS_HOSPITAL_ID"))
    private Hospital hospital;

    public enum QueryScope {
        /**
         * 门诊
         */
        OUTPATIENT("0"),
        /**
         * 住院
         */
        INPATIENT("1"),
        /**
         * 门诊和住院
         */
        BOTH("2");

        QueryScope(String code) {
            this.code = code;
        }

        private final String code;

        public String getCode() {
            return code;
        }
    }

    @Comment("项目名称")
    @Column(name = "item_name")
// 项目名称	Y
    private String itemName;

    @Comment("范围")
    @Column(name = "query_scope")
// 范围	Y
    private QueryScope queryScope;

    @Comment("项目代码")
    @Column(name = "item_code")
// 项目代码	Y
    private String itemCode;

    @Comment("单价 单位人民币元，保留2位小数")
    @Column(name = "price")
// 单价	Y	单位人民币元，保留2位小数
    private String price;

    @Comment("可报单价 单位人民币元，保留2位小数")
    @Column(name = "expense_price")
// 可报单价	Y	单位人民币元，保留2位小数
    private String expensePrice;

    @Comment("不可报单价 单位人民币元，保留2位小数")
    @Column(name = "non_expense_price")
// 不可报单价	Y	单位人民币元，保留2位小数
    private String nonExpensePrice;

    @Comment("大项目名称存储")
    @Column(name = "set_item_name")
// 大项目名称存储	Y	套餐名称
    private String setItemName;

    @Comment("单位")
    @Column(name = "unit")
// 单位	Y
    private String unit;

    @Comment("规格")
    @Column(name = "item_spec")
// 规格	Y
    private String itemSpec;


    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public QueryScope getQueryScope() {
        return queryScope;
    }

    public void setQueryScope(QueryScope queryScope) {
        this.queryScope = queryScope;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getExpensePrice() {
        return expensePrice;
    }

    public void setExpensePrice(String expensePrice) {
        this.expensePrice = expensePrice;
    }

    public String getNonExpensePrice() {
        return nonExpensePrice;
    }

    public void setNonExpensePrice(String nonExpensePrice) {
        this.nonExpensePrice = nonExpensePrice;
    }

    public String getSetItemName() {
        return setItemName;
    }

    public void setSetItemName(String setItemName) {
        this.setItemName = setItemName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getItemSpec() {
        return itemSpec;
    }

    public void setItemSpec(String itemSpec) {
        this.itemSpec = itemSpec;
    }
}
