package cn.taihealth.ih.domain;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.dict.Sign;
import org.hibernate.annotations.Comment;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 */
@Entity
@Table(name = UserSignLog.TABLE_NAME, indexes = {
    @Index(name = "IDX_PATIENT_SIGN_LOGS_R", columnList = "patient_id, latest")
})
public class UserSignLog extends AbstractEntity {

    public static final String TABLE_NAME = "ih_user_sign_logs";

    @Comment("生命体征")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sign_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_USER_SIGN_LOGS_SIGN_ID"))
    private Sign sign;

    @Comment("用户")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id",
        foreignKey = @ForeignKey(name = "FK_USER_SIGN_LOGS_USER_ID"))
    private User user;

    @Comment("就诊人")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id",
        foreignKey = @ForeignKey(name = "FK_USER_SIGN_LOGS_PATIENT_ID"))
    private Patient patient;

    @Comment("生命体征分类编码")
    @Column(name = "group_code")
    private String groupCode;

    @Comment("生命体征类型名")
    @Column(name = "value")
    private String value;

    @Comment("生命体征数值")
    @Column(name = "num_value")
    private double numValue;

    @Comment("记录时间")
    @Column(name = "record_date", nullable = false, updatable = false)
    private Date recordDate = new Date();

    @Comment("是否是最新的生命体征值")
    @Column(name = "latest")
    private boolean latest = true;

    public UserSignLog() {
    }

    public UserSignLog(boolean latest) {
        this.latest = latest;
    }

    public Sign getSign() {
        return sign;
    }

    public void setSign(Sign sign) {
        this.sign = sign;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Date getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(Date recordDate) {
        this.recordDate = recordDate;
    }

    public boolean isLatest() {
        return latest;
    }

    public void setLatest(boolean latest) {
        this.latest = latest;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public double getNumValue() {
        return numValue;
    }

    public void setNumValue(double numValue) {
        this.numValue = numValue;
    }
}
