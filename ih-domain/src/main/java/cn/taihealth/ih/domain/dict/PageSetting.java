package cn.taihealth.ih.domain.dict;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.MenuType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = PageSetting.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class PageSetting extends UpdatableEntity implements Cloneable {

    public static final String TABLE_NAME = "ih_page_settings";

    public enum Style {
        /**
         * 样式：一个长条格 内容：图片，视频，动态信息 用途：作为Banner
         */
        BANNER,
        /**
         * 样式：左右对称-2格 内容：图片+文字 用途：主要模块入口
         */
        TWO_GRIDS,
        /**
         * 样式：水平分布-3格 内容：图片+文字 用途：次要功能入口
         */
        THREE_GRIDS,
        /**
         * 样式：水平分布-4格 内容：图片+文字 用途：次要功能入口
         */
        FOUR_GRIDS,
        /**
         * 样式：水平分布-2/3/4/5个格子 内容：图片+文字 用途：底部固定Tab
         */
        BOTTOM_TAB,
        /**
         * 类型：最新动态 样式：滚动图片 内容：图片+文字 用途：展示最新动态
         */
        RECENT_NEWS,
        /**
         * 非特殊样式
         */
        GENERAL,
        /**
         * 类型：top两格 样式：顶部椭圆的左右对称-2格 内容：图片+文字 用途：主要模块入口
         */
        TOP_TWO_GRIDS,
        /**
         * 类型：top三格 样式：顶部椭圆的水平分布-3格  内容：图片+文字 用途：主要模块入口
         */
        TOP_THREE_GRIDS,
        /**
         * 类型：直播 样式：最多4个表格显示的直播内容  内容：缩略图片+文字 用途：展示最多4个直播
         */
        LIVE,
        /**
         * 类型：科普内容 样式：展示1页科普文章，可以加载更多  内容：文章列表 用途：用于首页展示文章
         */
        POPULAR_SCIENCE_CONTENT,
        /**
         * 类型：我的医生 样式：  内容：  用途
         */
        MY_DOCTOR

    }

    public enum Type {
        /**
         * PC端
         */
        PC,
        /**
         * PC-医生端
         */
        PC_DOCTOR,
        /**
         * PC-医生端
         */
        PC_ADMIN,
        /**
         * PC-药师端
         */
        PC_PHARMACIST,
        /**
         * PC-护士端
         */
        PC_NURSE,
        /**
         * 手机-护士端
         */
        MOBILE_NURSE,
        /**
         * 用户端-H5
         */
        MOBILE_USER,
        /**
         * 医生端小程序首页
         */
        DOCTOR_MINI,
        /**
         * 患者端小程序首页
         */
        PATIENT_MINI,
        /**
         * 其他项目
         */
        OTHER_PROJECT,
        /**
         * 原始页面内容
         */
        OLD
    }

    public enum Group {
        /**
         * 互联网诊疗服务
         */
        NET_SERVICE,
        /**
         * 患者就医服务
         */
        PATIENT_SERVICE,
        /**
         * 健康管理
         */
        HEALTH_MANAGE,
        /**
         * 医院信息
         */
        HOSPITAL_INFORMATION,
        /**
         * 底部快捷菜单
         */
        BOTTOM_MENU,
        /**
         * 肾医服务(2022.11.11renameCKD管理)
         */
        RENAL_MEDICAL_SERVICE,

        /**
         * 腹透管理
         */
        PERITONEAL_DIALYSIS_MANAGE,
        /**
         * 医生服务
         */
        DOCTOR_SERVICE,
        /**
         * 其他项目
         */
        OTHER_PROJECT,
        /**
         * 未知，主要给PC端使用
         */
        UNKNOWN
    }

    public enum IconSize {
        /**
         * 大图标
         */
        LARGE,
        /**
         * 小图标
         */
        SMALL,
        /**
         * 未知
         */
        UNKNOWN,

        WEEK

    }

    @Comment("医院ID")
    @ManyToOne
    @JoinColumn(name = "hospital_id",
        foreignKey = @ForeignKey(name = "FK_PAGE_SETTING_HOSPITAL_ID"))
    private Hospital hospital;

    @Comment("上级页面设置ID")
    @ManyToOne
    @JoinColumn(name = "parent_id",
        foreignKey = @ForeignKey(name = "FK_PAGE_SETTING_PARENT_ID"))
    private PageSetting parent;

    @Comment("编码")
    @Column(name = "code")
    private String code;

    @Comment("图标url")
    @Column(name = "icon")
    private String icon;

    @Comment("线性图标url")
    @Column(name = "list_icon")
    private String listIcon;

    @Comment("选中时图标url")
    @Column(name = "selected_icon")
    private String selectedIcon;

    @Comment("名称")
    @Column(name = "name")
    private String name;

    @Comment("路由url")
    @Column(name = "url")
    private String url;

    @Comment("等级")
    @Column(name = "`level`")
    private Integer level;

    @Comment("链接")
    @Column(name = "more_url")
    private String moreUrl;

    @Comment("是否需要先有就诊人")
    @Column(name = "need_patient")
    private Boolean needPatient;

    @Comment("订单编号")
    @Column(name = "order_number")
    private Integer orderNumber;

    @Comment("样式")
    @Column(name = "style")
    @Enumerated(EnumType.STRING)
    private Style style = Style.GENERAL;

    @Comment("面对用户的类型")
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private Type type;

    @Comment("分组")
    @Column(name = "group_part")
    @Enumerated(EnumType.STRING)
    private Group group = Group.UNKNOWN;

    @Comment("图标尺寸")
    @Column(name = "icon_size")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("UNKNOWN")
    private IconSize iconSize = IconSize.UNKNOWN;

    @Comment("是否展示")
    @Column(name = "is_show")
    private boolean show;

    /**
     * 菜单类型: 菜单,按钮
     */
    @Comment("菜单类型: 菜单,按钮")
    @Column(name = "page_type")
    @ColumnDefault("MENU")
    @Enumerated(EnumType.STRING)
    private MenuType menuType = MenuType.MENU;

    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<PageSetting> pageSettings = Lists.newArrayList();

    @Comment("备注")
    @Column(name = "remark")
    private String remark;

    public PageSetting() {
    }

    public PageSetting(String code, String name, boolean show, Type type) {
        this.code = code;
        this.name = name;
        this.show = show;
        this.type = type;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public PageSetting getParent() {
        return parent;
    }

    public void setParent(PageSetting parent) {
        this.parent = parent;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public List<PageSetting> getPageSettings() {
        return pageSettings;
    }

    public void setPageSettings(List<PageSetting> pageSettings) {
        this.pageSettings = pageSettings;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getListIcon() {
        return listIcon;
    }

    public void setListIcon(String listIcon) {
        this.listIcon = listIcon;
    }

    public String getSelectedIcon() {
        return selectedIcon;
    }

    public void setSelectedIcon(String selectedIcon) {
        this.selectedIcon = selectedIcon;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getMoreUrl() {
        return moreUrl;
    }

    public void setMoreUrl(String moreUrl) {
        this.moreUrl = moreUrl;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer order) {
        this.orderNumber = order;
    }

    public Style getStyle() {
        return style;
    }

    public void setStyle(Style style) {
        this.style = style;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public Boolean getNeedPatient() {
        return needPatient;
    }

    public void setNeedPatient(Boolean needPatient) {
        this.needPatient = needPatient;
    }

    public Group getGroup() {
        return group;
    }

    public void setGroup(Group group) {
        this.group = group;
    }

    public IconSize getIconSize() {
        return iconSize;
    }

    public void setIconSize(IconSize iconSize) {
        this.iconSize = iconSize;
    }

    public MenuType getMenuType() {
        return menuType;
    }

    public void setMenuType(MenuType menuType) {
        this.menuType = menuType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public PageSetting clone() {
        try {
            PageSetting p = (PageSetting) super.clone();
            if (CollectionUtils.isNotEmpty(this.pageSettings)) {
                p.setPageSettings(this.pageSettings.stream().map(PageSetting::clone)
                                      .collect(Collectors.toList()));
            }
            return p;
        } catch (CloneNotSupportedException e) {
            return null;
        }
    }
}
