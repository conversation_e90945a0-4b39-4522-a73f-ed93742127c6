package cn.taihealth.ih.domain.his;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.enums.AggregatePayment;
import cn.taihealth.ih.domain.enums.PayStatus;
import cn.taihealth.ih.domain.enums.PaymentMethod;
import cn.taihealth.ih.domain.enums.PaymentStatus;
import com.google.common.collect.Lists;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = HisOutpatientChargeGroup.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class HisOutpatientChargeGroup extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_his_outpatient_charge_group";

    /**
     * 医院id
     */
    @Comment("医院id")
    @Column(name = "hospital_id")
    private Long hospitalId;

    /**
     * 线下科室id
     */
    @Column(name = "offline_dept_id")
    @Comment(value = "线下科室id")
    private Long offlineDeptId;

    /**
     * 线下科室code
     */
    @Column(name = "offline_dept_code")
    @Comment(value = "线下科室code")
    private String offlineDeptCode;

    /**
     * 线下科室名称
     */
    @Column(name = "offline_dept_name")
    @Comment(value = "线下科室名称")
    private String offlineDeptName;

    @Comment(value = "患者姓名")
    @Column(name = "patname")
    private String patname; // 患者姓名 (必输)

    @Comment(value = "患者id")
    @Column(name = "patient_id")
    private Long patientId; // 患者id

    @Comment(value = "就诊人在his系统中的id")
    @Column(name = "his_patid")
    private String hisPatId;

    /**
     * 总金额
     */
    @Comment(value = "总金额")
    @Column(name = "total_pay_amount")
    private Integer totalPayAmount;

    /**
     * 是否已支付
     */
    @Comment(value = "是否已支付")
    @Column(name = "payment_status")
    private PaymentStatus paymentStatus = PaymentStatus.UNPAID;

    /**
     * 平台流水号
     */
    @Column(name = "serial_no")
    @Comment(value = "平台流水号")
    private String serialNo;

    @Column(name = "pay_type")
    @Comment(value = "支付方式")
    private PaymentMethod payType = PaymentMethod.WECHAT; // 支付方式 (必输)

    @Comment("聚合支付方式")
    @Enumerated(EnumType.STRING)
    @Column(name = "aggregate_payment")
    @ColumnDefault("OTHER")
    private AggregatePayment aggregatePayment = AggregatePayment.OTHER;

    @Comment("0根据病人医保代码结算,1自费结算")
    @Column(name = "self_flag")
    @ColumnDefault("1")
    private int selfFlag = 1;

    @Column(name = "self_amount")
    @Comment(value = "自费金额")
    private Integer selfAmount;

    @Comment("医保支付金额，统筹+个人医保")
    @Column(name = "insurance_fee")
    @ColumnDefault("0")
    private int insuranceFee;

    @Comment("医保支付金额，统筹")
    @Column(name = "pub_fee")
    @ColumnDefault("0")
    private int pubFee;

    @Comment("医保支付金额，个人")
    @Column(name = "pub_account_fee")
    @ColumnDefault("0")
    private int pubAccountFee;

    /**
     * 退款总金额
     */
    @Column(name = "refund_amount")
    @ColumnDefault("0")
    private Integer refundAmount = 0;

    @Comment("医保支付自费部分退款金额")
    @Column(name = "self_refund_amount")
    private Integer selfRefundAmount;

    @Comment("医保支付医保部分退款金额，统筹+个人医保")
    @Column(name = "insurance_refund_fee")
    @ColumnDefault("0")
    private Integer insuranceRefundFee;

    @Comment("支付时间")
    @Column(name = "pay_time")
    private Date payTime;

    @Enumerated(EnumType.STRING)
    @Comment("医保支付的支付状态,只是互联网侧医保支付的状态,不能判断整个订单支付成功,整个订单是payTime")
    @Column(name = "internet_pay_status")
    @ColumnDefault("UNKNOWN")
    private PayStatus internetPayStatus;

    @Enumerated(EnumType.STRING)
    @Comment("医保支付的支付状态,只是his侧医保支付的状态,不能判断整个订单支付成功,整个订单是payTime")
    @Column(name = "his_pay_status")
    @ColumnDefault("UNKNOWN")
    private PayStatus hisPayStatus = PayStatus.UNKNOWN;

    @Comment("医保结算中心订单号，对应医保接口[6301][6302]中的payOrdId")
    @Column(name = "internet_pay_order_id")
    private String internetPayOrderId;

    @Comment("上海医保se02中的订单号")
    @Column(name = "sh_bill_no")
    private String shBillNo;

    @OneToMany(mappedBy = "hisOutpatientChargeGroup")
    private List<HisOutpatientCharge> hisOutpatientCharges = Lists.newArrayList();

    @Comment("该账单调用his结算接口是否成功")
    @Column(name = "is_settlement_successful")
    @ColumnDefault("1")
    private Boolean isSettlementSuccessful = false;
    public Long getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
    }

    public String getPatname() {
        return patname;
    }

    public void setPatname(String patname) {
        this.patname = patname;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public Integer getTotalPayAmount() {
        return totalPayAmount;
    }

    public void setTotalPayAmount(Integer totalPayAmount) {
        this.totalPayAmount = totalPayAmount;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public PaymentMethod getPayType() {
        return payType;
    }

    public void setPayType(PaymentMethod payType) {
        this.payType = payType;
    }

    public Integer getSelfAmount() {
        return selfAmount;
    }

    public void setSelfAmount(Integer selfAmount) {
        this.selfAmount = selfAmount;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public List<HisOutpatientCharge> getHisOutpatientCharges() {
        return hisOutpatientCharges;
    }

    public void setHisOutpatientCharges(List<HisOutpatientCharge> hisOutpatientCharges) {
        this.hisOutpatientCharges = hisOutpatientCharges;
    }

    public Integer getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Integer refundAmount) {
        this.refundAmount = refundAmount;
    }

    public int getInsuranceFee() {
        return insuranceFee;
    }

    public void setInsuranceFee(int insuranceFee) {
        this.insuranceFee = insuranceFee;
    }

    public int getSelfFlag() {
        return selfFlag;
    }

    public void setSelfFlag(int selfFlag) {
        this.selfFlag = selfFlag;
    }

    public int getPubFee() {
        return pubFee;
    }

    public void setPubFee(int pubFee) {
        this.pubFee = pubFee;
    }

    public int getPubAccountFee() {
        return pubAccountFee;
    }

    public void setPubAccountFee(int pubAccountFee) {
        this.pubAccountFee = pubAccountFee;
    }

    public Long getOfflineDeptId() {
        return offlineDeptId;
    }

    public void setOfflineDeptId(Long offlineDeptId) {
        this.offlineDeptId = offlineDeptId;
    }

    public String getOfflineDeptCode() {
        return offlineDeptCode;
    }

    public void setOfflineDeptCode(String offlineDeptCode) {
        this.offlineDeptCode = offlineDeptCode;
    }

    public String getOfflineDeptName() {
        return offlineDeptName;
    }

    public void setOfflineDeptName(String offlineDeptName) {
        this.offlineDeptName = offlineDeptName;
    }

    public PayStatus getInternetPayStatus() {
        return internetPayStatus;
    }

    public void setInternetPayStatus(PayStatus internetPayStatus) {
        this.internetPayStatus = internetPayStatus;
    }

    public PayStatus getHisPayStatus() {
        return hisPayStatus;
    }

    public void setHisPayStatus(PayStatus hisPayStatus) {
        this.hisPayStatus = hisPayStatus;
    }

    public String getInternetPayOrderId() {
        return internetPayOrderId;
    }

    public void setInternetPayOrderId(String internetPayOrderId) {
        this.internetPayOrderId = internetPayOrderId;
    }

    public Integer getSelfRefundAmount() {
        return selfRefundAmount;
    }

    public void setSelfRefundAmount(Integer selfRefundAmount) {
        this.selfRefundAmount = selfRefundAmount;
    }

    public Integer getInsuranceRefundFee() {
        return insuranceRefundFee;
    }

    public void setInsuranceRefundFee(Integer insuranceRefundFee) {
        this.insuranceRefundFee = insuranceRefundFee;
    }

    public String getHisPatId() {
        return hisPatId;
    }

    public void setHisPatId(String hisPatId) {
        this.hisPatId = hisPatId;
    }

    public Boolean getSettlementSuccessful() {
        return isSettlementSuccessful;
    }

    public void setSettlementSuccessful(Boolean settlementSuccessful) {
        isSettlementSuccessful = settlementSuccessful;
    }

    public String getShBillNo() {
        return shBillNo;
    }

    public void setShBillNo(String shBillNo) {
        this.shBillNo = shBillNo;
    }

    public AggregatePayment getAggregatePayment() {
        return aggregatePayment;
    }

    public void setAggregatePayment(AggregatePayment aggregatePayment) {
        this.aggregatePayment = aggregatePayment;
    }
}
