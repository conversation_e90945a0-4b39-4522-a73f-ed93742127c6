package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.commons.util.TimeUtils.Shift;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.nursing.NursingConsulItem;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = Schedule.TABLE_NAME,
    indexes = {
        @Index(name = "IDX_SCHEDULES_HOSPITAL_DEPT_MEDICAL_WORKER", columnList = "hospital_id,dept_id,medical_worker_id"),
        @Index(name = "IDX_SCHEDULES_START_END", columnList = "start_time,end_time")
    })
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class Schedule extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_schedules";

    public enum ScheduleType {
        NORMAL("普通", "0"),
        EXPERT("专家", "1"),
        SPECIAL("特需", "2"),
        REMOTE("远程门诊", "3"),
        SPECIALIST_EXPERT("专病专家", "4"),
        SPECIALIST("专病", "5"),
        INTEGRATED("整合门诊", "6"),
        TRADITIONAL_CHINESE_MEDICINE("中医膏方门诊", "7"),
        GENERAL_DOCTOR("普通医生门诊", "8"),
        ONLINE("在线门诊与咨询", "9");

        private final String name;
        private final String code;

        ScheduleType(String name, String code) {
            this.name = name;
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public String getCode() {
            return code;
        }

        public  static ScheduleType getByCode(String code) {
            switch (code) {
                case "0":
                    return NORMAL;
                case "1":
                    return EXPERT;
                case "2":
                    return SPECIAL;
                case "3":
                    return REMOTE;
                case "4":
                    return SPECIALIST_EXPERT;
                case "5":
                    return SPECIALIST;
                case "6":
                    return INTEGRATED;
                case "7":
                    return TRADITIONAL_CHINESE_MEDICINE;
                case "8":
                    return GENERAL_DOCTOR;
                case "9":
                    return ONLINE;
                default:
                    return null;
            }
        }

        public static ScheduleType  getByHisCode(String code) {
            switch (code) {
                case "0":
                case "8":
                case "9":
                    return NORMAL;
                case "1":
                    return EXPERT;
                case "2":
                    return SPECIAL;
                case "3":
                    return REMOTE;
                case "4":
                    return SPECIALIST_EXPERT;
                case "5":
                    return SPECIALIST;
                case "6":
                    return INTEGRATED;
                case "7":
                    return TRADITIONAL_CHINESE_MEDICINE;
                default:
                    return null;
            }
        }
    }

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_HOSPITAL_ID"))
    private Hospital hospital;

    @Comment("科室")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dept_id",
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_DEPT_ID"))
    private Dept dept;

    @Comment("咨询项目")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "consul_item_id",
            foreignKey = @ForeignKey(name = "FK_SCHEDULE_CONSUL_ITEM_ID"))
    private NursingConsulItem consulItem;

    @Comment("专家")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "medical_worker_id",
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_MEDICAL_WORKER_ID"))
    private MedicalWorker medicalWorker;

    @Comment("排班类型")
    @Column(name = "type", nullable = false)
    @ColumnDefault("NORMAL")
    @Enumerated(EnumType.STRING)
    private ScheduleType type;

    @Comment("开始时间")
    @Column(name = "start_time", nullable = false)
    private Date startTime;

    @Comment("结束时间")
    @Column(name = "end_time", nullable = false)
    private Date endTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_CREATOR_ID"))
    private User creator;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "editor_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_EDITOR_ID"))
    private User editor;

    @Comment("科室/专家排班计划")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "group_id", foreignKey = @ForeignKey(name = "FK_SCHEDULE_GROUP_ID"))
    private ScheduleGroup group;

//    @OneToOne(mappedBy = "schedule", fetch = FetchType.LAZY)
//    @Transient
//    private ScheduleUse scheduleUse;

    @Column(name = "window_count", nullable = false)
    @ColumnDefault("0")
    private int windowCount; // 窗口号源（线下）

    @ColumnDefault("0")
    @Column(name = "intercount", nullable = false)
    private int internetCount; // 网络预约号源（线下）

    @ColumnDefault("0")
    @Column(name = "phone_count", nullable = false)
    private int phoneCount; // 电话预约（线下）

    @ColumnDefault("0")
    @Column(name = "online_count", nullable = false)
    private int onlineCount; // 线上复诊（线上）

    public Schedule(User user) {
        this.creator = user;
        this.editor = user;
    }

    @Transient
    @JsonIgnore
    public Shift getShift() {
        return TimeUtils.getShift(startTime, endTime);
    }

    public Schedule() {
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Dept getDept() {
        return dept;
    }

    public void setDept(Dept dept) {
        this.dept = dept;
    }

    public MedicalWorker getMedicalWorker() {
        return medicalWorker;
    }

    public void setMedicalWorker(MedicalWorker medicalWorker) {
        this.medicalWorker = medicalWorker;
    }

    public ScheduleType getType() {
        return type;
    }

    public void setType(ScheduleType type) {
        this.type = type;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public User getCreator() {
        return creator;
    }

    public void setCreator(User creator) {
        this.creator = creator;
    }

    public User getEditor() {
        return editor;
    }

    public void setEditor(User editor) {
        this.editor = editor;
    }

//    public ScheduleUse getScheduleUse() {
//        return scheduleUse;
//    }
//
//    public void setScheduleUse(ScheduleUse scheduleUse) {
//        this.scheduleUse = scheduleUse;
//    }

    public int getWindowCount() {
        return windowCount;
    }

    public void setWindowCount(int windowCount) {
        this.windowCount = windowCount;
    }

    public int getInternetCount() {
        return internetCount;
    }

    public void setInternetCount(int internetCount) {
        this.internetCount = internetCount;
    }

    public int getPhoneCount() {
        return phoneCount;
    }

    public void setPhoneCount(int phoneCount) {
        this.phoneCount = phoneCount;
    }

    public int getOnlineCount() {
        return onlineCount;
    }

    public void setOnlineCount(int onlineCount) {
        this.onlineCount = onlineCount;
    }

    public ScheduleGroup getGroup() {
        return group;
    }

    public void setGroup(ScheduleGroup group) {
        this.group = group;
    }

    public NursingConsulItem getConsulItem() {
        return consulItem;
    }

    public void setConsulItem(NursingConsulItem consulItem) {
        this.consulItem = consulItem;
    }
}
