package cn.taihealth.ih.domain;

import cn.taihealth.ih.commons.config.ApplicationProperties;
import com.google.common.base.Preconditions;
import liquibase.Liquibase;
import liquibase.exception.LiquibaseException;
import liquibase.integration.spring.SpringLiquibase;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Configuration
@EntityScan("cn.taihealth.ih.domain")
@ComponentScan("cn.taihealth.ih.domain")
public class DatabaseConfiguration {
    private static final Logger log = LoggerFactory.getLogger(DatabaseConfiguration.class);
    private final String settingKey = "LIQUIBASE_CHANGELOG_VERSION";
    private final ApplicationProperties applicationProperties;

    public DatabaseConfiguration(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @Bean
    public SpringLiquibase liquibase(DataSource dataSource,
                                     DatabaseChangeExecListener databaseChangeExecListener,
                                     RedissonClient redissonClient) {
        Preconditions.checkNotNull(redissonClient);
        return runLiquibase(dataSource, databaseChangeExecListener, 0);
    }

    @Bean
    @DependsOn("liquibase")
    public SpringLiquibase liquibase1(DataSource dataSource,
                                      DatabaseChangeExecListener databaseChangeExecListener,
                                      RedissonClient redissonClient) {
        Preconditions.checkNotNull(redissonClient);
        return runLiquibase(dataSource, databaseChangeExecListener, 1);
    }

    private SpringLiquibase runLiquibase(DataSource dataSource, DatabaseChangeExecListener databaseChangeExecListener, int i) {
        SpringLiquibase liquibase = new SpringLiquibase() {
            @Override
            protected void performUpdate(Liquibase liquibase) throws LiquibaseException {
                liquibase.setChangeExecListener(databaseChangeExecListener);
                super.performUpdate(liquibase);
                DatabaseChangeExecListener.changeCount += liquibase.getDatabaseChangeLog().getChangeSets().size();
            }

        };
        liquibase.setContexts(null);
        liquibase.setDefaultSchema(null);
        liquibase.setDropFirst(false);
        liquibase.setDataSource(dataSource);
        if (i == 0) {
            liquibase.setChangeLog("classpath:config/liquibase/master.xml");
        } else {
            liquibase.setChangeLog("classpath:config/liquibase/master" + i + ".xml");
            liquibase.setDatabaseChangeLogTable("databasechangelog" + i);
        }
        liquibase.setBeanName("springLiquibase" + i);
        boolean liquibaseEnabled = applicationProperties.getLiquibase().isShouldRun();
        if (liquibaseEnabled) {
            boolean shouldRun = shouldRun(dataSource, i);
            liquibase.setShouldRun(shouldRun);
            if (shouldRun) {
                saveLiquibaseChangelogVersion(dataSource, i);
            }
        } else {
            liquibase.setShouldRun(false);
        }

        return liquibase;
    }

    /**
     * 是否需要执行
     * @param dataSource
     * @param i
     * @return
     */
    private boolean shouldRun(DataSource dataSource, int i) {
        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            String sql = "select setting_value FROM ih_system_settings where setting_key = ?";
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, settingKey);
            if (result.isEmpty() || !result.get(0).containsKey("setting_value")) {
                return true;
            }
            int version = Integer.parseInt(result.get(0).get("setting_value") + "");
            return i >= version;
        } catch (Exception ignored) {
            log.info("数据库表不存在,需要全部执行");
            return true;
        }
    }

    /**
     * 保存执行号
     * @param dataSource
     * @param i
     */
    private void saveLiquibaseChangelogVersion(DataSource dataSource, int i) {
        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            String sql = "select setting_value FROM ih_system_settings where setting_key = ?";
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, settingKey);
            if (result.isEmpty()) {
                // 插入
                String insert = "INSERT INTO ih_system_settings (id, created_date, updated_date, setting_key, setting_value) VALUES (?, ?, ?, ?, ?)";
                jdbcTemplate.update(insert, 1, new Date(), new Date(), settingKey, i + "");
            } else {
                // 更新
                String update = "UPDATE ih_system_settings SET updated_date = ?, setting_value = ? WHERE setting_key = ?";
                jdbcTemplate.update(update, new Date(), i + "", settingKey);
            }

        } catch (Exception ignored) {
            log.info("数据库表不存在,忽略,等待下一次执行");
        }
    }


}
