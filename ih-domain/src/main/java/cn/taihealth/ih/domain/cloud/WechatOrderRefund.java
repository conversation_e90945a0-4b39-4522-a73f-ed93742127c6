package cn.taihealth.ih.domain.cloud;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.dict.StringListConvert;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.List;

/**
 */
@Entity
@Table(name = WechatOrderRefund.TABLE_NAME,
indexes = {
    @Index(name = "IDX_WECHAT_ORDERS_REFUNDS_REFUND_ID", columnList = "refund_id"),
    @Index(name = "IDX_WECHAT_ORDERS_REFUNDS_OUT_REFUND_NO", columnList = "out_refund_no"),
    @Index(name = "IDX_WECHAT_ORDERS_REFUNDS_OUT_TRADE_NO", columnList = "out_trade_no")
}, uniqueConstraints = {
    @UniqueConstraint(name = "U_WECHAT_ORDERS_REFUNDS_OUT_REFUND_NO", columnNames = {"out_refund_no"}),
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class WechatOrderRefund extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_wechat_order_refunds";

    /**
     * 微信支付退款号
     */
    @Comment("微信支付退款号")
    @Column(name = "refund_id")
    private String refundId;
    /**
     * 商户退款单号
     */
    @Comment("商户退款单号")
    @Column(name = "out_refund_no")
    private String outRefundNo;
    /**
     * 微信支付订单号
     */
    @Comment("微信支付订单号")
    @Column(name = "transaction_id")
    private String transactionId;
    /**
     * 商户订单号
     */
    @Comment("商户订单号")
    @Column(name = "out_trade_no")
    private String outTradeNo;
    /**
     * 退款金额
     */
    @Comment("退款金额")
    @Column(name = "amount")
    private Integer amount;
    /**
     * 退款渠道
     */
    @Comment("退款渠道")
    @Column(name = "channel")
    private String channel;
    /**
     * 退款入账账户
     */
    @Comment("退款入账账户")
    @Column(name = "user_received_account")
    private String userReceivedAccount;
    /**
     * 退款成功时间
     */
    @Comment("退款成功时间")
    @Column(name = "success_time")
    private String successTime;
    /**
     * 退款状态
     * SUCCESS：退款成功 -- 从微信得到了确认退款成功
     * CLOSED：退款关闭 -- 暂时没有这个业务
     * PROCESSING：退款处理中 -- 其余状态都是这个状态
     * ABNORMAL：退款异常 -- 比如余额不足微信返回了失败
     */
    @Comment("退款状态 SUCCESS：退款成功, CLOSED：退款关闭, PROCESSING：退款处理中, ABNORMAL：退款异常")
    @Column(name = "status")
    private String status = "PROCESSING";
    /**
     * 资金账户
     * UNSETTLED : 未结算资金
     * AVAILABLE : 可用余额
     * UNAVAILABLE : 不可用余额
     * OPERATION : 运营户
     */
    @Comment("资金账户 UNSETTLED : 未结算资金, AVAILABLE : 可用余额, UNAVAILABLE : 不可用余额, OPERATION : 运营户")
    @Column(name = "funds_account")
    private String fundsAccount;

    @Comment("退款失败后重试的次数")
    @Column(name = "refund_count")
    private Integer refundCount = 0;

    /**
     * 部分退款业务明细流水号
     * 对应 HisOutpatientChargeRefundRecord 的 cancelSettleId
     */
    @Comment("部分退款业务明细流水号")
    @Column(name = "refund_detail_no", columnDefinition = "text")
    @Convert(converter = StringListConvert.class)
    private List<String> refundDetailNo;

    @Comment("最后一次退款失败的原因")
    @Column(name = "failed_reason")
    private String failedReason;

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getUserReceivedAccount() {
        return userReceivedAccount;
    }

    public void setUserReceivedAccount(String userReceivedAccount) {
        this.userReceivedAccount = userReceivedAccount;
    }

    public String getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(String successTime) {
        this.successTime = successTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFundsAccount() {
        return fundsAccount;
    }

    public void setFundsAccount(String fundsAccount) {
        this.fundsAccount = fundsAccount;
    }

    public List<String> getRefundDetailNo() {
        return refundDetailNo;
    }

    public void setRefundDetailNo(List<String> refundDetailNo) {
        this.refundDetailNo = refundDetailNo;
    }

    public Integer getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(Integer refundCount) {
        this.refundCount = refundCount;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }
}
