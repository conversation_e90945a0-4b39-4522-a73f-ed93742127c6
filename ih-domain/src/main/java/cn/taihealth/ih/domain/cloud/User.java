package cn.taihealth.ih.domain.cloud;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.UserMedicalWorkerRel;
import cn.taihealth.ih.domain.convert.SM4Convert;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.domain.hospital.FollowupRecord;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.UserPlatformInfo;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gitq.jedi.common.app.AuthType;
import com.gitq.jedi.common.app.SimpleUser;
import org.hibernate.Hibernate;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@Entity
@Table(name = User.TABLE_NAME, uniqueConstraints = {
    @UniqueConstraint(name = "U_USERS_USERNAME", columnNames = {"username"}),
    @UniqueConstraint(name = "U_USERS_MOBILE", columnNames = {"mobile"}),})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class User extends UpdatableEntity implements SimpleUser {

    public static final String TABLE_NAME = "ih_users";
    @Comment("名称")
    @Column(name = "username", nullable = false, length = 32)
    private String username;
    @Comment("联系方式")
    @Column(name = "mobile", length = 20, nullable = false)
    private String mobile;
    @Comment("全称")
    @Column(name = "full_name", length = 128)
    private String fullName;
    @Comment("邮箱")
    @Column(name = "email", length = 128)
    private String email;

    @Comment("密码Hash")
    @JsonIgnore
    @Column(name = "password_hash")
    @Convert(converter = SM4Convert.class)
    private String password;

    @Comment("用户类型")
    @Column(name = "user_type", nullable = false)
    private UserType userType = UserType.USER;
    @Comment("是否锁定")
    @Column(name = "is_locked")
    private boolean locked = false;
    @Comment("出生日期")
    @Column(name = "birthday")
    private Date birthday;
    @Comment("性别")
    @Column(name = "gender", nullable = false)
    private Gender gender = Gender.UNKNOWN;
    @Comment("头像类型")
    @Column(name = "avatar_type")
    private AvatarType avatarType = AvatarType.LETTER;
    @Comment("头像上传ID")
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "uploaded_avatar_id", foreignKey = @ForeignKey(name = "FK_USERS_UPLOADED_AVATAR_ID"))
    private Upload uploadedAvatar;
    @Comment("头像URL")
    @Column(name = "avatar_url")
    private String avatarUrl;
    @Comment("地址")
    @Column(name = "address")
    private String address;
    @Comment("身份证号")
    @Column(name = "identity", length = 20)
    private String identity;
    @Comment("是否实名认证")
    @Column(name = "is_identified")
    private boolean identified; // 是否实名认证
    @Comment("实名认证时间")
    @Column(name = "identified_date")
    private Date identifiedDate;
    @Comment("注册来源")
    @Column(name = "signup_source")
    private Source signupSource = Source.APP;
    @Comment("来源")
    @Column(name = "source")
    private String source = "APP";
    @Comment("注册ID")
    @Column(name = "signup_ip")
    private String signupIp;
    @Comment("能力")
    @Column(name = "capabilities")
    private String capabilities;
    @Comment("是否VIP")
    @Column(name = "is_vip")
    private boolean vip;
    @Comment("是否统一最终用户许可协议")
    @Column(name = "is_eula_agreed")
    private boolean eulaAgreed;
    /**
     * 是否进行过人脸识别
     */
    @Comment("是否进行过人脸识别")
    @Column(name = "is_face_recognized")
    @ColumnDefault("0")
    private boolean faceRecognized;

    @Comment("健康档案密码")
    @JsonIgnore
    @Column(name = "health_record_password")
    private String healthRecordPassword;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "ih_user_user_role_rels",
        joinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id"),
        inverseJoinColumns = @JoinColumn(name = "user_role_id", referencedColumnName = "id"))
    private List<UserRole> userRoles = Lists.newArrayList();

    @ManyToMany(fetch = FetchType.LAZY)
    @Fetch(org.hibernate.annotations.FetchMode.SUBSELECT)
    @JoinTable(name = "ih_user_platform_role_rels",
        joinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id"),
        inverseJoinColumns = @JoinColumn(name = "platform_role_id", referencedColumnName = "id"))
    private List<PlatformRole> platformRoles = Lists.newArrayList();

//    /**
//    * 身份证国徽面路径
//    * */
//    @Column(name = "national_emblem")
//    private String nationalEmblem;
//
//    /**
//     * 身份证人像面路径
//     * */
//    @Column(name = "portrait")
//    private String portrait;
//
//    /**
//     * 身份证号
//     * */
//    @Column(name = "identity_card")
//    private String identityCard;

    /**
     * 电子签名路径
     * */
//    @Column(name = "signature_png")
//    private String signaturePng;
    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
    private List<Patient> patients = Lists.newArrayList();
    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
    private List<MedicalWorker> medicalWorkers = Lists.newArrayList();

    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
    private List<UserMedicalWorkerRel> userMedicalWorkerRels = Lists.newArrayList();

    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
    private List<UserPlatformInfo> userPlatformInfoList = Lists.newArrayList();

    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
    private List<FollowupRecord> followupRecords = Lists.newArrayList();


    public List<FollowupRecord> getFollowupRecords() {
        return followupRecords;
    }

    public void setFollowupRecords(List<FollowupRecord> followupRecords) {
        this.followupRecords = followupRecords;
    }

    public List<UserPlatformInfo> getUserPlatformInfoList() {
        return userPlatformInfoList;
    }

    public void setUserPlatformInfoList(List<UserPlatformInfo> userPlatformInfoList) {
        this.userPlatformInfoList = userPlatformInfoList;
    }

    public List<UserMedicalWorkerRel> getUserMedicalWorkerRels() {
        return userMedicalWorkerRels;
    }

    public void setUserMedicalWorkerRels(List<UserMedicalWorkerRel> userMedicalWorkerRels) {
        this.userMedicalWorkerRels = userMedicalWorkerRels;
    }

    @Override
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    @Override
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public boolean isLocked() {
        return locked;
    }

    public void setLocked(boolean locked) {
        this.locked = locked;
    }

    @JsonIgnore
    public boolean isNonLocked() {
        return !isLocked();
    }

    public boolean isIdentified() {
        return identified;
    }

    public void setIdentified(boolean identified) {
        this.identified = identified;
    }

    public Date getIdentifiedDate() {
        return identifiedDate;
    }

    public void setIdentifiedDate(Date identifiedDate) {
        this.identifiedDate = identifiedDate;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public AvatarType getAvatarType() {
        return avatarType;
    }

    public void setAvatarType(AvatarType avatarType) {
        this.avatarType = avatarType;
    }

    public boolean isFaceRecognized() {
        return faceRecognized;
    }

    public void setFaceRecognized(boolean faceRecognized) {
        this.faceRecognized = faceRecognized;
    }

    @Override
    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Source getSignupSource() {
        return signupSource;
    }

    public void setSignupSource(Source signupSource) {
        this.signupSource = signupSource;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSignupIp() {
        return signupIp;
    }

    public void setSignupIp(String signupIp) {
        this.signupIp = signupIp;
    }

    public String getCapabilities() {
        return capabilities;
    }

    public void setCapabilities(String capabilities) {
        this.capabilities = capabilities;
    }

    public Upload getUploadedAvatar() {
        return uploadedAvatar;
    }

    public void setUploadedAvatar(Upload uploadedAvatar) {
        this.uploadedAvatar = uploadedAvatar;
    }

    public boolean isVip() {
        return vip;
    }

    public void setVip(boolean vip) {
        this.vip = vip;
    }

    public boolean isEulaAgreed() {
        return eulaAgreed;
    }

    public void setEulaAgreed(boolean eulaAgreed) {
        this.eulaAgreed = eulaAgreed;
    }

    public String getHealthRecordPassword() {
        return healthRecordPassword;
    }

    public void setHealthRecordPassword(String healthRecordPassword) {
        this.healthRecordPassword = healthRecordPassword;
    }

    public List<UserRole> getUserRoles() {
        return userRoles;
    }

    public void setUserRoles(List<UserRole> userRoles) {
        this.userRoles = userRoles;
    }

    public List<MedicalWorker> getMedicalWorkers() {
        return medicalWorkers;
    }

    public void setMedicalWorkers(List<MedicalWorker> medicalWorkers) {
        this.medicalWorkers = medicalWorkers;
    }

    public List<PlatformRole> getPlatformRoles() {
        return platformRoles;
    }

    public void setPlatformRoles(List<PlatformRole> platformRoles) {
        this.platformRoles = platformRoles;
    }

    @JsonIgnore
    @Override
    public AuthType getAuthType() {
        return AuthType.DATABASE;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }

        User user = (User) o;
        return Objects.equals(getUsername(), user.getUsername());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getUsername());
    }

    public List<Patient> getPatients() {
        return patients;
    }

    public void setPatients(List<Patient> patients) {
        this.patients = patients;
    }

    @Override
    public String toString() {
        return "User {" + "username='" + username + '\'' + ", mobile='" + mobile + '\''
            + ", fullName='" + fullName + '\'' + ", birthday'" + birthday + '\'' +
            ", avatarUrl='" + avatarUrl + '\'' + ", locked='" + locked + '\''
            + "}";
    }

    public enum AvatarType {
        LETTER, UPLOAD, URL
    }

    public enum Source {
        APP,//app注册
        WEB,//web注册
        BACKEND,//后台注册
        WECHAT_APP,//微信-移动应用
        WECHAT_WEB,//微信-网站应用
        MP, //微信-公众号
        TEST, // 自动化测试
        API, // API服务调用
        QUEGE, // 鹊哥
        SST // 盛事通
        ;

        public boolean isWechatMiniProgram() {
            return false;
        }
    }


    public enum UserType {
        USER, ORGANIZATION, APP, TEST, API
    }
}
