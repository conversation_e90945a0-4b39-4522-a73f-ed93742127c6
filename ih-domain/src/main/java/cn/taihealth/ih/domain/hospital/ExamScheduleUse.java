package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;

import javax.persistence.*;

import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = ExamScheduleUse.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ExamScheduleUse extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_exam_schedule_uses";

    @Comment("设备排班Id")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "exam_schedule_id", nullable = false,
            foreignKey = @ForeignKey(name = "FK_SCHEDULE_USER_SCHEDULE_ID"))
    private ExamSchedule examSchedule;

    @Comment("住院预约数量")
    @Column(name = "in_used_count", nullable = false)
    private int inUsedCount;

    @Comment("门诊预约数量")
    @Column(name = "out_used_count", nullable = false)
    private int outUsedCount;

    public ExamScheduleUse(ExamSchedule schedule) {
        this.examSchedule = schedule;
    }

    public ExamScheduleUse() {
    }

    public ExamSchedule getExamSchedule() {
        return examSchedule;
    }

    public void setExamSchedule(ExamSchedule examSchedule) {
        this.examSchedule = examSchedule;
    }

    public int getInUsedCount() {
        return inUsedCount;
    }

    public void setInUsedCount(int inUsedCount) {
        this.inUsedCount = inUsedCount;
    }

    public int getOutUsedCount() {
        return outUsedCount;
    }

    public void setOutUsedCount(int outUsedCount) {
        this.outUsedCount = outUsedCount;
    }
}
