package cn.taihealth.ih.domain.his;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.dict.StringListConvert;
import cn.taihealth.ih.domain.enums.PaymentMethod;
import cn.taihealth.ih.domain.enums.PaymentStatus;
import com.google.common.collect.Lists;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = HisInpatientHospitalCharge.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class HisInpatientHospitalCharge extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_his_inpatient_hospital_charge";

    @Comment("用户")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "FK_INPATIENT_CHARGE_USER_ID"))
    private User user;

    @Comment("患者姓名")
    @Column(name = "patname")
    private String patname; // 患者姓名 (必输)

    @Comment(value = "患者在his系统中的id")
    @Column(name = "his_patid")
    private String his_patid;

    @Comment("患者ID")
    @Column(name = "patient_id")
    private String patient_id; // 患者姓名 (必输)

    @Comment("医院ID")
    @Column(name = "hospital_id")
    private String hospital_id;

    @Comment("住院号")
    @Column(name = "regno")
    private String regno; // 住院号 (必输)

    @Comment("预充值流水号")
    @Column(name = "advance_charge_id", length = 255)
    private String advance_charge_id; // 预充值流水号 (可选)

    @Comment("预充值流水号")
    @Column(name = "out_trade_no")
    private String out_trade_no; // HIS订单号 (必输)

    @Comment("HIS结算单号")
    @Column(name = "settle_id")
    @Convert(converter = StringListConvert.class)
    private List<String> settleId = Lists.newArrayList();

    @Comment("平台流水号")
    @Column(name = "serial_no", length = 255)
    private String serial_no; // 平台流水号 (可选)

    @Comment("支付方式")
    @Column(name = "pay_type")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("WECHAT")
    private PaymentMethod pay_type = PaymentMethod.WECHAT; // 支付方式 (必输)

    @Comment("支付金额")
    @Column(name = "self_amount")
    private String self_amount; // 支付金额 (必输)

    @Comment("退款金额")
    @Column(name = "refund_amount")
    @ColumnDefault("0")
    private int refund_amount; // 退款金额

    @Comment("支付流水号")
    @Column(name = "trade_no")
    private String trade_no; // 支付流水号 (必输)

    @Comment("账户ID")
    @Column(name = "account_id")
    private String account_id; // 账户标识 (可选)

    @Comment("支付时间")
    @Column(name = "pay_time")
    private Date pay_time; // 支付时间 (必输)

    @Comment("退款时间")
    @Column(name = "refund_time")
    private Date refund_time; // 退款时间

    @Comment("支付渠道")
    @Column(name = "port")
    private String port; // 支付渠道 (必输)

    @Comment("微信用户标识")
    @Column(name = "open_id", length = 255)
    private String open_id; // 微信用户标识 (可选)

    @Comment("微信用户联系方式")
    @Column(name = "open_phone", length = 255)
    private String open_phone; // 微信用户联系方式 (可选)

    @Comment("充值人联系电话")
    @Column(name = "telephone", length = 255)
    private String telephone; // 充值人联系电话 (可选)

    @Comment("充值联系人姓名")
    @Column(name = "contacts_name", length = 255)
    private String contacts_name; // 充值联系人姓名 (可选)

    @Comment("充值联系人证件号")
    @Column(name = "contacts_certificate_no", length = 255)
    private String contacts_certificate_no; // 充值联系人证件号 (可选)

    @Comment("科室")
    @Column(name = "dept_name")
    private String deptName;

    @Comment("医生")
    @Column(name = "doctor_name")
    private String doctorName;

    /**
     * 是否已支付
     */
    @Comment("是否已支付")
    @Column(name = "payment_status")
    @Enumerated(EnumType.STRING)
    private PaymentStatus paymentStatus = PaymentStatus.UNPAID;

    @Comment("该账单调用his结算接口是否成功")
    @Column(name = "is_settlement_successful")
    @ColumnDefault("1")
    private Boolean isSettlementSuccessful = false;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getPatname() {
        return patname;
    }

    public void setPatname(String patname) {
        this.patname = patname;
    }

    public String getHis_patid() {
        return his_patid;
    }

    public void setHis_patid(String his_patid) {
        this.his_patid = his_patid;
    }

    public String getRegno() {
        return regno;
    }

    public void setRegno(String regno) {
        this.regno = regno;
    }

    public String getAdvance_charge_id() {
        return advance_charge_id;
    }

    public void setAdvance_charge_id(String advance_charge_id) {
        this.advance_charge_id = advance_charge_id;
    }

    public String getOut_trade_no() {
        return out_trade_no;
    }

    public void setOut_trade_no(String out_trade_no) {
        this.out_trade_no = out_trade_no;
    }

    public String getSerial_no() {
        return serial_no;
    }

    public void setSerial_no(String serial_no) {
        this.serial_no = serial_no;
    }

    public String getSelf_amount() {
        return self_amount;
    }

    public void setSelf_amount(String self_amount) {
        this.self_amount = self_amount;
    }

    public String getTrade_no() {
        return trade_no;
    }

    public void setTrade_no(String trade_no) {
        this.trade_no = trade_no;
    }

    public String getAccount_id() {
        return account_id;
    }

    public void setAccount_id(String account_id) {
        this.account_id = account_id;
    }

    public Date getPay_time() {
        return pay_time;
    }

    public void setPay_time(Date pay_time) {
        this.pay_time = pay_time;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getOpen_id() {
        return open_id;
    }

    public void setOpen_id(String open_id) {
        this.open_id = open_id;
    }

    public String getOpen_phone() {
        return open_phone;
    }

    public void setOpen_phone(String open_phone) {
        this.open_phone = open_phone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getContacts_name() {
        return contacts_name;
    }

    public void setContacts_name(String contacts_name) {
        this.contacts_name = contacts_name;
    }

    public String getContacts_certificate_no() {
        return contacts_certificate_no;
    }

    public void setContacts_certificate_no(String contacts_certificate_no) {
        this.contacts_certificate_no = contacts_certificate_no;
    }

    public String getPatient_id() {
        return patient_id;
    }

    public void setPatient_id(String patient_id) {
        this.patient_id = patient_id;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getHospital_id() {
        return hospital_id;
    }

    public void setHospital_id(String hospital_id) {
        this.hospital_id = hospital_id;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public int getRefund_amount() {
        return refund_amount;
    }

    public void setRefund_amount(int refund_amount) {
        this.refund_amount = refund_amount;
    }

    public Date getRefund_time() {
        return refund_time;
    }

    public void setRefund_time(Date refund_time) {
        this.refund_time = refund_time;
    }

    public PaymentMethod getPay_type() {
        return pay_type;
    }

    public void setPay_type(PaymentMethod pay_type) {
        this.pay_type = pay_type;
    }

    public List<String> getSettleId() {
        return settleId;
    }

    public void setSettleId(List<String> settleId) {
        this.settleId = settleId;
    }

    public Boolean getSettlementSuccessful() {
        return isSettlementSuccessful;
    }

    public void setSettlementSuccessful(Boolean settlementSuccessful) {
        isSettlementSuccessful = settlementSuccessful;
    }
}
