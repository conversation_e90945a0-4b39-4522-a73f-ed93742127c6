package cn.taihealth.ih.domain.hospital;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.ScheduleModel;
import cn.taihealth.ih.domain.hospital.Schedule.ScheduleType;
import cn.taihealth.ih.domain.nursing.NursingConsulItem;
import com.google.common.collect.Lists;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 科室/专家排班计划
 */
@Entity
@Table(name = ScheduleGroup.TABLE_NAME)
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ScheduleGroup extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_schedule_groups";

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_GROUPS_HOSPITAL_ID"))
    private Hospital hospital;

    @Comment("科室")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dept_id",
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_GROUPS_DEPT_ID"))
    private Dept dept;

    @Comment("咨询项目")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "consul_item_id",
            foreignKey = @ForeignKey(name = "FK_SCHEDULE_GROUPS_CONSUL_ITEM_ID"))
    private NursingConsulItem consulItem;

    @Comment("用户")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_GROUPS_USER_ID"))
    private User creator;

    @Comment("专家")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "medical_worker_id",
        foreignKey = @ForeignKey(name = "FK_SCHEDULE_GROUPS_MEDICAL_WORKER_ID"))
    private MedicalWorker medicalWorker;

    @Comment("模式")
    @Column(name = "model", nullable = false)
    @Enumerated(EnumType.STRING)
    private ScheduleModel model = ScheduleModel.REPEAT;

    @Comment("重复周期")
    @Column(name = "repeat_str", nullable = false)
    @Convert(converter = ScheduleRepeatConvert.class)
    private List<Integer> repeat = Lists.newArrayList();

    @Comment("开始日期")
    @Column(name = "start_time", nullable = false)
    private Date startTime;

    @Comment("结束日期")
    @Column(name = "end_time", nullable = false)
    private Date endTime;

    @OneToMany(mappedBy = "group", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<ScheduleGroupShift> shifts = Lists.newArrayList();

    @Comment("排班类型")
    @Column(name = "schedule_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private Schedule.ScheduleType type;

    public ScheduleGroup() {

    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public Dept getDept() {
        return dept;
    }

    public void setDept(Dept dept) {
        this.dept = dept;
    }

    public User getCreator() {
        return creator;
    }

    public void setCreator(User creator) {
        this.creator = creator;
    }

    public ScheduleModel getModel() {
        return model;
    }

    public void setModel(ScheduleModel model) {
        this.model = model;
    }

    public List<Integer> getRepeat() {
        return repeat;
    }

    public void setRepeat(List<Integer> repeat) {
        this.repeat = repeat;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<ScheduleGroupShift> getShifts() {
        return shifts;
    }

    public void setShifts(List<ScheduleGroupShift> shifts) {
        this.shifts = shifts;
    }

    public MedicalWorker getMedicalWorker() {
        return medicalWorker;
    }

    public void setMedicalWorker(MedicalWorker medicalWorker) {
        this.medicalWorker = medicalWorker;
    }

    public ScheduleType getType() {
        return type;
    }

    public void setType(ScheduleType type) {
        this.type = type;
    }

    public NursingConsulItem getConsulItem() {
        return consulItem;
    }

    public void setConsulItem(NursingConsulItem consulItem) {
        this.consulItem = consulItem;
    }
}
