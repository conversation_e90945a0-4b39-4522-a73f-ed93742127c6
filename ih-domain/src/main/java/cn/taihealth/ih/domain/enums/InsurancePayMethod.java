package cn.taihealth.ih.domain.enums;

public enum InsurancePayMethod {

    ALL("", "现金+医保"),
    CASH_ONLY("CASH_ONLY", "现金");

    private final String code;

    private final String name;

    InsurancePayMethod(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName(){
        return this.name;
    }

}
