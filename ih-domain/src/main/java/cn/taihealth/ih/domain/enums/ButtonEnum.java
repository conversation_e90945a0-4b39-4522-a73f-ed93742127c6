package cn.taihealth.ih.domain.enums;

public enum ButtonEnum {
    /**
     * 咨询助手
     */
    doc_mini_consult_assistant("doc_mini_consult_assistant", "咨询助手", 1),
    /**
     * CKD助手
     */
    doc_mini_ckd_assistant("doc_mini_consult_assistant", "CKD助手", 2),
    /**
     * 腹透助手
     */
    doc_mini_ft_assistant("doc_mini_ft_assistant", "FT助手", 3),
    /**
     * 代理注册
     */
    doc_mini_delegate_register("doc_mini_delegate_register", "代理注册", 4),
    /**
     * 患教管理
     */
    doc_mini_teaching_management("doc_mini_teaching_management", "患教管理", 5),
    /**
     * 我的医生
     */
    doc_mini_my_doctor("doc_mini_my_doctor", "我的医生", 6),
    /**
     * 查看CKD患者电话号码
     */
    doc_mini_view_ckd_phone("doc_mini_view_ckd_phone", "查看CKD患者电话号码", -1),
    /**
     * 查看腹透患者电话号码
     */
    doc_mini_view_ft_phone("doc_mini_view_ft_phone", "查看腹透患者电话号码", -1),
    ;

    private final String code;
    private final String name;
    private final Integer order;

    ButtonEnum(String code, String name, Integer order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Integer getOrder() {
        return order;
    }
}