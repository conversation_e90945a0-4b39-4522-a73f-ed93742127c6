package cn.taihealth.ih.domain.convert;

import cn.taihealth.ih.domain.AbstractEntity;
import cn.taihealth.ih.domain.UserAddress;

/**
 */
public class UserAddressVO extends AbstractEntity {

    /**
     * 标签
     */
    private String label;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县或者区
     */
    private String area;

    /**
     * 电话号码
     */
    private String telephone;

    /**
     * 收货人姓名
     */
    private String receiverName;

    public UserAddressVO() {
    }

    public UserAddressVO(UserAddress userAddress) {
        this.setId(userAddress.getId());
        this.setLabel(userAddress.getLabel());
        this.setAddress(userAddress.getAddress());
        this.setLongitude(userAddress.getLongitude());
        this.setLatitude(userAddress.getLatitude());
        this.setProvince(userAddress.getProvince());
        this.setCity(userAddress.getCity());
        this.setArea(userAddress.getArea());
        this.setTelephone(userAddress.getTelephone());
        this.setReceiverName(userAddress.getReceiverName());
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

}
