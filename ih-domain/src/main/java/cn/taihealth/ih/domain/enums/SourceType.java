package cn.taihealth.ih.domain.enums;

/**
 * @Author: jzs
 * @Date: 2023-10-03
 */
public enum SourceType {
    /**
     * 线上就诊
     */
    DEPT_SOURCE("科室号源", "1"),
    /**
     * 线下就诊
     */
    DOCTOR_SOURCE("医生号源", "2");

    private final String name;
    private final String code;

    private SourceType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static SourceType getByCode(String code) {
        switch (code) {
            case "1":
                return DEPT_SOURCE;
            case "2":
                return DOCTOR_SOURCE;
            default:
                return null;
        }
    }
}
