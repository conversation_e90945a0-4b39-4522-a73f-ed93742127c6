package cn.taihealth.ih.domain.copyappointment;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.User;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import javax.persistence.*;

@Entity
@Table(name = UserMedicalRecordCopyAppointmentOperation.TABLE_NAME, indexes = {
        @Index(name = "IDX_copy_appointment_operation_step", columnList = "step")
})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class UserMedicalRecordCopyAppointmentOperation extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_user_medical_record_copy_appointment_operation";

    @Comment("用户")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_OPERATION_USER_ID"))
    private User user;

    @Comment("复印预约")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "copy_appointment_id", nullable = false, foreignKey = @ForeignKey(name = "FK_COPY_APPOINTMENT_OPERATION_APPOINTMENT_ID"))
    private UserMedicalRecordCopyAppointment userMedicalRecordCopyAppointment;

    // 订单操作前的状态
    @Comment("订单操作前的状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "pre_status")
    @ColumnDefault("UNKNOWN")
    private UserMedicalRecordCopyAppointment.CopyAppointmentOrderStatus preStatus = UserMedicalRecordCopyAppointment.CopyAppointmentOrderStatus.UNKNOWN;

    // 订单操作后的状态
    @Comment("订单操作后的状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "next_status")
    private UserMedicalRecordCopyAppointment.CopyAppointmentOrderStatus nextStatus;

    // 当前步骤
    @Comment("当前步骤")
    @Enumerated(EnumType.STRING)
    @Column(name = "step")
    private Step step;

    @Comment("备注")
    @Column(name = "remarks")
    private String remarks;

    public enum Step {
        SUBMIT,//提交订单
        CANCEL_PAY,//取消支付
        TIMEOUT,//订单支付超时
        PAY,//支付
        APPLY_REFUND,//订单申请退款
        REFUND_PASS,//同意退款
        REFUND_REJECT,//拒绝退款
        REVIEW_PASS,//审核通过
        REVIEW_REJECT,//审核驳回
        COPY_PASS,//复印完成
        CALCULATE,//开始核算
        CALCULATE_APPEND_PAY,//核算补缴
        START_PICK_UP,//发起自提
        START_SEND,//发起邮寄
        APPEND_PAY,//订单补缴
        REFUND_SURPLUS,//订单退差额
        DELIVER,//订单发货
        RECEIPTED,//订单签收
        ;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public UserMedicalRecordCopyAppointment getUserMedicalRecordCopyAppointment() {
        return userMedicalRecordCopyAppointment;
    }

    public void setUserMedicalRecordCopyAppointment(UserMedicalRecordCopyAppointment userMedicalRecordCopyAppointment) {
        this.userMedicalRecordCopyAppointment = userMedicalRecordCopyAppointment;
    }

    public UserMedicalRecordCopyAppointment.CopyAppointmentOrderStatus getPreStatus() {
        return preStatus;
    }

    public void setPreStatus(UserMedicalRecordCopyAppointment.CopyAppointmentOrderStatus preStatus) {
        this.preStatus = preStatus;
    }

    public UserMedicalRecordCopyAppointment.CopyAppointmentOrderStatus getNextStatus() {
        return nextStatus;
    }

    public void setNextStatus(UserMedicalRecordCopyAppointment.CopyAppointmentOrderStatus nextStatus) {
        this.nextStatus = nextStatus;
    }

    public Step getStep() {
        return step;
    }

    public void setStep(Step step) {
        this.step = step;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
