package cn.taihealth.ih.domain.cloud;

import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.dict.StringListConvert;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.util.List;

/**
 */
@Entity
@Table(name = WechatInsuranceOrderRefund.TABLE_NAME,
indexes = {
    @Index(name = "IDX_WECHAT_INSURANCE_ORDERS_REFUNDS_REFUND_ID", columnList = "refund_id"),
    @Index(name = "IDX_WECHAT_INSURANCE_ORDERS_REFUNDS_OUT_REFUND_NO", columnList = "out_refund_no"),
    @Index(name = "IDX_WECHAT_INSURANCE_ORDERS_REFUNDS_OUT_TRADE_NO", columnList = "out_trade_no"),
    @Index(name = "IDX_WECHAT_INSURANCE_ORDERS_REFUNDS_PAY_ORD_ID", columnList = "pay_ord_id"),
    @Index(name = "IDX_WECHAT_INSURANCE_ORDERS_REFUNDS_SH_BILL_NO", columnList = "sh_bill_no")
}, uniqueConstraints = {
    @UniqueConstraint(name = "U_WECHAT_INSURANCE_ORDERS_REFUNDS_OUT_REFUND_NO", columnNames = {"out_refund_no"}),
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class WechatInsuranceOrderRefund extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_wechat_insurance_order_refunds";

    @Comment("微信支付退款号")
    @Column(name = "refund_id")
    private String refundId;

    @Comment("商户退款单号")
    @Column(name = "out_refund_no")
    private String outRefundNo;

    @Comment("微信支付订单号")
    @Column(name = "transaction_id")
    private String transactionId;

    @Comment("商户订单号")
    @Column(name = "out_trade_no")
    private String outTradeNo;

    @Comment("退款金额")
    @Column(name = "amount")
    private Integer amount;

    @Comment("自费的退款金额")
    @Column(name = "self_amount")
    private Integer selfAmount;

    @Comment("医保的退款金额")
    @Column(name = "insurance_amount")
    private Integer insuranceAmount;

    /**
     * 微信医保退款只有只退现金部分和全额退款
     */
    @Comment("退款方式,CASH_ONLY则只退现金部分,其他则是全额退款")
    @Column(name = "refund_type")
    private String refundType;

    @Comment("退款成功时间")
    @Column(name = "success_time")
    private String successTime;


    @Comment("退款单总状态,\n" +
            "SUCCESS:退款成功,\n" +
            "CLOSED:退款关闭,\n" +
            "REFUNDING:退款处理中,\n" +
            "FAIL:退款异常,\n" +
            "CHANGE::转入代发，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，资金回流到商户的现金帐号，需要商户人工干预，通过线下或者财付通转账的方式进行退款。")
    @Column(name = "status")
    private String status = "REFUNDING";

    @Comment("现金退款状态,\n" +
            "SUCCESS—退款成功\n" +
            "FAIL—退款失败\n" +
            "REFUNDING—退款处理中")
    @Column(name = "cash_refund_status")
    private String cashRefundStatus = "REFUNDING";

    @Comment("医保部分退款状态,\n" +
            "SUCCESS:退款成功,\n" +
            "REFUNDING:退款处理中,\n" +
            "CHANGE::转入代发，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，资金回流到商户的现金帐号，需要商户人工干预，通过线下或者财付通转账的方式进行退款。")
    @Column(name = "insurance_refund_status")
    private String insuranceRefundStatus = "REFUNDING";

    @Comment("退款失败后重试的次数")
    @Column(name = "refund_count")
    private Integer refundCount = 0;

    @Comment("部分退款业务明细流水号")
    @Column(name = "refund_detail_no", columnDefinition = "text")
    @Convert(converter = StringListConvert.class)
    private List<String> refundDetailNo;

    @Comment("最后一次退款失败的原因")
    @Column(name = "failed_reason")
    private String failedReason;

    @Comment("撤销流水号, 医保退款必填，医保局无要求则由医院自定义")
    @Column(name = "cancel_serial_no")
    private String cancelSerialNo;

    @Comment("撤销单据号, 医保诊间退款必填，医保局无要求则由医院自定义")
    @Column(name = "cancel_bill_no")
    private String cancelBillNo;

    @Comment("对应处方上传的出参单号")
    @Column(name = "pay_ord_id")
    private String payOrdId;

    @Comment("上海医保se02中的订单号")
    @Column(name = "sh_bill_no")
    private String shBillNo;

    @Comment("退款原因")
    @Column(name = "ref_reason")
    private String refReason;

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(String successTime) {
        this.successTime = successTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<String> getRefundDetailNo() {
        return refundDetailNo;
    }

    public void setRefundDetailNo(List<String> refundDetailNo) {
        this.refundDetailNo = refundDetailNo;
    }

    public Integer getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(Integer refundCount) {
        this.refundCount = refundCount;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    public String getCancelSerialNo() {
        return cancelSerialNo;
    }

    public void setCancelSerialNo(String cancelSerialNo) {
        this.cancelSerialNo = cancelSerialNo;
    }

    public String getCancelBillNo() {
        return cancelBillNo;
    }

    public void setCancelBillNo(String cancelBillNo) {
        this.cancelBillNo = cancelBillNo;
    }

    public String getPayOrdId() {
        return payOrdId;
    }

    public void setPayOrdId(String payOrdId) {
        this.payOrdId = payOrdId;
    }

    public String getRefReason() {
        return refReason;
    }

    public void setRefReason(String refReason) {
        this.refReason = refReason;
    }

    public String getCashRefundStatus() {
        return cashRefundStatus;
    }

    public void setCashRefundStatus(String cashRefundStatus) {
        this.cashRefundStatus = cashRefundStatus;
    }

    public String getInsuranceRefundStatus() {
        return insuranceRefundStatus;
    }

    public void setInsuranceRefundStatus(String insuranceRefundStatus) {
        this.insuranceRefundStatus = insuranceRefundStatus;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public Integer getSelfAmount() {
        return selfAmount;
    }

    public void setSelfAmount(Integer selfAmount) {
        this.selfAmount = selfAmount;
    }

    public Integer getInsuranceAmount() {
        return insuranceAmount;
    }

    public void setInsuranceAmount(Integer insuranceAmount) {
        this.insuranceAmount = insuranceAmount;
    }

    public String getShBillNo() {
        return shBillNo;
    }

    public void setShBillNo(String shBillNo) {
        this.shBillNo = shBillNo;
    }
}
