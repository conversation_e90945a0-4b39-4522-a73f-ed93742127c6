package cn.taihealth.ih.domain.enums;

/**
 * 建议类型
 */
@Deprecated
public enum SuggestTypeEnum {
    ORG_QUALIFICATION("机构资质问题", "1"),//机构资质问题
    PRACTITIONERS_QUALIFICATION("执业人员资质问题", "2"),//执业人员资质问题
    REGISTRATION("挂号问题", "3"),//挂号问题
    CONSULTATION("就诊问题", "4"),//就诊问题
    PRESCRIPTION("处方开具问题", "5"),//处方开具问题
    DRUG_SUPPLY("药品供应问题", "6"),//药品供应问题
    DRUG_DELIVERY("药品配送问题", "7"),//药品配送问题
    PAYMENT("缴费问题", "8"),//缴费问题
    QUALITY("药品/器械质量问题", "9"),//药品/器械质量问题
    PRE_CONSULTATION("输入病情症状，诊前咨询", "10"), // 输入病情症状，诊前咨询
    OTHER_CONSULTATION("其他问题咨询，请描述咨询的问题", "11"), // 其他问题咨询，请描述咨询的问题
    OTHER("其他", "0");//其他

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

    private final String value;
    private final String code;

    SuggestTypeEnum(String value, String code){
        this.value = value;
        this.code = code;
    }

    public static SuggestTypeEnum getEnumByName(String name) {
        for (SuggestTypeEnum an : SuggestTypeEnum.values()) {
            if (name.equals(an.name())) {
                return an;
            }
        }
        return null;
    }
}
