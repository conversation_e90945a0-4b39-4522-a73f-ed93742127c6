package cn.taihealth.ih.domain.dict;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.enums.TimePeriod;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import com.google.common.collect.Lists;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.*;

@Entity
@Table(name = ExamItem.TABLE_NAME)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ExamItem extends UpdatableEntity {

    public static final String TABLE_NAME = "ih_exam_items";

    @Comment("医院")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hospital_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_EXAM_ITEMS_HOSPITAL_ID"))
    private Hospital hospital;

    /**
     * 编码
     */
    @Comment("检查项目编码")
    private String code;

    /**
     * 检查项目名称
     */
    @Comment("检查项目名称")
    private String name;

    /**
     * 检查项目名称拼音
     */
    @Comment("检查项目名称拼音")
    @Column(name = "input_code")
    private String inputCode;

    /**
     * 检查项目报告出具时限
     */
    @Comment("检查项目报告出具时限")
    @Column(name = "time_limit")
    private String timeLimit;

    /**
     * 检查项目类别
     */
    @Comment("检查项目类别")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", nullable = false,
        foreignKey = @ForeignKey(name = "FK_EXAM_ITEMS_CATEGORY_ID"))
    private ExamCategory category;

    /**
     * 检查项目科室
     */
    @Comment("检查项目科室")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "offline_dept_id", foreignKey = @ForeignKey(name = "FK_EXAM_ITEMS_OFFLINE_DEPT_ID"))
    private OfflineDept offlineDept;

    /**
     * 检查项目价格(单位: 分)
     */
    @Comment("检查项目价格(单位: 分)")
    private int price;

    /**
     * 是否医保
     */
    @Comment("是否医保")
    @Column(name = "is_insurance")
    private boolean insurance;

    /**
     * 推荐检查时段
     */
    @Comment("推荐检查时段")
    @Column(name = "time_period")
    @Enumerated(EnumType.STRING)
    @ColumnDefault("UNKNOWN")
    private TimePeriod timePeriod = TimePeriod.UNKNOWN;

    /**
     * 推荐检查时间[["08:00","09:00"],["16:00","17:00"]]
     */
    @Comment("推荐检查时间")
    @Column(name = "times_str")
    @Convert(converter = ExamItemTimesConvert.class)
    private List<List<String>> times = Lists.newArrayList();

    /**
     * 注意事项
     */
    @Comment("注意事项")
    @Column(name = "notice", length = 1000)
    @Type(type = "org.hibernate.type.TextType")
    private String notice;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getInputCode() {
        return inputCode;
    }

    public void setInputCode(String inputCode) {
        this.inputCode = inputCode;
    }

    public ExamCategory getCategory() {
        return category;
    }

    public void setCategory(ExamCategory category) {
        this.category = category;
    }

    public OfflineDept getOfflineDept() {
        return offlineDept;
    }

    public void setOfflineDept(OfflineDept offlineDept) {
        this.offlineDept = offlineDept;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public boolean isInsurance() {
        return insurance;
    }

    public void setInsurance(boolean insurance) {
        this.insurance = insurance;
    }

    public String getNotice() {
        return notice;
    }

    public void setNotice(String notice) {
        this.notice = notice;
    }

    public Hospital getHospital() {
        return hospital;
    }

    public void setHospital(Hospital hospital) {
        this.hospital = hospital;
    }

    public TimePeriod getTimePeriod() {
        return timePeriod;
    }

    public void setTimePeriod(TimePeriod timePeriod) {
        this.timePeriod = timePeriod;
    }

    public List<List<String>> getTimes() {
        return times;
    }

    public void setTimes(List<List<String>> times) {
        this.times = times;
    }

    public String getTimeLimit() {
        return timeLimit;
    }

    public void setTimeLimit(String timeLimit) {
        this.timeLimit = timeLimit;
    }
}
