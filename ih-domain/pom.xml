<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ih-parent</artifactId>
        <groupId>cn.taihealth.ih</groupId>
        <version>2.0.4873</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ih-domain</artifactId>

    <properties>
        <maven.hibernate.version>5.3.1.Final</maven.hibernate.version>
        <maven.hibernate-validator.version>5.3.1.Final</maven.hibernate-validator.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-spatial</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-jcache</artifactId>
            <version>${hibernate.version}</version>
        </dependency>
        -->

        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-hibernate5</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gitq.jedi</groupId>
            <artifactId>jedi-data</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.github.lonnyj</groupId>-->
<!--            <artifactId>liquibase-spatial</artifactId>-->
<!--            <version>1.2.1</version>-->
<!--        </dependency>-->


        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-realname</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dm</groupId>
            <artifactId>DmDialect-for-hibernate</artifactId>
            <version>5.4</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>lib</id>
            <url>file://${project.basedir}${file.separator}..${file.separator}lib</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-maven-plugin</artifactId>
                <version>4.22.0</version>
                <configuration>
                    <diffExcludeObjects>table:excludes,table:QRTZ_.*</diffExcludeObjects>
                    <propertyFile>src/main/resources/config/liquibase/mysql/liquibase-mysql.properties</propertyFile>
                    <changeLogFile>src/main/resources/config/liquibase/master1.xml</changeLogFile>
                    <diffChangeLogFile>src/main/resources/config/liquibase/mysql/changelog1/diff_${maven.build.timestamp}_changelog.xml</diffChangeLogFile>
                    <outputChangeLogFile>src/main/resources/config/liquibase/output.xml</outputChangeLogFile>
                    <verbose>true</verbose>
                    <logging>debug</logging>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.liquibase.ext</groupId>
                        <artifactId>liquibase-hibernate5</artifactId>
                        <version>${liquibase-hibernate5.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-core</artifactId>
                        <version>${hibernate.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.hibernate.validator</groupId>
                        <artifactId>hibernate-validator</artifactId>
                        <version>${hibernate-validator.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-data-jpa</artifactId>
                        <version>2.7.18</version>
                    </dependency>
                    <dependency>
                        <groupId>com.mysql</groupId>
                        <artifactId>mysql-connector-j</artifactId>
                        <version>${mysql.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>cn.taihealth.ih</groupId>
                        <artifactId>ih-domain</artifactId>
                        <version>${project.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
