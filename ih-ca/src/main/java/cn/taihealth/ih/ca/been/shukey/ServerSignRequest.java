package cn.taihealth.ih.ca.been.shukey;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ServerSignRequest extends UKeyRequest {

    private Seal seal; //	JSONObject	签章信息

    @Getter
    @Setter
    public static class Seal {

        @JsonProperty("document_no")
        private String documentNo; //	String	文档唯一编号
        private String picture; //	String	印章图片，Base64 或 16 进制编码格式备注：当策略规则允许接口传入时，该参数有效
        // UKEY签章接口的seal_strategy_id填ukey
        // server签章接口的seal_strategy_id填server
        @JsonProperty("seal_strategy_id")
        private String sealStrategyId; //	String	签章策略标识，由签章系统提供
        private String pdf; //	String	Base64 编码后的 PDF 文件数据
        @JsonProperty("pdf_path")
        private String pdfPath; //	String	Pdf 在磁盘中的路径
        @JsonProperty("pdf_url")
        private String pdfUrl; //	String	Pdf 文件下载地址
        private String encodingformat = "BASE64"; //	String	"数据编码格式，返回的数据格式和此对应。picture/pdf 的编码格式 默认为 BASE64 编码 BASE64：BASE64 编码格式 HEX：16 进制编码格式"	否
        private String type; //	String	"签章位置说明，支持后台配置规则定位、位置定位和关键字两种方式，只能选择一种，如果选择则对应的配置需要必填 position：基于位置盖章 keyword：基于关键字盖章"	是

        private String degree; //	String	pdf 旋转角度，格式为 0，90，180，270，360	否
        private Boolean perforation; //	Boolean	是否骑缝盖章 true：是 false：否	否
        private String alpha; //	String	图章透明设置，1-10，透明度依次递减	否
        private Boolean perpagevisible; //	Boolean	是否多页签章。（图章在 pdf 每一页的同一位置）true：是 false：否 备注：不能和 perforation 同时为 true	否
        private String reason; //	String	签名原因,PDF 指定签名原因信息	否
        private String location; //	String	签名位置，PDF 指定签名位置信息	否
        private boolean fullCompression; //	boolean	"签章时是否开启压缩模式，默认 false；1.调用签章接口时，若pdf已被其他服务签章，则最好使用压缩模式（通用模式）fullCompression 设置为 true，否则可能出现已有签章签名值丢失问题；2.若全部由本服务签章则最好使用非压缩模式，fullCompression 设置为 false 可以避免部分 word 转 pdf 文件，签章后无法在浏览器显示印章问题 3. 部分 pdf 使用非压缩模式签章后，pdf 异常，无法使用 adobe 阅读器打开，此时需要将 fullCompression 设置为 true，并重新签章"	否
        private Boolean unethicalReading; //	Boolean	是否为加密的 PDF，默认 false	否
        @JsonProperty("automatic_addition")
        private Boolean automaticAddition; //	Boolean	是否自动显示印章图片	否

        private PK position; //	JSONObject	签章位置信息，根据 type 类型区分；与‘type’ 字段与之匹配，必填项	是
        private PK keyword; //	JSONObject	签章位置信息，根据 type 类型区分；与‘type’ 字段与之匹配，必填项	是
        private Size size; //	JSONObject	PDF 中印章显示的大小	否
        private CaUser user; //	JSONObject	用户信息	否 备注：当策略规则允许接口传入时，该参数有效
    }

    @Getter
    @Setter
    public static class PK {

        private String page; //	String	页码	否
        private Float x; //	Float	对应文档 x 坐标，（左下角为原点，最大50000）,keyword 中表示横向偏移量	否
        private Float y; //	Float	对应文档 y 坐标，（左下角为原点，最大50000），Keyword 中表示纵向偏移量	否
        private String content; //	String	关键字位置文字，使用 UrlEncode(UTF-8)编码传输	否
    }

    @Getter
    @Setter
    public static class Size {

        private Integer width; //	Int	印章图片显示宽度，单位毫米（mm）	否
        private Integer height; //	Int	印章图片显示高度，单位毫米（mm）	否

    }

    @Getter
    @Setter
    public static class CaUser {

        private String name; //	String	URLEncode 后的用户名称	否
        private String idNo; //	String	用户证件号	否
        private String extend1; //	String	事件证书扩展项	否
    }

}
