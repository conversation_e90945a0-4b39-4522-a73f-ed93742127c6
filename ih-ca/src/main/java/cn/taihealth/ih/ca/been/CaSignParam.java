package cn.taihealth.ih.ca.been;

import lombok.Getter;
import lombok.Setter;

import java.io.File;

@Getter
@Setter
public class CaSignParam {

    /**
     * 信安签章用户名
     */
    private String accountId;

    private String host;
    /**
     * 签章成功回调地址
     */
    private String redirectUrl;
    /**
     * 平台分配的 API Key
     */
    private String apiKey;
    /**
     * 平台分配的 API Secret，当 api_sign 不存在时必填
     */
    private String apiSecret;
    /**
     * 应用鉴权，由应用私钥对应用 api_key && document_no && seal_strategy_id 的 Base64 编码签名值进行 Url encode 的数据。参考示例代码参数签名示例（JAVA 版）
     */
    private String apiSign;
    /**
     * 文档唯一编号
     */
    private String documentNo;
    /**
     * 印章图片
     */
    private File picture;
    /**
     * 印章图片，Base64
     */
    private String pictureBase64;
    /**
     * 未签章的pdf文件
     */
    private File pdf;
    /**
     * 未签章的pdf文件，Base64
     */
    private String pdfBase64;
    /**
     * 签章策略标识，由签章系统提供
     */
    private String sealStrategyId;
}
