package cn.taihealth.ih.ca.client;

import cn.taihealth.ih.ca.been.shukey.*;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

@Slf4j
public class ShUKeyClient {

    /**
     * 上海cawebservice调用
     * @param method 方法名
     * @param url 接口地址
     * @param request 请求对象
     * @param clazz 泛型类型对应的Class对象
     * @param <T> 泛型类型，必须继承自ShCaResponse
     * @return 泛型类型的响应对象
     */
    public static <T extends ShCaResponse> T webservicePost(String method, String url, ShCaRequest request, Class<T> clazz) {
        JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
        // 这个方法比较耗时,目前不管
        try (Client client = dcf.createClient(url)) {
            String requestXml = request.toXML();
            log.info("上海CA接口入参 " + method + " " + requestXml);
            Object[] response = client.invoke(method, requestXml);
            String responseXml = response[0].toString();
            log.info("上海CA接口出参 " + method + " " + responseXml);
            if (clazz == null) {
                ShCaResponse t = new ShCaResponse();
                t.loadFromXml(requestXml);
                return null;
            } else {
                T t = clazz.getDeclaredConstructor().newInstance();
                t.loadFromXml(responseXml);
                return t;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * ukey签章
     * @param url
     * @param request
     * @return
     */
    public static <T extends UKeyResponse> T ukeyPost(String url, UKeyRequest request, TypeReference<T> type) {
        try {
            String requestStr = StandardObjectMapper.stringify(request);
            log.info("调用ca接口，入参: " + url + " " + requestStr);
            Response response = OkHttpUtils.post(url, requestStr );
            String body = OkHttpUtils.getResponseBody(response).orElse("");
            log.info("调用ca接口，出参: " + body);
            return StandardObjectMapper.readValue(body, type);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     *	使用私钥进行签名
     *	@param privateKey 私钥
     *	@param data 待签名数据
     *	@return	BASE64 编码格式数据
     */
    private static String signature(String privateKey, String data) {
        String result = null;
        try {
            byte[] bData = data.getBytes(StandardCharsets.UTF_8);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey priKey = keyFactory.generatePrivate(keySpec);
            Signature oSig = Signature.getInstance("SHA256WithRSA");
            oSig.initSign(priKey);
            oSig.update(bData);
            byte[] signature = oSig.sign();
            if (signature != null && signature.length > 0) {
                result = new String(Base64.getEncoder().encode(signature));
            }
        } catch (Exception e) {
            throw new RuntimeException("签名失败", e);
        }
        return result;
    }

    public static void main(String[] args) {
//        ShCaTimestampRequest request = new ShCaTimestampRequest();
//        request.setHashAlg("3");
//        request.setHashData("4");
//        ShCaRequest<ShCaTimestampRequest> request0 = new ShCaRequest<>();
//        request0.setAppCode("ywxt");
//        request0.setAppPWD("12345678");
//        request0.setRequest(request);
//        ShCaTimestampResponse response = webservicePost("generateTimestampByHashData",
//                "http://**************:7845/services/TimeStampServices?wsdl", request0, ShCaTimestampResponse.class);

        VerifySignRequest request1 = new VerifySignRequest();
        request1.setCert("MIIDWzCCAwGgAwIBAgIQcRuZhtXwlzYAvpvW1ijIZjAKBggqgRzPVQGDdTA0MQswCQYDVQQGEwJDTjERMA8GA1UECgwIVW5pVHJ1c3QxEjAQBgNVBAMMCVNIRUNBIFNNMjAeFw0yNTA1MjcwMjE1NTlaFw0yNjA1MjcxNTU5NTlaMGQxCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAnkuIrmtbfluIIxLTArBgNVBAoMJOS4iua1t+W4guaZrumZgOWMuueyvuelnuWNq+eUn+S4reW/gzESMBAGA1UEAwwJ5byg5bO75byTMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE9Id1iPSRg7USPmKEQhuciOH5FbXoMwHGY++fAiLjHH0bVyqDlmnE2sTWFab0hdrreZrnBDs8/8fFhQlrRcLp2aOCAcMwggG/MCIGA1UdIwEB/wQYMBaAFIkxBJF7Q6qqmr+EHZuG7vC4cJmgMCAGA1UdDgEB/wQWBBTrMyPRikDkVfC/dP9RmCLbKC0IlzAOBgNVHQ8BAf8EBAMCB4AwggEUBgNVHR8EggELMIIBBzCBtaCBsqCBr4aBrGxkYXA6Ly9sZGFwMi5zaGVjYS5jb206Mzg5L2NuPTkzOGVhNDhmODAzMWFiMWMuY3JsLG91PWU4Yzg0OWJmLG91PTAzYWMsb3U9NTZiZixvdT1kNDEwNGI0ZCxvdT1jcmwsbz1VbmlUcnVzdD9jZXJ0aWZpY2F0ZVJldm9jYXRpb25MaXN0P2Jhc2U/b2JqZWN0Q2xhc3M9Y1JMRGlzdHJpYnV0aW9uUG9pbnQwTaBLoEmGR2h0dHA6Ly9sZGFwMi5zaGVjYS5jb20vZDQxMDRiNGQvNTZiZi8wM2FjL2U4Yzg0OWJmLzkzOGVhNDhmODAzMWFiMWMuY3JsMCkGCCqBHIbvOoEUBB0xMDNANTAwOFNGMDUyMDEwMzE5ODgwOTA3MjgxMTAkBgUqVhUBAQQbM0A1MDA4U0YwNTIwMTAzMTk4ODA5MDcyODExMAoGCCqBHM9VAYN1A0gAMEUCIQDKh1Nl4ywcHSacqHNbl/ZOp7EzcnMJgOhM/y9YmHz70QIgVg4YxMqR13H/MXsFYlxToyxAwr1XKgDv8CGYBKlj/G4=");
        request1.setInData("ilRU0OPUunFbunOg1dJmcw==");
        request1.setSignData("MEYCIQCc4LIXWTguxmdclx1VZMhvFFdXDnDq//3bILODdoSX8AIhAPys8bXZqEP/2rZUG8R8AYNNgAV3CMSyMWYdRa93Kt+/");
        ShCaRequest<VerifySignRequest> request2 = new ShCaRequest<>();
        request2.setAppCode("ywxt");
        request2.setAppPWD("12345678");
        request2.setRequest(request1);
        ShCaResponse response1 = ShUKeyClient.webservicePost("verifySignData",
                "http://**************:7845/services/CipherServices?wsdl", request2, ShCaResponse.class);
//        System.out.println(StandardObjectMapper.stringify(response));
        System.out.println(StandardObjectMapper.stringify(response1));
//        UKeySignRequest request = new UKeySignRequest();
//        UKeySignRequest.Seal seal = new UKeySignRequest.Seal();
//        seal.setKeyword(new UKeySignRequest.PK());
//        seal.setPosition(new UKeySignRequest.PK());
//
//        request.setSeal(seal);
//        System.out.println(StandardObjectMapper.stringify(request));

    }
}
