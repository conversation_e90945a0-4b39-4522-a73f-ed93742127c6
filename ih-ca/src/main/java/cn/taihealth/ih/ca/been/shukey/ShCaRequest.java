package cn.taihealth.ih.ca.been.shukey;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.io.StringWriter;

@Getter
@Setter
@XmlRootElement(name = "Root")
@XmlAccessorType(XmlAccessType.FIELD)
public class ShCaRequest<T> implements Serializable {

    @XmlElement(name = "AppCode")
    private String appCode; //	应用系统标识	VARCHAR(32)	应用系统标识	是
    @XmlElement(name = "AppPWD")
    private String appPWD; //	应用系统密码	VARCHAR(32)	应用系统密码	是

    @XmlElement(name = "Request")
    private T request;

    public String toXML() {
        try {
            JAXBContext context = JAXBContext.newInstance(this.getClass(), VerifySignRequest.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.FALSE);
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
            StringWriter sw = new StringWriter();
            marshaller.marshal(this, sw);
            return sw.toString();
        } catch (Exception e) {
            throw new RuntimeException("xml格式转换失败 " + this.getClass(), e);
        }
    }
}
