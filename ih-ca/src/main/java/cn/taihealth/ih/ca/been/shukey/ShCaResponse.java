package cn.taihealth.ih.ca.been.shukey;

import com.gitq.jedi.common.xml.XMLHelper;
import lombok.Getter;
import lombok.Setter;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.Serializable;

@Getter
@Setter
public class ShCaResponse implements Serializable {

    /**
     * 1成功，其他失败
     */
    private String retCode;
    /**
     * 返回说明
     */
    private String retMsg;

    public void loadFromXml(String xml) {
        try {
            Element mode = DocumentHelper.parseText(xml).getRootElement();
            this.retCode = XMLHelper.getString(mode, "RetCode");
            this.retMsg = XMLHelper.getString(mode, "RetMsg");
        } catch (Exception e) {
            throw new RuntimeException("xml解析失败", e);
        }
    }

}
