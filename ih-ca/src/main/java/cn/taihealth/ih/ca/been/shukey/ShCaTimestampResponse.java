package cn.taihealth.ih.ca.been.shukey;

import com.gitq.jedi.common.xml.XMLHelper;
import lombok.Getter;
import lombok.Setter;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@XmlRootElement(name = "Root")
@XmlAccessorType(XmlAccessType.FIELD)
public class ShCaTimestampResponse extends ShCaResponse {

    @XmlElement(name = "TimeStamp")
    private String timeStamp;

    public void loadFromXml(String xml) {
        try {
            Element mode = DocumentHelper.parseText(xml).getRootElement();
            this.setRetCode(XMLHelper.getString(mode, "RetCode"));
            this.setRetMsg(XMLHelper.getString(mode, "RetMsg"));
            this.timeStamp = XMLHelper.getString(mode, "TimeStamp");
        } catch (Exception e) {
            throw new RuntimeException("xml解析失败", e);
        }
    }

}
