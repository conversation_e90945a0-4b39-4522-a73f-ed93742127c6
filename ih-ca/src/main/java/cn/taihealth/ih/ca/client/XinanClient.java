package cn.taihealth.ih.ca.client;

import cn.taihealth.ih.ca.been.Qrcode;
import cn.taihealth.ih.ca.been.xinan.QrcodeLoginResponse;
import cn.taihealth.ih.ca.been.xinan.UploadPdfResponse;
import cn.taihealth.ih.ca.been.xinan.XinanQrcode;
import cn.taihealth.ih.ca.been.xinan.XinanResponse;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.HttpUtil;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class XinanClient {

    /**
     * 生成登录二维码
     * @param url
     * @return
     */
    public static Qrcode getLoginQrcode(String url) {
        String random = UUID.randomUUID().toString().replaceAll("-", "");
        Map<String, String> params = new HashMap<>();
        params.put("business", "infosec");
        params.put("random", random);
        try {
            log.info("调用ca接口，入参: " + url + "?" + HttpUtil.toHttpParams(params));
            Response response = OkHttpUtils.get(url, params);
            String body = OkHttpUtils.getResponseBody(response).orElse("");
            log.info("调用ca接口，出参: " + body);
            XinanQrcode xinanQrcode = StandardObjectMapper.readValue(body, new TypeReference<>() {});
            if ("000000".equals(xinanQrcode.getResultcode())) {
                return new Qrcode(true, null, xinanQrcode.getData(), random);
            } else {
                throw ErrorType.CA_REQUEST_ERROR.toProblem(xinanQrcode.getResultcode() + ": " + xinanQrcode.getResultmsg());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 二维码登录是否成功
     * @param url
     * @return
     */
    public static QrcodeLoginResponse loginWithQrcode(String url, String appid, String random) {
        Map<String, String> params = new HashMap<>();
        params.put("business", "infosec");
//        params.put("appid", appid);
        params.put("random", random);
        try {
            log.info("调用ca接口，入参: " + url + "?" + HttpUtil.toHttpParams(params));
            Response response = OkHttpUtils.get(url, params);
            String body = OkHttpUtils.getResponseBody(response).orElse("");
            log.info("调用ca接口，出参: " + body);
            // {"data":"g==","resultmsg":"成功","resultcode":"000000","data16":"82"}
            QrcodeLoginResponse response1 = StandardObjectMapper.readValue(body, new TypeReference<>() {});
            if ("000000".equals(response1.getResultcode())) {
                return response1;
            } else {
                throw ErrorType.CA_REQUEST_ERROR.toProblem(response1.getResultcode() + ": " + response1.getResultmsg());
            }

        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    public static UploadPdfResponse uploadPdf(String url, File pdf, String username) {
        try {

            Map<String, String> params = new HashMap<>();
//            params.put("business", "infosec");
            params.put("username", username);

            log.info("调用ca接口，入参: " + url + "?" + HttpUtil.toHttpParams(params));
            Response response = OkHttpUtils.upload(url, pdf, "pdfFile", params);
            String body = OkHttpUtils.getResponseBody(response).orElse("");
            log.info("调用ca接口，出参: " + body);
            // {"data":"g==","resultmsg":"成功","resultcode":"000000","data16":"82"}
            UploadPdfResponse response1 = StandardObjectMapper.readValue(body, new TypeReference<>() {});
            if ("000000".equals(response1.getResultcode())) {
                return response1;
            } else {
                throw ErrorType.CA_REQUEST_ERROR.toProblem(response1.getResultcode() + ": " + response1.getResultmsg());
            }

        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 下载pdf
     * @param url
     * @param pdf
     */
    public static void downloadPdf(String url, File pdf) {
        try {
            log.info("调用ca接口，downloadPdf入参: " + url);
            OkHttpUtils.download(url, pdf);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    public static boolean checkSignature(String url, String random) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("random", random);

            log.info("调用ca接口，入参: " + url + "?" + HttpUtil.toHttpParams(params));
            Response response = OkHttpUtils.get(url, params);
            String body = OkHttpUtils.getResponseBody(response).orElse("");
            log.info("调用ca接口，出参: " + body);
            XinanResponse response1 = StandardObjectMapper.readValue(body, new TypeReference<>() {});
            return "000000".equals(response1.getResultcode());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

}
