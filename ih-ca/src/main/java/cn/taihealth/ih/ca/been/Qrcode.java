package cn.taihealth.ih.ca.been;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
public class Qrcode implements Serializable {

    private static final long serialVersionUID = 1L;

    private boolean caLogin = true;
    private String url;
    private String base64Image;
    private String random;

    public Qrcode(boolean caLogin) {
        this.caLogin = caLogin;
    }

    public Qrcode(boolean caLogin, String url, String base64Image, String random) {
        this.caLogin = caLogin;
        this.url = url;
        this.base64Image = base64Image;
        this.random = random;
    }

}
