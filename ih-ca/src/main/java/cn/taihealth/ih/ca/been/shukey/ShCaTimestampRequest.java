package cn.taihealth.ih.ca.been.shukey;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@XmlRootElement(name = "Root")
@XmlAccessorType(XmlAccessType.FIELD)
public class ShCaTimestampRequest {

    @XmlElement(name = "HashData")
    private String hashData; //	摘要值 Base64编码原文摘要值	是
    @XmlElement(name = "HashAlg")
    private String hashAlg; //	摘要算法	HashAlg	VARCHAR(32)	对原文的摘要算法SHA256S M3	是
    @XmlElement(name = "CertReq")
    private String certReq = "true"; //	响应是否包含时间戳证书	CertReq	VARCHAR(32)	响应是否包含时间戳服务器证书true/false如果不填默认为true	否

}
