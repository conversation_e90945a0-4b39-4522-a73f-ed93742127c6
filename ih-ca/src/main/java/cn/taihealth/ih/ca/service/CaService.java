package cn.taihealth.ih.ca.service;

import cn.taihealth.ih.ca.been.CaSignParam;
import cn.taihealth.ih.ca.been.CaVerifySignParam;
import cn.taihealth.ih.ca.been.Qrcode;
import cn.taihealth.ih.ca.been.QrcodeLogin;
import cn.taihealth.ih.ca.been.xinan.UploadPdfResponse;
import cn.taihealth.ih.ca.enums.SignatureType;

import java.io.File;

public interface CaService {

    Qrcode getQrcode(String url);

    QrcodeLogin loginWithQrcode(String url, String appid, String random);

    /**
     * CA处方签名
     * @param localCAFile
     * @param drugSize
     * @param signatureType
     */
    String prescriptionSignature(String localCAFile, int drugSize, SignatureType signatureType,
                               CaSignParam signParam);

    /**
     * 添加处方签章时间
     * @param localCAFile
     * @param drugSize
     * @param signatureType
     * @param signParam
     */
    void prescriptionSignatureTime(String localCAFile, int drugSize, SignatureType signatureType,
                                     CaSignParam signParam);

    /**
     * CA诊断单签名
     * @param localCAFile
     * @param signatureType 只支持医生和医院
     */
    String diagnosisSignature(String localCAFile, SignatureType signatureType, CaSignParam signParam);

    /**
     * 上传pdf
     * @param host
     * @param pdf
     * @param username
     * @return
     */
    UploadPdfResponse uploadPdf(String host, File pdf, String username);

    void downloadPdf(File pdf, String url, CaSignParam signParam);

    /**
     * 校验签章是否成功
     * @param host
     * @param random
     * @return
     */
    boolean checkSignature(String host, String random);

    /**
     * 校验签名是否成功
     * @param host
     * @param caSignParam
     * @param inData 签名原文
     * @param signData 签名值
     * @return
     */
    boolean verifySignData(String host, CaVerifySignParam caSignParam, String inData, String signData);

    /**
     * 时间戳签发
     * @param host
     * @param caSignParam
     * @param inData
     * @return
     */
    String generateTimestamp(String host, CaVerifySignParam caSignParam, String inData);
}
