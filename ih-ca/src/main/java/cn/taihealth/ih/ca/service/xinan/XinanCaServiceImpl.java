package cn.taihealth.ih.ca.service.xinan;

import cn.taihealth.ih.ca.been.CaSignParam;
import cn.taihealth.ih.ca.been.CaVerifySignParam;
import cn.taihealth.ih.ca.been.Qrcode;
import cn.taihealth.ih.ca.been.QrcodeLogin;
import cn.taihealth.ih.ca.been.xinan.QrcodeLoginResponse;
import cn.taihealth.ih.ca.been.xinan.UploadPdfResponse;
import cn.taihealth.ih.ca.client.XinanClient;
import cn.taihealth.ih.ca.enums.SignatureType;
import cn.taihealth.ih.ca.service.CaService;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.PDFUtil;
import com.gitq.jedi.common.web.UrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;

@Slf4j
@Service("xinanCaService")
public class XinanCaServiceImpl implements CaService {

    private String concatSegments(String host, String url) {
        if (host.endsWith("MAuthServer") || host.endsWith("MAuthServer/")) {
            if (url.startsWith("MAuthServer") || url.startsWith("/MAuthServer")) {
                return UrlUtils.concatSegments(host, url).replace("MAuthServer/MAuthServer", "MAuthServer");
            } else {
                return UrlUtils.concatSegments(host, url);
            }
        } else {
            if (url.startsWith("MAuthServer") || url.startsWith("/MAuthServer")) {
                return UrlUtils.concatSegments(host, url);
            } else {
                return UrlUtils.concatSegments(host, "MAuthServer", url);
            }
        }
    }

    @Override
    public Qrcode getQrcode(String url) {
        return XinanClient.getLoginQrcode(concatSegments(url, "MAuthServer/qrcode/getQrcode.do"));
    }

    @Override
    public QrcodeLogin loginWithQrcode(String url, String appid, String random) {
        try {
            QrcodeLoginResponse response = XinanClient.loginWithQrcode(concatSegments(url, "MAuthServer/qrcode/isLogin.do"), appid, random);
            // TODO: 2023-07-17 14:42:32二维码过期如何判断，目前信安世纪 二维码不存在/没扫过码/二维码过期 都返回 {"resultmsg": "二维码登录失败","resultcode": "RS0038"}
            return new QrcodeLogin(response.getUsername());
        } catch (Exception e) {
            return new QrcodeLogin(false);
        }
    }

    @Override
    public void prescriptionSignatureTime(String localCAFile, int drugSize, SignatureType signatureType, CaSignParam signParam) {
    }

    /**
     * 信安签名，使用本地直接在pdf上加图片的方式
     */
    @Override
    public String prescriptionSignature(String localCAFile, int drugSize, SignatureType signatureType,
                                        CaSignParam signParam) {

        // 签章位置信息
        float posX;
        float posY;
        switch (signatureType) {
            case DOCTOR:
                posX = 95F;
                break;
            case DOCTOR_REVIEW:
                posX = 300F;
                break;
            case HOSPITAL:
                posX = 420F;
                break;
            default:
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem();
        }
        switch (drugSize) {
            case 1:
                posY = 400F;
                break;
            case 2:
                posY = 355F;
                break;
            case 3:
                posY = 300F;
                break;
            case 4:
                posY = 255F;
                break;
            case 5:
                posY = 210F;
                break;
            default:
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("处方数量大于5");
        }
        if (signatureType == SignatureType.HOSPITAL) {
            posY = posY - 100;
        }
        if (posY < 40) {
            posY = 40;
        }
        try {
            PDFUtil.addSign(signParam.getPdf(), localCAFile, signParam.getPicture(), -1, posX, posY);
            return null;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("处方签名失败");
        }
    }



    @Override
    public String diagnosisSignature(String localCAFile, SignatureType signatureType, CaSignParam signParam) {
        // 签章位置信息
        float posX;
        float posY;
        switch (signatureType) {
            case DOCTOR:
                posX = 400F;
                break;
            case HOSPITAL:
                posX = 40F;
                break;
            default:
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem();
        }
        posY = 150F;
        try {
            PDFUtil.addSign(signParam.getPdf(), localCAFile, signParam.getPicture(), -1, posX, posY);
            return null;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("诊断单签名失败");
        }
    }

    @Override
    public UploadPdfResponse uploadPdf(String host, File pdf, String username) {
        return XinanClient.uploadPdf(concatSegments(host, "MAuthServer/pdf/uploadPdfFile"), pdf, username);
    }

    @Override
    public void downloadPdf(File pdf, String url, CaSignParam signParam) {
        XinanClient.downloadPdf(concatSegments(signParam.getHost(), url), pdf);
    }

    @Override
    public boolean checkSignature(String host, String random) {
        return XinanClient.checkSignature(concatSegments(host, "MAuthServer/stamper/sign/result/get.do"), random);
    }

    @Override
    public boolean verifySignData(String host, CaVerifySignParam caSignParam, String inData, String signData) {
        return false;
    }

    @Override
    public String generateTimestamp(String host, CaVerifySignParam caSignParam, String inData) {
        return "";
    }
}
