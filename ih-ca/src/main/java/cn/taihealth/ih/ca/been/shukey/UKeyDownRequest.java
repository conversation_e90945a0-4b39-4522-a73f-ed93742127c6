package cn.taihealth.ih.ca.been.shukey;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UKeyDownRequest extends UKeyRequest {

    private Seal seal; //	JSONObject	签章信息

    @Getter
    @Setter
    public static class Seal {

        @JsonProperty("document_no")
        private String documentNo; //	String	文档唯一编号
        private String encodingformat = "BASE64"; //	String	"数据编码格式，返回的数据格式和此对应。picture/pdf 的编码格式 默认为 BASE64 编码 BASE64：BASE64 编码格式 HEX：16 进制编码格式"	否
    }

}
