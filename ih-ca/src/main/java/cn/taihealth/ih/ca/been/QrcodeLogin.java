package cn.taihealth.ih.ca.been;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class QrcodeLogin {

    /**
     * 是否登录
     */
    private boolean isLogin;
    /**
     * ca唯一标识
     */
    private String caUid;
    /**
     * 二维码过期，需要刷新
     */
    private boolean needRefresh;

    public QrcodeLogin(boolean isLogin) {
        this.isLogin = isLogin;
    }

    public QrcodeLogin(String caUid) {
        this.isLogin = true;
        this.caUid = caUid;
    }

    public QrcodeLogin(boolean isLogin, boolean needRefresh) {
        this.isLogin = isLogin;
        this.needRefresh = needRefresh;
    }

}
