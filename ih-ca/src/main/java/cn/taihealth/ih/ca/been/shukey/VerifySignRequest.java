package cn.taihealth.ih.ca.been.shukey;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
public class VerifySignRequest {

    @XmlElement(name = "Cert")
    private String cert; //	证书	VARCHAR	Base64编码公钥证书	是
    @XmlElement(name = "SignAlg")
    private String signAlg = "SM3withSM2"; //	签名算法	VARCHAR(32)	签名算法：SHA256withRSA, SM3withSM2	是
    @XmlElement(name = "InData")
    private String inData; //	原文	VARCHAR	签名原文	是
    @XmlElement(name = "SignData")
    private String signData; //	签名值	VARCHAR	Base64编码签名值	是

}
