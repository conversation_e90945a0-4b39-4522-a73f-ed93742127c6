package cn.taihealth.ih.ca.service.shukey;

import cn.taihealth.ih.ca.been.CaSignParam;
import cn.taihealth.ih.ca.been.CaVerifySignParam;
import cn.taihealth.ih.ca.been.Qrcode;
import cn.taihealth.ih.ca.been.QrcodeLogin;
import cn.taihealth.ih.ca.been.shukey.*;
import cn.taihealth.ih.ca.been.xinan.UploadPdfResponse;
import cn.taihealth.ih.ca.client.ShUKeyClient;
import cn.taihealth.ih.ca.enums.SignatureType;
import cn.taihealth.ih.ca.service.CaService;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.PDFUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.web.UrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.Base64;
import java.util.Objects;

@Slf4j
@Service("shCaService")
public class ShCaServiceImpl implements CaService {

    @Override
    public Qrcode getQrcode(String url) {
        return null;
    }

    @Override
    public QrcodeLogin loginWithQrcode(String url, String appid, String random) {
        return null;
    }

    @Override
    public String prescriptionSignature(String localCAFile, int drugSize, SignatureType signatureType,
                                      CaSignParam signParam) {
        if (signatureType == SignatureType.HOSPITAL) {
            return prescriptionHospitalSignature(drugSize, signParam);
        } else {
            // 签章位置信息
            float posX;
            float posY;
            switch (signatureType) {
                case DOCTOR:
                    posX = 95F;
                    break;
                case DOCTOR_REVIEW:
                    posX = 300F;
                    break;
                default:
                    throw ErrorType.INTERNAL_SERVER_ERROR.toProblem();
            }
            switch (drugSize) {
                case 1:
                    posY = 400F;
                    break;
                case 2:
                    posY = 355F;
                    break;
                case 3:
                    posY = 300F;
                    break;
                case 4:
                    posY = 255F;
                    break;
                case 5:
                    posY = 210F;
                    break;
                default:
                    throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("处方数量大于5");
            }

            UKeySignRequest signRequest = new UKeySignRequest();
            signRequest.setApiKey(signParam.getApiKey());
            signRequest.setApiSecret(signParam.getApiSecret());
//        signRequest.setApiSign(URLEncoder.encode(signParam.getApiSign(), StandardCharsets.UTF_8));
            signRequest.setIsMmultiSeal(false);
            UKeySignRequest.Seal seal = new UKeySignRequest.Seal();
            seal.setDocumentNo(signParam.getDocumentNo());
            seal.setPicture(signParam.getPictureBase64());
            seal.setSealStrategyId(signParam.getSealStrategyId());
            seal.setPdf(signParam.getPdfBase64());
            seal.setShowPage(true);
            seal.setDraggie(true);
            seal.setType("position");
            seal.setRedirectUrl(signParam.getRedirectUrl());
            seal.setReturnUrl(signParam.getRedirectUrl());
            seal.setFullCompression(false);

            UKeySignRequest.PK position = new UKeySignRequest.PK();
            position.setPage("1");
            position.setX(posX);
            position.setY(posY);
            seal.setPosition(position);
//        UKeySignRequest.Size size = new UKeySignRequest.Size();
//        size.setWidth(170);
//        size.setHeight(85);
//        seal.setSize(size);

            signRequest.setSeal(seal);
            UKeySignResponse signResponse = ShUKeyClient.ukeyPost(UrlUtils.concatSegments(signParam.getHost(), "/seal/ukeySign"),
                    signRequest, new TypeReference<>() {});
            if (!Objects.equals(0, signResponse.getRetCode())) {
                throw new RuntimeException("签章失败");
            }
            return signResponse.getSignUrl();
        }
    }

    private String prescriptionHospitalSignature(int drugSize, CaSignParam signParam) {
        // 签章位置信息
        float posX = 42000F;
        float posY = 6000F;
//        switch (drugSize) {
//            case 1:
//                posY = 22700F;
//                break;
//            case 2:
//                posY = 18600F;
//                break;
//            case 3:
//                posY = 14500F;
//                break;
//            case 4:
//                posY = 10400F;
//                break;
//            case 5:
//                posY = 6300F;
//                break;
//            default:
//                posY = 6000F;
//        }

        ServerSignRequest signRequest = new ServerSignRequest();
        signRequest.setApiKey(signParam.getApiKey());
        signRequest.setApiSecret(signParam.getApiSecret());
        ServerSignRequest.Seal seal = new ServerSignRequest.Seal();
        seal.setDocumentNo(signParam.getDocumentNo());
        if (StringUtils.isNotBlank(signParam.getPictureBase64())) {
            seal.setPicture(signParam.getPictureBase64());
        } else {
            try (InputStream in = new FileInputStream(signParam.getPicture())) {
                seal.setPicture(Base64.getEncoder().encodeToString(in.readAllBytes()));
            } catch (Exception e) {
                log.error("图片文件读取失败, ", e);
                throw new RuntimeException("图片文件读取失败");
            }
        }
        seal.setSealStrategyId(signParam.getSealStrategyId());
        seal.setPdf(signParam.getPdfBase64());
        seal.setType("position");
        seal.setFullCompression(false);

        ServerSignRequest.PK position = new ServerSignRequest.PK();
        position.setPage("1");
        position.setX(posX);
        position.setY(posY);
        seal.setPosition(position);
        ServerSignRequest.Size size = new ServerSignRequest.Size();
        size.setWidth(120);
        size.setHeight(120);
        seal.setSize(size);

        signRequest.setSeal(seal);
        ServerSignResponse signResponse = ShUKeyClient.ukeyPost(UrlUtils.concatSegments(signParam.getHost(), "/seal/serverSign"),
                signRequest, new TypeReference<>() {});
        if (!Objects.equals(0, signResponse.getRetCode())) {
            throw new RuntimeException("医院章签章失败");
        }
        return signResponse.getPdf();
    }

    @Override
    public void prescriptionSignatureTime(String localCAFile, int drugSize, SignatureType signatureType,
                                            CaSignParam signParam) {

        // 签章位置信息
        float posX;
        float posY;
        switch (signatureType) {
            case DOCTOR:
                posX = 95F;
                break;
            case DOCTOR_REVIEW:
                posX = 300F;
                break;
            case HOSPITAL:
                posX = 420F;
                break;
            default:
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem();
        }
        switch (drugSize) {
            case 1:
                posY = 400F;
                break;
            case 2:
                posY = 355F;
                break;
            case 3:
                posY = 300F;
                break;
            case 4:
                posY = 255F;
                break;
            case 5:
                posY = 210F;
                break;
            default:
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("处方数量大于5");
        }
        if (signatureType == cn.taihealth.ih.ca.enums.SignatureType.HOSPITAL) {
            posY = posY - 100;
        }
        if (posY < 40) {
            posY = 40;
        }
        try {
            PDFUtil.addSignTime(signParam.getPdf(), localCAFile, -1, posX, posY);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("处方签名失败");
        }
    }


    @Override
    public String diagnosisSignature(String localCAFile, SignatureType signatureType, CaSignParam signParam) {
        // 签章位置信息
        float posX;
        float posY;
        switch (signatureType) {
            case DOCTOR:
                posX = 400F;
                break;
            case HOSPITAL:
                posX = 40F;
                break;
            default:
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem();
        }
        posY = 150F;

        UKeySignRequest signRequest = new UKeySignRequest();
        signRequest.setApiKey(signParam.getApiKey());
        signRequest.setApiSecret(signParam.getApiSecret());
//        signRequest.setApiSign(URLEncoder.encode(signParam.getApiSign(), StandardCharsets.UTF_8));
        signRequest.setIsMmultiSeal(false);
        UKeySignRequest.Seal seal = new UKeySignRequest.Seal();
        seal.setDocumentNo(signParam.getDocumentNo());
        seal.setPicture(signParam.getPictureBase64());
        seal.setSealStrategyId(signParam.getSealStrategyId());
        seal.setPdf(signParam.getPdfBase64());
        seal.setShowPage(true);
        seal.setDraggie(true);
        seal.setType("position");
        seal.setRedirectUrl(signParam.getRedirectUrl());
        seal.setReturnUrl(signParam.getRedirectUrl());
        seal.setFullCompression(false);

        UKeySignRequest.PK position = new UKeySignRequest.PK();
        position.setPage("1");
        position.setX(posX);
        position.setY(posY);
        seal.setPosition(position);
//        UKeySignRequest.Size size = new UKeySignRequest.Size();
//        size.setWidth(170);
//        size.setHeight(85);
//        seal.setSize(size);

        signRequest.setSeal(seal);
        UKeySignResponse signResponse = ShUKeyClient.ukeyPost(UrlUtils.concatSegments(signParam.getHost(), "/seal/ukeySign"),
                signRequest, new TypeReference<>() {});
        if (!Objects.equals(0, signResponse.getRetCode())) {
            throw new RuntimeException("签章失败");
        }
        return signResponse.getSignUrl();
    }

    @Override
    public UploadPdfResponse uploadPdf(String host, File pdf, String username) {
        return null;
    }

    @Override
    public void downloadPdf(File pdf, String url, CaSignParam signParam) {
        UKeyDownRequest downRequest = new UKeyDownRequest();
        downRequest.setApiKey(signParam.getApiKey());
        downRequest.setApiSecret(signParam.getApiSecret());
        UKeyDownRequest.Seal seal = new UKeyDownRequest.Seal();
        seal.setDocumentNo(signParam.getDocumentNo());
        downRequest.setSeal(seal);
        UKeyDownResponse downResponse = ShUKeyClient.ukeyPost(UrlUtils.concatSegments(signParam.getHost(), "/seal/down"),
                downRequest, new TypeReference<>() {});
        if (Objects.equals(0, downResponse.getRetCode())) {
            try (OutputStream out = new FileOutputStream(pdf)) {
                out.write(Base64.getDecoder().decode(downResponse.getPdf()));
            } catch (Exception e) {
                log.error("下载pdf失败, pdf写入失败", e);
                throw new RuntimeException("下载pdf失败, pdf写入失败 " + e.getMessage());
            }
        } else {
            log.error("下载pdf失败" + downResponse.getRetMsg());
            throw new RuntimeException("下载pdf失败");
        }

    }

    @Override
    public boolean checkSignature(String host, String random) {
        return false;
    }

    @Override
    public boolean verifySignData(String host, CaVerifySignParam caSignParam, String inData, String signData) {
        VerifySignRequest signRequest = new VerifySignRequest();
        signRequest.setCert(caSignParam.getCert());
        signRequest.setInData(inData);
        signRequest.setSignData(signData);
        ShCaRequest<VerifySignRequest> request = new ShCaRequest<>();
        request.setAppCode(caSignParam.getAppCode());
        request.setAppPWD(caSignParam.getAppPwd());
        request.setRequest(signRequest);
        ShCaResponse response = ShUKeyClient.webservicePost("verifySignData",
                UrlUtils.concatSegments(host, "/services/CipherServices?wsdl"), request, ShCaResponse.class);
        return Objects.equals("1", response.getRetCode());
    }

    @Override
    public String generateTimestamp(String host, CaVerifySignParam caSignParam, String hashData) {
        ShCaTimestampRequest timestampRequest = new ShCaTimestampRequest();
        timestampRequest.setHashAlg("M3");
        timestampRequest.setHashData(hashData);
        ShCaRequest<ShCaTimestampRequest> request = new ShCaRequest<>();
        request.setAppCode(caSignParam.getAppCode());
        request.setAppPWD(caSignParam.getAppPwd());
        request.setRequest(timestampRequest);
        ShCaTimestampResponse response = ShUKeyClient.webservicePost("generateTimestampByHashData",
                UrlUtils.concatSegments(host, "/services/TimeStampServices?wsdl"), request, ShCaTimestampResponse.class);
        return response.getTimeStamp();
    }

}
