package cn.taihealth.ih.ca.been.shukey;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class UKeyRequest implements Serializable {

    @JsonProperty("api_key")
    private String apiKey; //	String	平台分配的 API Key
    @JsonProperty("api_secret")
    private String apiSecret; //	String	平台分配的 API Secret，当 api_sign 不存在时必填

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("api_sign")
    private String apiSign; //	String	应用鉴权，由应用私钥对应用 api_key && document_no && seal_strategy_id 的 Base64 编码签名值进行 Url encode 的数据。参考示例代码参数签名示例（JAVA 版）

}
