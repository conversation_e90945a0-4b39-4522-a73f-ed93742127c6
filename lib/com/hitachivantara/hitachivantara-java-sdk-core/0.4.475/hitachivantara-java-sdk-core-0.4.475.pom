<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.hitachivantara</groupId>
    <artifactId>hitachivantara-java-sdk-core</artifactId>
    <version>0.4.475</version>
    <name>hitachivantara sdk</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 		<dependency> -->
        <!-- 			<groupId>junit</groupId> -->
        <!-- 			<artifactId>junit</artifactId> -->
        <!-- 			<version>4.12</version> -->
        <!-- 			<scope>test</scope> -->
        <!-- 		</dependency> -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.10</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <target>1.7</target>
                    <source>1.7</source>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>