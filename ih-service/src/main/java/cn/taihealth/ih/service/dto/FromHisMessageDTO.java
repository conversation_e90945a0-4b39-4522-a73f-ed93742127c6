package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.his.FromHisMessage;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class FromHisMessageDTO extends UpdatableDTO {

    @ApiModelProperty("身份证号")
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String certificateNo;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("患者唯一号")
    private String patientId;

    @ApiModelProperty("卡号")
    private String cardNo;

    @ApiModelProperty("医院代码")
    private String organCode;

    @ApiModelProperty("医院名称")
    private String organName;

    @ApiModelProperty("就诊流水号")
    private String regno;

    @ApiModelProperty("就诊来源 1门诊 2住院")
    private String userSource;

    @ApiModelProperty("消息类型")
    private String msgType;

    @ApiModelProperty("业务流水号")
    private String operationSn;

    @ApiModelProperty("消息详情")
    private String msgDetails;

    @ApiModelProperty("请求ID")
    private String requestId;

    @ApiModelProperty("时间戳")
    private String timestamp;

    @ApiModelProperty("请求路径")
    private String requestPath;

    @ApiModelProperty("ih返回的参数")
    private String returnMessage;

    @ApiModelProperty("是否成功")
    private Boolean success;

    public FromHisMessageDTO(FromHisMessage message, Patient patient) {
        super(message);
        this.certificateNo = message.getCertificateNo();
        this.patientName = message.getPatientName();
        this.patientId = message.getPatientId();
        this.cardNo = patient.getIdCardNum();
        this.organCode = message.getOrganCode();
        this.organName = message.getOrganName();
        this.regno = message.getRegno();
        this.userSource = message.getUserSource();
        this.msgType = message.getMsgType();
        this.operationSn = message.getOperationSn();
        this.msgDetails = message.getMsgDetails();
        this.requestId = message.getRequestId();
        this.timestamp = message.getTimestamp();
        this.requestPath = message.getRequestPath();
        this.returnMessage = message.getReturnMessage();
        this.success = message.getSuccess();
    }

    public FromHisMessageDTO(FromHisMessage message) {
        super(message);
        this.certificateNo = message.getCertificateNo();
        this.patientName = message.getPatientName();
        this.patientId = message.getPatientId();
        this.cardNo = message.getCardNo();
        this.organCode = message.getOrganCode();
        this.organName = message.getOrganName();
        this.regno = message.getRegno();
        this.userSource = message.getUserSource();
        this.msgType = message.getMsgType();
        this.operationSn = message.getOperationSn();
        this.msgDetails = message.getMsgDetails();
        this.requestId = message.getRequestId();
        this.timestamp = message.getTimestamp();
        this.requestPath = message.getRequestPath();
        this.returnMessage = message.getReturnMessage();
        this.success = message.getSuccess();
    }

}
