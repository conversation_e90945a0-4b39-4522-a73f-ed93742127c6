package cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: jzs
 * @Date: 2023-10-23
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class DuplicateTotalStatisticsVM {

    @ApiModelProperty("总复印份数")
    private int totalShareCopies;

    @ApiModelProperty("总复印张数")
    private int totalFixCopies;
}
