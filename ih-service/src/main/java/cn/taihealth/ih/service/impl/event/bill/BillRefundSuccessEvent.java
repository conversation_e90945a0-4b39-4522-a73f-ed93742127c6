package cn.taihealth.ih.service.impl.event.bill;

import cn.taihealth.ih.domain.cloud.WechatInsuranceOrderRefund;
import cn.taihealth.ih.domain.cloud.WechatOrderRefund;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.service.impl.event.AbstractEntityEvent;
import java.util.List;


/**
 * 用户挂号成功事件
 *
 * @Author: Moon
 * @Date: 2020/8/6 3:31 下午
 */
public class BillRefundSuccessEvent extends AbstractEntityEvent<Long> {

    private final List<WechatOrderRefund> wechatOrderRefunds;
    private final List<WechatInsuranceOrderRefund> wechatInsuranceOrderRefunds;
    private final List<AliPayOrderRefund> aliPayOrderRefunds;
    private final Long hospitalId;

    public BillRefundSuccessEvent(Long hospitalId, List<WechatOrderRefund> wechatOrderRefunds,
                                  List<WechatInsuranceOrderRefund> wechatInsuranceOrderRefunds,
                                  List<AliPayOrderRefund> aliPayOrderRefunds) {
        super(hospitalId);
        this.hospitalId = hospitalId;
        this.wechatOrderRefunds = wechatOrderRefunds;
        this.wechatInsuranceOrderRefunds = wechatInsuranceOrderRefunds;
        this.aliPayOrderRefunds = aliPayOrderRefunds;
    }


    public List<WechatOrderRefund> getWechatOrderRefunds() {
        return wechatOrderRefunds;
    }


    public List<WechatInsuranceOrderRefund> getWechatInsuranceOrderRefunds() {
        return wechatInsuranceOrderRefunds;
    }


    public List<AliPayOrderRefund> getAliPayOrderRefunds() {
        return aliPayOrderRefunds;
    }

    public Long getHospitalId() {
        return hospitalId;
    }
}
