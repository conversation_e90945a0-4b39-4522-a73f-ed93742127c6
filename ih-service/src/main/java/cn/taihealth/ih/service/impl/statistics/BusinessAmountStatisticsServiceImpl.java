package cn.taihealth.ih.service.impl.statistics;

import cn.taihealth.ih.bean.statistics.BusinessResult;
import cn.taihealth.ih.commons.util.MathUtils;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.commons.util.TimeUtils.TimeUnit;
import cn.taihealth.ih.dao.StatisticsDaoHibernate;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.ClinicType;
import cn.taihealth.ih.domain.enums.DateUnitForSelect;
import cn.taihealth.ih.domain.enums.OnlineType;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.hospital.order.CheckGroupRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.statistics.BusinessAmountStatisticsService;
import cn.taihealth.ih.service.vm.statistics.evaluation.*;
import cn.taihealth.ih.service.vm.statistics.evaluation.BusinessStatisticsDaysVM.BusinessDaysCountVM;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Moon
 * @Date: 2021/3/17 上午9:15
 */
@Service
public class BusinessAmountStatisticsServiceImpl implements BusinessAmountStatisticsService {

    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final CheckGroupRepository checkGroupRepository;
    private final OrderRepository orderRepository;
    private final DeptRepository deptRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final List<OrderStatus> statusForStatistics = OrderStatus.getStatusForStatistics();
    private final StatisticsDaoHibernate statisticsDaoHibernate;

    public BusinessAmountStatisticsServiceImpl(
        PrescriptionOrderRepository prescriptionOrderRepository,
        CheckGroupRepository checkGroupRepository,
        OrderRepository orderRepository, DeptRepository deptRepository,
        MedicalWorkerRepository medicalWorkerRepository,
        StatisticsDaoHibernate statisticsDaoHibernate) {
        this.prescriptionOrderRepository = prescriptionOrderRepository;
        this.checkGroupRepository = checkGroupRepository;
        this.orderRepository = orderRepository;
        this.deptRepository = deptRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.statisticsDaoHibernate = statisticsDaoHibernate;
    }

    @Override
    public BusinessStatisticsVM getStatisticsToday(Hospital hospital, Date startTime, Date endTime) {
        BusinessStatisticsVM statToday = getStatVM(hospital, startTime, endTime);
        BusinessStatisticsVM statYesterday = getStatVM(hospital, DateUtils.addDays(startTime, -1),
            DateUtils.addDays(endTime, -1));
        String orderY = MathUtils.subtractAndDivision(statToday.getOrderCount(), statYesterday.getOrderCount());
        String checkY = MathUtils.subtractAndDivision(statToday.getChecksCount(), statYesterday.getChecksCount());
        String preY = MathUtils
            .subtractAndDivision(statToday.getPrescriptionCount(), statYesterday.getPrescriptionCount());
        String TreY = MathUtils
            .subtractAndDivision(statToday.getTreatmentDuration(), statYesterday.getTreatmentDuration());
        statToday.setOrderCountYield(orderY);
        statToday.setChecksCountYield(checkY);
        statToday.setPrescriptionCountYield(preY);
        statToday.setTreatmentDurationYield(TreY);

        return statToday;
    }

    @Override
    public List<BusinessStatisticsDeptsVM> getStatisticsDept(Hospital hospital, Date startTime, Date endTime) {

        List<BusinessResult> results = statisticsDaoHibernate
            .getBusinessGroupByDept(startTime, endTime, statusForStatistics, hospital.getId());
        return results.stream().map(u -> {
            BusinessStatisticsDeptsVM vm = new BusinessStatisticsDeptsVM(u);
            Dept dept = deptRepository.getById(u.getDeptId());
            vm.setDept(dept.getDeptName());
            return vm;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BusinessStatisticsDoctorsVM> getStatisticsDoctor(Hospital hospital, Date startTime, Date endTime) {
        List<BusinessResult> results = statisticsDaoHibernate
            .getBusinessGroupByDoctor(startTime, endTime, hospital.getId());
        return results.stream().map(u -> {
            BusinessStatisticsDoctorsVM vm = new BusinessStatisticsDoctorsVM(u);
            MedicalWorker doctor = medicalWorkerRepository.getById(u.getDoctorId());
            vm.setDoctor(doctor.getUser().getFullName());
            vm.setOrderCount(
                    (int) statisticsDaoHibernate
                    .getOrderCount(startTime, endTime, statusForStatistics, hospital.getId(), u.getDoctorId(), null,
                        null));
            return vm;
        }).filter(u -> u.getOrderCount() > 0)
            .sorted(Comparator.comparingLong(BusinessStatisticsDoctorsVM::getOrderCount).reversed())
            .collect(Collectors.toList());
    }

    @Override
    public List<BusinessStatisticsDrugsVM> getStatisticsDrug(Hospital hospital, Date startTime, Date endTime) {
        List<BusinessResult> results = statisticsDaoHibernate
            .getBusinessGroupByDrug(startTime, endTime, hospital.getId());

        return results.stream().map(BusinessStatisticsDrugsVM::new)
            .collect(Collectors.toList());
    }

    @Override
    public List<BusinessStatisticsDiseasesVM> getStatisticsDisease(Hospital hospital, Date startTime, Date endTime) {
        List<BusinessResult> results = statisticsDaoHibernate
            .getBusinessGroupByDisease(startTime, endTime, hospital.getId());
        Set<String> diseases = new HashSet<>();
        results.forEach(u -> {
            String[] split = StringUtils.split(u.getDisease(), "|");
            if (split != null) {
                diseases.addAll(Arrays.asList(split));
            }
        });
        return diseases.stream().map(u -> {
            BusinessStatisticsDiseasesVM vm = new BusinessStatisticsDiseasesVM(u);
            results.forEach(r -> {
                if (StringUtils.contains(r.getDisease(), vm.getDisease())) {
                    vm.setPrescriptionCount(vm.getPrescriptionCount() + r.getPrescriptionCount());
                }
            });
            vm.setOrderCount(
                    (int) statisticsDaoHibernate
                    .getOrderCount(startTime, endTime, statusForStatistics, hospital.getId(), null, null, u));
            return vm;
        }).sorted(Comparator.comparing(BusinessStatisticsDiseasesVM::getOrderCount).reversed())
            .collect(Collectors.toList());
    }

    @Override
    public BusinessStatisticsDeptsVM getStatistics(Hospital hospital, Date startTime, Date endTime) {
        BusinessResult results = statisticsDaoHibernate
            .getBusiness(startTime, endTime, statusForStatistics, hospital.getId());
        BusinessStatisticsDeptsVM vm = new BusinessStatisticsDeptsVM(results);
        return vm;
    }

    private BusinessStatisticsVM getStatVM(Hospital hospital, Date startTime, Date endTime) {
        BusinessStatisticsVM vm = new BusinessStatisticsVM();
        Long checksCount = getChecksCount(hospital, startTime, endTime);
        Long orderCount = getOrderCount(hospital, startTime, endTime);
        Long prescriptionCount = getPrescriptionCount(hospital, startTime, endTime);
        Long treatmentDuration = getTreatmentDuration(hospital, startTime, endTime);
        vm.setChecksCount(checksCount);
        vm.setOrderCount(orderCount);
        vm.setPrescriptionCount(prescriptionCount);
        vm.setTreatmentDuration(treatmentDuration);
        return vm;
    }


    private Long getOrderCount(Hospital hospital, Date startTime, Date endTime) {
        List<OrderStatus> statusForStatistics = OrderStatus.getStatusForStatistics();
        long orderCount = statisticsDaoHibernate
            .getOrderCount(startTime, endTime, statusForStatistics, hospital.getId(), null, null, null);
        return orderCount;
    }

    private Long getPrescriptionCount(Hospital hospital, Date startTime, Date endTime) {
        return prescriptionOrderRepository
            .countBySendUserDateIsAfterAndSendUserDateBeforeAndOrderHospital(startTime, endTime, hospital);
    }

    private Long getChecksCount(Hospital hospital, Date startTime, Date endTime) {
        return checkGroupRepository
            .countByPayTimeIsAfterAndPayTimeBeforeAndOrderHospital(startTime, endTime, hospital);
    }

    private Long getTreatmentDuration(Hospital hospital, Date startTime, Date endTime) {
        List<Specification<Order>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("onlineType", OnlineType.ONLINE));
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.or(Lists.newArrayList(Specifications.eq("orderType", ClinicType.OUT),
            Specifications.eq("orderType", ClinicType.CONSULT), Specifications.eq("orderType", ClinicType.EMERGENCY))));
        specs.add(Specifications.lt("endedDate", endTime));
        specs.add(Specifications.ge("endedDate", startTime));
        specs.add(Specifications.eq("status", OrderStatus.COMPLETED));
        List<Order> orders = orderRepository.findAll(Specifications.and(specs));
        long sum = orders.stream().filter(u -> u.getAdmissionDate() != null && u.getEndedDate() != null)
            .mapToLong(u -> u.getEndedDate().getTime() - u.getAdmissionDate().getTime()).sum();
        return sum / TimeUnit.MINUTE.getMillisecond();
    }

    @Override
    public List<BusinessStatisticsDaysVM> getStatisticsGroupByDate(
        Hospital hospital, Date startTime, Date endTime,
        DateUnitForSelect dateUnit) {
        String dateFormat = getDateFormatForMysqlByArg(dateUnit, startTime, endTime);
        List<BusinessResult> evaluationResults = statisticsDaoHibernate
            .getBusinessGroupByTypeAndDate(hospital.getId(), startTime, endTime, statusForStatistics, dateFormat);
        List<Date> dateList;
        if (dateUnit == DateUnitForSelect.CUSTOM) {
            dateList = TimeUtils.getDateListCustom(startTime, endTime);
        } else {
            dateList = TimeUtils.getDateListByDateUnit(dateUnit.getValue());
        }
        Map<Date, Integer> dateRatingMap = Maps.newHashMap();
        dateList.forEach(u -> dateRatingMap.put(u, 0));

        List<BusinessStatisticsDaysVM> vms = Lists
            .newArrayList(new BusinessStatisticsDaysVM(ClinicType.OUT.getName()),
                new BusinessStatisticsDaysVM(ClinicType.EMERGENCY.getName()),
                new BusinessStatisticsDaysVM(ClinicType.CONSULT.getName()));
        vms.forEach(u -> {
            evaluationResults.forEach(e -> {
                if (u.getName().equals(e.getType().getName())) {
                    dateRatingMap.put(e.getDateLabel(), e.getOrderCount());
                }
            });

            List<BusinessDaysCountVM> countVMs = dateRatingMap.keySet().stream().map(k -> {
                BusinessDaysCountVM countVM = new BusinessDaysCountVM();
                countVM.setDateLabel(k);
                countVM.setCount(dateRatingMap.get(k));
                return countVM;
            }).sorted(Comparator.comparing(BusinessDaysCountVM::getDateLabel)).collect(Collectors.toList());
            u.setData(countVMs);
            dateList.forEach(d -> dateRatingMap.put(d, 0));
        });
        return vms;
    }

    private List<BusinessDaysCountVM> getCountVM(Map<Date, Integer> dateRatingMap) {
        return dateRatingMap.keySet().stream().map(k -> {
            BusinessDaysCountVM countVM = new BusinessDaysCountVM();
            countVM.setDateLabel(k);
            countVM.setCount(dateRatingMap.get(k));
            return countVM;
        }).sorted(Comparator.comparing(BusinessDaysCountVM::getDateLabel)).collect(Collectors.toList());
    }

    private String getDateFormatForMysqlByArg(DateUnitForSelect dateUnit, Date startTime, Date endTime) {
        String dateFormat;
        switch (dateUnit) {
            case WEEK:
            case MONTH:
                dateFormat = "%Y-%m-%d";
                break;
            case YEAR:
            case OLD_YEAR:
                dateFormat = "%Y-%m-01";
                break;
            case CUSTOM:
                if (DateUtils.addDays(startTime, 31).getTime() > endTime.getTime()) {
                    dateFormat = "%Y-%m-%d";
                } else {
                    dateFormat = "%Y-%m-01";
                }
                break;
            default:
                throw new IllegalArgumentException("Unknown date");
        }
        return dateFormat;
    }


}
