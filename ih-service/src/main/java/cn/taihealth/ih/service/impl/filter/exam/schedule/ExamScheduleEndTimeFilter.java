package cn.taihealth.ih.service.impl.filter.exam.schedule;

import cn.taihealth.ih.domain.hospital.ExamSchedule;
import cn.taihealth.ih.service.impl.filter.DateFilter;

/**
 *
 * <AUTHOR>
 */
public class ExamScheduleEndTimeFilter extends DateFilter<ExamSchedule> {

    public ExamScheduleEndTimeFilter(String param) {
        super("endTime", param);
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public boolean equals(Object other) {
        if (!(other instanceof ExamScheduleEndTimeFilter)) {
            return false;
        }

        return super.equals(other);
    }
}
