package cn.taihealth.ih.service.vm.drugstore;

import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.wechat.service.vm.drug.DicMedAdmFreqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 药品基础信息
 * </p>
 *
 */
@Getter
@Setter
@NoArgsConstructor
public class DrugAdmFreqDTO extends AbstractEntityDTO {


    @ApiModelProperty("医院code")
    private String hospitalCode;
    /**
     * 给药频次代码
     */
    @ApiModelProperty("给药频次代码")
    private String frequencyCode;

    /**
     * 给药频次描述
     */
    @ApiModelProperty("给药频次描述")
    private String frequencyDesc;

    /**
     * 执行时间
     */
    @ApiModelProperty("执行时间")
    private String administrationTime;

    @ApiModelProperty("院内编码")
    private String hisCode;

    @ApiModelProperty("监管上报代码")
    private String supervisionCode;

    public DicMedAdmFreqDTO toDTO() {
        DicMedAdmFreqDTO dto = new DicMedAdmFreqDTO();
        dto.setId(this.getId());
        dto.setAdministrationTime(this.administrationTime);
        dto.setFrequencyCode(this.frequencyCode);
        dto.setFrequencyDesc(this.frequencyDesc);
        dto.setHospitalCode(this.hospitalCode);
        dto.setHisCode(this.hisCode);
        dto.setSupervisionCode(this.supervisionCode);
        return dto;
    }

}
