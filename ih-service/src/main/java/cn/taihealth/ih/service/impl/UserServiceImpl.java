package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.IdCardUtils;
import cn.taihealth.ih.commons.util.IdCardUtils.IdCardBean;
import cn.taihealth.ih.commons.util.RandomUtils;
import cn.taihealth.ih.domain.IdCard;
import cn.taihealth.ih.domain.UserAddress;
import cn.taihealth.ih.domain.UserStats;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.service.LinkService;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.cloud.PlatformRoleRepository;
import cn.taihealth.ih.repo.cloud.UserFileRepository;
import cn.taihealth.ih.repo.cloud.UserRoleRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.wechat.WechatClient;
import cn.taihealth.ih.service.api.wechat.WechatService;
import cn.taihealth.ih.service.cache.*;
import cn.taihealth.ih.service.cache.RoleAuthCache.CacheAuth;
import cn.taihealth.ih.service.cache.RoleAuthCache.CacheResource;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.impl.event.UserAddEvent;
import cn.taihealth.ih.service.impl.face.FaceRecognitionService;
import cn.taihealth.ih.service.impl.im.tencent.TencentSignatureUtils;
import cn.taihealth.ih.service.util.GenderUtils;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.SettingsHelper;
import cn.taihealth.ih.service.util.UserRoleProvider;
import cn.taihealth.ih.service.vm.MedicalWorkerVM;
import cn.taihealth.ih.service.vm.SignupVM;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.UserClearVM;
import cn.taihealth.ih.spring.security.Interceptor.AuthorizedUrl;
import cn.taihealth.ih.spring.security.PlatformRoleCodeName;
import cn.taihealth.ih.spring.security.UserRoleCodeName;
import cn.taihealth.ih.wechat.service.vm.wechat.IhWxMpOAuth2AccessToken;
import cn.taihealth.ih.wechat.service.vm.wechat.IhWxUser;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTimeConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    private final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    private final UserRepository userRepository;
    private final PasswordManager passwordEncoder;
    private final TencentIMRepository tencentIMRepository;
    private final PatientRepository patientRepository;
    private final UploadService uploadService;
    private final UserIdCardRepository userIdCardRepository;
    private final TencentIMService tencentIMService;
    private final WechatCodeCache wechatTokenCache;
    private final WechatMiniUserCache wechatMiniUserCache;
    private final UserAddressRepository userAddressRepository;
    private final UserCache userCache;
    private final ApplicationProperties applicationProperties;
    private final MedicalWorkerService medicalWorkerService;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final FaceRecognitionService faceRecognitionService;
    private final UploadRepository uploadRepository;
    private final LockService lockService;
    private final UserManager userManager;
    private final UserRoleRepository userRoleRepository;
    private final RoleAuthCache roleAuthCache;
    private final UserFileRepository userFileRepository;
    private final UserCacheFindService userCacheFindService;
    private final WechatClient wechatClient;
    private final ApplicationEventPublisher eventPublisher;
    private final PlatformRoleRepository platformRoleRepository;

    @PersistenceContext
    private EntityManager em;

    @Inject
    public UserServiceImpl(UserRepository userRepository,
                           TencentIMRepository tencentIMRepository,
                           PatientRepository patientRepository,
                           PasswordManager passwordEncoder,
                           UploadService uploadService,
                           UserIdCardRepository userIdCardRepository,
                           TencentIMService tencentIMService,
                           WechatCodeCache wechatTokenCache,
                           WechatMiniUserCache wechatMiniUserCache,
                           UserAddressRepository userAddressRepository,
                           UserCache userCache,
                           ApplicationProperties applicationProperties,
                           MedicalWorkerService medicalWorkerService,
                           MedicalWorkerRepository medicalWorkerRepository,
                           FaceRecognitionService faceRecognitionService,
                           LockService lockService,
                           UserManager userManager,
                           UserRoleRepository userRoleRepository,
                           RoleAuthCache roleAuthCache,
                           UploadRepository uploadRepository,
                           UserCacheFindService userCacheFindService,
                           UserFileRepository userFileRepository, WechatClient wechatClient,
                           ApplicationEventPublisher eventPublisher,
                           PlatformRoleRepository platformRoleRepository) {
        this.userRepository = userRepository;
        this.tencentIMRepository = tencentIMRepository;
        this.patientRepository = patientRepository;
        this.passwordEncoder = passwordEncoder;
        this.uploadService = uploadService;
        this.userIdCardRepository = userIdCardRepository;
        this.tencentIMService = tencentIMService;
        this.wechatTokenCache = wechatTokenCache;
        this.wechatMiniUserCache = wechatMiniUserCache;
        this.userAddressRepository = userAddressRepository;
        this.userCache = userCache;
        this.userRoleRepository = userRoleRepository;
        this.applicationProperties = applicationProperties;
        this.medicalWorkerService = medicalWorkerService;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.faceRecognitionService = faceRecognitionService;
        this.uploadRepository = uploadRepository;
        this.lockService = lockService;
        this.userManager = userManager;
        this.roleAuthCache = roleAuthCache;
        this.userFileRepository = userFileRepository;
        this.userCacheFindService = userCacheFindService;
        this.wechatClient = wechatClient;
        this.eventPublisher = eventPublisher;
        this.platformRoleRepository = platformRoleRepository;
    }

    private String statsLock(User user) {
        return "lock.user.stats." + user.getId();
    }

    @Override
    @Transactional
    public User checkOrCreateUser(String name, String mobile, String idNo, String appCode) {

        Optional<User> userOptional = userCacheFindService.findOneByMobile(mobile);
        User user;
        if (userOptional.isPresent()) {
            user = userOptional.get();
        } else {
            IdCardBean idCard = null;
            if (StringUtils.isNotBlank(idNo)) {
                idCard = IdCardUtils.isValidatedAllIdCard(idNo);
            }
            ManagedUserDTO userDTO = new ManagedUserDTO();
            userDTO.setMobile(mobile);
            if (org.apache.logging.log4j.util.Strings.isBlank(name)) {
                userDTO.setFullName(mobile);
            } else {
                userDTO.setFullName(name);
            }
            userDTO.getRoles().add(new UserRoleCodeName("USER", "用户"));
            userDTO.setSource(appCode);
            userDTO.setIdentity(idNo);
            if (idCard != null) {
                userDTO.setBirthday(idCard.getBirthday());
                userDTO.setGender(GenderUtils.toGender(idCard.getSex()));
            }
            user = createUser(userDTO);
            // 添加用户来源05 API
            UserSource source = new UserSource();
            source.setSignupSource(SignupSource.MINI_PATIENT);
            source.setUserId(user.getId());
            source.setSignupSource(SignupSource.API);
            eventPublisher.publishEvent(new UserAddEvent(source));
        }
        return user;
    }

    @Override
    public User signup(String appId, SignupVM vm, String ipAddress, Hospital hospital) {
        User newUser = userCacheFindService.findOneByMobile(vm.getMobile()).orElse(new User());
        if (newUser.isNew()) {
            newUser.setSource(vm.getSignupSource().name());
            if (Strings.isNullOrEmpty(vm.getUsername())) {
                newUser.setUsername(UUID.randomUUID().toString().replace("-", ""));
            } else {
                newUser.setUsername(vm.getUsername());
            }
            newUser.setMobile(vm.getMobile());
            newUser.setFullName(vm.getFullName());
            if (Strings.isNullOrEmpty(newUser.getFullName())) {
                newUser.setFullName(newUser.getMobile());
            }
            newUser.setSignupIp(ipAddress);
            userRoleRepository.findOneByCode("USER")
                .ifPresent(r -> newUser.getUserRoles().add(r));
            if (Strings.isNullOrEmpty(vm.getPassword())) {
                newUser.setPassword(passwordEncoder.encode(RandomUtils.generatePassword()));
            } else {
                newUser.setPassword(passwordEncoder.encode(vm.getPassword()));
            }
        }
        IhWxUser wxUser = null;
        WechatService wechatService = AppContext.getInstance(WechatService.class);
        if (vm.getWechatCode() != null) {
            if (vm.getSignupSource() == SignupSource.MINI_DOCTOR || vm.getSignupSource() == SignupSource.MINI_PATIENT) {
                // 小程序
                wxUser = wechatMiniUserCache.getUser(vm.getWechatCode());
            } else {
                IhWxMpOAuth2AccessToken token = wechatTokenCache.getToken(vm.getWechatCode());
                if (token == null) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("页面已过期，请退出后重新进入");
                }
                wxUser = wechatClient.getIhWxUser(token.getAccessToken(), token.getOpenId());
            }
            if (newUser.isNew()) {
                // 微信性别 0:UNKNOWN, 1:MALE, 2:FEMALE
//                Gender gender = Gender.values()[Integer.parseInt(wxUser.getSex())];
//                newUser.setGender(gender);
                newUser.setFullName(wxUser.getNickname());
            }
        }
        if (newUser.isNew()) {
            saveUser(newUser, null);
            // 添加用户来源03 H5_PATIENT
            UserSource userSource = new UserSource();
            userSource.setUserId(newUser.getId());
            userSource.setSignupSource(SignupSource.H5_PATIENT);
            userSource.setAppId(appId);
            userSource.setHospital(hospital);
            userSource.setSignupIp(ipAddress);
            eventPublisher.publishEvent(new UserAddEvent(userSource));
        }
        if (wxUser != null) {
            if (newUser.isNew()) {
                uploadAvatar(newUser, wxUser.getHeadImgUrl());
            }
            wechatService.addOrUpdateWechatInfo(newUser, wxUser, appId, hospital);
        }

        return newUser;
    }

    /**
     * 根据头像url保存头像
     *
     * @param user
     * @param headImgUrl
     */
    private void uploadAvatar(User user, String headImgUrl) {
        UploadResource resource = UploadResource.of(headImgUrl, UploadType.AVATAR, null);
        Upload upload = uploadService.upload(user, resource);
        user.setAvatarType(User.AvatarType.UPLOAD);
        user.setUploadedAvatar(upload);
        user.setAvatarUrl(AppContext.getInstance(LinkService.class).urlOfUserAvatar(user));
        saveUser(user, null);
    }

    @Override
    public User createUser(ManagedUserDTO user) {
        User newUser = user.toUser();
        if (Strings.isNullOrEmpty(user.getUsername())) {
            String username = UUID.randomUUID().toString().replace("-", "");
            newUser.setUsername(username);
            user.setUsername(username);
        } else {
            newUser.setUsername(user.getUsername());
        }
        if (Strings.isNullOrEmpty(newUser.getFullName())) {
            newUser.setFullName(newUser.getUsername());
        }
        if (Strings.isNullOrEmpty(user.getPassword())) {
            newUser.setPassword(passwordEncoder.encode(RandomUtils.generatePassword()));
        } else {
            newUser.setPassword(passwordEncoder.encode(user.getPassword()));
        }

        AtomicBoolean haveUser = new AtomicBoolean(false);
        newUser.setUserRoles(user.getRoles().stream().map(r -> {
            if ("USER".equals(r.getCode())) {
                haveUser.set(true);
            }
            return userRoleRepository.findOneByCode(r.getCode()).orElse(null);
        }).filter(Objects::nonNull).collect(Collectors.toList()));
        if (!haveUser.get()) {
            userRoleRepository.findOneByCode("USER")
                .ifPresent(r -> newUser.getUserRoles().add(r));
        }

        saveUser(newUser, null);
        return newUser;
    }

    @Override
    public void updateUserRoles(User user, List<UserRoleCodeName> roleCodeNames) {
        List<String> roleCodes;
        if (roleCodeNames == null) {
            roleCodes = Lists.newArrayList();
        } else {
            roleCodes = roleCodeNames.stream().map(UserRoleCodeName::getCode).collect(Collectors.toList());
        }
        if (!roleCodes.contains("USER")) {
            roleCodes.add("USER");
        }
        List<UserRole> otherRoles = user.getUserRoles().stream()
                .filter(u -> u.getHospital() != null).collect(Collectors.toList());
        List<UserRole> oldRoles = user.getUserRoles().stream()
                .filter(u -> u.getHospital() == null).collect(Collectors.toList());
        // 删除旧的没有的角色关联
        for (int i = oldRoles.size() - 1; i > -1; i--) {
            UserRole oldRole = oldRoles.get(i);
            if (roleCodes.stream().noneMatch(u -> Objects.equals(u, oldRole.getCode()))) {
                oldRoles.remove(i);
            }
        }

        // 添加新的角色关联
        List<String> newCodes = Lists.newArrayList();
        roleCodes.forEach(u -> {
            if (oldRoles.stream().noneMatch(v -> Objects.equals(u, v.getCode()))) {
                newCodes.add(u);
            }
        });

        newCodes.forEach(u -> userRoleRepository.findOneByCodeAndHospital(u, null).ifPresent(oldRoles::add));
        otherRoles.addAll(oldRoles);
        user.setUserRoles(otherRoles);
        saveUser(user, null);
    }

    @Override
    public void updatePlatformUser(UpdatePlatformRolesVM vm) {
        User user = userCacheFindService.findOneByUsername(vm.getUsername()).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        user.setFullName(vm.getFullName());
        saveUser(user, null);
        updatePlatformRoles(user, vm.getPlatformRoles());
    }

    @Override
    public void updatePlatformRoles(User user, List<PlatformRoleCodeName> roleCodeNames) {
        List<String> roleCodes = Lists.newArrayList();
        if (roleCodeNames == null) {
            roleCodes = Lists.newArrayList();
        } else {
            roleCodes = roleCodeNames.stream().map(PlatformRoleCodeName::getCode).collect(Collectors.toList());
        }
//        User user = userCacheFindService.findOneByUsername(username).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        List<PlatformRole> otherRoles = user.getPlatformRoles() == null ? user.getPlatformRoles() : Lists.newArrayList();
        List<PlatformRole> oldRoles = user.getPlatformRoles() == null ? user.getPlatformRoles() : Lists.newArrayList();

        // 删除旧的没有的角色关联
        for (int i = otherRoles.size() - 1; i > -1; i--) {
            PlatformRole oldRole = otherRoles.get(i);
            if (roleCodes.stream().noneMatch(u -> Objects.equals(u, oldRole.getCode()))) {
                otherRoles.remove(i);
            }
        }

        // 添加新的角色关联
        List<String> newCodes = Lists.newArrayList();
        roleCodes.forEach(u -> {
            if (oldRoles.stream().noneMatch(v -> Objects.equals(u, v.getCode()))) {
                newCodes.add(u);
            }
        });

        newCodes.forEach(u -> platformRoleRepository.findOneByCode(u).ifPresent(oldRoles::add));
        otherRoles.addAll(oldRoles);
        user.setPlatformRoles(otherRoles);
        saveUser(user, null);
        roleAuthCache.evictSupervisorRoles(user.getUsername());
    }

    @Override
    public boolean FaceRecognition(UploadDTO uploadDTO, User user) {
        try {
            Optional<IdCard> oneByUser = userIdCardRepository.findOneByUserAndPatient(user, null);
            if (oneByUser.isPresent()) {
                IdCard idCard = oneByUser.get();
                UploadDTO backImage = new UploadDTO(idCard.getBackImage());
                return faceRecognitionService
                    .FaceRecognitionByUrl(uploadDTO.getUrl(), backImage.getUrl());
            }
            return false;
        } finally {
            uploadService.deleteUpload(uploadRepository.getById(uploadDTO.getId()));
        }

    }

    @Override
    @Transactional
    public User saveHospitalUser(Hospital hospital, MedicalWorkerVM vm) {
        User user;
        boolean isTestUser = false;
        if (vm.isNew()) {
            user = userCacheFindService.findOneByMobile(vm.getMobile()).orElse(null);
            isTestUser = user != null && user.getUserType() == User.UserType.TEST;
            if (!isTestUser) {
                if (user != null) {
                    throw ErrorType.MOBILE_ALREADY_USED.toProblem("您输入的手机号已经被使用，请重新输入。");
                } else {
                    user = userCacheFindService.findOneByUsername(vm.getUsername()).orElse(null);
                    if (user != null) {
                        throw ErrorType.USERNAME_ALREADY_USED.toProblem("您输入的用户名已经被使用，请重新输入。");
                    }
                }
            }
        } else {
            user = medicalWorkerRepository.getById(vm.getId()).getUser();
            User finalUser = user;
            userCacheFindService.findOneByMobile(vm.getMobile()).ifPresent(u -> {
                if (!Objects.equals(finalUser, u)) {
                    throw ErrorType.MOBILE_ALREADY_USED.toProblem("您输入的手机号已经被使用，请重新输入。");
                }
            });
        }
        if (user == null) {
            ManagedUserDTO mDto = vm.toManagedUser();
            user = createUser(mDto);
            // 添加用户来源06 DASHBOARD
            UserSource source = new UserSource();
            source.setSignupSource(SignupSource.DASHBOARD);
            source.setUserId(user.getId());
            source.setHospital(hospital);
            eventPublisher.publishEvent(new UserAddEvent(source));
            vm.setOnlineVisit(user.getUserRoles().stream().anyMatch(u -> u.isDoctor() || u.isNurse() || u.isHeadNurse()));
        } else {
            UserClearVM uvm = new UserClearVM(user);
            List<UserRole> roles = user.getUserRoles();
            roles = roles.stream().filter(r -> !Objects.equals(r.getHospital(), hospital))
                .collect(Collectors.toList());
            roles.addAll(
                vm.getRoles().stream().map(r ->
                                               userRoleRepository.findOneByCode(r.getCode()).orElse(null))
                    .filter(r -> r != null && r.getHospital() != null)
                    .collect(Collectors.toList())
            );
            user.setUserRoles(roles);

            user.setGender(vm.getGender());
            user.setFullName(vm.getName());
            user.setMobile(vm.getMobile());
            if (!medicalWorkerService.userIsInOtherHospital(hospital, user)) {
                user.setUsername(vm.getUsername());
            }
            saveUser(user, uvm);
            roleAuthCache.evictHospitalRoles(hospital.getCode(), user.getUsername());
            vm.setOnlineVisit(roles.stream().anyMatch(u -> u.isDoctor() || u.isNurse() || u.isHeadNurse()));
        }

        if (vm.getAvatar() != null && !vm.getAvatar().isNew()) {
            user.setAvatarType(User.AvatarType.UPLOAD);
            user.setUploadedAvatar(uploadRepository.getById(vm.getAvatar().getId()));
            user.setAvatarUrl(AppContext.getInstance(LinkService.class).urlOfUserAvatar(user));
        }

        vm.setUser(new UserDTO(user));
        if (null != vm.getSignature()) {
            saveSignature(user, vm.getSignature());
        }

        medicalWorkerService.save(hospital, vm);
        getUserIM(hospital, user);
        return user;
    }

    @Override
    public void saveSignature(User user, UploadVM signature) {
        Upload upload = uploadRepository.findById(signature.getId())
            .orElseThrow(ErrorType.FILE_NOT_FOUND::toProblem);
        UserFile userFile = userFileRepository.findOneByUser(user).orElseGet(UserFile::new);
        userFile.setUser(user);
        userFile.setSignature(upload);
        userFileRepository.save(userFile);
    }

    @Override
    public UserFile getUserFile(User user) {
        return userFileRepository.findOneByUser(user).orElse(null);
    }

    @Override
    @Transactional
    public TencentIM updateTencentIM(Hospital hospital, User user) {
        if (HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.IM_TYPE, HospitalIMEnum.class) == HospitalIMEnum.UNKNOWN) {
            log.error("医院：" + hospital.getCode() + "没有配置IM，跳过IM相关逻辑");
            return null;
        }
        TencentIM tim = tencentIMRepository.findOneByUserAndHospitalId(user, hospital.getId());

        if (tim == null) {
            tim = new TencentIM();
            tim.setUser(user);
        }

        // expires one day longer than refresh token expires
        long expiresInSeconds = applicationProperties.getSecurity().getAuthentication().getJwt()
            .getRefreshTokenValidityInSeconds() + DateTimeConstants.SECONDS_PER_DAY;

        Date expirationDate = DateUtils.addSeconds(new Date(), (int) expiresInSeconds);
        tim.setSignature(
            TencentSignatureUtils.generateSignature(hospital, user.getUsername(), 2 * expiresInSeconds));
        tim.setExpirationDate(expirationDate);
        tim.setHospitalId(hospital.getId());
        tencentIMRepository.save(tim);
        return tim;
    }

    @Override
    public TencentIM getUserIM(Hospital hospital, User user) {
        if (HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.IM_TYPE, HospitalIMEnum.class) == HospitalIMEnum.UNKNOWN) {
            log.error("医院：" + hospital.getCode() + "没有配置IM，跳过IM相关逻辑");
            return null;
        }
        return lockService.executeWithLock("ih.userIM." + user.getId(), () -> {
            tencentIMService.createAccount(hospital, user);

            TencentIM tim = tencentIMRepository.findOneByUserAndHospitalId(user, hospital.getId());
            if (tim == null || StringUtils.isEmpty(tim.getSignature())) {
                tencentIMService.createAccount(hospital, user);
                tim = AppContext.getInstance(UserService.class).updateTencentIM(hospital, user);
            }
            if (new Date().compareTo(tim.getExpirationDate()) > 0) {
                tim = AppContext.getInstance(UserService.class).updateTencentIM(hospital, user);
            }
            return tim;
        });
    }

    @Override
    public TencentIM getUserIM(Hospital hospital, String username) {
        if (HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.IM_TYPE, HospitalIMEnum.class) == HospitalIMEnum.UNKNOWN) {
            log.error("医院：" + hospital.getCode() + "没有配置IM，跳过IM相关逻辑");
            return null;
        }
        User user = userCacheFindService.findOneByUsername(username)
            .orElseThrow(EntityNotFoundException::new);
        return getUserIM(hospital, user);
    }

    @Override
    @Transactional
    public User updateUser(User updater, UserDTO dto) {
        User user = userCacheFindService.findOneByUsername(dto.getUsername()).orElse(null);
        if (user == null) {
            return null;
        }

        UserClearVM vm = new UserClearVM(user);

        // TODO: 锁定用户
//        if (updater.hasAuthority(User.Authority.MODERATOR)) {
//            user.setAuthority(dto.getAuthority());
//            user.setLocked(dto.isLocked());
//            user.setUserType(dto.getUserType());
//        }

        user.setAddress(dto.getAddress());
        user.setGender(dto.getGender());
        user.setFullName(dto.getFullName());
        user.setBirthday(dto.getBirthday());
        user.setMobile(dto.getMobile());
        if (StringUtils.isNotBlank(dto.getIdentity())) {
            user.setIdentity(dto.getIdentity());
        }
        if (dto.getUserType() != null) {
            user.setUserType(dto.getUserType());
        }
        saveUser(user, vm);

        log.debug("Changed Information for User: {}", user);
        return user;
    }

//    private String createLetterAvatar(User user) {
//        String url = avatarService.createLetterAvatar(user);
//        if (user.getAvatarType() == User.AvatarType.LETTER) {
//            user.setAvatarUrl(url);
//            saveUser(user);
//        }
//
//        return url;
//    }

    @Override
    public User updatePassword(User user, String password) {
        user.setPassword(passwordEncoder.encode(password));
        saveUser(user, null);
        return user;
    }

    @Override
    public void blockUser(User user) {
        user.setLocked(true);
        saveUser(user, null);
    }

    @Override
    public void unblockUser(User user) {
        user.setLocked(false);
        saveUser(user, null);
    }

    @Override
    public void deleteUser(User user) {

    }

    @Override
    public void saveUser(User user, UserClearVM vm) {
        if (vm != null) {
            userCache.evictMobile(vm.getMobile());
            userCache.evictUsername(vm.getUsername());
            if (user != null && !user.isNew()) {
                tencentIMRepository.deleteByUserId(user.getId());
            }
        }
        roleAuthCache.evictRoles(user.getUsername());
        userRepository.save(user);
        userCache.evictMobile(user.getMobile());
        userCache.evictUsername(user.getUsername());
    }

    @Override
    public void alterUser(User user, String oldUserName, String oldMobile) {
        user.setPlatformRoles(Lists.newArrayList());
        updateUserRoles(user, Lists.newArrayList());
        userCache.evictMobile(oldMobile);
        userCache.evictUsername(oldUserName);

        List<String> hospitalCodeList =
                user.getUserRoles().stream().map(UserRole::getHospital)
                        .filter(hospital -> !Objects.isNull(hospital))
                        .map(Hospital::getCode)
                        .distinct()
                        .collect(Collectors.toList());
        hospitalCodeList.forEach(elem -> roleAuthCache.evictHospitalRoles(elem, oldUserName));
    }


    @Override
    @SuppressWarnings("unchecked")
    public Page<User> findAll(String pattern, int page, int size) {
        Specification<User> spec = null;
        if (!Strings.isNullOrEmpty(pattern)) {
            spec = Specifications.or(Specifications.like("username", pattern),
                                     Specifications.like("mobile", pattern), Specifications.like("fullName", pattern));
        }

        Pageable request = PageRequest.of(page, size == 0 ? Constants.BATCH_SIZE : size);
        return userRepository.findAll(spec, request);
    }

    @Override
    public UserAddress saveUserAddress(User user, UserAddressDTO dto, Hospital hospital) {
        if (Strings.isNullOrEmpty(dto.getAddress())) {
            throw new RuntimeException("Address is required");
        }

        UserAddress userAddress;
        if (dto.isNew()) {
            //新增第一条地址，设置默认地址,如果是更新默认地址，需要把之前的默认地址改为非默认
            List<UserAddress> addressesList = userAddressRepository.findByUserAndHospital(user, hospital);

            //是否达到地址限制数量
            int addressLimit = SettingsHelper.getInt(SettingKey.USER_ADDRESS_MAX_LIMIT);
            if (addressesList.size() >= addressLimit) {
                throw ErrorType.ADDRESS_MAX_LIMIT.toProblem("超出地址数量上限");
            }
            userAddress = new UserAddress();
            if (dto.isDefaultAddress()) {
                UserAddress userAddressUpdate = addressesList.stream()
                    .filter(ud -> ud.isDefaultAddress()).findFirst().orElse(null);
                if (null != userAddressUpdate) {
                    updateDefaultAddress(userAddressUpdate);
                }
                userAddress.setDefaultAddress(dto.isDefaultAddress());
            }
            userAddress.setUser(user);
        } else {
            userAddress = userAddressRepository.getById(dto.getId());
            if (dto.isDefaultAddress()) {
                List<UserAddress> addressesList = userAddressRepository.findByUserAndHospital(user, hospital);
                UserAddress userAddressUpdate = addressesList.stream()
                    .filter(ud -> ud.isDefaultAddress()).findFirst().orElse(null);
                if (null != userAddressUpdate) {
                    updateDefaultAddress(userAddressUpdate);
                }
            }
            userAddress.setDefaultAddress(dto.isDefaultAddress());
        }
        userAddress.setProvince(dto.getProvince());
        userAddress.setCity(dto.getCity());
        userAddress.setArea(dto.getArea());
        userAddress.setAddress(dto.getAddress());
        userAddress.setTelephone(dto.getTelephone());
        userAddress.setReceiverName(dto.getReceiverName());
        userAddress.setHospital(hospital);
//        GeoUtils.GeoInfo geoInfo = null;
//        try {
//            geoInfo = GeoUtils.addressToGeoInfo(dto.getAddress(), null);
//            userAddress.setLongitude(geoInfo.getLngLat().getLongitude());
//            userAddress.setLatitude(geoInfo.getLngLat().getLatitude());
//            userAddress.setCoordinator(GeoUtils.makePoint(
//                userAddress.getLongitude(),
//                userAddress.getLatitude()));
//        } catch (GeoException e) {
//            log.error("Unable to locate address " + dto.getAddress(), e);
//        }

        userAddress.setVisibility(dto.getVisibility());
        userAddress.setLabel(dto.getLabel());
        userAddressRepository.save(userAddress);

        return userAddress;
    }

    private void updateDefaultAddress(UserAddress userAddress) {
        userAddress.setDefaultAddress(false);
        userAddressRepository.saveAndFlush(userAddress);
    }

    @Override
    public User getAdmin() {
        return userCacheFindService.findOneByUsername(Constants.ADMIN_ACCOUNT)
            .orElse(null);
    }

    @Override
    public User getSystem() {
        return userCacheFindService.findOneByUsername(Constants.SYSTEM_ACCOUNT)
            .orElseThrow(() -> new RuntimeException("system user not found"));
    }

    @Override
    public User updateHealthRecordPassword(User user, String password) {
        user.setHealthRecordPassword(passwordEncoder.encode(password));
        saveUser(user, null);
        return user;
    }

    @Override
    public void saveUserSignature(Hospital hospital, User user, Upload uploadSignature) {
        MedicalWorker medicalWorker = medicalWorkerRepository
            .findOneByHospitalAndUser(hospital, user).orElse(null);
        if (null != medicalWorker) {
            medicalWorker.setSignature(uploadSignature);
            AppContext.getInstance(MedicalWorkerManager.class).save(medicalWorker);
        }
    }

    @Override
    public UserStats addVisitCount(Order order) {
        return lockService.executeWithLock(statsLock(order.getDoctor().getUser()),
                                           () -> userManager.addVisitCount(order));
    }

    @Override
    public UserStats removeVisitCount(Order order) {
        return lockService.executeWithLock(statsLock(order.getDoctor().getUser()),
                                           () -> userManager.removeVisitCount(order));
    }


    @Override
    public UserStats addRating(User user, int rating) {
        return lockService.executeWithLock(statsLock(user),
                                           () -> userManager.addRating(user, rating));
    }

    @Override
    @Transactional
    public void initLocalImAccount(Hospital hospital) {
        log.info("删除全部im签名");
        tencentIMRepository.deleteByHospitalId(hospital.getId());
//        User admin = getAdmin();
//        if (admin != null) {
//            updateTencentIM(hospital, admin);
//        }
    }

    @Override
    public LinkedHashMap<RequestMatcher, Collection<ConfigAttribute>> getAuth(String hospitalCode, String roleCode) {
        String role = "ROLE_" + roleCode;
        List<CacheAuth> auths = roleAuthCache.getAuths(roleCode);
        AuthorizedUrl authorizedUrl = new AuthorizedUrl();
        if (StringUtils.isBlank(hospitalCode) || "USER".equals(roleCode)) {
            auths.forEach(a -> {
                a.getResources().forEach(r -> {
                    authorizedUrl(authorizedUrl, r, role);
                });
            });
        } else {
            List<PageSettingDTO> dtos = AppContext.getInstance(PageSettingService.class)
                .getPCPageSettings(hospitalCode);

            if (roleCode.startsWith("HOSPITAL_OPT_")) {
                // 医院管理员拥有医院全部菜单权限
                UserRole r = UserRoleProvider.getInstance().getRole("CUSTOM");
                r.getAuthorities().forEach(a -> {
                    if (authInPageSetting(a, dtos)) {
                        a.getResources().forEach(s -> {
                            authorizedUrl(authorizedUrl, s, role);
//                            authorizedUrl.antMatchers(s.getMethod(), s.getResourcePath()).hasAnyAuthority(role);
                        });
                    }
                });
            } else {
                auths.forEach(a -> {
                    if (authInPageSetting(a, dtos)) {
                        a.getResources().forEach(r -> {
                            authorizedUrl(authorizedUrl, r, role);
//                            authorizedUrl.antMatchers(r.getMethod(), r.getResourcePath()).hasAnyAuthority(role);
                        });
                    }
                });
            }
        }
        return authorizedUrl.getRequestMap();
    }

    @Override
    public LinkedHashMap<RequestMatcher, Collection<ConfigAttribute>> getSupervisorAuth(String roleCode) {
        String role = "ROLE_" + roleCode;
        List<CacheAuth> auths = roleAuthCache.getSupervisorAuths(roleCode);
        AuthorizedUrl authorizedUrl = new AuthorizedUrl();
        auths.forEach(a -> {
            if (a == null || a.getResources() == null) {
                return;
            }
            a.getResources().forEach(r -> {
                authorizedUrl(authorizedUrl, r, role);
            });
        });
        return authorizedUrl.getRequestMap();
    }


    private void authorizedUrl(AuthorizedUrl authorizedUrl, UserResource r, String role) {
        if (StringUtils.isNotBlank(r.getResourcePath())) {
            authorizedUrl.antMatchers(r.getMethod(), r.getResourcePath()).hasAnyAuthority(role);
        }
    }

    private void authorizedUrl(AuthorizedUrl authorizedUrl, CacheResource r, String role) {
        if (StringUtils.isNotBlank(r.getResourcePath())) {
            authorizedUrl.antMatchers(r.getMethod(), r.getResourcePath()).hasAnyAuthority(role);
        }
    }

    private boolean authInPageSetting(CacheAuth auth, List<PageSettingDTO> dtos) {
        if (dtos.stream().anyMatch(d -> Objects.equals(d.getCode(), auth.getCode()))) {
            return true;
        }
        boolean in = false;
        for (PageSettingDTO d : dtos) {
            if (authInPageSetting(auth, d.getPageSettings())) {
                in = true;
                break;
            }
        }
        return in;
    }

    private boolean authInPageSetting(UserAuthority auth, List<PageSettingDTO> dtos) {
        if (dtos.stream().anyMatch(d -> Objects.equals(d.getCode(), auth.getCode()))) {
            return true;
        }
        boolean in = false;
        for (PageSettingDTO d : dtos) {
            if (authInPageSetting(auth, d.getPageSettings())) {
                in = true;
                break;
            }
        }
        return in;
    }

    @Override
    public boolean isDoctor(User user, Hospital hospital) {
        return roleAuthCache.getHospitalRoles(hospital.getCode(), user.getUsername())
            .stream().anyMatch(role -> role.isDoctor() && !role.isAdmin());
    }

    @Override
    public boolean isHeadNurse(User user, Hospital hospital) {
        return roleAuthCache.getHospitalRoles(hospital.getCode(), user.getUsername())
                .stream().anyMatch(role -> role.isHeadNurse() && !role.isAdmin());
    }

    @Override
    public boolean isNurse(User user, Hospital hospital) {
        return roleAuthCache.getHospitalRoles(hospital.getCode(), user.getUsername())
                .stream().anyMatch(role -> role.isNurse() && !role.isAdmin());
    }

    @Override
    public boolean isPharmacist(User user, Hospital hospital) {
        return roleAuthCache.getHospitalRoles(hospital.getCode(), user.getUsername())
                .stream().anyMatch(role -> role.isPharmacist() && !role.isAdmin());
    }

    @Override
    public void logout(User user, String idToken, Date expiredDate) {
        AppContext.getInstance(JwtTokenCache.class).putUserToken(user.getUsername(), idToken, expiredDate);
    }

}
