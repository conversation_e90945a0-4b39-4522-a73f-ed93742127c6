package cn.taihealth.ih.service.impl.filter.crm.taskcount;

import cn.taihealth.ih.domain.crm.CrmTaskDetail;
import cn.taihealth.ih.service.impl.filter.DateFilter;
import cn.taihealth.ih.service.impl.filter.signlog.RecordDateFilter;
import java.util.Objects;

/**
 *
 */
public class EndTimeFilter extends DateFilter<CrmTaskDetail> {


    public EndTimeFilter(String param) {
        super("planTime", param);
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public String toExpression() {
        return "recordDate:" + this.getDate();
    }

    @Override
    public boolean isValid() {
        return true;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof RecordDateFilter)) {
            return false;
        }

        RecordDateFilter rhs = (RecordDateFilter) other;
        return Objects.equals(this.getDate(), rhs.getDate());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.getDate());
    }

    @Override
    public String toString() {
        return toExpression();
    }
}
