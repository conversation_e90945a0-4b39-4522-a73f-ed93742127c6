package cn.taihealth.ih.service.database;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.hospital.HisBill;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.WechatOrderRepository;
import cn.taihealth.ih.repo.hospital.HisBillRepository;
import com.gitq.jedi.context.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class Migrator98 {


    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Async
    public void run() {
        // 根据ih_his_bill中的transaction_id和ih_wechat_orders中的transaction_id关联，查出ih_wechat_orders中的hospital_id，然后更新ih_his_bill中的hospital_id
        log.info("开始将今天之前所有ih_his_bill中hospital_id维护, 开始关联ih_wechat_orders表");
        WechatOrderRepository wechatOrderRepository = AppContext.getInstance(WechatOrderRepository.class);
        HisBillRepository hisBillRepository = AppContext.getInstance(HisBillRepository.class);
        HospitalRepository hospitalRepository = AppContext.getInstance(HospitalRepository.class);

        // 缓存医院信息，避免重复查询数据库
        Map<Long, Hospital> hospitalCache = new HashMap<>();

        // 每页大小
        int pageSize = 1000;

        // 查询第一页数据
        Pageable pageable = PageRequest.of(0, pageSize, Direction.ASC, "id");
        Page<HisBill> page = hisBillRepository.findAll(pageable);

        int totalUpdated = 0;

        // 遍历每一页数据
        while (!page.isEmpty()) {
            List<HisBill> hisBills = page.getContent();

            int updatedInBatch = 0;

            for (HisBill hisBill : hisBills) {
                if (hisBill.getTransaction_id() != null && hisBill.getHospital() ==null) {
                    // 根据 ih_his_bill 中的 transaction_id 查找对应的 ih_wechat_orders
                    Optional<WechatOrder> optionalWechatOrder = wechatOrderRepository.findByTransactionId(hisBill.getTransaction_id());
                    if (optionalWechatOrder.isPresent()) {
                        WechatOrder wechatOrder = optionalWechatOrder.get();
                        Long hospitalId = wechatOrder.getHospitalId();

                        // 从缓存中获取医院信息，如果缓存中不存在，则从数据库中查询并加入缓存
                        Hospital hospital = hospitalCache.computeIfAbsent(hospitalId, id -> hospitalRepository.findById(id).orElse(null));
                        if (hospital == null) {
                            log.info("wechatOrder中的hospital_id对应的医院不存在, wechatOrder:{}", wechatOrder);
                            continue;
                        }

                        // 更新 ih_his_bill 中的 hospital_id
                        hisBill.setHospital(hospital);
                        hisBillRepository.save(hisBill);

                        updatedInBatch++;
                        totalUpdated++;
                    } else {
                        log.info("his WechatOrder not found: " + hisBill.getId());
                    }
                }
            }

            log.info("本次更新{}, 已更新his_bill{}条数据", updatedInBatch, totalUpdated);

            // 查询下一页数据
            pageable = pageable.next();
            page = hisBillRepository.findAll(pageable);
        }

        log.info("已更新his_bill总数:{}", totalUpdated);
    }

}
