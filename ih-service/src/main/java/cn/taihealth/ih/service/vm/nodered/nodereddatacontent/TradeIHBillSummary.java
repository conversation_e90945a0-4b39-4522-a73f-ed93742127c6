package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 上传交易账单，账单汇总
 */
@Data
public class TradeIHBillSummary implements Serializable {

    // 总交易单数	Y	该份账单内明细数据的笔数
    private String order_count;
    // 账单开始时间 yyyyMMddHHmmss
    private String start_date;
    // 账单结束时间 yyyyMMddHHmmss
    private String end_date;
    // 应结订单总金额	Y	账单内所有应结订单金额字段之和，保留小数点后2位
    private String settlement_order_amount_count;
    // 退款总金额	Y	账单内所有退款金额字段之和，保留小数点后2位
    private String refund_amount_count;
    // 充值券退款总金额	Y	账单内所有充值券退款金额字段之和，保留小数点后2位
    private String recharge_voucher_refund_amount_count;
    // 手续费总金额	Y	账单内所有交易手续费字段之和，保留小数点后2位
    private String handling_fee_count;
    // 订单总金额	Y	账单内所有交易订单金额字段之和，保留小数点后2位
    private String order_amount_count;
    // 申请退款总金额	Y	账单内所有申请退款金额字段之和，保留小数点后2位
    private String refund_application_amount_count;
    // 具体订单
    private List<TradeIHOrderInfo> orders;
}
