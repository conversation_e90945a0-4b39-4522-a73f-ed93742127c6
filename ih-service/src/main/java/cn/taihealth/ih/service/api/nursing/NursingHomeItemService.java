package cn.taihealth.ih.service.api.nursing;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.nursing.NursingHomeItem;
import cn.taihealth.ih.service.vm.nursing.NursingHomeItemVM;

public interface NursingHomeItemService {

    /**
     * 添加
     * @param hospital
     * @param vm
     * @return
     */
    NursingHomeItem add(Hospital hospital, NursingHomeItemVM vm);

    /**
     * 修改护理到家项目
     * @param vm
     * @return
     */
    NursingHomeItem edit(NursingHomeItemVM vm);

    /**
     * 删除护理到家项目
     * @param homeItem
     */
    void delete(NursingHomeItem homeItem);
}
