package cn.taihealth.ih.service.impl.filter.bill;

import cn.taihealth.ih.domain.enums.BillPayStatusEnum;
import cn.taihealth.ih.domain.enums.BillSourceEnum;
import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class BillPayStatusFilter implements SearchFilter<Bill> {
    private final BillPayStatusEnum payStatus;

    public BillPayStatusFilter(BillPayStatusEnum payStatus) {
        this.payStatus = payStatus;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Bill> toSpecification() {
        return Specifications.eq("billPayStatus", payStatus);
    }

    @Override
    public String toExpression() {
        String str = " payStatus:" + payStatus;
        return str;
    }

    @Override
    public boolean isValid() {
        return payStatus != null;
    }
}
