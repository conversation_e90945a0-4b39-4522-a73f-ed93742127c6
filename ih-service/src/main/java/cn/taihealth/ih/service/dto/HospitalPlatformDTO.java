package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel(description = "互联网医院绑定的公众平台")
@Data
@NoArgsConstructor
public class HospitalPlatformDTO extends HospitalPublicPlatformDTO {

    @ApiModelProperty(value = "小程序类型", example = "小程序:MINI,公众号:OFFICIAL_ACCOUNT")
    private PlatformTypeEnum platformType;

    @ApiModelProperty(value = "医生端||患者端", example = "医生端:DOCTOR,患者端:PATIENT")
    private HospitalPublicPlatform.PlatformForEnum platformFor;


    public HospitalPlatformDTO(HospitalPublicPlatform platform) {
        super(platform);
        platformType = platform.getPlatformType();
        platformFor = platform.getPlatformFor();
    }
}
