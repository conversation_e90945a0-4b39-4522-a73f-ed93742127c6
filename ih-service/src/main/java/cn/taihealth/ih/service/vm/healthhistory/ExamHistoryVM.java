package cn.taihealth.ih.service.vm.healthhistory;

import cn.taihealth.ih.domain.hospital.Checks;
import cn.taihealth.ih.domain.hospital.ExamOrder;
import cn.taihealth.ih.domain.hospital.ExamOrderReport;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.compress.utils.Lists;

public class ExamHistoryVM {

    private Long id;

    @ApiModelProperty("医生名称")
    private String doctorName;

    @ApiModelProperty("科室名称")
    private String deptName;

    @ApiModelProperty("医院名称")
    private String hospitalName;

    @ApiModelProperty("检查时间")
    private Date time;

    @ApiModelProperty("检查项目名称")
    private String examItemName;

    @ApiModelProperty("检查结果")
    private List<String> results = Lists.newArrayList();

    public ExamHistoryVM() {
    }

    public ExamHistoryVM(Checks check) {
        ExamOrder examOrder = check.getExamOrder();
        this.id = examOrder.getId();
        this.hospitalName = examOrder.getHospital().getName();
        this.time = examOrder.getService().getStartTime();
        this.examItemName = examOrder.getExamItem().getName();
//        this.results = examOrder.getReports().stream().map(ExamOrderReport::getDiagnosisResults)
//            .collect(Collectors.toList());
//        this.doctorName = examOrder.getExecuteDoctorName();
        this.deptName = examOrder.getExamItem().getOfflineDept().getDeptName();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getExamItemName() {
        return examItemName;
    }

    public void setExamItemName(String examItemName) {
        this.examItemName = examItemName;
    }

    public List<String> getResults() {
        return results;
    }

    public void setResults(List<String> results) {
        this.results = results;
    }
}
