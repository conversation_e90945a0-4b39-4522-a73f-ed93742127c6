package cn.taihealth.ih.service.impl.guidance;

import cn.taihealth.ih.domain.GuidanceRecord;
import cn.taihealth.ih.domain.enums.GuidenceQuestionTypeEnum;
import cn.taihealth.ih.repo.GuidanceLogRepo;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import cn.taihealth.ih.service.api.GuidanceLogService;
/**
 * <AUTHOR>
 * @Date 2020/10/28
 */
@Service
@Slf4j
public class GuidanceLogServiceImpl implements GuidanceLogService {

  @Resource
  GuidanceLogRepo guidanceLogRepo;

  @Override
  @Async
  public void generateLog(List<GuidanceRecord> guidanceRecordList) {
    if(CollectionUtils.isEmpty(guidanceRecordList)){
      return ;
    }

    for(GuidanceRecord guidanceRecord: guidanceRecordList){
      if(guidanceRecord.getQuestionType() != null && (guidanceRecord.getQuestionType().getId() ==
          GuidenceQuestionTypeEnum.EMERGENCY_WAITING.getId()
      || guidanceRecord.getQuestionType().getId() == GuidenceQuestionTypeEnum.REVISIT_WAITING.getId() )){
        log.info("候诊的记录不计入导诊统计");
        return;
      }
      if(guidanceRecord.getQuestionType() != null && (guidanceRecord.getQuestionType().getId() ==
          GuidenceQuestionTypeEnum.EMERGENCY.getId())){
           handlingEmergencyRecords(guidanceRecordList);
           break;
      }
      if(guidanceRecord.getQuestionType() != null && (guidanceRecord.getQuestionType().getId() ==
          GuidenceQuestionTypeEnum.REVISIT.getId())){
         handlingRevisitRecords(guidanceRecordList);
         break;
      }
    }
    guidanceRecordList.get(0).getQuestionType();
  }

  private void handlingRevisitRecords(List<GuidanceRecord> guidanceRecordList) {

  }

  private void handlingEmergencyRecords(List<GuidanceRecord> guidanceRecordList) {

  }
}
