package cn.taihealth.ih.service.impl.filter.billNew;

import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.hospital.BillNew;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class BillServiceTypeFilter implements SearchFilter<BillNew> {
    private final ProjectTypeEnum projectTypeEnum;

    public BillServiceTypeFilter(ProjectTypeEnum projectTypeEnum) {
        this.projectTypeEnum = projectTypeEnum;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<BillNew> toSpecification() {
        return Specifications.eq("billServiceType", projectTypeEnum);
    }

    @Override
    public String toExpression() {
        String str = " billServiceType:" + projectTypeEnum;
        return str;
    }

    @Override
    public boolean isValid() {
        return projectTypeEnum != null;
    }
}
