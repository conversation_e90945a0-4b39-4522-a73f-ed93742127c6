package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.MessageTemplate;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.MessageTemplateRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.MessageTemplateService;
import cn.taihealth.ih.service.dto.MessageTemplateDTO;
import cn.taihealth.ih.service.error.UserNotFoundException;
import cn.taihealth.ih.service.impl.filter.message.MessageTemplateSearch;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: hyp
 * @Date: 2021/2/26 上午9:52
 */
@Service
@RequiredArgsConstructor
public class MessageTemplateServiceImpl implements MessageTemplateService {

    private final MessageTemplateRepository messageTemplateRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;

    @Override
    public MessageTemplateDTO addTemplate(MessageTemplateDTO dto, Hospital hospital, MedicalWorker medicalWorker) {
        MessageTemplate template;
        if (dto.isNew()) {
            template = new MessageTemplate();
            template.setCreatedUser(CurrentUser.getOrNull());
        } else {
            template = messageTemplateRepository.getById(dto.getId());
            if (Objects.isNull(template) || !Objects.equals(template.getDoctor(), medicalWorker)) {
                throw ErrorType.MESSAGE_TEMPLATE_NOT_EXIST.toProblem("消息不存在");
            }
        }

        template.setHospital(hospital);
        template.setOrderNumber(dto.getOrderNumber());
        template.setContent(dto.getContent());
        template.setDoctor(medicalWorker);
        template.setUpdatedUser(CurrentUser.getOrNull());

        messageTemplateRepository.save(template);
        return new MessageTemplateDTO(template);
    }

    @Override
    public MessageTemplateDTO getTemplateById(Long templateId) {
        return new MessageTemplateDTO(messageTemplateRepository.getById(templateId));
    }

    @Override
    public void removeTemplate(Long templateId) {
        messageTemplateRepository.deleteById(templateId);
    }

    @Override
    public Page<MessageTemplateDTO> getTemplateList(String query, Pageable request, Hospital hospital,
                                                    MedicalWorker medicalWorker) {
        List<Specification<MessageTemplate>> specs = new LinkedList<>();
        MessageTemplateSearch search = MessageTemplateSearch.of(query);
        specs.add(search.toSpecification());

        if (Objects.nonNull(hospital)) {
            specs.add(Specifications.eq("hospital", hospital));
        } else {
            specs.add(Specifications.isNull("hospital"));
        }
        if (Objects.nonNull(medicalWorker)) {
            specs.add(Specifications.eq("doctor", medicalWorker));
        } else {
            specs.add(Specifications.isNull("doctor"));
        }
        specs.add(Specifications.orderBy(Sort.Order.asc("orderNumber")));
        return messageTemplateRepository.findAll(Specifications.and(specs), request).map(MessageTemplateDTO::new);
    }

    @Override
    public List<MessageTemplateDTO> getHospitalTemplateList(Hospital hospital) {
        return this.toMessageTemplateDtoList(this.messageTemplateRepository.findAllByHospitalAndDoctorIsNull(hospital));
    }

    @Override
    public List<MessageTemplateDTO> getDoctorTemplateList() {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        MedicalWorker medicalWorker =
                medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow(UserNotFoundException::new);
        return this.toMessageTemplateDtoList(this.messageTemplateRepository.findAllByDoctor(medicalWorker));
    }

    private List<MessageTemplateDTO> toMessageTemplateDtoList(List<MessageTemplate> templateList) {
        List<MessageTemplateDTO> dtoList = Lists.newArrayList();
        if (ObjectUtils.isEmpty(templateList)) {
            return dtoList;
        }

        templateList.forEach(template -> {
            dtoList.add(new MessageTemplateDTO(template));
        });
        return dtoList;
    }

}
