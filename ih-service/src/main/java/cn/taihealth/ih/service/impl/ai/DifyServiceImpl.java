package cn.taihealth.ih.service.impl.ai;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.service.api.ai.DifyService;
import cn.taihealth.ih.service.dto.ai.*;
import cn.taihealth.ih.service.dto.ai.dify.*;
import cn.taihealth.ih.service.impl.ai.dify.DifyClient;
import cn.taihealth.ih.service.impl.ai.dify.DifyClientFactory;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

@Service
@AllArgsConstructor
public class DifyServiceImpl implements DifyService {

    private final DifyClientFactory difyClientFactory;

    @Override
    public SiteResponse site(Hospital hospital) {
        String appid = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SMART_MEDICAL_CONSULT_APP_ID);
        String title = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SMART_MEDICAL_CONSULT_APP_NAME);
        String iconUrl = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SMART_MEDICAL_CONSULT_APP_ICON);

        SiteResponse siteResponse = new SiteResponse();
        siteResponse.setAppId(appid);
        siteResponse.setCanReplaceLogo(false);
        siteResponse.setCustomConfig(null);
        siteResponse.setEnableSite(true);
        siteResponse.setEndUserId("");
        siteResponse.setModelConfig(null);
        siteResponse.setPlan("basic");

        SiteResponse.Site site = new SiteResponse.Site();
        site.setTitle(title);
        site.setChatColorTheme(null);
        site.setChatColorThemeInverted(false);
        site.setIconType("image");
        site.setIcon("02f744a3-d7d7-426b-9ebc-f27a3b6f39d9");
        site.setIconBackground("#D5D9EB");
        site.setIconUrl(iconUrl);
        site.setCopyright("");
        site.setCustomDisclaimer("");
        site.setDefaultLanguage("zh-Hans");
        site.setDescription("");
        site.setPromptPublic(false);
        site.setShowWorkflowSteps(false);
        site.setUseIconAsAnswerIcon(true);

        siteResponse.setSite(site);

        return siteResponse;
    }

    @Override
    public ParametersResponse parameters(Hospital hospital) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.parameters();
    }

    @Override
    public MetaResponse meta(Hospital hospital) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.meta();
    }

    @Override
    public UploadResponse upload(String user, Hospital hospital, MultipartFile file) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.uploadFile(file, user);
    }

    @Override
    public SseEmitter chatStreaming(User user, Hospital hospital, MessageRequest message) {
        DifyClient difyClient = createDifyClient(hospital);
        message.setUser(user.getId() + "");
        message.setResponseMode("streaming");
        message.setAutoGenerateName(true);
        return difyClient.sendMessageStreaming(message);
    }

    @Override
    public StopResponse stop(String user, Hospital hospital, String taskId) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.stop(user, taskId);
    }

    @Override
    public MessageResponse chat(User user, Hospital hospital, MessageRequest message) {
        DifyClient difyClient = createDifyClient(hospital);
        message.setUser(user.getId() + "");
        message.setResponseMode("blocking");
        return difyClient.sendMessageBlocking(message);
    }

    @Override
    public FeedbackResponse feedbacks(String user, Hospital hospital, String messageId, FeedbackRequest feedback) {
        DifyClient difyClient = createDifyClient(hospital);
        feedback.setUser(user);
        return difyClient.feedbacks(messageId, feedback);
    }

    @Override
    public SuggestedResponse suggested(String user, Hospital hospital, String messageId) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.suggested(user, messageId);
    }

    @Override
    public HistoryMessages messages(String user, Hospital hospital, String conversationId, String limit, String firstId) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.messages(user, conversationId, limit, firstId);
    }

    @Override
    public HistoryConversations conversations(String user, Hospital hospital, String lastId, String limit, String pinned, String sortBy) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.conversations(user, lastId, limit, pinned, sortBy);
    }

    @Override
    public String renameConversations(User user, Hospital hospital, String conversationId, RenameRequest renameRequest) {
        DifyClient difyClient = createDifyClient(hospital);
        if (StringUtils.isEmpty(renameRequest.getUser())) {
            renameRequest.setUser(user.getId() + "");
        }
        return difyClient.renameConversations(conversationId, renameRequest);
    }

    @Override
    public String deleteConversations(User user, Hospital hospital, String conversationId) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.deleteConversations(user.getId() + "", conversationId);
    }

    @Override
    public VariablesResponse variables(Hospital hospital, String conversationId) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.variables(conversationId);
    }

    @Override
    public AudioToTextResponse audioToText(User user, Hospital hospital, MultipartFile file) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.audioToText(user.getId() + "", file);
    }

    @Override
    public byte[] textToAudio(User user, Hospital hospital, TextToAudioRequest textToAudioRequest) {
        DifyClient difyClient = createDifyClient(hospital);
        return difyClient.textToAudio(textToAudioRequest);
    }

    private DifyClient createDifyClient(Hospital hospital) {
        String apiKey = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SMART_MEDICAL_CONSULT_API_KEY);
        return difyClientFactory.createClient(apiKey);
    }
}
