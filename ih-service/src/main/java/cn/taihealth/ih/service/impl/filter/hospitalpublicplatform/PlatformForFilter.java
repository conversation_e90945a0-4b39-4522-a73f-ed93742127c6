package cn.taihealth.ih.service.impl.filter.hospitalpublicplatform;

import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;


public class PlatformForFilter implements SearchFilter<HospitalPublicPlatform> {

    private final HospitalPublicPlatform.PlatformForEnum name;

    public PlatformForFilter(HospitalPublicPlatform.PlatformForEnum name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<HospitalPublicPlatform> toSpecification() {
        return Specifications.eq("platformFor", name);
    }

    @Override
    public String toExpression() {
        String str = " platformFor:" + name;
        return str;
//        return not ? "-" + str : str;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof PlatformForFilter)) {
            return false;
        }

        PlatformForFilter rhs = (PlatformForFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
