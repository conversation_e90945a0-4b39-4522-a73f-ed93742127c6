package cn.taihealth.ih.service.impl.filter.bill;

import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class SolveFlagFilter implements SearchFilter<Bill> {
    private final Boolean value;

    public SolveFlagFilter(Boolean value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    // solveFlag
    @Override
    public Specification<Bill> toSpecification() {
        if (value) {
            return Specifications.isTrue("solveFlag");
        } else {
            return Specifications.isFalse("solveFlag");
        }
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
