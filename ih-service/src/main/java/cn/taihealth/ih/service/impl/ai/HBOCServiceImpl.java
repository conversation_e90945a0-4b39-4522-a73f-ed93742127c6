package cn.taihealth.ih.service.impl.ai;

import cn.hutool.json.JSONArray;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.Snowflake64;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ai.CaseReportForm;
import cn.taihealth.ih.domain.ai.Pedigree;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.crm.CrmTaskDetailResult;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.UploadType;
import cn.taihealth.ih.repo.ai.CaseReportFormRepository;
import cn.taihealth.ih.repo.ai.PedigreeRepository;
import cn.taihealth.ih.repo.crm.CrmTaskDetailResultRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.ai.HBOCService;
import cn.taihealth.ih.service.api.crm.CrmService;
import cn.taihealth.ih.service.dto.ai.*;
import cn.taihealth.ih.service.dto.ai.qwen.ChatResponse;
import cn.taihealth.ih.service.dto.ai.qwen.PedigreeData;
import cn.taihealth.ih.service.impl.ai.qwen.QwenClient;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.crm.CrmQuestionAnswerVM;
import cn.taihealth.ih.service.vm.crm.CrmQuestionOptionVM;
import cn.taihealth.ih.service.vm.crm.CrmQuestionnaireVM;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.common.web.UrlUtils;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class HBOCServiceImpl implements HBOCService {

    private QwenClient qwenClient;

    private final LockService lockService;

    private PedigreeRepository pedigreeRepository;

    private CaseReportFormRepository caseReportFormRepository;

    private final CrmService crmService;

    private CrmTaskDetailResultRepository crmTaskDetailResultRepository;

    @Override
    public void savePedigree(User user, Hospital hospital, String conversationId, List<PedigreeDTO> pedigreeList) {

        if (!validateDifyConfig(hospital)) {
            log.warn("未配置Dify API KEY, 无效请求");
            return;
        }
        
        // 家系图批次号
        Long batchId = Snowflake64.Holder.INSTANCE.nextId();

        List<Pedigree> pedigrees = new ArrayList<>();

        List<PedigreeDTO> datas = new ArrayList<>();
        datas.addAll(pedigreeList);

        // 对有子女无配偶的对象生成一个默认的配偶
        for (PedigreeDTO pedigreeDTO : pedigreeList) {
            List<Integer> childrenIds = pedigreeDTO.getChildrenIds();
            Integer spouseId = pedigreeDTO.getSpouseId();
            if (CollectionUtils.isNotEmpty(childrenIds) && spouseId == null) {
                int initSpouseId = datas.size() + 1;
                pedigreeDTO.setSpouseId(initSpouseId);
                PedigreeDTO unknowPedigree = initUnknowPedigreeDTO(initSpouseId, pedigreeDTO.getId(), childrenIds, "男".equals(pedigreeDTO.getGender()) ? "女" : "男");
                datas.add(unknowPedigree);
                // 为子女的ParentIds赋值
                datas.forEach(member -> {
                    if (childrenIds.contains(member.getId())){
                        member.setParentIds(Arrays.asList(pedigreeDTO.getId(), initSpouseId));
                    }
                });
            }
        }
        // 判断用户的祖父母/外祖父母的数据是否完整
        PedigreeDTO proband = datas.stream().filter(PedigreeDTO::isProband).collect(Collectors.toList()).get(0);
        // 先证者的父母
        List<Integer> parentIds = proband.getParentIds();
        for (Integer parentId : parentIds) {
            PedigreeDTO parent = datas.stream().filter(p -> p.getId() == parentId).collect(Collectors.toList()).get(0);
            // 先证者父母的兄弟姐妹
            List<Integer> siblingsIds = parent.getSiblingsIds();
            // 先证者的祖父母/外祖父母
            List<Integer> grandparentIds = parent.getParentIds();
            // 先证者的祖父母/外祖父母的子女
            List<Integer> childrenIds = new ArrayList<>();
            childrenIds.add(parentId);
            childrenIds.addAll(siblingsIds);
            if (!siblingsIds.isEmpty() && grandparentIds.isEmpty()) {
                int grandfatherId = datas.size() + 1;
                int grandmotherId = datas.size() + 2;
                // 创建祖父/外祖父
                PedigreeDTO grandfather = initUnknowPedigreeDTO(grandfatherId, grandmotherId, childrenIds, "男");
                // 创建祖母/外祖母
                PedigreeDTO grandmother = initUnknowPedigreeDTO(grandmotherId, grandfatherId, childrenIds, "女");
                datas.add(grandfather);
                datas.add(grandmother);
                // 为父母及他的兄弟姐妹设置parents_ids
                datas.forEach(member -> {
                    if (childrenIds.contains(member.getId())){
                        member.setParentIds(Arrays.asList(grandfatherId, grandmotherId));
                    }
                });
            }
        }

        for (PedigreeDTO pedigreeDTO : datas) {
            Pedigree pedigree = new Pedigree();
            convertPedigree(pedigree, user, hospital, conversationId, batchId, pedigreeDTO);

            pedigrees.add(pedigree);
        }

        pedigreeRepository.saveAll(pedigrees);
        log.info("家系图保存成功：User-{}，会话ID-{}", user.getId(), conversationId);
    }

    @Async
    @Override
    public void savePedigreeContext(User user, Hospital hospital, String conversationId, PedigreeContext pedigreeContext) {

        if (!validateDifyConfig(hospital)) {
            log.warn("未配置Dify API KEY, 无效请求");
            return;
        }

        String apiKey = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.QWEN_API_KEY);

        lockService.executeAutomaticRenewal(
                getClass().getName() + user.getId() + conversationId, () -> {
                    try {
                        String context = pedigreeContext.getContext();
                        Map<String, Object> payload = getResponseFormatPayload(context);
                        ChatResponse response = qwenClient.chatCompletions(apiKey, payload);
                        String pedigreeDTOListStr = response.getChoices().get(0).getMessage().getContent();
                        List<PedigreeData> pedigreeDataList = StandardObjectMapper.readValue(pedigreeDTOListStr, new TypeReference<>() {
                        });
                        List<PedigreeDTO> pedigreeDTOList = pedigreeDataList.stream().map(data -> convertPedigreeDTO(data)).collect(Collectors.toList());
                        savePedigree(user, hospital, conversationId, pedigreeDTOList);
                    } catch (Exception e) {
                        log.error("保存家系图（原始上下文数据）失败：", e);
                    }
                    return 0;
                });
    }

    private static Map<String, Object> getResponseFormatPayload(String context) {
        String prompt = "你需要根据用户提供的markdown格式的文本，提取每个家庭成员的健康信息及亲属关系，并生成JSONArray格式的JSON数据。每个成员应具备以下字段信息:\n" +
                        "成员字段：\n" +
                        " - id: 整数类型，每个成员的唯一标识（从1开始递增）。\n" +
                        " - proband: 布尔类型，是否为先证者。\n" +
                        " - gender: 字符串类型，值为“男”或“女”。\n" +
                        " - cancer: 字符串类型，填写具体肿瘤名称或为null（无肿瘤）。\n" +
                        " - age_at_diagnosis: 整数类型或null，表示患肿瘤时的年龄。\n" +
                        " - deceased: 布尔类型，表示是否去世。\n" +
                        " - death_age_at: 整数类型或null，去世年龄。\n" +
                        " - death_reason: 字符串类型或null，去世原因。\n" +
                        " - current_age: 整数类型或null，如果健在，填写当前年龄。\n" +
                        " - spouse_id: 整数或null，配偶的id。\n" +
                        " - children_ids: 整数数组类型，子女的id数组。\n" +
                        " - parent_ids: 整数数组类型，父母的id数组。\n" +
                        " - siblings_ids: 整数数组类型，兄弟姐妹的id数组。\n" +
                        "注意识别成员的亲缘关系，例如：:\n" +
                        " - 所有人中，有且只有一个人的is_proband为true，一般是id为1的人\n" +
                        " - 一个人的父母双方，必然互为配偶关系，且父母两人的children_ids必然一致\n" +
                        " - 一个人的兄弟姐妹，必然也是这个人父母的子女\n" +
                        " - 两人为配偶关系，spouse_id应该互为对方的id\n" +
                        " - 两人为父子或母子关系，孩子的parent_ids应该包含父母的id，父母的children_ids应该包含孩子的id\n" +
                        " - 多人互为兄弟姐妹，每个人的siblings_ids应该包含除自己以外所有兄弟姐妹的id，每个人的parent_ids必然一致";

        // 构造请求的消息内容
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", prompt);

        Map<String, String> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", context);

        ObjectMapper objectMapper = new ObjectMapper();
        ArrayNode messages = objectMapper.createArrayNode();
        messages.add(objectMapper.valueToTree(systemMessage));
        messages.add(objectMapper.valueToTree(userMessage));

        Map<String, Object> payload = new HashMap<>();
        payload.put("model", "qwen-max");
        payload.put("messages", messages);
        payload.put("temperature", 0.1);

        Map<String, String> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");
        payload.put("response_format", responseFormat);
        return payload;
    }

    private PedigreeDTO convertPedigreeDTO(PedigreeData pedigreeData) {
        PedigreeDTO pedigreeDTO = new PedigreeDTO();
        pedigreeDTO.setId(pedigreeData.getId());
        pedigreeDTO.setProband(pedigreeData.isProband());
        pedigreeDTO.setGender(pedigreeData.getGender());
        pedigreeDTO.setCancer(pedigreeData.getCancer());
        pedigreeDTO.setAgeAtDiagnosis(pedigreeData.getAge_at_diagnosis());
        pedigreeDTO.setDeceased(pedigreeData.isDeceased());
        pedigreeDTO.setDeathAgeAt(pedigreeData.getDeath_age_at());
        pedigreeDTO.setCurrentAge(pedigreeData.getCurrent_age());
        pedigreeDTO.setSpouseId(pedigreeData.getSpouse_id());
        pedigreeDTO.setChildrenIds(pedigreeData.getChildren_ids());
        pedigreeDTO.setParentIds(pedigreeData.getParent_ids());
        pedigreeDTO.setSiblingsIds(pedigreeData.getSiblings_ids());
        return pedigreeDTO;
    }

    @Override
    public List<PedigreeDTO> listPedigree(User current, Hospital hospital) {

        if (!validateDifyConfig(hospital)) {
            log.warn("未配置Dify API KEY, 无效请求");
            return null;
        }

        List<Specification<Pedigree>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("userId", current.getId()));

        List<Pedigree> pedigrees = pedigreeRepository.findAll(Specifications.and(specs));
        // 根据批次分组，取最新的家系图
        Map<Long, List<Pedigree>> groupedByBatchId = pedigrees.stream()
                .collect(Collectors.groupingBy(Pedigree::getBatchId));
        Optional<Map.Entry<Long, List<Pedigree>>> latestBatchEntry = groupedByBatchId.entrySet().stream()
                .max(Comparator.comparing(entry -> entry.getValue().stream()
                        .map(Pedigree::getCreatedDate)
                        .max(Comparator.naturalOrder())
                        .orElse(new Date(0))));

        // 返回最新 batchId 的数据
        return latestBatchEntry
                .map(Map.Entry::getValue)
                .orElse(Collections.emptyList())
                .stream()
                .map(item -> convertPedigreeDTO(item))
                .collect(Collectors.toList());
    }

    private void convertPedigree(Pedigree pedigree, User user, Hospital hospital, String conversationId, Long batchId, PedigreeDTO pedigreeDTO) {
        pedigree.setHospitalId(hospital.getId());
        pedigree.setUserId(user.getId());
        pedigree.setConversationId(conversationId);
        pedigree.setBatchId(batchId);
        pedigree.setMemberId(pedigreeDTO.getId());
        pedigree.setProband(pedigreeDTO.isProband());
        pedigree.setGender("男".equals(pedigreeDTO.getGender()) ? Gender.MALE : Gender.FEMALE);
        pedigree.setCancer(pedigreeDTO.getCancer());
        pedigree.setAgeAtDiagnosis(pedigreeDTO.getAgeAtDiagnosis());
        pedigree.setDeceased(pedigreeDTO.isDeceased());
        pedigree.setDeathAgeAt(pedigreeDTO.getDeathAgeAt());
        pedigree.setCurrentAge(pedigreeDTO.getCurrentAge());
        pedigree.setSpouseId(pedigreeDTO.getSpouseId());
        // 转换为字符串存储
        pedigree.setChildrenIds(toJsonString(pedigreeDTO.getChildrenIds()));
        pedigree.setParentIds(toJsonString(pedigreeDTO.getParentIds()));
        pedigree.setSiblingsIds(toJsonString(pedigreeDTO.getSiblingsIds()));
    }

    private PedigreeDTO initUnknowPedigreeDTO(Integer id, Integer spouseId, List<Integer> childredIds, String gender) {
        PedigreeDTO pedigreeDTO = new PedigreeDTO();
        pedigreeDTO.setId(id);
        pedigreeDTO.setProband(false);
        pedigreeDTO.setGender(gender);
        pedigreeDTO.setCancer(null);
        pedigreeDTO.setAgeAtDiagnosis(null);
        pedigreeDTO.setDeceased(false);
        pedigreeDTO.setDeathAgeAt(null);
        pedigreeDTO.setCurrentAge(null);
        pedigreeDTO.setSpouseId(spouseId);
        pedigreeDTO.setChildrenIds(childredIds);
        pedigreeDTO.setParentIds(new ArrayList<>());
        pedigreeDTO.setSiblingsIds(new ArrayList<>());
        return pedigreeDTO;
    }

    private String toJsonString(List<Integer> ids) {
        return new JSONArray(ids).toString();
    }

    private PedigreeDTO convertPedigreeDTO(Pedigree pedigree) {
        PedigreeDTO pedigreeDTO = new PedigreeDTO();
        pedigreeDTO.setId(pedigree.getMemberId());
        pedigreeDTO.setProband(pedigree.isProband());
        pedigreeDTO.setGender(pedigree.getGender().getValue());
        pedigreeDTO.setCancer(pedigree.getCancer());
        pedigreeDTO.setAgeAtDiagnosis(pedigree.getAgeAtDiagnosis());
        pedigreeDTO.setDeceased(pedigree.isDeceased());
        pedigreeDTO.setDeathAgeAt(pedigree.getDeathAgeAt());
        pedigreeDTO.setCurrentAge(pedigree.getCurrentAge());
        pedigreeDTO.setSpouseId(pedigree.getSpouseId());

        pedigreeDTO.setChildrenIds(toIntegerList(pedigree.getChildrenIds()));
        pedigreeDTO.setParentIds(toIntegerList(pedigree.getParentIds()));
        pedigreeDTO.setSiblingsIds(toIntegerList(pedigree.getSiblingsIds()));

        pedigreeDTO.setCreateTime(pedigree.getCreatedDate());
        return pedigreeDTO;
    }

    private List<Integer> toIntegerList(String ids) {
        JSONArray jsonArray = new JSONArray(ids);
        return jsonArray.toList(Integer.class);
    }

    //------------------------------------------ CRF表单 ---------------------------------------------------
    @Override
    public void saveCaseReportForm(User user, Hospital hospital, String conversationId, CaseReportFormDTO caseReportFormDTO) {

        if (!validateDifyConfig(hospital)) {
            log.warn("未配置Dify API KEY, 无效请求");
            return;
        }

        // 组装CRF实体
        CaseReportForm caseReportForm = convertCaseReportForm(user, hospital, conversationId, caseReportFormDTO);
        // 一次会话只保存一条
        List<Specification<CaseReportForm>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospitalId", hospital.getId()));
        specs.add(Specifications.eq("conversationId", conversationId));
        List<CaseReportForm> crfByConversationId = caseReportFormRepository.findAll(Specifications.and(specs));
        if (CollectionUtils.isNotEmpty(crfByConversationId)) {
            // 说明之前该会话保存过CRF表单，此次只需要更新即可
            Long historyId = crfByConversationId.get(0).getId();
            caseReportForm.setId(historyId);
        }
        // 新增或更新
        caseReportFormRepository.save(caseReportForm);
    }

    @Override
    public List<CaseReportFormVM> listCaseReportForm(Hospital hospital, String type) {

        if (!validateDifyConfig(hospital)) {
            log.warn("未配置Dify API KEY, 无效请求");
            return null;
        }

        List<Specification<CaseReportForm>> specs = List.of(
                Specifications.eq("hospitalId", hospital.getId())
        );

        return caseReportFormRepository.findAll(Specifications.and(specs)).stream()
                .map(caseReportForm -> {
                    Long userId = caseReportForm.getUserId();
                    // 查询随访记录
                    List<Specification<CrmTaskDetailResult>> crmTaskDetailResultSpecs = List.of(
                            Specifications.eq("hospital", hospital),
                            Specifications.eq("answerer", userId),
                            Specifications.orderBy(Sort.Order.asc("resultTime"))
                    );

                    List<CrmQuestionnaireVM> questionnaireVMList = crmTaskDetailResultRepository.findAll(Specifications.and(crmTaskDetailResultSpecs)).stream()
                            .map(crmTaskDetailResult -> crmTaskDetailResultRepository.findById(crmTaskDetailResult.getId()).orElseThrow())
                            .map(crmService::getAnswerQuestion)
                            .collect(Collectors.toList());

                    CaseReportFormVM caseReportFormVM = new CaseReportFormVM(caseReportForm);
                    caseReportFormVM.setQuestionnaireVMList(questionnaireVMList);

                    return caseReportFormVM;
                })
                .collect(Collectors.toList());
    }

    @Override
    public UploadVM exportCaseReportForm(Hospital hospital) {
        if (!validateDifyConfig(hospital)) {
            log.warn("未配置Dify API KEY, 无效请求");
            return null;
        }

        List<Specification<CaseReportForm>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospitalId", hospital.getId()));
        List<CaseReportFormVM> caseReportFormVMList = listCaseReportForm(hospital, "");

        String fileName = "遗传性妇科肿瘤咨询者信息收集表单.xlsx";
        File file = new File(UrlUtils.concatSegments(AppContext.getInstance(ApplicationProperties.class).getHome(),
                "temp", fileName));
        ExcelWriter writer = null;
        try (OutputStream out = new FileOutputStream(file)) {
            String sheetName = "咨询者";
            if (writer == null) {
                writer = ExcelUtil.getWriterWithSheet(sheetName);
            }
            writer.setSheet(sheetName);

            // 最大随访次数，用于补充第一行
            List<Map<String, Object>> datas = caseReportFormVMList.stream()
                    .map(vm -> createRowData(vm))
                    .collect(Collectors.toList());
            // 补充第一行空白的随访列
            formatFirstRow(datas);

            writer.write(datas, true);

            if (writer != null) {
                writer.flush(out, true);
            }
            Upload upload = AppContext.getInstance(UploadService.class).upload(AppContext.getInstance(UserService.class).getSystem(),
                        UploadResource.of(file, UploadType.PUBLIC, null));
            return new UploadVM(upload);
        } catch (Exception e) {
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("导出失败:" + e.getMessage());
        } finally {
            file.delete();
        }
    }

    // 创建行数据的方法
    private Map<String, Object> createRowData(CaseReportFormVM vm) {
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("姓名", vm.getName());
        row.put("年龄", vm.getAge());
        row.put("性别", vm.getGender());
        row.put("疾病类型", vm.getDiseaseType());
        row.put("患病年龄", vm.getDiseaseAge());
        row.put("胚系致病基因", vm.getPathogenicGene());
        row.put("存活状态（1-死亡，2-存活）", vm.getSurvivalStatus());
        row.put("手机号", vm.getMobile());
        row.put("身份证号", vm.getIdCardNum());
        row.put("身高(cm)", vm.getHeight());
        row.put("体重(kg)", vm.getWeight());
        row.put("婚姻状况（1=已婚，2=未婚，3=离异）", vm.getMaritalStatus());
        row.put("受教育程度（1=高中及以下，2=大专，3=本科及以上）", vm.getEducationLevel());
        row.put("职业（1=无，2=有，具体名称）", vm.getOccupation());
        row.put("籍贯", vm.getNativePlace());
        row.put("肿瘤家族史（1=无，2=有，具体关系/患病年龄/肿瘤名称", vm.getFamilyCancerHistory());
        row.put("既往手术史（1=无，2=有，具体名称）", vm.getPastSurgeryHistory());
        row.put("初潮年龄", vm.getMenarcheAge());
        row.put("月经周期", vm.getMenstrualCycle());
        row.put("经期时长", vm.getMenstrualDuration());
        row.put("有无痛经（1=无，2=有）", vm.getDysmenorrhea() == null ? "" : vm.getDysmenorrhea());
        row.put("是否绝经（1=否，2=是)", vm.getMenopause() == null ? "" : vm.getMenopause());
        row.put("绝经年龄", vm.getMenopauseAge());
        row.put("生育史（足-早-流-现）", vm.getReproductiveHistory());
        row.put("有无吸烟史（1无，2=有）", vm.getSmokingHistory() == null ? "" : vm.getSmokingHistory());
        row.put("有无酗酒史（1=无，2=有）", vm.getDrinkingHistory() == null  ? "" : vm.getDrinkingHistory());
        row.put("其他妇科疾病史（1=无，2=有，具体名称）", vm.getOtherGynecologicalDiseases());
        row.put("其他慢性病史（1=无，2=有，具体名称）", vm.getOtherChronicDiseases());
        row.put("手术名称", vm.getSurgeryName());
        row.put("手术时间", vm.getSurgeryDate());
        row.put("术后病理", vm.getPostoperativePathology());
        row.put("出院诊断", vm.getDischargeDiagnosis());
        row.put("铂耐药（1=否，2=是）", vm.getPlatinumResistance() == null  ? "" : vm.getPlatinumResistance());
        row.put("PARPI治疗（1=无，2=有）", vm.getParpTreatment() == null  ? "" : vm.getParpTreatment());
        row.put("家系图（1=无，2=有）", vm.getFamilyPedigree() == null  ? "" : vm.getFamilyPedigree());
        row.put("基因检测报告（1=无，2=有）", vm.getGeneTestReport() == null  ? "" : vm.getGeneTestReport());
        row.put("风险结果", vm.getResult());
        row.put("筛查建议", vm.getSuggestion());
        row.put("咨询时间", vm.getCreateTime());

        List<CrmQuestionnaireVM> questionnaireVMList = vm.getQuestionnaireVMList();
        int index = 1;
        for (CrmQuestionnaireVM crmQuestionnaireVM : questionnaireVMList) {
            List<CrmQuestionAnswerVM> questions = crmQuestionnaireVM.getQuestions();
            String createdDate = TimeUtils.dateToString(crmQuestionnaireVM.getCreatedDate(), "yyyy-MM-dd HH:mm:ss");
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("随访时间：%s%n", createdDate));
            sb.append(String.format("患者姓名：%s%n", crmQuestionnaireVM.getPatientName()));
            for (CrmQuestionAnswerVM question : questions) {
                CrmQuestionOptionVM optionVM = question.getOptions().stream()
                        .filter(CrmQuestionOptionVM::getCheck)
                        .findFirst()
                        .orElse(null);

                if (optionVM != null) {
                    String value = StringUtils.defaultIfEmpty(optionVM.getContent(), optionVM.getValue());
                    sb.append(String.format("%s：%s%n", question.getContent(), value));
                }
            }
            row.put(String.format("第%d次随访", index++), sb.toString());
        }

        return row;
    }

    private void formatFirstRow(List<Map<String, Object>> rows) {
        if (rows == null || rows.isEmpty()) {
            return;
        }
        final int FIXED_COLUMNS = 39; // CRF 固定列数

        // 找到最大列数
        int maxSize = FIXED_COLUMNS;
        for (Map<String, Object> row : rows) {
            maxSize = Math.max(maxSize, row.size());
        }

        Map<String, Object> firstRow = rows.get(0);
        int existingColumnCount = firstRow.size(); // 现有列数
        int extraColumns = existingColumnCount - FIXED_COLUMNS; // 已有随访列数
        int missingColumns = maxSize - existingColumnCount; // 需要补充的随访列数

        // 补充缺失的随访列
        for (int i = extraColumns + 1; i <= extraColumns + missingColumns; i++) {
            firstRow.put(String.format("第%d次随访", i), "");
        }
    }

    private CaseReportForm convertCaseReportForm(User user, Hospital hospital, String conversationId, CaseReportFormDTO caseReportFormDTO) {
        CaseReportForm caseReportForm = new CaseReportForm();

        caseReportForm.setUserId(user.getId());
        caseReportForm.setHospitalId(hospital.getId());
        caseReportForm.setConversationId(conversationId);

        // 基本信息
        caseReportForm.setName(caseReportFormDTO.getName());
        caseReportForm.setAge(caseReportFormDTO.getAge());
        caseReportForm.setGender("男".equals(caseReportFormDTO.getGender()) ? Gender.MALE : Gender.FEMALE);
        String mobile = caseReportFormDTO.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            mobile = user.getMobile();
        }
        caseReportForm.setMobile(mobile);
        caseReportForm.setIdCardNum(caseReportFormDTO.getIdCardNum());
        caseReportForm.setHeight(caseReportFormDTO.getHeight());
        caseReportForm.setWeight(caseReportFormDTO.getWeight());

        // 患病信息
        caseReportForm.setCarrier(caseReportFormDTO.isCarrier());
        caseReportForm.setDiseaseType(caseReportFormDTO.getDiseaseType());
        caseReportForm.setDiseaseAge(caseReportFormDTO.getDiseaseAge());
        caseReportForm.setPathogenicGene(caseReportFormDTO.getPathogenicGene());
        caseReportForm.setSurvivalStatus(caseReportFormDTO.getSurvivalStatus());

        // 个人信息
        caseReportForm.setMaritalStatus(caseReportFormDTO.getMaritalStatus());
        caseReportForm.setEducationLevel(caseReportFormDTO.getEducationLevel());
        caseReportForm.setOccupation(caseReportFormDTO.getOccupation());
        caseReportForm.setNativePlace(caseReportFormDTO.getNativePlace());

        // 家族史、病史信息
        caseReportForm.setFamilyCancerHistory(caseReportFormDTO.getFamilyCancerHistory());
        caseReportForm.setPastSurgeryHistory(caseReportFormDTO.getPastSurgeryHistory());
        caseReportForm.setMenarcheAge(caseReportFormDTO.getMenarcheAge());
        caseReportForm.setMenstrualCycle(caseReportFormDTO.getMenstrualCycle());
        caseReportForm.setMenstrualDuration(caseReportFormDTO.getMenstrualDuration());
        caseReportForm.setDysmenorrhea(caseReportFormDTO.getDysmenorrhea());
        caseReportForm.setMenopause(caseReportFormDTO.getMenopause());
        caseReportForm.setMenopauseAge(caseReportFormDTO.getMenopauseAge());
        caseReportForm.setReproductiveHistory(caseReportFormDTO.getReproductiveHistory());

        // 行为习惯
        caseReportForm.setSmokingHistory(caseReportFormDTO.getSmokingHistory());
        caseReportForm.setDrinkingHistory(caseReportFormDTO.getDrinkingHistory());

        // 其他病史信息
        caseReportForm.setOtherGynecologicalDiseases(caseReportFormDTO.getOtherGynecologicalDiseases());
        caseReportForm.setOtherChronicDiseases(caseReportFormDTO.getOtherChronicDiseases());

        // 手术信息
        caseReportForm.setSurgeryName(caseReportFormDTO.getSurgeryName());
        caseReportForm.setSurgeryDate(caseReportFormDTO.getSurgeryDate());
        caseReportForm.setPostoperativePathology(caseReportFormDTO.getPostoperativePathology());
        caseReportForm.setDischargeDiagnosis(caseReportFormDTO.getDischargeDiagnosis());

        // 其他指标
        caseReportForm.setPlatinumResistance(caseReportFormDTO.getPlatinumResistance());
        caseReportForm.setParpTreatment(caseReportFormDTO.getParpTreatment());
        caseReportForm.setFamilyPedigree(caseReportFormDTO.getFamilyPedigree());
        caseReportForm.setGeneTestReport(caseReportFormDTO.getGeneTestReport());

        // 风险结果
        caseReportForm.setResult(caseReportFormDTO.getResult());
        // 筛查建议
        caseReportForm.setSuggestion(caseReportFormDTO.getSuggestion());

        return caseReportForm;
    }

    /**
     * 验证Dify配置是否存在
     * @param hospital 医院
     * @return 是否存在有效配置
     */
    private boolean validateDifyConfig(Hospital hospital) {
        String difyApiKey = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SMART_MEDICAL_CONSULT_API_KEY);
        if (StringUtils.isEmpty(difyApiKey)) {
            log.warn("医院ID: {}，未配置Dify API Key，接口不可用", hospital.getId());
            return false;
        }
        return true;
    }
}
