package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SatisfactionGradeStatisticsDetail {

    @ApiModelProperty("科室名称")
    private String dept_name;

    @ApiModelProperty("门诊调查人数")
    private int op_survey_count;

    @ApiModelProperty("门诊满意数")
    private int op_satisfaction_count;

    @ApiModelProperty("门诊一般数")
    private int op_ordinary_count;

    @ApiModelProperty("门诊不满意数")
    private int op_dissatisfaction_count;

    @ApiModelProperty("门诊满意率")
    private double op_satisfaction_rate;

    @ApiModelProperty("住院调查人数")
    private int ip_survey_count;

    @ApiModelProperty("住院满意数")
    private int ip_satisfaction_count;

    @ApiModelProperty("住院一般数")
    private int ip_ordinary_count;

    @ApiModelProperty("住院不满意数")
    private int ip_dissatisfaction_count;

    @ApiModelProperty("住院满意率")
    private double ip_satisfaction_rate;

}
