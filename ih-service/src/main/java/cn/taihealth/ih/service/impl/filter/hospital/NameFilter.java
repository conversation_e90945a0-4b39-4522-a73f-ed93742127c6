package cn.taihealth.ih.service.impl.filter.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 * <AUTHOR>
 */
public class NameFilter implements SearchFilter<Hospital> {

    private final String pattern;

    public NameFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Hospital> toSpecification() {
        return Specifications.likeIgnoreCase("name", pattern);
    }

    @Override
    public String toExpression() {
        return "name:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof NameFilter)) {
            return false;
        }

        NameFilter rhs = (NameFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
