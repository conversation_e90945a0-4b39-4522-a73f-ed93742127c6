package cn.taihealth.ih.service.vm.offline;


import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.cloud.OfflineHospitalAnnualInspection;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.dto.UploadDTO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

/**
 * 线下医院年检信息表
 */
@Data
@NoArgsConstructor
public class OfflineHospitalAnnualInspectionVM extends AbstractEntityDTO {


    @ApiModelProperty("线下医院id")
    @NotNull
    private AbstractEntityDTO offlineHospital;

    /**
     * 排序序号
     */
    @ApiModelProperty("年份")
    private String year;

    /**
     * 登记时间 说明: 格式 'yyyy-MM-dd' 备注: 例如 2019-12-10
     */
    @ApiModelProperty("登记时间")
    private Date registrationDate;

    /**
     * 上传时间 说明: 格式 'yyyy-MM-dd HH24:mi:ss' 备注: 例如 2019-12-10 16:34:32
     */
    @ApiModelProperty("上传时间")
    private Date uploadedDate;

    /**
     * 来源 说明: 不一致以卫监所信息为准：0：卫监所, 1：医院填报
     */
    @ApiModelProperty("来源 0：卫监所, 1：医院填报")
    private String resource;

    /**
     * 三级等保是否更新 说明: 0：否, 1：是
     */
    @ApiModelProperty("三级等保是否更新 0：否, 1：是")
    private String updateThreelevelEqualprotection;

    /**
     * 派 出（分支）机构数量
     */
    @ApiModelProperty("派 出（分支）机构数量")
    private Integer branchNumber;

    /**
     * 职工总数
     */
    @ApiModelProperty("职工总数")
    private Integer workForce;

    /**
     * 客户服务人数总数
     */
    @ApiModelProperty("客户服务人数总数")
    private Integer serviceCustomerSum;

    /**
     * 日均坐诊医生数
     */
    @ApiModelProperty("日均坐诊医生数")
    private Integer averageDoctorsNumber;

    /**
     * 许可证号码 说明: 需要调取《文件上传及验证接口》，上传年检校验记录PDF 文件。
     */
    @ApiModelProperty("许可证号码")
    private String licenceNumber;

    /**
     * 许可证有效期 说明: 格式 'yyyy-MM-dd' 备注: 例如 2019-12-10
     */
    @ApiModelProperty("许可证有效期")
    private Date licenceExpiration;

    /**
     * 业务用房面积
     */
    @ApiModelProperty("业务用房面积")
    private BigDecimal premisesArea;

    /**
     * 总收入（万元）
     */
    @ApiModelProperty("总收入（万元）")
    private BigDecimal totalIncome;

    /**
     * 总支出（万元）
     */
    @ApiModelProperty("总支出（万元）")
    private BigDecimal totalExpenditure;

    /**
     * 总资产（万元）
     */
    @ApiModelProperty("总资产（万元）")
    private BigDecimal totalAssets;

    /**
     * 流动资产（万元）
     */
    @ApiModelProperty("流动资产（万元）")
    private BigDecimal flowAssets;

    /**
     * 对外资产（万元）
     */
    @ApiModelProperty("对外资产（万元）")
    private BigDecimal externalAssets;

    /**
     * 固定资产（万元）
     */
    @ApiModelProperty("固定资产（万元）")
    private BigDecimal fixedAssets;

    /**
     * 无形资产及开办费（万元）
     */
    @ApiModelProperty("无形资产及开办费（万元）")
    private BigDecimal intangibleAssets;

    /**
     * 负债（万元）
     */
    @ApiModelProperty("负债（万元）")
    private BigDecimal liabilities;

    /**
     * 净资产（万元）
     */
    @ApiModelProperty("净资产（万元")
    private BigDecimal netAssets;

    /**
     * 三级等保编码 说明: 需要调取《文件上传及验证接口》，上传信息安全等级保护证书。
     */
    @ApiModelProperty("三级等保编码")
    private String threelevelEqualprotectionCode;

    @ApiModelProperty("年检证明文件")
    private UploadDTO annualInspectionFile;

    @ApiModelProperty("年检证明文件")
    private UploadDTO threelevelEqualprotectionFile;

    public OfflineHospitalAnnualInspectionVM(OfflineHospitalAnnualInspection entity) {
        super(entity);
        if(entity.getOfflineHospital() != null) {
            this.offlineHospital = new AbstractEntityDTO(entity.getOfflineHospital());
        }
        if (entity.getAnnualInspectionFile() != null) {
            this.annualInspectionFile = new UploadDTO(entity.getAnnualInspectionFile());
        }
        if (entity.getThreelevelEqualprotectionFile() != null) {
            this.threelevelEqualprotectionFile = new UploadDTO(entity.getThreelevelEqualprotectionFile());
        }
        this.averageDoctorsNumber = entity.getAverageDoctorsNumber();
        this.year = entity.getYear();
        this.registrationDate = entity.getRegistrationDate();
        this.uploadedDate = entity.getUploadedDate();
        this.resource = entity.getResource();
        this.updateThreelevelEqualprotection = entity.getUpdateThreelevelEqualprotection();
        this.branchNumber = entity.getBranchNumber();
        this.workForce = entity.getWorkForce();
        this.serviceCustomerSum = entity.getServiceCustomerSum();
        this.averageDoctorsNumber = entity.getAverageDoctorsNumber();
        this.licenceNumber = entity.getLicenceNumber();
        this.licenceExpiration = entity.getLicenceExpiration();
        this.premisesArea = entity.getPremisesArea();
        this.totalIncome = entity.getTotalIncome();
        this.totalExpenditure = entity.getTotalExpenditure();
        this.totalAssets = entity.getTotalAssets();
        this.flowAssets = entity.getFlowAssets();
        this.externalAssets = entity.getExternalAssets();
        this.fixedAssets = entity.getFixedAssets();
        this.intangibleAssets = entity.getIntangibleAssets();
        this.liabilities = entity.getLiabilities();
        this.netAssets = entity.getNetAssets();
        this.threelevelEqualprotectionCode = entity.getThreelevelEqualprotectionCode();
    }

    public OfflineHospitalAnnualInspection toEntity() {
        OfflineHospitalAnnualInspection entity = new OfflineHospitalAnnualInspection();
        entity.setId(this.getId());
        entity.setYear(this.year);
        entity.setRegistrationDate(this.registrationDate);
        entity.setUploadedDate(this.uploadedDate);
        entity.setResource(this.resource);
        entity.setUpdateThreelevelEqualprotection(this.updateThreelevelEqualprotection);
        entity.setBranchNumber(this.branchNumber);
        entity.setWorkForce(this.workForce);
        entity.setServiceCustomerSum(this.serviceCustomerSum);
        entity.setAverageDoctorsNumber(this.averageDoctorsNumber);
        entity.setLicenceNumber(this.licenceNumber);
        entity.setLicenceExpiration(this.licenceExpiration);
        entity.setPremisesArea(this.premisesArea);
        entity.setTotalIncome(this.totalIncome);
        entity.setTotalExpenditure(this.totalExpenditure);
        entity.setTotalAssets(this.totalAssets);
        entity.setFlowAssets(this.flowAssets);
        entity.setExternalAssets(this.externalAssets);
        entity.setFixedAssets(this.fixedAssets);
        entity.setIntangibleAssets(this.intangibleAssets);
        entity.setLiabilities(this.liabilities);
        entity.setNetAssets(this.netAssets);
        entity.setThreelevelEqualprotectionCode(this.threelevelEqualprotectionCode);
        return entity;
    }
}
