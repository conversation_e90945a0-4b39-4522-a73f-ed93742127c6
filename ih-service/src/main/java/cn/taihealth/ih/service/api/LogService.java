package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.service.dto.SystemLogDTO;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @Date 2020/11/9
 */
public interface LogService {

    /**
     * 查询日志
     * @param hospital
     * @param pageNo
     * @param size
     * @param lastPageId
     * @param action
     * @return
     */
    Page<SystemLogDTO> getLogs(Hospital hospital, Integer pageNo, Integer size, Long lastPageId, String action);

    /**
     * 查询全部日志-OMC
     * @param hospital
     * @param pageNo
     * @param size
     * @param lastPageId
     * @param action
     * @return
     */
    Page<SystemLogDTO> allLogs(Hospital hospital, Integer pageNo, Integer size, Long lastPageId, String action);
}
