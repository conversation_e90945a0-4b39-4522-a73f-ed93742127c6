package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.aop.logging.ActionMessage;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.*;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.Inspection;
import cn.taihealth.ih.domain.MedicalCase;
import cn.taihealth.ih.domain.PatientClinic;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.convert.UserAddressVO;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.Dept.DeptType;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform.PlatformForEnum;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import cn.taihealth.ih.domain.hospital.OrderImage.Type;
import cn.taihealth.ih.domain.hospital.OrderOperation.Step;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.Status;
import cn.taihealth.ih.domain.nursing.NursingConsulItem;
import cn.taihealth.ih.domain.nursing.NursingHomeChargeItemPrice;
import cn.taihealth.ih.domain.nursing.NursingHomeItem;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.hospital.DiseaseRepository;
import cn.taihealth.ih.repo.hospital.order.CheckRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOperationRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.nursing.NursingConsulItemRepository;
import cn.taihealth.ih.repo.nursing.NursingHomeItemRepository;
import cn.taihealth.ih.repo.nursing.OrderNursingExtRepository;
import cn.taihealth.ih.repo.order.*;
import cn.taihealth.ih.repo.schedule.ScheduleRepository;
import cn.taihealth.ih.repo.schedule.ScheduleUseRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.wechat.WechatService;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.dto.drugorder.OrderInfoH5DTO;
import cn.taihealth.ih.service.dto.historyrecord.OrderHistoryRecordDTO;
import cn.taihealth.ih.service.dto.historyrecord.PrescriptionOrdersHistoryDTO;
import cn.taihealth.ih.service.impl.event.OrderConfirmedEvent;
import cn.taihealth.ih.service.impl.filter.order.OrderHistoryFilter;
import cn.taihealth.ih.service.impl.filter.order.OrderSearch;
import cn.taihealth.ih.service.impl.filter.order.OrderTriagedStatusFilter;
import cn.taihealth.ih.service.impl.filter.order.OrderWaitingStatusFilter;
import cn.taihealth.ih.service.impl.filter.order.hospitalorder.HospitalOrderSearch;
import cn.taihealth.ih.service.impl.filter.order.hospitalorder.HospitalOrderWorkersSearch;
import cn.taihealth.ih.service.impl.nursing.NursingHomeOrderConstants;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.SuperviseUtil;
import cn.taihealth.ih.service.vm.*;
import cn.taihealth.ih.service.vm.EndOrderVM.EndType;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.PreRegistrationResult;
import cn.taihealth.ih.service.vm.nursing.CreateNursingOrderVM;
import cn.taihealth.ih.service.vm.nursing.OrderNursingExtDTO;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.service.vm.offline.HisRefundParam;
import cn.taihealth.ih.service.vm.order.CompleteOrderVM;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.service.SuperviseFactory;
import cn.taihealth.ih.supervise.service.SuperviseService;
import cn.taihealth.ih.wechat.service.vm.WechatTemplatedData;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage.MiniProgram;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.persistence.criteria.Join;
import javax.sql.DataSource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class OrderServiceImpl implements OrderService {

    private final DataSource dataSource = AppContext.getInstance(DataSource.class);

    private static final Logger log = LoggerFactory.getLogger(OrderServiceImpl.class);

    private final OrderRepository orderRepository;
    private final OrderImageRepository orderImageRepository;
    private final DeptRepository deptRepository;
    private final UploadRepository uploadRepository;
    private final LockService lockService;
    private final OrderManager orderManager;
    private final ApplicationEventPublisher eventPublisher;
    private final DiseaseRepository diseaseRepository;
    private final ScheduleRepository scheduleRepository;
//    private final FileStore fileStore;
    private final OrderHistoryRecordRepository orderHistoryRecordRepository;
    private final CheckRepository checkRepository;
    private final InspectionRepository inspectionRepository;
    private final PatientClinicRepository patientClinicRepository;
    private final MedicalCaseRepository medicalCaseRepository;
    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final OrderWorkerRepository orderWorkerRepository;
    private final PrescriptionOperationRepository prescriptionOperationRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final ScheduleUseRepository scheduleUseRepository;
    private final UploadService uploadService;
    private final ElectronicMedicCardRepository electronicMedicCardRepository;
    private final DrugStoreUserService drugStoreUserService;
    private final MedicalWorkerScoreRepository medicalWorkerScoreRepository;
    private final OrderExtraInfoRepository orderExtraInfoRepository;
    private final UserAddressRepository userAddressRepository;
    private final OrderNursingExtRepository orderNursingExtRepository;
    private final RedisUtil redisUtil;


    public OrderServiceImpl(OrderRepository orderRepository,
                            OrderImageRepository orderImageRepository,
                            DeptRepository deptRepository, UploadRepository uploadRepository,
                            LockService lockService, OrderManager orderManager,
                            ApplicationEventPublisher eventPublisher,
                            DiseaseRepository diseaseRepository,
                            ScheduleRepository scheduleRepository,
                            OrderHistoryRecordRepository orderHistoryRecordRepository,
                            CheckRepository checkRepository,
                            InspectionRepository inspectionRepository,
                            PatientClinicRepository patientClinicRepository,
                            MedicalCaseRepository medicalCaseRepository,
                            PrescriptionOrderRepository prescriptionorderrepository,
                            OrderWorkerRepository orderWorkerRepository,
                            PrescriptionOperationRepository prescriptionOperationRepository,
                            ScheduleUseRepository scheduleUseRepository,
                            MedicalWorkerRepository medicalWorkerRepository,
                            UploadService uploadService,
                            ElectronicMedicCardRepository electronicMedicCardRepository,
                            DrugStoreUserService drugStoreUserService, MedicalWorkerScoreRepository medicalWorkerScoreRepository,
                            OrderExtraInfoRepository orderExtraInfoRepository, UserAddressRepository userAddressRepository,
                            OrderNursingExtRepository orderNursingExtRepository, RedisUtil redisUtil) {
        this.orderRepository = orderRepository;
        this.orderImageRepository = orderImageRepository;
        this.deptRepository = deptRepository;
        this.uploadRepository = uploadRepository;
        this.lockService = lockService;
        this.orderManager = orderManager;
        this.eventPublisher = eventPublisher;
        this.diseaseRepository = diseaseRepository;
        this.scheduleRepository = scheduleRepository;
        this.orderWorkerRepository = orderWorkerRepository;
        this.prescriptionOperationRepository = prescriptionOperationRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.uploadService = uploadService;
        this.orderHistoryRecordRepository = orderHistoryRecordRepository;
        this.checkRepository = checkRepository;
        this.inspectionRepository = inspectionRepository;
        this.patientClinicRepository = patientClinicRepository;
        this.medicalCaseRepository = medicalCaseRepository;
        prescriptionOrderRepository = prescriptionorderrepository;
        this.scheduleUseRepository = scheduleUseRepository;
        this.electronicMedicCardRepository = electronicMedicCardRepository;
        this.drugStoreUserService = drugStoreUserService;
        this.medicalWorkerScoreRepository = medicalWorkerScoreRepository;
        this.orderExtraInfoRepository = orderExtraInfoRepository;
        this.userAddressRepository = userAddressRepository;
        this.orderNursingExtRepository = orderNursingExtRepository;
        this.redisUtil = redisUtil;
    }

    private JdbcTemplate getJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    private <T> T executeWithLock(String lockName, Callable<T> callable) {
        return lockService.executeWithLock(lockName, callable);
    }

    public String orderScheduleLock(Long scheduleId) {
        return "lock.order.schedule." + scheduleId;
    }

    public String orderLock(Long orderId) {
        return "lock.order." + orderId;
    }


    @Transactional
    @Override
    @ActionLog(action = "创建订单")
    public Order createOrder(Hospital hospital, User user, Patient patient, CreateOrderVM vm) {
        orderManager.checkRegister(vm.getOrderType(), vm.getOnlineType(), vm.getRealTimeStatus());
        Order order = new Order();
        order.setOnlineType(vm.getOnlineType());
        order.setVisitType(vm.getVisitType());
        order.setRealTimeStatus(vm.getRealTimeStatus());
        order.setDescription(vm.getDescription());
        order.setDisease(StringUtil.joinWithDoubleSeparator(vm.getDiseases(), "|"));
        order.setExamination(vm.getExamination());
        order.setMedicine(vm.getMedicine());
        order.setUser(user);
        order.setHospital(hospital);
        order.setPatient(patient);
        order.setPaymentStatus(PaymentStatus.UNPAID);
        order.setOrderType(vm.getOrderType());
        order.setSource(vm.getSource());
        order.setAllergyHistory(vm.getAllergyHistory());
        order.setPresentHistory(vm.getPresentHistory());
        order.setPastHistory(vm.getPastHistory());
        String imGroupId = Snowflake64.Holder.INSTANCE.nextId() + "";
        order.setImGroupId(imGroupId);
        ElectronicMedicCardDTO electronicMedicCard = vm.getElectronicMedicCard();
        ElectronicMedicCard card = electronicMedicCardRepository.getById(electronicMedicCard.getId());
        order.setElectronicMedicCard(card);

        if (vm.getOrderType() == ClinicType.EMERGENCY) {
            // 急诊
            createEmergencyOrder(order, vm);
        } else if (vm.getRealTimeStatus() == RealTimeStatus.REAL_TIME) {
            // 除急诊外的所有实时挂号
            createReturnVisitOrder(order, vm);
        } else if (vm.getRealTimeStatus() == RealTimeStatus.APPOINTMENT) {
            // 所有预约
            createOutOrder(order, vm);
        } else {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("不支持的订单类型");
        }

        List<OrderImage> orderImages = Lists.newArrayList();
        vm.getImages().forEach(u -> {
            Upload image = uploadRepository.getById(u.getId());
            if (image != null && !user.equals(image.getUser())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
            OrderImage orderImage = new OrderImage(order, image, Type.ORDER);
            orderImages.add(orderImage);
        });
        orderImageRepository.saveAll(orderImages);
        order.setImages(orderImages);

        return order;
    }


    /**
     * 急诊提交
     *
     * @param order
     * @param vm
     */
    private void createEmergencyOrder(Order order, CreateOrderVM vm) {
        Dept dept = deptRepository.findAllByHospitalAndDeptType(order.getHospital(), DeptType.EMERGENCY)
            .stream().findFirst()
            .orElseThrow(() -> ErrorType.DEPT_NOT_REGISTERED.toProblem("急诊科未排班", "急诊科未排班"));
        List<Schedule> schedules = orderManager.findSchedules(order.getHospital(), dept)
            .stream().filter(s -> s.getMedicalWorker() == null).collect(Collectors.toList());
        if (schedules.isEmpty()) {
            throw ErrorType.DEPT_NOT_REGISTERED
                .toProblem("非常抱歉，线上医务人员繁忙，短时间内无法处理您的请求。建议您及时线下就诊，以免耽误病情！");
        }
        order.setService(schedules.get(0));
        order.setDept(dept);
        orderManager.operationOrder(order.getUser(), null, order, Step.SUBMIT, "提交订单");

        //是否开启护士自动分诊
//        if (order.getOrderType() == ClinicType.EMERGENCY &&
//            HospitalSettingsHelper.getBoolean(order.getHospital(), HospitalSettingKey.NURSE_TRIAGE_ENABLED)) {
//            try {
//                automaticScheduler.nurseTriageTask(order.getId());
//            } catch (SchedulerException e) {
//                throw ErrorType.NURSE_AUTO_TRIAGE_ERROR.toProblem("护士自动分诊任务出错");
//            }
//        }
    }

    /**
     * 复诊咨询提交
     *
     * @param order
     * @param vm
     */
    private void createReturnVisitOrder(Order order, CreateOrderVM vm) {
        if (HospitalSettingsHelper.getBoolean(order.getHospital(), HospitalSettingKey.HIS_SYNC_SCHEDULE)) {
            createHisReturnVisitOrder(order, vm);
        } else {
            createIhReturnVisitOrder(order, vm);
        }
    }

    private void createIhReturnVisitOrder(Order order, CreateOrderVM vm) {
        Schedule schedule;
        if (vm.getService() == null || vm.getService().isNew()) {
            if (vm.getDept() == null) {
                throw ErrorType.DEPT_NOT_FOUND.toProblem();
            }
            Dept dept = deptRepository.getById(vm.getDept().getId());
            List<Schedule> schedules = orderManager.findSchedules(order.getHospital(), dept)
                    .stream().filter(u -> u.getMedicalWorker() == null).collect(Collectors.toList());
            if (schedules.isEmpty()) {
                throw ErrorType.DEPT_NOT_REGISTERED.toProblem(dept.getDeptName() + "7天内无在线出诊计划");
            }
            schedule = schedules.get(0);
        } else {
            schedule = scheduleRepository.getById(vm.getService().getId());
        }
        if (orderManager.isOutDays(schedule, false)) {
            throw ErrorType.REGISTER_IS_NOT_ON_TIME.toProblem();
        }
        scheduleUseRepository.findOneBySchedule(schedule)
                .ifPresent(u -> {
                    if (u.getOnlineUsedCount() >= schedule.getOnlineCount()) {
                        throw ErrorType.REGISTER_COUNT_IS_FULL.toProblem();
                    }
                });
        order.setService(schedule);
        order.setDept(schedule.getDept());
        order.setDoctor(schedule.getMedicalWorker());
        order.setMedicineDuration(vm.getMedicineDate());
//        orderManager.addRecordToOrder(order, vm.getPatientClinics(), vm.getPatientPrescriptions());
        orderManager.operationOrderSendMessage(order.getUser(), null, order, Step.DRAFT, "复诊/咨询提交订单");
    }

    private void createHisReturnVisitOrder(Order order, CreateOrderVM vm) {
        MedicalWorker doctor = medicalWorkerRepository.getById(vm.getService().getMedicalWorker().getId());
        Dept dept = deptRepository.getById(vm.getService().getDept().getId());
        order.setDept(dept);
        order.setDoctor(doctor);
        order.setMedicineDuration(vm.getMedicineDate());

        OrderExtraInfo orderExtraInfo = orderExtraInfoRepository.findByOrderId(order.getId()).orElse(new OrderExtraInfo());
        orderExtraInfo.setHisScheduleId(vm.getService().getHisSchedulingId());
        orderExtraInfo.setRegistrationFeeCode(vm.getService().getHisRegistrationFeeCode());
        orderExtraInfo.setTreatmentFeeCode(vm.getService().getHisTreatmentFeeCode());
        orderExtraInfo.setChildrenTreatmentFeeCode(vm.getService().getHisChildrenTreatmentFeeCode());
        orderExtraInfo.setHisSourceNumber(vm.getSourceNumber());
        orderExtraInfo.setHospital(order.getHospital());

        // 预挂号：包括两步：（锁号 -> 预算）
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        PreRegistrationResult preRegistrationResult = businessService.preRegisterOnlineOrder(order, orderExtraInfo, vm.getPlatformType());
        order.setRegistrationFee(Integer.parseInt(preRegistrationResult.getShould_pay_amount()));
//        orderManager.addRecordToOrder(order, vm.getPatientClinics(), vm.getPatientPrescriptions());
        orderManager.operationOrderSendMessage(order.getUser(), null, order, Step.DRAFT, "复诊/咨询提交订单");

        //保存his信息
        orderExtraInfo.setOrderId(order.getId());
        orderExtraInfoRepository.save(orderExtraInfo);
    }

    /**
     * 门诊预约提交
     *
     * @param order
     * @param vm
     */
    private void createOutOrder(Order order, CreateOrderVM vm) {
        if (vm.getService() == null) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("未选择排班");
        }
        Schedule schedule = scheduleRepository.getById(vm.getService().getId());
        if (orderManager.isOutDays(schedule, true)) {
            throw ErrorType.REGISTER_IS_NOT_ON_TIME.toProblem();
        }
        Dept dept = schedule.getDept();

        scheduleUseRepository.findOneBySchedule(schedule)
            .ifPresent(u -> {
                if (order.getOnlineType() == OnlineType.ONLINE && u.getOnlineUsedCount() >= schedule.getOnlineCount()
                    || order.getOnlineType() == OnlineType.OFFLINE && u.getInternetUsedCount() >= schedule
                    .getInternetCount()) {
                    throw ErrorType.REGISTER_COUNT_IS_FULL.toProblem();
                }
            });

        MedicalWorker doctor = schedule.getMedicalWorker();
        if (doctor != null) {
            if (!doctor.isEnabled() || !doctor.isOpenVisit()) {
                throw ErrorType.REGISTER_IS_NOT_ON_TIME.toProblem("医生已下班");
            }
            order.setDoctor(doctor);
        }
        order.setService(schedule);
        order.setDept(dept);
        order.setMedicineDuration(vm.getMedicineDate());
//        orderManager.addRecordToOrder(order, vm.getPatientClinics(), vm.getPatientPrescriptions());
        orderManager.operationOrderSendMessage(order.getUser(), null, order, Step.DRAFT, "门诊预约提交订单");
    }

    @Override
    @Transactional
    public Order startTriageOrder(User current, Order order) {
        return executeWithLock(orderLock(order.getId()),
                               () -> orderManager.startTriageOrder(current, order));
    }

    @Override
    public Page<Order> findAllOrdered(String query, User user, Pageable pageRequest, Hospital hospital) {
        OrderSearch search = OrderSearch.of(query, user, null, hospital);
        // new OrderFilter(user.getId() + "").toSpecification(); 操作记录数据
        Specification<Order> filter = search.toSpecification()
            .and(Specifications.eq("nurse.user", user))
            .and(new OrderTriagedStatusFilter("").toSpecification())
            .and(Specifications.eq("hospital", hospital));
        return orderRepository.findAll(filter, pageRequest);
    }

    @Override
    public List<Order> findAllOrderWaiting(String query, User user, Hospital hospital) {
        List<Dept> deptList = getDept(user, hospital);
        if (deptList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Specification<Order>> specs = Lists.newArrayList();
        Specification<Order> filterDept = Specifications.in("dept", deptList.toArray());
        OrderSearch search = OrderSearch.of(query, user, null, hospital);
        specs.add(search.toSpecification());
        specs.add(filterDept);
        specs.add(new OrderWaitingStatusFilter("").toSpecification());
        specs.add(Specifications.eq("hospital", hospital));
        // .sorted(Comparator.comparing(Order::getRecommendLevel).thenComparing(Order::getCreatedDate))
        return orderRepository.findAll(Specifications.and(specs));
    }

    // 查询医生所在科室
    private List<Dept> getDept(User user, Hospital hospital) {

        Optional<MedicalWorker> medicalWorkers = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user);
        List<Dept> deptList = Lists.newArrayList();
        medicalWorkers.ifPresent(s -> s.getDeptMedicalWorkers().forEach(deptMedicalWorker -> {
            deptList.add(deptMedicalWorker.getDept());
        }));
        return deptList;
    }

    @Override
    public Page<OrderVM> findOrderList(User user, String query, Hospital hospital, Integer pageNum, Integer pageSize) {
        List<Dept> deptList = getDept(user, hospital);
        MedicalWorker medicalWorker = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        List<Specification<Order>> specs = Lists.newArrayList();
        OrderSearch search = OrderSearch.of(query, user, deptList, hospital);
        specs.add(search.toSpecification());
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.eq("doctor", medicalWorker));

        Page<OrderVM> page = orderRepository.findAll(Specifications.and(specs), PageRequest.of(pageNum, pageSize))
                .map(order -> {
                    OrderVM orderVM = new OrderVM(order);
                    orderNursingExtRepository.findOneByBaseOrder(order)
                            .ifPresent(orderNursingExt -> orderVM.setOrderNursingExt(new OrderNursingExtDTO(orderNursingExt)));
                    return orderVM;
                });
        if (page.hasContent()) {
            List<Long> orderIds = page.getContent().stream().map(OrderVM::getId).collect(Collectors.toList());
            List<PrescriptionOrder> prescriptionList = this.prescriptionOrderRepository.findByOrderIdIn(orderIds);
            if (ObjectUtils.isEmpty(prescriptionList)) {
                return page;
            }
            List<OrderInfoH5DTO> drugOrderList =
                    this.drugStoreUserService.getOrderByPrescriptionIds(prescriptionList.stream().map(PrescriptionOrder::getId).collect(Collectors.toList()));
            this.processDrugOrderFee(page.getContent(), prescriptionList, drugOrderList);
        }

        return page;
    }

    private List<OrderVM> processDrugOrderFee(List<OrderVM> orderList, List<PrescriptionOrder> prescriptionList, List<OrderInfoH5DTO> drugOrderList) {
        if (ObjectUtils.isEmpty(orderList) || ObjectUtils.isEmpty(prescriptionList) || ObjectUtils.isEmpty(drugOrderList)) {
            return orderList;
        }

        Map<Long, List<PrescriptionOrder>> prescriptionMap =
                prescriptionList.stream().collect(Collectors.groupingBy(p -> p.getOrder().getId()));
        Map<Long, List<OrderInfoH5DTO>> drugOrderMap =
                drugOrderList.stream().collect(Collectors.groupingBy(OrderInfoH5DTO::getOutPrescriptionId));
        orderList.forEach(order -> {
            List<PrescriptionOrder> thisPrescription = prescriptionMap.getOrDefault(order.getId(), null);
            if (ObjectUtils.isEmpty(thisPrescription)) {
                return;
            }
            medicalCaseRepository.findByOrderId(order.getId()).ifPresent(medicalCase -> order.setMedicalCase(new MedicalCaseVM(medicalCase)));
            AtomicInteger feeAtomic = new AtomicInteger();
            thisPrescription.forEach(prescription -> {
                List<OrderInfoH5DTO> thisDrugList = drugOrderMap.getOrDefault(prescription.getId(), null);
                if (ObjectUtils.isEmpty(thisDrugList)) {
                    return;
                }
                feeAtomic.addAndGet(thisDrugList.stream().map(d -> d.getPayPrice() + d.getFreightPrice()).reduce(0, Integer::sum));
            });

            order.setDrugOrderFee(feeAtomic.get());
        });
        return orderList;
    }

    @Override
    public Page<Order> findOrderListByHospital(String query, Integer pageNum, Integer pageSize, Hospital hospital) {
        List<Specification<Order>> specs = Lists.newArrayList();
        HospitalOrderSearch search = HospitalOrderSearch.of(query);
        specs.add(search.toSpecification());
        specs.add(Specifications.ne("orderType", ClinicType.NURSING_HOME));
        specs.add(Specifications.eq("hospital", hospital));
        return orderRepository.findAll(Specifications.and(specs), PageRequest.of(pageNum, pageSize));
    }

    @Override
    public Page<Order> findPerformanceOrderList(String query, Integer pageNum, Integer pageSize, Hospital hospital, MedicalWorker medicalWorker) {
        List<Specification<Order>> specs = Lists.newArrayList();
        HospitalOrderSearch search = HospitalOrderSearch.of(query);
        specs.add(search.toSpecification());
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.eq("doctor", medicalWorker));
        specs.add(Specifications.eq("status", OrderStatus.COMPLETED));
        specs.add(Specifications.eq("onlineType", OnlineType.ONLINE));

        Specification<Order> orderTypeSpecs =
                Specifications.or(Specifications.eq("orderType", ClinicType.OUT), Specifications.eq("orderType", ClinicType.CONSULT));
        specs.add(orderTypeSpecs);

        return orderRepository.findAll(Specifications.and(specs), PageRequest.of(pageNum, pageSize));
    }

    @Override
    public long getOrderReceptionTotal(User user, String query, Hospital hospital) {
        List<Dept> deptList = getDept(user, hospital);
        // 待接诊如果没有科室，直接返回
        if (query.contains("receptionType:wait")) {
            if (deptList.isEmpty()) {
                return 0;
            }
        }
        List<Specification<Order>> specs = Lists.newArrayList();
        OrderSearch search = OrderSearch.of(query, user, deptList, hospital);
        specs.add(search.toSpecification());
        specs.add(Specifications.eq("hospital", hospital));
        return orderRepository.count(Specifications.and(specs));
    }

    @Override
    public List<Order> findOrderWaitingList(User user, String query, Hospital hospital) {

        List<Specification<Order>> specs = Lists.newArrayList();
        OrderSearch search = OrderSearch.of(query, user, null, hospital);
        specs.add(search.toSpecification());
        specs.add(Specifications.eq("hospital", hospital));
        return orderRepository.findAll(Specifications.and(specs));
    }

    @Override
    public Upload uploadPrescriptionJpg(User user, File file, String html)
        throws IOException {
        //html转PDF
        if (html != null) {
            OutputStream out = new FileOutputStream(file.getPath());
            PDFUtil.writeStringToOutputStreamAsPDF(html, out);
            out.close();
        }
        //PDF转png
        Upload upload = null;
        try {
            List<File> files = PDFUtil.pdf2Image(file.getPath(), file.getParent(), 130, 1);
            Optional<File> first = files.stream().findFirst();
            String fileP = "";
            if (first.isPresent()) {
                File file1 = first.get();
                fileP = file1.getParent();
                upload = uploadService.upload(user, UploadResource.of(file1, UploadType.MEDICAL, -1));
            }
            files.forEach(File::delete);
            File fileParent = new File(fileP);
            fileParent.delete();
        } catch (Exception e) {
            log.error("处方html转pdf转png出错", e);
        } finally {
            file.delete();
        }

//        HtmlImageGenerator imageGenerator = new HtmlImageGenerator();
//        //加载html模版
//        imageGenerator.loadHtml(html);
//        //把html写入到图片
//        imageGenerator.getBufferedImage();
//        Thread.sleep(300);
//        imageGenerator.saveAsImage(file);
        //Upload upload = fileStore.upload(user, UploadResource.of(file, UploadType.MEDICAL, -1L));
        //需要删除本地临时文件
        // 判断文件是否存在
        if (file.isFile() && file.exists()) {
            file.delete();
        }
        return upload;
    }

    @Override
    @ActionLog(action = "用户评价")
    public OrderEvaluateDTO saveOrderWorkerEvaluate(Order order, OrderEvaluateDTO evaluateDTO,
                                                    MedicalWorker medicalWorker) {
        List<OrderWorker> workers = order.getWorkers()
            .stream().distinct().collect(Collectors.toList());
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        UserService userService = AppContext.getInstance(UserService.class);
        workers.forEach(u -> {
            if (Objects.equals(medicalWorker.getUser(), u.getUser())) {
                u.setRating(evaluateDTO.getRating());
                u.setReview(evaluateDTO.getReview());
                u.setEvaluateDate(new Date());
                if (CollectionUtils.isNotEmpty(evaluateDTO.getEvaluateTag())) {
                    u.setEvaluateTag(JSONObject.toJSONString(evaluateDTO.getEvaluateTag()));
                }
                orderWorkerRepository.save(u);
            }
            userService.addRating(u.getUser(), evaluateDTO.getRating());
        });
        WechatService wechatService = AppContext.getInstance(WechatService.class);
        List<UserPlatformInfo> list = wechatService.screenOfficialAccount(hospital,
                                                                          order.getDoctor().getUser(),
                                                                          PlatformForEnum.DOCTOR);
        String pagePath = "subpackages/doc/order-detail/index?orderId=" + order.getId();
        MiniProgram miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                               PlatformForEnum.DOCTOR,
                                                               pagePath);
        WechatTemplatedData data = new WechatTemplatedData();
        data.setFirstStr("有患者给了您" + evaluateDTO.getRating() + "星评价");
        data.addKeywordsStr(order.getPatient().getName());
        data.addKeywordsStr(order.getDoctor().getUser().getFullName());
        data.addKeywordsStr(hospital.getName());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        data.addKeywordsStr(format.format(order.getConfirmedDate()));
        data.setMiniProgram(miniProgram);
        data.setRemarkStr("点击查看详情");
        wechatService.sendWechatNotify(list, data, HospitalSettingKey.NOTIFY_TO_EVALUATE_DOCTOR, order.getHospital());
        // 护理咨询评价不上传
        if (order.getOrderType() != ClinicType.NURSING_CONSULT) {
            SuperviseDto dto = SuperviseUtil.getDto(hospital);
            SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
            if (superviseService != null){
                workers.forEach(wo -> {
                    superviseService.evaluateService(wo.getId(), dto);
                });
            }
        }
        return evaluateDTO;
    }

    @Override
    public OrderEvaluateStatsDTO getOrderEvaluateStats(Hospital hospital, MedicalWorker medicalWorker) {
        OrderEvaluateStatsDTO dto = new OrderEvaluateStatsDTO();
        Map<String, Object> count = getOrderEvaluateCount(hospital, medicalWorker);

        dto.setEvaluateTimes((int) (long) count.getOrDefault("evaluateTimes", 0));

        int ratingTotal = (int) (long) count.getOrDefault("ratingTotal", 0L);
        dto.setRatingTotal(ratingTotal);

        int favorTimes = (int) (long) count.getOrDefault("favorTimes", 0L);
        dto.setFavorTimes(favorTimes);
        medicalWorkerScoreRepository.findFirstByMedicalWorker(medicalWorker)
                .ifPresent(score -> dto.setFavorRate((int) (score.getGoodFeedbackScore() * 100)));
        return dto;
    }

    private Map<String, Object> getOrderEvaluateCount(Hospital hospital, MedicalWorker medicalWorker) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select count(0) as evaluateTimes,");
        sql.append(" sum(case when rating is null then 0 else rating end) as ratingTotal, ");
        sql.append(" sum(case when rating is not null and rating >= 3 then 1 else 0 end) as favorTimes ");
        sql.append(" from ih_orders a  ");
        sql.append(" inner join ih_order_workers b on a.id=b.order_id  ");
        if (medicalWorker != null) {
            sql.append(" where a.doctor_id = ").append(medicalWorker.getId());
        }
        sql.append(" and a.hospital_id =").append(hospital.getId());
        sql.append(" and b.rating > 0 ");
        Map<String, Object> result = getJdbcTemplate().queryForMap(sql.toString());
        result.putIfAbsent("favorTimes", 0L);
        result.putIfAbsent("ratingTotal", 0L);
        return result;
    }

    @Override
    public Page<OrderEvaluateDTO> findOrderEvaluateList(String query, Integer pageNum, Integer pageSize, Hospital hospital, MedicalWorker medicalWorker) {
        List<Specification<OrderWorker>> specs = Lists.newArrayList();
        HospitalOrderWorkersSearch search = HospitalOrderWorkersSearch.of(query);
        specs.add(search.toSpecification());
        specs.add(Specifications.gt("rating", 0));
        specs.add(Specifications.isTrue("showed"));

        Specification<OrderWorker> specification = (root, criteriaQuery, criteriaBuilder) -> {
            Join<OrderWorker, Order> orderJoin = root.join("order");
            return criteriaBuilder.and(criteriaBuilder.equal(orderJoin.get("doctor"), medicalWorker),
                    criteriaBuilder.equal(orderJoin.get("hospital"), hospital));
        };
        specs.add(specification);

        return orderWorkerRepository.findAll(Specifications.and(specs), PageRequest.of(pageNum, pageSize)).map(OrderEvaluateDTO::new);
    }

    @Override
    public Page<OrderEvaluateDTO> findOrderEvaluateList(String query, Integer pageNum, Integer pageSize, Hospital hospital) {
        List<Specification<OrderWorker>> specs = Lists.newArrayList();
        HospitalOrderWorkersSearch search = HospitalOrderWorkersSearch.of(query);
        specs.add(search.toSpecification());
        specs.add(Specifications.gt("rating", 0));

        Specification<OrderWorker> specification = (root, criteriaQuery, criteriaBuilder) -> {
            Join<OrderWorker, Order> orderJoin = root.join("order");
            return criteriaBuilder.equal(orderJoin.get("hospital"), hospital);
        };
        specs.add(specification);

        return orderWorkerRepository.findAll(Specifications.and(specs), PageRequest.of(pageNum, pageSize)).map(OrderEvaluateDTO::new);
    }

    @Override
    @Transactional
    public boolean orderHistoryRecord(User user, Order order, String medicalHtml) {

        // 咨询的话， 不需要查处方和病历
        if (order.getOrderType() != ClinicType.CONSULT) {
            List<PrescriptionOrder> preOrder = prescriptionOrderRepository.findByOrderIdAndEnabled(order.getId(), true);
            preOrder.forEach(prescriptionOrder -> {
                if (!prescriptionOrder.getStatus().equals(Status.SENT) && !prescriptionOrder
                    .getStatus().equals(Status.USED)) {
                    throw ErrorType.MEDICAL_PRESCRIPTION_STATUS.toProblem("处方未审核通过，不能结束会诊");
                }
            });
        }

        OrderHistoryRecord orderHistoryRecord = orderHistoryRecordRepository
            .findByOrderIdOrPatientId(order.getId(), null);
        if (null != orderHistoryRecord) {
            throw ErrorType.CONSTRAINT_VIOLATION.toProblem("记录已经存在，无法再次生成");
        }
        OrderHistoryRecord orderHistory = new OrderHistoryRecord();
        OrderHistroryRecordConvert convert = new OrderHistroryRecordConvert();
        orderHistory.setUser(order.getUser().getUsername());
        orderHistory.setPatient(convert.convertToDatabaseColumn(new PatientDTO(order.getPatient(), null)));
        orderHistory.setPatientId(order.getPatient().getId());

        MedicalCase medicalcase = medicalCaseRepository.findByOrderId(order.getId()).orElse(null);
        if (null != medicalcase) {
            MedicalCaseDTO medicalCaseDTO = new MedicalCaseDTO(medicalcase);
            medicalCaseDTO.setOrder(null);
            medicalCaseDTO.setPatient(null);
            medicalCaseDTO.setMedicalCaseHtml(medicalHtml);
            orderHistory.setMedicalCase(convert.convertToDatabaseColumn(medicalCaseDTO));
        }

        List<Checks> checksList = checkRepository.findByOrderId(order.getId());
        if (!checksList.isEmpty()) {
            StringBuilder checkStr = new StringBuilder();
            checkStr.append("[");
            checksList.forEach(s -> {
                ChecksDTO cDto = new ChecksDTO(s);
                cDto.setOrder(null);
                cDto.setUser(null);
                cDto.setDoctor(null);
                cDto.setPatient(null);
                // cDto.setExamItem(null);
                cDto.setChecksGroup(null);
                checkStr.append(convert.convertToDatabaseColumn(cDto));
                checkStr.append(",");
            });
            checkStr.append("]");
            orderHistory.setCheck(checkStr.length() <= 2 ? checkStr.toString()
                                      : checkStr.replace(checkStr.length() - 2, checkStr.length() - 1, "").toString());
            if (checkStr.length() > 65534) {
                orderHistory.setCheck("[]");
            }
        }

        List<Inspection> inspectionList = inspectionRepository.findByOrderId(order.getId());
        if (!inspectionList.isEmpty()) {
            StringBuilder inspectionStr = new StringBuilder();
            inspectionStr.append("[");
            inspectionList.forEach(s -> {
                InspectionDTO iDto = new InspectionDTO(s);
                iDto.setOrder(null);
                iDto.setUser(null);
                iDto.setDoctor(null);
                iDto.setPatient(null);
                inspectionStr.append(convert.convertToDatabaseColumn(iDto));
                inspectionStr.append(",");
            });
            inspectionStr.append("]");
            orderHistory.setInspection(inspectionStr.length() <= 2 ? inspectionStr.toString()
                                           : inspectionStr
                                               .replace(inspectionStr.lastIndexOf(","), inspectionStr.length() - 1, "")
                                               .toString());
            if (inspectionStr.length() > 65534) {
                orderHistory.setInspection("[]");
            }
        }
        List<PrescriptionOrder> prescriptionOrders = prescriptionOrderRepository
            .findByOrderIdAndEnabled(order.getId(), true);
        //List<Prescription> prescriptionList = prescriptionRepository.findByOrderId(order.getId());
        if (!prescriptionOrders.isEmpty()) {
            StringBuilder prescriptionStr = new StringBuilder();
            prescriptionStr.append("[");
            prescriptionOrders.forEach(s -> {
                PrescriptionOrdersHistoryDTO pOrder = new PrescriptionOrdersHistoryDTO().toHistory(s);
                prescriptionStr.append(StandardObjectMapper.stringify(pOrder));
                prescriptionStr.append(",");
            });
            prescriptionStr.append("]");
            orderHistory.setPrescription(prescriptionStr.length() <= 2 ? prescriptionStr.toString()
                                             : prescriptionStr
                                                 .replace(prescriptionStr.lastIndexOf(","),
                                                          prescriptionStr.length() - 1, "")
                                                 .toString());
            if (prescriptionStr.length() > 65534) {
                orderHistory.setPrescription("[]");
            }
        }

        orderHistory.setOperator(order.getOperator().getUsername());
        orderHistory.setHospitalName(order.getHospital().getName());
        orderHistory.setDeptName(order.getDept().getDeptName());
        orderHistory.setDoctorName(order.getDoctor().getUser().getFullName());
        orderHistory.setEndedDate(order.getEndedDate());
        orderHistory.setTriagedDate(order.getTriagedDate());
        orderHistory.setRegisteredDate(order.getRegisteredDate());
        orderHistory.setAdmissionDate(order.getAdmissionDate());
        orderHistory.setConfirmedDate(order.getConfirmedDate());
        orderHistory.setRemarks("");

        orderHistory.setOrderId(order.getId());
        OrderDTO orderDto = new OrderDTO(order, false);
  /*      orderDto.setAudio(null);
        orderDto.setDept(null);
        orderDto.setDoctor(null);
        orderDto.setHospital(null);
        orderDto.setOperator(null);
        orderDto.setPatient(null);
        orderDto.setWorkers(null);
        orderDto.setImages(null);*/
        orderHistory.setOrder(convert.convertToDatabaseColumn(orderDto));
        orderHistoryRecordRepository.save(orderHistory);
        PatientClinic patientClinic = new PatientClinic();
        patientClinic.setOrderHistoryRecord(orderHistory);
        patientClinic.setPatient(order.getPatient());
        patientClinic.setType(order.getOrderType());
        patientClinic.setSource(InputSource.PATH);
        patientClinic.setOnlineType(order.getOnlineType());
        patientClinic.setRealTimeStatus(order.getRealTimeStatus());
        patientClinic.setUser(order.getUser());
        patientClinic.setVisitDate(order.getRegisteredDate());
        if (medicalcase != null) {
            String disease = medicalcase.getDiagnosis();
            List<String> ds = medicalcase.getDiseases().stream().map(d -> d.getDisease().getDiseaseName())
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ds)) {
                if (disease == null) {
                    disease = StringUtils.join(ds, Constants.INVISIBLE_CHARACTERS);
                } else {
                    disease =
                        disease + Constants.INVISIBLE_CHARACTERS + StringUtils.join(ds, Constants.INVISIBLE_CHARACTERS);
                }
            }
            patientClinic.setResult(disease);
        }
        patientClinicRepository.save(patientClinic);
        return true;
    }

    @Override
    public OrderHistoryRecordDTO orderHistoryRecordDetail(User user, Long orderId) {

        OrderHistoryRecord orderHistoryRecord = orderHistoryRecordRepository
            .findByOrderIdOrPatientId(orderId, null);
        return new OrderHistoryRecordDTO(orderHistoryRecord);
    }

    @Override
    public List<PrescriptionOrderVM> getDoctorPrescriptionOrderDetails(User user, long orderId) {
        AtomicReference<String> diagnosis = new AtomicReference<>("");
        List<MedicalCaseDiseaseDTO> diseases = medicalCaseRepository.findByOrderId(orderId)
            .map(u -> {
                diagnosis.set(u.getDiagnosis());
                return u.getDiseases().stream().map(MedicalCaseDiseaseDTO::new)
                    .collect(Collectors.toList());
            }).orElse(Lists.newArrayList());

        List<PrescriptionOrder> prescriptionOrderResult = prescriptionOrderRepository
            .findByOrderIdAndEnabled(orderId, true);
        return prescriptionOrderResult.stream().map(PrescriptionOrderVM::new)
            .peek(p -> {
                p.setDiagnosis(diagnosis.get());
                p.setDiseases(diseases);
            }).collect(Collectors.toList());
    }

    @Override
    public List<PrescriptionOrder> findAllPrescriptionOrder(User user, Hospital hospital) {
        List<PrescriptionOrder.Status> statuses = Lists.newArrayList();
        //statuses.add(Status.REJECT);
        statuses.add(Status.REVIEWING);
        return prescriptionOrderRepository.findAllByStatusInAndEnabledAndHospital(statuses, true, hospital);
    }

    @Override
    public List<PrescriptionOrderVM> listTodayPharmacist(User user, Hospital hospital) {
        Date startTime = (Date) DataTypes.DATE
            .fromString(DataTypes.DATE.asString(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        Date endTime = (Date) DataTypes.DATE
            .fromString(DataTypes.DATE.asString(TimeUtils.getAddDay(1), "yyyy-MM-dd"),
                        "yyyy-MM-dd");
        Specification<PrescriptionOrderOperation> spec = Specifications
            .and(Specifications.ge("createdDate", startTime),
                 Specifications.lt("createdDate", endTime),
                 Specifications.and(Specifications.eq("operator", user)),
                 Specifications.or(Specifications.eq("afterStatus", PrescriptionOrder.Status.REJECTED),
                                   Specifications.eq("afterStatus", PrescriptionOrder.Status.UNSENT),
                                   Specifications.eq("afterStatus", PrescriptionOrder.Status.SENT),
                                   Specifications.eq("afterStatus", PrescriptionOrder.Status.USED)),
                 Specifications.eq("hospital", hospital));
      /*  List<PrescriptionOrderDTO> preList = prescriptionOperationRepository.findAll(spec).stream()
            .map(PrescriptionOrderOperation::getPrescriptionOrder).map(PrescriptionOrderDTO::new)
            .collect(Collectors.toList());*/

        List<PrescriptionOrderDTO> result = prescriptionOperationRepository.findAll(spec).stream().map(po -> {
            if (StringUtils.isNotBlank(po.getPrescriptionOrderStr())
                && !"null".equals(po.getPrescriptionOrderStr())) {
                PrescriptionOrderDTO prescriptionOrderDTO = StandardObjectMapper.readValue(po.getPrescriptionOrderStr(),
                                                                                           new TypeReference<>() {
                                                                                           });
                PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.findById(prescriptionOrderDTO.getId())
                    .orElse(null);
                prescriptionOrderDTO.setOperationsId(po.getId());
                MedicalCase medicalCase = medicalCaseRepository.findByOrderId(prescriptionOrder.getOrder().getId())
                    .orElse(null);
                prescriptionOrderDTO.setDiagnosis(null != medicalCase ? medicalCase.getDiagnosis() : (""));
                if (null != medicalCase && !medicalCase.getDiseases().isEmpty()) {
                    prescriptionOrderDTO.setDiseases(
                        medicalCase.getDiseases().stream().map(MedicalCaseDiseaseDTO::new)
                            .collect(Collectors.toList()));
                }
                if (prescriptionOrder.getCaPicture() != null) {
                    prescriptionOrderDTO.setCaPicture(new UploadDTO(prescriptionOrder.getCaPicture()));
                }
                if (prescriptionOrder.getCaPdf() != null) {
                    prescriptionOrderDTO.setCaPdf(new UploadDTO(prescriptionOrder.getCaPdf()));
                }
                return prescriptionOrderDTO;
            }
            return new PrescriptionOrderDTO();
        }).collect(Collectors.toList());
        return result.stream().map(u -> {
            PrescriptionOrderVM prescriptionOrderVM = new PrescriptionOrderVM(u);
            PrescriptionOrder po = prescriptionOrderRepository.getById(u.getId());
            if (po.getOrder().getDept() != null) {
                prescriptionOrderVM.setDept(new DeptVM(po.getOrder().getDept()));
            }
            if (null != po.getUpload()) {
                prescriptionOrderVM.setUpload(new UploadVM(po.getUpload()));
            }
            if (null != po.getCaPdf()) {
                prescriptionOrderVM.setCaPdf(new UploadVM(po.getCaPdf()));
            }
            if (null != po.getCaPicture()) {
                prescriptionOrderVM.setCaPicture(new UploadVM(po.getCaPicture()));
            }
            return prescriptionOrderVM;
        }).collect(Collectors.toList());

      /*  return preList.stream().map(prescriptinOrderDTO -> {
            MedicalCase medicalCase = medicalCaseRepository
                .findByOrderId(prescriptinOrderDTO.getOrder().getId()).orElse(null);
            prescriptinOrderDTO.setDiagnosis(null != medicalCase ? medicalCase.getDiagnosis() : "");
            if (null != medicalCase && !medicalCase.getDiseases().isEmpty()) {
                prescriptinOrderDTO.setDiseases(
                    medicalCase.getDiseases().stream().map(MedicalCaseDiseaseDTO::new).collect(Collectors.toList()));
            }
            return prescriptinOrderDTO;
        }).collect(Collectors.toList());*/
    }

    @Override
    public List<OrderVM> consultList(Patient patient, Hospital hospital) {
        Specification<Order> filter = Specifications.and(Specifications.eq("patient", patient),
                                                         Specifications.eq("orderType", ClinicType.CONSULT));
        return orderRepository.findAll(filter).stream().map(OrderVM::new)
            .collect(Collectors.toList());
    }

    @Override
    public Page<Order> findAllOrderReceptionHistory(User user, String query, Pageable pageRequest, Hospital hospital) {
        OrderSearch search = OrderSearch.of(query, user, null, hospital);
        Specification<Order> userStatus = Specifications.eq("doctor.user.id", user.getId());
        Specification<Order> filter;
        List<Specification<Order>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(userStatus);
        specs.add(search.toSpecification());
        if (null == query) {
            filter = new OrderHistoryFilter("status").toSpecification();
            specs.add(filter);
        } else if (!query.contains("status:")) {
            filter = new OrderHistoryFilter("status").toSpecification();
            specs.add(filter);
        }
        return orderRepository.findAll(Specifications.and(specs), pageRequest);

    }

    @Override
    @Transactional
    @ActionLog(action = "医生抢单")
    public Order grabAnOrder(User current, Order order) {
        return executeWithLock(orderLock(order.getId()),
                               () -> orderManager.updateGrabAnOrder(current, order));
    }

    @Override
    @Transactional
    public void sentPrescriptionByIm(User sender, User receiver,
                                     PrescriptionOrder prescriptionOrder) {
        orderManager.sentPrescriptionByIm(sender, receiver, prescriptionOrder);
    }

    @Override
    @Transactional
    @ActionLog(action = "结束订单")
    public Order completeConsultation(User current, Order order, String medicalHtml,
                                      @ActionMessage String reason, CompleteOrderVM completeOrderVM) {
        EndOrderVM vm = new EndOrderVM();
        vm.setReason(reason);
        vm.setEndType(EndType.FINISH);
        vm.setDiagnosisCa(completeOrderVM.getDiagnosisCa());
        Hospital hospital = CurrentHospital.getOrThrow();

        Order withLock = executeWithLock(orderLock(order.getId()),
                () -> {
                    MedicalWorker doctor = medicalWorkerRepository.findOneByHospitalAndUser(order.getHospital(), current).orElse(null);
                    Order o = orderManager.endOrder(current, doctor, order, vm);
                    orderHistoryRecord(current, order, medicalHtml);
                    return o;
                });
        SuperviseDto dto = SuperviseUtil.getDto(order.getHospital());
        SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
        if (superviseService != null){
            if (order.getOrderType() == ClinicType.OUT) {
                superviseService.createVisitRecord(hospital, order.getId(), dto);
            } else if (order.getOrderType() == ClinicType.CONSULT) {
                superviseService.createConsultationRecord(order.getId(), dto);
            }
        }
        return withLock;
    }

    @Override
    @Transactional
    public OrderVM waitingOrder(User current, Order order) {
        return waitingOrder(current, order, null);
    }

    @Override
    @Transactional
    @ActionLog(action = "医生开始接诊")
    public OrderVM waitingOrder(User current, Order order, User assistant) {
        return new OrderVM(executeWithLock(orderLock(order.getId()), () -> orderManager.waitingOrder(current, order, assistant)));
    }


    @Override
    @Transactional
    @ActionLog(action = "护士分诊")
    public Order triageOrder(User current, Order order, TriageOrderVM vm) {
        return executeWithLock(orderLock(order.getId()),
                               () -> orderManager.triageOrder(current, order, vm));
    }

    @Override
    @ActionLog(action = "患者挂号")
    public Order registerOrder(User current, Order order, RegisterVM vm) {
        log.info("患者挂号 user：{} order: {} registerVM: {}", current.getUsername(), order.getId(), vm);
        return executeWithLock(orderLock(order.getId()),
                () -> {
                    if (order.getService() == null) {
                        return orderManager.registerOrder(current, order, vm);
                    } else {
                        return executeWithLock(orderScheduleLock(order.getService().getId()),
                                () -> orderManager.registerOrder(current, order, vm));
                    }
                }
        );
    }

    @Override
    @Transactional
    public Order getOrderPaymentStatus(User user, Order order) {
//        if (order.getStatus() == OrderStatus.REFUNDING) {
//            RefundWechatOrderVM vm = new RefundWechatOrderVM();
//            vm.setId(order.getId());
//            vm.setType(WechatOrderType.REGISTER);
//            IhWxPayRefund result = AppContext.getInstance(WechatService.class).checkRefundStatus(user, vm);
//            if (result == null) {
//                refundedOrder(user, order.getId(), null);
//            } else if (result.isSuccess()) {
//                WechatRefundCheckResult checkResult = new WechatRefundCheckResult(result);
//                AppContext.getInstance(WechatService.class).refundOrder(checkResult, null);
//            }
//            return orderRepository.getById(order.getId());
//        } else if (order.getStatus() == OrderStatus.PENDING) {
//            CreateWechatOrderVM vm = new CreateWechatOrderVM();
//            vm.setId(order.getId());
//            vm.setType(WechatOrderType.REGISTER);
//            IhWxPayOrderQueryResult result = AppContext.getInstance(WechatService.class).checkPaymentStatus(user, vm);
//            if (result != null && "SUCCESS".equalsIgnoreCase(result.getTradeState())) {
//                WechatPayCheckResult checkResult = new WechatPayCheckResult(result);
//                AppContext.getInstance(WechatService.class).payOrder(checkResult);
//            }
//            return orderRepository.getById(order.getId());
//        }
        return order;
    }

    @Override
    @Transactional
    public Order payOrder(User user, long orderId, Date payTime, HisPayParam hisPayParam) {
        return executeWithLock(orderLock(orderId), () -> {
            Order order = orderRepository.getById(orderId);
            order = orderManager.payOrder(user, order, payTime, hisPayParam);
            return order;
        });
    }

    @Override
    @Transactional
    public Order refundedOrder(User user, long orderId, Step step, HisRefundParam refundParam) {
        return executeWithLock(orderLock(orderId), () -> {
            Order order = orderRepository.getById(orderId);
//            Hospital hospital = order.getHospital();
//             his这里撤销结算
//            boolean aBoolean = HospitalSettingsHelper.getBoolean(order.getHospital(),
//                    HospitalSettingKey.HIS_SYNC_SCHEDULE);
//            BusinessServiceStrategy.getInstance().getStrategy(aBoolean)
//                    .returnRegist(hospital, order, transactionId);
            if (order.getService() == null) {
                return orderManager.refundedOrder(user, order, step, refundParam);
            } else {
                return executeWithLock(orderScheduleLock(orderId),
                        () -> orderManager.refundedOrder(user, order, step, refundParam));
            }
        });
    }

    @Override
    @ActionLog(action = "订单结束")
    public Order endOrder(User user, MedicalWorker medicalWorker, Order order, EndOrderVM vm, @ActionMessage String message) {
        Order returnOrder;
        if (order.getService() == null) {
            returnOrder = executeWithLock(orderLock(order.getId()), () -> orderManager.endOrder(user, medicalWorker, order, vm));
        } else {
            returnOrder = executeWithLock(orderLock(order.getId()),
                    () -> executeWithLock(orderScheduleLock(order.getService().getId()),
                            () -> orderManager.endOrder(user, medicalWorker, order, vm)));
        }
        SuperviseDto dto = SuperviseUtil.getDto(order.getHospital());
        SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
        if (superviseService != null){
            // 如果候诊中医生主动退款，即拒绝接诊，上海监管平台上报
            if (order.getOrderType() == ClinicType.OUT && vm.getEndType() == EndType.ONTIME_CONFIRMED_DOCTOR_REFUNDED) {
                superviseService.rejectRegistration(order.getHospital(), order.getId(), dto);
            } else if (order.getOrderType() == ClinicType.CONSULT) {
                superviseService.createCancelConsultationRecord(order.getId(), dto, vm.getEndType().getOrderStep());
            }
        }
        return returnOrder;
    }

    @Override
    @Transactional
    @ActionLog(action = "修改订单")
    public Order updateOrder(Order order, OrderDTO dto) {
        return executeWithLock(orderLock(order.getId()), () -> orderManager.updateOrder(order, dto));
    }


    @Override
    public List<Upload> updateImagesForOrder(User user, Order order, OrderImage.Type type, List<UploadVM> vms) {
        List<OrderImage> orderImagesDB = orderImageRepository.findAllByOrder(order);
        List<OrderImage> olds = orderImagesDB.stream().filter(u -> u.getType() == type)
            .collect(Collectors.toList());

        Set<Upload> uploads = vms.stream().map(u -> {
                Upload upload = uploadRepository.getById(u.getId());
                if (!Objects.equals(upload.getUser(), user)) {
                    throw ErrorType.FORBIDDEN.toProblem();
                }
                return upload;
            })
            .collect(Collectors.toSet());
        List<OrderImage> list = Lists.newArrayList();

        for (Upload each : uploads) {
            OrderImage image = olds
                .stream()
                .filter(u -> Objects.equals(each, u.getImage()))
                .findFirst().orElse(new OrderImage());
            if (image.isNew()) {
                image.setOrder(order);
                image.setImage(each);
                image.setType(type);
                orderImageRepository.save(image);
            }
            list.add(image);
        }

        olds.stream().filter(old -> list.stream().noneMatch(now -> Objects.equals(old, now)))
            .forEach(orderImageRepository::delete);

        return list.stream()
            .map(OrderImage::getImage)
            .collect(Collectors.toList());
    }

    //    @Override
//    @Transactional
//    public void startOrJoinVideoChat(User user, Order order, VideoRoom room) {
//        executeWithLock(orderLock(order), () -> {
//            if (room == null || room.getStatus() == VideoRoom.Status.CLOSED) {
//                orderManager.startVideoChat(user, order);
//            } else {
//                orderManager.joinVideoChat(room, user, order);
//            }
//            return 0;
//        });
//    }
//
//    @Override
//    public void quitVideoChat(VideoRoom room, User user) {
//        executeWithLock(orderLock(room.getOrder()), () -> {
//            orderManager.quitVideoChat(room, user);
//            return 0;
//        });
//    }

    @Override
    public int getEstimateWaitTime(Order order) {
        int estimateWaitTime = 0;
        if (order.getService() != null && order.getService().getMedicalWorker() != null) {
            // 到了医生的
            MedicalWorker doctor = order.getDoctor();
            List<Specification<Order>> sp = new ArrayList<>();
            sp.add(Specifications.eq("hospital", order.getHospital()));
            sp.add(Specifications.eq("doctor", doctor));
            sp.add(Specifications.or(
                    Specifications.eq("status", OrderStatus.STARTED),
                    Specifications.and(
                            Specifications.or(Specifications.eq("status", OrderStatus.ONTIME_CONFIRMED),
                                    Specifications.eq("status", OrderStatus.NOTONTIME_CONFIRMED),
                                    Specifications.eq("status", OrderStatus.REGISTERED)),
                            Specifications.lt("createdDate", order.getCreatedDate())
                    )
            ));

            long submitted = orderRepository.count(Specifications.and(sp));
            int treatmentDuration = doctor.getTreatmentDuration() == null ? 0 : doctor.getTreatmentDuration();
            estimateWaitTime = (int) (submitted * treatmentDuration);
        }  else {
            StackTraceElement[] stacks = new Throwable().getStackTrace();
            log.error("没有挂号到医生的， 由于业务发生变化， 未阐明这一块的业务， 这里的代码未做适配 " + Thread.currentThread().getName()
                    + "," + stacks[1].getClassName() + "," + stacks[1].getLineNumber());
            return 0;
//            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("没有挂号到医生的， 由于业务发生变化， 未阐明这一块的业务， 这里的代码未做适配");
        }
//        if (order.getDept() != null) {
//            // TODO: 没到医生的， 由于业务发生变化， 未阐明这一块的业务， 这里的代码未做适配
//            Dept dept = order.getDept();
//            Date now = new Date();
//            String today = DataTypes.DATE.asString(now, "yyyy-MM-dd");
//            String tomorrow = DataTypes.DATE
//                .asString(new Date(now.getTime() + Constants.ONE_DAY_MILLIONS), "yyyy-MM-dd");
//            String search =
//                "START_DATE:<" + tomorrow + " END_DATE:>=" + today + " DEPT:" + dept.getId()
//                    + " auth:DOCTOR";
//            long submitted = orderRepository.count(Specifications
//                                                       .and(Specifications.eq("hospital", order.getHospital()),
//                                                            Specifications.eq("dept", dept),
//                                                            Specifications.eq("status", OrderStatus.ONTIME_CONFIRMED)));
//            long doctorCount = scheduleRepository.count(Specifications
//                                                            .and(ScheduleSearch.of(search, order.getHospital(), null)
//                                                                     .toSpecification(),
//                                                                 Specifications.eq("hospital", order.getHospital())));
//            int treatmentDuration = dept.getTreatmentDuration() == null ? 0 : dept.getTreatmentDuration();
//            estimateWaitTime = (int) (submitted / (doctorCount + 1)) * treatmentDuration;
//        }
        return estimateWaitTime;
    }

    private void sendNotify(Hospital hospital, User user, MedicalWorker medicalWorker, Order order) {
        NotifyService notifyService = AppContext.getInstance(NotifyService.class);
        if (checkMessageCenterOnOff(HospitalSettingKey.NOTIFY_TO_EVALUATE_DOCTOR,
                                    hospital)) {
            String content =
                user.getFullName() + "对您的咨询服务进行了评价，点击查看。";
            //消息中心提醒
            notifyService.createNotify(
                content,
                medicalWorker.getUser(),
                HospitalSettingKey.NOTIFY_TO_EVALUATE_DOCTOR,
                null, hospital, "/pages/doc/orderdetail/index?orderId=" + order.getId(), null);
        }
    }

    private boolean checkMessageCenterOnOff(HospitalSettingKey key, Hospital hospital) {
        List<String> methods = HospitalSettingsHelper.getListString(hospital, key);
        return methods.contains(HospitalSettingKey.NotificationMethod.MESSAGE_CENTER.name());
    }

    @Transactional
    @Override
    public OrderNursingExt createNursingOrder(Hospital hospital, User user, Patient patient, CreateNursingOrderVM vm) {
        ClinicType orderType = vm.getOrderType();
        if (!(orderType == ClinicType.NURSING_CONSULT || orderType == ClinicType.NURSING_HOME)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        OrderNursingExt orderExt = new OrderNursingExt();
        if (vm.getConsulItemId() != null) {
            NursingConsulItem consulItem = AppContext.getInstance(NursingConsulItemRepository.class).getById(vm.getConsulItemId());
            if (!Objects.equals(hospital, consulItem.getHospital())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
            orderExt.setConsulItem(consulItem);
        }
        Order order = new Order();
        if (vm.getHomeItemId() != null) {
            NursingHomeItem homeItem = AppContext.getInstance(NursingHomeItemRepository.class).getById(vm.getHomeItemId());
            if (!Objects.equals(hospital, homeItem.getHospital())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
            orderExt.setHomeItem(homeItem);
            orderExt.setChargeItems(homeItem.getChargeItems().stream().map(NursingHomeChargeItemPrice::new)
                    .collect(Collectors.toList()));
            orderExt.setNursingAdvancePrice(homeItem.getNursingAdvancePrice());
        }
        order.setOnlineType(OnlineType.ONLINE);
        order.setVisitType(VisitType.GRAPHIC);
        order.setRealTimeStatus(orderType == ClinicType.NURSING_CONSULT ? RealTimeStatus.REAL_TIME : RealTimeStatus.APPOINTMENT);
        order.setDescription(vm.getDescription());
        order.setUser(user);
        order.setHospital(hospital);
        order.setPatient(patient);
        order.setPaymentStatus(PaymentStatus.UNPAID);
        order.setOrderType(orderType);
        String imGroupId = Snowflake64.Holder.INSTANCE.nextId() + "";
        order.setImGroupId(imGroupId);
        ElectronicMedicCardDTO electronicMedicCard = vm.getElectronicMedicCard();
        ElectronicMedicCard card = electronicMedicCardRepository.getById(electronicMedicCard.getId());
        order.setElectronicMedicCard(card);

        // 护理咨询需要排班，护理到家不需要排班
        Schedule schedule;
        if (vm.getService() != null && !vm.getService().isNew()) {
            schedule = scheduleRepository.getById(vm.getService().getId());
        } else {
            schedule = null;
        }
        if (schedule != null) {
            scheduleUseRepository.findOneBySchedule(schedule)
                    .ifPresent(u -> {
                        if (u.getOnlineUsedCount() >= schedule.getOnlineCount()) {
                            throw ErrorType.REGISTER_COUNT_IS_FULL.toProblem();
                        }
                    });
            order.setService(schedule);
            order.setDoctor(schedule.getMedicalWorker());
        }
        // 预约上门时间
        if (vm.getOrderType() == ClinicType.NURSING_HOME && vm.getDoorVisitDate() != null) {
            orderExt.setAppointDate(vm.getDoorVisitDate());
        }
        orderExt.setBaseOrder(order);
        orderExt.setHeight(vm.getHeight());
        orderExt.setWeight(vm.getWeight());
        if (vm.getAddressId() != null) {
            UserAddressVO address = new UserAddressVO(userAddressRepository.getById(vm.getAddressId()));
            orderExt.setAddress(address);
        }
        order.setRegistrationFee(calcFee(order, orderExt));
        if (orderType == ClinicType.NURSING_CONSULT) {
            orderManager.operationOrder(user, null, order, Step.DRAFT, "下单");
             // 如果护理咨询的价格是0的话，直接修改订单的状态
             if (order.getRegistrationFee() == 0) {
                 order.setOperator(user);
                 order.setPaymentStatus(PaymentStatus.PAID);
                 order.setRegisteredDate(new Date());
                 order.setAccessCode(RandomUtils.generateRandomNumbers(8));
                 order.setVisitId("" + Snowflake64.Holder.INSTANCE.nextId());
                 ScheduleUse use = scheduleUseRepository.findOneBySchedule(schedule)
                         .orElseGet(() -> new ScheduleUse(schedule));
                 if (orderManager.canRegister(schedule, ClinicType.NURSING_CONSULT, OnlineType.ONLINE,
                         RealTimeStatus.REAL_TIME, use, true)) {
                     scheduleUseRepository.save(use);
                 } else {
                     throw ErrorType.REGISTER_COUNT_IS_FULL.toProblem();
                 }
                 orderManager.operationOrder(user, null, order, Step.PAY, "患者挂号，支付费用");
                 // 触发患者支付成功事件
                 order.setConfirmedDate(new Date());
                 eventPublisher.publishEvent(new OrderConfirmedEvent(order, null));
             }
        } else {
            orderManager.operationNursingOrder(user, null, order, Step.SUBMIT, "下单");
        }
        // 门诊病历/出院小结/伤口图片
        List<OrderImage> orderImages = Lists.newArrayList();
        vm.getImages().forEach(u -> {
            Upload image = uploadRepository.getById(u.getId());
            if (image != null && !user.equals(image.getUser())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
            OrderImage orderImage = new OrderImage(order, image, Type.ORDER);
            orderImages.add(orderImage);
        });
        orderImageRepository.saveAll(orderImages);
        order.setImages(orderImages);
        orderRepository.save(order);
        orderNursingExtRepository.save(orderExt);
        // 创建订单流程走完，开始设置订单支付过期时间
        if (orderType == ClinicType.NURSING_HOME) {
            int anInt = HospitalSettingsHelper.getInt(order.getHospital(), HospitalSettingKey.PAY_ORDER_LIMIT_TIME);
            // 待支付提醒
            redisUtil.set("nursingOrder_waitingPay:" + order.getId(), 1, anInt * 30L);
            // 超时提醒
            redisUtil.set("nursingOrder_expire:" + order.getId(), 1, anInt * 60L);
        }
        return orderExt;
    }

    private int calcFee(Order order, OrderNursingExt ext) {
        int price = 0;
        switch (order.getOrderType()) {
            case NURSING_HOME:
                // 护理到家
                NursingHomeItem homeItem = ext.getHomeItem();
                price = homeItem.getEstimatedPrice();
                return price;
            case NURSING_CONSULT:
                // 护理咨询
                price = order.calcRegistrationFee();
                return price;
            default:
                return -1;
        }
    }

    @Override
    @Transactional
    public void cancelNursingOrder(User user, Order order) {
        lockService.executeWithLock("ih.nursing_order." + order.getId(), () -> {
            OrderStatus status = order.getStatus();
            if (!NursingHomeOrderConstants.CAN_CANCEL_ORDER.contains(status)) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("当前订单不可取消");
            }
            orderManager.operationNursingOrder(user, null, order, Step.CANCEL_PAY, "用户取消订单");
            return 0;
        });
    }

    @Override
    @Transactional
    public void payNursingOrder(User user, long orderId, WechatOrder order) {
        Order nursingOrder = orderRepository.getById(orderId);
        if (nursingOrder.getStatus() == OrderStatus.CLOSED) {
            log.info("护理到家订单id: {}，因为订单超时未支付关闭订单，为用户办理退款", orderId);
            // TODO 调用微信退款接口
        } else {
            lockService.executeWithLock("ih.nursing_order." + order.getId(), () -> {
                orderManager.operationNursingOrder(nursingOrder.getUser(), null, nursingOrder, Step.PAY, "订单支付成功待审核");
                return 0;
            });
        }
    }

    @Override
    @Transactional
    public void nursingOrderPending(Order nursingOrder) {
        lockService.executeWithLock("ih.nursing_order." + nursingOrder.getId(), () -> {
            OrderStatus status = nursingOrder.getStatus();
            if (status == OrderStatus.PENDING) {
                return 0;
            }
            orderManager.operationNursingOrder(nursingOrder.getUser(), null, nursingOrder, Step.PAY, "订单支付中");
            return 0;
        });
    }
}

