package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import lombok.Data;

import java.io.Serializable;

/**
 * 上传交易账单，账单详情
 */
@Data
public class TradeIHOrderInfo implements Serializable {

    // 交易时间	Y	格式为yyyy-MM-dd HH:MM:SS
    private String transaction_time;
    // 交易流水号	Y	与结算接口中的trade_no对应
    private String transaction_id;
    // 平台流水号	N	结算时互联网医院传给his的流水号
    private String serial_no;
    // 交易状态	Y	标识该笔明细数据的类型：
    // SUCCESS，支付成功，说明该行数据为一笔支付成功的订单
    // REFUND，转入退款，说明该行数据为一笔发起退款成功的退款单
    // REVOKED，已撤销，说明该行数据为一笔在用户支付成功后发起撤销的退款单
    private String transaction_status;
    // 应结订单金额	Y
    private String settlement_order_amount;
    // 代金券金额	Y
    private String voucher_amount;
    /**
     * 第三方支付退款单号
     * 取微信退款表：refund_id，对应微信退款接口refund_id
     * 取支付宝退款表：refund_no，对应支付宝退款接口out_request_no
     */
    private String refund_id;
    /**
     * 商户退款单号
     * 取微信退款表：out_refund_no，对应微信退款接口refund_id
     * 取支付宝退款表：refund_no，对应支付宝退款接口out_request_no
     */
    private String merchant_refund_number;
    // 退款总金额	Y
    private String refund_amount;
    // 个人退款金额	Y
    private String self_refund_amount;
    // 医保退款金额
    private String medicare_refund_amount;
    // 充值券退款金额	Y
    private String recharge_voucher_refund_amount;
    // 医保订单必传，订单支付金额（个人自负部分）
    private String self_order_amount;
    // 退款类型	Y	ORIGINAL—原路退款
    // BALANCE—转退到用户的微信支付零钱
    // 如果该行数据为订单（交易状态SUCCESS）则留空
    private String refund_type;
    // 退款状态	Y	生成账单文件时该笔退款的状态、出账后不会更新，如果该行数据为订单（交易状态SUCCESS），则留空
    // SUCCESS，退款成功
    // PROCESSING，退款处理中
    // FAIL，退款失败
    // CHANGE，退款异常
    private String refund_status;
    // 商品名称	Y	挂号费等
    private String product_name;
    // 支付总金额	Y
    private String order_amount;
    // 医保支付金额  N
    private String medicare_order_amount;
    // 申请退款金额	Y
    private String refund_application_amount;
    // 医保支付订单号
    private String pay_order_id;
    // 0 自费订单 1 医保订单，默认是0
    private String order_type = "0";
    // 支付方式，自费自负部分 1 微信支付 2 支付宝支付
    private String pay_type = "1";
    // 收据号 收款时传入收款收据号，退款状态传退款收据号
    private String settle_id;
    // 商户订单号
    private String out_trade_no;
    // 医院id
    private Long hospitalId;
}
