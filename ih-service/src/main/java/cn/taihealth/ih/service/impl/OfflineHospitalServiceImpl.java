package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.cloud.OfflineHospital.Medicare;
import cn.taihealth.ih.domain.cloud.OfflineHospitalAnnualInspection;
import cn.taihealth.ih.domain.cloud.OfflineHospitalRecommend;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.crm.Province;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.repo.OfflineHospitalAnnualInspectionRepository;
import cn.taihealth.ih.repo.OfflineHospitalRepository;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.repo.cloud.OfflineHospitalRecommendRepository;
import cn.taihealth.ih.repo.hospital.HospitalPublicPlatformRepository;
import cn.taihealth.ih.service.api.OfflineHospitalService;
import cn.taihealth.ih.service.api.UploadService;
import cn.taihealth.ih.service.dto.OfflineHospitalDTO;
import cn.taihealth.ih.service.impl.filter.offlinehospital.OffHospitalSearch;
import cn.taihealth.ih.service.impl.filter.offlinehospital.RecommendFilter;
import cn.taihealth.ih.service.util.GeoUtils;
import cn.taihealth.ih.service.util.LngLat;
import cn.taihealth.ih.service.vm.offline.MiniAppOfflineHospitalVM;
import cn.taihealth.ih.service.vm.offline.OfflineHospitalAnnualInspectionVM;
import cn.taihealth.ih.service.vm.offline.OfflineHospitalRecommendVM;
import cn.taihealth.ih.service.vm.offline.OpenMiniAppVM;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.criteria.*;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 */
@Service
public class OfflineHospitalServiceImpl implements OfflineHospitalService {

    private static final Logger log = LoggerFactory.getLogger(OfflineHospitalServiceImpl.class);

    private final UploadService uploadService;
    private final OfflineHospitalRecommendRepository offlineHospitalRecommendRepository;
    private final OfflineHospitalRepository offlineHospitalRepository;
    private final OfflineHospitalAnnualInspectionRepository offlineHospitalAnnualInspectionRepository;

    public OfflineHospitalServiceImpl(OfflineHospitalRepository offlineHospitalRepository,
                                      OfflineHospitalRecommendRepository offlineHospitalRecommendRepository,
                                      UploadService uploadService,
                                      OfflineHospitalAnnualInspectionRepository offlineHospitalAnnualInspectionRepository) {
        this.offlineHospitalRepository = offlineHospitalRepository;
        this.uploadService = uploadService;
        this.offlineHospitalRecommendRepository = offlineHospitalRecommendRepository;
        this.offlineHospitalAnnualInspectionRepository = offlineHospitalAnnualInspectionRepository;
    }

    @Override
    public void importHospitals(Upload upload) {
        log.info("start import hospitals");
        try (GZIPInputStream gzin = new GZIPInputStream(uploadService.openStream(upload))) {
            List<OfflineHospital> offlineHospitals = StandardObjectMapper.getInstance()
                .readValue(gzin, new TypeReference<>() {
                });
            int allCount = 0;
            int count = 1000;
            for (int i = 0; i < offlineHospitals.size(); i += count) {
                List<OfflineHospital> needImports = offlineHospitals
                    .subList(i, Math.min(i + count, offlineHospitals.size()))
                    .stream()
                    .filter(h -> {
                        h.setId(null);
                        if (h.isYibao()) {
                            h.setMedicare(Medicare.LOCAL);
                        } else {
                            h.setMedicare(Medicare.NONSUPPORT);
                        }
                        h.setNature(h.getMode());
                        if (Province.isShortName(h.getProvince())) {
                            h.setProvince(Province.fromShortName(h.getProvince()).getFullName());
                        }
                        return !offlineHospitalRepository.findOneByName(h.getName()).isPresent();
                    }).collect(Collectors.toList());
                offlineHospitalRepository.saveAll(needImports);
                allCount += needImports.size();
                log.info("import part of hospitals count: " + needImports.size());
            }
            log.info("import all hospitals count: " + allCount);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("end import hospitals");
    }

    @Override
    public void save(OfflineHospital offlineHospital) {
        updateHospitalFields(offlineHospital);
        offlineHospitalRepository.save(offlineHospital);
//        if (hospital.isEnabled()) {
//            redisGeoService.updateHospitalGeo(hospital);
//        } else {
//            redisGeoService.deleteHospitalGeo(hospital.getId());
//        }
    }

    private void updateHospitalFields(OfflineHospital offlineHospital) {
        updateLevel(offlineHospital);
    }

    @Override
    @Transactional
    public void delete(OfflineHospital offlineHospital) {
        offlineHospitalRepository.delete(offlineHospital);
//        redisGeoService.deleteHospitalGeo(hospital.getId());
    }

    @Override
    public void deleteById(Long id) {
        offlineHospitalRepository.findById(id).ifPresent(offlineHospitalRepository::delete);
//        redisGeoService.deleteHospitalGeo(id);
    }


    private void updateLevel(OfflineHospital offlineHospital) {
        String level = "";
        switch (offlineHospital.getRank()) {
            case UNKNOWN:
                level = null;
                break;
            case THREE:
                level += "三级";
                break;
            case TWO:
                level += "二级";
                break;
            case ONE:
                level += "一级";
                break;
            default:
        }
        switch (offlineHospital.getGrade()) {
            case UNKNOWN:
                level = null;
                break;
            case JIA:
                level += "甲等";
                break;
            case YI:
                level += "乙等";
                break;
            case BING:
                level += "丙等";
                break;
            default:
        }

        offlineHospital.setLevel(level);
    }

    @Override
    public List<MiniAppOfflineHospitalVM> findUsedOfflineHospitals(String query, Double longitude, Double latitude) {
        LngLat lngLat = latitude == null || longitude == null ? null : new LngLat(longitude, latitude);

        // 在短期内数量不会多，全部查询后一个个处理
        Specification<OfflineHospital> sp = OffHospitalSearch.of(query).toSpecification();

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();

        CriteriaQuery<OfflineHospital> criteriaQuery = criteriaBuilder.createQuery(OfflineHospital.class);
        Root<OfflineHospital> rootHospital = criteriaQuery.from(OfflineHospital.class);
//        CriteriaQuery<Hospital> criteriaQuery1 = criteriaBuilder.createQuery(Hospital.class);
//        Root<Hospital> root = criteriaQuery1.from(Hospital.class);

        Join<OfflineHospital, Hospital> offlineHospitalJoin = rootHospital.join("hospitals");
        Predicate p = criteriaBuilder.and(criteriaBuilder.isTrue(offlineHospitalJoin.get("enabled")));

        if (sp == null) {
            criteriaQuery.where(p);
        } else {
            Predicate condition = sp.toPredicate(rootHospital, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(condition, p);
        }

        List<OfflineHospital> offlineHospitals = entityManager.createQuery(criteriaQuery).getResultList();

        offlineHospitals = offlineHospitals.stream().distinct().collect(Collectors.toList());
        List<Hospital> hospitals = Lists.newArrayList();
        for (OfflineHospital o : offlineHospitals) {
            hospitals.addAll(o.getHospitals());
        }
        hospitals = hospitals.stream().distinct().collect(Collectors.toList());
        HospitalPublicPlatformRepository hospitalPublicPlatformRepository = AppContext.getInstance(HospitalPublicPlatformRepository.class);
        Map<Hospital, List<OpenMiniAppVM>> hospitalMap = new HashMap<>();
        for (Hospital hospital : hospitals) {
            List<OpenMiniAppVM> openMini = hospitalPublicPlatformRepository.findAllByHospitalAndPlatformTypeAndPlatformFor(hospital,
                            PlatformTypeEnum.MINI, HospitalPublicPlatform.PlatformForEnum.PATIENT)
                    .stream().map(OpenMiniAppVM::new).collect(Collectors.toList());
            if (!openMini.isEmpty()) {
                hospitalMap.put(hospital, openMini);
            }
        }

        List<MiniAppOfflineHospitalVM> vms = offlineHospitals.stream()
                .map(u -> {
                    MiniAppOfflineHospitalVM o = new MiniAppOfflineHospitalVM(u);
                    List<OpenMiniAppVM> opens = Lists.newArrayList();
                    for (Hospital v : u.getHospitals()) {
                        if (hospitalMap.containsKey(v)) {
                            opens.addAll(hospitalMap.get(v));
                        }
                    }
                    opens = opens.stream().distinct().collect(Collectors.toList());
                    o.setOpenMiniApps(opens);
                    if (lngLat != null && u.getLatitude() != null && u.getLongitude() != null) {
                        try {
                            LngLat hll = new LngLat(u.getLongitude(), u.getLatitude());
                            o.setDistance((int) GeoUtils.calculateLineDistance(lngLat, hll));
                        } catch (Exception e) {
                            log.error("医院经纬度不正确: offlineHospitalId=" + u.getHospitals());
                        }
                    }
                    if (o.getDistance() == null) {
                        o.setDistance(99999999);
                    }
                    return o;
                })
                .filter(u -> !u.getOpenMiniApps().isEmpty())
                .sorted(Comparator.comparing(MiniAppOfflineHospitalVM::getDistance)
                        .thenComparing(MiniAppOfflineHospitalVM::getId))
                .collect(Collectors.toList());
        vms.forEach(u -> {
            if (u.getDistance() == 99999999) {
                u.setDistance(null);
            }
        });

        return vms;
    }

    // demo
//    // 输入的固定经纬度
//    double inputLatitude = 40.7128; // 例如，纬度
//    double inputLongitude = -74.0060; // 例如，经度
//    double maxDistance = 10.0; // 最大距离（以某个单位表示，如千米）
//
//    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
//    CriteriaQuery<Entity> query = criteriaBuilder.createQuery(Entity.class);
//    Root<Entity> root = query.from(Entity.class);
//
//    Metamodel metamodel = entityManager.getMetamodel();
//    EntityType<Entity> entityEntityType = metamodel.entity(Entity.class);
//    SingularAttribute<Entity, Double> latitudeAttribute = entityEntityType.getDeclaredSingularAttribute("latitude", Double.class);
//    SingularAttribute<Entity, Double> longitudeAttribute = entityEntityType.getDeclaredSingularAttribute("longitude", Double.class);
//
//    // 使用公式计算距离
//    Expression<Double> distanceExpression = criteriaBuilder.sqrt(
//            criteriaBuilder.sum(
//                    criteriaBuilder.prod(
//                            criteriaBuilder.diff(
//                                    criteriaBuilder.literal(inputLatitude),
//                                    root.get(latitudeAttribute)
//                            ),
//                            criteriaBuilder.diff(
//                                    criteriaBuilder.literal(inputLatitude),
//                                    root.get(latitudeAttribute)
//                            )
//                    ),
//                    criteriaBuilder.prod(
//                            criteriaBuilder.diff(
//                                    criteriaBuilder.literal(inputLongitude),
//                                    root.get(longitudeAttribute)
//                            ),
//                            criteriaBuilder.diff(
//                                    criteriaBuilder.literal(inputLongitude),
//                                    root.get(longitudeAttribute)
//                            )
//                    )
//            )
//    );
//
//    Predicate distancePredicate = criteriaBuilder.le(distanceExpression, maxDistance);
//
//query.where(distancePredicate);
//query.orderBy(criteriaBuilder.asc(distanceExpression));
//
//    TypedQuery<Entity> typedQuery = entityManager.createQuery(query);
//    List<Entity> result = typedQuery.getResultList();
    @Override
    public void addRecommendHospital(OfflineHospitalRecommendVM vm) {
        OfflineHospital offlineHospital = offlineHospitalRepository.getById(vm.getOfflineHospital().getId());
        OfflineHospitalRecommend recommend = offlineHospitalRecommendRepository.findOneByOfflineHospital(offlineHospital)
                .orElseGet(OfflineHospitalRecommend::new);
        recommend.setOfflineHospital(offlineHospital);
        recommend.setSortNum(vm.getSortNum());
        offlineHospitalRecommendRepository.save(recommend);
    }

    @Override
    public void addOfflineHospitalAnnualInspection(OfflineHospitalAnnualInspectionVM vm) {
        OfflineHospitalAnnualInspection offlineHospitalAnnualInspection = vm.toEntity();
        OfflineHospital offlineHospital = offlineHospitalRepository.getById(vm.getOfflineHospital().getId());
        if (vm.getAnnualInspectionFile() != null) {
            Upload file = AppContext.getInstance(UploadRepository.class).getById(vm.getAnnualInspectionFile().getId());
            offlineHospitalAnnualInspection.setAnnualInspectionFile(file);
        }
        if (vm.getThreelevelEqualprotectionFile() != null) {
            Upload file = AppContext.getInstance(UploadRepository.class).getById(vm.getThreelevelEqualprotectionFile().getId());
            offlineHospitalAnnualInspection.setThreelevelEqualprotectionFile(file);
        }
        offlineHospitalAnnualInspection.setOfflineHospital(offlineHospital);
        offlineHospitalAnnualInspectionRepository.save(offlineHospitalAnnualInspection);
    }

    @Override
    public void saveRecommendHospital(long recommendId, OfflineHospitalRecommendVM vm) {
        OfflineHospitalRecommend recommend = offlineHospitalRecommendRepository.getById(recommendId);
        OfflineHospital offlineHospital = offlineHospitalRepository.getById(vm.getOfflineHospital().getId());
        recommend.setOfflineHospital(offlineHospital);
        recommend.setSortNum(vm.getSortNum());
        offlineHospitalRecommendRepository.save(recommend);
    }

    @Override
    public void deleteRecommendHospital(long recommendId) {
        offlineHospitalRecommendRepository.deleteById(recommendId);
    }

    @Override
    public List<OfflineHospitalDTO> getOfflineHospitalList(String query, Double longitude, Double latitude) {
        LngLat lngLat = latitude == null || longitude == null ? null : new LngLat(longitude, latitude);
        OffHospitalSearch search = OffHospitalSearch.of(query);
        Specification<OfflineHospital> sp = search.toSpecification();
        RecommendFilter recommendFilter = search.getRecommendFilter();
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<OfflineHospital> criteriaQuery = criteriaBuilder.createQuery(OfflineHospital.class);
        Root<OfflineHospital> rootHospital = criteriaQuery.from(OfflineHospital.class);
        Predicate p = criteriaBuilder.and(criteriaBuilder.isTrue(rootHospital.get("enabled")));
        if (sp == null) {
            criteriaQuery.where(p);
        } else {
            Predicate condition = sp.toPredicate(rootHospital, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(condition, p);
        }
        List<OfflineHospital> offlineHospitals = entityManager.createQuery(criteriaQuery).getResultList();
        offlineHospitals = offlineHospitals.stream().distinct().collect(Collectors.toList());
        List<OfflineHospitalDTO> offlineHospitalDTOS = offlineHospitals.stream()
                .map(u -> {
                    OfflineHospitalDTO o = new OfflineHospitalDTO(u);
                    if (lngLat != null && u.getLatitude() != null && u.getLongitude() != null) {
                        try {
                            LngLat hll = new LngLat(u.getLongitude(), u.getLatitude());
                            o.setDistance((int) GeoUtils.calculateLineDistance(lngLat, hll));
                        } catch (Exception e) {
                            log.error("医院经纬度不正确: offlineHospitalId=" + u.getHospitals());
                        }
                    }
                    if (o.getDistance() == null) {
                        o.setDistance(99999999);
                    }
                    return o;
                }).collect(Collectors.toList());
        if (recommendFilter == null) {
            // recommendFilter != null时用数据库的排序，这里不处理
            offlineHospitalDTOS =  offlineHospitalDTOS.stream().sorted(Comparator.comparing(OfflineHospitalDTO::getDistance)
                            .thenComparing(OfflineHospitalDTO::getId))
                    .collect(Collectors.toList());
        }
        offlineHospitalDTOS.forEach(u -> {
            if (u.getDistance() == 99999999) {
                u.setDistance(null);
            }
        });
        return offlineHospitalDTOS;
    }

    @Override
    public int addressDistance(Hospital hospital, LngLat lngLat) {
        OfflineHospital offlineHospital = hospital.getOfflineHospitals().stream().findFirst().orElse(null);
        if (offlineHospital == null) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("该互联网医院未绑定实体医院");
        }
        if (offlineHospital.getLatitude() == null || offlineHospital.getLongitude() == null) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("实体医院缺少经纬度信息");
        }
        LngLat end = new LngLat(offlineHospital.getLongitude(), offlineHospital.getLatitude());
        return (int) GeoUtils.calculateLineDistance(lngLat, end);
    }
}
