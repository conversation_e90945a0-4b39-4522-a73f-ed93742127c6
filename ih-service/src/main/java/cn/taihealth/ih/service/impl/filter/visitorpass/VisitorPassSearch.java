package cn.taihealth.ih.service.impl.filter.visitorpass;

import cn.taihealth.ih.domain.VisitorPass;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class VisitorPassSearch extends SearchCriteria<VisitorPass> {

    public static VisitorPassSearch of(String query) {
        VisitorPassSearch visitorPassSearch = new VisitorPassSearch();
        visitorPassSearch.parse(query);
        return visitorPassSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<VisitorPass> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier
                .valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case STARTDATE:
                return new VisitorPassStartDateFilter<>(value);
            case REGNO:
                return new VisitorPassRegnoFilter(value);
            case PATNAME:
                return new VisitorPassPatnameFilter(value);
            case PAT_CERTIFICATE_NO:
                return new VisitorPassPatCertNoFilter(value);
            case ATTENDANT_NAME:
                return new VisitorPassAttendantNameFilter(value);
            case ATTENDANT_CERTIFICATE_NO:
                return new VisitorPassAttendantCertNoFilter(value);
            case STATUS:
                VisitorPass.VisitorPassStatusEnum statusEnum = VisitorPass.VisitorPassStatusEnum.valueOf(value);
                return new VisitorPassStatusFilter(statusEnum);
            default:
                return null;
        }
    }


    @Override
    protected SearchFilter<VisitorPass> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        STARTDATE,
        REGNO,
        PATNAME,
        PAT_CERTIFICATE_NO,
        ATTENDANT_NAME,
        ATTENDANT_CERTIFICATE_NO,
        STATUS
    }
}
