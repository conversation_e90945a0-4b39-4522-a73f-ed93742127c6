package cn.taihealth.ih.service.impl.filter.nursing.homeitem;

import cn.taihealth.ih.domain.nursing.NursingHomeItem;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

/**
 */
public class HomeItemClassifyFilter implements SearchFilter<NursingHomeItem> {

    private final long itemClassifyId;

    public HomeItemClassifyFilter(long itemClassifyId) {
        this.itemClassifyId = itemClassifyId;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<NursingHomeItem> toSpecification() {
        return Specifications.eq("itemClassify.id", itemClassifyId);
    }

    @Override
    public String toExpression() {
        return "item_classify_id = " + itemClassifyId;
    }

    @Override
    public boolean isValid() {
        return true;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof HomeItemClassifyFilter)) {
            return false;
        }

        HomeItemClassifyFilter rhs = (HomeItemClassifyFilter) other;
        return Objects.equals(itemClassifyId, rhs.itemClassifyId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(itemClassifyId);
    }
}
