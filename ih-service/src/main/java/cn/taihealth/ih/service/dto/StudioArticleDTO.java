package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.Studio;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.Article;
import cn.taihealth.ih.domain.hospital.Article.ArticleClient;
import cn.taihealth.ih.domain.hospital.ArticleAuthor;
import cn.taihealth.ih.domain.hospital.ArticleCategory;
import cn.taihealth.ih.domain.hospital.StudioArticle;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Getter
@Setter
public class StudioArticleDTO extends UpdatableDTO {

    @ApiModelProperty("医院")
    private HospitalDTO hospital;

    @ApiModelProperty("名医工作室")
    private StudioDTO studio;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("封面url")
    private String coverImageUrl;

    @ApiModelProperty("是否显示")
    private boolean show;

    @ApiModelProperty("创建者")
    private UserDTO creator;

    public StudioArticleDTO(StudioArticle studioArticle) {
        super(studioArticle);
        this.title = studioArticle.getTitle();
        this.content = studioArticle.getContent();
        this.coverImageUrl = studioArticle.getCoverImageUrl();
        this.show = studioArticle.isShow();

        this.hospital = new HospitalDTO(studioArticle.getHospital());
        this.studio = new StudioDTO(studioArticle.getStudio(),false);
        this.creator = new UserDTO(studioArticle.getCreator());

    }
}
