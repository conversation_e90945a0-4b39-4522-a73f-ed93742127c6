package cn.taihealth.ih.service.vm.statistics.evaluation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderStatisticsTopVM {

    // 图文问诊订单数
    @ApiModelProperty(value = "图文问诊订单数")
    private Integer graphicConsultationCount;
    @ApiModelProperty(value = "图文问诊订单数环比")
    private String graphicConsultationCompare;

    // 图文问诊总体完成率
    @ApiModelProperty(value = "图文问诊总体完成率")
    private String graphicConsultationCompletionRate;
    @ApiModelProperty(value = "图文问诊总体完成率环比")
    private String graphicConsultationCompletionRateCompare;

    // 图文问诊订单完成总数
    @ApiModelProperty(value = "图文问诊订单完成总数")
    private Integer graphicConsultationCompletedCount;
    @ApiModelProperty(value = "图文问诊订单完成总数环比")
    private String graphicConsultationCompletedCountCompare;

    // 今日新增图文问诊订单数
    @ApiModelProperty(value = "今日新增图文问诊订单数")
    private Integer graphicConsultationTodayCount;
    @ApiModelProperty(value = "今日新增图文问诊订单数环比")
    private String graphicConsultationTodayCountCompare;

    // 视频问诊订单数
    @ApiModelProperty(value = "视频问诊订单数")
    private Integer videoConsultationCount;
    @ApiModelProperty(value = "视频问诊订单数环比")
    private String videoConsultationCompare;

    // 视频问诊总体完成率
    @ApiModelProperty(value = "视频问诊总体完成率")
    private String videoConsultationCompletionRate;
    @ApiModelProperty(value = "视频问诊总体完成率环比")
    private String videoConsultationCompletionRateCompare;

    // 视频问诊订单完成总数
    @ApiModelProperty(value = "视频问诊订单完成总数")
    private Integer videoConsultationCompletedCount;
    @ApiModelProperty(value = "视频问诊订单完成总数环比")
    private String videoConsultationCompletedCountCompare;

    // 今日新增视频问诊订单数
    @ApiModelProperty(value = "今日新增视频问诊订单数")
    private Integer videoConsultationTodayCount;
    @ApiModelProperty(value = "今日新增视频问诊订单数环比")
    private String videoConsultationTodayCountCompare;

    // 开具处方总数
    @ApiModelProperty(value = "开具处方总数")
    private Integer prescriptionCount;
    @ApiModelProperty(value = "开具处方总数环比")
    private String prescriptionCountCompare;

    // 处方支付总数
    @ApiModelProperty(value = "处方支付总数")
    private Integer prescriptionPaidCount;
    @ApiModelProperty(value = "处方支付总数环比")
    private String prescriptionPaidCountCompare;

}
