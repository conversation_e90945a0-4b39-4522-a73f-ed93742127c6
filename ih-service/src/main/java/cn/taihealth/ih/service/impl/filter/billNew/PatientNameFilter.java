package cn.taihealth.ih.service.impl.filter.billNew;

import cn.taihealth.ih.domain.hospital.BillNew;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import lombok.Getter;
import org.springframework.data.jpa.domain.Specification;

@Getter
public class PatientNameFilter implements SearchFilter<BillNew> {
    private final String patientName;

    public PatientNameFilter(String patientName) {
        this.patientName = patientName;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<BillNew> toSpecification() {
        return Specifications.like("patientName", patientName);
    }

    @Override
    public String toExpression() {
        return "patientName: " + patientName;
    }

    @Override
    public boolean isValid() {
        return patientName != null;
    }
}
