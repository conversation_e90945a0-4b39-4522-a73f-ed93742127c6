package cn.taihealth.ih.service.impl.filter.hisoutpatientcharge;

import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.his.HisOutpatientChargeGroup;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import java.util.Objects;
import javax.persistence.criteria.Join;
import org.springframework.data.jpa.domain.Specification;

public class RecipeNoFilter implements SearchFilter<HisOutpatientCharge> {

    private final String name;

    public RecipeNoFilter(String id) {
        this.name = id;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<HisOutpatientCharge> toSpecification() {
        return Specifications.eq("recipeNo", name);
    }

    @Override
    public String toExpression() {
        return "recipeNo:" + name;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(name);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof RecipeNoFilter)) {
            return false;
        }

        RecipeNoFilter rhs = (RecipeNoFilter) other;
        return Objects.equals(name, rhs.name);
    }
}
