package cn.taihealth.ih.service.vm.hospital;

import com.google.common.collect.Lists;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: Moon
 * @Date: 2021/5/24 下午2:52
 */
public class HospitalDataInitStageVM implements Serializable {

    public enum Stage {
        NOT_START,
        DEPT,
        DISEASE,
        MEDICAL_TEMPLATE,
        MEDICAL,
        CRM_QUESTION,
        ROLE,
        SYSTEM_SETTING,
        DICTIONARY,
        COMMON_MESSAGE,
        FINISHED
    }

    private List<Stage> finishedStages = Lists.newArrayList();

    private Stage runningStage = Stage.NOT_START;

    public List<Stage> getFinishedStages() {
        return finishedStages;
    }

    public void setFinishedStages(List<Stage> finishedStages) {
        this.finishedStages = finishedStages;
    }

    public Stage getRunningStage() {
        return runningStage;
    }

    public void setRunningStage(Stage runningStage) {
        this.runningStage = runningStage;
    }
}
