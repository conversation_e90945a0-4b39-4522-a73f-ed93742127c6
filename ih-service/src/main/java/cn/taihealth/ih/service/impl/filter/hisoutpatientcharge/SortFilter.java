package cn.taihealth.ih.service.impl.filter.hisoutpatientcharge;

import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.his.HisOutpatientChargeGroup;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

public class SortFilter implements SearchFilter<HisOutpatientCharge> {
    public static final String NAME = "HisOutpatientCharge-sort-filter";

    private List<SortField> fields = Lists.newArrayList();

    public enum Field {
        /**
         * 创建时间
         */
        CREATEDDATE("createdDate"),
        /**
         * 更新时间
         */
        UPDATEDDATE("updatedDate");

        final String fieldName;

        Field(String fieldName) {
            this.fieldName = fieldName;
        }
    }

    public static class SortField {
        private final Field field;
        private final boolean asc;

        public SortField(Field field, boolean asc) {
            this.field = field;
            this.asc = asc;
        }

        public Field getField() {
            return field;
        }

        public boolean isAsc() {
            return asc;
        }

        public Sort.Order toOrder() {
            return new Sort.Order(asc ? Sort.Direction.ASC : Sort.Direction.DESC, field.fieldName);
        }
    }

    public SortFilter(String values) {
        String sortStr = values;
        if (sortStr.startsWith("\"")) {
            sortStr = sortStr.substring(1);
        }
        if (sortStr.endsWith("\"")) {
            sortStr = sortStr.substring(0, sortStr.length() - 1);
        }
        for (String value : sortStr.split(" ")) {
            int pos = value.indexOf('-');
            String field;
            boolean fieldAsc;
            if (pos > 0) {
                field = value.substring(0, pos);
                String a = value.substring(pos + 1);
                fieldAsc = "asc".equalsIgnoreCase(a);
            } else {
                field = value;
                fieldAsc = false;
            }

            try {
                fields.add(new SortField(Field.valueOf(field.toUpperCase()), fieldAsc));
            } catch (IllegalArgumentException e) {
            }
        }
    }

    @Override
    public boolean isValid() {
        return fields != null;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Specification<HisOutpatientCharge> toSpecification() {
        if (fields.isEmpty()) {
            return null;
        } else {
            return Specifications.orderBy(
                    Sort.by(
                            fields.stream().map(SortField::toOrder).collect(Collectors.toList())
                    )
            );
        }
    }

    @Override
    public String toExpression() {
        if (fields != null) {
            return "sort:\"" +
                    fields.stream()
                            .map(u -> u.getField().fieldName.toLowerCase() + "-" + (u.isAsc() ? "asc" : "desc"))
                            .collect(Collectors.joining(" "))
                    + "\"";
        } else {
            return "";
        }
    }

    public List<SortField> getFields() {
        return fields;
    }

    public void setFields(List<SortField> fields) {
        this.fields = fields;
    }

    @Override
    public int hashCode() {
        return Objects.hash(fields);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (!(obj instanceof SortFilter)) {
            return false;
        }

        SortFilter f = (SortFilter) obj;
        return Objects.equals(f.fields, fields);
    }

    @Override
    public String toString() {
        return toExpression();
    }
}
