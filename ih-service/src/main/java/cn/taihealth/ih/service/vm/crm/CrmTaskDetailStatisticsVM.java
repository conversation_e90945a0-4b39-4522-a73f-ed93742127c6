package cn.taihealth.ih.service.vm.crm;

import cn.taihealth.ih.domain.crm.CrmTaskDetail;
import cn.taihealth.ih.domain.enums.CrmStatus;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import cn.taihealth.ih.service.vm.healthhistory.PatientDiseaseVM;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
public class CrmTaskDetailStatisticsVM extends UpdatableDTO {

    @ApiModelProperty("就诊人信息")
    private PatientDiseaseVM patient;

    @ApiModelProperty("状态 CREATED or STARTED-待随访,FINISHED-完成,TERMINATED-终止;")
    private CrmStatus status;

    @ApiModelProperty("终止原因")
    private String terminateReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("随访计划名")
    private String planName;

    public CrmTaskDetailStatisticsVM(CrmTaskDetail detail) {
        super(detail);
        this.status = detail.getStatus();
        this.planName = detail.getPlan().getName();
    }

}
