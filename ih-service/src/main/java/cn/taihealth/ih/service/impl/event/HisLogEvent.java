package cn.taihealth.ih.service.impl.event;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.aspectj.lang.ProceedingJoinPoint;

@Getter
@Setter
public class HisLogEvent extends AbstractEntityEvent<ProceedingJoinPoint> {
    private boolean success;
    private String errMsg;
    private Object result;

    public HisLogEvent(ProceedingJoinPoint point, boolean success, String errMsg) {
        super(point);
        this.success = success;
        this.errMsg = errMsg;
    }

    public HisLogEvent(ProceedingJoinPoint point, boolean success, Object result) {
        super(point);
        this.success = success;
        this.result = result;
    }

}
