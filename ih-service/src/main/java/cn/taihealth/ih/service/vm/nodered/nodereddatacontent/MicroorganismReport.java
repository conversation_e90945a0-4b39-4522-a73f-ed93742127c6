package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-09-07
 */
@Data
public class MicroorganismReport implements Serializable {
    @ApiModelProperty("报告单号	Y")
    private String report_no;
    @ApiModelProperty("就诊流水号	Y")
    private String regno;
    @ApiModelProperty("就诊类别	Y	1门诊 2住院")
    private String user_source;
    @ApiModelProperty("报告类别代码	Y")
    private String report_type_code;
    @ApiModelProperty("报告类别名称	Y")
    private String report_type_name;
    @ApiModelProperty("标本类型	N")
    private String specimen;
    @ApiModelProperty("申请时间	N")
    private String apply_time;
    @ApiModelProperty("送检时间	N")
    private String censorship_time;
    @ApiModelProperty("报告发布时间	N")
    private String report_time;
    @ApiModelProperty("申请科室名称	N")
    private String apply_dept_name;
    @ApiModelProperty("申请医生名称	N")
    private String apply_doctor_name;
    @ApiModelProperty("审核医生名称	N")
    private String check_doctor_name;
    @ApiModelProperty("执行科室名称	N")
    private String exec_dept_name;
    @ApiModelProperty("执行医生名称	N")
    private String exec_doctor_name;
    @ApiModelProperty("备注	N")
    private String memo;
}
