package cn.taihealth.ih.service.vm.nodered.nodereddatacontent.appointment.request;

import lombok.Data;

import java.io.Serializable;


@Data
public class DictChangeMsgReq implements Serializable {
    // 渠道编码	Y
    private String channel_code;
    // 医院代码	Y
    private String organ_code;
    private String msg_type;
    private String timestamp;
    private String msg_details;
    private String request_id;
}
