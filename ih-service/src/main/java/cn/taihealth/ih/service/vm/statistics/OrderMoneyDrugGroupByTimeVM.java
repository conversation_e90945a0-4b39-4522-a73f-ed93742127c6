package cn.taihealth.ih.service.vm.statistics;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * 根据时间和类型展示金额
 * @Author: Moon
 * @Date: 2020/11/20 上午10:24
 */
public class OrderMoneyDrugGroupByTimeVM {

    @ApiModelProperty("时间")
    private String time;

    @ApiModelProperty("时间总金额")
    private String totalMoney;

    @ApiModelProperty("处方类型分类结果集")
    private List<OrderMoneyDrugGroupByTypeVM> orderMoneyDrugGroupByTypeVMS;

    public OrderMoneyDrugGroupByTimeVM(String time, String diseaseTotalMoney,
                                       List<OrderMoneyDrugGroupByTypeVM> orderMoneyDrugGroupByTypeVMS) {
        this.time = time;
        this.totalMoney = diseaseTotalMoney;
        this.orderMoneyDrugGroupByTypeVMS = orderMoneyDrugGroupByTypeVMS;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(String totalMoney) {
        this.totalMoney = totalMoney;
    }

    public List<OrderMoneyDrugGroupByTypeVM> getOrderMoneyDrugGroupByTypeVMS() {
        return orderMoneyDrugGroupByTypeVMS;
    }

    public void setOrderMoneyDrugGroupByTypeVMS(
        List<OrderMoneyDrugGroupByTypeVM> orderMoneyDrugGroupByTypeVMS) {
        this.orderMoneyDrugGroupByTypeVMS = orderMoneyDrugGroupByTypeVMS;
    }
}
