package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.service.api.CacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

/**
 */
@Service
public class CacheServiceImpl implements CacheService {

    private static final Logger log = LoggerFactory.getLogger(CacheServiceImpl.class);

    public static final String USER_HIBERNATE_CACHE = "cn.taihealth.ih.domain.cloud.User";

    public CacheServiceImpl() {
    }

    @Override
    @CacheEvict(value = { Constants.CACHE_USER_USERNAMES, Constants.CACHE_USER_MOBILES, USER_HIBERNATE_CACHE }, allEntries = true)
    public void evictUsers() {
        log.info("Clearing users cache ...");
    }

    @Override
    @CacheEvict(value = { Constants.CACHE_SYSTEM_SETTINGS}, allEntries = true)
    public void evictSystemSettings() {
        log.info("Clearing system settings cache ...");
    }

    @Override
    @CacheEvict(value = { Constants.CACHE_DOCS}, allEntries = true)
    public void evictDocs() {
        log.info("Clearing docs cache ...");
    }

    @Override
    @CacheEvict(value = { Constants.EXAM_COUNTER_CACHE }, allEntries = true)
    public void evictExamCounterCache() {
        log.info("Clearing exam counter cache ...");
    }


    @Override
    @CacheEvict(value = { Constants.CACHE_ARTICLE_CATEGORIES}, allEntries = true)
    public void evictCategories() {
    }
}
