package cn.taihealth.ih.service.vm.crm;

import cn.taihealth.ih.bean.statistics.CrmPlanResult;
import cn.taihealth.ih.commons.Constants;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.util.Date;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 随访-计划-计划表
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
public class CrmStatisticsPlanVM {

    @ApiModelProperty("计划名")
    @NotEmpty(message = "计划名必填")
    private String name;

    @ApiModelProperty("计划描述")
    private String description;

    @ApiModelProperty("随访天数")
    private int daysCount;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("随访完成人数")
    private int finishedNumber;

    @ApiModelProperty("随访总人数")
    private int usedNumber;

    @ApiModelProperty("完成率，小数例如： 0.5 0.877 需自行处理")
    private float finishedRating;

    public CrmStatisticsPlanVM(CrmPlanResult plan) {
        this.name = plan.getName();
        this.description = plan.getDescription();
        this.daysCount = getDayCount(plan.getCreatedDate());
        this.finishedNumber = plan.getFinishedNumber();
        this.usedNumber = plan.getUsedNumber();
        this.startTime = plan.getCreatedDate();
        this.finishedRating = plan.getFinishedRating();
    }

    private int getDayCount(Date createdDate) {
        LocalDate d1 = createdDate.toInstant().atZone(Constants.defaultZoneId).toLocalDate();
        LocalDate d2 = LocalDate.now();
        return (int) (d2.toEpochDay() - d1.toEpochDay()) + 1;
    }
}
