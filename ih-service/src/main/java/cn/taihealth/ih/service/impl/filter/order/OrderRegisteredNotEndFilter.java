package cn.taihealth.ih.service.impl.filter.order;

import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import cn.taihealth.ih.service.impl.order.OrderConstants;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import java.util.List;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class OrderRegisteredNotEndFilter implements SearchFilter<Order> {

    public OrderRegisteredNotEndFilter() {
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Order> toSpecification() {
        List<Specification<Order>> statusList = Lists.newArrayList();
        for (Order.OrderStatus status : OrderConstants.ORDER_STATUS_REGISTERED_NOT_END) {
            statusList.add(Specifications.eq("status", status));
        }
        return Specifications.or(statusList);
    }

    @Override
    public String toExpression() {
        return "order: stats";
    }

    @Override
    public boolean isValid() {
        return true;
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        return other instanceof OrderRegisteredNotEndFilter;
    }
}
