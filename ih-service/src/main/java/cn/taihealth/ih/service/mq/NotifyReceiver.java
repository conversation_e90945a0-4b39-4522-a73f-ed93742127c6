package cn.taihealth.ih.service.mq;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.AbstractEntity;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.crm.*;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.mq.config.RabbitMQChannelConfig;
import cn.taihealth.ih.mq.consts.CommonConst;
import cn.taihealth.ih.mq.entity.dto.*;
import cn.taihealth.ih.mq.entity.message.HisDicDiagnoseMsg;
import cn.taihealth.ih.mq.entity.message.QuestionChangeMsg;
import cn.taihealth.ih.mq.entity.message.QuestionnaireChangeMsg;
import cn.taihealth.ih.mq.enums.MessageTypeEnum;
import cn.taihealth.ih.mq.utils.MQUtil;
import cn.taihealth.ih.repo.UserRepository;
import cn.taihealth.ih.repo.crm.*;
import cn.taihealth.ih.service.api.HospitalService;
import cn.taihealth.ih.service.api.NoticeService;
import cn.taihealth.ih.service.cache.HospitalCache;
import cn.taihealth.ih.service.util.JpaUtils;
import com.alibaba.fastjson.JSONObject;
import com.gitq.jedi.context.AppContext;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * MQ Receiver - NotifyReceiver
 */
@Slf4j
@Component
public class NotifyReceiver {

    private final NoticeService noticeService;
    private final UserRepository userRepository;
    private final HospitalService hospitalService;
    private final CrmQuestionnaireQuestionRepository crmQuestionnaireQuestionRepository;
    private final CrmQuestionnaireRepository crmQuestionnaireRepository;
    private final CrmQuestionRepository crmQuestionRepository;

    private final CrmTaskDetailRepository crmTaskDetailRepository;
    private final CrmTaskDetailAnswerRepository crmTaskDetailAnswerRepository;
    private final CrmTaskDetailResultRepository crmTaskDetailResultRepository;

    public NotifyReceiver(NoticeService noticeService, UserRepository userRepository,
                          HospitalService hospitalService,
                          CrmQuestionnaireQuestionRepository crmQuestionnaireQuestionRepository,
                          CrmQuestionnaireRepository crmQuestionnaireRepository,
                          CrmQuestionRepository crmQuestionRepository,
                          CrmTaskDetailRepository crmTaskDetailRepository,
                          CrmTaskDetailAnswerRepository crmTaskDetailAnswerRepository,
                          CrmTaskDetailResultRepository crmTaskDetailResultRepository) {
        this.noticeService = noticeService;
        this.userRepository = userRepository;
        this.hospitalService = hospitalService;
        this.crmQuestionnaireQuestionRepository = crmQuestionnaireQuestionRepository;
        this.crmQuestionnaireRepository = crmQuestionnaireRepository;
        this.crmQuestionRepository = crmQuestionRepository;
        this.crmTaskDetailRepository = crmTaskDetailRepository;
        this.crmTaskDetailAnswerRepository = crmTaskDetailAnswerRepository;
        this.crmTaskDetailResultRepository = crmTaskDetailResultRepository;
    }

    @RabbitListener(queues = RabbitMQChannelConfig.NOTIFY_QUEUE)
    public void processNotifyMsg(Message message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        try {
            JSONObject payload = MQUtil.byteArray2JsonObject(message.getBody());
            JSONObject msgHeader = payload.getJSONObject(CommonConst.MSG_HEADER_KEY);
            String messageType = msgHeader.getString(CommonConst.MSG_HEADER_MSG_TYPE_KEY);
            if (MessageTypeEnum.NOTIFY.getName().equalsIgnoreCase(messageType)) {
                handleNotifyMessage(payload);
//            } else if (MessageTypeEnum.DOC_TODO_NUM.getName().equalsIgnoreCase(messageType)) {
//                handleDocTodoMessage(payload);
            } else if (MessageTypeEnum.SYNC_HIS_DIC_DIAGNOSE.getName().equalsIgnoreCase(messageType)) {
                handleSyncHisDicDiagnose(payload);
            } else if (MessageTypeEnum.SYNC_HIS_DRUG_INFO.getName().equalsIgnoreCase(messageType)) {
                handleSyncHisDrugInfo(payload);
            } else if (MessageTypeEnum.HIS_SYNC_DEPT_DICT.getName().equalsIgnoreCase(messageType)) {
                handleSyncHisDept(payload);
            } else if (MessageTypeEnum.HIS_SYNC_DOC_DICT.getName().equalsIgnoreCase(messageType)) {
                handleSyncHisDoc(payload);
            }else if (MessageTypeEnum.QUESTION_CHANGE.getName().equalsIgnoreCase(messageType)) {
                handleQuestionChange(payload);
            } else if (MessageTypeEnum.QUESTIONNAIRES_CHANGE.getName().equalsIgnoreCase(messageType)) {
                handleQuestionnairesChange(payload);
            }
            else {
                log.warn("收到的消息类型: {}, 本队列处理的类型是: {}, 不做处理", messageType,
                         MessageTypeEnum.NOTIFY.getName());
            }
            channel.basicAck(tag, false);
        } catch (Exception e) {
            log.error("消息处理失败", e);
            try {
                channel.basicAck(tag, false);
                //后期再处理死信队列
                //channel.basicNack(tag, false, false);
            } catch (Exception ex) {
                log.error("消息转入 [通知业务死信队列]: {} 失败: {}", RabbitMQChannelConfig.NOTIFY_QUEUE,
                          ex.getMessage());
            }
        }
    }

    private void handleQuestionnairesChange(JSONObject payload) {
        log.info("Receiver [QuestionnairesChange] message: {}", payload);
        JpaUtils.executeInTransaction(() -> {
            QuestionnaireChangeMsg questionnaireChangeMsg = payload.getJSONObject(CommonConst.MSG_BODY_KEY)
                    .toJavaObject(QuestionnaireChangeMsg.class);
            CrmQuestionnaire questionnaire = crmQuestionnaireRepository.findById(questionnaireChangeMsg.getQuestionId()).orElseThrow();
            List<CrmTaskDetail> detailList = crmTaskDetailRepository.findAllByQuestionnaire(questionnaire);
            List<CrmQuestion> questionList = crmQuestionnaireQuestionRepository
                    .findAllByQuestionnaireAndDeletedIsFalse(questionnaire)
                    .stream().map(CrmQuestionnaireQuestion::getQuestion).collect(Collectors.toList());

            for (CrmTaskDetail crmTaskDetail : detailList) {
                List<CrmTaskDetailAnswer> answerList = crmTaskDetailAnswerRepository.findAllByTaskDetail(crmTaskDetail);
                int score = 0;
                for (CrmTaskDetailAnswer answer : answerList) {
                    CrmQuestion question = answer.getQuestion();
                    if (!questionList.contains(question)) {
                        crmTaskDetailAnswerRepository.delete(answer);
                    } else {
                        score += answer.getScore();
                    }
                }
                int finalScore = score;
                crmTaskDetailResultRepository.findOneByTaskDetail(crmTaskDetail).ifPresent(result -> {
                    result.setScore(finalScore);
                    crmTaskDetailResultRepository.save(result);
                });
            }
            return 0;
        });
    }

    private void handleQuestionChange(JSONObject payload) {
        log.info("Receiver [QuestionChange] message: {}", payload);
        JpaUtils.executeInTransaction(() -> {
            QuestionChangeMsg hisDicDiagnoseMsg = payload.getJSONObject(CommonConst.MSG_BODY_KEY)
                    .toJavaObject(QuestionChangeMsg.class);
            CrmQuestion question = crmQuestionRepository.findById(hisDicDiagnoseMsg.getQuestionId()).orElseThrow();
            List<CrmQuestionnaire> questionnaires = crmQuestionnaireQuestionRepository.findAllByQuestion(question)
                    .stream().filter(qq -> !qq.isDeleted()).map(CrmQuestionnaireQuestion::getQuestionnaire)
                    .map(AbstractEntity::getId).map(crmQuestionnaireRepository::getById).collect(Collectors.toList());
            for (CrmQuestionnaire questionnaire : questionnaires) {
                List<CrmTaskDetail> detailList = crmTaskDetailRepository.findAllByQuestionnaire(questionnaire);
                for (CrmTaskDetail crmTaskDetail : detailList) {
                    crmTaskDetailAnswerRepository.deleteByTaskDetailAndQuestion(crmTaskDetail, question);

                    List<CrmTaskDetailAnswer> taskDetailList = crmTaskDetailAnswerRepository.findAllByTaskDetail(crmTaskDetail);
                    int score = 0;
                    for (CrmTaskDetailAnswer answer : taskDetailList) {
                        score += answer.getScore();
                    }
                    int finalScore = score;
                    crmTaskDetailResultRepository.findOneByTaskDetail(crmTaskDetail).ifPresent(result -> {
                        result.setScore(finalScore);
                        crmTaskDetailResultRepository.save(result);
                    });
                }
            }
            return 0;
        });
    }

    private void handleSyncHisDept(JSONObject payload) {
        log.info("Receiver [SyncHisDept] message: {}", payload);
        HisDicDiagnoseMsg hisDicDiagnoseMsg = payload.getJSONObject(CommonConst.MSG_BODY_KEY)
            .toJavaObject(HisDicDiagnoseMsg.class);
        String hospitalCode = hisDicDiagnoseMsg.getHospitalCode();
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(hospitalCode)
            .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("Hospital不存在"));
        hospitalService.syncHisDept(hospital);
    }

    private void handleSyncHisDoc(JSONObject payload) {
        log.info("Receiver [SyncHisDoc] message: {}", payload);
        HisDicDiagnoseMsg hisDicDiagnoseMsg = payload.getJSONObject(CommonConst.MSG_BODY_KEY)
            .toJavaObject(HisDicDiagnoseMsg.class);
        String hospitalCode = hisDicDiagnoseMsg.getHospitalCode();
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(hospitalCode)
            .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("Hospital不存在"));
        hospitalService.syncHisDoc(hospital);
    }

    private void handleSyncHisDicDiagnose(JSONObject payload) {
        log.info("Receiver [SyncHisDicDiagnose] message: {}", payload);
        HisDicDiagnoseMsg hisDicDiagnoseMsg = payload.getJSONObject(CommonConst.MSG_BODY_KEY)
                .toJavaObject(HisDicDiagnoseMsg.class);
        String hospitalCode = hisDicDiagnoseMsg.getHospitalCode();
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(hospitalCode)
                .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("Hospital不存在"));
        hospitalService.syncHisDicDiagnose(hospital);
    }

    private void handleSyncHisDrugInfo(JSONObject payload) {
        log.info("Receiver [SyncHisDrugInfo] message: {}", payload);
        HisDicDiagnoseMsg hisDicDiagnoseMsg = payload.getJSONObject(CommonConst.MSG_BODY_KEY)
                .toJavaObject(HisDicDiagnoseMsg.class);
        String hospitalCode = hisDicDiagnoseMsg.getHospitalCode();
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(hospitalCode)
                .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("Hospital不存在"));
        hospitalService.syncHisDrugInfo(hospital);
    }

//    private void handleDocTodoMessage(JSONObject payload) {
//        log.info("Receiver [DocTodo] message: {}", payload);
//        //通知前端调用后端相应接口
//        TodoMsgCount todoMsgCount = payload.getJSONObject(CommonConst.MSG_BODY_KEY).toJavaObject(TodoMsgCount.class);
//        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(todoMsgCount.getHospitalCode()).orElse(null);
//        if (hospital == null) {
//            log.error("hospitalCode:{}不存在", todoMsgCount.getHospitalCode());
//            return;
//        }
//        User user = userRepository.findById(todoMsgCount.getUserId()).orElse(null);
//        if (user == null) {
//            log.error("userId:{}不存在", todoMsgCount.getUserId());
//            return;
//        }
//        if (!userService.isDoctor(user, hospital)) {
//            log.error("userId:{}不是医生", todoMsgCount.getUserId());
//            return;
//        }
//        medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).ifPresent(
//            medicalWorker -> tencentIMService.sendMessage(hospital, user, new MessageIm(Desc.CKD_DOC_TODO_COUNT, medicalWorker)));
//    }
    private void handleNotifyMessage(JSONObject payload) {
        log.info("Receiver [Notify] message: {}", payload);
        //NotifyDTO notify = message.getObject(CommonConst.MSG_BODY_KEY, NotifyDTO.class);
        NotifyDTO notify = payload.getJSONObject(CommonConst.MSG_BODY_KEY).toJavaObject(NotifyDTO.class);
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(notify.getHospitalCode())
            .orElseThrow(ErrorType.NO_HOSPITAL_ERROR::toProblem);
        String notifyKey = notify.getNotifyKey();
        String role = notify.getRole();
        JSONObject data = notify.getData();
        HospitalSettingKey hospitalSettingKey = Arrays.stream(HospitalSettingKey.values())
            .filter(key -> key.name().equalsIgnoreCase(notifyKey))
            .filter(key ->
                        Arrays.stream(key.getClientTypes())
                            .anyMatch(client -> client.name().equalsIgnoreCase(role)))
            .findAny().orElseThrow(ErrorType.MQ_MESSAGE_BODY_VALUE_NOT_MATCH::toProblem);

        switch (hospitalSettingKey) {
            //随访提醒
//            case NOTIFY_DISEASE_ACCOMPANY:
            case NOTIFY_DISEASE_ACCOMPANY_DOCTOR:
                handleAccompany(notify, data, hospital, hospitalSettingKey);
                break;
            //随访提醒
            case NOTIFY_ACCOMPANY:
                handleFollowUp(notify, data, hospital, hospitalSettingKey);
                break;
            //认证失败提醒
//            case NOTIFY_CERTIFY_CANCEL:
//            case NOTIFY_CERTIFY_FAILED:
//                handleCertifyCancel(notify, data, hospital, hospitalSettingKey);
//                break;
            //检测记录提醒
//            case NOTIFY_DAILY_RECORD:
//                handleDailyRecord(notify, data, hospital, hospitalSettingKey);
//                break;
            //指标异常通知
//            case NOTIFY_INDICATOR_ANOMALIES:
//            case NOTIFY_INDICATOR_ANOMALIES_DOCTOR:
//                handleIndicatorAnomalies(notify, data, hospital, hospitalSettingKey);
//                break;
            //审核结果通知
//            case NOTIFY_RESULT_CHECK:
//                handleResultCheck(notify, data, hospital, hospitalSettingKey);
//                break;
            default:
                log.warn("模板消息key: {}不符合规范, 不做处理", hospitalSettingKey);
        }
    }

    private void handleAccompany(NotifyDTO notify, JSONObject data, Hospital hospital,
                                 HospitalSettingKey hospitalSettingKey) {
        AccompanyDTO accompany = (AccompanyDTO) getJavaObject(data, AccompanyDTO.class);
        notify.getUserId().stream().map(id -> userRepository.findById(getLongId(id))).forEach(
            user -> user.ifPresent(u -> noticeService.noticeForAccompany(u, hospital, accompany, hospitalSettingKey)));
    }

    private void handleFollowUp(NotifyDTO notify, JSONObject data, Hospital hospital,
                                HospitalSettingKey hospitalSettingKey) {
        FollowUpDTO followUp = (FollowUpDTO) getJavaObject(data, FollowUpDTO.class);
        notify.getUserId().stream().map(id -> userRepository.findById(getLongId(id))).forEach(
            user -> user.ifPresent(u -> noticeService.noticeForFollowUp(u, hospital, followUp, hospitalSettingKey)));
    }

    private void handleDailyRecord(NotifyDTO notify, JSONObject data, Hospital hospital,
                                   HospitalSettingKey hospitalSettingKey) {
        String notice = data.getString("notice");
        if (StringUtils.isBlank(notice)) {
            log.error("通知内容不能为空");
            return;
        }
        notify.getUserId().stream().map(id -> userRepository.findById(getLongId(id))).forEach(
            user -> user.ifPresent(u -> noticeService.noticeForDailyRecord(u, hospital, notice, hospitalSettingKey)));
    }

    private ValidDTO getJavaObject(JSONObject data, Class<? extends ValidDTO> clazz) {
        ValidDTO javaData = data.toJavaObject(clazz);
        /*if (!javaData.valid()) {
            throw ErrorType.MQ_MESSAGE_BODY_VALUE_NOT_MATCH.toProblem("必要字段不能为空");
        }*/
        return javaData;
    }


    private long getLongId(String id) {
        long result = -1L;
        try {
            result = Long.parseLong(id);
        } catch (Exception e) {
            log.error("传入的id: {}格式有误,", id);
        }
        return result;
    }


}
