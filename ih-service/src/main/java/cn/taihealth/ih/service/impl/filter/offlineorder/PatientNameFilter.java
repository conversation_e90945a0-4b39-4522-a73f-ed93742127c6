package cn.taihealth.ih.service.impl.filter.offlineorder;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.*;

public class PatientNameFilter implements SearchFilter<OfflineOrder> {
    private final String value;

    public PatientNameFilter(String value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OfflineOrder> toSpecification() {
        return new Specification<OfflineOrder>() {
            @Override
            public Predicate toPredicate(Root<OfflineOrder> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
                // 创建 Patient 子查询
                Subquery<Long> patientSubquery = query.subquery(Long.class);
                Root<Patient> patientRoot = patientSubquery.from(Patient.class);
                patientSubquery.select(patientRoot.get("id"))
                        .where(criteriaBuilder.like(patientRoot.get("name"), "%" + value + "%"));

                return criteriaBuilder.in(root.get("patientId")).value(patientSubquery);
            }
        };
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
