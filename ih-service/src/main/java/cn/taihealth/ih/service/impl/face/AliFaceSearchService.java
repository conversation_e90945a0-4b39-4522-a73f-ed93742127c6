/*
package cn.taihealth.ih.service.impl.face;

import cn.taihealth.ih.domain.enums.SettingKey;
import cn.taihealth.ih.service.api.SystemSettingService;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import javax.inject.Inject;
import org.springframework.stereotype.Service;

*/
/**
 * @Author: Moon
 * @Date: 2020/10/21 6:32 下午
 *//*

@Service
public class AliFaceSearchService {


    //DefaultProfile.getProfile的参数分别是地域，access_key_id, access_key_secret
    private final DefaultProfile profile;
    private final DefaultAcsClient client;
    private final String groupName = "taiFaceStore";

    @Inject
    public AliFaceSearchService(SystemSettingService settingService) {
        this.profile = DefaultProfile.getProfile("cn-shanghai",
            settingService.getString(SettingKey.FACE_RECOGNITION_ACCESS_KEY_ID),
            settingService.getString(SettingKey.FACE_RECOGNITION_SECRET));
        this.client = new DefaultAcsClient(profile);
    }


    */
/**
     * AddFace接口用于向人脸库中添加人脸
     * @param person 添加人脸的姓名
     * @param image 添加人脸的编号
     * @param imageUrl 检测图片的URL
     *//*

    public CommonResponse AddFace(String person, String image, String imageUrl, String image64) {
        CommonRequest request = new CommonRequest();
        request.setMethod(MethodType.POST);
        request.setDomain("face.cn-shanghai.aliyuncs.com");
        request.setVersion("2018-12-03");
        request.setAction("AddFace");
        request.putBodyParameter("Group", groupName);
        request.putBodyParameter("Person", person);
        request.putBodyParameter("Image", image);
        if (imageUrl != null) {
            request.putBodyParameter("ImageUrl", imageUrl);
        }
        if (image64 != null) {
            request.putBodyParameter("Content", image64);  //检测图片的内容，Base64编码
        }
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            e.printStackTrace();
        }
        return response;
    }

    */
/**
     * DeleteFace接口用于从人脸库中删除人脸
     * @param person 添加人脸的姓名
     * @param image 添加人脸的编号
     * @throws ClientException
     *//*

    public CommonResponse DeleteFace(String person, String image)
        throws ClientException {
        CommonRequest request = new CommonRequest();
        request.setMethod(MethodType.POST);
        request.setDomain("face.cn-shanghai.aliyuncs.com");
        request.setVersion("2018-12-03");
        request.setAction("DeleteFace");
        request.putBodyParameter("Group", groupName);
        request.putBodyParameter("Person", person);
        request.putBodyParameter("Image", image);
        CommonResponse response = client.getCommonResponse(request);

        return response;

    }

    */
/**
     * ListFace接口用于列举注册库中的人脸
     * @throws ClientException
     *//*

    public CommonResponse ListFace() throws ClientException {
        CommonRequest request = new CommonRequest();
        request.setMethod(MethodType.POST);
        request.setDomain("face.cn-shanghai.aliyuncs.com");
        request.setVersion("2018-12-03");
        request.setAction("ListFace");
        request.putBodyParameter("Group", "taiFaceStore");
        CommonResponse response = client.getCommonResponse(request);
        return response;
    }

    */
/**
     * ListGroup接口用于列举人脸组
     * @throws ClientException
     *//*

    public CommonResponse ListGroup() throws ClientException {
        CommonRequest request = new CommonRequest();
        request.setMethod(MethodType.POST);
        request.setDomain("face.cn-shanghai.aliyuncs.com");
        request.setVersion("2018-12-03");
        request.setAction("ListGroup");
        CommonResponse response = client.getCommonResponse(request);
        return response;
    }

    */
/**
     * RecognizeFace接口用于查找注册库中的人脸
     * @param recognizeFaceImageUrl 需要查询的人类图片URL
     * @param recognizeFaceImage64 需要查询的人类图片64
     * @throws ClientException
     *//*

    public CommonResponse RecognizeFace(String recognizeFaceImageUrl, String recognizeFaceImage64)
        throws ClientException {
        CommonRequest request = new CommonRequest();
        request.setMethod(MethodType.POST);
        request.setDomain("face.cn-shanghai.aliyuncs.com");
        request.setVersion("2018-12-03");
        request.setAction("RecognizeFace");
        request.putBodyParameter("Group", groupName);
        if (recognizeFaceImageUrl != null) {
            request.putBodyParameter("ImageUrl", recognizeFaceImageUrl); //检测图片，url
        }
        if (recognizeFaceImage64 != null) {
            request.putBodyParameter("Content", recognizeFaceImage64);  //检测图片的内容，Base64编码
        }
        CommonResponse response = client.getCommonResponse(request);
        return response;
    }
}
*/
