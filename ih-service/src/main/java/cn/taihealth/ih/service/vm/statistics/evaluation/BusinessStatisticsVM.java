package cn.taihealth.ih.service.vm.statistics.evaluation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Moon
 * @Date: 2021年03月15日11:27:10
 */
@Data
@NoArgsConstructor
public class BusinessStatisticsVM {

    @ApiModelProperty("科室")
    private String dept;

    @ApiModelProperty("医生")
    private String doctor;

    @ApiModelProperty("接诊数")
    private Long orderCount;

    @ApiModelProperty("处方数")
    private Long prescriptionCount;

    @ApiModelProperty("检查单数")
    private Long checksCount;

    @ApiModelProperty("平均接诊时长 单位：分钟")
    private Long treatmentDuration;

    @ApiModelProperty("收益-接诊数")
    private String orderCountYield;

    @ApiModelProperty("收益-处方数")
    private String prescriptionCountYield;

    @ApiModelProperty("收益-检查单数")
    private String checksCountYield;

    @ApiModelProperty("收益-平均接诊时长")
    private String treatmentDurationYield;

    public BusinessStatisticsVM(Long orderCount, Long prescriptionCount, Long checksCount, Long treatmentDuration) {
        this.orderCount = orderCount;
        this.prescriptionCount = prescriptionCount;
        this.checksCount = checksCount;
        this.treatmentDuration = treatmentDuration;
    }

}
