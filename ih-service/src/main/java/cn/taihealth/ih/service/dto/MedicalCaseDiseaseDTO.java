package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.MedicalCaseDisease;
import io.swagger.annotations.ApiModelProperty;

public class MedicalCaseDiseaseDTO extends AbstractEntityDTO {

    @ApiModelProperty("疾病名称")
    private String diseaseName;

    @ApiModelProperty("疾病编码")
    private String diseaseCode;

    public MedicalCaseDiseaseDTO() {
    }

    public MedicalCaseDiseaseDTO(MedicalCaseDisease disease) {
        super(disease.getDisease());
        this.diseaseName = disease.getDiseaseName();
        this.diseaseCode = disease.getDiseaseCode();
    }

    public String getDiseaseName() {
        return diseaseName;
    }

    public void setDiseaseName(String diseaseName) {
        this.diseaseName = diseaseName;
    }

    public String getDiseaseCode() {
        return diseaseCode;
    }

    public void setDiseaseCode(String diseaseCode) {
        this.diseaseCode = diseaseCode;
    }

}
