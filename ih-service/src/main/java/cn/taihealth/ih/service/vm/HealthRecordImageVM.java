package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.HealthRecord;
import cn.taihealth.ih.service.dto.HealthRecordDTO;
import com.google.common.collect.Lists;
import java.util.List;

/**
 *
 */
public class HealthRecordImageVM extends HealthRecordDTO {

    public HealthRecordImageVM() {
    }

    public HealthRecordImageVM(HealthRecord healthRecord) {
        super(healthRecord);
    }

    private ImageCount bloodTypeImages = new ImageCount();

    private ImageCount allergyImages = new ImageCount();

    private ImageCount foodAllergyImages = new ImageCount();

    private ImageCount occupationalDiseasesImages = new ImageCount();

    private ImageCount geneticDiseasesImages = new ImageCount();

    private ImageCount medicationsImages = new ImageCount();

    private ImageCount medicalConditionsImages = new ImageCount();

    public ImageCount getBloodTypeImages() {
        return bloodTypeImages;
    }

    public void setBloodTypeImages(ImageCount bloodTypeImages) {
        this.bloodTypeImages = bloodTypeImages;
    }

    public ImageCount getAllergyImages() {
        return allergyImages;
    }

    public void setAllergyImages(ImageCount allergyImages) {
        this.allergyImages = allergyImages;
    }

    public ImageCount getFoodAllergyImages() {
        return foodAllergyImages;
    }

    public void setFoodAllergyImages(ImageCount foodAllergyImages) {
        this.foodAllergyImages = foodAllergyImages;
    }

    public ImageCount getOccupationalDiseasesImages() {
        return occupationalDiseasesImages;
    }

    public void setOccupationalDiseasesImages(ImageCount occupationalDiseasesImages) {
        this.occupationalDiseasesImages = occupationalDiseasesImages;
    }

    public ImageCount getGeneticDiseasesImages() {
        return geneticDiseasesImages;
    }

    public void setGeneticDiseasesImages(ImageCount geneticDiseasesImages) {
        this.geneticDiseasesImages = geneticDiseasesImages;
    }

    public ImageCount getMedicationsImages() {
        return medicationsImages;
    }

    public void setMedicationsImages(ImageCount medicationsImages) {
        this.medicationsImages = medicationsImages;
    }

    public ImageCount getMedicalConditionsImages() {
        return medicalConditionsImages;
    }

    public void setMedicalConditionsImages(ImageCount medicalConditionsImages) {
        this.medicalConditionsImages = medicalConditionsImages;
    }

    public static class ImageCount {
        private int count;
        private List<String> images = Lists.newArrayList();

        public ImageCount() {
        }

        public ImageCount(int count, List<String> images) {
            this.count = count;
            this.images = images;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public List<String> getImages() {
            return images;
        }

        public void setImages(List<String> images) {
            this.images = images;
        }
    }
}
