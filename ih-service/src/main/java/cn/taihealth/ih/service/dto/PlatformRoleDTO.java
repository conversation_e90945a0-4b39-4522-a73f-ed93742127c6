package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.cloud.PlatformRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 */
@Getter
@Setter
@NoArgsConstructor
public class PlatformRoleDTO extends UpdatableDTO {

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称最多255字符")
    @NotBlank(message = "名称必填")
    @Size(max = 255, message = "名称最多255字符")
    private String name;

    @ApiModelProperty("描述， 最长255字符")
    @Size(max = 255, message = "描述最多255字符")
    private String description;


    public PlatformRoleDTO(PlatformRole role) {
        super(role);
        code = role.getCode();
        name = role.getName();
        description = role.getDescription();
    }


}
