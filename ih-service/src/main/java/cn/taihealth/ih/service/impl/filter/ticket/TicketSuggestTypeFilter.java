package cn.taihealth.ih.service.impl.filter.ticket;

import cn.taihealth.ih.domain.Ticket;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class TicketSuggestTypeFilter implements SearchFilter<Ticket> {

//    private final List<SuggestTypeEnum> types = Lists.newArrayList();

    private final String suggestType;


    public TicketSuggestTypeFilter(String suggestType) {
        this.suggestType = suggestType;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Ticket> toSpecification() {

        return Specifications.likeIgnoreCase("suggestTypes", suggestType);
    }

    @Override
    public String toExpression() {
        String str = " suggestTypes:";
        return str;
    }

    @Override
    public boolean isValid() {
        return suggestType != null;
    }
}
