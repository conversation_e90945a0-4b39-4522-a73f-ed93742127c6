package cn.taihealth.ih.service.impl.listener;

import cn.taihealth.ih.domain.VideoRoom;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.UserRole;
import cn.taihealth.ih.domain.enums.ClinicType;
import cn.taihealth.ih.domain.enums.VideoChatType;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.service.api.TencentIMService;
import cn.taihealth.ih.service.impl.event.*;
import cn.taihealth.ih.service.impl.im.IMException;
import cn.taihealth.ih.service.impl.im.tencent.MessageIm;
import cn.taihealth.ih.service.impl.im.tencent.MessageIm.Desc;
import cn.taihealth.ih.service.vm.UserVM;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.HashMap;
import java.util.Map;

/**
 */
@Service
public class VideoChatEventListener {

    private static final Logger log = LoggerFactory.getLogger(VideoChatEventListener.class);

    private final TencentIMService imService;
    private final HospitalRepository hospitalRepository;


    public VideoChatEventListener(TencentIMService imService,
                                  HospitalRepository hospitalRepository) {

        this.imService = imService;
        this.hospitalRepository = hospitalRepository;
    }

    @TransactionalEventListener
    @Async
    public void onVideoChatStarted(VideoChatStartedEvent event) {
        VideoRoom room = event.getEntity();
        User operator = event.getOperator();
        Desc desc = event.getDesc();
        String fullName = operator.getFullName();
        String username = operator.getUsername();
        String roomId = room.getRoomId();
        String imGroupId = event.getImGroupId();
        VideoChatType type = event.getType();
        Hospital hospital = hospitalRepository.getById(event.getHospitalId());

        log.info("Trying to create video room for {} {}", fullName, roomId);
        try {
            imService.createVideoChatGroup(hospital, username, roomId, roomId);
            log.info("Video room {} created", room.getRoomId());
        } catch (IMException e) {
            log.error("Failed creating video chat group", e);
        }
        Map<String, Object> dateMap = createDateMap(new UserVM(operator), type,
            room.getBusinessEntityId());

        MessageIm messageIm = new MessageIm(desc, dateMap);
        imService.sendGroupMessageAsync(hospital, imGroupId, messageIm, null);
    }


    @TransactionalEventListener
    @Async
    public void onVideChatJoined(VideoChatJoinedEvent event) {
        VideoRoom room = event.getEntity();
        User operator = event.getOperator();
        VideoChatType type = event.getType();
        Desc desc = event.getDesc();
        String imGroupId = event.getImGroupId();
        Hospital hospital = hospitalRepository.getById(event.getHospitalId());
        // 对应bug13573 将user中的fullName替换成就诊人姓名
        UserVM user = new UserVM(operator);
        Order order = room.getOrder();
        if (order != null && operator != order.getDoctor().getUser()) {
            user.setFullName(order.getPatient().getName());
        }
        Map<String, Object> dateMap = createDateMap(user, type, room.getBusinessEntityId());
        MessageIm messageIm = new MessageIm(desc, dateMap);
        imService.sendGroupMessageAsync(hospital, imGroupId, messageIm, null);
    }

    @TransactionalEventListener
    @Async
    public void onVideoChatEnded(VideoChatEndedEvent event) {
        User operator = event.getEndUser();
        Desc desc = event.getDesc();
        VideoRoom room = event.getEntity();
        String roomId = room.getRoomId();
        String imGroupId = event.getImGroupId();
        VideoChatType type = event.getType();
        Hospital hospital = hospitalRepository.getById(event.getHospitalId());
        Order order = room.getOrder();
        UserVM user = new UserVM(operator);
        // 对应bug13573 将user中的fullName替换成就诊人姓名
        if (order != null && operator != order.getDoctor().getUser()) {
            user.setFullName(order.getPatient().getName());
        }
        Map<String, Object> dateMap = createDateMap(user, type, room.getBusinessEntityId());
        if (order != null && order.getOrderType() == ClinicType.EMERGENCY
            && operator.getUserRoles().stream().anyMatch(UserRole::isNurse)
        ) {
            MessageIm m = new MessageIm(Desc.NURSE_QUIT_CHAT, order);
            imService.sendMessage(hospital, order.getUser(), m);
        }
        MessageIm messageIm = new MessageIm(desc, dateMap);
        imService.sendGroupMessageAsync(hospital, imGroupId, messageIm, u -> {
            // 删除视频聊天群组 （和im群组不是同一个）
            log.info("Trying to delete video chat room {} ", roomId);
            imService.deleteGroup(hospital, roomId, null);
            return true;
        });
    }

    @TransactionalEventListener
    @Async
    public void onVideoChatQuited(VideoChatQuitedEvent event) {
        User operator = event.getUser();
        Desc desc = event.getDesc();
        VideoRoom room = event.getEntity();
        String imGroupId = event.getImGroupId();
        VideoChatType type = event.getType();
        Hospital hospital = hospitalRepository.getById(event.getHospitalId());
        UserVM user = new UserVM(operator);
        // 对应bug13573 将user中的fullName替换成就诊人姓名
        Order order = room.getOrder();
        if (order != null && operator != order.getDoctor().getUser()) {
            user.setFullName(order.getPatient().getName());
        }
        Map<String, Object> dateMap = createDateMap(user, type, room.getBusinessEntityId());
        MessageIm messageIm = new MessageIm(desc, dateMap);
        imService.sendGroupMessageAsync(hospital, imGroupId, messageIm, null);
    }


    @TransactionalEventListener
    @Async
    public void onVideoChatRefused(VideoChatRefusedEvent event) {
        User operator = event.getRefusedUser();
        Desc desc = event.getDesc();
        VideoRoom room = event.getEntity();
        String imGroupId = event.getImGroupId();
        VideoChatType type = event.getType();
        Hospital hospital = hospitalRepository.getById(event.getHospitalId());
        // 对应bug13573 将user中的fullName替换成就诊人姓名
        UserVM user = new UserVM(operator);
        Order order = room.getOrder();
        if (order != null && operator != order.getDoctor().getUser()) {
            user.setFullName(order.getPatient().getName());
        }
        Map<String, Object> dateMap = createDateMap(user, type, room.getBusinessEntityId());
        MessageIm messageIm = new MessageIm(desc, dateMap);
        imService.sendGroupMessageAsync(hospital, imGroupId, messageIm, null);
    }

    private Map<String, Object> createDateMap(UserVM operator, VideoChatType type, Long id) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("operator", operator);
        dataMap.put("type", type);
        dataMap.put("id", id);
        return dataMap;
    }
}
