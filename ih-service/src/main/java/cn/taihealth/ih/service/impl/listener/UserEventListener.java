package cn.taihealth.ih.service.impl.listener;

import cn.taihealth.ih.domain.cloud.UserSource;
import cn.taihealth.ih.repo.UserRepository;
import cn.taihealth.ih.service.api.TencentIMService;
import cn.taihealth.ih.service.api.UserSourceService;
import cn.taihealth.ih.service.impl.event.UserAddEvent;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 *
 */
@Component
public class UserEventListener {

    private final UserRepository userRepository;
    private final TencentIMService tencentIMService;
    private final UserSourceService userSourceService;

    public UserEventListener(UserRepository userRepository,
                             TencentIMService tencentIMService, UserSourceService userSourceService) {
        this.userRepository = userRepository;
        this.tencentIMService = tencentIMService;
        this.userSourceService = userSourceService;
    }

    @Async
    @TransactionalEventListener
    public void onUserAdd(UserAddEvent event) {
        UserSource entity = event.getEntity();
        userSourceService.addUserSource(entity);
    }

}
