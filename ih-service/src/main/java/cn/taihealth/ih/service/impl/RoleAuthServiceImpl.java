package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.Snowflake64.Holder;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.UserAuthority;
import cn.taihealth.ih.domain.cloud.UserRole;
import cn.taihealth.ih.domain.cloud.UserRoleAuthorityRel;
import cn.taihealth.ih.repo.cloud.UserAuthorityRepository;
import cn.taihealth.ih.repo.cloud.UserRoleAuthorityRelRepository;
import cn.taihealth.ih.repo.cloud.UserRoleRepository;
import cn.taihealth.ih.repo.cloud.UserUserRoleRelRepository;
import cn.taihealth.ih.service.api.PageSettingService;
import cn.taihealth.ih.service.api.RoleAuthService;
import cn.taihealth.ih.service.cache.RoleAuthCache;
import cn.taihealth.ih.service.cache.RoleAuthCache.CacheAuth;
import cn.taihealth.ih.service.dto.PageSettingDTO;
import cn.taihealth.ih.service.dto.UserRoleDTO;
import cn.taihealth.ih.service.vm.RolePageVM;
import cn.taihealth.ih.service.vm.RolePageVM.Menu;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class RoleAuthServiceImpl implements RoleAuthService {

    private static final Logger log = LoggerFactory.getLogger(RoleAuthServiceImpl.class);

    private final UserRoleRepository userRoleRepository;
    private final UserUserRoleRelRepository userUserRoleRelRepository;
    private final UserAuthorityRepository userAuthorityRepository;
    private final UserRoleAuthorityRelRepository userRoleAuthorityRelRepository;
    private final RoleAuthCache roleAuthCache;
    private final PageSettingService pageSettingService;

    public RoleAuthServiceImpl(UserRoleRepository userRoleRepository,
                               UserUserRoleRelRepository userUserRoleRelRepository,
                               UserRoleAuthorityRelRepository userRoleAuthorityRelRepository,
                               UserAuthorityRepository userAuthorityRepository,
                               PageSettingService pageSettingService,
                               RoleAuthCache roleAuthCache) {
        this.userRoleRepository = userRoleRepository;
        this.userUserRoleRelRepository = userUserRoleRelRepository;
        this.roleAuthCache = roleAuthCache;
        this.pageSettingService = pageSettingService;
        this.userAuthorityRepository = userAuthorityRepository;
        this.userRoleAuthorityRelRepository = userRoleAuthorityRelRepository;
    }

    @Override
    @Transactional
    public UserRoleDTO addRole(Hospital hospital, UserRoleDTO dto) {
        UserRole userRole = new UserRole();
        userRole.setCode(Holder.INSTANCE.nextId() + "");
        userRole.setName(dto.getName());
        userRole.setDescription(dto.getDescription());
        userRole.setHospital(hospital);
        return new UserRoleDTO(userRoleRepository.save(userRole));
    }

    @Override
    @Transactional
    public void deleteRole(Hospital hospital, long roleId) {
        userRoleRepository.findById(roleId).ifPresent(r -> {
            if (!Objects.equals(hospital, r.getHospital())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
            if (r.isAdmin() || r.getHospital() == null) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("该角色不能被删除");
            }
            userUserRoleRelRepository.deleteByUserRoleId(roleId);
            userRoleRepository.delete(r);
            roleAuthCache.evictAllHospitalRoles();
        });
    }

    @Override
    @Transactional
    public UserRoleDTO editRole(Hospital hospital, UserRoleDTO dto) {
        return userRoleRepository.findById(dto.getId()).map(r -> {
            if (!Objects.equals(hospital, r.getHospital())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
            if (r.isAdmin()) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("该角色菜单不能被修改");
            }
            if (r.getHospital() == null) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("该角色不能被修改");
            }
            r.setName(dto.getName());
            r.setDescription(dto.getDescription());
            return new UserRoleDTO(userRoleRepository.save(r));
        }).orElse(null);
    }

    @Override
    public Menu getMenus(Hospital hospital, List<String> roles) {
        boolean isHospitalOpt = roles.stream().anyMatch(u -> u.startsWith("HOSPITAL_OPT_"));
        List<PageSettingDTO> dtos = pageSettingService.getPCPageSettings(hospital.getCode());
        List<Menu> menus = dtos.stream().map(Menu::new).collect(Collectors.toList());
        if (!isHospitalOpt) {
            List<UserAuthority> authorities = Lists.newArrayList();
            roles.forEach(r -> {
                List<CacheAuth> cacheAuths = roleAuthCache.getAuths(r);
                userRoleRepository.findOneByCode(r).ifPresent(ur ->
                        authorities.addAll(
                                cacheAuths.stream().sorted(Comparator.comparing(CacheAuth::getIndex))
                                        .map(c -> {
                                            UserAuthority authority = new UserAuthority(c.getCode(), c.getCode());
                                            authority.setId(c.getId());
                                            return authority;
                                        }).collect(Collectors.toList())
                        )
                );
            });
            menus = menus.stream().map(m -> filter(m, authorities))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            menus.forEach(Menu::sortAll);
        }
        return CollectionUtils.isEmpty(menus) ? null : menus.get(0);
    }


    @Override
    public RolePageVM getRoleAuths(Hospital hospital, long roleId) {

        UserRole role = userRoleRepository.getById(roleId);
        List<CacheAuth> cacheAuths = roleAuthCache.getAuths(role.getCode());
        RolePageVM vm = new RolePageVM(role);
        List<UserAuthority> authorities = cacheAuths
            .stream()
            .sorted(Comparator.comparing(CacheAuth::getIndex))
            .map(r -> {
                UserAuthority authority = new UserAuthority(r.getCode(), r.getCode());
                authority.setId(r.getId());
                return authority;
            }).collect(Collectors.toList());
        List<PageSettingDTO> dtos = pageSettingService.getPCPageSettings(hospital.getCode());
        List<Menu> allMenus = dtos.stream().map(Menu::new).collect(Collectors.toList());
        List<Menu> menus = allMenus.stream().map(m -> filter(m, authorities))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        menus.forEach(Menu::sortAll);
        vm.setMenus(CollectionUtils.isEmpty(menus) ? null : menus.get(0));
        return vm;
    }

    @Override
    @Transactional
    public void saveRoleAuths(Hospital hospital, RolePageVM vm) {
        UserRole role = userRoleRepository.getById(vm.getId());
        if (!Objects.equals(hospital, role.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (role.isAdmin()) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("该角色菜单不能被修改");
        }
//        List<UserAuthority> authorities = role.getAuthorities();
//        List<UserAuthority> rmAuthorities = Lists.newArrayList();
        Menu menuVM = vm.getMenus();
        List<Menu> menus = menuVM.getChildren();
        List<PageSettingDTO> dtos = pageSettingService.getPCPageSettings(hospital.getCode());
        List<Menu> moreMenus = Lists.newArrayList();
        addParentMenus(dtos, menus, moreMenus);
        menus.addAll(moreMenus);
//        authorities.forEach(a -> {
//            if (!authInMenus(a, Lists.newArrayList(menuVM))) {
//                rmAuthorities.add(a);
//            }
//        });
//        authorities.removeAll();
        boolean isHeadNurse = isAppletHeadNurse(menus);
        boolean isNurse = isAppletNurse(menus);
        if (isHeadNurse && isNurse) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("小程序权限护士长端和护士端只能选择其一");
        }
        role.setHeadNurse(isHeadNurse);
        role.setNurse(isNurse);
        role.setDoctor(isDoctor(menus));
        role.setPharmacist(isPharmacist(menus));
        role.setOut(isOut(menus));
        role.setEmergency(isEmergency(menus));
        role.setAuthorities(Lists.newArrayList());
        role.setDoctorAssistant(isDoctorAssistant(menus));
        userRoleRepository.save(role);
        List<UserRoleAuthorityRel> rels = userRoleAuthorityRelRepository.findAllByUserRoleId(vm.getId());
        List<UserAuthority> authorities = Lists.newArrayList();
        saveMenus(role, rels, Lists.newArrayList(vm.getMenus()), authorities);
//        role.setAuthorities(authorities);
        roleAuthCache.evictAllHospitalRoles();
        roleAuthCache.evictAuth(role.getCode());
//        return getRoleAuths(hospital, vm.getId());
    }

    /**
     * 根据菜单判断是否有护士权限
     * @param menus
     * @return
     */
    private boolean isNurse(List<Menu> menus) {
        return menus.stream().anyMatch(u -> "jzfz".equals(u.getCode()));
    }

    /**
     * 根据菜单判断是否有医生权限
     *
     * @param menus
     * @return
     */
    private boolean isDoctor(List<Menu> menus) {
        return menus.stream().anyMatch(u -> "wdzs".equals(u.getCode()));
    }

    /**
     * 根据菜单判断是否有医生助手权限
     *
     * @param menus
     * @return
     */
    private boolean isDoctorAssistant(List<Menu> menus) {
        return menus.stream().anyMatch(u -> "yszl".equals(u.getCode()));
    }

    /**
     * 根据菜单判断是否有药师权限
     *
     * @param menus
     * @return
     */
    private boolean isPharmacist(List<Menu> menus) {
        return menus.stream().anyMatch(u -> "zxsf".equals(u.getCode()));
    }

    /**
     * 根据菜单判断是否在线门诊权限
     * @param menus
     * @return
     */
    private boolean isOut(List<Menu> menus) {
        return menus.stream().anyMatch(u -> "zxmz".equals(u.getCode()));
    }

    /**
     * 根据菜单判断是否在线急诊权限
     * @param menus
     * @return
     */
    private boolean isEmergency(List<Menu> menus) {
        return menus.stream().anyMatch(u -> "zxjz".equals(u.getCode()));
    }


    private boolean addParentMenus(List<PageSettingDTO> pageSettings, List<Menu> menus, List<Menu> moreMenus) {
        boolean havePage = false;
        for (PageSettingDTO pageSetting : pageSettings) {
            if (CollectionUtils.isNotEmpty(pageSetting.getPageSettings())) {
                if (addParentMenus(pageSetting.getPageSettings(), menus, moreMenus)) {
                    havePage = true;
                    if (menus.stream().noneMatch(m -> m.getCode().equals(pageSetting.getCode()))) {
                        moreMenus.add(new Menu(pageSetting.getCode(), pageSetting.getName(), pageSetting.getMenuType()));
                    }
                }
            } else {
                if (menus.stream().anyMatch(m -> m.getCode().equals(pageSetting.getCode()))) {
                    havePage = true;
                }
            }
        }
        return havePage;
    }

    private boolean authInMenus(UserAuthority auth, List<Menu> menus) {
        if (menus.stream().anyMatch(d -> Objects.equals(d.getCode(), auth.getCode()))) {
            return true;
        }
        boolean in = false;
        for (Menu d : menus) {
            if (authInMenus(auth, d.getChildren())) {
                in = true;
                break;
            }
        }
        return in;
    }

    private void saveMenus(UserRole role, List<UserRoleAuthorityRel> rels, List<Menu> menus, List<UserAuthority> authorities) {
        for (int i = 0; i < menus.size(); i++) {
            Menu m = menus.get(i);
            int finalI = i;
            UserRoleAuthorityRel rel = authorities.stream().filter(a -> Objects.equals(a.getCode(), m.getCode())).findFirst()
                .map(a -> rels.stream().filter(r -> Objects.equals(r.getUserAuthorityId(), a.getId()))
                    .findFirst().orElseGet(UserRoleAuthorityRel::new))
                .orElseGet(UserRoleAuthorityRel::new);
            if (StringUtils.isNotBlank(m.getCode())) {
                userAuthorityRepository.findOneByCode(m.getCode()).ifPresent(a -> {
                    if (rel.isNew()) {
                        authorities.add(a);
                    }
                    rel.setOrderNum(finalI);
                    rel.setUserRoleId(role.getId());
                    rel.setUserAuthorityId(a.getId());
                    userRoleAuthorityRelRepository.save(rel);
                });
            }
            saveMenus(role, rels, m.getChildren(), authorities);
        }
    }

    /**
     * 菜单是否使用, 1: 这个菜单配置了使用, 2: 这个菜单下面有使用的子菜单
     * @param menu
     * @param authorities
     * @return
     */
    private boolean isUse(Menu menu, List<UserAuthority> authorities) {
        for (int i = 0; i < authorities.size(); i++) {
            if (Objects.equals(authorities.get(i).getCode(), menu.getCode())) {
                menu.setOrderNum(i);
                break;             }
        }
        boolean have = authorities.stream().anyMatch(a -> Objects.equals(a.getCode(), menu.getCode()));
        if (CollectionUtils.isEmpty(menu.getChildren())) {
            return have;
        } else {
            Menu m = filter(menu, authorities);
            if (m == null || CollectionUtils.isEmpty(m.getChildren())) {
                return have;
            } else {
                return true;
            }
        }
    }

    /**
     * 过滤菜单, 把没使用的去除
     * @param menu
     * @param authorities
     * @return
     */
    private Menu filter(Menu menu, List<UserAuthority> authorities) {
        menu.setChildren(menu.getChildren().stream().filter(c ->
            isUse(c, authorities)
        ).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(menu.getChildren())) {
            if (isUse(menu, authorities)) {
                return menu;
            } else {
                return null;
            }
        }
        return menu;
    }

    /**
     * 根据菜单判断是否有医生端小程序护士长端权限
     * @param menus
     * @return
     */
    private boolean isAppletHeadNurse(List<Menu> menus) {
        return menus.stream().anyMatch(u -> "hszd".equals(u.getCode()));
    }

    /**
     * 根据菜单判断是否有医生端小程序护士端权限
     * @param menus
     * @return
     */
    private boolean isAppletNurse(List<Menu> menus) {
        return menus.stream().anyMatch(u -> "hsd".equals(u.getCode()));
    }
}
