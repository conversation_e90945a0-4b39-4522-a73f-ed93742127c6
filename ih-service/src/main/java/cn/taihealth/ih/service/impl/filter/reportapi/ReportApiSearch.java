package cn.taihealth.ih.service.impl.filter.reportapi;


import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.domain.cloud.ReportApi;
import cn.taihealth.ih.domain.cloud.SupervisePlatform;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.NotFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class ReportApiSearch extends SearchCriteria<ReportApi> {

    public static ReportApiSearch of(String query) {
        ReportApiSearch search = new ReportApiSearch();
        search.parse(query);
        return search;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<ReportApi> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier
                .valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case NAME:
                return new ReportApiNameFilter(value);
            case CODE:
                return new ReportApiCodeFilter(value);
            case REPORT:
                return new GenericFilter<>("report", Operator.eq, StringUtil.stringToBoolean(value));
            default:
                return null;
        }
    }


    @Override
    protected SearchFilter<ReportApi> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        NAME,
        CODE,
        REPORT
    }
}
