package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StudioArticleVM extends AbstractEntityDTO {
    @ApiModelProperty(value = "标题", required = true)
    @NotBlank
    @Size(max = 128, message = "标题不能超过128位")
    private String title;

    @ApiModelProperty(value = "正文", required = true)
    @NotBlank
    private String content;

    @ApiModelProperty(value = "是否展示 true 展示 ")
    private Boolean show;

    @ApiModelProperty("封面url")
    private String coverImageUrl;

    @ApiModelProperty(value = "名医工作室", required = true)
    @NotNull
    private StudioVM studio;


}
