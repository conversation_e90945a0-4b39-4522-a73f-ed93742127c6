package cn.taihealth.ih.service.dto.nodeRed;

import cn.taihealth.ih.domain.ElectronicMedicCard;
import lombok.Data;

@Data
public class PatientCards {

    /**
     * 门诊患者唯一号
     */
    private String patid;

    /**
     * 患者姓名
     */
    private String patname;

    /**
     * 病历号
     */
    private String hiscardno;

    /**
     * 卡号
     */
    private String cardno;

    /**
     * 卡类型 [0]自费卡 [1]医保卡 [2]社保卡 [3]身份证
     */
    private String cardtype;

    /**
     * 证件号码
     */
    private String certificate_no;

    /**
     * 性别 男、女、未知
     */
    private String sex;

    /**
     * 婚姻状况 0未婚,1已婚,2离独,3丧偶
     */
    private String marriage;

    /**
     * 民族中文名称
     */
    private String nation;

    /**
     * 职业中文名称
     */
    private String career;

    /**
     * 出生日期，格式yyyyMMdd
     */
    private String birth;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 院内就诊卡账户余额，如医院没有院内账户则为空
     */
    private String account_balance;

    /**
     * 医保类型说明
     */
    private String chargetype_name;

    /**
     * 医保类型代码(根据各家医院实际情况反馈)
     */
    private String chargetype_code;

    /**
     * 外部电子卡号，需以当地医院实际规范为准
     */
    private String virtual_card;


}
