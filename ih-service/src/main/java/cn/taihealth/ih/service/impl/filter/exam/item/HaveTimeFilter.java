package cn.taihealth.ih.service.impl.filter.exam.item;

import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.domain.dict.ExamItem;
import cn.taihealth.ih.domain.enums.TimePeriod;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 * <AUTHOR>
 */
public class HaveTimeFilter implements SearchFilter<ExamItem> {

    private final String pattern;

    public HaveTimeFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<ExamItem> toSpecification() {
        if (StringUtil.stringToBoolean(pattern)) {
            return Specifications.not(Specifications.eq("timePeriod", TimePeriod.UNKNOWN));
        } else {
            return null;
        }
    }

    @Override
    public String toExpression() {
        return "haveTime:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof HaveTimeFilter)) {
            return false;
        }

        HaveTimeFilter rhs = (HaveTimeFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
