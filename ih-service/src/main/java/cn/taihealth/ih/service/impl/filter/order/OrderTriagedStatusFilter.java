package cn.taihealth.ih.service.impl.filter.order;

import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import cn.taihealth.ih.service.impl.order.OrderConstants;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 * <AUTHOR>
 */
public class OrderTriagedStatusFilter implements SearchFilter<Order> {

    private final String pattern;

    public OrderTriagedStatusFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Order> toSpecification() {
        List<Specification<Order>> statusList = Lists.newArrayList();
        for (Order.OrderStatus status : OrderConstants.ORDER_STATUS_TRIAGED){
            statusList.add(Specifications.eq("status",status));
        }
         return Specifications.or(statusList);
    }

    @Override
    public String toExpression() {
        return "OrderTriagedStatusFilter:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof OrderTriagedStatusFilter)) {
            return false;
        }

        OrderTriagedStatusFilter rhs = (OrderTriagedStatusFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
