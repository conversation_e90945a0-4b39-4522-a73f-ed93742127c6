package cn.taihealth.ih.service.impl.filter.category;

import cn.taihealth.ih.domain.hospital.Category;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

public class CategorySortFilter implements SearchFilter<Category> {
    public static final String NAME = "category-sort-filter";

    private List<SortField> fields = Lists.newArrayList();

    public enum Field {
        CREATEDDATE("createdDate"),
        ORDERVALUE("orderValue");

        final String fieldName;

        Field(String fieldName) {
            this.fieldName = fieldName;
        }
    }

    public static class SortField {
        private final Field field;
        private final boolean asc;

        public SortField(Field field, boolean asc) {
            this.field = field;
            this.asc = asc;
        }

        public Field getField() {
            return field;
        }

        public boolean isAsc() {
            return asc;
        }

        public Sort.Order toOrder() {
            return new Sort.Order(asc ? Sort.Direction.ASC : Sort.Direction.DESC, field.fieldName);
        }
    }

    public CategorySortFilter(String values) {
        String sortStr = values;
        if (sortStr.startsWith("\"")) {
            sortStr = sortStr.substring(1);
        }
        if (sortStr.endsWith("\"")) {
            sortStr = sortStr.substring(0, sortStr.length() - 1);
        }
        for (String value : sortStr.split(" ")) {
            int pos = value.indexOf('-');
            String field;
            boolean fieldAsc;
            if (pos > 0) {
                field = value.substring(0, pos);
                String a = value.substring(pos + 1);
                fieldAsc = "asc".equalsIgnoreCase(a);
            } else {
                field = value;
                fieldAsc = false;
            }

            try {
                SortField sortField = new SortField(Field.valueOf(field.toUpperCase()), fieldAsc);
                fields.add(new SortField(Field.valueOf(field.toUpperCase()), fieldAsc));
            } catch (IllegalArgumentException e) {
            }
        }
    }

    @Override
    public boolean isValid() {
        return fields != null;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Specification<Category> toSpecification() {
        if (fields.isEmpty()) {
            return null;
        } else {
            return Specifications.orderBy(
                Sort.by(
                    fields.stream().map(SortField::toOrder).collect(Collectors.toList())
                )
            );
        }
    }

    @Override
    public String toExpression() {
        if (fields != null) {
            return "sort:\"" +
                fields.stream()
                .map(u -> u.getField().fieldName.toLowerCase() + "-" + (u.isAsc() ? "asc" : "desc"))
                .collect(Collectors.joining(" "))
                + "\"";
        } else {
            return "";
        }
    }

    public List<SortField> getFields() {
        return fields;
    }

    public void setFields(List<SortField> fields) {
        this.fields = fields;
    }

    @Override
    public int hashCode() {
        return Objects.hash(fields);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (!(obj instanceof CategorySortFilter)) {
            return false;
        }

        CategorySortFilter f = (CategorySortFilter) obj;
        return Objects.equals(f.fields, fields);
    }

    @Override
    public String toString() {
        return toExpression();
    }
}
