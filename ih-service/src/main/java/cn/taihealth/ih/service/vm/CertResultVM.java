package cn.taihealth.ih.service.vm;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class CertResultVM {

    @NotNull(message = "是否通过必填")
    @ApiModelProperty("是否通过")
    private boolean approved;

    @ApiModelProperty("备注")
    private String comment;

}

