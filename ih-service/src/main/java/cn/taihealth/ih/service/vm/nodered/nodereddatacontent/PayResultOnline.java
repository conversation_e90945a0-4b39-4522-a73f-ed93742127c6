package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import lombok.Data;

@Data
public class PayResultOnline implements java.io.Serializable {

    // 支付类别	Y	0门诊预交金 1门诊结算 2住院预交金 3住院结算
    private int trade_type;
    // 状态	Y	0 HIS成功  1HIS不成功
    private int status;
    // HIS单号	Y
    private String out_trade_no;
    // 医保出参	N	占位，具体参数格式内容需要根据当地医保确定
    private Object insurance_param;
    // 扩展信息	N	以json格式返回
    private Object extra_content;
}
