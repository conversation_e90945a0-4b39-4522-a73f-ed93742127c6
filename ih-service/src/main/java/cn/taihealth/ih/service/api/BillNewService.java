package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.service.vm.BillReconciliationSummaryVM1;
import cn.taihealth.ih.service.vm.UploadVM;

public interface BillNewService {


    BillReconciliationSummaryVM1 reconciliationSummary1(Hospital hospital, String query);

    UploadVM exportBills(Hospital hospital, String startDate, String endDate);

//    BillServiceSummaryVM1 getBillServiceSummary1(Hospital hospital, BillOperateTypeEnum operateType,
//                                                 String query);
}
