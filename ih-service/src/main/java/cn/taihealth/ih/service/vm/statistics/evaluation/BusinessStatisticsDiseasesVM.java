package cn.taihealth.ih.service.vm.statistics.evaluation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Moon
 * @Date: 2021年03月15日11:27:10
 */
@Data
@NoArgsConstructor
public class BusinessStatisticsDiseasesVM {

    @ApiModelProperty("疾病名")
    private String disease;

    @ApiModelProperty("接诊数")
    private int orderCount;

    @ApiModelProperty("处方数")
    private int prescriptionCount;

    public BusinessStatisticsDiseasesVM(String disease) {
        this.disease = disease;
    }

}
