package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.enums.BillOperateTypeEnum;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

import java.util.TreeMap;

/**
 * 交易概况
 */
@Data
public class BillServiceSummaryVM {


    @ApiModelProperty("支付操作类型")
    private BillOperateTypeEnum operateType;

    @ApiModelProperty("总金额")
    private Long totalAmount;

    @ApiModelProperty("医保部分")
    private Integer medicareAmount;

    @ApiModelProperty("个人部分")
    private Integer selfAmount;

    @ApiModelProperty("各服务交易类型金额")
    private TreeMap<ProjectTypeEnum, Long> serviceAmount;


}
