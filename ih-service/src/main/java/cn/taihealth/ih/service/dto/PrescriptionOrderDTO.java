package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.DrugOrderStatus;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.PrescriptionType;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.Status;
import cn.taihealth.ih.service.vm.PrescriptionVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 处方表DTO
 * <AUTHOR>
 */
@Data
@ToString(exclude = {"order", "patient"})
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class PrescriptionOrderDTO extends UpdatableDTO {

    @ApiModelProperty("急诊订单")
    private OrderDTO order;

    @ApiModelProperty("患者")
    private PatientDTO patient;

    @ApiModelProperty("医生")
    private MedicalWorkerDTO doctor;

    @ApiModelProperty("发起人")
    private UserDTO user;

    @ApiModelProperty("处方审核状态")
    private PrescriptionOrder.Status status = Status.UNREVIEWED;

    @ApiModelProperty("药品订单状态")
    private PrescriptionOrder.DrugOrderStatus drugOrderStatus = DrugOrderStatus.CANCELED;

    @ApiModelProperty("处方图片（原生）")
    private UploadDTO upload;

    @ApiModelProperty("处方图片（ca）")
    private UploadDTO caPicture;

    @ApiModelProperty("处方pdf文件")
    private UploadDTO caPdf;

    @ApiModelProperty("拒绝原因，最长255字符")
    private String rejectReason;

    @ApiModelProperty("是否发送给用户 0:false,1:true")
    private boolean sendUser;

    @ApiModelProperty("处方单类型")
    private PrescriptionOrder.PrescriptionType type = PrescriptionType.UNIVERSAL;

    @ApiModelProperty("医生提交处方审核时间")
    private Date commitedDate;

    @ApiModelProperty("审核人(药师)")
    private MedicalWorkerDTO doctorReview;

    @ApiModelProperty("审核时间")
    private Date reviewTime;

    @ApiModelProperty("关联药品")
    @Valid
    private List<PrescriptionVM> prescriptionDTO = Lists.newArrayList();

    @ApiModelProperty("发送处方通知给用户的时间")
    private Date sendUserDate;

    @ApiModelProperty("处方失效时间")
    private Date expirationDate;

    @ApiModelProperty("处方是否过期，true已过期，false未过期")
    private boolean expired;

    @ApiModelProperty(name = "是否逻辑删除，0删除，1未删")
    private boolean enabled;

    //封装属性，诊断
    private String diagnosis;

    //疾病，医生写病历使用
    private List<MedicalCaseDiseaseDTO> diseases = Lists.newArrayList();

    @ApiModelProperty("记录操作日志ID")
    private Long operationsId;

    @ApiModelProperty("记录操作日志 增加orderId单独封装")
    private Long orderId;

    @ApiModelProperty("医院 所属医院")
    private HospitalDTO hospitalDTO;

    @ApiModelProperty("医嘱")
    private String medicalOrder;

    public PrescriptionOrderDTO(PrescriptionOrder prescriptionOrder) {
        super(prescriptionOrder);
        rejectReason = prescriptionOrder.getRejectReason();
        order = new OrderDTO(prescriptionOrder.getOrder());
        patient = new PatientDTO(prescriptionOrder.getPatient(), null);
        doctor = new MedicalWorkerDTO(prescriptionOrder.getDoctor(), false);
        user = new UserDTO(prescriptionOrder.getUser());
        if (prescriptionOrder.getUpload() != null) {
            upload = new UploadDTO(prescriptionOrder.getUpload());
        }
        if (prescriptionOrder.getCaPicture() != null) {
            caPicture = new UploadDTO(prescriptionOrder.getCaPicture());
        }
        status = prescriptionOrder.getStatus();
        rejectReason = prescriptionOrder.getRejectReason();
        sendUser = prescriptionOrder.isSendUser();
        type = prescriptionOrder.getType();
        reviewTime = prescriptionOrder.getReviewTime();
        doctorReview = null != prescriptionOrder.getDoctorReview() ? new MedicalWorkerDTO(
            prescriptionOrder.getDoctorReview()) : null;
        commitedDate = prescriptionOrder.getCommitedDate();
        prescriptionDTO = prescriptionOrder.getPrescription().stream()
            .map(PrescriptionVM::new).collect(Collectors.toList());
        sendUserDate = prescriptionOrder.getSendUserDate();
        enabled = prescriptionOrder.isEnabled();
        hospitalDTO = new HospitalDTO(prescriptionOrder.getHospital());
        medicalOrder = prescriptionOrder.getMedicalOrder();
        if (prescriptionOrder.getDrugOrderStatus() != null) {
            drugOrderStatus = prescriptionOrder.getDrugOrderStatus();
        }
    }

}
