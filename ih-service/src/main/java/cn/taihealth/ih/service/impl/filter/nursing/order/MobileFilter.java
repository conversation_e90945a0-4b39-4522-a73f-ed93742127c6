package cn.taihealth.ih.service.impl.filter.nursing.order;


import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

public class MobileFilter implements SearchFilter<OrderNursingExt> {

    private final String pattern;

    public MobileFilter(String pattern ) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OrderNursingExt> toSpecification() {
        return Specifications.and(Specifications.like("baseOrder.patient.mobile", pattern));
    }

    @Override
    public String toExpression() {
        return "mobile:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof MobileFilter)) {
            return false;
        }

        MobileFilter rhs = (MobileFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
