package cn.taihealth.ih.service.impl.actionlog;

import cn.taihealth.ih.service.impl.event.HisLogEvent;
import lombok.AllArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
@AllArgsConstructor
public class HisLoggingAspect {

    private final ApplicationEventPublisher eventPublisher;

    /**
     * 记录日志, 如果在异步方法中使用, 方法参数中需要携带user对象和ip对象, user对象使用类型判断, ip对象使用参数名称判断
     * @param point
     */
    @Around("@annotation(cn.taihealth.ih.commons.aop.logging.HisLog)")
    public Object logAction(ProceedingJoinPoint point) throws Throwable {
        try {
            Object result = point.proceed();
            eventPublisher.publishEvent(new HisLogEvent(point, true, result));
            return result;
        } catch (Throwable throwable) {
            eventPublisher.publishEvent(new HisLogEvent(point, false, throwable.getMessage()));
            throw throwable;
        }
    }

}
