package cn.taihealth.ih.service.vm.ca;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class SignaturePdfVM {

    @ApiModelProperty("二维码base64串")
    private String imgCode;
    @ApiModelProperty("随机数,与pdf文件关联用，业务系统留存后续轮询使用")
    private String random;
    @ApiModelProperty("PDF 文件MD5,验证文件一致性")
    private String fileHash;
    @ApiModelProperty("ca签名页面地址")
    private String signUrl;

    public SignaturePdfVM(String imgCode, String random, String fileHash) {
        this.imgCode = imgCode;
        this.random = random;
        this.fileHash = fileHash;
    }

}
