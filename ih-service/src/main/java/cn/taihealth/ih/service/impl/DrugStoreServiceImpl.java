package cn.taihealth.ih.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.taihealth.ih.commons.vm.PageHelperBean;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.DrugStoreService;
import cn.taihealth.ih.service.dto.drug.DoctorDrugParamDTO;
import cn.taihealth.ih.service.dto.drugorder.*;
import cn.taihealth.ih.service.dto.express.ExpressData;
import cn.taihealth.ih.wechat.service.impl.drug.DrugStoreUtils;
import cn.taihealth.ih.service.util.PageUtils;
import cn.taihealth.ih.wechat.service.vm.drug.*;
import cn.taihealth.ih.service.vm.drugstore.DrugAdmFreqDTO;
import cn.taihealth.ih.service.vm.drugstore.DrugDictVM;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class DrugStoreServiceImpl implements DrugStoreService {

    private static final String GET = "GET";

    private static final String POST = "POST";

    private static final String DELETE = "DELETE";

    private static final String PUT = "PUT";

    private static final String PATCH = "PATCH";

    private static final String INNER_ADMIN = "/inner/admin/drug_dict";

    @Autowired
    private MedicalWorkerRepository medicalWorkerRepository;


    private Map<String, String> getCodeParam(String hospitalCode) {
        Map<String, String> params = new HashMap<>();
        params.put("hospitalCode", hospitalCode);
        return params;
    }

    private Map<String, String> getPageParam(Integer pageNo, Integer size, String query, String code) {
        Map<String, String> params = new HashMap<>();
        params.put("page", pageNo + 1 + "");
        params.put("size", size + "");
        params.put("hospitalCode", code);
        if (StringUtils.isNotBlank(query)) {
            params.put("query", query);
        }

        return params;
    }

    @Override
    public Page<DicMedInfoDTO> getDrugDict(Pageable page, String name, String hospitalCode) {
        String query = "";
        if (StringUtils.isNotBlank(name)) {
            if (name.contains(" ")) {
                name = "\"" + name + "\"";
            }
            query = "name:" + name;
        }
        Map<String, String> params = new HashMap<>();
        params.put("size", page.getPageSize() + "");
        params.put("pageNo", page.getPageNumber() + 1 + "");
        params.put("hospitalCode", hospitalCode);
        params.put("query", query);
        PageHelperBean<DrugDictVM> result = DrugStoreUtils.send(GET, "/inner/drugdict/drugs",
                params, null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        Page<DrugDictVM> drugs1 = PageUtils.toHibernatePage(result);
        return drugs1.map(DrugDictVM::toDTO);
    }

    // 添加药品类别
    @Override
    public DicCategoryDTO createDrugCategory(DicCategoryDTO dto) {
        return DrugStoreUtils.send(POST, INNER_ADMIN + "/categories",
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public DicCategoryDTO updateDrugCategory(long dicCategoryId, DicCategoryDTO dto) {
        return DrugStoreUtils.send(PUT, INNER_ADMIN + "/categories/" + dicCategoryId,
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public Page<DicCategoryDTO> getDicCategory(Integer pageNo, Integer size, String code) {

        PageHelperBean<DicCategoryDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/categories",
                getPageParam(pageNo, size, null, code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return PageUtils.toHibernatePage(result);
    }

    @Override
    public List<DicCategoryDTO> getDicCategoryByIds(List<Long> ids, String code) {
        List<DicCategoryDTO> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/categories/ids",
                null,
                ids, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public void delDicCategoryById(Long id, String hospitalCode) {
        DrugStoreUtils.send(DELETE, INNER_ADMIN + "/categories/" + id,
                getCodeParam(hospitalCode), null, null);
    }

    @Override
    public List<DicCategoryDTO> getAllDicCategory(String code) {
        List<DicCategoryDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/categories/all",
                getCodeParam(code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public DicDosageFormDTO addDicDosageForm(DicDosageFormDTO dto) {
        return DrugStoreUtils.send(POST, INNER_ADMIN + "/dosage_forms",
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public DicDosageFormDTO updateDicDosageForm(Long dicDosageFormId, DicDosageFormDTO dto) {
        return DrugStoreUtils.send(PUT, INNER_ADMIN + "/dosage_forms/" + dicDosageFormId,
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public Page<DicDosageFormDTO> getDicDosageForms(Integer pageNo, Integer size, String code) {
        PageHelperBean<DicDosageFormDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/dosage_forms",
                getPageParam(pageNo, size, null, code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return PageUtils.toHibernatePage(result);
    }

    @Override
    public List<DicDosageFormDTO> getDicDosageFormByIds(List<Long> ids, String code) {
        List<DicDosageFormDTO> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/dosage_forms/ids",
                null,
                ids, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public void delDicDosageFormById(Long id, String hospitalCode) {
        DrugStoreUtils.send(DELETE, INNER_ADMIN + "/dosage_forms/" + id,
                getCodeParam(hospitalCode), null, null);
    }

    @Override
    public List<DicDosageFormDTO> getAllDicDosageFormDTO(String code) {
        List<DicDosageFormDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/dosage_forms/all",
                getCodeParam(code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public DicDosageUnitDTO addDicDosageUnit(DicDosageUnitDTO dto) {
        return DrugStoreUtils.send(POST, INNER_ADMIN + "/dosage_units",
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public DicDosageUnitDTO updateDicDosageUnit(long dicDosageUnitId, DicDosageUnitDTO dto) {
        return DrugStoreUtils.send(PUT, INNER_ADMIN + "/dosage_units/" + dicDosageUnitId,
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public Page<DicDosageUnitDTO> getDicDosageUnits(Integer pageNo, Integer size, String code) {
        PageHelperBean<DicDosageUnitDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/dosage_units",
                getPageParam(pageNo, size, null, code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return PageUtils.toHibernatePage(result);
    }

    @Override
    public List<DicDosageUnitDTO> getDicDosageUnitDTOByIds(List<Long> ids, String code) {
        List<DicDosageUnitDTO> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/dosage_units/ids",
                null,
                ids, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public void delDicDosageUnitById(Long id, String hospitalCode) {
        DrugStoreUtils.send(DELETE, INNER_ADMIN + "/dosage_units/" + id,
                getCodeParam(hospitalCode), null, null);
    }

    @Override
    public List<DicDosageUnitDTO> getAllDicDosageUnit(String code) {
        List<DicDosageUnitDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/dosage_units/all",
                getCodeParam(code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public DicMedInfoDTO createDicMedInfo(DicMedInfoDTO dto) {
        return DrugStoreUtils.send(POST, INNER_ADMIN + "/drugs",
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public DicMedInfoDTO updateDicMedInfo(long dicMedInfoId, DicMedInfoDTO dto) {
        return DrugStoreUtils.send(PUT, INNER_ADMIN + "/drugs/" + dicMedInfoId,
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public Page<DicMedInfoDTO> searchDrugDicts(Integer pageNo, Integer size, String query, String code, Hospital hospital) {
        Map<String, String> pageParam = getPageParam(pageNo, size, query, code);
        PageHelperBean<DicMedInfoDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/drugs", pageParam,
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        User user = CurrentUser.getOrNull();
        Page<DicMedInfoDTO> hibernatePage = PageUtils.toHibernatePage(result);
        if (user == null) {
            return hibernatePage;
        }
        MedicalWorker medicalWorker =
                medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElse(null);


        // 判断药品有没有被医生收藏
        if (medicalWorker != null && CollectionUtil.isNotEmpty(hibernatePage.getContent())) {
            Page<DicMedInfoDTO> doctorDrugs = getDoctorDrugs(PageRequest.of(0, 200), medicalWorker.getId(), null, hospital);
            List<Long> likeDrugIds = doctorDrugs.getContent().stream().map(DicMedInfoDTO::getId).collect(Collectors.toList());

            hibernatePage.getContent().forEach(med -> {
                if (CollectionUtil.isEmpty(likeDrugIds)) {
                    med.setLike(false);
                } else med.setLike(likeDrugIds.contains(med.getId()));
            });
        }
        return hibernatePage;
    }

    @Override
    public List<DrugStoreDrugDictVM> getDrugDictsByIds(List<Long> ids, Hospital hospital) {
        List<DrugStoreDrugDictVM> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/drugs/ids",
                null,
                ids, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        User user = CurrentUser.getOrNull();
        if (user == null) {
            return result;
        }
        MedicalWorker medicalWorker =
                medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElse(null);

        // 判断药品有没有被医生收藏
        if (medicalWorker != null && CollectionUtil.isNotEmpty(result)) {
            Page<DicMedInfoDTO> doctorDrugs = getDoctorDrugs(PageRequest.of(0, 200), medicalWorker.getId(), null, hospital);
            List<Long> likeDrugIds = doctorDrugs.getContent().stream().map(DicMedInfoDTO::getId).collect(Collectors.toList());
            result.forEach(med -> {
                if (CollectionUtil.isEmpty(likeDrugIds)) {
                    med.setLike(false);
                } else med.setLike(likeDrugIds.contains(med.getId()));
            });
        }


        return result;
    }

    @Override
    public DicMedInfoDTO getDrugDictsById(Long id) {
        List<DicMedInfoDTO> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/drugs/ids",
                null,
                Lists.newArrayList(id), new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result.stream().findAny().orElse(null);
    }

    @Override
    public DicMedInfoDTO getDrugDictsByCode(String code, String hospitalCode) {
        DrugSelectVM drugSelectVM = new DrugSelectVM();
        drugSelectVM.setCodes(Lists.newArrayList(code));
        drugSelectVM.setHospitalCode(hospitalCode);
        List<DicMedInfoDTO> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/drugs/codes",
                                                         null,
                                                         drugSelectVM, new TypeReference<>() {
            });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result.stream().findAny().orElse(null);
    }

    @Override
    public void delDicMedInfoById(Long id, String hospitalCode) {
        DrugStoreUtils.send(DELETE, INNER_ADMIN + "/drugs/" + id,
                getCodeParam(hospitalCode), null, null);
    }

    @Override
    public DicPackageUnitDTO addDicPackageUnit(DicPackageUnitDTO dto) {
        return DrugStoreUtils.send(POST, INNER_ADMIN + "/package_units",
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public DicPackageUnitDTO updateDicPackageUnit(long dicPackageUnitId, DicPackageUnitDTO dto) {
        return DrugStoreUtils.send(PUT, INNER_ADMIN + "/package_units/" + dicPackageUnitId,
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public Page<DicPackageUnitDTO> getDicPackageUnits(Integer pageNo, Integer size, String code) {
        PageHelperBean<DicPackageUnitDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/package_units",
                getPageParam(pageNo, size, null, code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return PageUtils.toHibernatePage(result);
    }

    @Override
    public List<DicPackageUnitDTO> getDicPackageUnitsByIds(List<Long> ids, String code) {
        List<DicPackageUnitDTO> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/package_units/ids",
                null,
                ids, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public void delDicPackageUnitById(Long id, String hospitalCode) {
        DrugStoreUtils.send(DELETE, INNER_ADMIN + "/package_units/" + id,
                getCodeParam(hospitalCode), null, null);
    }

    @Override
    public List<DicPackageUnitDTO> getAllDicPackageUnitDTO(String code) {
        List<DicPackageUnitDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/package_units/all",
                getCodeParam(code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public DicMedAdmFreqDTO addDicMedAdmFreq(DicMedAdmFreqDTO dto) {
        return DrugStoreUtils.send(POST, INNER_ADMIN + "/drug_adm_freqs",
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public DicMedAdmFreqDTO updateDicMedAdmFreq(long dicMedAdmFreqId, DicMedAdmFreqDTO dto) {
        return DrugStoreUtils.send(PUT, INNER_ADMIN + "/drug_adm_freqs/" + dicMedAdmFreqId,
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public Page<DicMedAdmFreqDTO> getDicMedAdmFreqs(Integer pageNo, Integer size, String code) {
        PageHelperBean<DicMedAdmFreqDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/drug_adm_freqs",
                getPageParam(pageNo, size, null, code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return PageUtils.toHibernatePage(result);
    }

    @Override
    public List<DicMedAdmFreqDTO> getDicMedAdmFreqsByIds(List<Long> ids, String code) {
        List<DicMedAdmFreqDTO> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/drug_adm_freqs/ids",
                null,
                ids, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public void delDicMedAdmFreqById(Long id, String hospitalCode) {
        DrugStoreUtils.send(DELETE, INNER_ADMIN + "/drug_adm_freqs/" + id,
                getCodeParam(hospitalCode), null, null);
    }

    @Override
    public List<DicMedAdmFreqDTO> getAllDicMedAdmFreqDTO(String code) {
        List<DrugAdmFreqDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/drug_adm_freqs/all",
                                                          getCodeParam(code),
                                                          null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result.stream().map(DrugAdmFreqDTO::toDTO).collect(Collectors.toList());
    }

    @Override
    public DicMedAdmRouteDTO addDicMedAdmRoute(DicMedAdmRouteDTO dto) {
        return DrugStoreUtils.send(POST, INNER_ADMIN + "/drug_adm_routes",
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public DicMedAdmRouteDTO updateDicMedAdmRoute(long dicMedAdmRouteId, DicMedAdmRouteDTO dto) {
        return DrugStoreUtils.send(PUT, INNER_ADMIN + "/drug_adm_routes/" + dicMedAdmRouteId,
                null, dto, new TypeReference<>() {
                });
    }

    @Override
    public Page<DicMedAdmRouteDTO> getDicMedAdmRoutes(Integer pageNo, Integer size, String code) {
        PageHelperBean<DicMedAdmRouteDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/drug_adm_routes",
                getPageParam(pageNo, size, null, code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return PageUtils.toHibernatePage(result);
    }

    @Override
    public List<DicMedAdmRouteDTO> getDicMedAdmRoutesByIds(List<Long> ids, String code) {
        List<DicMedAdmRouteDTO> result = DrugStoreUtils.send(POST, INNER_ADMIN + "/drug_adm_routes/ids",
                null,
                ids, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public void delDicMedAdmRouteById(Long id, String hospitalCode) {
        DrugStoreUtils.send(DELETE, INNER_ADMIN + "/drug_adm_routes/" + id,
                getCodeParam(hospitalCode), null, null);
    }

    @Override
    public List<DicMedAdmRouteDTO> getAllDicMedAdmRouteDTO(String code) {
        List<DicMedAdmRouteDTO> result = DrugStoreUtils.send(GET, INNER_ADMIN + "/drug_adm_routes/all",
                getCodeParam(code),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public void syncHisDrugInfo(Hospital hospital) {
        DrugStoreUtils.send(GET, INNER_ADMIN + "/sync_his_drug_info",
                getCodeParam(hospital.getCode()), null, null);
    }

    @Override
    public Page<DicMedInfoDTO> getDoctorDrugs(Pageable page, Long doctorId, String name, Hospital hospital) {
        if (StringUtils.isNotBlank(name)) {
            if (name.contains(" ")) {
                name = "\"" + name + "\"";
            }
        }
        Map<String, String> params = new HashMap<>();
        params.put("size", page.getPageSize() + "");
        params.put("page", page.getPageNumber() + 1 + "");
        params.put("doctorId", String.valueOf(doctorId));
        params.put("name", name);
        if (hospital != null) {
            params.put("hospitalCode", hospital.getCode());
        }

        PageHelperBean<DicMedInfoDTO> result = DrugStoreUtils.send("GET", "/inner/doctor/drug/list",
                params, null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return PageUtils.toHibernatePage(result);
    }

    @Override
    public DicMedInfoDTO getDrugDetail(String drugCode) {
        Map<String, String> params = new HashMap<>();
        params.put("drugCode", drugCode);

        DicMedInfoDTO result = DrugStoreUtils.send("GET", "/inner/doctor/drug/detail",
                                                                   params, null, new TypeReference<>() {
            });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public void saveDoctorDrug(DoctorDrugParamDTO dto) {
        DrugStoreUtils.send("POST", "/inner/doctor/drug", null, StandardObjectMapper.stringify(dto), new TypeReference<>() {
        });
    }

    @Override
    public void delDoctorDrug(DoctorDrugParamDTO dto) {
        DrugStoreUtils.send("DELETE-JSON", "/inner/doctor/drug", null, StandardObjectMapper.stringify(dto), new TypeReference<>() {
        });
    }

    @Override
    public void toPaying(OrderStatusChangeVM changeVM) {
        DrugStoreUtils.send(POST, "/inner/admin/orders/toPaying",
                null, changeVM, new TypeReference<>() {
                });
    }

    @Override
    public void paySuccess(OrderStatusChangeVM changeVM) {
        DrugStoreUtils.send(POST, "/inner/admin/orders/paySuccess",
                null, changeVM, new TypeReference<>() {
                });
    }


    @Override
    public void refundSuccess(OrderStatusChangeVM changeVM) {
        DrugStoreUtils.send(POST, "/inner/admin/orders/refundSuccess",
                null, changeVM, new TypeReference<>() {
                });
    }

    @Override
    public void recipeInvalid(OrderStatusChangeVM changeVM) {
        DrugStoreUtils.send(POST, "/inner/admin/orders/recipe/invalid",
                null, changeVM, new TypeReference<>() {
                });
    }

    @Override
    public PageHelperBean<OrderInfoAdminDTO> getDrugOrderPage(Integer pageNo, Integer size, String hospitalCode, String query) {
        PageHelperBean<OrderInfoAdminDTO> result = DrugStoreUtils.send(GET, "/inner/admin/orders",
                getPageParam(pageNo, size, query, hospitalCode),
                null, new TypeReference<>() {
                });
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public OrderInfoDTO getDrugStoreOrderInfo(long prescriptionOrderId) {
        // 获取已发货和已收货的
        String query = "orderStatusList:DELIVER,DELIVER_SIGN selfPick:false";

        OrderInfoDTO result = DrugStoreUtils.send(GET, "/inner/prescription/out/" + prescriptionOrderId,
                null, null, new TypeReference<>() {});
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }

    @Override
    public OrderInfoAdminDTO getDrugOrderById(long drugOrderId) {
        return DrugStoreUtils.send(GET, "/inner/admin/orders/" + drugOrderId, null, null, new TypeReference<>() {});
    }

    @Override
    public DrugPrescriptionDTO getDrugRecipeByOrderId(long drugOrderId) {
        return DrugStoreUtils.send(GET, "/inner/admin/orders/" + drugOrderId + "/recipe",
                null, null, new TypeReference<>() {});
    }

    @Override
    public DrugPrescriptionDTO statusChange(OrderStatusChangeDTO change) {
        return DrugStoreUtils.send(POST, "/inner/admin/orders/statusChange", null, change, new TypeReference<>() {});
    }

    @Override
    public List<LogisticsRouteInfo> getLogisticsInfo(String hospitalCode, String trackingType, String trackingNumber,
                                                     String phone, ExpressData expressData) {
        Map<String, String> param = new HashMap<>();
        param.put("hospitalCode", hospitalCode);
        param.put("trackingType", trackingType);
        param.put("trackingNumber", trackingNumber);
        param.put("phone", phone);

        List<LogisticsRouteInfo> result = DrugStoreUtils.send(POST, "/inner/admin/logistics/info",
                param, expressData, new TypeReference<>() {});
        if (result == null) {
            throw new RuntimeException("药品库调用失败");
        }
        return result;
    }
}
