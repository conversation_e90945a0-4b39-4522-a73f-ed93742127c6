package cn.taihealth.ih.service.impl.filter.user;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.impl.filter.SearchFilter;

import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;

import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

/**
 */
public class FullNameFilter implements SearchFilter<User> {

    private final String pattern;

    public FullNameFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<User> toSpecification() {
        return Specifications.likeIgnoreCase("fullName", pattern);
    }

    @Override
    public String toExpression() {
        return "fullName:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof FullNameFilter)) {
            return false;
        }

        FullNameFilter rhs = (FullNameFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
