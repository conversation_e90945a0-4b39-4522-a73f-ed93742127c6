package cn.taihealth.ih.service.impl.filter.offline.evaluate;

import cn.taihealth.ih.domain.Evaluate;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import org.springframework.data.jpa.domain.Specification;
import javax.persistence.criteria.Join;
import java.util.Objects;

/**
 *
 */
public class DoctorNameSearch implements SearchFilter<Evaluate> {

    private final String pattern;

    public DoctorNameSearch(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Evaluate> toSpecification() {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Join<Evaluate, OfflineOrder> offlineOrderJoin = root.join("offlineOrder");
            Join<OfflineOrder, OfflineMedicalWorker> offlineMedicalWorkerJoin = offlineOrderJoin.join("doctor");
            return criteriaBuilder.like(offlineMedicalWorkerJoin.get("name"), "%" + pattern + "%");
        };
    }

    @Override
    public String toExpression() {
        return "Order:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof DoctorNameSearch)) {
            return false;
        }

        DoctorNameSearch rhs = (DoctorNameSearch) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
