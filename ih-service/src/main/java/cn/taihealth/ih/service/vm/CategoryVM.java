package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.hospital.Category;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 *
 */
public class CategoryVM extends UpdatableDTO {

    private String name;

    private String slug;

    @ApiModelProperty(value = "背景色RGB值，不填默认是#FFFFFF（白色）", required = true)
    private String color = "#FFFFFF";

    @ApiModelProperty(value = "文字颜色RGB值，不填默认是#000000（黑色）", required = true)
    private String textColor = "#000000";
    private long parentId;

    private String keyword;

    private Boolean isUse = true;

    private Integer orderValue;

    private List<CategoryVM> categories = Lists.newArrayList();

    public CategoryVM() {
    }

    public CategoryVM(Category category) {
        super(category);
        name = category.getName();
        slug = category.getSlug();
        color = category.getColor();
        textColor = category.getTextColor();
        keyword = category.getKeyword();
        isUse = category.getIsUse();
        orderValue = category.getOrderValue();
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Boolean getIsUse() {
        return isUse;
    }

    public void setIsUse(Boolean isUse) {
        this.isUse = isUse;
    }

    public Integer getOrderValue() {
        return orderValue;
    }

    public void setOrderValue(Integer orderValue) {
        this.orderValue = orderValue;
    }

    public List<CategoryVM> getCategories() {
        return categories;
    }

    public void setCategories(List<CategoryVM> categories) {
        this.categories = categories;
    }
}