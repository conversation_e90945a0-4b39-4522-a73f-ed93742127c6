package cn.taihealth.ih.service.api.ai;

import cn.taihealth.ih.domain.cloud.DifyAppConfig;
import cn.taihealth.ih.domain.cloud.Hospital;

import java.util.List;
import java.util.Optional;

/**
 * Dify应用配置服务接口
 */
public interface DifyAppConfigService {

    /**
     * 根据应用ID查找配置
     * 
     * @param appId 应用ID
     * @return 配置信息
     */
    Optional<DifyAppConfig> findByAppId(String appId);

    /**
     * 根据医院ID和应用类型查找配置
     * 
     * @param hospitalId 医院ID
     * @param appType 应用类型
     * @return 配置信息
     */
    Optional<DifyAppConfig> findByHospitalIdAndAppType(Long hospitalId, String appType);

    /**
     * 根据医院ID查找所有配置
     * 
     * @param hospitalId 医院ID
     * @return 配置列表
     */
    List<DifyAppConfig> findByHospitalId(Long hospitalId);

    /**
     * 根据医院ID和启用状态查找配置
     * 
     * @param hospitalId 医院ID
     * @param enabled 启用状态
     * @return 配置列表
     */
    List<DifyAppConfig> findByHospitalIdAndEnabled(Long hospitalId, Boolean enabled);

    /**
     * 保存配置
     * 
     * @param config 配置信息
     * @return 保存后的配置
     */
    DifyAppConfig save(DifyAppConfig config);

    /**
     * 删除配置
     * 
     * @param id 配置ID
     */
    void deleteById(Long id);

    /**
     * 清除缓存
     * 
     * @param appId 应用ID
     */
    void evictCache(String appId);
}
