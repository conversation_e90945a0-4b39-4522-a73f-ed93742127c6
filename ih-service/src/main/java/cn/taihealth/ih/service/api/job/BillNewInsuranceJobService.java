package cn.taihealth.ih.service.api.job;


import cn.taihealth.ih.domain.cloud.WechatInsuranceOrder;
import cn.taihealth.ih.domain.cloud.WechatInsuranceOrderRefund;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.domain.hospital.HisBill;

import java.util.List;

public interface BillNewInsuranceJobService {
    void checkWeChatInsurancePayedBills(List<WechatInsuranceOrder> wechatInsuranceOrderList);
    void checkWeChatInsuranceRefundBills(List<WechatInsuranceOrderRefund> refunds);
    void checkAliPayInsurancePayedBills(List<AliPayOrder> aliPayOrderList);
    void checkAliPayInsuranceRefundBills(List<AliPayOrderRefund> refunds);
    void groupGenerateHisBill(List<HisBill> hisBills);
}
