package cn.taihealth.ih.service.impl.strategy;

import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.ElectronicMedicCard.CardType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.service.dto.CreateOfflineOrderDTO;
import cn.taihealth.ih.service.dto.ElectronicMedicCardDTO;
import cn.taihealth.ih.service.dto.OfflineOrderDTO;
import cn.taihealth.ih.service.dto.nodeRed.ConfirmRefund;
import cn.taihealth.ih.service.vm.crm.FollowUpParam;
import cn.taihealth.ih.service.vm.nodered.NodeRedResponseData;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.LockNumberResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.ReturnRegistResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.SyncInspectMRResult;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.service.vm.platform.PayPlatform;
import cn.taihealth.ih.wechat.service.vm.wechat.MedicalInsuranceParam;
import org.springframework.data.domain.Page;

import java.util.List;

public interface BusinessService {

    /**
     * 同步诊断字典（疾病信息）
     *
     * @param hospital 医院
     */
    void syncDicDiagnose(Hospital hospital);

    /**
     * 同步收费项目
     *
     * @param hospital 医院
     */
    void syncChargeItem(Hospital hospital);


    /**
     * 获取电子就诊卡
     *
     * @param patient  就诊人
     * @param hospital 医院
     * @return 电子就诊卡列表
     */
    List<ElectronicMedicCardDTO> getElectronicMedicCard(Patient patient, Hospital hospital);

    /**
     * 添加电子就诊卡
     *
     * @param hospcode 医院编码
     * @param patient  就诊人
     * @param cardNo   就诊卡号
     * @param cardType 就诊卡类型
     * @return 就诊人在his唯一id   hisPatid
     */
    String addPatientCards(String hospcode, Patient patient, String cardNo, CardType cardType, String patid);

    MedicalCardInfo createMedicalCard(Hospital hospital, Patient patient);

    /**
     * 更新就诊人信息
     *
     * @param hospital 医院
     * @param patient  就诊人
     */
    void updatePatientInfo(Hospital hospital, Patient patient);
    void syncDoctorOffline(Hospital hospital);
    void syncDept(Hospital hospital);

    /**
     * 字典类接口
     */
    void syncCheckCategory(Hospital hospital);
    void syncCheckItem(Hospital hospital);
    void syncCheckDevices(Hospital hospital);

    /**
     * 推送变更消息
     * @param hospital
     * @param msg_type 1 渠道字典变更 2 排班信息变更
     * @param channel_code 渠道编码
     */
    void pushDictChangeMsg(Hospital hospital, String msg_type, String channel_code);


    /**
     * 查询某个检查项目的报告出具时限
     * @param hospital
     * @param item_code 检查项目代码
     */
    ReportTimeLimit getReportTimeLimit(Hospital hospital, String item_code);

    /**
     * 查询患者检查申请单信息
     * @param hospitalCode
     * @param pat_name 患者姓名
     * @param apply_no 申请单号
     * @param certificate_no 身份证号、军官证号、护照号等
     * @param card_no 就诊卡号码
     * @param pat_id 患者唯一号
     * @param begin_date 开始日期
     * @param end_date 结束日期
     * @return
     */
    List<ApplicationInfo> getApplicationInfoList(String hospitalCode, String pat_name, String apply_no, String certificate_no,
                                                 String card_no, String pat_id, String patient_type, String begin_date, String end_date);

    void cancelCheckApplication(String hospitalCode, String patname, String patid, String application_no,
                                String application_category, String message);

    /**
     * 查询检查报告结果
     * @param hospitalCode
     * @param report_no 报告单号
     * @param report_type_code 报告类别代码
     * @return
     */
    List<CheckReportResult> getCheckReportResult(String hospitalCode, String report_no, String report_type_code);

    /**
     * 查询患者医技排队候诊信息
     * @param hospitalCode
     * @param patname 患者姓名
     * @param patid 门诊患者唯一号
     * @param regist_type 急诊标志
     * @return
     */
    MedTechWaitingList getMedTechWaiting(String hospitalCode, String patname, String patid, String regist_type);

    void pushAppointmentChangeMsg(String hospitalCode, String application_id, String appointment_id,
                                          String operation_id, String operation_name,
                                          String operation_time, String channel_code, String status, String message);

    /**
     * 查询门诊患者可续方处方信息(含明细)
     * @param hospital
     * @param patient
     * @param beginDate
     * @param endDate
     * @param insuranceParam
     * @return
     */
    List<RecipeInfo> getPatientRecipeListRenewable(Hospital hospital, Patient patient, String beginDate, String endDate, String insuranceParam);


    /**
     * 查询全院当天号源信息
     * @param hospital
     * @param channel_type
     * @return
     */
    List<CurrentDayAppointment> getCurrentDayAppointmentList(Hospital hospital, String channel_type);

    /**
     * 获取当天出诊医生列表
     *
     * @param hospital 医院
     * @param dept     科室
     * @return 医生列表信息列表
     */
    List<DoctorScheduleInfo> getCurrentDayDoctorList(Hospital hospital, Dept dept);

    /**
     * 获取当天出诊医生号源详情
     *
     * @param hospital
     * @param medicalWorker
     * @return
     */
    List<DoctorSourceDetail> getCurrentDayDoctorSourceDetail(Hospital hospital, MedicalWorker medicalWorker);

    /**
     * 取就诊排序信息
     *
     * @param hospital  医院
     * @param scheduleId his排班id
     * @return
     */
    SchedulingSourceNumber getCurrentDaySchedulingSourceNumber(Hospital hospital, String scheduleId, String source_number);

    /**
     * 在线复诊/咨询预算
     * 预挂号：包括两步：（锁号 -> 预算）
     * 这个方法会把把订单关联的his信息都set到对应的实体类上，但是没有保存这个关联信息，需要在调用这个方法之后需要保存OrderExtraInfo
     *
     * @param order             订单
     * @param orderExtraInfo     his排班信息
     * @return
     */
    PreRegistrationResult preRegisterOnlineOrder(Order order, OrderExtraInfo orderExtraInfo, PlatformTypeEnum platformType);

    /**
     * 门诊挂号预算
     * @param hospital
     * @param order
     * @param registrationFeeCode
     * @param treatmentFeeCode
     * @param childrenTreatmentFeeCode
     * @param insuranceParam 医保结算代码
     * @return
     */
    PreRegistrationResult preRegisterOfflineOrder(Hospital hospital, OfflineOrder order, String registrationFeeCode,
                                                  String treatmentFeeCode, String childrenTreatmentFeeCode, MedicalInsuranceParam insuranceParam);

    /**
     * 门诊预约登记
     * @param hospital
     * @param offlineOrder
     * @param registrationFeeCode
     * @param treatmentFeeCode
     * @param childrenTreatmentFeeCode
     * @return
     */
    void outPatientAppointment(Hospital hospital, OfflineOrder offlineOrder, String registrationFeeCode,
                               String treatmentFeeCode, String childrenTreatmentFeeCode);

    /**
     * 预约登记转预算
     * @param hospital
     * @param order
     * @param patient
     * @param registrationFeeCode
     * @param treatmentFeeCode
     * @param childrenTreatmentFeeCode
     * @param insuranceParam 医保结算代码
     * @return
     */
    PreRegistrationResult outPatientPreRegister(Hospital hospital, OfflineOrder order, Patient patient,
                                                String registrationFeeCode, String treatmentFeeCode,
                                                String childrenTreatmentFeeCode, MedicalInsuranceParam insuranceParam);

    /**
     * 取消预挂号
     *
     * @param hospital 医院
     * @param order    就诊订单
     * @return
     */
    HisGeneralResult cancelPreRegist(Hospital hospital, Order order);

    /**
     * 门诊挂号预算撤销
     * @param hospital
     * @param patient
     * @param regNo
     * @param settleId
     * @return
     */
    HisGeneralResult cancelPreRegist(Hospital hospital, Patient patient, String regNo, String settleId);

    /**
     * 挂号结算
     *
     * @param hospital
     * @param order
     * @param hisPayParam
     * @return
     */
    NodeRedResponseData<ConfirmRegistResult> confirmRegist(Hospital hospital, Order order, HisPayParam hisPayParam);

    NodeRedResponseData<ConfirmRegistResult> confirmRegist(Hospital hospital, OfflineOrder order, HisPayParam hisPayParam);

    /**
     * his门诊预约挂号签到
     * @param hospcode
     * @param signInReq
     * @return
     */
    NodeRedResponseData<AppointmentSignInResult> appointmentSignIn(String hospcode, AppointmentSignInReq signInReq);

    ReturnRegistResult returnRegist(Hospital hospital, Order order, PayPlatform payPlatform);

    /**
     * 预约挂号退号
     * @param hospital
     * @param order
     * @param transactionId
     * @param insuranceParam 医保代码
     * @return
     */
    ReturnRegistResult returnRegist(Hospital hospital, OfflineOrder order, String transactionId, MedicalInsuranceParam insuranceParam);

    LockNumberResult lockNumber(Hospital hospital, String hisPid, OrderExtraInfo orderExtraInfo);

    HisGeneralResult unlock(Hospital hospital, Order order);

    SaveRecipeResult saveRecipeOnline(Hospital hospital, PrescriptionOrder prescriptionOrder);
    SaveMedicalCaseResult saveMedicalCaseOnline(Hospital hospital, Order order);
    SaveMedicalCaseResult saveDiagnoseOnline(Hospital hospital, Order order);

    ReturnRegistResult deleteRecipeOnline(Hospital hospital, Long orderId, String patientName, String patHisId,
                                          String recipeId);

    HisGeneralResult noticeForRecipeCheck(Hospital hospital, Order order, List<PrescriptionOrder> prescriptionOrders);

    PreChargeResult preCharge(Hospital hospital, PrescriptionOrder order, PlatformTypeEnum platformType);

    /**
     * 门诊缴费预算
     * @param hospital
     * @param patient
     * @param card
     * @param regno
     * @param insuranceParam 医保线上核验payAuthNo，对应his的insurance_param医保交易入参
     * @param recipeNoList
     * @return
     */
    PreChargeResult outPatientPreCharge(Hospital hospital, Patient patient, ElectronicMedicCard card, String regno, String insuranceParam,
                                        List<String> recipeNoList, String payType, String extraContent);

    PreChargeResult outPatientPreCharge(Hospital hospital, String hisPatId,
                                        String regno,
                                        String insuranceParam, List<String> recipeNoList, String payType, String extraContent);

    HisGeneralResult cancelPreCharge(Hospital hospital, PrescriptionOrder prescriptionOrder);

    /**
     * 线上处方结算
     * @param hospital
     * @param prescriptionOrder
     * @param wechatOrder
     * @return
     */
    NodeRedResponseData<ConfirmChargeResult> confirmCharge(Hospital hospital, PrescriptionOrder prescriptionOrder, WechatOrder wechatOrder,
                                                           HisPayParam hisPayParam);
    NodeRedResponseData<ConfirmChargeResult> confirmCharge(Hospital hospital, PrescriptionOrder prescriptionOrder,
                                                           AliPayOrder ailPayOrder);

    /**
     * 线下门诊缴费结算
     * @param hospital
     * @param card 就诊卡
     * @param hisPayParam
     * @param info
     * @return
     */
    NodeRedResponseData<ConfirmChargeResult> outPatientConfirmCharge(Hospital hospital, ElectronicMedicCard card, HisPayParam hisPayParam,
                                                                     OutpatientUnChargeRecipeInfo info, HisOutpatientCharge charge);

    NodeRedResponseData<ConfirmChargeResult> outPatientConfirmCharge(Hospital hospital, String hisPatId,
                                                                     HisPayParam hisPayParam,
                                                                     OutpatientUnChargeRecipeInfo info,
                                                                     HisOutpatientCharge charge);

    PayResultOnline getPayResultOnline(Hospital hospital, Order order);

    CancelOutpatientSettleResult cancelOutpatientSettle(Hospital hospital, Order order, PayPlatform payPlatform);

    HisFileUploadInfo uploadFile(Hospital hospital, Long orderId, String type, String url);

    Page<FollowUpInfo> getFollowUpListByPatient(Hospital hospital, Patient patient, FollowUpParam param, Integer pageNo, Integer size);

    Page<DeptAndWardInfo> getDeptAndWardInfo(Hospital hospital, Integer pageNo, Integer size);
    List<WardBedInfo> getWardBedInfo(Hospital hospital, String deptId);

    /**
     * 查询his账单
     * @param hospital 医院
     * @param billDate  日期，查日期当天
     * @param payType   1 微信支付 2 支付宝支付
     * @return
     */
    BillSummary getHisBills(Hospital hospital, String billDate, String payType);

    /**
     * 门诊预约登记
     * @param hospital
     * @param createOfflineOrderDTO
     * @return
     */
    OfflineOrderDTO saveAppointment(Hospital hospital, CreateOfflineOrderDTO createOfflineOrderDTO);

    /**
     * 门诊取消预约
     * @param hospital
     * @param offlineOrder
     * @return
     */
    ReponseResult cancelAppointment(Hospital hospital, OfflineOrder offlineOrder);

    /**
     * 异常账单冲正（退款）
     * @param hospital
     * @param order
     * @return
     */
    ReverseAbnormalBillResult reverseAbnormalBill(Hospital hospital, Order order);

    /**
     * 上传互联网医院交易账单
     * @param hospital
     * @param billSummary
     * @return
     */
    TradeIHBillResult tradeIHBill(Hospital hospital, TradeIHBillSummary billSummary);

    /**
     * 住院预交金预充值
     * @param hospital
     * @param patientName
     * @param rego
     * @return
     */
    InpatientHospCardPreChargeResult inpatientHospCardPreCharge(Hospital hospital, String patientName, String rego);

    /**
     * 住院预交金充值
     * @param hospital
     * @param patientName
     * @param reg_no
     * @param inpatientHospitalChargeReq
     * @return
     */
    NodeRedResponseData<InpatientHospCardChargeResult> inpatientHospCardCharge(Hospital hospital,
                                                          String patientName,
                                                          String reg_no,
                                                          InpatientHospitalChargeReq inpatientHospitalChargeReq);

    /**
     * 病人出院预算
     * @param hospital
     * @param order
     * @return
     */
    InpatientPreChargeResult inpatientPreCharge(Hospital hospital, Order order);

    /**
     * 病人出院结算
     * @param hospital
     * @param order
     * @return
     */
    InpatientConfirmChargeResult inpatientConfirmCharge(Hospital hospital, Order order);

    /**
     * 全院预约号源信息
     * @param hospital
     * @param beginDate
     * @param endDate
     * @param channelType
     * @param deptId
     * @param doctorName
     * @return
     */
    List<AppointmentInfo> getSourceDetails(Hospital hospital, String beginDate, String endDate, String channelType, String deptId, String doctorName);

    /**
     * 排班序号的号源信息
     * @param hospital
     * @param schedulingId
     * @param sourceType
     * @param channelType
     * @return
     */
    List<SchedulingInfo> getSchedulingListById(Hospital hospital, String schedulingId, String sourceType, String channelType);

    /**
     * 时间区间内的有排班的科室信息（包含专家出诊的科室
     * @param hospital
     * @param beginDate
     * @param endDate
     * @param channelType
     * @return
     */
    List<SchedulingDeptInfo> getSchedulingDeptList(Hospital hospital, String beginDate, String endDate, String channelType);
    /**
     * 时间区间内的有排班的科室信息（包含专家出诊的科室
     * @param hospital
     * @param beginDate
     * @param endDate
     * @param channelType
     * @return
     */
    List<SchedulingDeptInfo> getSchedulingDeptListTest(Hospital hospital, String beginDate, String endDate, String channelType);

    /**
     * 时间区间内的有排班的医生信息
     * @param hospital
     * @param deptId
     * @param beginDate
     * @param endDate
     * @param channelType
     * @return
     */
    List<SchedulingDoctorInfo> getSchedulingDoctorList(Hospital hospital, String deptId, String beginDate, String endDate, String channelType);

    /**
     * 指定科室预约号源信息
     * @param hospital
     * @param beginDate
     * @param endDate
     * @param deptId
     * @param channelType
     * @return
     */
    List<SchedulingDeptSourceInfo> getSchedulingDeptSourceDetails(Hospital hospital, String beginDate, String endDate, String deptId, String channelType);

    /**
     * 查询预约医生号源信息
     * @param hospital
     * @param beginDate
     * @param endDate
     * @param doctorId
     * @param channelType
     * @return
     */
    List<SchedulingDoctorSourceInfo> getSchedulingDoctorSourceDetails(Hospital hospital,
                                                                      String beginDate, String endDate, String doctorId,
                                                                      String channelType);

    /**
     * 查询患者预约记录
     * @param hospital
     * @param patientName
     * @param patientId
     * @param beginDate
     * @param endDate
     * @param routeFlag
     * @param dateType
     * @return
     */
    List<PatientAppointmentInfo> getPatientAppointment(Hospital hospital, String patientName, String patientId, String beginDate,
                                                       String endDate, String routeFlag, String dateType);

    /**
     * 根据门诊患者唯一号查询患者的挂号记录
     * @param hospital
     * @param patientName
     * @param patId
     * @param beginDate
     * @param endDate
     * @param routeFlag
     * @return
     */
    List<PatientRegistInfo> getRegistListByPatId(Hospital hospital, String patientName, String patId, String beginDate,
                                                 String endDate, String routeFlag);

    /**
     * 根据门诊患者唯一号和挂号唯一号查询患者的此条挂号记录
     * @param hospital
     * @param patientName
     * @param regno
     * @param appointmentId
     * @param patientId
     * @param routeFlag
     * @return
     */
    PatientRegistInfo getRegistListByRegno(Hospital hospital, String patientName, String regno, String appointmentId,
                                                 String patientId, String routeFlag);


    /**
     * 通过门诊患者唯一号查询患者当前候诊信息
     * @param hospital
     * @param patientName
     * @param hisPatientId
     * @return
     */
    List<PatientRegistWaitingInfo> getPatientRegistWaitingListByPatId(Hospital hospital, String patientName, String hisPatientId);

    /**
     * 查询患者取药排队信息
     * @param hospital
     * @param patientName
     * @param hisPatientId
     * @return
     */
    List<PatientDrugWaitingInfo> getPatientDrugWaitingList(Hospital hospital, String patientName, String hisPatientId);

    /**
     * 查询门诊项目提示信息
     * @param hospital
     * @param hisPatientId
     * @param settleId
     * @return
     */
    List<OutpatientItemHintInfo> getOutpatientItemHint(Hospital hospital, String hisPatientId, String settleId);

    /**
     * 查询患者到诊信息 返回该预约渠道预约的患者到诊记录，仅支持查询一天。
     * @param hospital
     * @param date
     * @return
     */
    List<PatientSignInfo> getPatientSignInfo(Hospital hospital, String date);

    /**
     * 当前门诊候诊队列查询
     * @param hospital
     * @param medicalWorker
     * @return
     */
    List<PatientWaitingInfo> getPatientWaiting(Hospital hospital, MedicalWorker medicalWorker);

    /**
     * 查询门诊患者待缴费处方信息(未收费处方)
     * @param hospital
     * @param patient
     * @param card
     * @param beginDate
     * @param endDate
     * @param insuranceParam
     * @return
     */
    List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(Hospital hospital, Patient patient, ElectronicMedicCard card,
                                                                       String beginDate, String endDate, String insuranceParam, String channelType);

    List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(Hospital hospital, String hisPatId,
                                                                       String patName,
                                                                       ElectronicMedicCard card,
                                                                       String beginDate, String endDate,
                                                                       String insuranceParam, String channelType);

    /**
     * 查询价格公示列表
     * @param hospital
     * @param pricePublicityType
     * @param keyword
     * @return
     */
    List<PricePublicityInfo> getPricePublicityList(Hospital hospital, String pricePublicityType, String keyword);
    Page<PricePublicityInfo> getPricePublicityPage(Hospital hospital, String pricePublicityType, String keyword, int page, int size);

    /**
     * 查询专家特需出诊公示列表
     * @param hospital
     * @param deptId
     * @param doctorName
     * @return
     */
    List<DoctorSchedules> getDoctorSchedules(Hospital hospital, String deptId, String doctorName, String beginDate, String endDate);

    /**
     * 查询专病门诊公示列表
     * @param hospital
     * @return
     */
    List<SpecialClinicInfo> getSpecialClinicList(Hospital hospital, String beginDate, String endDate);

    /**
     * 查询普通门诊公示列表
     * @param hospital
     * @return
     */
    List<GeneralClinicInfo> getGeneralClinicList(Hospital hospital, String beginDate, String endDate);

    /**
     * 查询检验报告列表（通过患者院内卡号）
     * @param hospital
     * @param patientName
     * @param cardNo
     * @param beginDate
     * @param endDate
     * @return
     */
    List<InspectionReport> getInspectionReportListByCardNo(Hospital hospital, String patientName, String cardNo, String beginDate, String endDate);

    /**
     * 查询检验报告列表
     * @param hospital
     * @param card
     * @param userSource
     * @param beginDate
     * @param endDate
     * @return
     */
    List<InspectionReport> getInspectionReportListByPatId(Hospital hospital, ElectronicMedicCard card, String userSource, String beginDate, String endDate);

    /**
     * 查询实验室检验报告结果
     * @param hospital
     * @param reportNo
     * @param reportTypeCode
     * @return
     */
    List<LaboratoryReport> getLaboratoryReportList(Hospital hospital, String reportNo, String reportTypeCode);


    /**
     * 查询微生物报告列表（通过患者院内卡号）
     * @param hospital
     * @param patientName
     * @param cardNo
     * @param beginDate
     * @param endDate
     * @return
     */
    List<MicroorganismReport> getMicroorganismReportsByCardNo(Hospital hospital, String patientName, String cardNo, String beginDate, String endDate);

    /**
     * 查询微生物报告列表（通过门诊患者唯一号）
     * @param hospital
     * @param patientName
     * @param id
     * @param userSource
     * @param beginDate
     * @param endDate
     * @return
     */
    List<MicroorganismReport> getMicroorganismReportsByPatId(Hospital hospital, String patientName, Long id, Integer userSource,
                                                             String beginDate, String endDate);

    /**
     * 查询实验室微生物报告结果
     * @param hospital
     * @param reportNo
     * @param reportTypeCode
     * @return
     */
    List<LaboratoryMicroorganismsReportResult> getLaboratoryMicroorganismsReportResult(Hospital hospital, String reportNo, String reportTypeCode);

    /**
     * 查询微生物报告列表（通过患者院内卡号）
     * @param hospital
     * @param patientName
     * @param cardNo
     * @param beginDate
     * @param endDate
     * @return
     */
    List<RisReport> getRisReportsByCardNo(Hospital hospital, String patientName, String cardNo, String beginDate, String endDate);



    /**
     * 查询检查报告结果
     * @param hospital
     * @param reportNo
     * @param reportTypeCode
     * @return
     */
    List<RisReportResult> getRisReportResult(Hospital hospital, String reportNo, String reportTypeCode);

    /**
     * 查询检验危急值信息
     * @param hospital
     * @param patientName
     * @param id
     * @param userSource
     * @param beginDate
     * @param endDate
     * @return
     */
    List<CriticalValueReport> getCriticalValueReportByPatId(Hospital hospital, String patientName, Long id, Integer userSource,
                                                            String beginDate, String endDate);

    /**
     * 查询住院患者基本信息（通过病历号和患者联系电话）- 如果his不涉及病历号，可不实现这个接口
     * @param hospital
     * @param patientName
     * @param hisCardNo
     * @param telephone
     * @return
     */
    List<InpatientInfo> getInpatientListByHisCardNo(Hospital hospital, String patientName, String hisCardNo, String telephone);

    /**
     * 查询住院患者基本信息（通过患者院内卡号）
     * @param hospital
     * @param patientName
     * @param cardNo
     * @return
     */
    List<InpatientInfo> getInpatientListByCardNo(Hospital hospital, String patientName, String cardNo);

    /**
     * 查询住院患者基本信息（通过证件号）
     * @param hospital
     * @param patientName
     * @param certNo
     * @return
     */
    List<InpatientInfo> getInpatientListByCertNo(Hospital hospital, String patientName, String certNo);

    /**
     * 查询患者住院就诊记录
     * @param hospital
     * @param patientName
     * @param hisPatientId
     * @param beginDate
     * @param endDate
     * @param status
     * @return
     */
    List<InpatientRecord> getInpatientRecordList(Hospital hospital, String patientName, String hisPatientId, String beginDate,
                                                 String endDate, String status);

    /**
     * 查询患者住院就诊记录
     * @param hospital
     * @param patientName
     * @param hisPatientId
     * @param hospNo
     * @param beginDate
     * @param endDate
     * @param status
     * @return
     */
    List<InpatientRecord> getInpatientRecordList(Hospital hospital, String patientName, String hisPatientId, String hospNo, String beginDate,
                                                 String endDate, String status);


    /**
     * 查询住院患者预交金汇总信息
     * @param hospital
     * @param patientName
     * @param regno
     * @return
     */
    InpatientAdvanceCharge getInpatientAdvanceCharge(Hospital hospital, String patientName, String regno);

    /**
     * 查询住院患者预交金明细
     * @param hospital
     * @param patientName
     * @param regno
     * @return
     */
    List<InpatientAdvanceChargeDetail> getInpatientAdvanceChargeDetailList(Hospital hospital, String patientName, String regno);


    /**
     * 住院病人入院单信息填写
     * @param hospital
     * @param supplyHospitalAdmissionCertificate
     * @return
     */
    SupplyHospitalAdmissionCertificateResult saveSupplyHospitalAdmissionCertificate(Hospital hospital, long card_id,
                                                                                    SupplyHospitalAdmissionCertificateReq supplyHospitalAdmissionCertificate);

    /**
     * 查询患者处方列表
     * @param hospital
     * @param patientName
     * @param patientId
     * @param beginDate
     * @param endDate
     * @param insuranceParam
     * @return
     */
    List<OutpatientRecipeInfo> getOutpatientRecipeList(Hospital hospital, String patientName, String patientId,
                                                       String beginDate, String endDate, String insuranceParam);

    /**
     * 住院病人入院单信息更新
     * @param hospital
     * @param param
     * @return
     */
    ReponseResult updateHospitalAdmissionCertificate(Hospital hospital, HospitalAdmissionCertificateReq param);


    /**
     * 线上入院判断
     * @param hospital
     * @param patientName
     * @param regNo
     * @param admissionNo
     * @param patientId
     * @return
     */
    ReponseResult checkHospitalAdmissionOnline(Hospital hospital, String patientName,
                                               String regNo, String admissionNo, String patientId);

    /**
     * 查询门诊患者缴费记录
     * @param hospital
     * @param patient
     * @param channelType
     * @param beginDate
     * @param endDate
     * @return
     */
    List<OutpatientCharge> getOutpatientChargeList(Hospital hospital, Patient patient, String channelType, String beginDate, String endDate);

    /**
     * 查询门诊患者缴费详细信息
     * @param hospital
     * @param settleId
     * @return
     */
    OutpatientChargeDetail getOutpatientChargeDetails(Hospital hospital, String settleId);

    /**
     * 查询住院患者费用明细信息
     * @param hospital
     * @param patientName
     * @param regNo
     * @param beginDate
     * @param endDate
     * @return
     */
    List<InpatientFeeDetail> getInpatientFeeDetails(Hospital hospital, String patientName, String regNo, String beginDate, String endDate);

    /**
     * 查询住院患者一日清单
     * @param hospital
     * @param name
     * @param regNo
     * @param date
     * @param summaryFlag
     * @return
     */
    List<InpatientDaily> getInpatientDaily(Hospital hospital, String name, String regNo, String date, String summaryFlag);

    /**
     * 查询住院患者结算记录
     * @param hospital
     * @param name
     * @param regNo
     * @return
     */
    List<InpatientSettleInfo> getInpatientSettleList(Hospital hospital, String name, String regNo);

    /**
     * 查询患者入院证信息
     * @param hospital
     * @param name
     * @param cardNo
     * @param hisPatid
     * @param identity
     * @param admissionNo
     * @param beginDate
     * @param endDate
     * @return
     */
    List<HospitalAdmissionCertificate> getHospitalAdmissionCertificateByCardNo(Hospital hospital, String name, String cardNo,
                                                                               String hisPatid, String identity, String admissionNo,
                                                                               String beginDate, String endDate);

    /**
     * 查询住院患者出院带药信息
     * @param hospital
     * @param name
     * @param regNo
     * @return
     */
    List<DischargeMedication> getDischargeMedicationList(Hospital hospital, String name, String regNo);

    /**
     * 查询患者手术提示告知信息
     * @param hospital
     * @param name
     * @param regNo
     * @return
     */
    List<OperationHints> getOperationHints(Hospital hospital, String name, String regNo);

    /**
     * 查询患者手术进展信息
     * @param hospital
     * @param name
     * @param userSource
     * @param regNo
     * @return
     */
    List<OperationSchedule> getOperationSchedule(Hospital hospital, String name, String userSource, String regNo);

    /**
     * 查询患者住院病历(文本段)
     * @param hospital
     * @param name
     * @param regNo
     * @param recType
     * @return
     */
    List<InpatientMedicalRecordText> getInpatientMedicalRecordText(Hospital hospital, String name, String regNo, String recType);

    /**
     * 入院登记提交
     * @param hospital
     * @param param
     * @return
     */
    HospitalAdmission addHospitalAdmission(Hospital hospital, HospitalAdmissionReq param);

    /**
     * 查询检查报告列表
     * @param hospital
     * @param card
     * @param userSource
     * @param beginDate
     * @param endDate
     * @return
     */
    List<RisReport>  getRisReportsByPatId(Hospital hospital, ElectronicMedicCard card, String userSource, String beginDate, String endDate);

    /**
     * 申请电子发票（异步）
     * @param hospital
     * @param param
     * @return
     */
    ApplyElectronicInvoice applyElectronicInvoice(Hospital hospital, Patient patient, ApplyElectronicInvoiceReq param);

    /**
     * 电子发票文件查询
     * @param hospital
     * @param patient
     * @param eleInvoiceNo
     * @return
     */
    ElectronicInvoiceFile getElectronicInvoiceFile(Hospital hospital, Patient patient, String eleInvoiceNo);

    Page<ElectronicInvoiceItem> getElectronicInvoiceList(Hospital hospital, String hisPatid, String online_type, String page, String size);

    /**
     * 查询预约医生号源信息(专家号) 和医生个人信息
     * @param hospital
     * @param beginDate
     * @param endDate
     * @param doctorId
     * @param channelType
     * @return
     */
    SchedulingDoctorSourceDetails getSchedulingDoctorSourceInfo(Hospital hospital, String beginDate, String endDate, String doctorId, String channelType);

    /**
     * 查询专家特需出诊公示列表
     * @param hospital
     * @param deptId
     * @param doctorName
     * @param beginDate
     * @param endDate
     * @param pageNo
     * @param size
     * @return
     */
    Page<DoctorSchedules> getDoctorSchedulesByPage(Hospital hospital, String deptId, String doctorName, String beginDate, String endDate, Integer pageNo, Integer size);

    /**
     * 查询患者体检记录
     * @param hospital
     * @param card
     * @param beginDate
     * @param endDate
     * @return
     */
    List<PhysicalExaminationRecord> getPhysicalExaminationRecord(Hospital hospital, ElectronicMedicCard card, String beginDate, String endDate);

    /**
     * 查询患者体检报告
     * @param hospital
     * @param name
     * @param tjbh
     * @return
     */
    List<PhysicalExaminationReportFile> getPhysicalExaminationReportFile(Hospital hospital, String name, String tjbh);

    /**
     * <pre>
     *     门诊支付结果查询
     *     第三方交易对应的订单结果状态查询是否在HIS交易成功。在订单类交易发生失败时，如网络中断等，通过外部订单号查询结果状态（包含门诊预交金充值、门诊结算、住院预交金充值、住院结算、 门诊预约登记、医保支付结果查询）
     * </pre>
     * @param hospital
     * @param settleId
     * @param tradeType 支付类别	Y	0门诊预约挂号 1门诊缴费结算 2住院预交金充值 3住院结算 4互联网医院挂号 5互联网医院处方
     * @param tradeNo
     * @return
     */
    OutpatientPayResult getOutpatientPayResult(Hospital hospital, String settleId, String tradeType, String tradeNo);

    void sendRefundResultToHis(ConfirmRefund confirmRefund);

    ApplyElectricInvoiceForHuLiResult applyElectricInvoiceForHuLi(Order order, OrderNursingExt ext);

    /**
     * 查询门诊患者文本病历
     */
    List<PatientMedicalRecord> patientMedicalRecords(Hospital hospital, String patId, String beginDate,
                                                     String endDate);

    /**
     * 检查检验结果互认情况同步给his
     * @param order
     * @return
     */
    SyncInspectMRResult syncInspectMR(OfflineOrder order, boolean agreed);


}
