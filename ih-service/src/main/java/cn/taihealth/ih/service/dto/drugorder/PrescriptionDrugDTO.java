package cn.taihealth.ih.service.dto.drugorder;

import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PrescriptionDrugDTO  extends AbstractEntityDTO {

    /**
     * 药品ID
     */
    @ApiModelProperty("药品ID")
    private Long drugId;

    @ApiModelProperty("渠道来源")
    private String channel;

    @ApiModelProperty("渠道中商品编码")
    private String itemCode;

    @ApiModelProperty("仓库ID")
    private String storageId;

    @ApiModelProperty("药品名")
    private String drugProductNameCn;

    @ApiModelProperty("药品通用名")
    private String drugCommonName;

    /**
     * 剂量规格
     */
    @ApiModelProperty("药品规格")
    private String drugSpecification;

    @ApiModelProperty("用法")
    private String usageMethod;

    /**
     * 单次剂量
     */
    @ApiModelProperty("每次用药数量")
    private String usagePerUseCount;

    /**
     * 单次剂量单位
     */
    @ApiModelProperty("每次用药单位")
    private String usagePerUseUnit;

    /**
     * 用药频次
     */
    @ApiModelProperty("频率数量")
    private String useFrequency;

    /**
     * 疗程
     */
    @ApiModelProperty("频率周期")
    private String usageFrequencyCount;

    @ApiModelProperty("数量")
    private int count;

    @ApiModelProperty("单价(单位:分)")
    private Long retailPrice;

    @ApiModelProperty("生产厂家")
    private String manufacturer;

    @ApiModelProperty("图片")
    private String picPath;
}
