package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.hospital.MedicalCaseTemplate;
import cn.taihealth.ih.domain.hospital.MedicalCaseTemplate.NodeType;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 病历模板树 视图模型
 * <AUTHOR>
 */
public class MedicalCaseTemplateVM extends UpdatableDTO {

    @ApiModelProperty("类型 文件夹：FOLDER 模板：TEMPLATE，必填")
    private NodeType type;

    @ApiModelProperty("排序序号，默认添加时不需要，向上或向下添加时传计算后的数值")
    private double orderNumber;

    @ApiModelProperty("模板或文件夹名称，必填")
    private String name;

    @ApiModelProperty("病历模板内容")
    private List<MedicalCaseTemplateVM> templates;

    public MedicalCaseTemplateVM(MedicalCaseTemplate template) {
        super(template);
        this.name = template.getName();
        this.type = template.getType();
        this.orderNumber = template.getOrderNumber();
        this.templates = template.getTemplates().stream().map(MedicalCaseTemplateVM::new).collect(Collectors.toList());
    }

    public NodeType getType() {
        return type;
    }

    public void setType(NodeType type) {
        this.type = type;
    }

    public double getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(double orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<MedicalCaseTemplateVM> getTemplates() {
        return templates;
    }

    public void setTemplates(List<MedicalCaseTemplateVM> templates) {
        this.templates = templates;
    }
}
