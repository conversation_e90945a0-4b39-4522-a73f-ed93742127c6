package cn.taihealth.ih.service.impl.wechat;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.config.ProxyConfig;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.security.TokenValidator;
import cn.taihealth.ih.commons.util.RedisUtil;
import cn.taihealth.ih.commons.util.Snowflake64.Holder;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.ManualPushList;
import cn.taihealth.ih.domain.ManualPushList.ManualPushStatus;
import cn.taihealth.ih.domain.SMSCode;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.cloud.Hospital.NoticeMode;
import cn.taihealth.ih.domain.cloud.WechatOrder.FeeType;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.enums.HospitalSettingKey.ClientType;
import cn.taihealth.ih.domain.his.HisInpatientHospitalCharge;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.his.HisOutpatientChargeGroup;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.ExamOrderOperation.Step;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform.PlatformForEnum;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.DrugOrderStatus;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.mq.entity.dto.AccompanyDTO;
import cn.taihealth.ih.mq.entity.dto.FollowUpDTO;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.cloud.RetryRefundRecordRepository;
import cn.taihealth.ih.repo.his.HisInpatientHospitalChargeRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeGroupRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeRepository;
import cn.taihealth.ih.repo.hospital.HospitalPublicPlatformRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.nursing.OrderNursingExtRepository;
import cn.taihealth.ih.repo.order.ExamOrderOperationRepository;
import cn.taihealth.ih.repo.order.ExamOrderRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.medicalrecordcooyappointment.MedicalRecordCopyAppointmentService;
import cn.taihealth.ih.service.api.nursing.NursingOrderService;
import cn.taihealth.ih.service.api.offline.InpatientService;
import cn.taihealth.ih.service.api.offline.OutpatientService;
import cn.taihealth.ih.service.api.wechat.WechatClient;
import cn.taihealth.ih.service.api.wechat.WechatMpServer;
import cn.taihealth.ih.service.api.wechat.WechatService;
import cn.taihealth.ih.service.cache.WechatCodeCache;
import cn.taihealth.ih.service.dto.drugorder.OrderInfoH5DTO;
import cn.taihealth.ih.service.dto.drugorder.OrderStatusChangeVM;
import cn.taihealth.ih.service.error.LockException;
import cn.taihealth.ih.service.impl.PayManager;
import cn.taihealth.ih.service.impl.event.bill.BillPaymentSuccessEvent;
import cn.taihealth.ih.service.impl.event.bill.BillRefundSuccessEvent;
import cn.taihealth.ih.service.impl.order.BusinessOrderManager;
import cn.taihealth.ih.service.util.*;
import cn.taihealth.ih.service.vm.OrderQueueStatusVM;
import cn.taihealth.ih.service.vm.WxPayUnifiedOrder;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.service.vm.offline.HisRefundParam;
import cn.taihealth.ih.supervise.dto.ln.ac.ChargeAndRefundReq;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.service.SuperviseFactory;
import cn.taihealth.ih.supervise.service.SuperviseService;
import cn.taihealth.ih.wechat.service.api.IHWxClient;
import cn.taihealth.ih.wechat.service.api.MedicalInsuranceService;
import cn.taihealth.ih.wechat.service.api.WechatServerService;
import cn.taihealth.ih.wechat.service.impl.wechat.QrCodeExpireException;
import cn.taihealth.ih.wechat.service.vm.*;
import cn.taihealth.ih.wechat.service.vm.miniProgram.TemplateMsg;
import cn.taihealth.ih.wechat.service.vm.miniProgram.WechatMiniMedicalInsuranceUserQueryParam;
import cn.taihealth.ih.wechat.service.vm.miniProgram.WechatMiniMedicalInsuranceUserQueryResult;
import cn.taihealth.ih.wechat.service.vm.wechat.*;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.binarywang.wxpay.bean.result.WxPayOrderCloseResult;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.common.web.UrlUtils;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage.MiniProgram;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class WechatServiceImpl implements WechatService {

    private static final Logger log = LoggerFactory.getLogger(WechatServiceImpl.class);

    // 用于小程序消息示例中正则匹配对应的key
    private static final String MINI_PROGRAM_MSG_MATCHER = "\\{\\{(.*?)\\}\\}";

    private final WechatMpServer wechatMpServer;

    private final TokenValidator tokenValidator;
    private final OrderService orderService;
    private final WechatInfoRepository wechatInfoRepository;
    private final WechatOrderRepository wechatOrderRepository;
    private final WechatOrderRefundRepository wechatOrderRefundRepository;
    private final WechatInsuranceOrderRefundRepository wechatInsuranceOrderRefundRepository;
    private final WechatCodeCache wechatCodeCache;
    private final HospitalSettingService hospitalSettingService;
    private final OrderQueueService orderQueueService;
    private final ExamOrderOperationRepository examOrderOperationRepository;
    private final ExamOrderRepository examOrderRepository;
    private final HospitalRepository hospitalRepository;
    private final UserCacheFindService userCacheFindService;
    private final WechatClient wechatClient;
    private final IHWxClient ihWxClient;
    private final UserPlatformInfoService userPlatformInfoService;
    private final HospitalPublicPlatformService hospitalPublicPlatformService;
    private final Map<String, WxPayService> wxPayServiceMap;
    private final Map<String, WxPayService> wxInsurancePayServiceMap;
    private final HospitalPublicPlatformRepository hospitalPublicPlatformRepository;
    private final UserPlatformInfoRepository userPlatformInfoRepository;
    private final BusinessOrderManager businessOrderManager;
    private final DrugStoreUserService drugStoreUserService;
    private final DrugStoreService drugStoreService;
    private final OrderRepository orderRepository;
    private final LockService lockService;
    private final RedisUtil redisUtil;
    private final Environment env;
    private final MedicalInsuranceService medicalInsuranceService;
    private final PatientRepository patientRepository;

    private final WechatServerService wechatServerService;
    private final WechatInsuranceOrderRepository wechatInsuranceOrderRepository;
    private final ManualPushListRepository manualPushListRepository;
    private final RetryRefundRecordRepository retryRefundRecordRepository;
    private final ApplicationProperties applicationProperties;
    private final ApplicationEventPublisher publisher;


    private final PayManager payManager;
    public WechatServiceImpl(
        TokenValidator smsTokenValidator,
        OrderService orderService,
        WechatInfoRepository wechatInfoRepository,
        WechatOrderRepository wechatOrderRepository,
        WechatCodeCache wechatCodeCache,
        HospitalSettingService hospitalSettingService,
        WechatOrderRefundRepository wechatOrderRefundRepository,
        OrderQueueService orderQueueService,
        ExamOrderOperationRepository examOrderOperationRepository,
        ExamOrderRepository examOrderRepository,
        UserCacheFindService userCacheFindService,
        HospitalRepository hospitalRepository,
        WechatClient wechatClient,
        IHWxClient ihWxClient,
        UserPlatformInfoService userPlatformInfoService,
        HospitalPublicPlatformService hospitalPublicPlatformService,
        Map<String, WxPayService> wxPayServiceMap,
        Map<String, WxPayService> wxInsurancePayServiceMap,
        BusinessOrderManager businessOrderManager,
        LockService lockService,
        HospitalPublicPlatformRepository hospitalPublicPlatformRepository,
        UserPlatformInfoRepository userPlatformInfoRepository,
        MedicalInsuranceService medicalInsuranceService,
        WechatServerService wechatServerService,
        WechatInsuranceOrderRepository wechatInsuranceOrderRepository,
        PatientRepository patientRepository,
        WechatInsuranceOrderRefundRepository wechatInsuranceOrderRefundRepository,
        DrugStoreUserService drugStoreUserService,
        DrugStoreService drugStoreService,
        OrderRepository orderRepository,
        RedisUtil redisUtil,
        Environment env,
        ManualPushListRepository manualPushListRepository,
        ApplicationProperties applicationProperties,
        RetryRefundRecordRepository retryRefundRecordRepository, ApplicationEventPublisher publisher, PayManager payManager) {
        tokenValidator = smsTokenValidator;
        this.orderService = orderService;
        this.wechatInfoRepository = wechatInfoRepository;
        this.wechatCodeCache = wechatCodeCache;
        this.wechatOrderRepository = wechatOrderRepository;
        this.wechatOrderRefundRepository = wechatOrderRefundRepository;
        this.hospitalSettingService = hospitalSettingService;
        this.orderQueueService = orderQueueService;
        this.examOrderOperationRepository = examOrderOperationRepository;
        this.examOrderRepository = examOrderRepository;
        this.hospitalRepository = hospitalRepository;
        this.userCacheFindService = userCacheFindService;
        this.wechatClient = wechatClient;
        this.ihWxClient = ihWxClient;
        this.userPlatformInfoService = userPlatformInfoService;
        this.hospitalPublicPlatformService = hospitalPublicPlatformService;
        this.drugStoreUserService = drugStoreUserService;
        this.hospitalPublicPlatformRepository = hospitalPublicPlatformRepository;
        this.userPlatformInfoRepository = userPlatformInfoRepository;
        this.drugStoreService = drugStoreService;
        this.orderRepository = orderRepository;
        this.redisUtil = redisUtil;
        this.env = env;
        this.retryRefundRecordRepository = retryRefundRecordRepository;
        this.publisher = publisher;
        wechatMpServer = AppContext.getInstance(WechatMpServer.class);
        this.lockService = lockService;
        this.businessOrderManager = businessOrderManager;
        this.medicalInsuranceService = medicalInsuranceService;
        this.wechatServerService = wechatServerService;
        this.wechatInsuranceOrderRepository = wechatInsuranceOrderRepository;
        this.patientRepository = patientRepository;
        this.wechatInsuranceOrderRefundRepository = wechatInsuranceOrderRefundRepository;
        this.manualPushListRepository = manualPushListRepository;
        this.payManager = payManager;
        this.applicationProperties = applicationProperties;
        this.wxPayServiceMap = wxPayServiceMap;
        this.wxInsurancePayServiceMap = wxInsurancePayServiceMap;
    }

    @Override
    public String getRedirectOauth2Url(String appId, String returnUrl) {
        try {
            String returnUrlEncode = StringUtils.isBlank(returnUrl) ? "" : URLEncoder.encode(returnUrl, "UTF-8");
            String url = SettingsHelper.getString(SettingKey.MP_API_SERVER_URL) + "?returnUrl=" + returnUrlEncode;
            return wechatClient.getOauth2buildAuthorizationUrl(appId, url);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public UserPlatformInfo checkAndInsertByCode(Hospital hospital, String appId, String code) {
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        IhWxOfficialAccount ihWxOfficialAccount = ihWxClient.getIhWxOfficialAccount(appId, host);
        String secret = ihWxOfficialAccount.getSecret();
        IhWxMpOAuth2AccessToken token = wechatClient.getIhWxMpOAuth2AccessToken(appId, secret, code);
        UserPlatformInfo userPlatformInfo = userPlatformInfoService.getUserPlatformInfo(hospital, token.getOpenId());
        if (userPlatformInfo != null) {
            return userPlatformInfo;
        }
        wechatCodeCache.putToken(code, token);
        throw ErrorType.WECHAT_USER_NOT_FOUND.toProblem();
    }

    @Override
    @Transactional
    public WechatInfo addOrUpdateWechatInfo(User user, IhWxUser wxMpUser, String appId, Hospital hospital) {
        Preconditions.checkNotNull(user);
        WechatInfo wechatInfo = wechatInfoRepository.findOneByOpenId(wxMpUser.getOpenId()).orElseGet(WechatInfo::new);
        wechatInfo.setCity(wxMpUser.getCity());
        wechatInfo.setCountry(wxMpUser.getCountry());
        wechatInfo.setHeadImageUrl(wxMpUser.getHeadImgUrl());
        wechatInfo.setNickname(wxMpUser.getNickname());
        wechatInfo.setProvince(wxMpUser.getProvince());
        wechatInfo.setGender(Integer.parseInt(wxMpUser.getSex()));
        wechatInfo.setUser(user);
        wechatInfo.setOpenId(wxMpUser.getOpenId());
        wechatInfo.setUnionId(wxMpUser.getUnionId());
        wechatInfoRepository.save(wechatInfo);

        // 通过openId查不到的直接加到表中，查到的不操作
        // 这里的逻辑是，公众号登录，一个微信号在同一个公众号下面，只能同时绑定一个用户，在业务上限制了，用户在公众号上退出登录时，会调用解绑的接口
        UserPlatformInfo userPlatformInfo = userPlatformInfoService.getUserPlatformInfo(hospital, wxMpUser.getOpenId());
        if (userPlatformInfo == null) {
            userPlatformInfo = new UserPlatformInfo();
            userPlatformInfo.setUser(user);
            userPlatformInfo.setAppId(appId);
            userPlatformInfo.setHospital(hospital);
            userPlatformInfo.setOpenId(wxMpUser.getOpenId());
        }
        userPlatformInfo.setUnionId(wxMpUser.getUnionId());
        userPlatformInfo.setPlatformType(PlatformTypeEnum.OFFICIAL_ACCOUNT);
        userPlatformInfoService.saveOrUpdate(userPlatformInfo);
        return wechatInfo;
    }

    @Override
    public IhWxJsapiSignature getJsapiConfig(String appId, String url, Hospital hospital) {
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        String accessToken = ihWxClient.getAccessToken(appId, host);
        return wechatClient.getJsapiConfig(appId, accessToken, url);
    }

    @Override
    public User bindWechatUser(String appId, WechatBindingVM vm, Hospital hospital) {
        String code = vm.getCode();
        String mobile = vm.getMobile();

        log.info("验证绑定公众号验证码, mobile=" + mobile + ", code=" + code);
        if (!tokenValidator.validate(mobile, vm.getSmsCode(), SMSCode.CodeType.WECHAT.name())) {
            throw ErrorType.INVALID_VALIDATION_TOKEN.toProblem();
        }

        User user = userCacheFindService.findOneByMobile(mobile)
            .orElseThrow(ErrorType.MOBILE_NOT_FOUND::toProblem);
        IhWxMpOAuth2AccessToken token = wechatCodeCache.getToken(code);
        if (token == null) {
            throw ErrorType.WECHAT_TOKEN_EXPIRED.toProblem();
        }
        UserPlatformInfo userPlatformInfo = userPlatformInfoService.getUserPlatformInfo(hospital, token.getOpenId());
        if (userPlatformInfo != null) {
            throw ErrorType.USER_ALREADY_BIND_WECHAT.toProblem();
        }
        IhWxUser mpUser = wechatClient.getIhWxUser(token.getAccessToken(), token.getOpenId());
        addOrUpdateWechatInfo(user, mpUser, appId, hospital);
        return user;
    }

    @Transactional
    @Override
    public void unbindWechatUser(User user, String appId, Hospital hospital) {
        userPlatformInfoRepository.deleteByUserAndHospitalAndAppId(user, hospital, appId);
    }

    @Override
    @Transactional(noRollbackFor = WechatException.class)
    public WxPayUnifiedOrder getUnifiedOrder(User user, String appId, CreateWechatOrderVM vm, String ip,
                                             Hospital hospital, Long userPlatform) {
        String openId;
        if (vm.getTradeType() == WechatOrder.TradeType.NATIVE) {
            openId = null;
            if (vm.getPlatform() != null) {
                log.info("微信native支付,使用端: " + vm.getPlatform());
                List<HospitalPublicPlatform> hpbs = hospitalPublicPlatformRepository.findAllByHospitalAndPlatformFor(hospital, vm.getPlatform());
                if (CollectionUtils.isEmpty(hpbs)) {
                    throw ErrorType.BAD_REQUEST_ERROR.toProblem("没有可用的微信小程序或公众号");
                }
                String finalAppId = appId;
                if (hpbs.stream().noneMatch(u -> Objects.equals(u.getAppId(), finalAppId))) {
                    appId = hpbs.get(0).getAppId();
                }
            }
        } else {
            UserPlatformInfo upi = userPlatformInfoService.getOneUserPlatformInfo(hospital, user, appId, userPlatform);
            openId = upi.getOpenId();
        }
        WechatOrderInVM wechatOrderInVM = new WechatOrderInVM();
        wechatOrderInVM.setType(vm.getType());
        wechatOrderInVM.setOrderId(vm.getId());
        if (vm.getTradeType() != null) {
            wechatOrderInVM.setTradeType(vm.getTradeType());
        } else {
            wechatOrderInVM.setTradeType(WechatOrder.TradeType.JSAPI);
        }

        List<WechatOrder> orders = wechatOrderRepository
            .findAllByWechatOrderTypeAndProductId(vm.getType(), vm.getId() + "");
        WechatOrder wechatOrder = orders.stream().filter(WechatOrder::getPayed).findFirst().orElse(null);
        if (wechatOrder != null) {
            switch (wechatOrderInVM.getType()) {
                case REGISTER:
//                    orderService.payOrder(user, vm.getId(), wechatOrder.getOutTradeNo(), wechatOrder.getTransactionId());
                    throw new WechatException("账单已支付");
                case RECIPE:
                    // 药品服务修改状态为已付款
//                    AppContext.getInstance(PrescriptionService.class).payPrescriptionOrder(hospital,
//                            Long.parseLong(wechatOrder.getProductId()), user, StandardObjectMapper.readValue(wechatOrder.getDetail(),
//                                    new TypeReference<>() {}), wechatOrder);
                    throw new WechatException("账单已支付");
                case OUTPATIENT_REGISTER_FEE:
//                    AppContext.getInstance(OfflineOrderService.class).payOutpatientRegisterCharge(user,
//                            Long.parseLong(wechatOrder.getProductId()), wechatOrder);
                    throw new WechatException("账单已支付");
                case BEFORE_INPATIENT_FEE:
//                    AppContext.getInstance(InpatientService.class).inpatientCharge(user,
//                            Long.parseLong(wechatOrder.getProductId()), wechatOrder);
                    throw new WechatException("账单已支付");
                case OUTPATIENT_FEE:
//                    AppContext.getInstance(OutpatientService.class).payOutpatientCharge(user,
//                            Long.parseLong(wechatOrder.getProductId()), wechatOrder);
                    throw new WechatException("账单已支付");
                default:
                    throw ErrorType.BAD_REQUEST_ERROR.toProblem("账单已支付");
            }
        }

        switch (vm.getType()) {
            case NURSING_CONSULT:
                // 护理咨询
                Order nursingCoOrder = orderRepository.getById(vm.getId());
                wechatOrderInVM.setTotalFee(nursingCoOrder.getRegistrationFee());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("护理咨询费用");
                wechatOrderInVM.setDetail("护理咨询费用");
                wechatOrderInVM.setProductId(vm.getId() + "");
                log.info("------------------------患者创建护理到家订单--------------------------");
            case NURSING_HOME: // 护理到家
                Order nursingOrder = orderRepository.getById(vm.getId());
                wechatOrderInVM.setTotalFee(nursingOrder.getRegistrationFee());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("护理到家预交金");
                wechatOrderInVM.setDetail("护理到家预交金");
                wechatOrderInVM.setProductId(vm.getId() + "");
                log.info("------------------------患者创建护理到家订单--------------------------");
                break;
            case NURSING_HOME_APPEND:
                // 护理到家补缴
                Order no = orderRepository.getById(vm.getId());
                OrderNursingExt ext = AppContext.getInstance(OrderNursingExtRepository.class).findOneByBaseOrder(no).orElse(null);
                wechatOrderInVM.setTotalFee(ext.getSupplementaryPaymentAmount());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("护理到家预交金补缴");
                wechatOrderInVM.setDetail("护理到家预交金补缴");
                wechatOrderInVM.setProductId(vm.getId() + "");
                break;
            case REGISTER:
                Order order = AppContext.getInstance(OrderRepository.class).getById(vm.getId());
                if (order.getPaymentMethod() != PaymentMethod.WECHAT) {
                    throw ErrorType.ILLEGAL_STATE.toProblem(ThirdOrderType.REGISTER.name() + "订单未使用微信支付");
                }
                int totalFee = HospitalSettingsHelper.getBoolean(order.getHospital(), HospitalSettingKey.PAY_ONE_FEN)
                    ? 1 : order.getRegistrationFee();
                wechatOrderInVM.setTotalFee(totalFee);
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody(ThirdOrderType.REGISTER.getName() + "费");
                wechatOrderInVM.setDetail(ThirdOrderType.REGISTER.getName() + "费");
                wechatOrderInVM.setProductId(order.getId() + "");
                if (vm.getType() == ThirdOrderType.REGISTER) {
                    // 订单待支付 设置支付时限的一半时间 anInt * 30L
                    int anInt = HospitalSettingsHelper.getInt(order.getHospital(), HospitalSettingKey.PAY_ORDER_LIMIT_TIME);
                    log.info("------------------------患者已经下单,但是没有付款,等待支付的状态--------------------------");
                    redisUtil.set("order_waitingPay:" + order.getId(), 1, anInt * 30L);
                    // 订单超时未支付
                    redisUtil.set("order_close:" + order.getId(), 1, anInt * 60L);
                }
                break;
            case RECIPE:
                List<OrderInfoH5DTO> recipeInfos = drugStoreUserService.getOrderByPrescriptionIds(
                    vm.getPrescriptionOrderIds());
                int totalRecipeFee = 0;
                String detail = StandardObjectMapper.stringify(vm.getPrescriptionOrderIds());
                for (OrderInfoH5DTO recipe : recipeInfos) {
                    totalRecipeFee += (recipe.getPayPrice() + recipe.getFreightPrice());
                }
                wechatOrderInVM.setTotalFee(totalRecipeFee);
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("问诊处方费用");
                wechatOrderInVM.setDetail(detail);
                wechatOrderInVM.setProductId(vm.getId() + "");
                break;
            case OUTPATIENT_FEE:
                HisOutpatientChargeGroup group = AppContext.getInstance(HisOutpatientChargeGroupRepository.class)
                    .getById(vm.getId());
                wechatOrderInVM.setTotalFee(group.getSelfAmount());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("门诊缴费");
                wechatOrderInVM.setDetail("门诊缴费");
                wechatOrderInVM.setProductId(vm.getId() + "");
                log.info("------------------------患者创建门诊缴费账单--------------------------");
                AppContext.getInstance(OutpatientService.class).changeAggregatePayment(group, AggregatePayment.OTHER);
                break;
            case BEFORE_INPATIENT_FEE:
                HisInpatientHospitalCharge charge = AppContext.getInstance(HisInpatientHospitalChargeRepository.class)
                    .getById(vm.getId());
                wechatOrderInVM.setTotalFee(Integer.parseInt(charge.getSelf_amount()));
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("住院预交金缴费");
                wechatOrderInVM.setDetail("住院预交金缴费");
                wechatOrderInVM.setProductId(vm.getId() + "");
                log.info("------------------------患者创建预交金缴费账单--------------------------");
                break;
            case OUTPATIENT_REGISTER_FEE:
                OfflineOrder offlineOrder = AppContext.getInstance(OfflineOrderRepository.class).getById(vm.getId());
                if (offlineOrder.getSelfFlag() == 0) {
                    throw ErrorType.VALIDATION_FAILED.toProblem("医保挂号不能使用自费支付");
                }
                wechatOrderInVM.setTotalFee(offlineOrder.getRegistrationFee());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("门诊挂号费");
                wechatOrderInVM.setDetail("门诊挂号费");
                wechatOrderInVM.setProductId(vm.getId() + "");
                log.info("------------------------患者创建门诊挂号费账单--------------------------");
                AppContext.getInstance(OfflineOrderService.class).orderPending(vm.getId());
                AppContext.getInstance(OfflineOrderService.class).changeAggregatePayment(offlineOrder, AggregatePayment.OTHER);
                break;
            case OUTPATIENT_NUCLEIC_ACID_FEE:
                OfflineOrder nucleicAcidOrder = AppContext.getInstance(OfflineOrderRepository.class)
                    .getById(vm.getId());
                wechatOrderInVM.setTotalFee(nucleicAcidOrder.getRegistrationFee());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("核酸检测费");
                wechatOrderInVM.setDetail("核酸检测费");
                wechatOrderInVM.setProductId(vm.getId() + "");
                log.info("------------------------患者创建核算预约订单--------------------------");
                AppContext.getInstance(OfflineOrderService.class).nucleicAcidOrderPending(vm.getId());
                break;
            case SELF_BILLING:
                OfflineOrder selfBillingOrder = AppContext.getInstance(OfflineOrderRepository.class)
                    .getById(vm.getId());
                wechatOrderInVM.setTotalFee(selfBillingOrder.getRegistrationFee());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("自助开单费");
                wechatOrderInVM.setDetail("自助开单费");
                wechatOrderInVM.setProductId(vm.getId() + "");
                log.info("------------------------患者创建自助开单--------------------------");
                break;
            case MEDICAL_RECORD_COPY_APPOINTMENT:
                OfflineOrder copyAppointmentOrder = AppContext.getInstance(OfflineOrderRepository.class)
                    .getById(vm.getId());
                wechatOrderInVM.setTotalFee(copyAppointmentOrder.getRegistrationFee());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("病案复印预约预交金");
                wechatOrderInVM.setDetail("病案复印预约预交金");
                wechatOrderInVM.setProductId(vm.getId() + "");
                AppContext.getInstance(MedicalRecordCopyAppointmentService.class).copyAppointmentPending(vm.getId());
                log.info("------------------------患者创建病案复印预约订单--------------------------");
                break;
            case MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:
                OfflineOrder copyAppointmentAppendOrder = AppContext.getInstance(OfflineOrderRepository.class)
                    .getById(vm.getId());
                wechatOrderInVM.setTotalFee(copyAppointmentAppendOrder.getRegistrationFee());
                wechatOrderInVM.setDeviceInfo("WEB");
                wechatOrderInVM.setBody("病案复印预约补缴金");
                wechatOrderInVM.setDetail("病案复印预约补缴金");
                wechatOrderInVM.setProductId(vm.getId() + "");
                AppContext.getInstance(MedicalRecordCopyAppointmentService.class)
                    .appendCopyAppointmentPending(vm.getId());
                log.info("------------------------患者补缴病案复印预约--------------------------");
                break;
            default:
                throw ErrorType.VALIDATION_FAILED.toProblem();
        }
        orders.sort(Comparator.comparing(WechatOrder::getExpireTime).reversed());
        String finalAppId1 = appId;
        return orders
            .stream()
            .filter(u -> u.getExpireTime().after(new Date()))
            .findFirst()
            .map(WxPayUnifiedOrder::new)
            .orElseGet(() ->
                           getUnifiedOrder(user, finalAppId1, wechatOrderInVM, ip, hospital, openId)
            );
    }

    private String getPayNotifyUrl(String appId) {
        return UrlUtils.concatSegments(SettingsHelper.getString(SettingKey.API_SERVER_URL),
                                       "/wechat/apps/" + appId +
                                           "/order/pay");
    }

    private String getInsurancePayNotifyUrl(String appId) {
        return UrlUtils.concatSegments(SettingsHelper.getString(SettingKey.API_SERVER_URL),
                                       "/wechat/insurance/apps/" + appId +
                                           "/order/pay");
    }

    private WxPayUnifiedOrder getUnifiedOrder(User user, String appId, WechatOrderInVM orderVM, String ip,
                                              Hospital hospital, String openId) {
        // 判断appId是否有效
        hospitalPublicPlatformService.getPlatform(hospital, appId);

        String notifyUrl = getPayNotifyUrl(appId);
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime expireTime = startTime.plusMinutes(5);
        IhWxPayOrderRequest ihWxRequest = new IhWxPayOrderRequest();
        ihWxRequest.setBody(orderVM.getBody());
        ihWxRequest.setDetail(orderVM.getDetail());
        ihWxRequest.setOutTradeNo(Holder.INSTANCE.nextId() + "");
        ihWxRequest.setDeviceInfo(orderVM.getDeviceInfo());
        ihWxRequest.setTimeStart(startTime);
        ihWxRequest.setTimeExpire(expireTime);
        // 注释，放开信用卡使用限制
//        ihWxRequest.setLimitPay("no_credit");
        if (StringUtils.isNotBlank(openId)) {
            ihWxRequest.setOpenId(openId);
        }
        ihWxRequest.setFeeType(FeeType.CNY.name());
        ihWxRequest.setTotalFee(orderVM.getTotalFee());
        ihWxRequest.setIp(ip);
        ihWxRequest.setNotifyUrl(notifyUrl);
        ihWxRequest.setTradeType(orderVM.getTradeType().name());
        ihWxRequest.setProductId(orderVM.getProductId());
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        // 临时添加wxPayService
        setWxPayService(appId, notifyUrl, host);
        IhWxPayOrder wxPayOrder = wechatMpServer.getUnifiedOrder(appId, ihWxRequest, host);
        // wechatInfo 不用了 这里的微信订单中nickName改为user fullName
        WechatOrder order = saveOrder(wxPayOrder,
                                      Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()),
                                      Date.from(expireTime.atZone(ZoneId.systemDefault()).toInstant()),
                                      user.getFullName(),
                                      orderVM.getType(),
                                      hospital.getId(), appId, user.getId());
        return new WxPayUnifiedOrder(order);
    }

    private WechatOrder saveOrder(IhWxPayOrder payOrder, Date startTime, Date expireTime, String nickName,
                                  ThirdOrderType type, Long hospitalId, String appId, long createdUserId) {
        WechatOrder order = new WechatOrder();
        order.setTimeStamp(payOrder.getTimeStamp());
        order.setWechatOrderType(type);
        order.setBody(payOrder.getBody());
        order.setDetail(payOrder.getDetail());
        order.setOpenId(payOrder.getOpenId());
        order.setNickName(nickName);
        order.setDeviceInfo(payOrder.getDeviceInfo());
        order.setTotalFee(payOrder.getTotalFee());
        order.setFeeType(WechatOrder.FeeType.valueOf(payOrder.getFeeType()));
        order.setTradeType(WechatOrder.TradeType.valueOf(payOrder.getTradeType()));
        order.setProductId(payOrder.getProductId());
        order.setNonceStr(payOrder.getNonceStr());
        order.setSign(payOrder.getSign());
        order.setSignType(payOrder.getSignType());
        order.setMchId(payOrder.getMchId());
        order.setPackageValue(payOrder.getPackageValue());
        order.setOutTradeNo(payOrder.getOutTradeNo());
        order.setStartTime(startTime);
        order.setExpireTime(expireTime);
        order.setLimitPay(payOrder.getLimitPay());
        order.setSpbillCreateIp(payOrder.getSpbillCreateIp());
        order.setNotifyUrl(payOrder.getNotifyUrl());
        order.setPayed(false);
        order.setPaySign(payOrder.getPaySign());
        order.setHospitalId(hospitalId);
        order.setAppId(appId);
        order.setCodeUrl(payOrder.getCodeUrl());
        order.setCreatedUserId(createdUserId);

        wechatOrderRepository.save(order);
        return order;
    }

    private WechatPayCheckResult checkPayResult(String appId, String xml, Hospital hospital) {
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        // 临时添加wxPayService
        setWxPayService(appId, getPayNotifyUrl(appId), host);
        return wechatMpServer.checkPayResult(appId, xml, host);
    }

    @Override
    public IhWxPayOrderQueryResult getPayResult(String appId,
                                                String transactionId, String outTradeNo, Hospital hospital) {
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        // 临时添加wxPayService
        setWxPayService(appId, getPayNotifyUrl(appId), host);
        return wechatMpServer.checkPaymentStatus(appId, transactionId, outTradeNo, host);
    }

    @Override
    public String refundOrderXml(String appId, String xml, String step) {
        Optional<HospitalPublicPlatform> hospitalPublicPlatformOpt = hospitalPublicPlatformService.getByAppId(appId);
        HospitalPublicPlatform hospitalPublicPlatform = hospitalPublicPlatformOpt.orElseThrow(() -> {
            throw ErrorType.NOT_FOUND_ERROR.toProblem("appId对应公众号或小程序未绑定医院");
        });
        WechatRefundCheckResult checkResult = checkRefundNotifyResult(appId, xml, hospitalPublicPlatform.getHospital());
        if (!checkResult.isSuccess()) {
            return checkResult.getResultXml();
        }

        return lockService.executeWithLock("lock.wechat.refund.outTradeNo." + checkResult.getOutTradeNo(),
                                           () -> refundOrder(checkResult));
    }

    @Override
    @Transactional
    public String refundOrder(WechatRefundCheckResult checkResult) {

//        String outTradeNo = checkResult.getOutTradeNo();
        String outRefundNo = checkResult.getOutRefundNo();
        WechatOrderRefund refund = wechatOrderRefundRepository.findOneByOutRefundNo(outRefundNo).orElse(null);
        if (refund == null) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("微信退款账单不存在");
        }
        if ("SUCCESS".equalsIgnoreCase(refund.getStatus())) {
            return checkResult.getResultXml();
        }
        refund.setChannel(checkResult.getChannel());
        refund.setFundsAccount(checkResult.getFundsAccount());
        refund.setStatus(checkResult.getStatus());
        refund.setSuccessTime(checkResult.getSuccessTime());
        refund.setUserReceivedAccount(checkResult.getUserReceivedAccount());
        refund.setRefundId(checkResult.getRefundId());

        // 2024年04月26日11:37:35 查出退款重试记录，将状态改为SUCCESS
        Specification<RetryRefundRecord> specification = Specifications.and(
                Specifications.eq("transactionId", refund.getTransactionId()),
                Specifications.eq("count", refund.getRefundCount()),
                Specifications.eq("status", "PROCESSING")
            );
        RetryRefundRecord retryRefundRecord = retryRefundRecordRepository.findOne(specification).orElse(null);
        if (retryRefundRecord != null) {
            retryRefundRecord.setStatus("SUCCESS");
            retryRefundRecord.setSuccessTime(checkResult.getSuccessTime());
            retryRefundRecordRepository.save(retryRefundRecord);
        }

        wechatOrderRefundRepository.save(refund);
        WechatOrder order = wechatOrderRepository.findOneByOutTradeNo(refund.getOutTradeNo()).get();
        UserService userService = AppContext.getInstance(UserService.class);
//        User user = wechatInfoRepository.findOneByOpenId(order.getOpenId())
//                .map(WechatInfo::getUser).orElseGet(userService::getAdmin);
        User user = userService.getAdmin();
        boolean report = true;
        String patid = "";
        switch (order.getWechatOrderType()) {
            case REGISTER:
                Order registerOrder = orderService.refundedOrder(user, Long.parseLong(order.getProductId()), null,
                        new HisRefundParam(refund));
                if (!Objects.isNull(registerOrder.getElectronicMedicCard())){
                    patid = registerOrder.getElectronicMedicCard().getHisPatid();
                }
                break;
            case RECIPE:
                // 药品服务修改状态为已退款
                String detail = order.getDetail();
                Order orderInfo = orderRepository.getById(Long.parseLong(order.getProductId()));
                if (!Objects.isNull(orderInfo.getElectronicMedicCard())){
                    patid = orderInfo.getElectronicMedicCard().getHisPatid();
                }
                List<Long> ids = StandardObjectMapper.readValue(detail, new TypeReference<>() {
                });
                // 处方单修改状态为
                PrescriptionOrderRepository prescriptionOrderRepository = AppContext.getInstance(
                    PrescriptionOrderRepository.class);
                List<PrescriptionOrder> prescriptionOrders = prescriptionOrderRepository.findAllById(ids);
                if (prescriptionOrders.stream().allMatch(u -> DrugOrderStatus.CANCELED == u.getDrugOrderStatus())) {
                    report = false;
                    break;
                }
                OrderStatusChangeVM orderStatusChangeVM = new OrderStatusChangeVM();
                orderStatusChangeVM.setOutPrescriptionIds(ids);
                orderStatusChangeVM.setOperatorMobile(user.getMobile());
                orderStatusChangeVM.setHospitalCode(orderInfo.getHospital().getCode());
                drugStoreService.refundSuccess(orderStatusChangeVM);
                for (PrescriptionOrder prescriptionOrder : prescriptionOrders) {
                    prescriptionOrder.setDrugOrderStatus(DrugOrderStatus.CANCELED);
                    prescriptionOrderRepository.save(prescriptionOrder);
                }
                break;
            case OUTPATIENT_REGISTER_FEE:
                OfflineOrder outpatientRegisterOrder = AppContext.getInstance(OfflineOrderService.class).refundedAppointmentRegisterCharge(
                        Long.parseLong(order.getProductId()), new HisRefundParam(refund));
                if (!Objects.isNull(outpatientRegisterOrder.getElectronicMedicCard())) {
                    patid = outpatientRegisterOrder.getElectronicMedicCard().getHisPatid();
                }
                break;
            case MEDICAL_RECORD_COPY_APPOINTMENT:
                OfflineOrder mrcaOrder = AppContext.getInstance(MedicalRecordCopyAppointmentService.class).refundedAppointment(
                    Long.parseLong(order.getProductId()), refund);
                if (!Objects.isNull(mrcaOrder.getElectronicMedicCard())) {
                    patid = mrcaOrder.getElectronicMedicCard().getHisPatid();
                }
                break;
            case BEFORE_INPATIENT_FEE:
                HisInpatientHospitalCharge inpatientHospitalCharge = AppContext.getInstance(InpatientService.class).inpatientChargeRefunded(
                        Long.parseLong(order.getProductId()), refund);
                Patient patient = new Patient();
                patient.setId(Long.parseLong(inpatientHospitalCharge.getPatient_id()));
                List<ElectronicMedicCard> electronicMedicCardList = AppContext.getInstance(ElectronicMedicCardRepository.class)
                        .findAllByOnlineTypeAndPatient(ElectronicMedicCard.OnlineType.HIS, patient);
                if (CollectionUtils.isNotEmpty(electronicMedicCardList)) {
                    List<ElectronicMedicCard> enabledCardList = electronicMedicCardList.stream().filter(ElectronicMedicCard::isEnabled).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(enabledCardList)) {
                        patid = enabledCardList.get(0).getHisPatid();
                    }
                }
                break;
            case OUTPATIENT_FEE:
                HisOutpatientChargeGroup group = AppContext.getInstance(OutpatientService.class).outpatientChargeRefunded(
                        Long.parseLong(order.getProductId()), new HisRefundParam(refund));
                List<HisOutpatientCharge> charges = AppContext.getInstance(HisOutpatientChargeRepository.class).findAllByHisOutpatientChargeGroupId(group.getId());
                if (CollectionUtils.isNotEmpty(charges) && !Objects.isNull(charges.get(0).getElectronicMedicCardId())) {
                    ElectronicMedicCard electronicMedicCard = AppContext.getInstance(ElectronicMedicCardRepository.class).getById(charges.get(0).getElectronicMedicCardId());
                    patid = electronicMedicCard == null ? "" : electronicMedicCard.getHisPatid();
                }
                break;
            case NURSING_HOME:
                AppContext.getInstance(NursingOrderService.class).refundedHome(Long.parseLong(order.getProductId()),
                        new HisRefundParam(refund));
                break;
            case NURSING_CONSULT:
                AppContext.getInstance(NursingOrderService.class).refundedConsult(user, Long.parseLong(order.getProductId()),
                        new HisRefundParam(refund));
                break;
            default:
        }

        // 退款成功
        payManager.refundSuccess(
                                 patid,
                                 refund.getRefundDetailNo(),
                                 refund.getRefundId(),
                                 refund.getOutRefundNo(),
                                 refund.getAmount(),
                                 refund.getSuccessTime(),
                                 order.getHospitalId(),
                                 order.getWechatOrderType());

        if (report && order.getWechatOrderType() == ThirdOrderType.REGISTER
            || order.getWechatOrderType() == ThirdOrderType.RECIPE) {
            // 只有线上的处方和挂号需要上报
            // 监管上报逻辑移动到这里
            Order orderInfo = orderRepository.getById(Long.parseLong(order.getProductId()));
            SuperviseDto dto = SuperviseUtil.getDto(orderInfo.getHospital());
            SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
            if (superviseService != null) {
                if (order.getWechatOrderType() == ThirdOrderType.REGISTER) {
                    superviseService.cancelRegistration(orderInfo.getId(), dto);
                }
//            if (orderInfo.getRegistrationFee() > 0 && StringUtils.isBlank(refund.getOutRefundNo())) {
                // 退费时accountNo为微信的商户退款单号
                ChargeAndRefundReq req = new ChargeAndRefundReq(orderInfo.getId(), refund.getRefundId(), "2",
                                                                order.getWechatOrderType(), order.getId(),
                                                                refund.getTransactionId());
                superviseService.chargeAndRefund(req, dto);
//            }
            }
        }
        log.info("退款成功监听事件发布 order outTradeNo {}", order.getOutTradeNo());
        // 退款成功实时上传订单
        publisher.publishEvent(new BillRefundSuccessEvent(order.getHospitalId(),
                                                                                     Lists.newArrayList(refund), null,
                                                                                     null));
        return checkResult.getResultXml();
    }

    @Override
    public String payOrder(WechatPayCheckResult checkResult) {
        String outTradeNo = checkResult.getOutTradeNo();
        return lockService.executeWithLockThrowError("lock.wechat.pay.outTradeNo." + checkResult.getOutTradeNo(),
                () -> {
                    WechatOrder order = wechatOrderRepository.findOneByOutTradeNo(outTradeNo).get();
                    if (!order.getPayed()) {
                        order.setTimeEnd(checkResult.getTimeEnd());
                        order.setTransactionId(checkResult.getTransactionId());
                        order.setPayed(true);
                        order.setPayTime(TimeUtils.convert(checkResult.getTimeEnd()));
                        if (StringUtils.isBlank(order.getOpenId())) {
                            order.setOpenId(checkResult.getOpenId());
                        }
                        wechatOrderRepository.save(order);
                    }
                    if (order.getPayed() && businessOrderManager.checkOrderPaid(order.getWechatOrderType(), order.getProductId())) {
                        return checkResult.getResultXml();
                    }
                    String appId = order.getAppId();
                    PlatformTypeEnum platformType = hospitalPublicPlatformService.getByAppId(appId)
                            .map(HospitalPublicPlatform::getPlatformType).orElse(null);
                    HisPayParam hisPayParam = new HisPayParam(order, platformType);
                    UserService userService = AppContext.getInstance(UserService.class);
                    User user = userService.getAdmin();
                    Order orderInfo = null;
                    switch (order.getWechatOrderType()) {
                        case NURSING_CONSULT:
                        case NURSING_HOME:
                            // 护理咨询和护理到家一个逻辑
                            orderInfo = orderRepository.findById(Long.parseLong(order.getProductId())).orElse(null);
                            AppContext.getInstance(NursingOrderService.class).payOrder(user, orderInfo, hisPayParam);
                            break;
                        case NURSING_HOME_APPEND:
                            orderInfo = orderRepository.findById(Long.parseLong(order.getProductId())).orElse(null);
                            AppContext.getInstance(NursingOrderService.class).payAppendOrder(user, orderInfo, hisPayParam);
                            break;
                        case REGISTER:
                            orderInfo = orderRepository.findById(Long.parseLong(order.getProductId())).orElse(null);
                            if (orderInfo == null) {
                                log.error("支付订单不存在, outTradeNo: " + checkResult.getResultXml());
                            } else {
                                orderService.payOrder(user, Long.parseLong(order.getProductId()), order.getPayTime(), hisPayParam);
                            }
                            break;
                        case RECIPE:
                            orderInfo = orderRepository.findById(Long.parseLong(order.getProductId())).orElse(null);
                            if (orderInfo == null) {
                                log.error("支付订单不存在, outTradeNo: " + checkResult.getResultXml());
                            } else {
                                // 药品服务修改状态为已付款
                                AppContext.getInstance(PrescriptionService.class).payPrescriptionOrder(orderInfo.getHospital(),
                                        orderInfo.getId(), user, StandardObjectMapper.readValue(order.getDetail(), new TypeReference<>() {}),
                                        order, hisPayParam);
                            }
                            break;
                        case BEFORE_INPATIENT_FEE:
                            AppContext.getInstance(InpatientService.class).inpatientCharge(user, Long.parseLong(order.getProductId()),
                                    order, hisPayParam);
                            break;
                        case OUTPATIENT_FEE:
                            AppContext.getInstance(OutpatientService.class)
                                    .payOutpatientCharge(Long.parseLong(order.getProductId()), hisPayParam);
                            break;
                        case OUTPATIENT_REGISTER_FEE:
                            AppContext.getInstance(OfflineOrderService.class).payOutpatientRegisterCharge(Long.parseLong(order.getProductId()),
                                    hisPayParam);
                            break;
                        case OUTPATIENT_NUCLEIC_ACID_FEE:
                            AppContext.getInstance(OfflineOrderService.class).payNucleicAcidCharge(user, Long.parseLong(order.getProductId()), order);
                            break;
                        case SELF_BILLING:
                            AppContext.getInstance(PatientService.class).paySelfBilling(Long.parseLong(order.getProductId()), order);
                            break;
                        case MEDICAL_RECORD_COPY_APPOINTMENT:
                            AppContext.getInstance(MedicalRecordCopyAppointmentService.class)
                                    .payCopyAppointment(user, Long.parseLong(order.getProductId()), order);
                            break;
                        case MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:
                            AppContext.getInstance(MedicalRecordCopyAppointmentService.class)
                                    .payAppendCopyAppointment(user, Long.parseLong(order.getProductId()), order);
                            break;
                        default:
                    }
                    if (orderInfo != null && (order.getWechatOrderType() == ThirdOrderType.REGISTER || order.getWechatOrderType() == ThirdOrderType.RECIPE)) {
                        SuperviseDto dto = SuperviseUtil.getDto(orderInfo.getHospital());
                        SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
                        if (superviseService != null) {
                            // 支付类型为挂号时，才会上报
                            if (order.getWechatOrderType() == ThirdOrderType.REGISTER) {
                                superviseService.register(orderInfo.getHospital(), orderInfo.getId(), dto);
                            }
                            if (orderInfo.getRegistrationFee() > 0 && !StringUtils.isBlank(order.getOutTradeNo())) {
                                ChargeAndRefundReq req = new ChargeAndRefundReq(orderInfo.getId(), order.getOutTradeNo(),
                                        "1", order.getWechatOrderType(), order.getId(), order.getTransactionId());
                                superviseService.chargeAndRefund(req, dto);
                            }
                        }
                    }
                    // 2025年03月03日 新增实时上传互联网医院支付订单的功能
                    log.info("支付成功监听事件发布 order outTradeNo {}", order.getOutTradeNo());
                    publisher.publishEvent(new BillPaymentSuccessEvent(
                        order.getHospitalId(), Lists.newArrayList(order), null, null));
                    return checkResult.getResultXml();
                });
    }

    @Override
    public String payOrderXml(String appId, String xml) {
        Optional<HospitalPublicPlatform> hospitalPublicPlatformOpt = hospitalPublicPlatformService.getByAppId(appId);
        HospitalPublicPlatform hospitalPublicPlatform = hospitalPublicPlatformOpt.orElseThrow(() -> {
            log.error("appId对应公众号或小程序未绑定医院, appId: " + appId);
            throw ErrorType.NOT_FOUND_ERROR.toProblem("appId对应公众号或小程序未绑定医院");
        });
        WechatPayCheckResult checkResult = checkPayResult(appId, xml, hospitalPublicPlatform.getHospital());
        if (!checkResult.isSuccess()) {
            return checkResult.getResultXml();
        }
        try {
            return payOrder(checkResult);
        } catch (LockException e) {
            log.info("账单结算中，跳过本次结算", e);
            return checkResult.getResultXml();
        }
    }

    @Override
    public String payInsuranceOrderXml(String appId, String xml) {
        // 这里不能出现一个小程序或公众号绑定多个医院
        // 解码时需要微信医保Key，每个医院都不一样，在没有传医院的时候，如果有多个医院，无法确定key
        HospitalPublicPlatform upi = hospitalPublicPlatformRepository.findOneByAppId(appId)
                .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("公众号或小程序未绑定医院, appId=" + appId));
        Hospital hospital = upi.getHospital();
        String key = null;
        switch (upi.getPlatformType()) {
            case MINI:
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_KEY);
                break;
            case OFFICIAL_ACCOUNT:
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_KEY);
                break;
        }
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        setWxInsurancePayService(appId, key, host);
        WechatInsurancePayCheckResult checkResult = wechatServerService.checkInsurancePayResult(appId, xml, host);

        if (!checkResult.isSuccess()) {
            return checkResult.getResultXml();
        }
        try {
            return payInsuranceOrder(checkResult);
        } catch (LockException e) {
            log.info("账单结算中，跳过本次结算", e);
            return checkResult.getResultXml();
        }
    }

    @Override
    @Async
    public void sendTemplateMessage(Hospital hospital, User user, WechatTemplatedData msg) {
        JpaUtils.executeInTransaction(() -> {
            List<String> openIds = getOpenIds(hospital, user, msg.getAppId());
            if (CollectionUtils.isEmpty(openIds)) {
                return 0;
            }
            msg.setOpenIds(openIds);
            sendTemplateMessage(Lists.newArrayList(msg), hospital);
            return 0;
        });
    }

    @Override
    @Async
    public void sendTemplateMessage(User user, Object data, HospitalSettingKey hospitalSettingKey) {
        JpaUtils.executeInTransaction(() -> {
            Hospital hospital = getHospital(data);
            WechatTemplatedData msg = getWxMpTemplateMsg(hospitalSettingKey, data);
            if (msg == null) {
                log.info("没有获取到消息内容, key={}, dataClass={}", hospitalSettingKey, data.getClass());
                return 0;
            }
            List<String> openIds = getOpenIds(hospital, user, msg.getAppId());
            if (CollectionUtils.isEmpty(openIds)) {
                return 0;
            }
            msg.setOpenIds(openIds);
            sendTemplateMessage(Lists.newArrayList(msg), hospital);

            return 0;
        });
    }

    @Override
    public void sendPrescriptionReadyMessage(User user, PrescriptionOrder prescriptionOrder,
                                             Order order) {
        if (order.getHospital().getNoticeMode() != NoticeMode.MINI_PROGRAM) {
            return;
        }
        WechatTemplatedData msg = setMsgForPrescriptionReady(prescriptionOrder, order);
        msg.setOpenIds(getOpenIds(order.getHospital(), user, msg.getAppId()));
        sendTemplateMessage(Lists.newArrayList(msg), order.getHospital());
    }

    @Override
    public void sendAccompany(User user, Hospital hospital, AccompanyDTO accompany, HospitalSettingKey key) {
        WechatTemplatedData msg = setMsgForAccompany(hospital, accompany, key);
        msg.setOpenIds(getOpenIds(hospital, user, msg.getAppId()));
        sendTemplateMessage(Lists.newArrayList(msg), hospital);
    }

    private WechatTemplatedData setMsgForAccompany(Hospital hospital, AccompanyDTO accompany, HospitalSettingKey key) {
        return JpaUtils.executeInTransaction(() -> {
            WechatTemplatedData data = new WechatTemplatedData();

            String appId;
            if (ClientType.DOCTOR.equals(key.getClientTypes()[0])) {
                appId = getAppId(hospital, PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.DOCTOR);
            } else {
                appId = getAppId(hospital, PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
            }
            manageData(data, key, appId, hospital);
            data.setFirstStr("");
            data.addKeywordsStr(accompany.getDate() + accompany.getRemark());
            data.addKeywordsStr(accompany.getUsername());
            data.setRemarkStr("");
            // 小程序跳转
            data.setMiniProgram(new MiniProgram(accompany.getAppid(), accompany.getPagePath(), false));
            return data;
        });
    }

    @Override
    public void sendForFollowUp(User user, Hospital hospital, FollowUpDTO followUp, HospitalSettingKey key) {
        WechatTemplatedData msg = setMsgForFollowUp(hospital, followUp, key);
        msg.setOpenIds(getOpenIds(hospital, user, msg.getAppId()));
        sendTemplateMessage(Lists.newArrayList(msg), hospital);
    }

    private WechatTemplatedData setMsgForFollowUp(Hospital hospital, FollowUpDTO followUp, HospitalSettingKey key) {
        return JpaUtils.executeInTransaction(() -> {
            WechatTemplatedData data = new WechatTemplatedData();

            String appId = getAppId(hospital, PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
            manageData(data, key, appId, hospital);
            data.setFirstStr("");
            data.addKeywordsStr(followUp.getUsername());
            data.addKeywordsStr(followUp.getDate());
            data.addKeywordsStr(followUp.getContent());
            data.setRemarkStr("");
            // 小程序跳转
            data.setMiniProgram(new MiniProgram(followUp.getAppid(), followUp.getPagePath(), false));
            return data;
        });
    }


    private WechatTemplatedData getWxMpTemplateMsg(HospitalSettingKey hospitalSettingKey,
                                                   Object data) {
        if (data instanceof Order) {
            Order order = orderRepository.getById(((Order) data).getId());
            switch (hospitalSettingKey) {
                case NOTIFY_WAITING_FOR_DOCTOR_START:
                    return setMsgForWaitDoctor(order);
                case NOTIFY_RE_WAITING_FOR_DOCTOR_START:
                    return setMsgForReWaitDoctor(order);
                case NOTIFY_CALL_FOR_VISIT:
                    return setMsgForVisit(order);
                case NOTIFY_TO_EVALUATE:
                    return setMsgForEvaluate(order);
                case NOTIFY_ORDER_CLOSED:
                    return setMsgForOrderClosed(order);
                case NOTIFY_ORDER_CANCELED:
                    return setMsgForOrderCanceled(order);
                case NOTIFY_ORDER_PENDING:
                    return setMsgForOrderPending(order);
                case NOTIFY_VISIT_REMIND:
                    return setMsgForVisitRemind(order);
                default:
                    return null;
            }
        }

        if (data instanceof ExamOrder) {
            ExamOrder examOrder = (ExamOrder) data;
            switch (hospitalSettingKey) {
                case NOTIFY_TO_APPOINTMENT_CHECK:
                    return setMsgForToAppointmentCheck(examOrder);
                case NOTIFY_APPOINTMENT_CHECK_SUCCESS_TEMPLATE:
                    return setMsgForAppointmentCheckSuccess(examOrder);
                case NOTIFY_APPOINTMENT_CHECK_CANCEL_TEMPLATE:
                    return setMsgForAppointmentCheckCancel(examOrder);
                case NOTIFY_SIGN_SUCCESS:
                    return setMsgForSignSuccess(examOrder);
                default:
                    return null;
            }
        }

        if (data instanceof ExamOrderReport) {
            ExamOrderReport report = (ExamOrderReport) data;
            switch (hospitalSettingKey) {
                case NOTIFY_REPORT_CREATED:
                    return setMsgForReportCreated(report);
                default:
                    return null;
            }
        }

        return null;
    }

    private Hospital getHospital(Object data) {
        if (data instanceof Order) {
            Order order = (Order) data;
            return order.getHospital();
        }

        if (data instanceof ExamOrder) {
            ExamOrder examOrder = (ExamOrder) data;
            return examOrder.getHospital();
        }

        if (data instanceof ExamOrderReport) {
            ExamOrderReport report = (ExamOrderReport) data;
            return report.getExamOrder().getHospital();
        }

        return null;
    }

    // 根据医院、用户、AppId查询openId
    private List<String> getOpenIds(Hospital hospital, User user, String appId) {
        List<UserPlatformInfo> upiList = userPlatformInfoService.getByHospitalAndUserAndAppId(hospital, user, appId);
        if (CollectionUtils.isEmpty(upiList)) {
            log.info("getOpenIdApps:  userId: {}, appId: {}", user.getId(), appId);
            log.error("通过用户和appId未查出openId，请检查");
        }
        return upiList.stream().map(UserPlatformInfo::getOpenId).collect(Collectors.toList());
    }

    private WechatTemplatedData setMsgForToAppointmentCheck(ExamOrder order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_TO_APPOINTMENT_CHECK, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "appointment/examine/detail/" + order.getId()));
        data.setFirstStr("尊敬的" + order.getPatient().getName() + "，您预约了明天的检查，预约详情如下：");
        data.setRemarkStrColor("请您提前30分钟签到，取号等候检查。 \n"
                                   + "点击查看检查注意须知>>", "#FF0000");

        data.addKeywordsStr(order.getHospital().getName());
        data.addKeywordsStr(order.getExamItem().getOfflineDept().getDeptName());
        data.addKeywordsStr(order.getExamItem().getName());
        Date startTime = order.getService().getStartTime();
        Date endTime = order.getService().getEndTime();
        data.addKeywordsStr(TimeUtils.getAppointmentTime(startTime, endTime));
        return data;
    }

    private WechatTemplatedData setMsgForAppointmentCheckSuccess(ExamOrder order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_APPOINTMENT_CHECK_SUCCESS_TEMPLATE, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "appointment/examine/detail/" + order.getId()));
        data.setFirstStr("您的检查项目预约成功！");
        data.setRemarkStrColor("请您提前30分钟签到，取号等候检查。 \n"
                                   + "点击查看检查注意须知>>", "#FF0000");

        data.addKeywordsStr(order.getExamItem().getName());
        data.addKeywordsStr(order.getPatient().getName());
        Date startTime = order.getService().getStartTime();
        Date endTime = order.getService().getEndTime();
        data.addKeywordsStr(TimeUtils.getAppointmentTime(startTime, endTime));
        data.addKeywordsStr(order.getExamItem().getOfflineDept().getDeptName());
        data.addKeywordsStr(order.getService().getDevice().getAddress());
        return data;
    }

    private WechatTemplatedData setMsgForAppointmentCheckCancel(ExamOrder order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_APPOINTMENT_CHECK_CANCEL_TEMPLATE, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "appointment/examine"));
        data.setFirstStr("尊敬的" + order.getPatient().getName() + "，您预约的检查已被取消，详情如下：");
        data.setRemarkStrColor("如需重新预约，请在网上或者医院预约窗口重新预约。 \n"
                                   + "点击重新预约>>>>", "#FF0000");

        Hospital hospital = hospitalRepository.getById(order.getHospital().getId());
        data.addKeywordsStr(hospital.getName());
        data.addKeywordsStr(order.getExamItem().getOfflineDept().getDeptName());
        data.addKeywordsStr(order.getExamItem().getName());
        Date startTime = order.getService().getStartTime();
        Date endTime = order.getService().getEndTime();
        data.addKeywordsStr(TimeUtils.getAppointmentTime(startTime, endTime));
        return data;
    }

    private WechatTemplatedData setMsgForReportCreated(ExamOrderReport report) {
        return JpaUtils.executeInTransaction(() -> {
            WechatTemplatedData data = new WechatTemplatedData();

            ExamOrder order = report.getExamOrder();
            order = examOrderRepository.getById(order.getId());
            Optional<ExamOrderOperation> operation = examOrderOperationRepository
                .findByExamOrderAndStep(order, Step.REPORT);

            String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
            manageData(data, HospitalSettingKey.NOTIFY_REPORT_CREATED, appId, order.getHospital());
            data.setUrl(getAddHospitalUrl(order.getHospital()
                , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                , "appointment/examine/report/" + order.getId() + "?patientId=" + order.getPatient().getId()));
            data.setFirstStr(order.getPatient().getName() + "您的检查报告已经完成，请进入系统查看报告结果。");
            data.setRemarkStrColor("点击查看报告详情>>", "#FF0000");

            Hospital hospital = hospitalRepository.getById(order.getHospital().getId());

            data.addKeywordsStr(hospital.getName());
            data.addKeywordsStr(order.getExamItem().getOfflineDept().getDeptName());
            data.addKeywordsStr(order.getExamItem().getName());
            data.addKeywordsStr(order.getExecuteDoctorName());
            String reportDateStr = "";
            if (operation.isPresent()) {
                Date createdDate = operation.get().getCreatedDate();
                reportDateStr = DataTypes.DATE.asString(createdDate, "yyyy-MM-dd HH:mm:ss");
            }
            data.addKeywordsStr(reportDateStr);

            return data;
        });
    }

    private WechatTemplatedData setMsgForSignSuccess(ExamOrder order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_SIGN_SUCCESS, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "appointque?orderid=" + order.getId()));
        data.setFirstStr("您已成功签到！");
        data.setRemarkStrColor("点击查看排队状态>>", "#FF0000");

        data.addKeywordsStr(order.getPatient().getName());
        data.addKeywordsStr(order.getExamItem().getName());
        data.addKeywordsStr(order.getQueueNumber() + "");

        Date startTime = order.getService().getStartTime();
        Date endTime = order.getService().getEndTime();
        data.addKeywordsStr(TimeUtils.getAppointmentTime(startTime, endTime));
        data.addKeywordsStr(DataTypes.DATE.asString(order.getSignDate(), "yyyy-MM-dd HH:mm:ss"));

        return data;
    }

    private WechatTemplatedData setMsgForWaitDoctor(Order order) {
        int estimateWaitTime = orderService.getEstimateWaitTime(order);
        String date = DataTypes.DATE.asString(DateUtils.addMinutes(order.getConfirmedDate(), estimateWaitTime),
                                              Constants.WxMpDate);
        OrderQueueStatusVM queueStatusByOrder = orderQueueService.getQueueStatusByOrder(order);
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_WAITING_FOR_DOCTOR_START, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "waitingroom/" + order.getId()));
        data.setFirstStr("您已开始排队候诊，请提前15分钟准备，留意接收就诊通知。");
        data.setRemarkStrColor("排队状态实时更新↓", "#FF0000");
        data.addKeywordsStr(order.getPatient().getName());
        data.addKeywordsStr(order.getDoctor().getUser().getFullName() + " " + order.getDept().getDeptName());
        data.addKeywordsStr(queueStatusByOrder.getNo() + "");
        data.addKeywordsStr(queueStatusByOrder.getWaitingPersons() + "");
        data.addKeywordsStr(date);
        return data;
    }

    private WechatTemplatedData setMsgForReWaitDoctor(Order order) {
        int estimateWaitTime = orderService.getEstimateWaitTime(order);
        String date = DataTypes.DATE.asString(DateUtils.addMinutes(order.getConfirmedDate(), estimateWaitTime),
                                              Constants.WxMpDate);
        OrderQueueStatusVM queueStatusByOrder = orderQueueService.getQueueStatusByOrder(order);
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_RE_WAITING_FOR_DOCTOR_START, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "waitingroom/" + order.getId()));
        data.setFirstStr("由于超时未进入云诊间，您已重新开始排队候诊，请提前15分钟准备，留意接收就诊通知。");
        data.setRemarkStrColor("排队状态实时更新↓", "#FF0000");
        data.addKeywordsStr(order.getPatient().getName());
        data.addKeywordsStr(order.getDoctor().getUser().getFullName() + " " + order.getDept().getDeptName());
        data.addKeywordsStr(queueStatusByOrder.getNo() + "");
        data.addKeywordsStr(queueStatusByOrder.getWaitingPersons() + "");
        data.addKeywordsStr(date);
        return data;
    }


    private WechatTemplatedData setMsgForEvaluate(Order order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_TO_EVALUATE, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "evaluation/" + order.getId()));
        data.setFirstStr("本次就诊已完成，请您对我们的服务进行评价！");
        data.setRemarkStrColor("为本次服务打个分呗，您的好评是我们进步的最大动力！", "#FF0000");
        data.addKeywordsStr(order.getPatient().getName());
        data.addKeywordsStr(
            order.getDoctor().getUser().getFullName() + " " + order.getDept().getDeptName());
        data.addKeywordsStr(order.getHospital().getName());
        data.addKeywordsStr(DataTypes.DATE.asString(order.getAdmissionDate(), Constants.WxMpDate));
        return data;
    }

    private WechatTemplatedData setMsgForOrderClosed(Order order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_ORDER_CLOSED, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital(), HospitalSettingsHelper.getString(order.getHospital(),
                                                                                            HospitalSettingKey.NOTIFY_USR_URL_PREFIX),
                                      "orders"));
        data.setFirstStr("您的订单未及时完成支付，现已自动关闭。");
        data.setRemarkStrColor("已关闭的订单仅可查看，不可支付↓", "#FF0000");
        data.addKeywordsStr(order.getPatient().getName());
        data.addKeywordsStr(
            DataTypes.DATE.asString(order.getPayOrderCreatedDate(), Constants.WxMpDate));
        data.addKeywordsStr("超时未支付");
        data.addKeywordsStr(DataTypes.DATE.asString(new Date(), Constants.WxMpDate));
        return data;
    }

    private WechatTemplatedData setMsgForOrderCanceled(Order order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_ORDER_CANCELED, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "orders"));
        data.setFirstStr("您的" + order.getOrderType().getName() + "订单已取消。");
        data.setRemarkStrColor("已关闭的订单仅可查看，不可支付↓", "#FF0000");
        data.addKeywordsStr(order.getPatient().getName());
        data.addKeywordsStr(DataTypes.DATE.asString(order.getEndedDate(), Constants.WxMpDate));
        data.addKeywordsStr(order.getId().toString());
        data.addKeywordsStr(order.getEndedReason());
        return data;
    }

    private WechatTemplatedData setMsgForOrderPending(Order order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_ORDER_PENDING, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "orders"));
        data.setFirstStr("您有一笔订单尚未支付。");
        int anInt = hospitalSettingService
            .getInt(order.getHospital(), HospitalSettingKey.PAY_ORDER_LIMIT_TIME);
        String date = DataTypes.DATE
            .asString(DateUtils.addMinutes(order.getPayOrderCreatedDate(), anInt), Constants.WxMpDate);
        data.setRemarkStrColor("请在" + date + "之前完成支付，如过期未支付，订单将自动关闭。↓", "#FF0000");
        data.addKeywordsStr(
            order.getPatient().getName() + " " + order.getOrderType().getName() + "挂号 " + order.getDept()
                .getDeptName());
        data.addKeywordsStr(order.getId().toString());

        data.addKeywordsStr(NumberUtils.getBigDecimalStrFromInt(order.getRegistrationFee()) + "元");
        data.addKeywordsStr(DataTypes.DATE.asString(order.getPayOrderCreatedDate(), Constants.WxMpDate));
        return data;
    }


    private WechatTemplatedData setMsgForPrescriptionReady(PrescriptionOrder prescriptionOrder,
                                                           Order order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_PRESCRIPTION_READY, appId, order.getHospital());
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "order/prescription?prescriptionId=" + prescriptionOrder.getId() + "&orderId=" + order.getId()));
        data.setFirstStr("医生已为您开具处方并通过药师审核，可进行后续操作。");
        int anInt = hospitalSettingService
            .getInt(order.getHospital(), HospitalSettingKey.PRESCRIPTION_EXPIRED_TIME);
        String date = DataTypes.DATE
            .asString(DateUtils.addDays(prescriptionOrder.getSendUserDate(), anInt),
                      Constants.WxMpDate);
        data.setRemarkStrColor("处方在" + date + "前有效，如需购买处方中的药品，请及时操作↓", "#FF0000");
        data.addKeywordsStr(order.getPatient().getName());
        data.addKeywordsStr(
            order.getDoctor().getUser().getFullName() + " " + order.getDept().getDeptName());
        data.addKeywordsStr(DataTypes.DATE.asString(prescriptionOrder.getSendUserDate(), Constants.WxMpDate));
        return data;
    }

    private WechatTemplatedData setMsgForVisitRemind(Order order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_VISIT_REMIND, appId, order.getHospital());
        // TODO url 待业务完善后补充
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , ""));
        data.setFirstStr("您明天有一个预约就诊，请提前30分钟签到，取号候诊。");
        data.setRemarkStrColor("请及时签到取号，以免延误就诊↓", "#FF0000");
        data.addKeywordsStr(order.getDept().getDeptName());
        data.addKeywordsStr(order.getDoctor().getUser().getFullName());
        Date startTime = order.getService().getStartTime();
        Date endTime = order.getService().getEndTime();
        data.addKeywordsStr(TimeUtils.getAppointmentTime(startTime, endTime));
        return data;
    }

    private WechatTemplatedData setMsgForVisit(Order order) {
        WechatTemplatedData data = new WechatTemplatedData();
        String appId = getAppId(order.getHospital(), PlatformTypeEnum.OFFICIAL_ACCOUNT, PlatformForEnum.PATIENT);
        manageData(data, HospitalSettingKey.NOTIFY_CALL_FOR_VISIT, appId, order.getHospital());
        int anInt = hospitalSettingService.getInt(order.getHospital(), HospitalSettingKey.REVISIT_AUTOMATIC_TIME);
        String date = DataTypes.DATE.asString(DateUtils.addMinutes(order.getAdmissionDate(), anInt),
                                              Constants.WxMpDate);
        data.setUrl(getAddHospitalUrl(order.getHospital()
            , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
            , "visitchat/" + order.getId()));
        data.setFirstStr("医生已接诊，请就诊人" + order.getPatient().getName() + "进入云诊间开始就诊。");
        data.setRemarkStrColor("如超时未进入，需要医生重新邀请！请尽快进入云诊间↓", "#FF0000");
        data.addKeywordsStr(order.getDept().getDeptName());
        data.addKeywordsStr(order.getDoctor().getUser().getFullName());
        data.addKeywordsStr(date + "前");
        return data;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean refund(Hospital hospital, RefundOrderVM vm, Integer refundAmount, List<String> refundNo) {
        Long orderId = vm.getId();
        refundNo = refundNo.stream().filter(Objects::nonNull).collect(Collectors.toList());
        WechatOrder wechatOrder = wechatOrderRepository
            .findAllByWechatOrderTypeAndProductId(vm.getType(), orderId + "")
            .stream().filter(WechatOrder::getPayed).findFirst().orElse(null);
        if (wechatOrder != null) {
            // 一次退款全部的，只有1个退款单，一次退款部分的，有多个退款单
            List<WechatOrderRefund> refunds = wechatOrderRefundRepository.findAllByOutTradeNo(
                wechatOrder.getOutTradeNo());
            List<String> nos = Lists.newArrayList();
            for (WechatOrderRefund refund : refunds) {
                nos.addAll(refund.getRefundDetailNo());
            }

            WechatOrderRefund refund;
            // https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_4
            // 一笔退款失败后重新提交，请不要更换退款单号，请使用原商户退款单号
            if (refundAmount == null || refundAmount == wechatOrder.getTotalFee()) {
                refund = refunds.stream().filter(u -> "SUCCESS".equalsIgnoreCase(u.getStatus())).findFirst()
                    .orElse(null);
                if (refund == null) {
                    refund = refunds.stream().max(Comparator.comparing(WechatOrderRefund::getCreatedDate))
                        .orElseGet(WechatOrderRefund::new);
                } else {
                    // 全额退款的，如果已经退款成功，不能重复退款
                    throw ErrorType.BAD_REQUEST_ERROR.toProblem("账单不能重复退款");
                }
                refund.setAmount(wechatOrder.getTotalFee());
            } else {
                if (refundNo.stream().anyMatch(StringUtils::isBlank) && !refunds.isEmpty()) {
                    // 没有退款单号的，无法判断是否重复退款，因此不允许多次退
                    throw ErrorType.BAD_REQUEST_ERROR.toProblem("账单不能重复退款");
                }
                if (nos.stream().anyMatch(refundNo::contains)) {
                    throw ErrorType.BAD_REQUEST_ERROR.toProblem("账单不能重复退款");
                }
                refund = new WechatOrderRefund();
                refund.setAmount(refundAmount);
            }
            if (refund.isNew()) {
                refund.setOutRefundNo(UUID.randomUUID().toString());
                refund.setTransactionId(wechatOrder.getTransactionId());
                refund.setOutTradeNo(wechatOrder.getOutTradeNo());
                refund.setFundsAccount("AVAILABLE");
            } else {
                if (System.currentTimeMillis() - refund.getUpdatedDate().getTime() < 120_000) {
                    // 时间间隔太短的重复退款请求，直接返回成功
                    return true;
                }
            }
            refund.setRefundDetailNo(refundNo);
            refund.setStatus("PROCESSING");
            wechatOrderRefundRepository.save(refund);
            IhWxPayRefundRequest request = new IhWxPayRefundRequest();
            request.setDeviceInfo(wechatOrder.getDeviceInfo());
            request.setRefundFee(refund.getAmount());
            request.setOpUserId(wechatOrder.getMchId());
            request.setTransactionId(refund.getTransactionId());
            request.setOutRefundNo(refund.getOutRefundNo());
            request.setTotalFee(wechatOrder.getTotalFee());
            String appId = wechatOrder.getAppId();
            String notifyUrl = getPayNotifyUrl(appId);
            notifyUrl = UrlUtils.concatSegments(notifyUrl, "/refund");
            request.setNotifyUrl(notifyUrl);

            String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
            // 临时添加wxPayService
            setWxPayService(appId, notifyUrl, host);
            IhWxPayRefund wxPayRefund = wechatMpServer.refund(appId, request, host);
            if (wxPayRefund.isSuccess()) {
                return true;
            } else {
                refund.setFailedReason(wxPayRefund.getErrCodeDes());
                refund.setStatus("ABNORMAL");
                wechatOrderRefundRepository.save(refund);
                throw ErrorType.BAD_REQUEST_ERROR.toProblem(wxPayRefund.getResultCode(), wxPayRefund.getErrCodeDes());
            }
        }
        // 没有微信订单，不需要去微信退款，直接修改订单状态
        return false;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean retryRefund(long wechatOrderRefundId) {
        AtomicInteger count = new AtomicInteger(-1);
        try {
            return lockService.executeWithLockThrowError("lock.retryRefund." + wechatOrderRefundId, () -> {
                WechatOrderRefund refund = wechatOrderRefundRepository.getById(wechatOrderRefundId);
                if (Objects.equals("SUCCESS", refund.getStatus())) {
                    return true;
                }
                count.set(refund.getRefundCount() == null ? 0 : refund.getRefundCount());
                count.getAndIncrement();
                refund.setRefundCount(count.get());

                WechatOrder wechatOrder = wechatOrderRepository.findOneByOutTradeNo(refund.getOutTradeNo())
                    .orElse(null);
                if (wechatOrder == null) {
                    log.error("退款重试, 微信支付账单不存在, refundId: " + wechatOrderRefundId + ", transactionId: "
                                  + refund.getTransactionId());
                    refund.setFailedReason("支付账单不存在");
                    wechatOrderRefundRepository.save(refund);
                    return false;
                }
                Hospital hospital = hospitalRepository.getById(wechatOrder.getHospitalId());
                IhWxPayRefundRequest request = new IhWxPayRefundRequest();
                request.setDeviceInfo(wechatOrder.getDeviceInfo());
                request.setRefundFee(refund.getAmount());
                request.setOpUserId(wechatOrder.getMchId());
                request.setTransactionId(refund.getTransactionId());
                request.setOutRefundNo(refund.getOutRefundNo());
                request.setTotalFee(wechatOrder.getTotalFee());
                String appId = wechatOrder.getAppId();
                String notifyUrl = getPayNotifyUrl(appId);
                notifyUrl = UrlUtils.concatSegments(notifyUrl, "/refund");
                request.setNotifyUrl(notifyUrl);

                String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
                // 临时添加wxPayService
                setWxPayService(appId, notifyUrl, host);
                IhWxPayRefund wxPayRefund = wechatMpServer.refund(appId, request, host);

                // 2024年04月26日11:21:31 添加退款重试记录
                RetryRefundRecord retryRefundRecord = new RetryRefundRecord();
                retryRefundRecord.setTransactionId(refund.getTransactionId());
                retryRefundRecord.setTime(new Date());
                retryRefundRecord.setCount(count.get());
                retryRefundRecord.setHospitalId(hospital.getId());
                if (wxPayRefund.isSuccess()) {
                    retryRefundRecord.setSuccess(true);
                    retryRefundRecord.setStatus("PROCESSING");
                    retryRefundRecordRepository.save(retryRefundRecord);

                    refund.setStatus("PROCESSING");
                    wechatOrderRefundRepository.save(refund);
                    return true;
                } else {
                    retryRefundRecord.setSuccess(false);
                    retryRefundRecord.setStatus("ABNORMAL");
                    retryRefundRecord.setFailedReason(wxPayRefund.getErrCodeDes());
                    retryRefundRecordRepository.save(retryRefundRecord);

                    refund.setFailedReason(wxPayRefund.getErrCodeDes());
                    refund.setStatus("ABNORMAL");
                    log.error("退款重试失败失败, refundId=" + refund.getId());
                    wechatOrderRefundRepository.save(refund);
                    retryRefundRecordRepository.save(retryRefundRecord);
                    return true;
                }
            });
        } catch (LockException e) {
            log.info("退款正在进行中, wechatOrderRefundId: " + wechatOrderRefundId, e);
            return false;
        } catch (EntityNotFoundException e) {
            log.info("微信退款账单不存在无法重试退款, wechatOrderRefundId: " + wechatOrderRefundId, e);
            return false;
        } catch (Exception e) {
            WechatOrderRefund refund = wechatOrderRefundRepository.getById(wechatOrderRefundId);
            refund.setFailedReason(e.getMessage());
            if (count.get() == -1) {
                count.set(refund.getRefundCount() == null ? 0 : refund.getRefundCount());
                count.getAndIncrement();
            }
            refund.setRefundCount(count.get());
            wechatOrderRefundRepository.save(refund);

            // 2024年04月26日11:21:31 添加退款重试记录
            RetryRefundRecord retryRefundRecord = new RetryRefundRecord();
            retryRefundRecord.setTransactionId(refund.getTransactionId());
            retryRefundRecord.setTime(new Date());
            retryRefundRecord.setCount(count.get());
            retryRefundRecord.setSuccess(false);
            retryRefundRecord.setStatus("ABNORMAL");
            retryRefundRecord.setFailedReason("程序异常：" + e.getMessage());
            WechatOrder wechatOrder = wechatOrderRepository.findOneByOutTradeNo(refund.getOutTradeNo())
                .orElse(null);
            if (wechatOrder != null) {
                Hospital hospital = hospitalRepository.getById(wechatOrder.getHospitalId());
                retryRefundRecord.setHospitalId(hospital.getId());
            }
            retryRefundRecordRepository.save(retryRefundRecord);

            log.info("退款重试失败, wechatOrderRefundId: " + wechatOrderRefundId, e);
            return false;
        }
    }

    @Override
    public boolean closeWechatOrder(Hospital hospital, ThirdOrderType type, Long orderId) {
        WechatOrder wechatOrder = wechatOrderRepository
            .findAllByWechatOrderTypeAndIsPayedAndProductId(type, false, orderId + "")
            .stream().findFirst().orElse(null);
        log.info("wechat order close..........");
        if (wechatOrder != null) {
            log.info("wechat order close......wechatOrder != null");
            // 临时添加wxPayService
            String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
            setWxPayService(wechatOrder.getAppId(), getPayNotifyUrl(wechatOrder.getAppId()), host);
            WxPayOrderCloseResult close = wechatServerService.closeOrder(wechatOrder.getAppId(),
                                                                         wechatOrder.getOutTradeNo(), host);
            log.info("wechat order close result: {}", close.getXmlString());
            return true;
        }
        return false;
    }

    @Override
    public boolean closeWechatInsuranceOrder(Hospital hospital, ThirdOrderType type, Long orderId) {
        WechatInsuranceOrder wechatOrder = wechatInsuranceOrderRepository
            .findAllByWechatOrderTypeAndProductId(type, orderId + "")
            .stream().filter(u -> !u.getPayed()).findFirst().orElse(null);
        log.info("wechat order close..........");
        if (wechatOrder != null) {
            log.info("wechat order close......wechatOrder != null");
            HospitalPublicPlatform upi = hospitalPublicPlatformRepository.findOneByAppIdAndHospital(wechatOrder.getAppId(), hospital)
                    .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("公众号或小程序未绑定医院, appId=" + wechatOrder.getAppId()));
            String key = null;
            switch (upi.getPlatformType()) {
                case MINI:
                    key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_KEY);
                    break;
                case OFFICIAL_ACCOUNT:
                    key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_KEY);
                    break;
            }
            String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
            setWxInsurancePayService(wechatOrder.getAppId(), key, host);
            BaseWxInstrancePayResult close = wechatServerService.closeInsuranceOrder(wechatOrder.getAppId(),
                                                                            wechatOrder.getHospOutTradeNo(), host);
            log.info("wechat order close result: {}", close.getXmlString());
            return true;
        }
        return false;
    }

    @Override
    public IhWxPayRefund getRefundStats(String appId, String outRefundNo, Hospital hospital) {
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        // 临时添加wxPayService
        setWxPayService(appId, getPayNotifyUrl(appId), host);
        return wechatMpServer.getRefundStats(appId, outRefundNo, host);
    }


    /**
     * 校验退款回调状态
     *
     * @param appId
     * @param xml
     * @return
     */
    private WechatRefundCheckResult checkRefundNotifyResult(String appId, String xml, Hospital hospital) {
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        // 临时添加wxPayService
        setWxPayService(appId, getPayNotifyUrl(appId), host);
        return wechatMpServer.checkRefundNotifyResult(appId, xml, host);
    }

    @Override
    public String getAddHospitalUrl(Hospital hospital, String urlPrefix, String urlSuffix) {
        return UrlUtils.concatSegments(urlPrefix, "hospcode-" + hospital.getCode(), urlSuffix);
    }

    @Override
    public String getAppId(Hospital hospital, PlatformTypeEnum platformType, PlatformForEnum platformForEnum) {
        HospitalPublicPlatform platform = hospitalPublicPlatformService.getPlatform(hospital, platformType,
                                                                                    platformForEnum);
        if (platform == null) {
            throw ErrorType.DATA_ACCESS_ERROR.toProblem("医院" + hospital.getName() + "未绑定有效的公众平台，请检查");
        }
        return platform.getAppId();
    }

    /**
     * 封装重复操作，通过appId和settingKey拿到templateId放入data中
     *
     * @param data
     * @param appId
     */
    @Override
    public void manageData(WechatTemplatedData data, HospitalSettingKey settingKey, String appId, Hospital hospital) {
        String key = settingKey.name();
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        String templateId = ihWxClient.getTemplateId(appId, key, host);
        data.setTemplateId(templateId);
        data.setAppId(appId);
    }


    @Override
    public void sendTemplateMessage(List<WechatTemplatedData> templatedData, Hospital hospital) {
        if (CollectionUtils.isEmpty(templatedData)) {
            return;
        }
        String appId = templatedData.stream().findAny().get().getAppId();
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        String accessToken = ihWxClient.getAccessToken(appId, host);
        log.info("发送模板消息：appid: {}， accessToken: {}", appId, accessToken);
        templatedData.forEach(d -> {
            List<WxMpTemplateData> data = Lists.newArrayList();
            data.add(toWxMpTemplateData("first", new KeywordColor()));
            List<KeywordColor> keywordColors = d.getKeywords();
            for (int i = 0; i < keywordColors.size(); i++) {
                data.add(toWxMpTemplateData("keyword" + (i + 1), keywordColors.get(i)));
            }
            data.add(toWxMpTemplateData("remark", new KeywordColor()));
            WxMpTemplateMessage msg = new WxMpTemplateMessage();
            msg.setData(data);
            msg.setTemplateId(d.getTemplateId());
            msg.setUrl(d.getUrl());
            if (d.getMiniProgram() != null) {
                msg.setMiniProgram(d.getMiniProgram());
            }
            d.getOpenIds().forEach(o -> {
                msg.setToUser(o);
                wechatClient.sendTemplateMessage(accessToken, msg);
            });
        });
    }

    private WxMpTemplateData toWxMpTemplateData(String name, KeywordColor keywordColor) {
        String value = keywordColor.getValue();
        if (value == null) {
            value = "";
        }
        String color = keywordColor.getColor();
        if (color == null) {
            color = "";
        }
        return new WxMpTemplateData(name, value, color);
    }

    private void setWxPayService(String appId, String notifyUrl, String host) {
//        Hospital hospital = hospitalPublicPlatformService.getByAppId(appId).map(HospitalPublicPlatform::getHospital).orElseThrow();
//        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        WxPayService wxPayService = wxPayServiceMap.get(appId);
        log.info("setWxPayService appId：{} ------------------------------", appId);
        IhWxOfficialAccount account = ihWxClient.getIhWxOfficialAccount(appId, host);
        if (StringUtils.isBlank(account.getMerchantId()) || StringUtils.isBlank(account.getMerchantKey())
                || StringUtils.isBlank(account.getMerchantCertificateUrl())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("用于支付的商户号信息不全，请到微信服务中检查");
        }
        String name = FileUtils.getFileName(account.getMerchantCertificateUrl());
        String keyPath = UrlUtils.concatSegments(applicationProperties.getHome(), "keyPath", account.getAppId() + "_" + name);
        FileUtils.downLoadMerchantCertificate(account.getMerchantCertificateUrl(), keyPath);
        if (wxPayService == null || !StringUtils.equals(wxPayService.getConfig().getAppId(), account.getAppId())
                || !StringUtils.equals(wxPayService.getConfig().getAppId(), account.getAppId())
                || !StringUtils.equals(wxPayService.getConfig().getMchId(), account.getMerchantId())
                || !StringUtils.equals(wxPayService.getConfig().getMchKey(), account.getMerchantKey())
                || !StringUtils.equals(wxPayService.getConfig().getKeyPath(), keyPath)) {
            WxPayConfig config = new WxPayConfig();
            config.setAppId(account.getAppId());
            config.setMchId(account.getMerchantId());
            config.setMchKey(account.getMerchantKey());
            config.setKeyPath(keyPath);
            config.setNotifyUrl(notifyUrl);
            config.setSignType(WxPayConstants.SignType.MD5);
            // 设置代理
            ProxyConfig proxyConfig = AppContext.getInstance(ProxyConfig.class);
            ApplicationProperties.IhProxy proxyByUrl = proxyConfig.findProxyByUrl("https://api.mch.weixin.qq.com");
            if (proxyByUrl != null) {
                config.setHttpProxyHost(proxyByUrl.getHost());
                config.setHttpProxyPort(proxyByUrl.getPort());
            }
            WxPayService service = new WxPayServiceImpl();
            service.setConfig(config);
            log.info("setWxPayService appId：{}，service == null", account.getAppId());
            wxPayServiceMap.put(account.getAppId(), service);
        }
    }

    /**
     * 添加或修改医保使用的wxPayService，如果已存在并且不需要修改的，直接返回，医保mchKey不适用商户号的mchKey
     * 调用医保支付平台的API（即https://api.weixin.qq.com/payinsurance/前缀的API）需要使用单独的key，这个key向医保支付平台人工申请(医院和药店申请开通医保支付权限后，医保支付平台会给申请的医院和药店发送一个key)。
     * 当申请微信医保支付权限审核通过后，即会给申请者邮箱发送对应的key。具体申请方式见链接：微信医保支付权限开通操作指引。
     * @param appId
     */
    public void setWxInsurancePayService(String appId, String key, String host) {
        WxPayService wxPayService = wxInsurancePayServiceMap.get(appId);
        log.info("setWxInsurancePayService appId：{} ------------------------------", appId);
        IhWxOfficialAccount account = ihWxClient.getIhWxOfficialAccount(appId, host);

        if (StringUtils.isBlank(account.getMerchantId()) || StringUtils.isBlank(account.getMerchantCertificateUrl())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("用于支付的商户号信息不全，请到微信服务中检查");
        }
        String name = FileUtils.getFileName(account.getMerchantCertificateUrl());
        String keyPath = UrlUtils.concatSegments(applicationProperties.getHome(), "keyPath", account.getAppId() + "_insurance_" + name);
        FileUtils.downLoadMerchantCertificate(account.getMerchantCertificateUrl(), keyPath);

        if (wxPayService == null || !StringUtils.equals(wxPayService.getConfig().getAppId(), account.getAppId())
                || !StringUtils.equals(wxPayService.getConfig().getAppId(), account.getAppId())
                || !StringUtils.equals(wxPayService.getConfig().getMchId(), account.getMerchantId())
                || !StringUtils.equals(wxPayService.getConfig().getMchKey(), key)
                || !StringUtils.equals(wxPayService.getConfig().getKeyPath(), keyPath)) {
            WxPayConfig config = new WxPayConfig();
            config.setAppId(account.getAppId());
            config.setMchId(account.getMerchantId());
            config.setMchKey(key);
            config.setKeyPath(keyPath);
            config.setNotifyUrl(null);
            config.setSignType(WxPayConstants.SignType.MD5);
            // 设置代理
            ProxyConfig proxyConfig = AppContext.getInstance(ProxyConfig.class);
            ApplicationProperties.IhProxy proxyByUrl = proxyConfig.findProxyByUrl("https://api.mch.weixin.qq.com");
            if (proxyByUrl != null) {
                config.setHttpProxyHost(proxyByUrl.getHost());
                config.setHttpProxyPort(proxyByUrl.getPort());
            }
            WxPayService service = new WxPayServiceImpl();
            service.setConfig(config);
            log.info("setWxInsurancePayService appId：{}，service == null", account.getAppId());
            wxInsurancePayServiceMap.put(account.getAppId(), service);
        }
    }

    @Override
    public void sendKFMessage(String officialAccountAppId, WechatKFMessage message, String host) {
        if (message == null) {
            return;
        }
        String accessToken = ihWxClient.getAccessToken(officialAccountAppId, host);
        log.info("发送客服消息，WechatKFMessage={},accessToken={}", JSONObject.toJSONString(message), accessToken);
        wechatClient.sendKFMessage(accessToken, message);
    }

    @Override
    public void sendWechatNotify(List<UserPlatformInfo> list, WechatTemplatedData data, HospitalSettingKey key,
                                 Hospital hospital) {
        log.info("模板消息 key={},开关={}", key, checkWeChatOnOff(key,
                                                                  hospital));
        log.info("发送的公众号为={}", list.toString());
        log.info("发送的消息data={}", JSONObject.toJSONString(data));
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        if (checkWeChatOnOff(key, hospital)) {
            for (UserPlatformInfo userPlatformInfo : list) {
                data.setOpenIds(Lists.newArrayList(userPlatformInfo.getOpenId()));
                data.setAppId(userPlatformInfo.getAppId());

                String templateId = ihWxClient.getTemplateId(userPlatformInfo.getAppId(), key.name(), host);
                if (StringUtils.isEmpty(templateId)) {
                    //公众号模板消息提醒
                    log.error("未找到key={}，appId={}对应的模板消息id", key, userPlatformInfo.getAppId());
                }
                data.setTemplateId(templateId);
                log.info("发送模板消息，appId={},name={},templateId={}",
                         userPlatformInfo.getAppId(), key.name(), templateId);
                log.info("发送模板消息，name={},WechatTemplatedData = {}", key.name(), JSONObject.toJSONString(data));
                if (data.getMiniProgram() != null) {
                    log.info("模板消息key={},pagePath={}", key.name(), data.getMiniProgram().getPagePath());
                }
                sendTemplateMessage(Lists.newArrayList(data), hospital);
            }
        }
    }

    @Override
    public List<UserPlatformInfo> screenOfficialAccount(Hospital hospital, User user,
                                                        PlatformForEnum platformFor) {
        List<Specification<HospitalPublicPlatform>> sps1 = Lists.newArrayList();
        sps1.add(Specifications.eq("hospital", hospital));
        sps1.add(Specifications.eq("platformType", PlatformTypeEnum.OFFICIAL_ACCOUNT));
        sps1.add(Specifications.eq("platformFor", platformFor));
        List<HospitalPublicPlatform> allAccount = hospitalPublicPlatformRepository.findAll(
            Specifications.and(sps1));
        //筛选出该用户关注的所有公众号
        List<UserPlatformInfo> list = new ArrayList<>();
        for (HospitalPublicPlatform hospitalPublicPlatform : allAccount) {
            List<UserPlatformInfo> accounts = userPlatformInfoRepository.findAllByUserAndAppId(
                user, hospitalPublicPlatform.getAppId());
            list.addAll(accounts);
        }
        return list;
    }

    @Override
    public MiniProgram getMiniProgram(Hospital hospital, HospitalPublicPlatform.PlatformForEnum platform,
                                      String pagePath) {
        //查出该医院的医生端/患者端小程序
        HospitalPublicPlatform publicPlatform = hospitalPublicPlatformService.getPlatform(
            hospital,
            PlatformTypeEnum.MINI,
            platform);
        WxMpTemplateMessage.MiniProgram miniProgram = new WxMpTemplateMessage.MiniProgram();
        if (publicPlatform != null) {
            miniProgram.setAppid(publicPlatform.getAppId());
            miniProgram.setPagePath(pagePath);
        }
        return miniProgram;
    }

    private boolean checkWeChatOnOff(HospitalSettingKey key, Hospital hospital) {
        List<String> methods = HospitalSettingsHelper.getListString(hospital, key);
        return methods.contains(HospitalSettingKey.NotificationMethod.WECHAT.name());
    }

    // 手动推送消息
    @Override
    public void sendMiniProgramMessageManually(Hospital hospital, User sender, List<Patient> patients,
                                               List<UserPlatformInfo> userPlatformInfos, HospitalSettingKey key,
                                               String msgDetails) {
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        String accessToken = ihWxClient.getAccessToken(appId, host);
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // 遍历patients，取出userid到userPlatformInfos中找到对应的userid的openId，发送消息
        // 将 userPlatformInfos 转换为 Map，以用户ID作为键
        Map<Long, UserPlatformInfo> userPlatformInfoMap = userPlatformInfos.stream()
            .collect(Collectors.toMap(info -> info.getUser().getId(), info -> info));

        for (Patient patient : patients) {
            // 获取患者对应的用户ID
            Long userId = patient.getUser().getId();
            // 在 userPlatformInfoMap 中查找对应用户ID的平台信息
            UserPlatformInfo userPlatformInfo = userPlatformInfoMap.get(userId);
            if (userPlatformInfo != null && userPlatformInfo.getOpenId() != null) {
                // 数据装配
                List<String> values = Lists.newArrayList();
                values.add(patient.getName());
                values.add("-");
                values.add(msgDetails);
                values.add(hospital.getName());
                values.add("pages/pat/service/index");
                List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);
                // 发送消息
                WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                    .toUser(userPlatformInfo.getOpenId())
                    .templateId(templateId)
                    .page("-")
                    .miniprogramState(getMiniProgramState())
                    .data(data)
                    .build();
                wechatClient.sendMiniProgramMessage(accessToken, message);
            }
        }
        manualPushListRepository.saveAll(
            patients.stream().map(p -> new ManualPushList(hospital, p, ManualPushStatus.SUCCESS, sender, new Date(), msgDetails))
                .collect(Collectors.toList()));
    }

    /**
     * 发送小程序消息, 这个接口以后不再使用, 每个消息单独写
     * 参考 sendNoticeOutpatientRegister
     * @param hospital
     * @param user
     * @param key
     * @param details
     */
    @Deprecated
    @Override
    public void sendMiniProgramMessage(Hospital hospital, User user, HospitalSettingKey key,
                                       Map<String, String> details) {
        switch (key) {
            case NOTICE_PATIENT_TO_VISIT:
                sendNoticePatientToVisit(hospital, user, details);
                break;
            case NOTICE_PATIENT_TO_EVALUATE:
                sendNoticePatientToEvaluate(hospital, user, details);
                break;
            case NOTICE_PATIENT_DOCTOR_RECEIVED:
                sendNoticePatientDoctorReceived(hospital, user, details);
                break;
            case NOTICE_DOCTOR_TO_REVIEW:
            case NOTICE_DOCTOR_TO_SEND:
                sendNoticeDoctorToReview(hospital, user, details);
                break;
            case NOTICE_OUTPATIENT_CHARGE_SUCCESS:
                sendNoticeOutpatientChargeSuccess(hospital, user, details);
                break;
            case NOTICE_DOCTOR_PATIENT_PAID:
                sendNoticeDoctorPatientPaid(hospital, user, details);
                break;
            case NOTICE_COPY_APPOINTMENT:
            case NOTIFY_NURSE_REFUND:
                sendNoticeCopyAppointment(hospital, user, details, key);
                break;
            case NOTICE_QUESTIONNAIRE_PUSH:
                sendNoticeQuestionnairePush(hospital, user, details);
                break;
            case NOTICE_QUESTIONNAIRE_SATISFACTION:
            case NOTICE_OUT_REGISTER_REFUNDED:
            case NOTICE_OUTPATIENT_CHARGE_REFUNDED:
            case NOTICE_REPORT_SEND:
            case NOTIFY_ORDER_DELIVERED:
            case NOTICE_PATIENT_NURSE_RECEIVED:
                sendNoticeOutpatientRegister(hospital, user, details, key);
                break;
            case NOTICE_IN_PATIENT:
                sendNoticeInPatient(hospital, user, details, key);
                break;
            case NOTICE_OUT_PATIENT:
                sendNoticeOutPatient(hospital, user, details, key);
                break;
            case NOTICE_OUT_REGISTER_DOCTOR_SUSPEND:
                sendNoticeDoctorSuspend(hospital, user, details, key);
                break;
            case NOTICE_PATIENT_GET_MEDICINE:
                sendNoticeOutpatientGetDrugs(hospital, user, details, key);
                break;
            //case NOTICE_OUT_REGISTER_WAIT_PAY:
            case NOTICE_OUTPATIENT_CHARGE_WAIT_PAY:
                sendNoticeOutpatientRegisterWaitPay(hospital, user, details, key);
                break;
            case NOTICE_WAITING_DIAGNOSIS_SEND:
                sendNoticeWaitingDiagnosis(hospital, user, details, key);
                break;
            case NOTIFY_APPOINTMENT_CHECK_SUCCESS_TEMPLATE:
            case NOTIFY_APPOINTMENT_CHECK_CANCEL_TEMPLATE:
                sendNoticeAppointmentCheckOperation(hospital, user, details, key);
                break;
            case NOTIFY_NURSE_START_HOME:
            case NOTIFY_NURSE_UPDATE_VISIT_TIME:
                sendNoticeNursingHome(hospital, user, details, key);
                break;
            case NOTIFY_NURSE_PAY_APPEND_SUCCESS:
                sendNoticeNursingHomePayAppend(hospital, user, details, key);
                break;
            case NOTIFY_NURSE_NEW_MESSAGE:
                sendNoticeNewIMNursingMessage(hospital, user, details);
                break;
        }
    }

    private void sendNoticeAppointmentCheckOperation(Hospital hospital, User user, Map<String, String> details,
                                                   HospitalSettingKey key) {
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("patientName"));
        values.add(details.get("itemName"));
        values.add(details.get("desc"));
        values.add(details.get("appointmentTime"));

        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                    .toUser(openId)
                    .templateId(templateId)
                    .page(details.get("path"))
                    .miniprogramState(getMiniProgramState())
                    .data(data)
                    .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeNursingHome(Hospital hospital, User user, Map<String, String> details,
                                          HospitalSettingKey key) {
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        if (key == HospitalSettingKey.NOTIFY_NURSE_START_HOME) {
            values.add(details.get("patient"));
            values.add(details.get("tip"));
            values.add(details.get("project"));
            values.add(details.get("time"));
        } else if (key ==  HospitalSettingKey.NOTIFY_NURSE_UPDATE_VISIT_TIME) {
            values.add(details.get("desc"));
            values.add(details.get("patient"));
            values.add(details.get("time"));
        }
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                    .toUser(openId)
                    .templateId(templateId)
                    .page(details.get("path"))
                    .miniprogramState(getMiniProgramState())
                    .data(data)
                    .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeNursingHomePayAppend(Hospital hospital, User user, Map<String, String> details, HospitalSettingKey key) {
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.DOCTOR);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("price"));
        values.add(details.get("status"));
        values.add(details.get("homeItem"));
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                    .toUser(openId)
                    .templateId(templateId)
                    .page(details.get("path"))
                    .miniprogramState(getMiniProgramState())
                    .data(data)
                    .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticePatientToVisit(Hospital hospital, User user, Map<String, String> details) {
        HospitalSettingKey key = HospitalSettingKey.NOTICE_PATIENT_TO_VISIT;

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("content"));
        values.add(details.get("desc"));
        values.add(details.get("patientName"));
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page("/subpackages/pat/follow-up/index")
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }

    }

    private void sendNoticePatientToEvaluate(Hospital hospital, User user, Map<String, String> details) {
        HospitalSettingKey key = HospitalSettingKey.NOTICE_PATIENT_TO_EVALUATE;

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("desc"));
        values.add(details.get("user"));
        values.add(details.get("type"));
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page("/subpackages/pat/evaluation-my-detail/index?orderId=" + details.get("orderId"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticePatientDoctorReceived(Hospital hospital, User user, Map<String, String> details) {
        HospitalSettingKey key = HospitalSettingKey.NOTICE_PATIENT_DOCTOR_RECEIVED;

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("patient"));
        values.add(details.get("doctor"));
        values.add(details.get("type"));
        values.add(details.get("desc"));
        values.add(details.get("date"));
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page("/subpackages/common/chat-room/index?orderId=" + details.get("orderId"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeDoctorToReview(Hospital hospital, User user, Map<String, String> details) {
        HospitalSettingKey key = HospitalSettingKey.NOTICE_DOCTOR_TO_REVIEW;

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.DOCTOR);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("doctor"));
        values.add(details.get("patient"));
        values.add(details.get("content"));
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page("/subpackages/login/scan/index")
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    /**
     * 门诊缴费成功通知
     *
     * @param hospital
     * @param user
     * @param details
     */
    private void sendNoticeOutpatientChargeSuccess(Hospital hospital, User user, Map<String, String> details) {
        HospitalSettingKey key = HospitalSettingKey.NOTICE_OUTPATIENT_CHARGE_SUCCESS;

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("amount"));
        values.add(details.get("groupId"));
        values.add("缴费成功，请继续检查、化验或取药");
        values.add("缴费成功");
        values.add("门诊缴费");
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                // TODO: 个人中心缴费记录地址
                .page("/subpackages/pat/register-payment-records/index")
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeDoctorPatientPaid(Hospital hospital, User user, Map<String, String> details) {
        HospitalSettingKey key = HospitalSettingKey.NOTICE_DOCTOR_PATIENT_PAID;

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.DOCTOR);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("content"));
        values.add(details.get("desc"));
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page("/pages/common/chat-room/index?orderId=" + details.get("order_id"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeQuestionnairePush(Hospital hospital, User user, Map<String, String> details) {
        HospitalSettingKey key = HospitalSettingKey.NOTICE_QUESTIONNAIRE_PUSH;

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("patientName"));
        values.add(details.get("deptName")); // 消化内科（王医生），2023-05-06 08:00-09:00就诊
        values.add("邀请您对我院就诊服务做出满意度调查!");
        values.add(details.get("hospitalName"));

        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page("subpackages/pat/satisfaction-survey/survey-detail/index?id=" + details.get("id"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    @Override
    public void sendNoticeOutpatientRegister(Hospital hospital, User user, Map<String, String> details,
                                             HospitalSettingKey key) {
        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        //就诊人
        //就诊科室
        //提示说明
        //医院名称
        List<String> values = Lists.newArrayList();
        values.add(details.getOrDefault("patient", "-"));
        values.add(details.getOrDefault("dept", "-"));
        values.add(details.getOrDefault("desc", "-"));
        values.add(details.getOrDefault("hospital", "-"));

        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page(details.get("path"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeInPatient(Hospital hospital, User user, Map<String, String> details,
                                     HospitalSettingKey key) {
        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        //姓名
        //科室
        //温馨提示
        //办理办法
        //入院时间
        List<String> values = Lists.newArrayList();
        values.add(details.get("patient"));
        values.add(details.get("inDept"));
        values.add(details.get("desc"));
        values.add(details.get("note"));
        values.add(details.get("date"));

        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
//                .page(details.get("path"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeOutPatient(Hospital hospital, User user, Map<String, String> details,
                                      HospitalSettingKey key) {
        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        //姓名
        //温馨提示
        //住院科室
        //出院时间
        List<String> values = Lists.newArrayList();
        values.add(details.get("patient"));
        values.add(details.get("desc"));
        values.add(details.get("dept"));
        values.add(details.get("date"));

        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
//                .page(details.get("path"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeDoctorSuspend(Hospital hospital, User user, Map<String, String> details,
                                         HospitalSettingKey key) {
        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        // 温馨提示
        // 科室医生
        // 就诊人
        // 停诊时间
        List<String> values = Lists.newArrayList();
        values.add(details.get("desc"));
        values.add(details.get("dept") + " (" + details.get("doctor") + "）");
        values.add(details.get("patient"));
        values.add(details.get("date"));

        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page(details.get("path"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeOutpatientGetDrugs(Hospital hospital, User user, Map<String, String> details,
                                              HospitalSettingKey key) {
        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("patient")); //phrase2
        String drugName = details.get("drugName");
        values.add(drugName.length() > 17 ? drugName.substring(0, 17) + "..." : drugName); //thing10
        values.add(details.get("address")); //thing3
        values.add(details.get("desc")); //thing5

        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page(details.get("path"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    private void sendNoticeOutpatientRegisterWaitPay(Hospital hospital, User user, Map<String, String> details,
                                                     HospitalSettingKey key) {
        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("price"));
        values.add(details.get("patientName"));
        values.add(details.get("deptName"));
        values.add(details.get("expenseType"));
        values.add(details.get("desc"));

        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page(details.get("path"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    /**
     * @param hospital 要发送的消息对应的医院
     * @param user     要发送的消息对应的用户
     * @param details  对应的参数信息
     * @param key      消息板对应的key
     *                 <p>
     *                 其中details对应的参数的意思是： patient:    订单中患者的名字 hospital:   订单中医院的名字 desc:       订单中需要告知用户的内容 dept:
     *                 订单中科室的名字，产品说这里暂时置空 path:       小程序消息跳转的前端路径
     */
    private void sendNoticeCopyAppointment(Hospital hospital, User user, Map<String, String> details,
                                           HospitalSettingKey key) {
        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("patient"));
        values.add(details.get("dept"));
        values.add(details.get("desc"));
        values.add(details.get("hospital"));
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page(details.get("path"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    /**
     * 候诊排队消息
     *
     * @param hospital
     * @param user
     * @param details
     * @param key
     */
    private void sendNoticeWaitingDiagnosis(Hospital hospital, User user, Map<String, String> details,
                                            HospitalSettingKey key) {
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.PATIENT);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        List<String> values = Lists.newArrayList();
        values.add(details.get("visitTime")); // 就诊时间
        values.add(details.get("doctorName")); // 医生
        values.add(details.get("desc")); // 排队情况
        values.add(details.get("deptName")); // 科室
        values.add(details.get("patientName")); // 就诊人
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page(details.get("path"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }

    public static void main(String[] args) {
        List<String> keys = Lists.newArrayList("thing1", "amount2", "phrase");
        Collections.sort(keys);
        System.out.println(keys);
    }

    /**
     * 根据模板id组装发送的通知内容
     *
     * @param templateId 模板ID
     * @param values     内容中对应的值，这里要注意，values的顺序必须要和微信小程序模板中的 key按照字符串排序之后的顺序一致！
     * @return
     */
    private List<WxMaSubscribeMessage.MsgData> assembleData(String appId, String templateId, List<String> values, Hospital hospital) {
//        String appId = getAppId(hospital, PlatformTypeEnum.MINI, platformForEnum);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);
        //先查出来这条模板消息对应的data示例拿到数据的key
        TemplateMsg.Template template = wechatClient.getMessageKeys(appId, accessToken, templateId);
        String content = template.getContent();

        // matching
        Pattern pattern = Pattern.compile(MINI_PROGRAM_MSG_MATCHER); // 正则表达式用于匹配{{}}之间的内容
        Matcher matcher = pattern.matcher(content);
        List<String> keys = Lists.newArrayList();
        while (matcher.find()) {
            keys.add(matcher.group(1).split("\\.")[0]);
        }

        // sort
        Collections.sort(keys);

        // assemble
        List<WxMaSubscribeMessage.MsgData> dataList = Lists.newArrayList();
        for (String key : keys) {
            dataList.add(new WxMaSubscribeMessage.MsgData(key, values.get(keys.indexOf(key))));
        }
        return dataList;
    }

    private String getMiniProgramState() {
        return env.acceptsProfiles(Profiles.of("dev")) ? "trial" : "formal";
    }

    @Override
    public MedicalInsuranceParamResult medicalInsuranceUserQuery(Hospital hospital, User user, String appId,
                                                                 String qrcode) {
        WechatMiniMedicalInsuranceUserQueryParam param = new WechatMiniMedicalInsuranceUserQueryParam();
        if (env.acceptsProfiles(Profiles.of("prod"))) {
            param.setEnv("prod");
        } else {
            param.setEnv("test");
        }

        List<UserPlatformInfo> infos = userPlatformInfoService.getByUserAndAppId(user, appId);
        UserPlatformInfo upi = infos.stream().filter(u -> Objects.equals(u.getHospital(), hospital))
            .findFirst().orElseThrow(ErrorType.WECHAT_USER_NOT_FOUND::toProblem);
        log.info("获取微信医保参数-qrcode获取payAuthNo 用户fullName: {}", upi.getUser().getFullName());
        param.setOpenId(upi.getOpenId());
        param.setQrcode(qrcode);
        param.setPartnerId(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_PARTNER_ID));
        param.setPartnerSecret(
            HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_PARTNER_SECRET));

        try {
            WechatMiniMedicalInsuranceUserQueryResult result = medicalInsuranceService.getWechatMiniMedicalInsuranceUserQuery(
                    param);
            return new MedicalInsuranceParamResult(result);
        } catch (QrCodeExpireException e) {
            log.info("获取用户医保参数失败", e);
            throw ErrorType.QR_CODE_EXPIRED.toProblem(e.getMessage());
        } catch (Exception e) {
            log.error("获取用户医保参数失败", e);
            throw ErrorType.BAD_REQUEST_ERROR.toProblem(e.getMessage());
        }

    }

    private IhWxMedicalInsurancePayOrderResult getMedicalInsuranceUnifiedOrder(String appId,
                                                                               WechatInsuranceOrderInVM orderVM,
                                                                               String ip,
                                                                               Hospital hospital,
                                                                               UserPlatformInfo upi,
                                                                               IhWxMedicalInsurancePayOrderRequest.MedicalInsuranceContent medicalInsuranceContent) {
        // 是否是国家医保, 上海5期医保没有payOrderId字段
        boolean isNational = StringUtils.isNotBlank(orderVM.getPayOrderId());
        String notifyUrl = getInsurancePayNotifyUrl(appId);
        IhWxMedicalInsurancePayOrderRequest ihWxRequest = new IhWxMedicalInsurancePayOrderRequest();
        ihWxRequest.setOrderType(orderVM.getMedicalInsuranceOrderType().getWxCode());
        ihWxRequest.setOpenid(upi.getOpenId());
        ihWxRequest.setHospOutTradeNo(Holder.INSTANCE.nextId() + "");
        ihWxRequest.setHospitalName(hospital.getName());
        ihWxRequest.setTotalFee(orderVM.getTotalFee());
        ihWxRequest.setCashFee(orderVM.getCashFee());
        ihWxRequest.setSpbillCreateIp(ip);
        ihWxRequest.setNotifyUrl(notifyUrl);
        ihWxRequest.setLimitPay("no_credit");
        ihWxRequest.setBody(orderVM.getBody());
        if (StringUtils.isNotBlank(orderVM.getReturnUrl())) {
            ihWxRequest.setReturnUrl(orderVM.getReturnUrl());
        }
        ihWxRequest.setCityId(orderVM.getCityId());
        ihWxRequest.setInsuranceFee(orderVM.getInsuranceFee());
        // 1：现金 2：医保 3：现金+医保
        // 备注说明：
        // 1、当订单要走医保支付时，
        // （1）cash_fee>0, paytype填3
        // （2）cash_fee=0, paytype填2
        // 2、当订单不使用医保支付时 paytype填1。
        // 3、当订单有走医保【6201】相关接口时，如果订单为纯自费订单，paytype填3。
        if (ihWxRequest.getCashFee() == 0) {
            ihWxRequest.setPayType(2);
        } else {
            ihWxRequest.setPayType(3);
        }
        ihWxRequest.setUserCardType(1);
        ihWxRequest.setUserCardNo(DigestUtils.md5Hex(orderVM.getUserCardNo().toUpperCase()));
        ihWxRequest.setUserName(orderVM.getPatientName());
//        ihWxRequest.setIsDept(StringUtils.isBlank(orderVM.getPayOrderId()) ? null : "4");
        ihWxRequest.setIsDept("4");
        ihWxRequest.setSerialNo(orderVM.getSerialNo());
        ihWxRequest.setGmtOutCreate(TimeUtils.dateToString(orderVM.getGmtOutCreate(), "yyyyMMddHHmmss"));
        ihWxRequest.setRequestContent(StandardObjectMapper.stringify(medicalInsuranceContent));
        ihWxRequest.setAttach(""); // 查询与通知中返回，可作为自定义参数
        ihWxRequest.setBillNo(StringUtils.isBlank(orderVM.getPayOrderId()) ? orderVM.getShBillNo() : orderVM.getProductId());
        String key = null;
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        switch (orderVM.getPlatformType()) {
            case MINI:
                ihWxRequest.setOrgNo(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_ORG_CODG));
                ihWxRequest.setChannelNo(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_CHANNEL));
                if (isNational) {
                    ihWxRequest.setCityId(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_ORDER_CITY_CODE));
                } else {
                    ihWxRequest.setCityId(orderVM.getCityId());
                }
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_KEY);
                break;
            case OFFICIAL_ACCOUNT:
                ihWxRequest.setOrgNo(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_ORG_CODG));
                ihWxRequest.setChannelNo(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_CHANNEL));
                if (isNational) {
                    ihWxRequest.setCityId(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_ORDER_CITY_CODE));
                } else {
                    ihWxRequest.setCityId(orderVM.getCityId());
                }
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_KEY);
                break;
            default:
        }
        setWxInsurancePayService(appId, key, host);
        IhWxMedicalInsurancePayOrderResult result = wechatServerService.getMedicalInsuranceUnifiedOrder(appId, ihWxRequest, host, isNational);
        saveInsuranceOrder(ihWxRequest, result, orderVM, hospital.getId());
        return result;
    }

    /**
     * 保存微信医保账单
     *
     * @param request
     * @param result
     * @param orderVM
     * @param hospitalId
     * @return
     */
    private WechatInsuranceOrder saveInsuranceOrder(IhWxMedicalInsurancePayOrderRequest request,
                                                    IhWxMedicalInsurancePayOrderResult result,
                                                    WechatInsuranceOrderInVM orderVM, Long hospitalId) {
        WechatInsuranceOrder order = new WechatInsuranceOrder();
        order.setWechatOrderType(orderVM.getType());
        order.setProductId(orderVM.getProductId());
        order.setHospitalId(hospitalId);
        order.setAppId(request.getAppid());
        order.setOpenId(request.getOpenid());
        order.setOrderType(orderVM.getMedicalInsuranceOrderType());
        order.setHospOutTradeNo(request.getHospOutTradeNo());
        order.setHospitalName(request.getHospitalName());
        order.setTotalFee(request.getTotalFee());
        order.setCashFee(request.getCashFee());
        order.setCashAddFee(request.getCashAddFee());
        order.setCashAddWording(request.getCashAddWording());
        order.setCashReducedFee(request.getCashReducedFee());
        order.setCashReducedWording(request.getCashReducedWording());
        order.setAllowFeeChange(request.getAllowFeeChange());
        order.setSpbillCreateIp(request.getSpbillCreateIp());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setLimitPay(request.getLimitPay());
        order.setBody(request.getBody());
        order.setReturnUrl(request.getReturnUrl());
        order.setPayType(request.getPayType());
        order.setCityId(request.getCityId());
        order.setConsumeType(request.getConsumeType());
        order.setInsuranceFee(request.getInsuranceFee());
        order.setUserCardType(request.getUserCardType());
        order.setUserCardNo(request.getUserCardNo());
        order.setUserName(request.getUserName());
        order.setIsDept(request.getIsDept());
        order.setSerialNo(request.getSerialNo());
        order.setOrgNo(request.getOrgNo());
        order.setGmtOutCreate(TimeUtils.convert(request.getGmtOutCreate()));
        order.setRequestContent(request.getRequestContent());
        order.setBillNo(request.getBillNo());
        order.setExtend(request.getExtend());
        order.setAttach(request.getAttach());
        order.setChannelNo(request.getChannelNo());
        order.setMedTransId(result.getMedTransId());
        order.setPayUrl(result.getPayUrl());
        order.setPayAppId(result.getPayAppid());
        order.setPayOrderId(orderVM.getPayOrderId());
        order.setShBillNo(request.getBillNo());
        wechatInsuranceOrderRepository.save(order);
        return order;
    }


    @Override
    @Transactional(noRollbackFor = WechatException.class)
    public IhWxMedicalInsurancePayOrderResult getMedicalInsuranceUnifiedOrder(User user, String appId,
                                                                              CreateWechatOrderVM vm, String ip,
                                                                              Hospital hospital, Long userPlatform) {
        UserPlatformInfo upi = userPlatformInfoService.getOneUserPlatformInfo(hospital, user, appId, userPlatform);

        MedicalInsuranceParam insuranceParam = vm.getInsuranceParam();
        insuranceParam.setPlatformType(upi.getPlatformType());
        WechatInsuranceOrderInVM wechatOrderInVM = new WechatInsuranceOrderInVM();
        wechatOrderInVM.setType(vm.getType());
        wechatOrderInVM.setTradeType(WechatOrder.TradeType.JSAPI);
        wechatOrderInVM.setCityId(insuranceParam.getCityId());
        wechatOrderInVM.setPlatformType(insuranceParam.getPlatformType());
        IhWxMedicalInsurancePayOrderRequest.MedicalInsuranceContent content = new IhWxMedicalInsurancePayOrderRequest.MedicalInsuranceContent();
        content.setPayAuthNo(insuranceParam.getPayAuthNo());
        content.setSetlLatlnt(insuranceParam.getUserLongitudeLatitude().toString());

        List<WechatInsuranceOrder> orders = wechatInsuranceOrderRepository
            .findAllByWechatOrderTypeAndProductId(vm.getType(), vm.getId() + "");
        WechatInsuranceOrder wechatOrder = orders.stream().filter(WechatInsuranceOrder::getPayed).findFirst()
            .orElse(null);
        if (wechatOrder != null) {
            throw new WechatException("账单已支付");
        }
        Patient patient = null;
        String serialNo = null;
        switch (vm.getType()) {
            case REGISTER:
                // TODO: 待实现,线上自诩复诊
                break;
            case RECIPE:
                // TODO: 待实现,线上处方
                break;
            case OUTPATIENT_FEE:
                HisOutpatientChargeGroup group = AppContext.getInstance(HisOutpatientChargeGroupRepository.class)
                    .getById(vm.getId());
                wechatOrderInVM.setTotalFee(group.getTotalPayAmount());
                wechatOrderInVM.setBody("门诊缴费");
                wechatOrderInVM.setProductId(vm.getId() + "");
                wechatOrderInVM.setInsuranceFee(group.getInsuranceFee());
                wechatOrderInVM.setCashFee(group.getSelfAmount());
                wechatOrderInVM.setMedicalInsuranceOrderType(MedicalInsuranceOrderType.DIAG_PAY);
                // 医保结算，只能是单项目
                wechatOrderInVM.setSerialNo(group.getHisOutpatientCharges().get(0).getHisSerialNo());
                serialNo = wechatOrderInVM.getSerialNo();
                wechatOrderInVM.setGmtOutCreate(group.getHisOutpatientCharges().get(0).getGmtOutCreate());
                wechatOrderInVM.setPayOrderId(group.getHisOutpatientCharges().get(0).getPayOrdId());
                wechatOrderInVM.setShBillNo(group.getHisOutpatientCharges().get(0).getShBillNo());
                content.setPayOrdId(group.getHisOutpatientCharges().get(0).getPayOrdId());
                patient = patientRepository.getById(group.getPatientId());
                // TODO: 对应处方上传的出参单号 不知道是什么
                log.info("------------------------患者创建门诊缴费账单--------------------------");
                AppContext.getInstance(OutpatientService.class).changeAggregatePayment(group, AggregatePayment.OTHER);
                break;
            case BEFORE_INPATIENT_FEE:
                // TODO: 待实现,住院预交金
                break;
            case OUTPATIENT_REGISTER_FEE:
                OfflineOrder offlineOrder = AppContext.getInstance(OfflineOrderRepository.class).getById(vm.getId());
                if (offlineOrder.getSelfFlag() == 1) {
                    throw ErrorType.VALIDATION_FAILED.toProblem("自费挂号不能使用医保卡支付");
                }
                wechatOrderInVM.setTotalFee(offlineOrder.getRegistrationFee());
                wechatOrderInVM.setInsuranceFee(offlineOrder.getInsuranceFee());
                wechatOrderInVM.setCashFee(offlineOrder.getSelfAmount());
                wechatOrderInVM.setBody("门诊挂号费");
                wechatOrderInVM.setProductId(vm.getId() + "");
                wechatOrderInVM.setMedicalInsuranceOrderType(MedicalInsuranceOrderType.REG_PAY);
                wechatOrderInVM.setSerialNo(offlineOrder.getHisSerialNo());
                wechatOrderInVM.setGmtOutCreate(offlineOrder.getGmtOutCreate());
                wechatOrderInVM.setPayOrderId(offlineOrder.getInternetPayOrderId());
                wechatOrderInVM.setShBillNo(offlineOrder.getShBillNo());
                log.info("------------------------患者创建门诊挂号费账单--------------------------");
                AppContext.getInstance(OfflineOrderService.class).orderPending(vm.getId());
                patient = patientRepository.getById(Long.valueOf(offlineOrder.getPatientId()));
                content.setPayOrdId(offlineOrder.getInternetPayOrderId());
                serialNo = wechatOrderInVM.getSerialNo();
                AppContext.getInstance(OfflineOrderService.class).changeAggregatePayment(offlineOrder, AggregatePayment.OTHER);
                break;
            case OUTPATIENT_NUCLEIC_ACID_FEE:
                // TODO: 待实现,核酸检测费
                break;
            case SELF_BILLING:
                // TODO: 待实现,自助开单费
                break;
            case MEDICAL_RECORD_COPY_APPOINTMENT:
                // TODO: 待实现,病案复印预约预交金
                break;
            case MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:
                // TODO: 待实现,病案复印预约补缴金
                break;
            default:
                throw ErrorType.VALIDATION_FAILED.toProblem();
        }
//        if (patient == null || patient.getRelationship() != Patient.Relationship.SELF) {
//            throw ErrorType.VALIDATION_FAILED.toProblem("医保结算只能使用自己的医保卡，请在就诊人管理中检查就诊人关系是否为本人");
//        }
        wechatOrderInVM.setUserCardNo(patient.getIdCardNum());
        wechatOrderInVM.setPatientName(patient.getName());
        wechatOrderInVM.setReturnUrl(vm.getReturnUrl());
        return orders
            .stream()
            .filter(u -> Objects.equals(u.getSerialNo(), wechatOrderInVM.getSerialNo()))
            .findFirst()
            .map(IhWxMedicalInsurancePayOrderResult::new)
            .orElseGet(() ->
                    getMedicalInsuranceUnifiedOrder(appId, wechatOrderInVM, ip, hospital, upi, content)
            );
    }

    @Override
    public String payInsuranceOrder(WechatInsurancePayCheckResult checkResult) {
        String outTradeNo = checkResult.getOutTradeNo();
        return lockService.executeWithLockThrowError("lock.wechat.pay.outTradeNo." + checkResult.getOutTradeNo(),
                () -> {
                    WechatInsuranceOrder order = wechatInsuranceOrderRepository.findOneByHospOutTradeNo(outTradeNo).get();
                    String appId = order.getAppId();
                    PlatformTypeEnum platformType = hospitalPublicPlatformService.getByAppId(appId)
                            .map(HospitalPublicPlatform::getPlatformType).orElse(null);
                    order.setTradeState(checkResult.getTradeState());
                    order.setCashTradeStatus(checkResult.getCashTradeStatus());
                    order.setInsuranceTradeStatus(checkResult.getInsuranceTradeStatus());
                    order.setInsuranceSelfFee(checkResult.getInsuranceSelfFee());
                    order.setInsuranceFundFee(checkResult.getInsuranceFundFee());
                    order.setInsuranceOtherFee(checkResult.getInsuranceOtherFee());
                    order.setInsuranceOrderId(checkResult.getInsuranceOrderId());
                    order.setCashOrderId(checkResult.getCashOrderId());
                    if (Objects.equals("REFUND", checkResult.getTradeState())) {
                        // 已转入退款的，不进行业务处理，退款轮询时会处理
                        order.setPayed(true);
                        order.setPayTime(TimeUtils.convert(checkResult.getTimeEnd()));
                        wechatInsuranceOrderRepository.save(order);
                        return checkResult.getResultXml();
                    }
                    if (!checkResult.isSuccess()) {
                        wechatInsuranceOrderRepository.save(order);
                        return checkResult.getResultXml();
                    }
                    if (!order.getPayed()) {
                        order.setPayed(checkResult.isSuccess());
                        order.setPayTime(TimeUtils.convert(checkResult.getTimeEnd()));
                        wechatInsuranceOrderRepository.save(order);
                    }
                    if (order.getPayed() && businessOrderManager.checkOrderPaid(order.getWechatOrderType(), order.getProductId())) {
                        return checkResult.getResultXml();
                    }
                    HisPayParam payParam = new HisPayParam(order, platformType);
                    if (Objects.equals("SUCCESS", checkResult.getTradeState())) {
                        payParam.setPayStatus(PayStatus.SUCCESS);
                    } else {
                        payParam.setPayStatus(PayStatus.PAYING);
                    }
                    switch (order.getWechatOrderType()) {
                        case REGISTER:
                            break;
                        case RECIPE:
                            break;
                        case BEFORE_INPATIENT_FEE:
                            break;
                        case OUTPATIENT_FEE:
                            AppContext.getInstance(OutpatientService.class)
                                    .payOutpatientCharge(Long.parseLong(order.getProductId()), payParam);
                            break;
                        case OUTPATIENT_REGISTER_FEE:
                            AppContext.getInstance(OfflineOrderService.class)
                                    .payOutpatientRegisterCharge(Long.parseLong(order.getProductId()), payParam);
                            break;
                        case OUTPATIENT_NUCLEIC_ACID_FEE:
                            break;
                        case SELF_BILLING:
                            break;
                        case MEDICAL_RECORD_COPY_APPOINTMENT:
                            break;
                        case MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:
                            break;
                        default:
                    }
                    // 2025年03月03日 支付成功实时上传订单
                    log.info("医保支付成功监听事件发布 order outTradeNo {}", order.getHospOutTradeNo());
                    publisher.publishEvent(new BillPaymentSuccessEvent(
                        order.getHospitalId(),null, Lists.newArrayList(order), null));
                    return checkResult.getResultXml();
                });
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean insuranceRefund(Hospital hospital, RefundOrderVM vm, List<String> refundNo) {
        Long orderId = vm.getId();
        // 结算失败直接退款，传入的refundNo为空
        if (CollectionUtils.isNotEmpty(refundNo)) {
            refundNo = refundNo.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        // 由于医保统筹账户只能退款1次，因此医保退款只做全额退款
        WechatInsuranceOrder wechatOrder = wechatInsuranceOrderRepository
            .findAllByWechatOrderTypeAndProductId(vm.getType(), orderId + "")
            .stream().filter(WechatInsuranceOrder::getPayed).findFirst().orElse(null);
        if (wechatOrder != null) {
//            if (refundAmount != null && !Objects.equals(refundAmount, wechatOrder.getTotalFee())) {
//                throw ErrorType.BAD_REQUEST_ERROR.toProblem("医保必须全额退款");
//            }

            WechatInsuranceOrderRefund refund = wechatInsuranceOrderRefundRepository.findOneByOutTradeNo(
                    wechatOrder.getHospOutTradeNo())
                .orElseGet(WechatInsuranceOrderRefund::new);
            if ("SUCCESS".equalsIgnoreCase(refund.getStatus())) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("账单不能重复退款");
            }
            if (refund.isNew()) {
                refund.setOutRefundNo(UUID.randomUUID().toString());
                refund.setTransactionId(wechatOrder.getMedTransId());
                refund.setOutTradeNo(wechatOrder.getHospOutTradeNo());
                refund.setAmount(wechatOrder.getTotalFee());
                refund.setSelfAmount(wechatOrder.getCashFee());
                refund.setInsuranceAmount(wechatOrder.getInsuranceFee());
                refund.setCancelSerialNo(vm.getCancelSerialNo());
                refund.setCancelBillNo(vm.getCancelBillNo());
                refund.setRefundType(vm.getRefundType().getCode());
                refund.setPayOrdId(wechatOrder.getPayOrderId());
                refund.setShBillNo(wechatOrder.getShBillNo());
                if (vm.getRefundType() == InsurancePayMethod.CASH_ONLY) {
                    refund.setRefundType(InsurancePayMethod.CASH_ONLY.getCode());
                }
            }
            refund.setRefReason(vm.getRefReason());
            refund.setRefundDetailNo(refundNo);
            refund.setStatus("REFUNDING");
            wechatInsuranceOrderRefundRepository.save(refund);
            IhWxInsurancePayRefundRequest request = new IhWxInsurancePayRefundRequest();
            request.setTransactionId(wechatOrder.getMedTransId());
            request.setOutTradeNo(wechatOrder.getHospOutTradeNo());
            request.setOutRefundNo(refund.getOutRefundNo());
            request.setCancelSerialNo(refund.getCancelSerialNo());
            request.setCancelBillNo(refund.getCancelBillNo());
            request.setPartRefundType(refund.getRefundType());
            IhWxInsurancePayRefundRequest.RequestContent requestContent = new IhWxInsurancePayRefundRequest.RequestContent();
            requestContent.setPayOrdId(refund.getPayOrdId());
            requestContent.setRefReason(refund.getRefReason());
            request.setRequestContent(requestContent);
            String appId = wechatOrder.getAppId();
            HospitalPublicPlatform hospitalPublicPlatform = hospitalPublicPlatformService.getByAppId(appId).orElse(null);
            if (hospitalPublicPlatform == null) {
                refund.setFailedReason("小程序或公众号未绑定或已解绑");
                refund.setStatus("FAIL");
                wechatInsuranceOrderRefundRepository.save(refund);
                return false;
            }

            String key = null;
            switch (hospitalPublicPlatform.getPlatformType()) {
                case MINI:
                    key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_KEY);
                    break;
                case OFFICIAL_ACCOUNT:
                    key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_KEY);
                    break;
            }
            String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
            setWxInsurancePayService(appId, key, host);
            IhWxPayRefund wxPayRefund = wechatServerService.insuranceRefund(appId, request, host);
            if (wxPayRefund.isSuccess()) {
                return true;
            } else {
                refund.setFailedReason(wxPayRefund.getErrCodeDes());
                refund.setStatus("FAIL");
                wechatInsuranceOrderRefundRepository.save(refund);
                throw ErrorType.BAD_REQUEST_ERROR.toProblem(wxPayRefund.getResultCode(), wxPayRefund.getErrCodeDes());
            }
        }
        // 没有微信订单，不需要去微信退款，直接修改订单状态
        return false;
    }

    @Override
    public IhWxPayRefund getInsuranceRefundStats(String appId, String outRefundNo, Hospital hospital) {
        HospitalPublicPlatform upi = hospitalPublicPlatformRepository.findOneByAppIdAndHospital(appId, hospital)
                .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("公众号或小程序未绑定医院, appId=" + appId));
        String key = null;
        switch (upi.getPlatformType()) {
            case MINI:
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_KEY);
                break;
            case OFFICIAL_ACCOUNT:
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_KEY);
                break;
        }
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        setWxInsurancePayService(appId, key, host);
        return wechatServerService.getInsuranceRefundStats(appId, outRefundNo, host);
    }

    @Override
    public WxInsurancePayQueryResult getInsurancePayResult(String appId, String transactionId, String outTradeNo, Hospital hospital) {
        HospitalPublicPlatform upi = hospitalPublicPlatformRepository.findOneByAppIdAndHospital(appId, hospital)
                .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("公众号或小程序未绑定医院, appId=" + appId));
        String key = null;
        switch (upi.getPlatformType()) {
            case MINI:
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_KEY);
                break;
            case OFFICIAL_ACCOUNT:
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_KEY);
                break;
        }
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        setWxInsurancePayService(appId, key, host);
        return wechatServerService.checkInsurancePaymentStatus(appId, transactionId, outTradeNo, host);
    }


    @Override
    @Transactional
    public String refundInsuranceOrder(IhWxPayRefund checkResult) {
        String outRefundNo = checkResult.getOutRefundNo();
        WechatInsuranceOrderRefund refund = wechatInsuranceOrderRefundRepository.findOneByOutRefundNo(outRefundNo)
            .orElse(null);
        if (refund == null) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("微信退款账单不存在");
        }
        if ("SUCCESS".equalsIgnoreCase(refund.getStatus())) {
            return "SUCCESS";
        }
        refund.setStatus(checkResult.getStatus());
        refund.setCashRefundStatus(checkResult.getCashRefundStatus());
        refund.setInsuranceRefundStatus(checkResult.getInsuranceRefundStatus());
        refund.setSuccessTime(checkResult.getSuccessTime());
        refund.setRefundId(checkResult.getRefundId());

        wechatInsuranceOrderRefundRepository.save(refund);
        WechatInsuranceOrder order = wechatInsuranceOrderRepository.findOneByHospOutTradeNo(refund.getOutTradeNo())
            .get();
        switch (order.getWechatOrderType()) {
            case REGISTER:
                break;
            case RECIPE:
                break;
            case OUTPATIENT_REGISTER_FEE:
                AppContext.getInstance(OfflineOrderService.class).refundedAppointmentRegisterCharge(
                    Long.parseLong(order.getProductId()), new HisRefundParam(refund));
                break;
            case MEDICAL_RECORD_COPY_APPOINTMENT:
                break;
            case BEFORE_INPATIENT_FEE:
                break;
            case OUTPATIENT_FEE:
                AppContext.getInstance(OutpatientService.class).outpatientChargeRefunded(
                    Long.parseLong(order.getProductId()), new HisRefundParam(refund));
                break;
            default:
        }
        // 2025年03月03日 退款成功上传订单
        log.info("退款成功监听事件发布 order outTradeNo {}", order.getHospOutTradeNo());
        publisher.publishEvent(new BillRefundSuccessEvent(order.getHospitalId(),null,
                                                                                         Lists.newArrayList(refund),
                                                                                    null));
        return "SUCCESS";
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean retryInsuranceRefund(long wechatOrderRefundId) {
        AtomicInteger count = new AtomicInteger(-1);
        try {
            return lockService.executeWithLockThrowError("lock.retryRefund." + wechatOrderRefundId, () -> {
                WechatInsuranceOrderRefund refund = wechatInsuranceOrderRefundRepository.getById(wechatOrderRefundId);
                if (Objects.equals("SUCCESS", refund.getStatus())) {
                    return true;
                }
                count.set(refund.getRefundCount() == null ? 0 : refund.getRefundCount());
                count.getAndIncrement();
                refund.setRefundCount(count.get());

                WechatInsuranceOrder wechatOrder = wechatInsuranceOrderRepository.findOneByHospOutTradeNo(
                    refund.getOutTradeNo()).orElse(null);
                if (wechatOrder == null) {
                    log.error("退款重试, 微信支付账单不存在, refundId: " + wechatOrderRefundId + ", transactionId: "
                                  + refund.getTransactionId());
                    refund.setFailedReason("支付账单不存在");
                    wechatInsuranceOrderRefundRepository.save(refund);
                    return false;
                }
                Hospital hospital = hospitalRepository.getById(wechatOrder.getHospitalId());
                IhWxInsurancePayRefundRequest request = new IhWxInsurancePayRefundRequest();
                request.setTransactionId(wechatOrder.getMedTransId());
                request.setOutTradeNo(wechatOrder.getHospOutTradeNo());
                request.setOutRefundNo(refund.getOutRefundNo());
                request.setCancelSerialNo(refund.getCancelSerialNo());
                request.setCancelBillNo(refund.getCancelBillNo());
                request.setPartRefundType(refund.getRefundType());
                IhWxInsurancePayRefundRequest.RequestContent requestContent = new IhWxInsurancePayRefundRequest.RequestContent();
                requestContent.setPayOrdId(refund.getPayOrdId());
                requestContent.setRefReason(refund.getRefReason());
                request.setRequestContent(requestContent);

                String appId = wechatOrder.getAppId();
                HospitalPublicPlatform hospitalPublicPlatform = hospitalPublicPlatformService.getByAppId(appId).orElse(null);
                if (hospitalPublicPlatform == null) {
                    refund.setFailedReason("小程序或公众号未绑定或已解绑");
                    refund.setStatus("FAIL");
                    wechatInsuranceOrderRefundRepository.save(refund);
                    return false;
                }
                String key = null;
                switch (hospitalPublicPlatform.getPlatformType()) {
                    case MINI:
                        key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_KEY);
                        break;
                    case OFFICIAL_ACCOUNT:
                        key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_KEY);
                        break;
                }
                String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
                setWxInsurancePayService(appId, key, host);
                IhWxPayRefund wxPayRefund = wechatServerService.insuranceRefund(appId, request, host);
                if (wxPayRefund.isSuccess()) {
                    refund.setFailedReason(null);
                    refund.setStatus("REFUNDING");
                    wechatInsuranceOrderRefundRepository.save(refund);
                    return true;
                } else {
                    refund.setFailedReason(wxPayRefund.getErrCodeDes());
                    refund.setStatus("FAIL");
                    wechatInsuranceOrderRefundRepository.save(refund);
                }
                // 没有微信订单，不需要去微信退款，直接修改订单状态
                return true;
            });
        } catch (LockException e) {
            log.info("医保退款正在进行中, wechatOrderRefundId: " + wechatOrderRefundId, e);
            return false;
        } catch (EntityNotFoundException e) {
            log.info("微信医保退款账单不存在无法重试退款, wechatOrderRefundId: " + wechatOrderRefundId, e);
            return false;
        } catch (Exception e) {
            WechatInsuranceOrderRefund refund = wechatInsuranceOrderRefundRepository.getById(wechatOrderRefundId);
            refund.setFailedReason(e.getMessage());
            if (count.get() == -1) {
                count.set(refund.getRefundCount() == null ? 0 : refund.getRefundCount());
                count.getAndIncrement();
            }
            refund.setRefundCount(count.get());
            wechatInsuranceOrderRefundRepository.save(refund);
            log.info("医保退款重试失败, wechatOrderRefundId: " + wechatOrderRefundId, e);
            return false;
        }
    }

    @Override
    public InputStream getTradeBillStream(Hospital hospital, String appId, Date date) {
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        // 临时添加wxPayService
        setWxPayService(appId, null, host);
        return wechatServerService.getTradeBillStream(appId, date);
    }

    @Override
    public InputStream getFundFlowBillStream(Hospital hospital, String appId, Date date) {
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        // 临时添加wxPayService
        setWxPayService(appId, null, host);
        return wechatServerService.getFundFlowBillStream(appId, date);
    }

    @Override
    public InputStream getInsuranceBillStream(Hospital hospital, String appId, Date date) {
        HospitalPublicPlatform upi = hospitalPublicPlatformRepository.findOneByAppIdAndHospital(appId, hospital)
                .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("公众号或小程序未绑定医院, appId=" + appId));
        List<String> platforms = HospitalSettingsHelper.getListString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_PLATFORM);
        String key = null;
        switch (upi.getPlatformType()) {
            case MINI:
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_KEY);
                if (!platforms.contains(MedicalInsurancePlatform.WECHAT_MINI.name())) {
                    return null;
                }
                break;
            case OFFICIAL_ACCOUNT:
                key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MP_MEDICAL_INSURANCE_KEY);
                if (!platforms.contains(MedicalInsurancePlatform.WECHAT_MP.name())) {
                    return null;
                }
                break;
        }
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        setWxInsurancePayService(appId, key, host);
        return wechatServerService.getInsuranceBillStream(appId, host, date);
    }


    private void sendNoticeNewIMNursingMessage(Hospital hospital, User user, Map<String, String> details) {
        HospitalSettingKey key = HospitalSettingKey.NOTIFY_NURSE_NEW_MESSAGE;

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.DOCTOR);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        String thing1 = details.get("thing1");
        if (thing1.length() > 20) {
            thing1 = thing1.substring(0, 20);
        }
        List<String> values = Lists.newArrayList();
        values.add(details.get("amount2")); // 订单金额
        values.add(details.get("phrase5")); // 订单状态：有患者新消息，请及时回复
        values.add(thing1); // 超过20字截断
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);

        // send
        for (String openId : getOpenIds(hospital, user, appId)) {
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .page("subpackages/common/chat-room/index?orderId=" + details.get("orderId"))
                .miniprogramState(getMiniProgramState())
                .data(data)
                .build();
            wechatClient.sendMiniProgramMessage(accessToken, message);
        }
    }
    @Override
    public void sendNoticeNursingOrderMessage(Hospital hospital, List<User> users, Map<String, String> details,
                                              HospitalSettingKey key) {

        // accessToken
        String appId = getAppId(hospital, PlatformTypeEnum.MINI, PlatformForEnum.DOCTOR);
        String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);

        String accessToken = ihWxClient.getAccessToken(appId, host);

        // templateId
        String templateId = ihWxClient.getTemplateId(appId, key.name(), host);

        // data
        String thing1 = details.get("thing1");
        if (thing1.length() > 20) {
            thing1 = thing1.substring(0, 20);
        }
        List<String> values = Lists.newArrayList();
        values.add(details.get("amount2")); // 订单金额
        values.add(details.get("phrase5")); // 订单状态：有患者新消息，请及时回复
        values.add(thing1); // 超过20字截断
        List<WxMaSubscribeMessage.MsgData> data = assembleData(appId, templateId, values, hospital);
        for (User user : users) {
            for (String openId : getOpenIds(hospital, user, appId)) {
                WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                    .toUser(openId)
                    .templateId(templateId)
                    .page("pages/common/edu-or-order/index")
                    .miniprogramState(getMiniProgramState())
                    .data(data)
                    .build();
                wechatClient.sendMiniProgramMessage(accessToken, message);
            }
        }
    }

}
