package cn.taihealth.ih.service.impl.filter.nursing.order;


import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.enums.ClinicType;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.service.impl.filter.CreatedDateFilter;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class NursingOrderSearch extends SearchCriteria<OrderNursingExt> {

    @Setter
    @Getter
    private ClinicType orderType;

    public static NursingOrderSearch of(String query) {
        NursingOrderSearch deptSearch = new NursingOrderSearch();
        deptSearch.parse(query);
        return deptSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<OrderNursingExt> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case CREATE_TIME:
                return new CreatedDateFilter<>(value);
            case SETTLE_TIME:
                return new SettleDateFilter<>(value);
            case APPOINTMENT_START_TIME:
                return new AppointmentStartDateFilter(TimeUtils.getStartOfDay(TimeUtils.convert(value)));
            case APPOINTMENT_END_TIME:
                return new AppointmentEndDateFilter(TimeUtils.getEndOfDay(TimeUtils.convert(value)));
            case ENABLED:
                return new GenericFilter<>("baseOrder.enabled", Operator.eq, StringUtil.stringToBoolean(value));
            case ORDERSTATUSCONTAINS:
                return new OrderStatusContainsFilter(value);
            case PATIENTNAME:
                return new PatientNameFilter(value);
            case ORDERID:
                return new GenericFilter<>("baseOrder.id", Operator.eq, Long.parseLong(value));
            case MOBILE:
                return new MobileFilter(value);
            case NURSEID:
                return new NurseFilter(Long.parseLong(value));
            case ITEMCLASSIFYID:
                return new GenericFilter<>("homeItem.itemClassify.id", Operator.eq, Long.parseLong(value));
            case HOMEITEMID:
                return new GenericFilter<>("homeItem.id", Operator.eq, Long.parseLong(value));
            case CONSULITEMID:
                return new GenericFilter<>("consulItem.id", Operator.eq, Long.parseLong(value));
            case UPLOADFLAG:
                return new ServiceVideoFilter(StringUtil.stringToBoolean(value));
            case LOSSFLAG:
                return new GenericFilter<>("lossAmount", StringUtil.stringToBoolean(value) ? Operator.gt : Operator.ge, 0);
            case ORDERTYPE:
                orderType = ClinicType.valueOf(value);
                return new GenericFilter<>("baseOrder.orderType", Operator.eq, ClinicType.valueOf(value));
            default:
                return null;
        }
    }


    @Override
    protected SearchFilter<OrderNursingExt> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        ENABLED,
        ORDERSTATUSCONTAINS,
        PATIENTNAME,
        CREATE_TIME,
        APPOINTMENT_START_TIME,
        APPOINTMENT_END_TIME,
        SETTLE_TIME,
        ORDERID,
        MOBILE,
        NURSEID,
        ITEMCLASSIFYID,
        HOMEITEMID,
        CONSULITEMID,
        UPLOADFLAG,
        LOSSFLAG,
        ORDERTYPE,
    }
}
