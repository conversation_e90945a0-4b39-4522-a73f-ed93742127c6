package cn.taihealth.ih.service.impl.filter.crm.questionnaire;

import cn.taihealth.ih.domain.crm.CrmQuestionnaire;
import cn.taihealth.ih.domain.enums.CrmClinicType;
import cn.taihealth.ih.domain.enums.CrmQuestionnaireStatus;
import cn.taihealth.ih.domain.enums.CrmRecoveryStatusEnum;
import cn.taihealth.ih.domain.enums.QuestionType;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

@NoArgsConstructor
public class CrmQuestionnaireSearch extends SearchCriteria<CrmQuestionnaire> {

    private NameFilter nameFilter;
    private IdFilter idFilter;

    public static CrmQuestionnaireSearch of(String query) {
        CrmQuestionnaireSearch articleSearch = new CrmQuestionnaireSearch();
        articleSearch.parse(query);
        return articleSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<CrmQuestionnaire> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }

        switch (qualifier) {
            case NAME:
                this.nameFilter = new NameFilter(value);
                return nameFilter;
            case TAG_NAME:
                return new TagFilter(value);
            case QUESTION_TYPE:
                return new GenericFilter<>("questionType", Operator.eq, Enum.valueOf(QuestionType.class, StringUtils.upperCase(value)));
            case CLINIC_TYPE:
                return new GenericFilter<>("clinicType", Operator.eq, Enum.valueOf(CrmClinicType.class, StringUtils.upperCase(value)));
            case TAG_ID:
                return new TagIdFilter(value);
            case CLASSIFY_ID:
                return new ClassifyIdFilter(value);
            case CREATED_DATE:
                return new CreatedDateFilter(value);
            case STATUS:
                return new GenericFilter<>("status", Operator.eq, Enum.valueOf(CrmQuestionnaireStatus.class, StringUtils.upperCase(value)));
            case PUT:
                return new PutFilter(CrmRecoveryStatusEnum.valueOf(value.toUpperCase()));
            case QUESTIONNAIRE_ID:
                this.idFilter = new IdFilter(Long.parseLong(value));
                return idFilter;
            default:
                return null;
        }
    }

    @Override
    public Specification<CrmQuestionnaire> toSpecification() {
        if (getFilters().isEmpty()) {
            return null;
        }
        List<SearchFilter<CrmQuestionnaire>> filters = getFilters();

        List<SearchFilter<CrmQuestionnaire>> tagIds = new ArrayList<>();
        List<SearchFilter<CrmQuestionnaire>> others = new ArrayList<>();
        for (SearchFilter<CrmQuestionnaire> filter : filters) {
            if (filter instanceof PutFilter) {
                // PutFilter需要单独处理
                continue;
            }
            if (filter instanceof TagIdFilter) {
                tagIds.add(filter);
            } else {
                others.add(filter);
            }
        }
        return Specifications
            .and(Specifications.or(tagIds.stream().map(SearchFilter::toSpecification).collect(Collectors.toList())),
                Specifications.and(others.stream().map(SearchFilter::toSpecification).collect(Collectors.toList())));
    }

    @Override
    protected SearchFilter<CrmQuestionnaire> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        /**
         * 标签名
         */
        TAG_NAME,
        /**
         * 问卷名
         */
        NAME,
        /**
         * 问题类型
         */
        QUESTION_TYPE,
        /**
         * 就诊类别
         */
        CLINIC_TYPE,
        CLASSIFY_ID,
        CREATED_DATE,
        STATUS,
        STARTING,
        /**
         * 标签id
         */
        TAG_ID,
        PUT,
        QUESTIONNAIRE_ID
    }

    public NameFilter getNameFilter() {
        return nameFilter;
    }

    public IdFilter getIdFilter() {
        return idFilter;
    }
}
