package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.MathUtils;
import cn.taihealth.ih.commons.util.RedisUtil;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.VisitorPass;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.his.HisInpatientHospitalCharge;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.his.HisOutpatientChargeGroup;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.OrderExtraInfo;
import cn.taihealth.ih.mq.enums.MessageTypeEnum;
import cn.taihealth.ih.mq.provider.MessageProvider;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.his.HisInpatientHospitalChargeRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeRepository;
import cn.taihealth.ih.repo.hospital.HospitalPublicPlatformRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.repo.order.OrderExtraInfoRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.alipay.AliPayBusinessService;
import cn.taihealth.ih.service.api.offline.InpatientService;
import cn.taihealth.ih.service.api.offline.OutpatientService;
import cn.taihealth.ih.service.api.wechat.WechatService;
import cn.taihealth.ih.service.cache.HisInPatientAmountCache;
import cn.taihealth.ih.service.cache.HisPatNameCache;
import cn.taihealth.ih.service.cache.HospitalCache;
import cn.taihealth.ih.service.dto.MessageCenterDTO;
import cn.taihealth.ih.service.dto.hisrequest.*;
import cn.taihealth.ih.service.dto.mq.RefundRetryDTO;
import cn.taihealth.ih.service.error.LockException;
import cn.taihealth.ih.service.impl.message.ShortMessageServiceImpl;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.wechat.service.impl.drug.DrugStoreUtils;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.EndOrderVM;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.LaboratoryReport;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.OutpatientUnChargeRecipeInfo;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.RisReportResult;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.wechat.service.api.IHWxClient;
import cn.taihealth.ih.wechat.service.vm.WechatTemplatedData;
import cn.taihealth.ih.wechat.service.vm.alipay.AliPayTemplatedData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class HisMessageHandler {

    private final PatientRepository patientRepository;
    private final ElectronicMedicCardRepository electronicMedicCardRepository;
    private final NoticeService noticeService;
    private final VisitorPassRepository visitorPassRepository;
    private final VisitorPassService visitorPassService;
    private final OfflineOrderRepository offlineOrderRepository;
    private final HisInpatientHospitalChargeRepository hisInpatientHospitalChargeRepository;
    private final HisOutpatientChargeRepository hisOutpatientChargeRepository;
    private final OrderExtraInfoRepository orderExtraInfoRepository;
    private final OrderRepository orderRepository;
    private final WechatOrderRefundRepository wechatOrderRefundRepository;
    private final RedisUtil redisUtil;
    private final WechatService wechatService;
    private final AliPayBusinessService aliPayBusinessService;

    private final HisPatNameCache hisPatNameCache;
    private final HisInPatientAmountCache hisInPatientAmountCache;

    public R<String> handle(Hospital hospital, PatientMessageDTO messageDTO) {
        String msgType = messageDTO.getMsgType();
        switch (msgType) {
            case "1001":
                log.info("预约挂号成功");
                break;
            case "1002":
                log.info("就诊提醒通知");
                break;
            case "1101":
                log.info("待缴费通知");
                outpatientChargeWaitPaidMsgHandler(hospital, messageDTO);
                break;
            case "1201":
                log.info("取药通知");
                getMedicineMsgHandler(hospital, messageDTO);
                break;
            case "1202":
                log.info("用药指导通知");
                break;
            case "1301":
                log.info("报告出具通知");
                reportSendMsgHandler(hospital, messageDTO);
                break;
            case "1302":
                log.info("危急值提醒通知");
                break;
            case "1303":
                log.info("监控报警通知");
                process1303Msg(hospital, messageDTO.getOperationSn());
                break;
            case "1304":
                log.info("医技预约成功通知");
                break;
            case "1305":
                log.info("医技取消预约通知");
                break;
            case "1306":
                log.info("检查注意事项通知");
                break;
            case "1401":
                log.info("排队消息通知");
                waitingDiagnosisSendMsgHandler(hospital, messageDTO);
                break;
            case "1501":
                log.info("医生停诊通知");
                doctorSuspendMsgHandler(hospital, messageDTO);
                break;
            case "1601":
                log.info("限号通知");
                break;
            case "1701":
                log.info("满意度调查问卷");
                break;
            case "1801":
                log.info("随访消息");
                followUpMsgHandler(hospital, messageDTO);
                break;
            case "2001":
                log.info("入院提示通知");
                patientInMsgHandler(hospital, messageDTO);
                break;
            case "2002":
                log.info("住院证审核结果通知");
                break;
            case "2003":
                log.info("陪护证审核结果通知");
                visitorPassMsgHandler(hospital, messageDTO);
                break;
            case "2004":
                log.info("延期办理入院通知");
                break;
            case "2101":
                log.info("手术通知");
                break;
            case "2201":
                log.info("住院床位变化通知");
                break;
            case "2301":
                log.info("出院提示通知");
                patientOutMsgHandler(hospital, messageDTO);
                break;
            case "2401":
                log.info("预交金充值成功通知");
                break;
            case "3001":
                log.info("发货通知");
                deliveryNotifyHandler(hospital, messageDTO);
                break;
            case "4001":
                log.info("缴费成功通知");
                break;
            case "4002":
                log.info("缴费失败通知");
                break;
            case "4003":
                log.info("退款通知");
                try {
                    refundPatientCharge(hospital, true, messageDTO);
                } catch (LockException e) {
                    log.info("重复的4003请求正在处理中,放入MQ，3分钟后重试");
                    RefundRetryDTO data = new RefundRetryDTO();
                    data.setHospitalId(hospital.getId());
                    data.setMessage(messageDTO);
                    AppContext.getInstance(MessageProvider.class).sendDelayMessage(data, MessageTypeEnum.MSG_4003);
                }
                break;
            case "4004":
                log.info("退款成功通知");
                refundPatientCharge(hospital, false, messageDTO);
                break;
            case "4006":
                log.info("预约挂号登记取消通知");
                cancelAppointment(hospital, messageDTO);
            case "4008":
                log.info("住院预交金退款通知");
                refundInpatientCharge(hospital,  messageDTO, true);
                break;
            case "4009":
                log.info("住院预交金退款通知");
                refundInpatientCharge(hospital,  messageDTO, false);
                break;
            case "4010":
                log.info("医保支付结果通知");
                insurancePaySendMsgHandler(hospital, messageDTO);
                break;
            case "9001":
                log.info("医生接诊通知");
                doctorCheckMsgHandler(hospital,  messageDTO, OfflineOrder.OutPatientStatus.CONFIRMED);
                break;
            case "9002":
                log.info("医生就诊完毕通知");
                doctorCheckMsgHandler(hospital,  messageDTO, OfflineOrder.OutPatientStatus.COMPLETED);
                break;
            case "9003":
                log.info("就诊医生退回患者");
                doctorCheckMsgHandler(hospital,  messageDTO, OfflineOrder.OutPatientStatus.WAITTREAT);
            default:
                return R.fail("未知消息类型");
        }
        return R.success();
    }

    public R<String> handleBusiness(Hospital hospital, BusinessMessageDTO messageDTO) {
        String msgType = messageDTO.getMsgType();
        switch (msgType) {
            case "301":
                log.info("科目变更");
                deptChangeHandler(hospital, messageDTO);
                break;
            case "302":
                log.info("排班变更");
                scheduleChangeHandler(hospital, messageDTO);
                break;
            case "303":
                log.info("药品变更");
                drugChangeHandler(hospital, messageDTO);
                break;
            case "304":
                log.info("医生变更");
                doctorChangeHandler(hospital, messageDTO);
                break;
            default:
                return R.fail("未知消息类型");
        }
        return R.success();
    }

    private void doctorChangeHandler(Hospital hospital, BusinessMessageDTO messageDTO) {
    }

    private void drugChangeHandler(Hospital hospital, BusinessMessageDTO messageDTO) {
    }

    private void scheduleChangeHandler(Hospital hospital, BusinessMessageDTO messageDTO) {
    }

    private void deptChangeHandler(Hospital hospital, BusinessMessageDTO messageDTO) {

    }

    public void deliveryNotifyHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        // TODO: his未实现，逻辑还不知道是否正确
        ExpressType type = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.EXPRESS_TYPE, new TypeReference<>() {
        });
//        boolean preOrderFlg = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.SHUNFENG_MONTHLY_PAY_ORDER);
        if (HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.HIS_SYNC_MEDICAL_ORDER_STATUS)) {
            if (type == ExpressType.SHUNFENG /* && preOrderFlg */) {
                log.info("选择了顺丰物流并且打开了快速下单开关，不同步使用his药房通知的信息功能");
            } else {
                log.info("医院：{}开启了药品订单状态同步", hospital.getCode());
                Map<String, Object> details = (Map<String, Object>) messageDTO.getMsgDetails();
                Map<String, String> request = Maps.newHashMap();
                if (details.containsKey("company_name")) {
                    request.put("companyName", details.get("company_name") + "");
                }
                if (details.containsKey("company_code")) {
                    request.put("companyCode", details.get("company_code") + "");
                }
                if (details.containsKey("logistics_no")) {
                    request.put("logisticsNo", details.get("logistics_no") + "");
                }
                // 订单发货标识
                request.put("orderStatus", "DELIVER");
                DrugStoreUtils.send("POST", "/hospital/order/statusChange", null, request, null);
            }
        }
    }

    public void followUpMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        log.info("医院：{} 收到随访消息：{}", hospital.getCode(), messageDTO);
        // his的患者唯一id
        String patientId = messageDTO.getPatientId();
        List<Patient> patientList = getPatientByHisPid(hospital, patientId);
        for (Patient patient : patientList) {
            Map<String, Object> details = (Map<String, Object>) messageDTO.getMsgDetails();
            Map<String, String> detail = Maps.newHashMap();
            String content = "";
            if (details.containsKey("dept")) {
                content = details.get("dept") + "诊后随访";
            }
            detail.put("content", content);
            detail.put("patientName", patient.getName());
            detail.put("desc", "为了解您的诊后状态，请您配合完成此次随访");
            detail.put("dept", details.get("dept") + "");
            detail.put("notice_content", String.format("您好，%s，为了解您的诊后状态，请配合完成此次随访（%s）",
                    patient.getName(), details.get("dept")));
            // 发小程序订阅消息
            noticeService.miniProgramNotice(hospital, patient, HospitalSettingKey.NOTICE_PATIENT_TO_VISIT, detail);
        }
    }

//    private void doctorSuspendMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
//        log.info("医院：{} 收到医生停诊通知消息：{}", hospital.getCode(), messageDTO);
//        boolean refundNumber = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.STOP_DIAGNOSIS_AUTO_REFUND_NUMBER);
//        log.info(refundNumber ? "自动退号" : "不自动退号");
//        Object od = messageDTO.getMsgDetails();
//        String msgDetailType = getMsgDetailType(od);
//        if (StringUtils.isBlank(messageDTO.getOperationSn())) {
//            log.error("operationSn字段不能为空！");
//            return;
//        }
//        boolean haveNextStep;
//        if (refundNumber) {
//            switch (msgDetailType) {
//                case "1":
//                    haveNextStep = stopRegisterCharge(hospital, true, messageDTO);
//                    break;
//                case "2":
//                    haveNextStep = stopConsultOrder(hospital, messageDTO);
//                    break;
//                default:
//                    haveNextStep = false;
//            }
//            if (haveNextStep) {
//                sendSuspendNotice(hospital, messageDTO, msgDetailType);
//            }
//        }
//    }

    private void doctorSuspendMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        log.info("医院：{} 收到医生停诊通知消息：{}", hospital.getCode(), messageDTO);
        Object od = messageDTO.getMsgDetails();
        String msgDetailType = getMsgDetailType(od);
        sendSuspendNotice(hospital, messageDTO, msgDetailType);

        //自动退号逻辑
//        boolean refundNumber = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.STOP_DIAGNOSIS_AUTO_REFUND_NUMBER);
//        log.info(refundNumber ? "自动退号" : "不自动退号");
//        if (refundNumber) {
//            switch (msgDetailType) {
//                case "1":
//                    stopRegisterCharge(hospital, true, messageDTO);
//                    break;
////                case "2":
////                    stopConsultOrder(hospital, messageDTO);
////                    break;
//            }
//        }
    }

//    private void sendSuspendNotice(Hospital hospital, PatientMessageDTO messageDTO, String msgDetailType) {
//        String operationSn = messageDTO.getOperationSn();
//        try {
//            Map<String, String> detail = Maps.newHashMap();
//            switch (msgDetailType) {
//                case "1":
//                    offlineOrderRepository.findFirstByHospitalAndSettleIdOrderByCreatedDateDesc(hospital, operationSn)
//                        .ifPresent(offlineOrder -> {
//                            Patient patient = patientRepository.getById(Long.valueOf(offlineOrder.getPatientId()));
//                            detail.put("patient", patient.getName());
//                            detail.put("hospital", hospital.getName());
//                            detail.put("desc", TimeUtils.dateToString(TimeUtils.convert(offlineOrder.getBeginTime()), "yyyy-MM-dd")
//                                + " 医生已停诊");
//                            detail.put("dept", offlineOrder.getDeptName() + " (" + offlineOrder.getDoctorName() + ")");
//                            detail.put("path", "subpackages/pat/orders/index?type=register");
//                            noticeService.miniProgramNotice(hospital, patient.getUser(),
//                                                            HospitalSettingKey.NOTICE_OUT_REGISTER_DOCTOR_SUSPEND, detail);
//                        });
//                    break;
//                case "2":
//                    orderExtraInfoRepository.findOneByHospitalAndSettleId(hospital, operationSn).ifPresent(ext -> {
//                        Order order = orderRepository.findById(ext.getOrderId()).orElse(null);
//                        if (order == null) {
//                            log.info("未找到复诊咨询订单orderId：{}", ext.getOrderId());
//                            return;
//                        }
//                        detail.put("patient", order.getPatient().getName());
//                        detail.put("hospital", hospital.getName());
//                        Date orderDate = order.getRegisteredDate() == null ? order.getCreatedDate() :order.getRegisteredDate();
//                        // 可能存在订单只是创建了但是还没有付款的情况，如果没有付款时间就使用订单的创建时间
//                        detail.put("desc", TimeUtils.dateToString(orderDate, "yyyy-MM-dd") + " 医生已停诊");
//                        detail.put("dept", order.getDept().getDeptName() + " (" + order.getDoctor().getUser().getFullName() + ")");
//                        detail.put("path", "subpackages/pat/orders/index?type=register");
//                        noticeService.miniProgramNotice(hospital, order.getPatient().getUser(),
//                                                        HospitalSettingKey.NOTICE_OUT_REGISTER_DOCTOR_SUSPEND, detail);
//                    });
//                    break;
//            }
//        } catch (Exception e) {
//            log.error("医生停诊消息发送失败", e);
//        }
//    }
    private void sendSuspendNotice(Hospital hospital, PatientMessageDTO messageDTO, String msgDetailType) {
        String operationSn = messageDTO.getOperationSn();
        if (StringUtils.isBlank(operationSn)) {
            log.error("operationSn字段不能为空！");
            return;
        }
        try {
            Map<String, String> detail = Maps.newHashMap();
            if ("1".equals(msgDetailType)) {
                offlineOrderRepository.findFirstByHospitalAndSettleIdOrderByCreatedDateDesc(hospital, operationSn)
                    .ifPresent(offlineOrder -> {
                        Patient patient = patientRepository.getById(Long.valueOf(offlineOrder.getPatientId()));
                        String doctorName = StringUtils.isEmpty(offlineOrder.getDoctorName()) ? "-" : offlineOrder.getDoctorName();
                        detail.put("patient", patient.getName());
                        detail.put("hospital", hospital.getName());
                        detail.put("desc", "您预约的医生临时停诊，挂号订单被取消。");
                        detail.put("dept", offlineOrder.getDeptName());
                        detail.put("doctor", doctorName);
                        detail.put("date", TimeUtils.dateToString(TimeUtils.convert(offlineOrder.getBeginTime()), "yyyy-MM-dd HH:mm"));
                        detail.put("doctorAndDate",
                                   " (" + offlineOrder.getDeptName() +
                                       "，就诊时间：" + TimeUtils.appointmentTimeSlotSpan(offlineOrder.getAppointmentTimeSlot()) +
                                       "）");
                        detail.put("path", "subpackages/pat/orders/index?type=register");
                        detail.put("product_id", offlineOrder.getId().toString());
                        detail.put("notice_content", String.format("很抱歉！%s，您预约的医生%s临时停诊，挂号订单被取消。",
                                                                   patient.getName(), detail.get("doctorAndDate")));

                        MessageCenterDTO mcDTO  = new MessageCenterDTO();
                        mcDTO.setTittle("停诊通知");
                        mcDTO.setContent(String.format("很抱歉！%s，您预约的医生%s临时停诊，挂号订单被取消。",
                                patient.getName(), detail.get("doctorAndDate")));
                        mcDTO.setPatient(patient);
                        mcDTO.setPatientName(patient.getName());
                        mcDTO.setDeptName(offlineOrder.getDeptName());
                        mcDTO.setProductId(offlineOrder.getId());
                        mcDTO.setH5Path(AppContext.getInstance(WechatService.class).getAddHospitalUrl(offlineOrder.getHospital()
                                , HospitalSettingsHelper.getString(offlineOrder.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                                , "/subpackages/pat/register-order-detail/index?orderId=" + offlineOrder.getId() + "&pid=" + patient.getId()));
                        mcDTO.setMiniPath("subpackages/pat/register-order-detail/index?orderId=" + offlineOrder.getId() + "&pid=" + patient.getId());

                        String url = AppContext.getInstance(WechatService.class).getAddHospitalUrl(offlineOrder.getHospital()
                                , HospitalSettingsHelper.getString(offlineOrder.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                                , "/subpackages/pat/register-order-detail/index?orderId=" + offlineOrder.getId() + "&pid=" + patient.getId());
                        // 微信公众号消息对象
                        WechatTemplatedData data = new WechatTemplatedData();
                        data.setUrl(url);
                        // 医院名称
                        data.addKeywordsStr(hospital.getName());
                        // 科室
                        data.addKeywordsStr(offlineOrder.getDeptName());
                        // 科室医生
                        data.addKeywordsStr(doctorName);
                        // 就诊时间
                        data.addKeywordsStr(TimeUtils.appointmentTimeSlotSpan(offlineOrder.getAppointmentTimeSlot()));
                        // 支付宝消息对象
                        AliPayTemplatedData aliPayTemplatedData = new AliPayTemplatedData();
                        aliPayTemplatedData.setUrl("/subpackages/pat/register-order-detail/index?orderId=" + offlineOrder.getId() + "&pid=" + patient.getId());
                        // 就诊人
                        aliPayTemplatedData.addKeywords(patient.getName());
                        // 科室
                        aliPayTemplatedData.addKeywords(offlineOrder.getDeptName());
                        // 科室医生
                        aliPayTemplatedData.addKeywords(doctorName);
                        // 停诊日期
                        aliPayTemplatedData.addKeywords(TimeUtils.dateToString(TimeUtils.convert(offlineOrder.getBeginTime()), "yyyy-MM-dd"));
                        // 停诊说明
                        aliPayTemplatedData.addKeywords("很抱歉！您预约的医生临时停诊，挂号订单被取消。");
                        noticeService.miniProgramNotice(hospital, patient.getUser(),
                                                        HospitalSettingKey.NOTICE_OUT_REGISTER_DOCTOR_SUSPEND, detail, mcDTO, data, aliPayTemplatedData);
                    });
            }
        } catch (Exception e) {
            log.error("医生停诊消息发送失败", e);
        }
    }

    public void doctorCheckMsgHandler(Hospital hospital, PatientMessageDTO messageDTO, OfflineOrder.OutPatientStatus status) {
        log.info("医院：{} 收到医生接诊通知消息：{}", hospital.getCode(), messageDTO);
        if (StringUtils.isBlank(messageDTO.getRegno())) {
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(String.format("医生接诊通知消息参数不正确: " +
                    "[regno:%s]", messageDTO.getRegno()));
        }
        if (status == OfflineOrder.OutPatientStatus.COMPLETED) {
            log.info("医院：{} 医生就诊完毕且是预约挂号三天后发送就诊评价通知：{}", hospital.getCode(), messageDTO);
            offlineOrderRepository.findFirstByRegNoAndHospitalAndType(messageDTO.getRegno(), hospital, ProjectTypeEnum.APPOINTMENT_REGISTRATION)
                    .ifPresent(offlineOrder -> {
                        log.info("医院：{} 三天后发送评价通知offlineOrderId：{}", hospital.getCode(), offlineOrder.getId());
                        Date dateAfterHours = TimeUtils.getDateAfterHours(new Date(), 72);
                        redisUtil.set("out_patient_register:" + offlineOrder.getId(), 1,
                                TimeUtils.dateDiff(new Date(), dateAfterHours));
                    });
        }
        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.eq("type", ProjectTypeEnum.APPOINTMENT_REGISTRATION));
        specs.add(Specifications.eq("regNo", messageDTO.getRegno()));
        offlineOrderRepository.findAll(Specifications.and(specs)).forEach(offlineOrder -> {
            offlineOrder.setStatus(status);
            offlineOrderRepository.save(offlineOrder);
        });
    }

    public void getMedicineMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        log.info("医院：{} 收到取药通知消息：{}", hospital.getCode(), messageDTO);
        // his的患者唯一id
        String patientId = messageDTO.getPatientId();
        List<Patient> patientList = getPatientByHisPid(hospital, patientId);
        Map<String, Object> details = (Map<String, Object>) messageDTO.getMsgDetails();
        String address = (String) details.get("address");;
        String drugName = (String) details.get("drug_name");
        String regNo = messageDTO.getRegno();

        for (Patient patient : patientList) {
            Map<String, String> detail = Maps.newHashMap();

            offlineOrderRepository.findFirstByRegNoAndHospital(regNo, hospital)
                .ifPresent(offlineOrder -> {
                    log.info("取药通知，获取开单科室：{}", offlineOrder.getDeptName());
                    detail.put("dept", offlineOrder.getDeptName());
                });

            detail.put("patient", patient.getName());
            detail.put("address", address);
            detail.put("desc", "请准备好处方笺和就诊卡");
            detail.put("drugName", drugName);
            detail.put("path", "subpackages/pat/register-payment-records/index");
            //details.get("patient") +  "，请准备好处方笺和就诊卡，" + "前往" + details.get("address") +
            //                    "取药，药品有：" + getAdornDrug(details.get("drugName"));
            detail.put("notice_content", String.format("%s，请准备好处方笺和就诊卡，前往%s取药，药品有：%s", detail.get("patient"),
                                                       detail.get("address"), getAdornDrug(drugName)));
            noticeService.miniProgramNotice(hospital, patient.getUser(), HospitalSettingKey.NOTICE_PATIENT_GET_MEDICINE, detail);

        }
    }

    public void outpatientChargeWaitPaidMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        log.info("医院：{} 收到门诊缴费待支付通知消息：{}", hospital.getCode(), messageDTO);
        // his的患者唯一id
        String patientId = messageDTO.getPatientId();
        List<Patient> patientList = getPatientByHisPid(hospital, patientId);
        // 门诊挂号序号
        String operationSn = messageDTO.getOperationSn();
        String todayStr = TimeUtils.dateToString(new Date(), "yyyyMMdd");
        String yesterdayStr = TimeUtils.dateToString(TimeUtils.getYesterdayDate(LocalDate.now()), "yyyyMMdd");
        boolean pick = false;
        List<String> snList = Lists.newArrayList();
        for (Patient patient : patientList) {
            ElectronicMedicCard card = electronicMedicCardRepository.findByPatientAndNumber(patient, messageDTO.getCardNo()).orElse(null);
            List<OutpatientUnChargeRecipeInfo> list = BusinessServiceStrategy.getInstance().getStrategy(true)
                    .getOutpatientUnChargeRecipeList(hospital, patient, card, yesterdayStr, todayStr, "", "0");
            snList = list.stream().map(OutpatientUnChargeRecipeInfo::getRegno).collect(Collectors.toList());
            for (OutpatientUnChargeRecipeInfo outpatientUnChargeRecipeInfo : list) {
                if (StringUtils.equals(outpatientUnChargeRecipeInfo.getRegno(), operationSn)) {
                    pick = true;
                    int total = outpatientUnChargeRecipeInfo.getItem_infos().stream()
                            .mapToInt(info -> Integer.parseInt(info.getAmount()))
                            .sum();
                    Map<String, String> detail = Maps.newHashMap();
                    detail.put("patientName", patient.getName());
                    detail.put("expenseType", "门诊缴费");
                    detail.put("price", MathUtils.division2(total, 100));
                    detail.put("deptName", outpatientUnChargeRecipeInfo.getRecipe_dept_name());
                    detail.put("path", "subpackages/pat/out-patient-fee/index?pid=" + patient.getId());
                    detail.put("desc", "缴费后，前去相应诊室进行检查、化验或取药");
                    detail.put("patient", patient.getName());
                    detail.put("dept", detail.get("deptName"));
                    detail.put("notice_content", String.format("您还需要缴费%s元,缴费后，前去相应诊室进行检查、化验或取药",detail.get("price")));

                    String url = AppContext.getInstance(WechatService.class).getAddHospitalUrl(hospital
                            , HospitalSettingsHelper.getString(hospital, HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                            , "subpackages/pat/out-patient-fee/index?pid=" + patient.getId());
                    // 微信公众号消息对象
                    WechatTemplatedData data = new WechatTemplatedData();
                    data.setUrl(url);
                    // 就诊人
                    data.addKeywordsStr(patient.getName());
                    // 费用类型
                    data.addKeywordsStr("门诊缴费");
                    // 金额
                    data.addKeywordsStr(MathUtils.division2(total, 100));
                    // 缴费项目
                    data.addKeywordsStr(outpatientUnChargeRecipeInfo.getRecipe_name());

                    // 支付宝消息对象
                    AliPayTemplatedData aliPayTemplatedData = new AliPayTemplatedData();
                    aliPayTemplatedData.setUrl("subpackages/pat/out-patient-fee/index?pid=" + patient.getId());
                    // 就诊人
                    aliPayTemplatedData.addKeywords(patient.getName());
                    // 缴费类型
                    aliPayTemplatedData.addKeywords("门诊缴费");
                    // 缴费金额
                    aliPayTemplatedData.addKeywords(MathUtils.division2(total, 100));
                    // 就诊科室
                    aliPayTemplatedData.addKeywords(outpatientUnChargeRecipeInfo.getRecipe_dept_name());
                    // 温馨提示
                    aliPayTemplatedData.addKeywords("请及时缴费后，前去相应诊室进行检查、化验或取药");

                    // 消息中心消息对象
                    MessageCenterDTO mcDTO  = new MessageCenterDTO();
                    mcDTO.setTittle("门诊待缴费通知");
                    mcDTO.setContent(String.format("您还需要缴费%s元，缴费后，前去相应诊室进行检查、化验或取药", MathUtils.division2(total, 100)));
                    mcDTO.setPatient(patient);
                    mcDTO.setPatientName(patient.getName());
                    mcDTO.setDeptName(outpatientUnChargeRecipeInfo.getRecipe_dept_name());
                    mcDTO.setProductId(null);
                    mcDTO.setH5Path(AppContext.getInstance(WechatService.class).getAddHospitalUrl(hospital
                            , HospitalSettingsHelper.getString(hospital, HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                            , "subpackages/pat/out-patient-fee/index?pid=" + patient.getId()));
                    mcDTO.setMiniPath("subpackages/pat/out-patient-fee/index?pid=" + patient.getId());

                    noticeService.miniProgramNotice(hospital, patient.getUser(), HospitalSettingKey.NOTICE_OUTPATIENT_CHARGE_WAIT_PAY, detail, mcDTO, data, aliPayTemplatedData);
                }
            }
        }
        if (!pick) {
            log.error("门诊挂号序号不对：his发送的1101消息中的operationSn：" + operationSn +"和就诊人查询代缴费的Regno:" + snList +"不匹配");
        }
    }

    public void patientInMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        log.info("医院：{} 收到入院提醒消息：{}", hospital.getCode(), messageDTO);
        Map<String, Object> details = (Map<String, Object>) messageDTO.getMsgDetails();
        String address = (String) details.getOrDefault("address", "");;
        String date = (String) details.getOrDefault("admission_time", "");
        String dept = (String) details.getOrDefault("depart_name", "");
        // his的患者唯一id
        String patientId = messageDTO.getPatientId();
        List<Patient> patientList = getPatientByHisPid(hospital, patientId);
        for (Patient patient : patientList) {
            Map<String, String> detail = Maps.newHashMap();
            detail.put("patient", patient.getName());
            detail.put("hospital", hospital.getName());
            detail.put("note", "请在入院时间当天办理入院，并带好入院凭证");
            detail.put("desc", address);
            detail.put("inDept", dept);
            detail.put("date", TimeUtils.dateToString(TimeUtils.convert(date), "yyyy-MM-dd") );
//            detail.put("path", "/subpackages/pat/satisfaction-survey/index");
            detail.put("notice_content", String.format("%s你好， 医生已为您开具入院凭证，请您于%s当天前往%s（%s）办理入院手续", detail.get("patient"), detail.get("date"), dept, address));
            noticeService.miniProgramNotice(hospital, patient.getUser(), HospitalSettingKey.NOTICE_IN_PATIENT, detail);
        }
    }

    public void patientOutMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        log.info("医院：{} 收到出院提醒知消息：{}", hospital.getCode(), messageDTO);
        Map<String, Object> details = (Map<String, Object>) messageDTO.getMsgDetails();
        String date = (String) details.getOrDefault("discharge_time", "");
        String dept = (String) details.getOrDefault("depart_name", "");
        // his的患者唯一id
        String patientId = messageDTO.getPatientId();
        List<Patient> patientList = getPatientByHisPid(hospital, patientId);
        for (Patient patient : patientList) {
            Map<String, String> detail = Maps.newHashMap();
            detail.put("patient", patient.getName());
            detail.put("hospital", hospital.getName());
            detail.put("desc", "具体出院以及出院结算事宜请向护士台了解");
            detail.put("dept", dept);
            detail.put("date", TimeUtils.dateToString(TimeUtils.convert(date), "yyyy-MM-dd") );
//            detail.put("path", "/subpackages/pat/satisfaction-survey/index");
            detail.put("notice_content", String.format("%s你好， 医生已为您开具了出院医嘱，预计出院时间为%s，请您向护士台了解具体出院结算事宜。", detail.get(
                "patient"), detail.get("date")));
            noticeService.miniProgramNotice(hospital, patient.getUser(), HospitalSettingKey.NOTICE_OUT_PATIENT, detail);
        }
    }

    public void reportSendMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        log.info("医院：{} 收到报告出具消息：{}", hospital.getCode(), messageDTO);
        // his的患者唯一id
        String patientId = messageDTO.getPatientId();
        List<Patient> patientList = getPatientByHisPid(hospital, patientId);
        Map<String, String> details = (Map<String, String>) messageDTO.getMsgDetails();
        String type = "";
        // 项目名称
        String itemName = "";
        // 检查结果
        String itemResult = "";
        // 检查时间
        String itemTime = "";
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        switch (details.get("type")) {
            case "1":
                type = "检查";
                RisReportResult risReport = businessService.getRisReportResult(hospital, messageDTO.getOperationSn(), null).stream().findFirst().orElse(null);
                itemName = risReport.getItem_name();
                itemResult = risReport.getReport_result();
                itemTime = risReport.getExec_time();
                break;
            case "2":
                type = "检验";
                LaboratoryReport lisReport = businessService.getLaboratoryReportList(hospital, messageDTO.getOperationSn(), null).stream().findFirst().orElse(null);
                itemName = lisReport.getItem_name();
                itemResult = lisReport.getResult();
                itemTime = lisReport.getExec_time();
                break;
            case "3":
                type = "体检";
                break;
            default:

        }
        String formatStr = String.format("您的%s报告已经出具，请及时查看", "体检".equals(type) ? "体检" : "检查检验");
        for (Patient patient : patientList) {

            Map<String, String> detail = Maps.newHashMap();
            detail.put("patient", patient.getName());
            detail.put("hospital", hospital.getName());
            detail.put("desc", formatStr);
            detail.put("path", "subpackages/pat/report-list/index?pid=" + patient.getId());
            detail.put("notice_content", formatStr);
            detail.put("messageCenterTitle", ("体检".equals(type) ? "体检" : "") + "报告出具提醒" );

            String url = AppContext.getInstance(WechatService.class).getAddHospitalUrl(hospital
                    , HospitalSettingsHelper.getString(hospital, HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                    , "subpackages/pat/report-list/index?pid=" + patient.getId());
            // 微信公众号消息对象
            WechatTemplatedData data = new WechatTemplatedData();
            data.setUrl(url);
            // 就诊人
            data.addKeywordsStr(patient.getName());
            // 项目名称
            data.addKeywordsStr(itemName);
            // 检查时间
            data.addKeywordsStr(itemTime);
            // 检查结果
            data.addKeywordsStr(itemResult);

            // 支付宝消息对象
            AliPayTemplatedData aliPayTemplatedData = new AliPayTemplatedData();
            aliPayTemplatedData.setUrl("subpackages/pat/report-list/index?pid=" + patient.getId());
            // 就诊人
            aliPayTemplatedData.addKeywords(patient.getName());
            // 项目名称
            aliPayTemplatedData.addKeywords(itemName);
            // 备注
            aliPayTemplatedData.addKeywords(formatStr);

            MessageCenterDTO mcDTO  = new MessageCenterDTO();
            mcDTO.setTittle("报告出具提醒");
            mcDTO.setContent(formatStr);
            mcDTO.setPatient(patient);
            mcDTO.setPatientName(patient.getName());
            mcDTO.setDeptName("-");
            mcDTO.setProductId(null);
            mcDTO.setH5Path(AppContext.getInstance(WechatService.class).getAddHospitalUrl(hospital
                    , HospitalSettingsHelper.getString(hospital, HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                    , "subpackages/pat/report-list/index?pid=" + patient.getId()));
            mcDTO.setMiniPath("subpackages/pat/report-list/index?pid=" + patient.getId());

            noticeService.miniProgramNotice(hospital, patient.getUser(), HospitalSettingKey.NOTICE_REPORT_SEND, detail, mcDTO, data, aliPayTemplatedData);
        }
    }

    public void visitorPassMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        log.info("医院：{} 收到陪护证审核通过消息：{}", hospital.getCode(), messageDTO);
        // his的患者唯一id
//        String patientId = messageDTO.getPatientId();
//        List<Patient> patientList = patientRepository.findAll(Specifications.eq("hisPatid", patientId));
//        if (CollectionUtils.isEmpty(patientList)) {
//            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("hisPatId: {" + patientId + "}对应的患者没有找到");
//        }

        Map<String, Object> details = (Map<String, Object>) messageDTO.getMsgDetails();
        String visitor_pass_no = "";
        if (details.containsKey("visitor_pass_no")) {
            visitor_pass_no = (String) details.get("visitor_pass_no");
        }
        List<VisitorPass> visitorPasses = visitorPassRepository.findAll(Specifications.eq("visitor_pass_no", visitor_pass_no));

        if (visitorPasses.isEmpty()) {
            log.info("陪护证号{}对应的数据没有找到", visitor_pass_no);
            return;
        }

        VisitorPass visitorPass = visitorPasses.get(0);

        if (visitorPass.getStatus() == VisitorPass.VisitorPassStatusEnum.UN_AUDIT) {
            Integer pass = (Integer) details.get("pass");
            if (pass == 1) {
                visitorPass.setStatus(VisitorPass.VisitorPassStatusEnum.VALID);
                // 生成陪护证图片
                Upload upload = visitorPassService.generateCertUpload(visitorPass);
                visitorPass.setVisitorPassCertPicture(upload);
            } else {
                visitorPass.setStatus(VisitorPass.VisitorPassStatusEnum.REJECT);
                visitorPass.setRejectReason(details.get("reject_reason").toString());
            }
            visitorPassRepository.save(visitorPass);
        }


    }

    /**
     * 默认为"1"门诊缴费
     * "type": "1", // 1 门诊缴费退款，2 患者服务预约挂线下号，在线下his退号，3 互联网医院的处方在线下退款 4 预约互联网医院号，在线下his退号
     * @param od
     * @return
     */
    private String getMsgDetailType(Object od) {
        String detailString;
        if (od instanceof String) {
            detailString = (String) od;
            return detailString;
        } else {
            detailString = StandardObjectMapper.stringify(od);
        }
        if (detailString != null && detailString.startsWith("{") && detailString.endsWith("}")) {
            Map<String, Object> detail = StandardObjectMapper.readValue(detailString, new TypeReference<>() {});
            return detail.get("type") + "";
        }
        return "1";
    }

    /**
     * 解析his推送消息中的msgDetail
     * @param od
     * @return
     */
    private Map<String, Object> getMsgDetail(Object od) {
        String detailString;
        if (od instanceof String) {
            detailString = (String) od;
        } else {
            detailString = StandardObjectMapper.stringify(od);
        }
        if (detailString != null && detailString.startsWith("{") && detailString.endsWith("}")) {
            return StandardObjectMapper.readValue(detailString, new TypeReference<>() {});
        }
        return null;
    }

    /**
     * 获取msgDetails中的详细类型
     * @param msgDetails 消息
     * @param typeName 类型key
     * @return
     */
    private String getMsgDetailType(Object msgDetails, String typeName) {
        if (StringUtils.isBlank(typeName)) {
            typeName = "type";
        }
        String detailString;
        if (msgDetails instanceof String) {
            detailString = (String) msgDetails;
            return detailString;
        } else {
            detailString = StandardObjectMapper.stringify(msgDetails);
        }
        if (detailString != null && detailString.startsWith("{") && detailString.endsWith("}")) {
            Map<String, Object> detail = StandardObjectMapper.readValue(detailString, new TypeReference<>() {});
            return detail.get(typeName) + "";
        }
        return "1";
    }


    private void refundPatientCharge(Hospital hospital, boolean needRefundAmount, PatientMessageDTO messageDTO) {
        Object od = messageDTO.getMsgDetails();
        String msgDetailType = getMsgDetailType(od);
        switch (msgDetailType) {
            case "2":
                log.info("hospital:" + hospital.getCode() + " 当前his的退款请求是预约挂号退费");
                refundRegisterCharge(hospital, needRefundAmount, messageDTO);
                break;
            case "3":
                log.info("hospital:" + hospital.getCode() + " 当前his的退款请求是线上处方退费");
                refundPrescriptionOrder(hospital, needRefundAmount, messageDTO);
                break;
            case "4":
                log.info("hospital:" + hospital.getCode() + " 当前his的退款请求是线上咨询复诊退费");
//                refundConsultCharge(hospital, messageDTO);
                break;
            case "1":
            default:
                log.info("hospital:" + hospital.getCode() + " 当前his的退款请求是门诊缴费退费");
                refundOutpatientCharge(hospital, needRefundAmount, messageDTO);
        }
    }

    private void refundOutpatientCharge(Hospital hospital, boolean needRefundAmount, PatientMessageDTO messageDTO) {
        Object od = messageDTO.getMsgDetails();
        String detailString = null;
        Map<String, Object> detail = null;
        if (od != null) {
            if (od instanceof String) {
                detailString = (String) od;
            } else {
                detailString = StandardObjectMapper.stringify(od);
            }
        }
        int refundAmount = -1;
        // detailString = null || detailString = {} || detailString = [] 时判断为没有数据，settleId使用operationSn，并且全部退款
        // 就算detailString有数据 也有可能是空字符串 也是使用operationSn，并且全部退款
        String settleId = messageDTO.getOperationSn();
        String cancelSettleId = settleId;
        if (detailString != null && detailString.length() > 2) {
            // TODO: 目前不支持单笔门诊缴费的部分退款
            detail = StandardObjectMapper.readValue(detailString, new TypeReference<>() {});
            if (detail.get("settle_id") != null) {
                cancelSettleId = detail.get("settle_id") + "";
//                refundAmount = Integer.parseInt(detail.get("total_refund_amount") + "");
            }
        }
        if (StringUtils.isBlank(settleId)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("settleId必传");
        }
        AppContext.getInstance(OutpatientService.class).refundOutpatientCharge(hospital.getId(), settleId,
                cancelSettleId, needRefundAmount, InsurancePayMethod.CASH_ONLY, refundAmount, detailString);
    }

    private void cancelAppointment(Hospital hospital, PatientMessageDTO messageDTO) {
        String appointmentId = messageDTO.getOperationSn();
        log.info("预约挂号登记取消通知id{}", appointmentId);
        List<Specification<OfflineOrder>> specs = com.google.common.collect.Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.eq("appointmentId", appointmentId));
        OfflineOrder offlineOrder = offlineOrderRepository.findOne(Specifications.and(specs))
                .orElseThrow(() -> ErrorType.BAD_REQUEST_ERROR.toProblem("订单不存在"));
        // 根据普精卫的业务需求做的改动，只有待支付和支付中的状态才能取消预约
        if (offlineOrder.getStatus() == OfflineOrder.OutPatientStatus.WAIT_PAY
                || offlineOrder.getStatus() == OfflineOrder.OutPatientStatus.PENDING) {
            offlineOrder.setStatus(OfflineOrder.OutPatientStatus.CANCELLED);
            offlineOrderRepository.save(offlineOrder);
            // TODO 微信模版还没有申请,等申请之后需要实现微信通知
        }
    }

    private void refundInpatientCharge(Hospital hospital, PatientMessageDTO messageDTO, boolean needRefundAmount) {
        String outTradeNo = messageDTO.getOperationSn();
        List<Specification<HisInpatientHospitalCharge>> specs = com.google.common.collect.Lists.newArrayList();
        specs.add(Specifications.eq("hospital_id", hospital.getId() + ""));
        specs.add(Specifications.eq("out_trade_no", outTradeNo));
        HisInpatientHospitalCharge charge =
                hisInpatientHospitalChargeRepository.findOne(Specifications.and(specs)).orElseThrow(() -> ErrorType.BAD_REQUEST_ERROR.toProblem(
                        "通过订单号：" + outTradeNo + "没有找到住院预交金订单"));
        String settleId = "";
        int refundAmount = -1;
        Object od = messageDTO.getMsgDetails();
        String detailString = null;
        Map<String, Object> detail = null;
        if (od != null) {
            if (od instanceof String) {
                detailString = (String) od;
            } else {
                detailString = StandardObjectMapper.stringify(od);
            }
        }
        if (detailString == null || detailString.length() <= 2) {
            settleId = messageDTO.getOperationSn();
        } else {
            detail = StandardObjectMapper.readValue(detailString, new TypeReference<>() {});
            settleId = detail.get("settle_id") + "";
            if (StringUtils.isNotBlank(settleId)) {
                List<String> nowSettleId = charge.getSettleId();
                if (nowSettleId.contains(settleId)) {
                    throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("settleId:" + settleId + "不可重复退款");
                }
                refundAmount = Integer.parseInt(detail.get("total_refund_amount") + "");
            }
        }
        AppContext.getInstance(InpatientService.class).inpatientChargeRefunding(hospital,
                outTradeNo, settleId, refundAmount, needRefundAmount);
    }

    /**
     * 线上咨询复诊退款
     * @param hospital
     * @param messageDTO
     */
    private boolean stopConsultOrder(Hospital hospital, PatientMessageDTO messageDTO) {
        String settleId = messageDTO.getOperationSn();
        Optional<OrderExtraInfo> registerOrderOptional = orderExtraInfoRepository.findOneByHospitalAndSettleId(hospital, settleId);
        log.info("his请求预约挂号退费传入的settleId：{}", settleId);
        if (registerOrderOptional.isPresent()) {
            log.info("his请求预约挂号退费传入的settleId正确：{}", settleId);
            Order order = orderRepository.findById(registerOrderOptional.get().getOrderId()).orElse(null);
            if (order == null) {
                log.info("未找到复诊咨询订单orderId：{}", registerOrderOptional.get().getOrderId());
                return false;
            }
            if (order.getEndedDate() != null) {
                log.info("订单已结束，不能退款, orderId=" + order.getId() + ", status=" + order.getStatus().name());
                return false;
            }
            EndOrderVM endOrderVM = new EndOrderVM();
            endOrderVM.setEndType(EndOrderVM.EndType.HOSPITAL_STOP);
            endOrderVM.setReason("医生停诊");
            endOrderVM.setSource(OrderRefundSource.HIS);
            User system = AppContext.getInstance(UserService.class).getSystem();
            AppContext.getInstance(OrderService.class).endOrder(system, null, order, endOrderVM, "医生停诊");
            return true;
        } else {
            return false;
        }
    }

    /**
     * 线下预约挂号退款
     * @param hospital
     * @param needRefundAmount
     * @param messageDTO
     */
    private void refundRegisterCharge(Hospital hospital, boolean needRefundAmount, PatientMessageDTO messageDTO) {
        Object od = messageDTO.getMsgDetails();
        Map<String, Object> detail = getMsgDetail(od);
        String settleId = messageDTO.getOperationSn();
        log.info("his请求预约挂号退费传入的settleId：{}", settleId);

        Optional<OfflineOrder> registerOrderOptional = offlineOrderRepository.findFirstByHospitalAndSettleIdOrderByCreatedDateDesc(hospital, settleId);
        if (registerOrderOptional.isPresent()) {
            OfflineOrder order = registerOrderOptional.get();
            if (OfflineOrder.OutPatientStatus.REFUNDING == order.getStatus() || OfflineOrder.OutPatientStatus.REFUND == order.getStatus()) {
                log.info("当前订单状态为: " + order.getStatus().getName() + ", 不执行后续操作。");
                return;
            }
            log.info("his请求预约挂号退费传入的settleId正确：{}", settleId);
            String cancelSettleId = null;
            if (detail != null) {
                cancelSettleId = (String) detail.get("settle_id");
            }
            AppContext.getInstance(OfflineOrderService.class).refundingAppointmentRegisterCharge(order.getId(),
                    OrderRefundSource.HIS, InsurancePayMethod.CASH_ONLY, null, cancelSettleId);
        }
    }

//    private boolean stopRegisterCharge(Hospital hospital, boolean needRefundAmount, PatientMessageDTO messageDTO) {
//        String settleId = messageDTO.getOperationSn();
//        Optional<OfflineOrder> registerOrderOptional = offlineOrderRepository.findFirstByHospitalAndSettleIdOrderByCreatedDateDesc(hospital, settleId);
//        log.info("his请求预约挂号停诊传入的settleId：{}", settleId);
//        if (registerOrderOptional.isPresent()) {
//            log.info("his请求预约挂号停诊传入的settleId正确：{}", settleId);
//            return AppContext.getInstance(OfflineOrderService.class).stopAppointmentRegisterCharge(registerOrderOptional.get().getId(),
//                    OrderRefundSource.HIS, InsurancePayMethod.CASH_ONLY);
//        } else {
//            return false;
//        }
//    }

    /**
     * 线上处方线下退款
     * @param hospital
     * @param needRefundAmount
     * @param messageDTO
     */
    private void refundPrescriptionOrder(Hospital hospital, boolean needRefundAmount, PatientMessageDTO messageDTO) {
        Object od = messageDTO.getMsgDetails();
        String detailString = null;
        Map<String, Object> detail = null;
        if (od != null) {
            if (od instanceof String) {
                detailString = (String) od;
            } else {
                detailString = StandardObjectMapper.stringify(od);
            }
        }
        int refundAmount = -1;
        // detailString = null || detailString = {} || detailString = [] 时判断为没有数据，settleId使用operationSn，并且全部退款
        // 就算detailString有数据 也有可能是空字符串 也是使用operationSn，并且全部退款
        String settleId = messageDTO.getOperationSn();
        String refundSettleId = null;
        if (detailString != null && detailString.length() > 2) {
            // TODO: 目前不支持单笔线上处方的部分退款
            detail = StandardObjectMapper.readValue(detailString, new TypeReference<>() {});
            if (detail.get("settle_id") != null && StringUtils.isNotBlank(detail.get("settle_id") + "")) {
                refundSettleId = detail.get("settle_id") + "";
//                refundAmount = Integer.parseInt(detail.get("total_refund_amount") + "");
            }
        }
        if (StringUtils.isBlank(settleId)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("settleId必传");
        }
        AppContext.getInstance(PrescriptionService.class).refundPrescriptionOrder(hospital, needRefundAmount, settleId, refundSettleId);
    }

    /**
     * 根据HisPid查询对应的就诊人
     *
     * @param hisPid his的患者唯一id
     * @return 符合条件的就诊人列表
     */
    private List<Patient> getPatientByHisPid(Hospital hospital, String hisPid) {
        List<Specification<Patient>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("electronicMedicCards.hisPatid", hisPid));
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.isTrue("enabled"));
        specs.add(Specifications.distinct());
        List<Patient> patientList = patientRepository.findAll(Specifications.and(specs));
        if (CollectionUtils.isEmpty(patientList)) {
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("hisPatId: {" + hisPid + "}对应的患者没有找到");
        }
        return patientList;
    }

    /**
     * 发送排队就诊消息
     * @param hospital
     * @param messageDTO
     */
    public void waitingDiagnosisSendMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        String patientId = messageDTO.getPatientId();
        List<Patient> patientList = getPatientByHisPid(hospital, patientId);
        Map<String, String> details = (Map<String, String>) messageDTO.getMsgDetails();
        for (Patient patient : patientList) {
            Map<String, String> detail = Maps.newHashMap();
            detail.put("visitTime", details.get("visit_time"));
            detail.put("doctorName", details.get("doctor_name"));
            detail.put("desc", "请前往" + details.get("depart_name") + "科室就诊");
            detail.put("deptName", details.get("depart_name"));
            detail.put("patientName", messageDTO.getPatientName());
            detail.put("path", "/subpackages/pat/queue-list/index?patid=" + patient.getId());
            noticeService.miniProgramNotice(hospital, patient.getUser(), HospitalSettingKey.NOTICE_WAITING_DIAGNOSIS_SEND, detail);
        }
    }

    /**
     * 修饰药品名称，使之保持在3个及以内。
     * @param drugName
     * @return
     */
    private String getAdornDrug(String drugName) {
        String[] medicines = StringUtils.split(drugName, "、");

        // 判断药品种类是否多于3个
        if (medicines.length > 3) {
            // 将前三个药品保留，后面的替换为"等"
            for (int i = 3; i < medicines.length; i++) {
                medicines[i] = "";
            }
            // 将数组转为字符串并去除多余的空白
            drugName = String.join("、", medicines).replaceAll("\\s*,\\s*$", "");
            drugName += "等";
        }
        return drugName;
    }

    private void insurancePaySendMsgHandler(Hospital hospital, PatientMessageDTO messageDTO) {
        Object od = messageDTO.getMsgDetails();
        String msgDetailType = getMsgDetailType(od, "trade_type");
        Map<String, Object> details = (Map<String, Object>) messageDTO.getMsgDetails();
        Date time = TimeUtils.convert(details.get("charge_time") + "");
        HisPayParam hisPayParam = new HisPayParam();
        if (details.get("pay_order_id") != null) {
            hisPayParam.setInternetPayOrderId(details.get("pay_order_id") + "");
        }
        if (details.get("bill_no") != null) {
            hisPayParam.setInternetShBillNo(details.get("bill_no") + "");
        }
        PayStatus payStatus = PayStatus.PAYING;
        switch (details.get("status") + "") {
            case "0":
                payStatus = PayStatus.SUCCESS;
                break;
            case "1":
                payStatus = PayStatus.FAIL;
                break;
            default:
        }
        hisPayParam.setPaySource(PaySource.HIS);
        hisPayParam.setPayTime(time);
        hisPayParam.setPayStatus(payStatus);
        String settleId = messageDTO.getOperationSn();
        String outTradeNo = details.get("out_trade_no") + "";
        switch (msgDetailType) {
            case "0":
                log.info("hospital: " + hospital.getCode() + " 当前his的医保结果推送请求是门诊预约挂号");
                OfflineOrder offlineOrder = offlineOrderRepository.findFirstByHospitalAndSettleIdOrderByCreatedDateDesc(hospital, settleId).orElse(null);
                log.info("his请求医保结果推送门诊预约挂号请求传入的settleId：{}", settleId);
                if (offlineOrder != null && offlineOrder.getSelfFlag() == 0) {
                    log.info("his请求预约挂号退费传入的settleId正确：{}", settleId);
                    AppContext.getInstance(OfflineOrderService.class).payOutpatientRegisterCharge(offlineOrder.getId(), hisPayParam);
                }
                break;
            case "1":
                log.info("hospital: " + hospital.getCode() + " 当前his的医保结果推送请求是门诊缴费结算");
                List<HisOutpatientCharge> charges = hisOutpatientChargeRepository.findAllByHospitalIdAndSettleId(hospital.getId(), settleId);
                if (charges.isEmpty()) {
                    charges = hisOutpatientChargeRepository.findAllByHospitalIdAndHisSerialNo(hospital.getId(), outTradeNo);
                }
                log.info("his请求医保结果推送门诊缴费结算请求传入的settleId：{}", settleId);
                if (!charges.isEmpty()) {
                    log.info("his请求医保结果推送门诊缴费结算请求传入的settleId正确：{}, HisOutpatientCharge数量: {}", settleId, charges.size());
                    HisOutpatientChargeGroup group = charges
                            .stream()
                            .max(Comparator.comparing(UpdatableEntity::getCreatedDate))
                            .get()
                            .getHisOutpatientChargeGroup();
                    AppContext.getInstance(OutpatientService.class).payOutpatientCharge(group.getId(), hisPayParam);
                }
                break;
            default:
                log.info("hospital: " + hospital.getCode() + " 当前his的医保结果推送请求类型是 " + msgDetailType);
        }
    }

    public R<OrderRefundResult> handleOrderRefund(String hospitalCode, OrderRefundDTO refundDTO) {
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(hospitalCode).orElse(null);
        if (hospital == null) {
            return R.fail("医院不存在");
        }
        OrderRefundResult orderRefundResult = new OrderRefundResult();
        String settleId = String.format("\"%s\"", refundDTO.getSettle_id());
        List<WechatOrderRefund> wechatOrderRefunds = wechatOrderRefundRepository.findByRefundDetailNo(settleId, hospital.getId());
        if (CollectionUtils.isEmpty(wechatOrderRefunds)) {
            // 未退款
            orderRefundResult.setStatus("3");
            orderRefundResult.setMessage("未查询到该退款数据");
            return R.success(orderRefundResult);
        }

        WechatOrderRefund wechatOrderRefund = wechatOrderRefunds.stream().findFirst().get();

        // 退款成功
        String status = "0";
        String message = "退款成功";
        if ("PROCESSING".equals(wechatOrderRefund.getStatus())) {
            // 退款中
            status = "2";
            message = "退款中";
        } else if ("ABNORMAL".equals(wechatOrderRefund.getStatus())) {
            // 退款异常
            status = "1";
            message = "退款异常";
        }
        orderRefundResult.setStatus(status);
        orderRefundResult.setMessage(message);
        orderRefundResult.setRefund_amount(Objects.toString(wechatOrderRefund.getAmount(), "0"));
        orderRefundResult.setRefund_time(TimeUtils.convert(wechatOrderRefund.getSuccessTime()));
        return R.success(orderRefundResult);
    }

    /**
     * 生成二维码
     * @param HospitalCode
     * @param request
     * @return
     */
    public String getQRCode(String HospitalCode, HisQRCodeRequest request) {
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(HospitalCode)
                .orElseThrow(ErrorType.NO_HOSPITAL_ERROR::toProblem);
        if ("1".equals(request.getQrCodeType())) {
            //微信小程序
            if ("1".equals(request.getType())) {
                // 门诊缴费
                HospitalPublicPlatform hospitalPublicPlatform = AppContext.getInstance(HospitalPublicPlatformRepository.class)
                        .findAllByHospital(hospital)
                        .stream().filter(u -> u.getPlatformType() == PlatformTypeEnum.MINI
                                && u.getPlatformFor() == HospitalPublicPlatform.PlatformForEnum.PATIENT)
                        .min(Comparator.comparing(HospitalPublicPlatform::getCreatedDate))
                        .orElseThrow(() -> ErrorType.NOT_FOUND_ERROR.toProblem("医院未绑定小程序"));
                String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
                String scene = String.format("t=%s&p=%s&r=%s", request.getType(), request.getPatid(),
                        request.getRegno());
                hisPatNameCache.putName(request.getPatid(), request.getPatName());
                String qrCode = AppContext.getInstance(IHWxClient.class).generateWxMiniUnlimitedQrcode(scene,
                        hospitalPublicPlatform.getAppId(), "pages/common/splash/index", host);
                if (StringUtils.isBlank(qrCode)) {
                    throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("二维码生成失败");
                } else {
                    return qrCode;
                }
            }
            if ("2".equals(request.getType())) {
                // 住院催款单
                HospitalPublicPlatform hospitalPublicPlatform = AppContext.getInstance(HospitalPublicPlatformRepository.class)
                        .findAllByHospital(hospital)
                        .stream().filter(u -> u.getPlatformType() == PlatformTypeEnum.MINI
                                && u.getPlatformFor() == HospitalPublicPlatform.PlatformForEnum.PATIENT)
                        .min(Comparator.comparing(HospitalPublicPlatform::getCreatedDate))
                        .orElseThrow(() -> ErrorType.NOT_FOUND_ERROR.toProblem("医院未绑定小程序"));
                String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
                String scene = String.format("t=%s&p=%s&r=%s", request.getType(), request.getPatid(), request.getRegno());
                hisPatNameCache.putName(request.getPatid(), request.getPatName());
                if (StringUtils.isNotEmpty(request.getAmount())) {
                    hisInPatientAmountCache.putAmount(request.getPatid(), request.getAmount());
                }
                String qrCode = AppContext.getInstance(IHWxClient.class).generateWxMiniUnlimitedQrcode(scene,
                        hospitalPublicPlatform.getAppId(), "pages/common/splash/index", host);
                if (StringUtils.isBlank(qrCode)) {
                    throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("二维码生成失败");
                } else {
                    return qrCode;
                }
            }
            if ("3".equals(request.getType())) {
                // 静态住院预交
                HospitalPublicPlatform hospitalPublicPlatform = AppContext.getInstance(HospitalPublicPlatformRepository.class)
                        .findAllByHospital(hospital)
                        .stream().filter(u -> u.getPlatformType() == PlatformTypeEnum.MINI
                                && u.getPlatformFor() == HospitalPublicPlatform.PlatformForEnum.PATIENT)
                        .min(Comparator.comparing(HospitalPublicPlatform::getCreatedDate))
                        .orElseThrow(() -> ErrorType.NOT_FOUND_ERROR.toProblem("医院未绑定小程序"));
                String host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
                String scene = String.format("t=%s", request.getType());
                String qrCode = AppContext.getInstance(IHWxClient.class).generateWxMiniUnlimitedQrcode(scene,
                        hospitalPublicPlatform.getAppId(), "pages/common/splash/index", host);
                if (StringUtils.isBlank(qrCode)) {
                    throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("二维码生成失败");
                } else {
                    return qrCode;
                }
            }
            throw ErrorType.ILLEGAL_PARAMS.toProblem("业务类型不正确");
        }
        if ("2".equals(request.getQrCodeType())) {
            // 支付宝小程序
            if ("2".equals(request.getType())) {
                // 住院催款单
                String appId = wechatService.getAppId(hospital, PlatformTypeEnum.ALI_PAY_MINI,
                        HospitalPublicPlatform.PlatformForEnum.PATIENT);
                String page = "pages/common/splash/index";
                String params = String.format("t=%s&p=%s&r=%s", request.getType(), request.getPatid(), request.getRegno());
                hisPatNameCache.putName(request.getPatid(), request.getPatName());
                if (StringUtils.isNotEmpty(request.getAmount())) {
                    hisInPatientAmountCache.putAmount(request.getPatid(), request.getAmount());
                }                String describe = "住院预交金";
                return aliPayBusinessService.createQrCode(hospital, appId, page, params, describe);
            }
            if ("3".equals(request.getType())) {
                // 静态住院预交
                String appId = wechatService.getAppId(hospital, PlatformTypeEnum.ALI_PAY_MINI,
                        HospitalPublicPlatform.PlatformForEnum.PATIENT);
                String page = "pages/common/splash/index";
                String params = String.format("t=%s", request.getType());
                String describe = "住院预交金";
                return aliPayBusinessService.createQrCode(hospital, appId, page, params, describe);
            }
            throw ErrorType.ILLEGAL_PARAMS.toProblem("业务类型不正确");
        }
        throw ErrorType.ILLEGAL_PARAMS.toProblem("qrCodeType二维码类型不正确");
    }

    private void process1303Msg(Hospital hospital, String phoneStr){
        String[] phoneArr = StringUtils.split(phoneStr, ",");
        ShortMessageServiceImpl shortMessageService = AppContext.getInstance(ShortMessageServiceImpl.class);
        for (String phone : phoneArr) {
            shortMessageService.sendSystemAlert(hospital, phone);
        }
    }
}
