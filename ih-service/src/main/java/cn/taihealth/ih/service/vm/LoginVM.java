package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.domain.cloud.User;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * View Model object for storing a user's credentials.
 */
public class LoginVM {

    @NotNull(message = "用户名或密码不正确")
    @ApiModelProperty("必填，用户名")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    private String username;

    @NotNull(message = "用户名或密码不正确")
    @ApiModelProperty("密码")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    private String password;

    private Boolean rememberMe = true;

    private User.Source source = User.Source.APP;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean isRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    public User.Source getSource() {
        return source;
    }

    public void setSource(User.Source source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "LoginVM{" +
                "username='" + username + '\'' +
                ", rememberMe=" + rememberMe +
                '}';
    }
}

