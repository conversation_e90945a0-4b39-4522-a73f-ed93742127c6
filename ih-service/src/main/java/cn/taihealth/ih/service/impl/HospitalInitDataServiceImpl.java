package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.Snowflake64.Holder;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.UserRole;
import cn.taihealth.ih.domain.cloud.UserRoleAuthorityRel;
import cn.taihealth.ih.domain.crm.CrmOption;
import cn.taihealth.ih.domain.crm.CrmQuestion;
import cn.taihealth.ih.domain.crm.CrmQuestionTag;
import cn.taihealth.ih.domain.crm.CrmTag;
import cn.taihealth.ih.domain.dict.ExamCategory;
import cn.taihealth.ih.domain.dict.ExamDevice;
import cn.taihealth.ih.domain.dict.ExamItem;
import cn.taihealth.ih.domain.enums.CrmType;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.QuestionType;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.HospitalCreateTask.STAGE;
import cn.taihealth.ih.domain.hospital.HospitalCreateTask.STATUS;
import cn.taihealth.ih.domain.hospital.meddic.*;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.cloud.UserRoleAuthorityRelRepository;
import cn.taihealth.ih.repo.cloud.UserRoleRepository;
import cn.taihealth.ih.repo.crm.CrmOptionRepository;
import cn.taihealth.ih.repo.crm.CrmQuestionRepository;
import cn.taihealth.ih.repo.crm.CrmQuestionTagRepository;
import cn.taihealth.ih.repo.crm.CrmTagRepository;
import cn.taihealth.ih.repo.hospital.*;
import cn.taihealth.ih.service.api.HospitalInitDataService;
import cn.taihealth.ih.service.api.HospitalSettingService;
import cn.taihealth.ih.service.api.InitDataService;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.service.cache.HospitalInitDataStageCache;
import cn.taihealth.ih.service.vm.hospital.HospitalDataInitStageVM;
import cn.taihealth.ih.service.vm.hospital.HospitalDataInitStageVM.Stage;
import com.gitq.jedi.common.util.SlugUtils;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class HospitalInitDataServiceImpl implements HospitalInitDataService {

    private final Logger log = LoggerFactory.getLogger(HospitalInitDataServiceImpl.class);
    private final DiseaseFlagsRepository diseaseFlagsRepository;
    private final DiseaseRepository diseaseRepository;
    private final DiseaseFlagsMiddleRepository diseaseFlagsMiddleRepository;
    private final DeptRepository deptRepository;
    private final DeptDiseaseRepository deptDiseaseRepository;
    private final HospitalRepository hospitalRepository;
    private final MedicalCaseTemplateRepository medicalCaseTemplateRepository;
    private final MedicalCaseContentRepository medicalCaseContentRepository;
    private final HospitalCreateTaskRepository hospitalCreateTaskRepository;
    private final ExamCategoryRepository examCategoryRepository;
    private final ExamItemRepository examItemRepository;
    private final ExamDeviceRepository examDeviceRepository;
    private final CrmQuestionRepository crmQuestionRepository;
    private final CrmQuestionTagRepository crmQuestionTagRepository;
    private final CrmTagRepository crmTagRepository;
    private final CrmOptionRepository crmOptionRepository;
    private final UserRoleRepository userRoleRepository;
    private final UserRoleAuthorityRelRepository userRoleAuthorityRelRepository;
    private final DicCategoryRepository dicCategoryRepository;
    private final DicDosageFormRepository dicDosageFormRepository;
    private final DicDosageUnitRepository dicDosageUnitRepository;
    private final DicPackageUnitRepository dicPackageUnitRepository;
    private final DicMedAdmRouteRepository dicMedAdmRouteRepository;
    private final DicMedAdmFreqRepository dicMedAdmFreqRepository;
    private final DicMedInfoRepository dicMedInfoRepository;
    private final DicMedInfoCategoryRelsRepository dicMedInfoCategoryRelsRepository;
    private final UserCacheFindService userCacheFindService;
    private final HospitalInitDataStageCache hospitalInitDataStageCache;
    private final HospitalSettingService hospitalSettingService;
    private Hospital defaultHospital;
    private CategoryRepository categoryRepository;

    private final HospitalDictionaryRepository hospitalDictionaryRepository;

    private final MessageTemplateRepository messageTemplateRepository;


    public HospitalInitDataServiceImpl(DiseaseFlagsRepository diseaseFlagsRepository,
                                       DiseaseRepository diseaseRepository,
                                       DiseaseFlagsMiddleRepository diseaseFlagsMiddleRepository,
                                       DeptRepository deptRepository,
                                       DeptDiseaseRepository deptDiseaseRepository,
                                       HospitalRepository hospitalRepository,
                                       MedicalCaseTemplateRepository medicalCaseTemplateRepository,
                                       MedicalCaseContentRepository medicalCaseContentRepository,
                                       HospitalCreateTaskRepository hospitalCreateTaskRepository,
                                       ExamCategoryRepository examCategoryRepository,
                                       ExamItemRepository examItemRepository,
                                       ExamDeviceRepository examDeviceRepository,
                                       CrmQuestionRepository crmQuestionRepository,
                                       CrmQuestionTagRepository crmQuestionTagRepository,
                                       CrmTagRepository crmTagRepository,
                                       CrmOptionRepository crmOptionRepository,
                                       UserRoleRepository userRoleRepository,
                                       UserRoleAuthorityRelRepository userRoleAuthorityRelRepository,
                                       DicCategoryRepository dicCategoryRepository,
                                       DicDosageFormRepository dicDosageFormRepository,
                                       DicDosageUnitRepository dicDosageUnitRepository,
                                       DicPackageUnitRepository dicPackageUnitRepository,
                                       DicMedAdmRouteRepository dicMedAdmRouteRepository,
                                       DicMedAdmFreqRepository dicMedAdmFreqRepository,
                                       DicMedInfoRepository dicMedInfoRepository,
                                       DicMedInfoCategoryRelsRepository dicMedInfoCategoryRelsRepository,
                                       HospitalInitDataStageCache hospitalInitDataStageCache,
                                       UserCacheFindService userCacheFindService,
                                       HospitalSettingService hospitalSettingService,
                                       CategoryRepository categoryRepository,
                                       HospitalDictionaryRepository hospitalDictionaryRepository,
                                       MessageTemplateRepository messageTemplateRepository) {
        this.crmQuestionRepository = crmQuestionRepository;
        this.crmQuestionTagRepository = crmQuestionTagRepository;
        this.crmTagRepository = crmTagRepository;
        this.crmOptionRepository = crmOptionRepository;
        this.diseaseFlagsRepository = diseaseFlagsRepository;
        this.diseaseRepository = diseaseRepository;
        this.diseaseFlagsMiddleRepository = diseaseFlagsMiddleRepository;
        this.deptRepository = deptRepository;
        this.deptDiseaseRepository = deptDiseaseRepository;
        this.hospitalRepository = hospitalRepository;
        this.medicalCaseTemplateRepository = medicalCaseTemplateRepository;
        this.medicalCaseContentRepository = medicalCaseContentRepository;
        this.hospitalCreateTaskRepository = hospitalCreateTaskRepository;
        this.examCategoryRepository = examCategoryRepository;
        this.examItemRepository = examItemRepository;
        this.examDeviceRepository = examDeviceRepository;
        this.userRoleRepository = userRoleRepository;
        this.userRoleAuthorityRelRepository = userRoleAuthorityRelRepository;
        this.dicCategoryRepository = dicCategoryRepository;
        this.dicDosageFormRepository = dicDosageFormRepository;
        this.dicDosageUnitRepository = dicDosageUnitRepository;
        this.dicPackageUnitRepository = dicPackageUnitRepository;
        this.dicMedAdmRouteRepository = dicMedAdmRouteRepository;
        this.dicMedAdmFreqRepository = dicMedAdmFreqRepository;
        this.dicMedInfoRepository = dicMedInfoRepository;
        this.dicMedInfoCategoryRelsRepository = dicMedInfoCategoryRelsRepository;
        this.userCacheFindService = userCacheFindService;
        this.hospitalInitDataStageCache = hospitalInitDataStageCache;
        this.hospitalSettingService = hospitalSettingService;
        this.categoryRepository = categoryRepository;
        this.hospitalDictionaryRepository = hospitalDictionaryRepository;
        this.messageTemplateRepository = messageTemplateRepository;
    }


    @Override
    @Transactional
    public void init(Hospital hospital, HospitalCreateTask task) {
        Optional<Hospital> defaultHospitalOptional = hospitalRepository.findOneByCode("default");
        if (!defaultHospitalOptional.isPresent()) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("默认医院不存在，无法初始化数据");
        }
        hospitalInitDataStageCache.flushStageVM(hospital.getCode());

        defaultHospital = defaultHospitalOptional.get();
        HospitalInitCacheData cacheData = new HospitalInitCacheData();
        cacheData.setInitHospital(hospital);

        //doc
        AppContext.getInstance(InitDataService.class).initDocs(hospital);

        // 科室
        initDept(cacheData);
        manageInitCache(hospital, Stage.DEPT, Stage.DISEASE);

        // 疾病
        initDisease(cacheData);
        manageInitCache(hospital, Stage.DISEASE, Stage.MEDICAL_TEMPLATE);

        // 病历模板
        initMedicalCaseContent(cacheData);
        manageInitCache(hospital, Stage.MEDICAL_TEMPLATE, Stage.CRM_QUESTION);

        // 题库管理
//        initCrmQuestion(cacheData);
//        manageInitCache(hospital, Stage.CRM_QUESTION, Stage.MEDICAL);

        // 药品管理
        initMedical(cacheData);
        manageInitCache(hospital, Stage.MEDICAL, Stage.ROLE);

        // 角色管理
        initRole(cacheData);
        manageInitCache(hospital, Stage.ROLE, Stage.SYSTEM_SETTING);

        // 医院参数设置
        initHospitalSetting(cacheData);
        manageInitCache(hospital, Stage.SYSTEM_SETTING, Stage.DICTIONARY);

        // 检查类别
//        initExamCategory();
        // 检查项目
//        initExamItem();
        // 检查设备
//        initExamDevice();

        //  健康测评 健康知识目前先不做

        // 标签设置
        initCategory(cacheData);

        // 医院字典初始化
        initHospitalDictionary(cacheData);
        manageInitCache(hospital, Stage.DICTIONARY, Stage.CRM_QUESTION);

        // 常用消息初始化
        initCommonMessage(cacheData);
        manageInitCache(hospital, Stage.CRM_QUESTION, Stage.FINISHED);

        // 新增一个默认问题
//        initCrmQuestion(hospital);

        task.setStage(STAGE.SEND_URL_PASSWORD);
        task.setStatus(STATUS.IN_PROCESS);
        hospitalCreateTaskRepository.save(task);
    }

    /**
     * 初始化标签
     */
    private void initCategory(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        List<Specification<Category>> specs = Lists.newArrayList();
        List<Specification<Category>> specs1 = Lists.newArrayList();
        specs.add(Specifications.isNotNull("keyword"));
        specs.add(Specifications.eq("hospital", defaultHospital));
        List<Category> categories = categoryRepository.findAll(Specifications.and(specs));
        Sort sort = Sort.by(Sort.Direction.ASC, "orderValue");
        List<Category> all = categoryRepository.findAll(Specifications.and(specs1), sort);
        categories.forEach(c -> {
            Category category = categoryRepository.findOneByKeywordAndHospital(c.getKeyword(),
                                                                               initHospital);
            int i = all.get(all.size() - 1).getOrderValue();
            if (category == null) {
                i++;
                category = new Category();
                category.setColor("#FFFFFF");
                category.setSlug(SlugUtils.slugify(c.getName()));
                category.setTextColor("#000000");
                category.setName(c.getName());
                category.setKeyword(c.getKeyword());
                category.setIsUse(true);
                category.setOrderValue(i);
                category.setHospital(initHospital);
                categoryRepository.save(category);
            }
        });
    }

    /**
     * 初始化角色
     */
    private void initRole(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();

        List<UserRole> roles = userRoleRepository.findAll(Specifications.eq("hospital", defaultHospital));
        roles.forEach(u -> {
            UserRole userRole = userRoleRepository.findOneByCodeAndHospital(u.getCode(), initHospital)
                .orElse(new UserRole());
            if (userRole.isNew()) {
                long code = Holder.INSTANCE.nextId();
                userRole.setCode(code + "");
                userRole.setDescription(u.getDescription());
                userRole.setName(u.getName());
                userRole.setHospital(initHospital);
                userRole.setDoctor(u.isDoctor());
                userRole.setPharmacist(u.isPharmacist());
                UserRole save = userRoleRepository.save(userRole);

                List<UserRoleAuthorityRel> authority = userRoleAuthorityRelRepository.findAllByUserRoleId(u.getId());
                authority.forEach(a -> {
                    UserRoleAuthorityRel userRoleAuthorityRel = new UserRoleAuthorityRel();
                    userRoleAuthorityRel.setUserAuthorityId(a.getUserAuthorityId());
                    userRoleAuthorityRel.setUserRoleId(save.getId());
                    userRoleAuthorityRel.setOrderNum(a.getOrderNum());
                    userRoleAuthorityRelRepository.save(userRoleAuthorityRel);
                });
            }
        });
    }

    /**
     * 初始化病历模板
     */
    private void initMedicalCaseContent(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        // 仅初始化一次
        List<MedicalCaseTemplate> templates = medicalCaseTemplateRepository.findByHospital(initHospital);
        if (CollectionUtils.isNotEmpty(templates)) {
            return;
        }
        List<Specification<MedicalCaseTemplate>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", defaultHospital));
        specs.add(Specifications.isNull("parent"));
        // 获取顶层菜单栏 递归复制
        List<MedicalCaseTemplate> templatesTop = medicalCaseTemplateRepository.findAll(Specifications.and(specs));
        saveMedicalCaseTemplate(null, templatesTop, initHospital);
    }

    private void saveMedicalCaseTemplate(MedicalCaseTemplate parent, List<MedicalCaseTemplate> templates,
                                         Hospital initHospital) {
        templates.forEach(u -> {
            MedicalCaseTemplate template = new MedicalCaseTemplate();
            template.setHospital(initHospital);
            template.setName(u.getName());
            template.setOrderNumber(u.getOrderNumber());
            template.setType(u.getType());
            template.setParent(parent);
            template.setCreatedUser(u.getCreatedUser());
            MedicalCaseTemplate save = medicalCaseTemplateRepository.save(template);
            MedicalCaseContent newContent = getNewContentFromTemplate(u);
            if (newContent != null) {
                newContent.setTemplate(save);
                medicalCaseContentRepository.save(newContent);
            }
            saveMedicalCaseTemplate(save, u.getTemplates(), initHospital);
        });


    }

    /**
     * 从病历模板中获取一个新的content
     *
     * @param template 病历模板
     * @return MedicalCaseContent content
     */
    private MedicalCaseContent getNewContentFromTemplate(MedicalCaseTemplate template) {
        Optional<MedicalCaseContent> OldContent = medicalCaseContentRepository.findByTemplate(template);

        if (OldContent.isPresent()) {
            MedicalCaseContent old = OldContent.get();
            MedicalCaseContent newContent = new MedicalCaseContent();
            newContent.setAllergiesHistory(old.getAllergiesHistory());
            newContent.setChecking(old.getChecking());
            newContent.setDiagnosis(old.getDiagnosis());
            newContent.setNowMedicalHistory(old.getNowMedicalHistory());
            newContent.setOldMedicalHistory(old.getOldMedicalHistory());
            newContent.setSelfSpeak(old.getSelfSpeak());
            newContent.setContent(old.getContent());
            return newContent;
        } else {
            return null;
        }

    }

    /**
     * 初始化检查设备
     */
    private void initExamDevice(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, ExamCategory> categoryMap = cacheData.getCategoryMap();
        Map<Long, OfflineDept> offlineDeptMap = cacheData.getOfflineDeptMap();
        List<ExamDevice> examDeviceList = examDeviceRepository.findAll(Specifications.eq("hospital", defaultHospital));
        examDeviceList.forEach(u -> {
            ExamDevice examDevice = examDeviceRepository.findOneByHospitalAndCode(initHospital, u.getCode())
                .orElse(new ExamDevice());
            if (examDevice.isNew()) {
                ExamCategory category = null;
                if (u.getCategory() != null) {
                    category = categoryMap.get(u.getCategory().getId());
                }
                OfflineDept offlineDept = null;
                if (u.getOfflineDept() != null) {
                    offlineDept = offlineDeptMap.get(u.getOfflineDept().getId());
                }
                examDevice.setOfflineDept(offlineDept);
                examDevice.setCategory(category);
                examDevice.setAddress(u.getAddress());
                examDevice.setCode(u.getCode());
                examDevice.setHospital(initHospital);
                examDevice.setEnabled(u.isEnabled());
                examDeviceRepository.save(examDevice);
            }
        });
    }

    /**
     * 初始化检查项目
     */
    private void initExamItem(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, ExamCategory> categoryMap = cacheData.getCategoryMap();
        Map<Long, OfflineDept> offlineDeptMap = cacheData.getOfflineDeptMap();

        List<ExamItem> defaultItems = examItemRepository.findAll(Specifications.eq("hospital", defaultHospital));
        defaultItems.forEach(u -> {
            ExamItem examItem = examItemRepository.findAllByHospitalAndCode(initHospital, u.getCode()).stream()
                .findFirst()
                .orElse(new ExamItem());
            if (examItem.isNew()) {
                ExamCategory category = null;
                if (u.getCategory() != null) {
                    category = categoryMap.get(u.getCategory().getId());
                }
                OfflineDept offlineDept = null;
                if (u.getOfflineDept() != null) {
                    offlineDept = offlineDeptMap.get(u.getOfflineDept().getId());
                }
                examItem.setOfflineDept(offlineDept);
                examItem.setCategory(category);
                examItem.setCode(u.getCode());
                examItem.setName(u.getName());
                examItem.setInputCode(u.getInputCode());
                examItem.setPrice(u.getPrice());
                examItem.setInsurance(u.isInsurance());
                examItem.setNotice(u.getNotice());
                examItem.setTimePeriod(u.getTimePeriod());
                examItem.setTimes(u.getTimes());
                examItem.setHospital(initHospital);

                examItemRepository.save(examItem);
            }
        });

    }

    /**
     * 初始化检查类别
     */
    private void initExamCategory(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, ExamCategory> categoryMap = cacheData.getCategoryMap();
        List<ExamCategory> defaultCategories = examCategoryRepository
            .findAll(Specifications.eq("hospital", defaultHospital));
        defaultCategories.forEach(u -> {
            ExamCategory category = examCategoryRepository
                .findAllByHospitalAndName(initHospital, u.getName())
                .stream().findFirst().orElseGet(ExamCategory::new);
            category.setHospital(initHospital);
            category.setName(u.getName());
            category.setNotice(u.getNotice());
            category.setAppointmentDays(u.getAppointmentDays());
            ExamCategory save = examCategoryRepository.save(category);
            categoryMap.put(u.getId(), save);
        });
    }


    /**
     * 初始化科室
     */
    private void initDept(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Dept> deptMap = cacheData.getDeptMap();
        List<Dept> deptList = deptRepository.findByHospital(defaultHospital);
        deptList.forEach(u -> {
            Dept dept = deptRepository.findOneByHospitalAndDeptCode(initHospital, u.getDeptCode()).orElse(new Dept());
            if (dept.isNew()) {
                dept = copyDeptWithoutId(u);
                dept.setHospital(initHospital);
                Dept save = deptRepository.save(dept);
                deptMap.put(u.getId(), save);
            }
        });
        cacheData.setDeptMap(deptMap);
    }

    /**
     * 初始化疾病
     */
    private void initDisease(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Disease> diseaseMap = cacheData.getDiseaseMap();
        Map<Long, DiseaseFlags> diseaseFlagsMap = cacheData.getDiseaseFlagsMap();
        Map<Long, Dept> deptMap = cacheData.getDeptMap();

        List<Disease> diseases = diseaseRepository.findAll(Specifications.eq("hospital", defaultHospital));
        List<DeptDisease> deptDiseases = Lists.newArrayList();
        diseases.forEach(u -> {
            Disease disease = diseaseRepository.findOneByHospitalAndDiseaseName(initHospital, u.getDiseaseName())
                .orElse(new Disease());
            if (disease.isNew()) {
                disease.setEmergencyLevel(u.getEmergencyLevel());
                disease.setDiseaseName(u.getDiseaseName());
                disease.setQuickSelection(u.getQuickSelection());
                disease.setCode(u.getCode());
                disease.setIcd10Code(u.getIcd10Code());
                disease.setIcd11Code(u.getIcd11Code());
                disease.setCode(u.getCode());
                disease.setGb95Code(u.getGb95Code());
                disease.setHospital(initHospital);
                Disease save = diseaseRepository.save(disease);
                diseaseMap.put(u.getId(), save);
                deptDiseases.addAll(u.getDepts());

                List<DiseaseFlagsMiddle> flagsMiddles = u.getFlagsMiddles();
                List<DiseaseFlags> diseaseFlagsList = diseaseFlagsRepository
                    .findAll(Specifications.eq("hospital", defaultHospital));
                diseaseFlagsList.forEach(f -> {
                    DiseaseFlags flag = diseaseFlagsRepository.findByNameAndHospital(f.getName(), initHospital)
                        .orElse(new DiseaseFlags());
                    if (flag.isNew()) {
                        flag.setName(f.getName());
                        flag.setHospital(initHospital);
                        DiseaseFlags saveFlag = diseaseFlagsRepository.save(flag);
                        diseaseFlagsMap.put(f.getId(), saveFlag);
                    }
                });
                flagsMiddles.forEach(f -> {
                    DiseaseFlagsMiddle diseaseFlagsMiddle = new DiseaseFlagsMiddle();
                    diseaseFlagsMiddle.setDiseaseFlags(diseaseFlagsMap.get(f.getDiseaseFlags().getId()));
                    diseaseFlagsMiddle.setDisease(save);
                    diseaseFlagsMiddleRepository.save(diseaseFlagsMiddle);
                });
            }
        });

        deptDiseases.forEach(u -> {
            DeptDisease deptDisease = new DeptDisease();
            deptDisease.setDept(deptMap.get(u.getDept().getId()));
            deptDisease.setDisease(diseaseMap.get(u.getDisease().getId()));
            deptDiseaseRepository.save(deptDisease);
        });
        cacheData.setDiseaseMap(diseaseMap);
        cacheData.setDiseaseFlagsMap(diseaseFlagsMap);
    }

    /**
     * 获取一个新的科室
     *
     * @param defaultDept
     * @return
     */
    private Dept copyDeptWithoutId(Dept defaultDept) {
        Dept dept = new Dept();
        dept.setCanRegister(defaultDept.isCanRegister());
        dept.setDeptCode(defaultDept.getDeptCode());
        dept.setDeptName(defaultDept.getDeptName());
        dept.setGuidanceNumber(defaultDept.getGuidanceNumber());
        dept.setEmergencyFee(defaultDept.getEmergencyFee());
        dept.setDeptType(defaultDept.getDeptType());
        dept.setEnabled(defaultDept.getEnabled());
        dept.setIntroduction(defaultDept.getIntroduction());
        dept.setOfflineGeneralFee(defaultDept.getOfflineGeneralFee());
        dept.setTreatmentDuration(defaultDept.getTreatmentDuration());

        dept.setReturnVisitFee(defaultDept.getReturnVisitFee());
        dept.setVideoReturnVisitFee(defaultDept.getVideoReturnVisitFee());
        dept.setPhoneReturnVisitFee(defaultDept.getPhoneReturnVisitFee());
        dept.setGraphicConsultFee(defaultDept.getGraphicConsultFee());
        dept.setVideoConsultFee(defaultDept.getVideoConsultFee());
        dept.setReturnVisitEnabled(defaultDept.getReturnVisitEnabled());
        dept.setVideoReturnVisitEnabled(defaultDept.getVideoReturnVisitEnabled());
        dept.setPhoneReturnVisitEnabled(defaultDept.getPhoneReturnVisitEnabled());
        dept.setGraphicConsultEnabled(defaultDept.getGraphicConsultEnabled());
        dept.setVideoConsultEnabled(defaultDept.getVideoConsultEnabled());
        return dept;
    }

    private void initCrmQuestion(Hospital hospital) {
        CrmQuestion crmQuestion = new CrmQuestion();
        crmQuestion.setContent("患者姓名");
        crmQuestion.setCrmType(CrmType.OTHER);
        crmQuestion.setQuestionType(QuestionType.ANSWER);
        crmQuestion.setCreator(AppContext.getInstance(UserService.class).getSystem());
        crmQuestion.setHospital(hospital);
        crmQuestion.setDefaulted(true);
        crmQuestionRepository.save(crmQuestion);
    }

    /**
     * 初始化随访题库
     */
    private void initCrmQuestion(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();

        Map<Long, CrmTag> tagMap = Maps.newHashMap();
        // 题库管理同步
        Optional<User> optionalUser = userCacheFindService.findOneByUsername("Admin_" + initHospital.getCode());
        User user;
        if (optionalUser.isPresent()) {
            user = optionalUser.get();
        } else {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("没有找到医院管理员，请检查管理员是否创建");
        }
        // tag
        List<CrmTag> tags = crmTagRepository.findAll(Specifications.eq("hospital", defaultHospital));
        tags.forEach(u -> {
            CrmTag crmTag = crmTagRepository.findAll(
                    Specifications.and(Specifications.eq("hospital", initHospital), Specifications.eq("name", u.getName())))
                .stream().findFirst().orElse(new CrmTag());
            if (crmTag.isNew()) {
                crmTag.setDescription(u.getDescription());
                crmTag.setHospital(initHospital);
                crmTag.setName(u.getName());
                crmTag = crmTagRepository.save(crmTag);
                tagMap.put(u.getId(), crmTag);
            }
        });
        // question
        List<Specification<CrmQuestion>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", defaultHospital));
        specs.add(Specifications.isTrue("enabled"));
        List<CrmQuestion> questions = crmQuestionRepository.findAll(Specifications.and(specs));

        questions.forEach(u -> {
            CrmQuestion question = crmQuestionRepository.findAllByHospitalAndContent(initHospital, u.getContent())
                .stream().findFirst().orElse(new CrmQuestion());
            if (question.isNew()) {
                question.setClinicType(u.getClinicType());
                question.setContent(u.getContent());
                question.setCrmType(u.getCrmType());
                question.setDescription(u.getDescription());
                question.setQuestionType(u.getQuestionType());
                question.setUsed(u.isUsed());
                question.setUsedCount(u.getUsedCount());
                question.setEnabled(u.isEnabled());
                question.setCreator(user);

                question = crmQuestionRepository.save(question);

                List<CrmTag> crmTagList = u.getTags();
                CrmQuestion finalQuestion = question;
                crmTagList.forEach(tag -> {
                    CrmQuestionTag crmQuestionTag = new CrmQuestionTag();
                    crmQuestionTag.setTag(tagMap.get(tag));
                    crmQuestionTag.setQuestion(finalQuestion);
                    crmQuestionTagRepository.save(crmQuestionTag);
                });
                // 选项
                List<CrmOption> options = u.getOptions();
                options.forEach(o -> {
                    CrmOption crmOption = new CrmOption();
                    crmOption.setContent(o.getContent());
                    crmOption.setOptionOrder(o.getOptionOrder());
                    crmOption.setScore(o.getScore());
                    crmOption.setQuestion(finalQuestion);
                    crmOptionRepository.save(crmOption);
                });
            }
        });
    }

    /**
     * 导入药品字典
     */
    private void initMedical(HospitalInitCacheData cacheData) {
        // 分类
        initMedicalType(cacheData);
        // "剂型":
        initDosageForm(cacheData);
        // "计量单位":
        initDicDosageUnity(cacheData);
        // "包装单位":
        initDicPackageUnit(cacheData);
        // "给药途径":
        initDicMedAdmRoute(cacheData);
        // "给药频次":
        initDicMedAdmFreq(cacheData);
        // "药品字典":
        initDicMedInfo(cacheData);


    }

    /**
     * DicCategory 药品分类（按性质）
     */
    private void initMedicalType(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Long> dicCategoryIdMap = cacheData.getDicCategoryIdMap();
        List<DicCategory> dicCategories = dicCategoryRepository.findAll(Specifications.eq("hospital", defaultHospital));
        dicCategories.forEach(u -> {
            DicCategory dicCategory = dicCategoryRepository
                .findOneByHospitalAndCategoryName(initHospital, u.getCategoryName())
                .orElse(new DicCategory());
            if (dicCategory.isNew()) {
                dicCategory.setCategoryName(u.getCategoryName());
                dicCategory.setCategoryCname(u.getCategoryCname());
                dicCategory.setHospital(initHospital);
                dicCategory = dicCategoryRepository.save(dicCategory);
                dicCategoryIdMap.put(u.getId(), dicCategory.getId());
            }
        });
        cacheData.setDicCategoryIdMap(dicCategoryIdMap);
    }

    /**
     * DicDosageForm 剂型
     */
    private void initDosageForm(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Long> dicDosageFormIdMap = cacheData.getDicDosageFormIdMap();

        List<DicDosageForm> dicDosageForms = dicDosageFormRepository
            .findAll(Specifications.eq("hospital", defaultHospital));
        dicDosageForms.forEach(u -> {
            DicDosageForm dosageForm = dicDosageFormRepository
                .findOneByHospitalAndFormName(initHospital, u.getFormName())
                .orElse(new DicDosageForm());
            if (dosageForm.isNew()) {
                dosageForm.setFormName(u.getFormName());
                dosageForm.setFormCname(u.getFormCname());
                dosageForm.setHospital(initHospital);
                dosageForm = dicDosageFormRepository.save(dosageForm);
                dicDosageFormIdMap.put(u.getId(), dosageForm.getId());
            }
        });
        cacheData.setDicDosageFormIdMap(dicDosageFormIdMap);
    }

    /**
     * DicDosageUnit 计量单位
     */
    private void initDicDosageUnity(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Long> dicDosageUnitIdMap = cacheData.getDicDosageUnitIdMap();
        List<DicDosageUnit> dicDosageUnits = dicDosageUnitRepository
            .findAll(Specifications.eq("hospital", defaultHospital));
        dicDosageUnits.forEach(u -> {
            DicDosageUnit dosageUnit = dicDosageUnitRepository
                .findOneByHospitalAndUnitName(initHospital, u.getUnitName()).orElse(new DicDosageUnit());
            if (dosageUnit.isNew()) {
                dosageUnit.setUnitName(u.getUnitName());
                dosageUnit.setUnitCname(u.getUnitCname());
                dosageUnit.setHospital(initHospital);
                dosageUnit = dicDosageUnitRepository.save(dosageUnit);
                dicDosageUnitIdMap.put(u.getId(), dosageUnit.getId());
            }
        });
        cacheData.setDicDosageUnitIdMap(dicDosageUnitIdMap);

    }

    /**
     * DicPackageUnit 包装单位
     */
    private void initDicPackageUnit(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Long> dicPackageUnitIdMap = cacheData.getDicPackageUnitIdMap();
        List<DicPackageUnit> dicPackageUnits = dicPackageUnitRepository
            .findAll(Specifications.eq("hospital", defaultHospital));
        dicPackageUnits.forEach(u -> {
            DicPackageUnit packageUnit = dicPackageUnitRepository
                .findOneByHospitalAndUnitName(initHospital, u.getUnitName()).orElse(new DicPackageUnit());
            if (packageUnit.isNew()) {
                packageUnit.setUnitName(u.getUnitName());
                packageUnit.setUnitCname(u.getUnitCname());
                packageUnit.setHospital(initHospital);
                packageUnit = dicPackageUnitRepository.save(packageUnit);
                dicPackageUnitIdMap.put(u.getId(), packageUnit.getId());
            }
        });
        cacheData.setDicPackageUnitIdMap(dicPackageUnitIdMap);
    }

    /**
     * DicMedAdmRoute 给药途径 (代码	名称)
     */
    private void initDicMedAdmRoute(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Long> dicMedAdmRouteIdMap = cacheData.getDicMedAdmRouteIdMap();
        List<DicMedAdmRoute> dicMedAdmRoutes = dicMedAdmRouteRepository
            .findAll(Specifications.eq("hospital", defaultHospital));
        dicMedAdmRoutes.forEach(u -> {
            DicMedAdmRoute medAdmRoute = dicMedAdmRouteRepository
                .findOneByHospitalAndRouteCode(initHospital, u.getRouteCode()).orElse(new DicMedAdmRoute());
            if (medAdmRoute.isNew()) {
                medAdmRoute.setRouteCode(u.getRouteCode());
                medAdmRoute.setRouteName(u.getRouteName());
                medAdmRoute.setHospital(initHospital);
                medAdmRoute = dicMedAdmRouteRepository.save(medAdmRoute);
                dicMedAdmRouteIdMap.put(u.getId(), medAdmRoute.getId());
            }
        });
        cacheData.setDicMedAdmRouteIdMap(dicMedAdmRouteIdMap);
    }

    /**
     * DicMedAdmFreq 给药频次 (代码	名称)
     */
    private void initDicMedAdmFreq(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Long> dicMedAdmFreqIdMap = cacheData.getDicMedAdmFreqIdMap();
        List<DicMedAdmFreq> dicMedAdmFreqs = dicMedAdmFreqRepository
            .findAll(Specifications.eq("hospital", defaultHospital));
        dicMedAdmFreqs.forEach(u -> {
            DicMedAdmFreq medAdmFreq = dicMedAdmFreqRepository
                .findOneByHospitalAndFrequencyCode(initHospital, u.getFrequencyCode())
                .orElse(new DicMedAdmFreq());

            if (medAdmFreq.isNew()) {
                medAdmFreq.setFrequencyCode(u.getFrequencyCode());
                medAdmFreq.setFrequencyDesc(u.getFrequencyDesc());
                medAdmFreq.setAdministrationTime(u.getAdministrationTime());
                medAdmFreq.setHospital(initHospital);
                medAdmFreq = dicMedAdmFreqRepository.save(medAdmFreq);
                dicMedAdmFreqIdMap.put(u.getId(), medAdmFreq.getId());
            }
        });
        cacheData.setDicMedAdmFreqIdMap(dicMedAdmFreqIdMap);
    }

    /**
     * DicMedAdmFreq 药品字典 (代码	通用名	通用名首拼	通用名英文	商品名	商品名首拼	商品名英文	批准文号	剂量规格	计量单位	剂型	包装单位	包装规格	生产厂家	分类	是否抗菌药物	抗菌药物分级	零售价	采购价	销售价	会员价	库存下限	库存上限	是否医保	是否处方	是否特价	条形码	渠道来源	默认用药途径	默认用药频次	是否精麻毒放)
     */
    private void initDicMedInfo(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, Long> dicDosageFormIdMap = cacheData.getDicDosageFormIdMap();
        Map<Long, Long> dicMedAdmFreqIdMap = cacheData.getDicMedAdmFreqIdMap();
        Map<Long, Long> dicPackageUnitIdMap = cacheData.getDicPackageUnitIdMap();
        Map<Long, Long> dicMedAdmRouteIdMap = cacheData.getDicMedAdmRouteIdMap();
        Map<Long, Long> dicDosageUnitIdMap = cacheData.getDicDosageUnitIdMap();
        Map<Long, Long> dicCategoryIdMap = cacheData.getDicCategoryIdMap();
        List<DicMedInfo> dicMedInfos = dicMedInfoRepository.findAll(Specifications.eq("hospital", defaultHospital));
        dicMedInfos.forEach(u -> {
            DicMedInfo medInfo = dicMedInfoRepository
                .findOneByHospitalAndNmpaNumberAndChannel(initHospital, u.getNmpaNumber(), u.getChannel())
                .orElse(new DicMedInfo());
            if (medInfo.isNew()) {
                medInfo.setHospital(initHospital);
                medInfo.setNmpaNumber(u.getNmpaNumber());
                medInfo.setChannel(u.getChannel());
                medInfo.setDosageFormId(dicDosageFormIdMap.get(u.getDosageFormId()));
                medInfo.setDicDosageUnitId(dicDosageUnitIdMap.get(u.getDicDosageUnitId()));
                medInfo.setDicPackageUnitId(dicPackageUnitIdMap.get(u.getDicPackageUnitId()));
                medInfo.setFrequencycodeDefault(dicMedAdmFreqIdMap.get(u.getFrequencycodeDefault()));
                medInfo.setRoutecodeDefault(dicMedAdmRouteIdMap.get(u.getRoutecodeDefault()));
                medInfo.setCode(u.getCode());
                medInfo.setGenericName(u.getGenericName());
                medInfo.setGenericNameInputCode(u.getGenericNameInputCode());
                medInfo.setGenericNameEnglish(u.getGenericNameEnglish());
                medInfo.setTradeName(u.getTradeName());
                medInfo.setTradeNameInputCode(u.getTradeNameInputCode());
                medInfo.setTradeNameEnglish(u.getTradeNameEnglish());
                medInfo.setDosageSpec(u.getDosageSpec());
                medInfo.setPackagingSpec(u.getPackagingSpec());
                medInfo.setManufacturer(u.getManufacturer());
                medInfo.setIsAntimicrobial(u.getIsAntimicrobial());
                medInfo.setAntimicrobialGrade(u.getAntimicrobialGrade());
                medInfo.setIsInsurance(u.getIsInsurance());
                medInfo.setIsPrescription(u.getIsPrescription());
                medInfo.setIsSpecial(u.getIsSpecial());
                medInfo.setBarCode(u.getBarCode());
                medInfo.setRetailPrice(u.getRetailPrice());
                medInfo.setSourcingPrice(u.getSourcingPrice());
                medInfo.setSalePrice(u.getSalePrice());
                medInfo.setMembershipPrice(u.getMembershipPrice());
                medInfo.setStockLowerLimit(u.getStockLowerLimit());
                medInfo.setStockUpperLimit(u.getStockUpperLimit());
                medInfo.setIsSpecialPrice(u.getIsSpecialPrice());
                medInfo.setChannel(u.getChannel());
                medInfo.setHospital(initHospital);

                medInfo = dicMedInfoRepository.save(medInfo);

                List<DicMedInfoCategoryRels> dicMedInfoCategoryRels = dicMedInfoCategoryRelsRepository
                    .findByMedinfoId(u.getId());
                DicMedInfo finalMedInfo = medInfo;
                dicMedInfoCategoryRels.forEach(d -> {
                    DicMedInfoCategoryRels dmcr = new DicMedInfoCategoryRels();
                    dmcr.setCategoryId(dicCategoryIdMap.get(d.getCategoryId()));
                    dmcr.setMedinfoId(finalMedInfo.getId());
                    dicMedInfoCategoryRelsRepository.save(dmcr);
                });
            }
        });
    }

    private void initHospitalSetting(HospitalInitCacheData cacheData) {
        HospitalSettingKey[] keys = HospitalSettingKey.values();
        Hospital initHospital = cacheData.getInitHospital();

        for (HospitalSettingKey key : keys) {
            Object value = hospitalSettingService.getValue(defaultHospital, key);
            hospitalSettingService.save(initHospital, key, value);
        }

    }

    private void initHospitalDictionary(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, HospitalDictionary> dictionaryMap = cacheData.getDictionaryMap();
        List<HospitalDictionary> dictionaryList = hospitalDictionaryRepository.findAllByHospitalAndParent(defaultHospital, null);
        for (HospitalDictionary dictTemp : dictionaryList) {
            HospitalDictionary dictionary = hospitalDictionaryRepository
                .findFirstByHospitalAndParentAndCode(initHospital, null, dictTemp.getCode())
                .orElse(new HospitalDictionary());
            if (dictionary.isNew()) {
                dictionary.setHospital(initHospital);
                dictionary.setEnabled(dictTemp.getEnabled());
                dictionary.setParent(null);
                dictionary.setName(dictTemp.getName());
                dictionary.setCode(dictTemp.getCode());
                HospitalDictionary save = hospitalDictionaryRepository.saveAndFlush(dictionary);
                dictionaryMap.put(save.getId(), save);
                List<HospitalDictionary> dictTempChildren = hospitalDictionaryRepository.findAllByHospitalAndParent(defaultHospital, dictTemp);
                for (HospitalDictionary dictTempChild : dictTempChildren) {
                    HospitalDictionary dictionaryChild = new HospitalDictionary();
                    dictionaryChild.setHospital(initHospital);
                    dictionaryChild.setEnabled(dictTempChild.getEnabled());
                    dictionaryChild.setParent(save);
                    dictionaryChild.setName(dictTempChild.getName());
                    dictionaryChild.setCode(dictTempChild.getCode());
                    HospitalDictionary saveChild = hospitalDictionaryRepository.save(dictionaryChild);
                    dictionaryMap.put(saveChild.getId(), saveChild);
                }
            }

        }
        cacheData.setDictionaryMap(dictionaryMap);
    }

    private void initCommonMessage(HospitalInitCacheData cacheData) {
        Hospital initHospital = cacheData.getInitHospital();
        Map<Long, MessageTemplate> messageTemplateMap = cacheData.getCommonMsgMap();
        List<MessageTemplate> messageTemplates = messageTemplateRepository.findAllByHospital(defaultHospital);
        messageTemplates.forEach(t -> {
            MessageTemplate messageTemplate = messageTemplateRepository
                .findFirstByHospitalAndContent(initHospital, t.getContent()).orElse(new MessageTemplate());
            if (messageTemplate.isNew()) {
                messageTemplate.setContent(t.getContent());
                messageTemplate.setHospital(initHospital);
                messageTemplate.setOrderNumber(t.getOrderNumber());
                messageTemplate.setCreatedUser(t.getCreatedUser());
                messageTemplate.setUpdatedUser(t.getUpdatedUser());
                MessageTemplate save = messageTemplateRepository.save(messageTemplate);
                messageTemplateMap.put(save.getId(), save);
            }
        });
        cacheData.setCommonMsgMap(messageTemplateMap);
    }

    private void manageInitCache(Hospital hospital, HospitalDataInitStageVM.Stage nowStage,
                                 HospitalDataInitStageVM.Stage nextStage) {
        HospitalDataInitStageVM stageVM = hospitalInitDataStageCache.getStageVM(hospital.getCode());
        stageVM.getFinishedStages().add(nowStage);
        stageVM.setRunningStage(nextStage);
        hospitalInitDataStageCache.putStageVM(hospital.getCode(), stageVM);
    }
}
