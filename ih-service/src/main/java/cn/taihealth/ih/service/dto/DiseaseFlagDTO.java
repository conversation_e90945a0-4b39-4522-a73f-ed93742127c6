package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.hospital.DiseaseFlags;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
@Accessors(chain = true)
public class DiseaseFlagDTO extends AbstractEntityDTO {

    @ApiModelProperty(value = "分组名称最长255个字符", required = true)
    @NotBlank
    private String name;

    public DiseaseFlagDTO(DiseaseFlags diseaseFlags) {
        super(diseaseFlags);
        this.name = diseaseFlags.getName();
    }

    public DiseaseFlags toEntity(DiseaseFlags diseaseFlags) {
        diseaseFlags.setId(this.getId());
        diseaseFlags.setName(this.name);
        return diseaseFlags;
    }

}
