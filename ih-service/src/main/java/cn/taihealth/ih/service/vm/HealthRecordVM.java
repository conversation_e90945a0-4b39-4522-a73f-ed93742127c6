package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.service.dto.HealthRecordDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 *
 */
public class HealthRecordVM extends HealthRecordDTO {

    private List<UploadVM> bloodTypeImages = Lists.newArrayList();

    private List<UploadVM> allergyImages = Lists.newArrayList();

    private List<UploadVM> foodAllergyImages = Lists.newArrayList();

    private List<UploadVM> occupationalDiseasesImages = Lists.newArrayList();

    private List<UploadVM> geneticDiseasesImages = Lists.newArrayList();

    private List<UploadVM> medicationsImages = Lists.newArrayList();

    private List<UploadVM> medicalConditionsImages = Lists.newArrayList();

    @ApiModelProperty("疾病标签，仅用于查询展示，修改时此值无效，有专门修改接口")
    private List<CategoryVM> categories = Lists.newArrayList();

    public List<UploadVM> getBloodTypeImages() {
        return bloodTypeImages;
    }

    public void setBloodTypeImages(List<UploadVM> bloodTypeImages) {
        this.bloodTypeImages = bloodTypeImages;
    }

    public List<UploadVM> getAllergyImages() {
        return allergyImages;
    }

    public void setAllergyImages(List<UploadVM> allergyImages) {
        this.allergyImages = allergyImages;
    }

    public List<UploadVM> getFoodAllergyImages() {
        return foodAllergyImages;
    }

    public void setFoodAllergyImages(List<UploadVM> foodAllergyImages) {
        this.foodAllergyImages = foodAllergyImages;
    }

    public List<UploadVM> getOccupationalDiseasesImages() {
        return occupationalDiseasesImages;
    }

    public void setOccupationalDiseasesImages(List<UploadVM> occupationalDiseasesImages) {
        this.occupationalDiseasesImages = occupationalDiseasesImages;
    }

    public List<UploadVM> getGeneticDiseasesImages() {
        return geneticDiseasesImages;
    }

    public void setGeneticDiseasesImages(List<UploadVM> geneticDiseasesImages) {
        this.geneticDiseasesImages = geneticDiseasesImages;
    }

    public List<UploadVM> getMedicationsImages() {
        return medicationsImages;
    }

    public void setMedicationsImages(List<UploadVM> medicationsImages) {
        this.medicationsImages = medicationsImages;
    }

    public List<UploadVM> getMedicalConditionsImages() {
        return medicalConditionsImages;
    }

    public void setMedicalConditionsImages(List<UploadVM> medicalConditionsImages) {
        this.medicalConditionsImages = medicalConditionsImages;
    }

    public List<CategoryVM> getCategories() {
        return categories;
    }

    public void setCategories(List<CategoryVM> categories) {
        this.categories = categories;
    }
}
