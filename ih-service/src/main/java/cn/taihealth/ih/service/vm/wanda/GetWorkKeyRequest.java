package cn.taihealth.ih.service.vm.wanda;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GetWorkKeyRequest {

    /**
     * 版本号  必填
     */
    private String version = "V3.0";    // 固定值 V3.0, (payService中, 默认值 V3.0,上海医保支付时填 V2.0,国家局医保支付时填 V3.0)

    /**
     * 商户应用号  必填
     */
    private String appid;    // 支付平台分配的固定值

    /**
     * 商户应用号绑定唯一密钥参数
     */
    private String appsecret;

}
