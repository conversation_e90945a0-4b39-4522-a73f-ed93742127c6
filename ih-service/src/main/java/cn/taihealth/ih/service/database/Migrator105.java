package cn.taihealth.ih.service.database;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.his.HisInpatientHospitalCharge;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.his.HisInpatientHospitalChargeRepository;
import com.gitq.jedi.context.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class Migrator105 {

    public void run() {
        log.info("补偿HisInpatientHospitalCharge中User 开始");
        HisInpatientHospitalChargeRepository hospitalChargeRepository = AppContext.getInstance(HisInpatientHospitalChargeRepository.class);
        PatientRepository patientRepository = AppContext.getInstance(PatientRepository.class);
        Pageable pageable = PageRequest.of(0, 1000, Sort.by("id"));
        Page<HisInpatientHospitalCharge> records =  hospitalChargeRepository.findAll(pageable);
        while (true) {
            records.forEach(u -> {
                Patient patient = patientRepository.getById(Long.valueOf( u.getPatient_id()));
                u.setUser(patient.getUser());
                hospitalChargeRepository.save(u);
            });

            if (records.hasNext()) {
                pageable = pageable.next();
                records = hospitalChargeRepository.findAll(pageable);
            } else {
                break;
            }
        }
        log.info("补偿HisInpatientHospitalCharge中User 完成");
    }

}
