package cn.taihealth.ih.service.vm.ca;

import cn.taihealth.ih.domain.enums.HospitalCAEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CaPrescriptionParam {

    @JsonIgnore
    private HospitalCAEnum hospitalCA;

    @ApiModelProperty("ca的用户名")
    private String username;

    @ApiModelProperty(value = "应用鉴权，由应用私钥对 api_key && document_no && seal_strategy_id 的 Base64 编码签名值进行 Url encode 的数据")
    private String apiSign;

//    @ApiModelProperty("印章图片 base64")
//    private String sealPicture;

    @ApiModelProperty("证书序列号")
    private String keySN;

}
