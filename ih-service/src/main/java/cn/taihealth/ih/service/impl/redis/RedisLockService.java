package cn.taihealth.ih.service.impl.redis;

import cn.taihealth.ih.service.api.LockService;
import cn.taihealth.ih.service.error.LockException;
import lombok.AllArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 */
@Service
@AllArgsConstructor
public class RedisLockService implements LockService {

    private static final Logger log = LoggerFactory.getLogger(RedisLockService.class);

    private final RedissonClient redissonClient;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public <T> T executeWithLock(String name, Callable<T> func) {
        try {
            return internalLockExecute(name, func);
        } catch (InterruptedException e) {
            throw new LockException(e);
        }
    }

    private <T> T internalLockExecute(String name, Callable<T> func) throws InterruptedException {
        if (!name.startsWith("lock.")) {
            name = "lock." + name;
        }
        RLock lock = redissonClient.getLock(name);
        log.trace("Acquiring lock {}", name);
        try {
            lock.lock(120, TimeUnit.SECONDS);
            log.trace("Acquired lock {}", name);
            return func.call();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new LockException(e);
            }
        } finally {
            lock.unlock();
            log.trace("Unlocked lock {}", name);
        }
    }

    @Override
    public <T> T executeWithLockThrowError(String name, Callable<T> func) throws LockException {
        if (!name.startsWith("lock.")) {
            name = "lock." + name;
        }
        RLock lock = redissonClient.getLock(name);
        log.trace("Acquiring lock {}", name);

        try {
            if (lock.isLocked()) {
                throw new LockException("处理中, 请稍后再试");
            }
            if (!lock.tryLock(0, 120, TimeUnit.SECONDS)) {
                throw new LockException("重复的请求正在处理中, 请稍后再试");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        try {
            log.trace("Acquired lock {}", name);
            return func.call();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new LockException(e);
            }
        } finally {
            lock.unlock();
            log.trace("Unlocked lock {}", name);
        }
    }

    @Override
    public <T> T executeAutomaticRenewal(String name, Callable<T> func) throws LockException {
        if (!name.startsWith("lock.")) {
            name = "lock." + name;
        }
        RLock lock = redissonClient.getLock(name);
        log.trace("AutomaticRenewal lock {}", name);
        try {
            if (lock.isLocked()) {
                throw new LockException("处理中, 请稍后再试");
            }
            if (!lock.tryLock(0, -1, TimeUnit.SECONDS)) {
                throw new LockException("处理中, 请稍后再试");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        try {
            log.trace("AutomaticRenewal lock {}", name);
            return func.call();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new LockException(e);
            }
        } finally {
            lock.unlock();
            log.trace("Unlocked lock {}", name);
        }
    }

    @Override
    public boolean getLockWithoutUnlock(String name, long time) {
        if (!name.startsWith("lock.")) {
            name = "lock." + name;
        }
        RLock lock = redissonClient.getLock(name);
        log.trace("Acquiring lock {}", name);
        try {
            if (lock.isLocked()) {
                return false;
            }
            return lock.tryLock(0, time, TimeUnit.SECONDS);
        } catch (Exception e) {
            return false;
        }
    }
}
