package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.commons.valid.XSSValid;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.Dept.DeptType;
import cn.taihealth.ih.service.vm.UploadVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@ToString(exclude = {"hospital", "diseaseDTOS"})
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
@Accessors(chain = true) // 链式编程写法
public class DeptDTO extends UpdatableDTO {


    @ApiModelProperty(value = "科室名称最长64个字符", required = true)
    @NotBlank
    @Size(max = 64, message = "科室名称最长64个字符")
    private String deptName;

    @ApiModelProperty("信息简介")
    @XSSValid
    private String introduction;

    @ApiModelProperty("科室类型")
    private Dept.DeptType deptType = DeptType.UNKNOWN;

    @ApiModelProperty("科室是否运营")
    private Boolean enabled = true;

    @ApiModelProperty(value = "科室编号最长64个字符", required = true)
    @NotBlank
    @Size(max = 64, message = "科室编号最长64个字符")
    private String deptCode;

    @ApiModelProperty(value = "科室上报代码最长64个字符")
    @Size(max = 64, message = "科室上报代码最长64个字符")
    private String reportCode;

    @ApiModelProperty(value = "科目代码")
    private String subjectCode;

    @ApiModelProperty(value = "科目名称")
    private String subjectName;

    @ApiModelProperty(value = "排序序号")
    private Integer orderValue;

    @ApiModelProperty("挂号费用(在线急诊 单位: 分, 默认25元)")
    private int emergencyFee = 2500;

    @ApiModelProperty("挂号费用(线下普通号 单位: 分, 默认25元)")
    private int offlineGeneralFee = 2500;

    @ApiModelProperty("所属医院")
    private HospitalDTO hospital;

    @ApiModelProperty("疾病标签")
    private List<DiseaseDTO> diseaseDTOS = Lists.newArrayList();

    /**
     * 导诊顺序(0是全科,1是内科/外科，2是专科，-1是不参与导诊的)
     */
    @ApiModelProperty("导诊顺序 导诊顺序(0是全科,1是内科/外科，2是专科，-1是不参与导诊的)")
    private Integer guidanceNumber;

    @ApiModelProperty("出诊预约开关, 是否能挂号")
    private boolean canRegister = true;

    @ApiModelProperty("在线门诊挂号费用 单位：分")
    private int returnVisitFee = 2500;

    @ApiModelProperty("在线视频问诊费用 单位：分")
    private int videoReturnVisitFee = 2500;

    @ApiModelProperty("电话问诊费用 单位：分")
    private int phoneReturnVisitFee = 2500;

    @ApiModelProperty("在线视频咨询挂号费用")
    private Integer videoConsultFee;

    @ApiModelProperty("在线图文咨询挂号费用")
    private Integer graphicConsultFee;

    @ApiModelProperty("是否开启在线视频咨询服务")
    private Boolean videoConsultEnabled = false;

    @ApiModelProperty("是否开启在线图文咨询服务")
    private Boolean graphicConsultEnabled = false;

    @ApiModelProperty("是否开启在线图文问诊服务")
    private Boolean returnVisitEnabled = false;

    @ApiModelProperty("是否开启在线视频问诊服务")
    private Boolean videoReturnVisitEnabled = false;

    @ApiModelProperty("是否开启电话问诊服务")
    private Boolean phoneReturnVisitEnabled = false;

    @ApiModelProperty("科室图片")
    @NotNull
    private UploadVM picture;


    public DeptDTO(Dept dept, boolean relation) {
        super(dept);
        this.deptName = dept.getDeptName();
        this.introduction = dept.getIntroduction();
        this.deptType = dept.getDeptType();
        this.deptCode = dept.getDeptCode();
        this.reportCode = dept.getReportCode();
        this.enabled = dept.getEnabled();
        this.emergencyFee = dept.getEmergencyFee();
        this.guidanceNumber = dept.getGuidanceNumber();
        if (relation) {
            this.hospital = new HospitalDTO(dept.getHospital());
            this.diseaseDTOS = dept.getHospitalDeptDiseaseList().stream()
                .map(u -> new DiseaseDTO(u.getDisease(), false)).collect(Collectors.toList());
        }
        this.offlineGeneralFee = dept.getOfflineGeneralFee();
        this.canRegister = dept.isCanRegister();

        this.returnVisitFee = dept.getReturnVisitFee();
        this.videoReturnVisitFee = dept.getVideoReturnVisitFee();
        this.phoneReturnVisitFee = dept.getPhoneReturnVisitFee();
        this.graphicConsultFee = dept.getGraphicConsultFee();
        this.videoConsultFee = dept.getVideoConsultFee();
        this.returnVisitEnabled = dept.getReturnVisitEnabled();
        this.videoReturnVisitEnabled = dept.getVideoReturnVisitEnabled();
        this.phoneReturnVisitEnabled = dept.getPhoneReturnVisitEnabled();
        this.graphicConsultEnabled = dept.getGraphicConsultEnabled();
        this.videoConsultEnabled = dept.getVideoConsultEnabled();
        if (dept.getPicture() != null) {
            picture = new UploadVM(dept.getPicture());
        }
        subjectCode = dept.getSubjectCode();
        orderValue = dept.getOrderValue();
        subjectName = dept.getSubjectName();
    }

    public DeptDTO(Dept dept) {
        this(dept, true);
    }

}
