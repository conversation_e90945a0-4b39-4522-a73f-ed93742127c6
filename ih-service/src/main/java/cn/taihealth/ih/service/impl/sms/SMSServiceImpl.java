package cn.taihealth.ih.service.impl.sms;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.SmsTemplateType;
import cn.taihealth.ih.domain.enums.SmsType;
import cn.taihealth.ih.service.api.SMSService;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 *
 */
@Service
public class SMSServiceImpl implements SMSService {
    private static final Logger log = LoggerFactory.getLogger(SMSServiceImpl.class);

    public SMSServiceImpl() {
    }

    /**
     * 登录验证码
     * @param mobile
     * @param token
     */
    @Override
    public void sendLoginMessage(Hospital hospital, String mobile, String token) {
        sendToken(hospital,HospitalSettingKey.SMS_LOGIN_TEMPLATE_ID, mobile, token, 10);
    }

    @Override
    public void sendAddPatientToken(Hospital hospital, String mobile, User user, String token) {
//        String userMobile = user.getMobile();
//        userMobile = userMobile.substring(0, 3) + "****" + userMobile.substring(7, userMobile.length());
//        String userName = String.join("", user.getName() == null ? "" : user.getName(), "(" + userMobile + ")");
//        String code = token + "，" + userName;
        sendMessage(hospital,HospitalSettingKey.SMS_ADD_PATIENT_TEMPLATE_ID, mobile,
                    ImmutableMap.<String, String>builder()
                        .put("code", token).build());
    }

    /**
     * 发送通用验证码
     * @param mobile
     * @param token
     * @param message
     */
//    public void sendVerificationCodeMessage(String mobile, String token, String message) {
//        sendMessage(hospital,VERIFICATION_CODE_TEMPLATE_ID, mobile, Lists.newArrayList(token, "10", message));
//    }

    /**
     * 注册验证码
     * @param mobile
     * @param token
     */
    @Override
    public void sendSignupMessage(Hospital hospital, String mobile, String token) {
        sendToken(hospital,HospitalSettingKey.SMS_SIGNUP_TEMPLATE_ID, mobile, token, 10);
    }

    @Override
    public void sendWechatMessage(Hospital hospital, String mobile, String token) {
        sendToken(hospital,HospitalSettingKey.SMS_SIGNUP_TEMPLATE_ID, mobile, token, 10);
    }

    /**
     * 修改密码验证码
     * @param mobile
     * @param token
     */
    @Override
    public void sendResetPasswordMessage(Hospital hospital, String mobile, String token) {
        sendToken(hospital,HospitalSettingKey.SMS_RESET_PASSWORD_TEMPLATE_ID, mobile, token, 10);
    }

    @Override
    public void sendResetMobile(Hospital hospital, String mobile, String token) {
        sendToken(hospital,HospitalSettingKey.SMS_RESET_MOBILE_TEMPLATE_ID, mobile, token, 10);
    }

    /**
     * 发送短信验证码
     * @param settingKey
     * @param mobile
     * @param code
     */
    private void sendToken(Hospital hospital, HospitalSettingKey settingKey, String mobile, String code, int validityTime) {
        log.info("发短信给 {}", mobile);
        sendMessage(hospital,settingKey, mobile,
            ImmutableMap.<String, String>builder().put("code", code).build());
    }


    @Override
    public void sendTriageResult(Hospital hospital, String mobile, String name) {
        log.info("发短信给 {}", name);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_TRIAGE_RESULT_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder().put("PatientName", name).build());
    }

    @Override
    public void sendTriageVideo(Hospital hospital, String mobile, String name) {
        log.info("发短信给 {}", name);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_TRIAGE_VIDEO_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder().put("PatientName", name).build());
    }

    @Override
    public void sendVisitRemind(Hospital hospital, String mobile, String patientName, String departmentName, String appointmentTime1,
                                String appointmentTime2) {
        log.info("发短信给：{}", patientName);
        SmsType smsType = getSmsType(hospital);
        String AppointmentTime1 = smsType == SmsType.MAS ? "Appointme1" : "AppointmentTime1";
        String AppointmentTime2 = smsType == SmsType.MAS ? "Appointme2" : "AppointmentTime2";
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_VISIT_REMIND_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("PatientName", patientName)
                .put("DepartmentName", departmentName)
                .put(AppointmentTime1, appointmentTime1)
                .put(AppointmentTime2, appointmentTime2)
                .build());
    }

    @Override
    public void sendWaitTreatStart(Hospital hospital, String mobile, String name, int time) {
        log.info("发短信给 {}", name);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_WAITING_FOR_DOCTOR_START_TEMPLATE,
            mobile, ImmutableMap.<String, String>builder()
                .put("PatientName", name)
                .put("VisitTime", "")
                .build());
    }

    @Override
    public void sendReWaitTreatStart(Hospital hospital, String mobile, String name, int time) {
        log.info("发短信给 {}", name);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_RE_WAITING_FOR_DOCTOR_START_TEMPLATE,
            mobile, ImmutableMap.<String, String>builder()
                .put("PatientName", name)
                .put("VisitTime", "")
                .build());
    }

    @Override
    public void sendVisitStart(Hospital hospital, String mobile, String deptName, String name, String doctorName) {
        log.info("发短信给 {}", name);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_CALL_FOR_VISIT_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("DepartmentName", deptName)
                .put("DoctorName", doctorName)
                .put("PatientName", name)
                .build());
    }

    @Override
    public void sendConsultVisitStart(Hospital hospital, String mobile, String consultName, String doctorName, String name) {
        log.info("护士已接诊，发短信给 {}", name);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_CALL_FOR_CONSULT_TEMPLATE, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DepartmentName", consultName)
                        .put("DoctorName", doctorName)
                        .put("PatientName", name)
                        .build());
    }

    @Override
    public void sendOrderClosed(Hospital hospital, String mobile, String orderId, String orderType) {
        log.info("发短信给 {}", mobile);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_ORDER_CLOSED_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("OrderType", orderType)
                .put("OrderID", orderId)
                .build());
    }

    @Override
    public void sendOrderConfirmed(Hospital hospital, String mobile, String orderId, String orderType) {
        log.info("订单支付成功,发短信的手机号为 {}", mobile);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_ORDER_CONFIRMED_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("OrderType", orderType)
                .put("OrderID", orderId)
                .build());
    }

    @Override
    public void sendOrderPending(Hospital hospital, String mobile, String orderId, String orderType,
                                 String orderExpireTime) {
        log.info("发短信给 {}", mobile);
        Map<String, String> params = new LinkedHashMap<>();
        params.put("OrderType", orderType);
        params.put("OrderID", orderId);
        if (getSmsType(hospital) == SmsType.MAS) {
            params.put("DateExpire", orderExpireTime.substring(0,10));
            params.put("TimeExpire", orderExpireTime.substring(11));
        } else {
            params.put("OrderExpireTime", orderExpireTime);
        }
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_ORDER_PENDING_TEMPLATE, mobile, params);
    }

    @Override
    public void sendPrescriptionReady(Hospital hospital, String mobile, String patientName,
                                      String prescriptionExpireTime) {
        log.info("发短信给 {}", mobile);
        SmsType smsType = getSmsType(hospital);
        Map<String, String> params = new LinkedHashMap<>();
        params.put("PatientName", patientName);
        if (smsType == SmsType.MAS) {
            params.put("DateExpire", prescriptionExpireTime.substring(0,10));
            params.put("TimeExpire", prescriptionExpireTime.substring(11));
        } else {
            params.put("PrescriptionExpireTime", prescriptionExpireTime);
        }
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_PRESCRIPTION_READY_TEMPLATE, mobile, params);
    }

    @Override
    public void sendOrderCanceled(Hospital hospital, String mobile, String orderId, String orderType) {
        log.info("咨询订单患者主动取消,发短信给 {}", mobile);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_ORDER_CANCELED_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("OrderType", orderType)
                .put("OrderID", orderId)
                .build());
    }

    @Override
    public void sendEvaluate(Hospital hospital, String mobile, String PatientName) {
        log.info("发短信给 {}", PatientName);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_CALL_FOR_EVALUATE_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("PatientName", PatientName)
                .build());
    }

    @Override
    public void sendToAppointmentCheck(Hospital hospital, String mobile, String patientName, String departmentName,
                                       String physicalExamName, String appointmentTime1, String appointmentTime2,
                                       String position) {
        log.info("发短信给 {}", patientName);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_TO_APPOINTMENT_CHECK_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("PatientName", patientName)
                .put("DepartmentName", departmentName)
                .put("PhysicalExamName", physicalExamName)
                .put("Appointment1", appointmentTime1)
                .put("Appointment2", appointmentTime2)
                .put("Position", position)
                .build());
    }

    @Override
    public void sendAppointmentCheckSuccess(Hospital hospital, String mobile, String patientName, String departmentName,
                                            String physicalExamName, String appointmentTime1, String appointmentTime2,
                                            String position) {
        log.info("发短信给 {}", patientName);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_APPOINTMENT_CHECK_SUCCESS_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("PatientName", patientName)
                .put("DepartmentName", departmentName)
                .put("PhysicalExamName", physicalExamName)
                .put("Appointment1", appointmentTime1)
                .put("Appointment2", appointmentTime2)
                .put("Position", position)
                .build());
    }

    @Override
    public void sendAppointmentCheckCancel(Hospital hospital, String mobile, String patientName, String departmentName,
                                           String physicalExamName) {
        log.info("发短信给 {}", patientName);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_APPOINTMENT_CHECK_CANCEL_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("PatientName", patientName)
                .put("DepartmentName", departmentName)
                .put("PhysicalExamName", physicalExamName)
                .build());
    }

    @Override
    public void sendAppointmentCheckChange(Hospital hospital, String mobile, String patientName, String departmentName,
                                           String physicalExamName, String appointmentTime1, String appointmentTime2) {
        log.info("发短信给 {}", patientName);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_APPOINTMENT_CHECK_CHANGE_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("PatientName", patientName)
                .put("DepartmentName", departmentName)
                .put("PhysicalExamName", physicalExamName)
                .put("Appointment1", appointmentTime1)
                .put("Appointment2", appointmentTime2)
                .build());
    }

    @Override
    public void sendSignSuccess(Hospital hospital, String mobile, String patientName, String departmentName, String physicalExamName,
                                String sequenceNumber) {
        log.info("发短信给 {}", patientName);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_SIGN_SUCCESS_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("PatientName", patientName)
                .put("DepartmentName", departmentName)
                .put("PhysicalExamName", physicalExamName)
                .put("SequenceNumber", sequenceNumber)
                .build());
    }

    @Override
    public void sendReportCreated(Hospital hospital, String mobile, String patientName, String departmentName,
                                  String physicalExamName) {
        log.info("发短信给 {}", patientName);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_REPORT_CREATED_TEMPLATE, mobile,
            ImmutableMap.<String, String>builder()
                .put("PatientName", patientName)
                .put("DepartmentName", departmentName)
                .put("PhysicalExamName", physicalExamName)
                .build());
    }

    @Override
    public void sendEnableToAppointment(Hospital hospital, String mobile, String patientName, String departmentName,
                                        String physicalExamName) {
        log.info("发短信给 {}", patientName);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_ENABLE_TO_APPOINTMENT_TEMPLATE, mobile,
                    ImmutableMap.<String, String>builder()
                        .put("PatientName", patientName)
                        .put("DepartmentName", departmentName)
                        .put("PhysicalExamName", physicalExamName)
                        .build());
    }

    @Override
    public void sendHospitalCreated(Hospital hospital, String mobile, String password, String url) {
        log.info("发短信给 {}", mobile);
//        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_ENABLE_TO_APPOINTMENT_TEMPLATE, mobile,
//            ImmutableMap.<String, String>builder()
//                .put("PatientName", patientName)
//                .put("DepartmentName", departmentName)
//                .put("PhysicalExamName", physicalExamName)
//                .build());
    }

    @Override
    public void sendRefundToTimeOutOnTimeConfirmed(Hospital hospital, String mobile, String doctorName, String consultType, String orderNumber) {
        log.info("问诊-候诊中咨询订单医生未接诊退款,发送短信的手机号: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_TIME_OUT_ONTIME_CONFIRMED_REFUNDED, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DoctorName", doctorName)
                        .put("ConsultType", consultType)
                        .put("OrderNumber", orderNumber)
                        .build());
    }

    @Override
    public void sendOrderCanceledToDoctor(Hospital hospital, String mobile, String doctorName, String consultType, String orderNumber) {
        log.info("患者短信-问诊-候诊中咨询订单医生退诊,发送短信的手机号: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_ONTIME_CONFIRMED_DOCTOR_REFUNDED, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DoctorName", doctorName)
                        .put("ConsultType", consultType)
                        .put("OrderNumber", orderNumber)
                        .build());
    }

    @Override
    public void sendOrderCanceledToNurse(Hospital hospital, String mobile, String doctorName, String orderNumber) {
        log.info("患者短信-问诊-候诊中护理咨询订单护士退诊,发送短信的手机号: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_ONTIME_CONFIRMED_NURSE_REFUNDED, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DoctorName", doctorName)
                        .put("OrderNumber", orderNumber)
                        .build());
    }

    @Override
    public void sendCallForVisitToMessageUnRead(Hospital hospital, String mobile, String patientName, String doctorName) {
        log.info("患者短信-医生回复患者,发送短信给: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_DOCTORS_REPLY_TO_PATIENT, mobile,
                ImmutableMap.<String, String>builder()
                        .put("PatientName", patientName)
                        .put("DoctorName", doctorName)
                        .build());
    }

    @Override
    public void sendCallForConsultToMessageUnRead(Hospital hospital, String mobile, String patientName, String doctorName) {
        log.info("患者短信-护士回复患者,发送短信给: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_NURSE_REPLY_TO_PATIENT, mobile,
                ImmutableMap.<String, String>builder()
                        .put("PatientName", patientName)
                        .put("DoctorName", doctorName)
                        .build());
    }

    @Override
    public void sendDoctorHangsUpVideo(Hospital hospital, String mobile, String doctorName, String consultType) {
        log.info("视频咨询中医生挂断视频,发送短信的手机号: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_DOCTOR_HANGS_UP_VIDEO, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DoctorName", doctorName)
                        .put("ConsultType", consultType)
                        .build());
    }

    @Override
    public void sendDoctorInitiateVideoConsultation(Hospital hospital, String mobile, String doctorName, String consultType) {
        log.info("视频咨询中医生发起视频通话,发送短信的手机号: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_VIDEO_CONSULTATION_INITIATED_BY_DOCTOR, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DoctorName", doctorName)
                        .put("ConsultType", consultType)
                        .build());
    }

    @Override
    public void sendOrderRefundToStartedRefunded(Hospital hospital, String mobile, String doctorName, String consultType, String orderNumber) {
        log.info("患者短信-进行中订单退款,发送短信的手机号: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_STARTED_REFUNDED, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DoctorName", doctorName)
                        .put("ConsultType", consultType)
                        .put("OrderNumber", orderNumber)
                        .build());
    }

    @Override
    public void sendOrderRefundToCompletedRefunded(Hospital hospital, String mobile, String doctorName, String consultType, String orderNumber) {
        log.info("患者短信-已完成订单退款,发送短信的手机号: " + mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_COMPLETED_REFUNDED, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DoctorName", doctorName)
                        .put("ConsultType", consultType)
                        .put("OrderNumber", orderNumber)
                        .build());
    }

    @Override
    public void sendDoctorAccumulateExcessiveConsultation(Hospital hospital, String mobile, String doctorName, String toBeTreatedPatientAmount,
                                                          String tobeRepliedPatientAmount, String time, String maxTime) {
        log.info("累积了过量的候诊与咨询,发送短信给医生: " + doctorName);
        if (getSmsType(hospital) != SmsType.MAS) {
            time = time + "小时";
            maxTime = maxTime + "小时";
        }
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_MESSAGE_ACCUMULATION, mobile,
                ImmutableMap.<String, String>builder()
                        .put("DoctorName",doctorName)
                        .put("ToBeTreatedPatientAmount", toBeTreatedPatientAmount)
                        .put("TobeRepliedPatientAmount", tobeRepliedPatientAmount)
                        .put("Time", time)
                        .put("MaxTime", maxTime)
                        .build());
    }

    @Override
    public void sendDrugOrderDelivered(Hospital hospital, String mobile, String orderType, String orderID, String deliveryName, String trackingNumber) {
        log.info("药品订单发货, 发短信给 {}", mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_ORDER_DELIVERED_TEMPLATE, mobile,
                ImmutableMap.<String, String>builder()
                        .put("OrderType",orderType)
                        .put("OrderID", orderID)
                        .put("DeliveryName", deliveryName)
                        .put("TrackingNumber", trackingNumber)
                        .build());
    }

    @Override
    public void sendDrugOrderReceived(Hospital hospital, String mobile, String orderType, String orderID) {
        log.info("药品订单收货, 发短信给 {}", mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_ORDER_RECEIVED_TEMPLATE, mobile,
                ImmutableMap.<String, String>builder()
                        .put("OrderType",orderType)
                        .put("OrderID", orderID)
                        .build());
    }

    @Override
    public void sendDrugOrderClosed(Hospital hospital, String mobile, String orderType, String orderID) {
        log.info("药品订单超时取消, 发短信给 {}", mobile);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_ORDER_CLOSED_TEMPLATE, mobile,
                ImmutableMap.<String, String>builder()
                        .put("OrderType", orderType)
                        .put("OrderID", orderID)
                        .build());
    }

    @Override
    public void sendDrugOrderCanceled(Hospital hospital, String mobile, String orderType, String orderID) {
        log.info("药品订单患者取消, 发短信给 {}", mobile);
        sendMessage(hospital,HospitalSettingKey.SMS_NOTIFY_ORDER_CANCELED_TEMPLATE, mobile,
                ImmutableMap.<String, String>builder()
                        .put("OrderType", orderType)
                        .put("OrderID", orderID)
                        .build());
    }

    @Override
    public void sendDrugOrderPending(Hospital hospital, String mobile, String orderType, String orderId, String orderExpireTime) {
        log.info("药品订单待支付, 发短信给 {}", mobile);
        Map<String, String> params = new LinkedHashMap<>();
        params.put("OrderType", orderType);
        params.put("OrderID", orderId);
        if (getSmsType(hospital) == SmsType.MAS) {
            params.put("DateExpire", orderExpireTime.substring(0,10));
            params.put("TimeExpire", orderExpireTime.substring(11));
        } else {
            params.put("OrderExpireTime", orderExpireTime);
        }
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_ORDER_PENDING_TEMPLATE, mobile, params);
    }

    @Override
    public void sendReplyComplaint(Hospital hospital, String telephone, String userName) {
        log.info("回复用户意见建议发送短信, 发短信给 {}", telephone);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_REPLAY_ADVICE, telephone, new LinkedHashMap<>());
    }

    @Override
    public void sendSystemAlert(Hospital hospital, String telephone) {
        log.info("监控系统报警发送短信, 发短信给 {}", telephone);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_MONITORING_SYSTEM_ALERT, telephone, new LinkedHashMap<>());
    }

    @Override
    public void sendSmartFollowUp(Hospital hospital, String mobile) {
        log.info("推送智能随访, 发短信给 {}", mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_SMART_FOLLOW_UP, mobile, new LinkedHashMap<>());
    }

    @Override
    @Async
    public void sendNursingOrdersPaid(Hospital hospital, String mobile) {
        log.info("护理上门 新订单待派单, 发短信给 {}", mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_NURSING_ORDERS_PAID, mobile, new LinkedHashMap<>());
    }

    @Override
    @Async
    public void sendNursingOrdersTaskAssigned(Hospital hospital, String mobile) {
        log.info("护理上门 新任务已分配, 发短信给 {}", mobile);
        sendMessage(hospital, HospitalSettingKey.SMS_NOTIFY_NURSING_ORDERS_TASK_ASSIGNED, mobile, new LinkedHashMap<>());
    }

    public void sendMessage(Hospital hospital, HospitalSettingKey settingKey, String toMobile, Map<String, String> params) {
        SmsType smsType = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.SMS_TYPE, SmsType.class);
        if (StringUtils.isBlank(smsType.getType())) {
            return;
        }
        SMSSender smsSender = AppContext.getInstance(smsType.getType(), SMSSender.class);
        SmsTemplateType templateType = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.SMS_TEMPLATE_TYPE, SmsTemplateType.class);
        String template = HospitalSettingsHelper.getString(hospital, settingKey);
        if (StringUtils.isBlank(template)) {
            log.error("短信模板未配置");
            return;
        }
        if (templateType == SmsTemplateType.TEMPLATE) {
            smsSender.sendTemplate(hospital, template, toMobile, params);
        } else {
            smsSender.sendNormal(hospital, template, toMobile, params);
        }
    }

    /**
     * 因为中国移动模版对参数的长度限制最长为10位,代码不能通用所以抽出来判断当前医院用的是哪家短信平台
     * @param hospital 当前医院
     * @return
     */
    private SmsType getSmsType(Hospital hospital) {
        return HospitalSettingsHelper.getValue(hospital,HospitalSettingKey.SMS_TYPE,SmsType.class);
    }
}
