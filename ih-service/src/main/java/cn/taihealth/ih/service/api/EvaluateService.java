package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.Evaluate;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorker;
import cn.taihealth.ih.service.dto.OfflineOrderEvaluateDTO;
import cn.taihealth.ih.service.dto.OrderEvaluateStatsDTO;
import org.springframework.data.domain.Page;

/**
 * @Author: jzs
 * @Date: 2023-11-10
 */
public interface EvaluateService {
    OfflineOrderEvaluateDTO saveEvaluate(User user, OfflineOrder offlineOrder, OfflineOrderEvaluateDTO offlineOrderEvaluateDTO);

    Page<OfflineOrderEvaluateDTO> queryOrderEvaluateList(String query, Integer pageNum, Integer pageSize, Hospital hospital);

    OrderEvaluateStatsDTO getOfflineOrderEvaluateStats(Hospital hospital);

    void editEvaluate(Evaluate evaluate);

    Page<OfflineOrderEvaluateDTO> getOfflineOrderEvaluateList(User user, Hospital hospital, int pageNo, int size);

    /**
     * 获取医生的好评率
     * @param hospital
     * @return
     */
    double getFavorableRate(Hospital hospital, OfflineMedicalWorker offlineMedicalWorker);
}
