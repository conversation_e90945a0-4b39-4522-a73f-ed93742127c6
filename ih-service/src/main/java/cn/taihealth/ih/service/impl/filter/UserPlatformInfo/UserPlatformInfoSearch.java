package cn.taihealth.ih.service.impl.filter.UserPlatformInfo;


import cn.taihealth.ih.domain.hospital.UserPlatformInfo;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class UserPlatformInfoSearch extends SearchCriteria<UserPlatformInfo> {

    public static UserPlatformInfoSearch of(String query) {
        UserPlatformInfoSearch categorySearch = new UserPlatformInfoSearch();
        categorySearch.parse(query);
        return categorySearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<UserPlatformInfo> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        UserPlatformInfoSearch.Qualifier qualifier = UserPlatformInfoSearch.Qualifier
            .valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case NAME:
                return new NameFilter(value);
            default:
                return null;
        }
    }


    @Override
    protected SearchFilter<UserPlatformInfo> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        NAME,
    }
}
