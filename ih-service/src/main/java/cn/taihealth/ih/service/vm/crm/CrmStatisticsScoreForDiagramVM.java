package cn.taihealth.ih.service.vm.crm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Moon
 * @Date: 2021年04月19日13:23:22
 */
@Data
@NoArgsConstructor
public class CrmStatisticsScoreForDiagramVM {

    @ApiModelProperty("分值区间临界值-左侧")
    private float scoreLeft;

    @ApiModelProperty("分值区间临界值-右侧")
    private float scoreRight;

    @ApiModelProperty("次数")
    private int times;

}
