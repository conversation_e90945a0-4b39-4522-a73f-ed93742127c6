package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import lombok.Data;

import java.io.Serializable;

/**
 * 检查项目
 */
@Data
public class CheckItemInfo implements Serializable {
    // 科室代码	Y	HIS中科室唯一代码
    private String dept_id;
    // 科室名称	Y
    private String dept_name;
    // 项目代码	Y	HIS中检查项目唯一代码
    private String item_code;
    // 项目名称	Y
    private String item_name;
    // 类别代码	Y	HIS中检查类别唯一代码
    private String category_code;
    // 类别名称	Y
    private String category_name;
    // 项目费用	Y
    private String price;
    // 是否支持医保	N	0不支持,1支持,默认0
    private String insurance;
    private String notes;
}
