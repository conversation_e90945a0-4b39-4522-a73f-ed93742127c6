package cn.taihealth.ih.service.impl.filter.bill;

import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

/**
 * 对账单根据医保结算单号过滤
 */
public class PayOrderIdFilter implements SearchFilter<Bill> {
    private final String value;

    public PayOrderIdFilter(String value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Bill> toSpecification() {
        return Specifications.eq("payOrderId", value);
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
