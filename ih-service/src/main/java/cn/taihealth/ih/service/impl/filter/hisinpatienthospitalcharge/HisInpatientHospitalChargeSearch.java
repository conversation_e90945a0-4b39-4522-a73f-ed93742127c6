package cn.taihealth.ih.service.impl.filter.hisinpatienthospitalcharge;

import cn.taihealth.ih.domain.enums.PaymentMethod;
import cn.taihealth.ih.domain.enums.PaymentStatus;
import cn.taihealth.ih.domain.his.HisInpatientHospitalCharge;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class HisInpatientHospitalChargeSearch extends SearchCriteria<HisInpatientHospitalCharge> {

    public HisInpatientHospitalChargeSearch() {
        super();
    }

    public static HisInpatientHospitalChargeSearch of(String query) {
        HisInpatientHospitalChargeSearch hospitalOrderSearch = new HisInpatientHospitalChargeSearch();
        hospitalOrderSearch.parse(query);
        return hospitalOrderSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier each : Qualifier.values()) {
            list.add(each.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<HisInpatientHospitalCharge> createFilter(String input) {
        Matcher matcher = qualifierRegex.matcher(input);
        if (!matcher.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(matcher.group("qualifier").toUpperCase());
        String value = matcher.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case SORT:
                return new SortFilter(value);
            case SELECT_TIME:
                return new CreatedDateFilter(value);
            case ORDER_NO:
                return new GenericFilter<>("id", Operator.eq, Long.valueOf(value));
            case REG_NO:
                return new GenericFilter<>("regno", Operator.eq, value);
            case PAY_STATUS:
                if ("OTHER".equals(value)) {
                    return new OtherStatusFilter(value);
                } else {
                    return new GenericFilter<>("paymentStatus", Operator.eq,
                                               PaymentStatus.valueOf(value));
                }
            case PAYMENT_METHOD:
                return new GenericFilter<>("pay_type", Operator.eq, PaymentMethod.valueOf(value));
            case DEPT_NAME:
                return new DeptNameFilter(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<HisInpatientHospitalCharge> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        SORT,
        //  2. 订单编号，精确搜索
        ORDER_NO,
        //  3. 住院号：精确搜索
        REG_NO,
        //  4. 支付状态（已支付、已退款、待支付、其他），默认为不限
        PAY_STATUS,
        //  5. 支付渠道（微信支付、支付宝、其他），默认为不限
        PAYMENT_METHOD,
        //  6. 就诊科室，支持模糊查询，字典来自HIS的线下门诊就诊科室
        DEPT_NAME,
        SELECT_TIME
    }
}
