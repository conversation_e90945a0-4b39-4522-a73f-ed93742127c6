package cn.taihealth.ih.service.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.taihealth.ih.domain.VisitorPass;
import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.batik.transcoder.TranscoderException;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.JPEGTranscoder;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 *
 */
@Component
@AllArgsConstructor
@Slf4j
public class BusinessCardGenerator {

    private Holder holder() {
        return Holder.INSTANCE;
    }

    private enum Holder {
        INSTANCE;

        final String grade1;
        final String phz;
//		String grade3;
//        String grade4;

        Holder() {
            try {
                grade1 = Resources.toString(Resources.getResource(BusinessCardGenerator.class, "/templates/grade1.svg"), Charsets.UTF_8);
                phz = Resources.toString(Resources.getResource(BusinessCardGenerator.class, "/templates/phz.svg"), Charsets.UTF_8);
//				grade3 = Resources.toString(Resources.getResource(BusinessCardGenerator.class, "/templates/grade3.svg"), Charsets.UTF_8);
//                grade4 = Resources.toString(Resources.getResource(BusinessCardGenerator.class, "/templates/grade4.svg"), Charsets.UTF_8);
            } catch (IOException e) {
                throw new RuntimeException("Unable to read resource: template.svg");
            }
        }
    }

    public File generatePhzImage(String filePath, String headImage, VisitorPass visitorPass) throws IOException {
        File outputFile = new File(filePath);
        FileUtils.forceMkdir(outputFile.getParentFile());


        String svg = StrUtil.replace(holder().phz, "{{attendant_picture}}", headImage);
        svg = StrUtil.replace(svg, "{{end_attendant_time}}", DateUtil.format(visitorPass.getEnd_attendant_time(), "yyyy-MM-dd"));
        svg = StrUtil.replace(svg, "{{attendant_name}}", visitorPass.getAttendant_name());
        svg = StrUtil.replace(svg, "{{attendant_certificate_no}}", visitorPass.getAttendant_certificate_no());
        svg = StrUtil.replace(svg, "{{start_attendant_time}}", DateUtil.format(visitorPass.getStart_attendant_time(), "yyyy-MM-dd"));
        svg = StrUtil.replace(svg, "{{patname}}", visitorPass.getPatname());
        svg = StrUtil.replace(svg, "{{regno}}", visitorPass.getRegno());
        svg = StrUtil.replace(svg, "{{dept_name}}", visitorPass.getDept_name());
        svg = StrUtil.replace(svg, "{{ward_name}}", visitorPass.getWard_name());
        svg = StrUtil.replace(svg, "{{bed_no}}", visitorPass.getBed_no());
        svg = StrUtil.replace(svg, "{{in_time}}", DateUtil.format(visitorPass.getIn_time(), "yyyy-MM-dd"));


        JPEGTranscoder transcoder = new JPEGTranscoder();
        transcoder.addTranscodingHint(JPEGTranscoder.KEY_QUALITY, 1.0f);
        try (OutputStream ostream = new FileOutputStream(outputFile);
             StringReader reader = new StringReader(svg)) {
            TranscoderInput input = new TranscoderInput(reader);
            TranscoderOutput output = new TranscoderOutput(ostream);
            transcoder.transcode(input, output);
        } catch (IOException | TranscoderException e) {
            throw new RuntimeException(e);
        }

        return outputFile;
    }


    /**
     * Generate card image using an svg template
     *
     * @param filePath 文件存放路径
     * @return the output image file
     */
    public File generateCardImage(String filePath, String headImage, String name, String hospitalName, String deptName,
                                  String titleName, String qrCode) throws IOException {
        File outputFile = new File(filePath);
        FileUtils.forceMkdir(outputFile.getParentFile());

        String svg = StrUtil.replace(holder().grade1, "{{headerImage}}", headImage);
        svg = StrUtil.replace(svg, "{{name}}", name);
        svg = StrUtil.replace(svg, "{{hospitalName}}", hospitalName);
        svg = StrUtil.replace(svg, "{{deptName}}", deptName);
        svg = StrUtil.replace(svg, "{{titleName}}", titleName);
        svg = StrUtil.replace(svg, "{{qrCode}}", qrCode);

        JPEGTranscoder transcoder = new JPEGTranscoder();
        transcoder.addTranscodingHint(JPEGTranscoder.KEY_QUALITY, 1.0f);
        try (OutputStream ostream = new FileOutputStream(outputFile);
             StringReader reader = new StringReader(svg)) {
            TranscoderInput input = new TranscoderInput(reader);
            TranscoderOutput output = new TranscoderOutput(ostream);
            transcoder.transcode(input, output);
        } catch (IOException | TranscoderException e) {
            throw new RuntimeException(e);
        }

        return outputFile;
    }

    private String insertChar(String code, char ch, int interval) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < code.length(); i++) {
            sb.append(code.charAt(i));
            if (i > 0 && (i % interval == interval - 1)) {
                sb.append(ch);
            }
        }
        return sb.toString();
    }

}
