package cn.taihealth.ih.service.dto.crm;

import cn.taihealth.ih.domain.crm.CrmOption;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 随访-问卷-问题选项
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CrmOptionDTO extends UpdatableDTO {

    @ApiModelProperty("问题")
    private AbstractEntityDTO question;

    @ApiModelProperty("选项内容")
    private String content;

    @Column(name = "排序")
    private int optionOrder;

    @Column(name = "分值")
    private Integer score;

    public CrmOptionDTO(CrmOption option) {
        super(option);
        this.question = new AbstractEntityDTO(option.getQuestion());
        this.content = option.getContent();
        this.optionOrder = option.getOptionOrder();
        this.score = option.getScore();

    }

    public CrmOption toEntity() {
        CrmOption option = new CrmOption();
        option.setContent(content);
        option.setOptionOrder(optionOrder);
        if(score != null){
            option.setScore(score);
        }
        return option;
    }
}
