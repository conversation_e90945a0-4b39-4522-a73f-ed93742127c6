package cn.taihealth.ih.service.dto.export;

import cn.taihealth.ih.commons.util.MathUtils;
import cn.taihealth.ih.service.vm.statistics.supervision.MoreTrafficRatioStats;
import cn.taihealth.ih.service.vm.statistics.supervision.MoreTrafficStats;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.text.DecimalFormat;

@Data
@AllArgsConstructor
public class BusinessStatisticsExportDTO {

    @ApiModelProperty("预约挂号")
    private String appointmentCount;

    @ApiModelProperty("体检预约")
    private String physicalAppointmentCount;

    @ApiModelProperty("报告查询")
    private String reportQueryCount;

    @ApiModelProperty("检查申请")
    private String examAppointmentCount;

    @ApiModelProperty("在线咨询")
    private String consultCount;

    @ApiModelProperty("在线复诊")
    private String returnVisitCount;

    @ApiModelProperty("病案复印预约")
    private String medicalRecordCopyingCount;

    @ApiModelProperty("检验申请")
    private String inspectAppointmentCount;

    @ApiModelProperty("门诊收费支付量")
    private String outpatientFeesCount;

    @ApiModelProperty("住院预交支付量")
    private String prePaymentInhospitalCount;

    @ApiModelProperty("电子处方申请量")
    private String electronicPrescriptionCount;

    private String name;

    public BusinessStatisticsExportDTO(MoreTrafficStats moreTraffic, String name) {
        this.appointmentCount = MathUtils.getDoubleIntString(moreTraffic.getAppointmentCount());
        this.physicalAppointmentCount = MathUtils.getDoubleIntString(moreTraffic.getPhysicalAppointmentCount());
        this.reportQueryCount = MathUtils.getDoubleIntString(moreTraffic.getReportQueryCount());
        this.examAppointmentCount = MathUtils.getDoubleIntString(moreTraffic.getExamAppointmentCount());
        this.consultCount = MathUtils.getDoubleIntString(moreTraffic.getConsultCount());
        this.returnVisitCount = MathUtils.getDoubleIntString(moreTraffic.getReturnVisitCount());
        this.medicalRecordCopyingCount = MathUtils.getDoubleIntString(moreTraffic.getMedicalRecordCopyingCount());
        this.inspectAppointmentCount = MathUtils.getDoubleIntString(moreTraffic.getInspectAppointmentCount());
        this.outpatientFeesCount = MathUtils.getDoubleIntString(moreTraffic.getOutpatientFeesCount());
        this.prePaymentInhospitalCount = MathUtils.getDoubleIntString(moreTraffic.getPrePaymentInhospitalCount());
        this.electronicPrescriptionCount = MathUtils.getDoubleIntString(moreTraffic.getElectronicPrescriptionCount());
        this.name = name;
    }

    public BusinessStatisticsExportDTO(MoreTrafficRatioStats moreTrafficRatioStats, String name) {
        this.appointmentCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getAppointmentRatio());
        this.physicalAppointmentCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getPhysicalAppointmentRatio());
        this.reportQueryCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getReportQueryRatio());
        this.examAppointmentCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getExamAppointmentRatio());
        this.consultCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getConsultRatio());
        this.returnVisitCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getReturnVisitRatio());
        this.medicalRecordCopyingCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getMedicalRecordCopyingRatio());
        this.inspectAppointmentCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getInspectAppointmentRatio());
        this.outpatientFeesCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getOutpatientFeesRatio());
        this.prePaymentInhospitalCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getPrePaymentInhospitalRatio());
        this.electronicPrescriptionCount = MathUtils.getRateByDouble(moreTrafficRatioStats.getElectronicPrescriptionRatio());
        this.name = name;
    }

    
}
