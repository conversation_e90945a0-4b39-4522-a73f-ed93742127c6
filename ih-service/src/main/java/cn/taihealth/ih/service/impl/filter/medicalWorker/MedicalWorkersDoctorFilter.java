package cn.taihealth.ih.service.impl.filter.medicalWorker;

import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.UserRole;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;

import java.util.Objects;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.ListJoin;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 */
public class MedicalWorkersDoctorFilter implements SearchFilter<MedicalWorker> {

    private final String enabled;
    private final Hospital hospital;


    public MedicalWorkersDoctorFilter(String enabled, Hospital hospital) {
        this.enabled = enabled;
        this.hospital = hospital;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<MedicalWorker> toSpecification() {
        if (StringUtil.stringToBoolean(enabled)) {
            return (root, criteriaQuery, criteriaBuilder) -> {
                criteriaQuery.distinct(true);
                Join<MedicalWorker, User> user = root.join("user");
                ListJoin<User, UserRole> userRoles = user.joinList("userRoles");
                return criteriaBuilder.and(criteriaBuilder.equal(userRoles.get("doctor"), 1),
                        criteriaBuilder.equal(userRoles.get("hospital"), hospital),
                        criteriaBuilder.equal(userRoles.get("admin"), 0));
            };
        }
        return null;
    }


    @Override
    public String toExpression() {
        return "enabled:" + enabled;
    }

    @Override
    public boolean isValid() {
        return StringUtils.isNoneEmpty(enabled);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof MedicalWorkersDoctorFilter)) {
            return false;
        }

        MedicalWorkersDoctorFilter mdf = (MedicalWorkersDoctorFilter) other;
        return Objects.equals(enabled, mdf.enabled);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enabled);
    }
}
