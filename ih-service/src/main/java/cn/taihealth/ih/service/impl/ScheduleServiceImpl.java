package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.DoctorFee;
import cn.taihealth.ih.domain.UserStats;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.dict.ExamDevice;
import cn.taihealth.ih.domain.dict.ExamDeviceMaintenance;
import cn.taihealth.ih.domain.dict.ExamItem;
import cn.taihealth.ih.domain.enums.ChannelType;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.Schedule.ScheduleType;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.domain.nursing.NursingConsulItem;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.nursing.NursingConsulItemRepository;
import cn.taihealth.ih.repo.order.ExamOrderRepository;
import cn.taihealth.ih.repo.schedule.*;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.ExamDeviceService;
import cn.taihealth.ih.service.api.LockService;
import cn.taihealth.ih.service.api.ScheduleService;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.dto.ExamScheduleOperationDTO;
import cn.taihealth.ih.service.impl.filter.exam.schedule.ExamScheduleEndTimeFilter;
import cn.taihealth.ih.service.impl.filter.exam.schedule.ExamScheduleSearch;
import cn.taihealth.ih.service.impl.filter.exam.schedule.ExamScheduleStartTimeFilter;
import cn.taihealth.ih.service.impl.filter.exam.schedulegroup.ExamScheduleGroupSearch;
import cn.taihealth.ih.service.impl.filter.schedule.ScheduleEndTimeFilter;
import cn.taihealth.ih.service.impl.filter.schedule.ScheduleSearch;
import cn.taihealth.ih.service.impl.filter.schedule.ScheduleStartTimeFilter;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.vm.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.DoctorSourceDetail;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.SchedulingDoctorSourceInfo;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Operator;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.quartz.TriggerUtils;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

/**
 */
@Service
@Slf4j
public class ScheduleServiceImpl implements ScheduleService {

    private final ScheduleRepository scheduleRepository;
    private final DeptRepository deptRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final ExamScheduleGroupRepository examScheduleGroupRepository;
    private final ExamScheduleRepository examScheduleRepository;
    private final LockService lockService;
    private final ExamScheduleGroupDetailRepository examScheduleGroupDetailRepository;
    private final ExamDeviceRepository examDeviceRepository;
    private final ExamDeviceService examDeviceService;
    private final ScheduleGroupRepository scheduleGroupRepository;
    private final ScheduleGroupShiftRepository scheduleGroupShiftRepository;
    private final ScheduleUseRepository scheduleUseRepository;
    private final UserStatsRepository userStatsRepository;
    private final NursingConsulItemRepository nursingConsulItemRepository;
    private final EntityManager entityManager;
    private final ExamScheduleNumberSourceRepository examScheduleNumberSourceRepository;
    private final HospitalDictionaryRepository hospitalDictionaryRepository;
    private final ExamItemRepository examItemRepository;
    private final ExamDeviceMaintenanceRepository examDeviceMaintenanceRepository;
    private final ExamOrderRepository examOrderRepository;

    public ScheduleServiceImpl(ScheduleRepository scheduleRepository,
                               DeptRepository deptRepository,
                               MedicalWorkerRepository medicalWorkerRepository,
                               ExamScheduleGroupRepository examScheduleGroupRepository,
                               ExamScheduleRepository examScheduleRepository,
                               LockService lockService,
                               ExamScheduleGroupDetailRepository examScheduleGroupDetailRepository,
                               ExamDeviceRepository examDeviceRepository,
                               ExamDeviceService examDeviceService,
                               EntityManager entityManager,
                               ScheduleGroupRepository scheduleGroupRepository,
                               ScheduleGroupShiftRepository scheduleGroupShiftRepository,
                               UserStatsRepository userStatsRepository,
                               ExamScheduleNumberSourceRepository examScheduleNumberSourceRepository,
                               HospitalDictionaryRepository hospitalDictionaryRepository,
                               ExamItemRepository examItemRepository,
                               ExamDeviceMaintenanceRepository examDeviceMaintenanceRepository,
                               ExamOrderRepository examOrderRepository,
                               NursingConsulItemRepository nursingConsulItemRepository,
                               ScheduleUseRepository scheduleUseRepository) {
        this.scheduleRepository = scheduleRepository;
        this.deptRepository = deptRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.examScheduleGroupRepository = examScheduleGroupRepository;
        this.examScheduleRepository = examScheduleRepository;
        this.lockService = lockService;
        this.examScheduleGroupDetailRepository = examScheduleGroupDetailRepository;
        this.examDeviceRepository = examDeviceRepository;
        this.examDeviceService = examDeviceService;
        this.scheduleGroupRepository = scheduleGroupRepository;
        this.scheduleGroupShiftRepository = scheduleGroupShiftRepository;
        this.scheduleUseRepository = scheduleUseRepository;
        this.userStatsRepository = userStatsRepository;
        this.entityManager = entityManager;
        this.examScheduleNumberSourceRepository = examScheduleNumberSourceRepository;
        this.hospitalDictionaryRepository = hospitalDictionaryRepository;
        this.examItemRepository = examItemRepository;
        this.examDeviceMaintenanceRepository = examDeviceMaintenanceRepository;
        this.examOrderRepository = examOrderRepository;
        this.nursingConsulItemRepository = nursingConsulItemRepository;
    }

    private <T> T executeWithLock(String lockName, Callable<T> callable) {
        return lockService.executeWithLock(lockName, callable);
    }

    private String examDeviceLock(Long deviceId) {
        return "lock.device." + deviceId;
    }

    private String deptLock(OfflineDept offlineDept) {
        return "lock.dept." + offlineDept.getId();
    }

    @Override
    @Transactional
    public AbstractEntityDTO saveSchedule(User user, Hospital hospital, CreateScheduleGroupVM vm) {
        //加同步锁
        return lockService.executeWithLockThrowError("schedule." + hospital.getId() + user.getId(), () -> {
            Dept dept = vm.getDept() == null ? null : deptRepository.getById(vm.getDept().getId());
            if (dept != null && !Objects.equals(dept.getHospital(), hospital)) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("医院不正确");
            }
            NursingConsulItem consulItem = vm.getConsulItem() == null
                    ? null : nursingConsulItemRepository.getById(vm.getConsulItem().getId());

            MedicalWorker worker = null;
            if (vm.getMedicalWorker() != null && !vm.getMedicalWorker().isNew()) {
                worker = medicalWorkerRepository.getById(vm.getMedicalWorker().getId());
                if (!worker.isEnabled()) {
                    throw ErrorType.USER_IS_LOCKED.toProblem();
                }
                if (!worker.isOpenVisit()) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("用户已停诊");
                }
                if (!Objects.equals(worker.getHospital(), hospital)) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("医院不正确");
                }
            }
            ScheduleGroup group = saveScheduleGroup(vm, hospital, dept, consulItem, worker);
            List<ScheduleTimeVM> times = vm.getTimes();
            saveGroupShifts(group, times);

            MedicalWorker finalWorker = worker;
            List<Schedule> newSchedules = Lists.newArrayList();
            List<Tuple2<Date, Date>> tuples = Lists.newArrayList();
            List<Tuple2<Date, Date>> finalTuples = tuples;
            times.forEach(u -> {
                List<ScheduleTimeVM> scheduleTimes = separateTimeByInterval(u);
                List<Integer> repeatList = group.getRepeat();
                int[] repeatArr = new int[repeatList.size()];
                for (int i = 0; i < repeatList.size(); i++) {
                    repeatArr[i] = repeatList.get(i);
                }
                List<ScheduleTimeVM> scheduleTimeVMS = toCron(repeatArr, scheduleTimes);
                scheduleTimeVMS.forEach(t -> {
                    List<Date> startTime = calcTotalDates(t.getStart(), vm.getStartTime(), vm.getEndTime());
                    List<Date> endTime = calcTotalDates(t.getEnd(), vm.getStartTime(), vm.getEndTime());
                    if (startTime.isEmpty() || endTime.isEmpty()) {
                        return;
                    }
                    if (endTime.get(0).compareTo(vm.getStartTime()) <= 0) {
                        endTime = endTime.subList(1, endTime.size());
                    }
                    if (endTime.isEmpty() || startTime.get(0).compareTo(endTime.get(0)) >= 0) {
                        throw ErrorType.ILLEGAL_PARAMS.toProblem("时间格式不正确");
                    }
                    List<Date> dates = Lists.newArrayList();
                    dates.addAll(startTime);
                    dates.addAll(endTime);
                    dates = dates.stream().sorted().collect(Collectors.toList());
                    int count = dates.size();
                    Schedule.ScheduleType type = finalWorker == null ? ScheduleType.NORMAL : t.getType();
                    for (int i = 0; i < count - 1; i += 2) {
                        Date ds = dates.get(i);
                        Date de = dates.get(i + 1);
                        Schedule schedule = new Schedule(user);
                        schedule.setHospital(hospital);
                        schedule.setDept(dept);
                        schedule.setConsulItem(consulItem);
                        schedule.setMedicalWorker(finalWorker);
                        schedule.setStartTime(ds);
                        schedule.setEndTime(de);
                        if (type == ScheduleType.NORMAL && finalWorker == null ||
                            type == ScheduleType.EXPERT && finalWorker.isExpert() ||
                            type == ScheduleType.SPECIAL && finalWorker.isSpecial() ||
                            type == ScheduleType.ONLINE && finalWorker.isOnlineVisit()) {
                            schedule.setType(type);
                        } else {
                            throw ErrorType.ILLEGAL_PARAMS.toProblem("不支持的排班类型");
                        }
                        schedule.setInternetCount(t.getInternetCount());
                        schedule.setOnlineCount(t.getOnlineCount());
                        schedule.setPhoneCount(t.getPhoneCount());
                        schedule.setWindowCount(t.getWindowCount());
                        List<Specification<Schedule>> ls = Lists
                            .newArrayList(Specifications.eq("hospital", hospital),
                                          Specifications.lt("startTime", de),
                                          Specifications.gt("endTime", ds));
                        if (consulItem == null) {
                            ls.add(Specifications.isNull("consulItem"));
                        } else {
                            ls.add(Specifications.eq("consulItem", consulItem));
                        }
                        if (dept == null) {
                            ls.add(Specifications.isNull("dept"));
                        } else {
                            ls.add(Specifications.eq("dept", dept));
                        }
                        if (finalWorker == null) {
                            ls.add(Specifications.isNull("medicalWorker"));
                        } else {
                            ls.add(Specifications.eq("medicalWorker", finalWorker));
                        }
                        Specification<Schedule> spec = Specifications.and(ls);
                        List<Schedule> schedules = scheduleRepository.findAll(spec);
                        List<String> messages = Lists.newArrayList();
                        if (!schedules.isEmpty()) {
                            schedules.forEach(s -> {
                                messages.add(
                                    TimeUtils.dateToString(s.getStartTime()) + " - " + TimeUtils
                                        .dateToString(s.getEndTime()));
                            });
                            throw ErrorType.ILLEGAL_PARAMS
                                .toProblem("指定时间段内已有排班: " + StringUtils.joinWith(" , ", messages.toArray()));
                        }
                        finalTuples.add(Tuples.of(ds, de));
                        schedule.setGroup(group);
                        newSchedules.add(schedule);
                    }
                });
            });
            tuples = tuples.stream().sorted(Comparator.comparing(Tuple2::getT1)).collect(Collectors.toList());
            int count = tuples.size() - 1;
            for (int i = 0; i < count; i++) {
                if (tuples.get(i + 1).getT1().compareTo(tuples.get(i).getT2()) < 0) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("时间有重复");
                }
            }
            if (!newSchedules.isEmpty()) {
                scheduleRepository.saveAll(newSchedules);
            }
            return new AbstractEntityDTO(group);
        });
    }

    @Override
    @Transactional
    public void deleteSchedule(User user, Hospital hospital, long id) {
        Optional<ScheduleGroup> scheduleGroup = scheduleGroupRepository.findById(id);
        if (scheduleGroup.isEmpty()) {
            throw ErrorType.SCHEDULE_GROUP_BANK.toProblem("排班不存在，无法删除");
        }
        List<Specification<Schedule>> specifications = Lists.newArrayList(Specifications.eq("hospital", hospital));
        specifications.add(Specifications.eq("group", scheduleGroup.get()));
        List<Schedule> schedules = scheduleRepository.findAll(Specifications.and(specifications));
        if (getSchedule(schedules)) {
            throw ErrorType.SCHEDULE_HAS_USED.toProblem("排班下已有预约,不可删除");
        }
        if (!scheduleGroup.get().getShifts().isEmpty()) {
            scheduleGroupShiftRepository.deleteAll(scheduleGroup.get().getShifts());
        }
        scheduleGroupRepository.delete(scheduleGroup.get());
        List<ScheduleUse> scheduleUses = scheduleUseRepository.findAllByGroup(scheduleGroup.get());
        if (!scheduleUses.isEmpty()) {
            scheduleUseRepository.deleteAll(scheduleUses);
        }
        scheduleRepository.deleteAll(schedules);

    }

    @Override
    @Transactional
    public ScheduleGroupVM updateSchedule(User user, Hospital hospital,
                                          CreateScheduleGroupVM vm, long id) {
        deleteSchedule(user, hospital, id);
        saveSchedule(user, hospital, vm);
        return null;
    }

    private boolean getSchedule(List<Schedule> schedules) {
        boolean enable = false;
        for (Schedule schedule : schedules) {
            if (scheduleUseRepository.findOneBySchedule(schedule).isPresent()) {
                enable = true;
                break;
            }
        }
        return enable;
    }

    @Override
    public List<ScheduleDayVM> getScheduleDay(Hospital hospital, User user, String query, String onlineType) {
        boolean online = "online".equalsIgnoreCase(onlineType);
        List<Specification<Schedule>> specifications = Lists.newArrayList(Specifications.eq("hospital", hospital));
        specifications.add(ScheduleSearch.of(query, hospital, user).toSpecification());
        List<Schedule> schedules = scheduleRepository.findAll(Specifications.and(specifications))
            .stream().filter(u -> {
                if (u.getMedicalWorker() != null) {
                    return u.getMedicalWorker().isOpenVisit() && u.getMedicalWorker().isEnabled();
                }
                return true;
            })
            .collect(Collectors.toList());
        HashMultimap<String, Schedule> map = HashMultimap.create();
        schedules.forEach(s -> {
            if (s.getMedicalWorker() == null) {
                map.put("" + s.getDept().getId(), s);
            } else {
                map.put(s.getDept().getId() + "," + s.getMedicalWorker().getId(), s);
            }
        });
        List<ScheduleDayVM> scheduleDays = Lists.newArrayList();
        map.asMap().forEach((k, v) -> {
            Schedule schedule = v.stream().findFirst().get();
            ScheduleDayVM scheduleDay = new ScheduleDayVM(schedule.getDept());
            if (schedule.getMedicalWorker() != null) {
                UserStats stats = userStatsRepository.findOneByUser(schedule.getMedicalWorker().getUser())
                    .orElse(new UserStats());
                scheduleDay.setMedicalWorker(new DoctorVM(schedule.getMedicalWorker(), stats));
            }

            if (schedule.getMedicalWorker() != null) {
                DoctorFee doctorFee = schedule.getMedicalWorker().getDoctorFee();
                scheduleDay.setExpertFee(doctorFee.getExpertFee());
                scheduleDay.setSpecialFee(doctorFee.getSpecialFee());
                scheduleDay.setOnlineConsultFee(doctorFee.getGraphicConsultFee());
                scheduleDay.setOnlineOutFee(doctorFee.getReturnVisitFee());
                scheduleDay.setOnlineVideoConsultFee(doctorFee.getVideoConsultFee());
                scheduleDay.setOnlineVideoOutFee(doctorFee.getVideoReturnVisitFee());
            }
            v.forEach(s -> {
                // 是否已满, 只要有1个不满, 就认为是不满
                int count;
                int used;
                Optional<ScheduleUse> sUsed = scheduleUseRepository.findOneBySchedule(s);
                if (online) {
                    count = s.getOnlineCount();
                    used = sUsed.map(ScheduleUse::getOnlineUsedCount).orElse(0);
                } else {
                    count = s.getInternetCount();
                    used = sUsed.map(ScheduleUse::getInternetUsedCount).orElse(0);
                }
                if (used < count) {
                    switch (s.getType()) {
                        case NORMAL:
                            scheduleDay.setNormalIsFull(false);
                            break;
                        case SPECIAL:
                            scheduleDay.setSpecialIsFull(false);
                            break;
                        case EXPERT:
                            scheduleDay.setExpertIsFull(false);
                            break;
                        case ONLINE:
                            scheduleDay.setOnlineIsFull(false);
                            break;
                        default:
                    }
                }

                switch (s.getType()) {
                    case EXPERT:
                        scheduleDay.setHaveExpert(true);
                        break;
                    case NORMAL:
                        scheduleDay.setHaveNormal(true);
                        break;
                    case SPECIAL:
                        scheduleDay.setHaveSpecial(true);
                        break;
                    case ONLINE:
                        scheduleDay.setHaveOnline(true);
                        break;
                    default:
                }
            });
            scheduleDays.add(scheduleDay);
        });
        return scheduleDays;
    }

    @Override
    @Transactional
    public void deleteExamScheduleGroup(User user, ExamScheduleGroup group) {
        List<ExamDevice> groupDevices = examDeviceService.getGroupDevices(group.getDevices());
        if (CollectionUtils.isNotEmpty(groupDevices)) {
            throw ErrorType.SCHEDULE_HAS_USED.toProblem("该计划含有绑定设备，请解绑后再删除");
        }
        examScheduleGroupRepository.delete(group);
    }

    @Override
    @Transactional
    public ExamScheduleGroup updateExamScheduleGroup(User user, ExamScheduleGroup group, CreateExamScheduleGroupVM vm) {
        List<ExamDevice> groupDevices = examDeviceService.getGroupDevices(group.getDevices());
        if (CollectionUtils.isNotEmpty(groupDevices)) {
            throw ErrorType.SCHEDULE_HAS_USED.toProblem("该计划含有绑定设备，请解绑后再编辑");
        }
        group.setCreator(user);
        group.setName(vm.getName());
        group.setStartTime(vm.getStartTime());
        group.setEndTime(vm.getEndTime());
        group.setModel(vm.getModel());
        group.setRepeat(Arrays.stream(vm.getRepeat()).boxed().collect(Collectors.toList()));
        List<ExamScheduleGroupShiftTimeVM> times = vm.getTimes();
        examScheduleGroupDetailRepository.deleteByGroup(group);
        saveGroupDetails(group, times);
        examScheduleGroupRepository.save(group);
        return group;
    }

    @Override
    @Transactional
    public ExamScheduleGroup saveExamDeviceSchedule(User user, Hospital hospital, ExamScheduleGroup group,
                                                    List<ExamDeviceNumberSourceVM> examDeviceNumberSources) {
        // 前端传过来的排班设备
        List<Long> deviceIds = examDeviceNumberSources.stream().map(ExamDeviceNumberSourceVM::getDeviceId)
                .collect(Collectors.toList());
        // 当前排班计划所绑定的排班设备
        List<Long> deviceIdsInGroup = group.getDevices();
        List<Long> toDeleteDeviceIds = Lists.newArrayList();
        deviceIdsInGroup.forEach(u -> {
            if (!deviceIds.contains(u)) {
                toDeleteDeviceIds.add(u);
            }
        });
        // 解绑排班设备
        if (CollectionUtils.isNotEmpty(toDeleteDeviceIds)) {
            List<ExamDevice> toDeleteDevices = examDeviceService.getGroupDevices(toDeleteDeviceIds);
            deleteExamScheduleByExamDevice(toDeleteDevices, group);
        }
        // 排班计划绑定设备
        if (CollectionUtils.isNotEmpty(examDeviceNumberSources)) {
            examDeviceNumberSources.forEach(examDeviceNumberSourceVM -> {
                executeWithLock(examDeviceLock(examDeviceNumberSourceVM.getDeviceId()),
                        () -> saveExamDeviceScheduleWithLock(user, hospital, group, examDeviceNumberSourceVM));
            });
        }
        group.setDevices(deviceIds);
        examScheduleGroupRepository.save(group);
        return group;
    }

    @Override
    @Transactional
    public List<Long> saveSchedulePlan(User user, Hospital hospital, CreateExamScheduleGroupVM vm, OfflineDept offlineDept) {
        return executeWithLock(deptLock(offlineDept), () -> saveSchedulePlanWithLock(user, hospital, vm, offlineDept));
    }

    @Override
    public Page<ExamScheduleGroupVM> pageExamSchedulePlan(User user, Hospital hospital, String query, Pageable page) {
        List<Specification<ExamScheduleGroup>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(ExamScheduleGroupSearch.of(query).toSpecification());
        return examScheduleGroupRepository.findAll(Specifications.and(specs), page)
                .map(u -> getExamSchedulePlanDetail(u, u.getDevices(), false));
    }

    @Override
    public ExamScheduleGroupVM getExamSchedulePlanDetail(ExamScheduleGroup group, List<Long> deviceIds, boolean needDetail) {
        ExamScheduleGroupVM planVM = new ExamScheduleGroupVM(group);
        // 排班计划开始时间
        Date startTime = group.getStartTime();
        // 排班计划结束时间
        Date endTime = group.getEndTime();
        List<Date> dateList = TimeUtils.getBetweenDates(startTime, endTime);
        List<ExamScheduleGroupDetail> details = group.getDetails();
        // 获取当前绑定的设备
        List<ExamDevice> groupDevices = examDeviceService.getGroupDevices(deviceIds);
        // 当前计划班次详情
        List<ExamScheduleGroupShiftVM> examSchedulePlanDetailVMS = details.stream().map(detail -> {
            ExamScheduleGroupShiftVM detailVM = new ExamScheduleGroupShiftVM(detail);
            ExamScheduleGroupShiftTimeVM timeVM = new ExamScheduleGroupShiftTimeVM(detail);
            List<ExamScheduleGroupShiftTimeVM> times = separateTimeByInterval(timeVM);
            // 初始化整个排班周期内的上午/下午/晚上总可约数
            int shiftCount = detailVM.getCount();
            int time = 0;
            if (shiftCount != 0 && CollectionUtils.isNotEmpty(times)) {
                time = shiftCount / times.size();
            }
            int finalTime = time;
            if (needDetail) {
//                times.forEach(u -> {
//                    u.setCount(finalTime);
//                });
                detailVM.setTimes(times);
            }
            // 初始化上午/下午/晚上单个时间段的可约数
            detailVM.setEach(finalTime);
            if (!needDetail) {
                int finalTotalCount = 0;
                // 实际整个排班周期内的上午/下午/晚上总可约数
                for (Date date : dateList) {
                    Date startDate = TimeUtils.combineDateAndTime(date, detail.getStart());
                    Date endDate = TimeUtils.combineDateAndTime(date, detail.getEnd());
                    List<Specification<ExamSchedule>> specs = Lists.newArrayList();
                    specs.add(Specifications.eq("scheduleGroup", group));
                    if (groupDevices.size() == 1) {
                        specs.add(Specifications.eq("device", groupDevices.get(0)));
                    }
                    specs.add(Specifications.ge("startTime", startDate));
                    specs.add(Specifications.le("endTime", endDate));
                    List<ExamSchedule> examSchedules = examScheduleRepository.findAll(Specifications.and(specs));
                    finalTotalCount += examSchedules.stream()
                            .flatMap(examSchedule -> examSchedule.getNumberSourceList().stream())
                            .mapToInt(ExamScheduleNumberSource::getCount)
                            .sum();
                }
                detailVM.setCount(finalTotalCount);
            }
            return detailVM;
        }).collect(Collectors.toList());
        planVM.setDetails(examSchedulePlanDetailVMS);
        // 整个排班计划的总可预约与已约数
        List<Integer> countFromGroup = getCountFromGroup(group, groupDevices);
        planVM.setCount(countFromGroup.get(0));
        planVM.setCountUse(countFromGroup.get(1));
        if (needDetail) {
            List<ExamScheduleVM> examScheduleVMS = Lists.newArrayList();
            for (ExamDevice device : groupDevices) {
                // 排班计划绑定的排班设备
                List<ExamSchedule> examSchedules = examScheduleRepository.findAllByDeviceAndScheduleGroup(device, group);
                for (ExamSchedule examSchedule : examSchedules) {
                    ExamScheduleVM examScheduleVM = getExamScheduleVM(examSchedule);
                    examScheduleVMS.add(examScheduleVM);
                }
            }
            planVM.setExamSchedules(examScheduleVMS);
        } else {
            planVM.setDevices(groupDevices.stream().map(ExamDeviceVM::new).collect(Collectors.toList()));
        }
        return planVM;
    }

    @NotNull
    private static ExamScheduleVM getExamScheduleVM(ExamSchedule examSchedule) {
        ExamScheduleVM examScheduleVM = new ExamScheduleVM(examSchedule, null);
        List<NumberSourceVM> numberSourceVMS = Lists.newArrayList();
        for (ExamScheduleNumberSource examScheduleNumberSource : examSchedule.getNumberSourceList()) {
            NumberSourceVM numberSourceVM = new NumberSourceVM(examScheduleNumberSource);
            numberSourceVMS.add(numberSourceVM);
        }
        examScheduleVM.setNumberSourceList(numberSourceVMS);
        return examScheduleVM;
    }

    @Override
    public ExamScheduleGroupVM getExamSchedulePlanDetail(CreateExamScheduleGroupVM vm) {
        // 排班计划预览信息
        ExamScheduleGroupVM planVM = new ExamScheduleGroupVM(vm);
        List<ExamScheduleGroupShiftVM> planDetailVMS = vm.getTimes().stream().map(u -> {
            ExamScheduleGroupShiftVM shiftDetail = new ExamScheduleGroupShiftVM();
            List<ExamScheduleGroupShiftTimeVM> scheduleTimes = separateTimeByInterval(u);
            shiftDetail.setTimes(scheduleTimes);
            shiftDetail.setShift(TimeUtils.getShift(u.getStart(), u.getEnd()));
            shiftDetail.setStart(u.getStart());
            shiftDetail.setEnd(u.getEnd());
            int count = 0;
            if (CollectionUtils.isNotEmpty(scheduleTimes)) {
                count = scheduleTimes.size() * u.getCount();
            }
            shiftDetail.setEach(u.getCount());
            shiftDetail.setCount(count);
            return shiftDetail;
        }).collect(Collectors.toList());
        planVM.setDetails(planDetailVMS);
        return planVM;
    }

    @Override
    public ScheduleGroupVM getSchedulePlanDetail(CreateScheduleGroupVM vm) {
        ScheduleGroupVM planVM = new ScheduleGroupVM(vm);
        List<ScheduleGroupShiftVM> planDetailVMS = vm.getTimes().stream().map(u -> {
            ScheduleGroupShiftVM shiftVM = new ScheduleGroupShiftVM();
            List<ScheduleTimeVM> scheduleTimes = separateTimeByInterval(u);
            shiftVM.setTimes(scheduleTimes);
            shiftVM.setShift(TimeUtils.getShift(u.getStart(), u.getEnd()));
            shiftVM.setStart(u.getStart());
            shiftVM.setEnd(u.getEnd());
            int size = scheduleTimes.size();
            int internetCount = size * u.getInternetCount();
            int onlineCount = size * u.getOnlineCount();
            int phoneCount = size * u.getPhoneCount();
            int windowCount = size * u.getWindowCount();
            shiftVM.setInternetEach(u.getInternetCount());
            shiftVM.setOnlineEach(u.getOnlineCount());
            shiftVM.setPhoneEach(u.getPhoneCount());
            shiftVM.setWindowEach(u.getWindowCount());
            shiftVM.setInternetCount(internetCount);
            shiftVM.setOnlineCount(onlineCount);
            shiftVM.setPhoneCount(phoneCount);
            shiftVM.setWindowCount(windowCount);
            shiftVM.setCount(internetCount + onlineCount + phoneCount + windowCount);

            return shiftVM;
        }).collect(Collectors.toList());
        planVM.setDetails(planDetailVMS);
        return planVM;
    }

    @Override
    public ScheduleGroupVM getSchedulePlanDetail(ScheduleGroup group, boolean needDetail) {
        ScheduleGroupVM planVM = new ScheduleGroupVM(group);
        List<ScheduleGroupShift> details = group.getShifts();
        List<ScheduleGroupShiftVM> SchedulePlanDetailVMS = details.stream().map(detail -> {
            ScheduleGroupShiftVM detailVM = new ScheduleGroupShiftVM(detail);
            if (needDetail) {
                ScheduleTimeVM timeVM = new ScheduleTimeVM(detail);
                timeVM.setType(group.getType());
                List<ScheduleTimeVM> times = separateTimeByInterval(timeVM);
                detailVM.setTimes(times);
            }
            return detailVM;
        }).collect(Collectors.toList());
        planVM.setDetails(SchedulePlanDetailVMS);
        planVM.setCount(getCountFromGroup(group));
        planVM.setCountUse(getCountUseFromGroup(group));
        return planVM;
    }

    private List<Long> saveSchedulePlanWithLock(User user, Hospital hospital, CreateExamScheduleGroupVM vm,
                                                OfflineDept offlineDept) {
        if (!Objects.equals(offlineDept.getHospital(), hospital)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("医院不正确");
        }
        List<ExamScheduleGroupShiftTimeVM> times = vm.getTimes();
        ExamScheduleGroup group = new ExamScheduleGroup();
        group.setOfflineDept(offlineDept);
        group.setCreator(user);
        group.setHospital(hospital);
        group.setName(vm.getName());
        group.setStartTime(vm.getStartTime());
        group.setEndTime(vm.getEndTime());
        group.setModel(vm.getModel());
        group.setRepeat(Arrays.stream(vm.getRepeat()).boxed().collect(Collectors.toList()));
        examScheduleGroupRepository.save(group);
        examScheduleGroupDetailRepository.deleteByGroup(group);
        saveGroupDetails(group, times);
        return Lists.newArrayList(group.getId());
    }

    private List<Long> saveExamDeviceScheduleWithLock(User user, Hospital hospital, ExamScheduleGroup group,
                                                      ExamDeviceNumberSourceVM examDeviceNumberSourceVM) {
        // 排班计划周期开始时间
        Date scheduleStartTime = group.getStartTime();
        // 排班计划周期结束时间
        Date scheduleEndTime = group.getEndTime();
        ExamDevice device = examDeviceRepository.getById(examDeviceNumberSourceVM.getDeviceId());
        if (!Objects.equals(device.getHospital(), hospital)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("医院不正确");
        }
        if (!device.isEnabled()) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem(device.getName() + "设备已被禁用，不可绑定");
        }
        List<Integer> repeatList = group.getRepeat();
        int[] repeatArr = new int[repeatList.size()];
        for (int i = 0; i < repeatList.size(); i++) {
            repeatArr[i] = repeatList.get(i);
        }
        List<Tuple2<Date, Date>> tuples = Lists.newArrayList();
        List<Tuple2<Date, Date>> finalTuples = tuples;
        List<String> errorMessages = Lists.newArrayList();
        examDeviceNumberSourceVM.getTimeSlotNumberSources().forEach(timeSlotNumberSource  -> {
            List<ExamScheduleGroupShiftTimeVM> scheduleTimes = Collections
                    .singletonList(new ExamScheduleGroupShiftTimeVM(timeSlotNumberSource));
            List<ExamScheduleGroupShiftTimeVM> scheduleTimeVMS = toCronForExamDevice(repeatArr, scheduleTimes);
            scheduleTimeVMS.forEach(t -> {
                // 获取排版计划周期中的每个时间段
                List<Date> startTime = calcTotalDates(t.getStart(), scheduleStartTime, scheduleEndTime);
                List<Date> endTime = calcTotalDates(t.getEnd(), scheduleStartTime, scheduleEndTime);
                if (startTime.isEmpty() || endTime.isEmpty()) {
                    return;
                }
                if (endTime.get(0).compareTo(group.getStartTime()) <= 0) {
                    endTime = endTime.subList(1, endTime.size());
                }
                if (endTime.isEmpty() || startTime.get(0).compareTo(endTime.get(0)) >= 0) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("时间格式不正确");
                }
                List<Date> dates = Lists.newArrayList();
                dates.addAll(startTime);
                dates.addAll(endTime);
                dates = dates.stream().sorted().collect(Collectors.toList());
                int count = dates.size();
                for (int i = 0; i < count - 1; i += 2) {
                    Date ds = dates.get(i);
                    Date de = dates.get(i + 1);
                    finalTuples.add(Tuples.of(ds, de));
                    List<Specification<ExamSchedule>> commonSpecs = Lists.newArrayList(
                            Specifications.eq("hospital", hospital),
                            Specifications.eq("device", device)
                    );
                    // 查看不是绑定在当前排班计划的设备是否有排班
                    List<Specification<ExamSchedule>> exitedSpecs = Lists.newArrayList();
                    exitedSpecs.addAll(commonSpecs);
                    exitedSpecs.add(Specifications.lt("startTime", de));
                    exitedSpecs.add(Specifications.gt("endTime", ds));
                    exitedSpecs.add(Specifications.not(Specifications.eq("scheduleGroup", group)));
                    List<ExamSchedule> exitedSchedules = examScheduleRepository.findAll(Specifications.and(exitedSpecs));
                    if (CollectionUtils.isNotEmpty(exitedSchedules)) {
                        log.info("【" + ds + "】 到 【" + de + "】内的排班数量为：{}", exitedSchedules.size());
                        List<String> messages = Lists.newArrayList();
                        exitedSchedules.forEach(s -> messages.add(
                                TimeUtils.dateToString(s.getStartTime()) + " - " + TimeUtils.dateToString(s.getEndTime())));
                        throw ErrorType.ILLEGAL_PARAMS
                                .toProblem("指定时间段内已有排班: " + StringUtils.joinWith(" , ", messages.toArray()));
                    }
                    List<Specification<ExamSchedule>> currentSpecs = Lists.newArrayList();
                    currentSpecs.addAll(commonSpecs);
                    currentSpecs.add(Specifications.eq("startTime", ds));
                    currentSpecs.add(Specifications.eq("endTime", de));
                    currentSpecs.add(Specifications.eq("scheduleGroup", group));
                    List<ExamSchedule> schedules = examScheduleRepository.findAll(Specifications.and(currentSpecs));
                    if (CollectionUtils.isEmpty(schedules)) {
                        ExamSchedule newSchedule = new ExamSchedule(user);
                        newSchedule.setHospital(hospital);
                        newSchedule.setDevice(device);
                        newSchedule.setStartTime(ds);
                        newSchedule.setEndTime(de);
                        newSchedule.setScheduleGroup(group);
                        ExamSchedule examSchedule = examScheduleRepository.saveAndFlush(newSchedule);
                        List<NumberSourceVM> numberSourceList = timeSlotNumberSource.getNumberSourceList();
                        List<ExamScheduleNumberSource> newNumberSourceList = Lists.newArrayList();
                        for (NumberSourceVM numberSourceVM : numberSourceList) {
                            HospitalDictionary hospitalDictionary = hospitalDictionaryRepository
                                    .getById(numberSourceVM.getHospitalDictionaryId());
                            ExamScheduleNumberSource examScheduleNumberSource = new ExamScheduleNumberSource();
                            examScheduleNumberSource.setExamSchedule(examSchedule);
                            examScheduleNumberSource.setDictionary(hospitalDictionary);
                            examScheduleNumberSource.setCount(numberSourceVM.getCount());
                            newNumberSourceList.add(examScheduleNumberSource);
                        }
                        List<ExamScheduleNumberSource> examScheduleNumberSourceList = examScheduleNumberSourceRepository
                                .saveAll(newNumberSourceList);
                        examSchedule.setNumberSourceList(examScheduleNumberSourceList);
                        examScheduleRepository.save(examSchedule);
                    }
                    if (schedules.size() == 1) {
                        // 之前已经绑定的设备
                        ExamSchedule examSchedule = schedules.get(0);
                        List<NumberSourceVM> numberSourceList = timeSlotNumberSource.getNumberSourceList();
                        // 前端传过来的渠道号源
                        List<Long> dictionaryIds = numberSourceList.stream().map(NumberSourceVM::getHospitalDictionaryId).collect(Collectors.toList());
                        List<ExamScheduleNumberSource> examScheduleNumberSources = examScheduleNumberSourceRepository.findAllByExamSchedule(examSchedule);
                        List<Long> existedDictionaryIds = examScheduleNumberSources.stream()
                                .map(examScheduleNumberSource -> examScheduleNumberSource.getDictionary().getId())
                                .collect(Collectors.toList());
                        List<Long> toDeleteDictionaryIds = existedDictionaryIds.stream()
                                .filter(id -> !dictionaryIds.contains(id))
                                .collect(Collectors.toList());
                        if (!toDeleteDictionaryIds.isEmpty()) {
                            List<HospitalDictionary> hospitalDictionaryList = toDeleteDictionaryIds
                                    .stream().map(dictionaryId -> hospitalDictionaryRepository.findById(dictionaryId).orElse(null))
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());
                            List<ExamScheduleNumberSource> toDeleteNumberSourceList = Lists.newArrayList();
                            for (HospitalDictionary hospitalDictionary : hospitalDictionaryList) {
                                examScheduleNumberSourceRepository
                                        .findByExamScheduleAndDictionary(examSchedule, hospitalDictionary)
                                        .ifPresent(toDeleteNumberSourceList::add);
                            }
                            if (CollectionUtils.isNotEmpty(toDeleteNumberSourceList)) {
                                examScheduleNumberSourceRepository.deleteAll(toDeleteNumberSourceList);
                            }
                        }
                        List<ExamScheduleNumberSource> updatedNumberSources = Lists.newArrayList();
                        for (NumberSourceVM numberSourceVM : numberSourceList) {
                            HospitalDictionary hospitalDictionary = hospitalDictionaryRepository
                                    .getById(numberSourceVM.getHospitalDictionaryId());
                            ExamScheduleNumberSource numberSource = examScheduleNumberSourceRepository
                                    .findByExamScheduleAndDictionary(examSchedule, hospitalDictionary).orElse(null);
                            if (numberSource != null) {
                                if (numberSourceVM.getCount() < numberSource.getUsedCount()) {
                                    errorMessages.add(TimeUtils.dateToString(ds) + " - " + TimeUtils.dateToString(de));
                                }
                                if (CollectionUtils.isEmpty(errorMessages)) {
                                    numberSource.setCount(numberSourceVM.getCount());
                                    updatedNumberSources.add(numberSource);
                                }
                            } else {
                                ExamScheduleNumberSource examScheduleNumberSource = new ExamScheduleNumberSource();
                                examScheduleNumberSource.setExamSchedule(examSchedule);
                                examScheduleNumberSource.setDictionary(hospitalDictionary);
                                examScheduleNumberSource.setCount(numberSourceVM.getCount());
                                updatedNumberSources.add(examScheduleNumberSource);
                            }
                            if (CollectionUtils.isNotEmpty(errorMessages)) {
                                throw ErrorType.ILLEGAL_PARAMS
                                        .toProblem("指定时间段内排班数量不能小于已约数量: " + StringUtils.joinWith(" , ", errorMessages.toArray()));
                            }
                        }
                        if (CollectionUtils.isNotEmpty(updatedNumberSources)) {
                            List<ExamScheduleNumberSource> examScheduleNumberSourceList = examScheduleNumberSourceRepository
                                    .saveAll(updatedNumberSources);
                            examSchedule.setNumberSourceList(examScheduleNumberSourceList);
                            examScheduleRepository.save(examSchedule);
                        }
                    }
                    if (schedules.size() > 1) {
                        throw ErrorType.ILLEGAL_PARAMS.toProblem("指定时间段内排班时间不正确");
                    }
                }
            });
        });
        tuples = tuples.stream().sorted(Comparator.comparing(Tuple2::getT1)).collect(Collectors.toList());
        int count = tuples.size() - 1;
        for (int i = 0; i < count; i++) {
            if (tuples.get(i + 1).getT1().compareTo(tuples.get(i).getT2()) < 0) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("时间有重复");
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 返回值中的start和end分别都变成cron表达式，把排班周期根据选中的循环日设置那些周期需要排班
     * @param repeat
     * @param times
     * @return
     */
    private List<ExamScheduleGroupShiftTimeVM> toCronForExamDevice(int[] repeat, List<ExamScheduleGroupShiftTimeVM> times) {
        String weekdays = StringUtils.join(repeat, ',');
        int[] tomorrow = new int[repeat.length];
        for (int i = 0; i < repeat.length; i++) {
            tomorrow[i] = 1 + ((repeat[i]) % 7);
        }
        String weekdaysT = StringUtils.join(tomorrow, ',');
        for (ExamScheduleGroupShiftTimeVM time : times) {
            String[] timeStrS = (time.getStart() + ":0:0").split(":");
            timeStrS = Arrays.copyOf(timeStrS, 3);
            time.setStart(timeStrS[2] + " " + timeStrS[1] + " " + timeStrS[0] + " ? * " + weekdays);
            String[] timeStrE = (time.getEnd() + ":0:0").split(":");
            timeStrE = Arrays.copyOf(timeStrE, 3);
            if ("24".equals(timeStrE[0])) {
                timeStrE[0] = "0";
                time.setEnd(MessageFormat
                    .format("{0} {1} {2} ? * {3}", timeStrE[2], timeStrE[1], timeStrE[0],
                        weekdaysT));
            } else {
                Date d1 = (Date) DataTypes.DATE.fromString("2020-01-01 "
                    + StringUtils.join(timeStrS, ':'), "yyyy-MM-dd H:m:s");
                Date d2 = (Date) DataTypes.DATE.fromString("2020-01-01 "
                    + StringUtils.join(timeStrE, ':'), "yyyy-MM-dd H:m:s");
                if (d1 != null && d2 != null && d1.compareTo(d2) >= 0) {
                    time.setEnd(MessageFormat
                        .format("{0} {1} {2} ? * {3}", timeStrE[2], timeStrE[1], timeStrE[0],
                            weekdaysT));
                } else {
                    time.setEnd(MessageFormat
                        .format("{0} {1} {2} ? * {3}", timeStrE[2], timeStrE[1], timeStrE[0],
                            weekdays));
                }
            }
        }
        return times;
    }

    /**
     * 返回cron表达式从startDate到endDate中，cron触发的所有时间
     * @param cron
     * @param startDate
     * @param endDate
     * @return
     */
    private static List<Date> calcTotalDates(String cron, Date startDate, Date endDate) {
        CronTriggerImpl cronTriggerImpl = new CronTriggerImpl();
        try {
            cronTriggerImpl.setCronExpression(cron);
            return TriggerUtils.computeFireTimesBetween(cronTriggerImpl, null, startDate, endDate);
        } catch (ParseException e) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("时间格式或重复格式不正确");
        }
    }

    /**
     * 返回值中的start和end分别都变成cron表达式
     * @param repeat
     * @param times
     * @return
     */
    private List<ScheduleTimeVM> toCron(int[] repeat, List<ScheduleTimeVM> times) {
        String weekdays = StringUtils.join(repeat, ',');
        int[] tomorrow = new int[repeat.length];
        for (int i = 0; i < repeat.length; i++) {
            tomorrow[i] = 1 + ((repeat[i]) % 7);
        }
        String weekdaysT = StringUtils.join(tomorrow, ',');
        for (ScheduleTimeVM time : times) {
            String[] timeStrS = (time.getStart() + ":0:0").split(":");
            timeStrS = Arrays.copyOf(timeStrS, 3);
            time.setStart(timeStrS[2] + " " + timeStrS[1] + " " + timeStrS[0] + " ? * " + weekdays);
            String[] timeStrE = (time.getEnd() + ":0:0").split(":");
            timeStrE = Arrays.copyOf(timeStrE, 3);
            if ("24".equals(timeStrE[0])) {
                timeStrE[0] = "0";
                time.setEnd(MessageFormat
                    .format("{0} {1} {2} ? * {3}", timeStrE[2], timeStrE[1], timeStrE[0],
                        weekdaysT));
            } else {
                Date d1 = (Date) DataTypes.DATE.fromString("2020-01-01 "
                    + StringUtils.join(timeStrS, ':'), "yyyy-MM-dd H:m:s");
                Date d2 = (Date) DataTypes.DATE.fromString("2020-01-01 "
                    + StringUtils.join(timeStrE, ':'), "yyyy-MM-dd H:m:s");
                if (d1 != null && d2 != null && d1.compareTo(d2) >= 0) {
                    time.setEnd(MessageFormat
                        .format("{0} {1} {2} ? * {3}", timeStrE[2], timeStrE[1], timeStrE[0],
                            weekdaysT));
                } else {
                    time.setEnd(MessageFormat
                        .format("{0} {1} {2} ? * {3}", timeStrE[2], timeStrE[1], timeStrE[0],
                            weekdays));
                }
            }
        }
        return times;
    }

    private List<ExamScheduleGroupShiftTimeVM> separateTimeByInterval(ExamScheduleGroupShiftTimeVM time) {
        List<ExamScheduleGroupShiftTimeVM> separateTimes = Lists.newArrayList();
        String start = time.getStart();
        String end = time.getEnd();
        int interval = time.getInterval();
        if (interval != 60 && interval != 30 && interval != 0) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("间隔（单位：分钟）只能是0 30 60");
        }
        if (interval > 0) {
            Date startTime = (Date) DataTypes.DATE.fromString(start, "HH:mm");
            Date endTime = (Date) DataTypes.DATE.fromString(end, "HH:mm");
            if (startTime == null || endTime == null || startTime.getTime() >= endTime.getTime()) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("时间格式不正确");
            }
            while (startTime.getTime() < endTime.getTime()) {
                ExamScheduleGroupShiftTimeVM vm = new ExamScheduleGroupShiftTimeVM();
                vm.setCount(time.getCount());
                vm.setInterval(time.getInterval());
                vm.setStart(DataTypes.DATE.asString(startTime, "HH:mm"));
                startTime = DateUtils.addMinutes(startTime, interval);
                if (startTime.getTime() > endTime.getTime()) {
                    vm.setEnd(DataTypes.DATE.asString(endTime, "HH:mm"));
                } else {
                    vm.setEnd(DataTypes.DATE.asString(startTime, "HH:mm"));
                }
                separateTimes.add(vm);
            }
        } else {
            separateTimes.add(time);
        }
        return separateTimes;
    }

    private List<ScheduleTimeVM> separateTimeByInterval(ScheduleTimeVM time) {
        List<ScheduleTimeVM> separateTimes = Lists.newArrayList();
        String start = time.getStart();
        String end = time.getEnd();
        int interval = time.getInterval();
        if (interval != 60 && interval != 30 && interval != 0) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("间隔（单位：分钟）只能是0 30 60");
        }
        if (interval > 0) {
            Date startTime = (Date) DataTypes.DATE.fromString(start, "HH:mm");
            Date endTime = (Date) DataTypes.DATE.fromString(end, "HH:mm");

            if (startTime == null || endTime == null || startTime.getTime() >= endTime.getTime()) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("时间格式不正确");
            }
            while (startTime.getTime() < endTime.getTime()) {
                ScheduleTimeVM vm = new ScheduleTimeVM();
                vm.setOnlineCount(time.getOnlineCount());
                vm.setInternetCount(time.getInternetCount());
                vm.setPhoneCount(time.getPhoneCount());
                vm.setWindowCount(time.getWindowCount());
                vm.setInterval(time.getInterval());
                vm.setType(time.getType());
                vm.setStart(DataTypes.DATE.asString(startTime, "HH:mm"));
                startTime = DateUtils.addMinutes(startTime, interval);
                if (startTime.getTime() > endTime.getTime()) {
                    vm.setEnd(DataTypes.DATE.asString(endTime, "HH:mm"));
                } else {
                    vm.setEnd(DataTypes.DATE.asString(startTime, "HH:mm"));
                }
                separateTimes.add(vm);
            }
        } else {
            separateTimes.add(time);
        }
        return separateTimes;
    }


    private void deleteExamScheduleByExamDevice(List<ExamDevice> devices, ExamScheduleGroup group) {
        devices.forEach(u -> {
            List<ExamSchedule> schedules = examScheduleRepository.findAllByDeviceAndScheduleGroup(u, group);
            schedules.forEach(s -> {
                if (s.getNumberSourceList().stream().anyMatch(numberSource -> numberSource.getUsedCount() > 0)) {
                    throw ErrorType.SCHEDULE_HAS_USED.toProblem(u.getName() + "设备下排班已有预约，无法解绑");
                }
                // 跟着排班设备走的，将该排班设备下的所有号源排班分配信息删除
                examScheduleNumberSourceRepository.deleteAll(examScheduleNumberSourceRepository.findAllByExamSchedule(s));
            });
            examScheduleRepository.deleteAll(schedules);
        });
    }

    private void saveGroupDetails(ExamScheduleGroup group, List<ExamScheduleGroupShiftTimeVM> times) {
        times.forEach(u -> {
            ExamScheduleGroupDetail detail = new ExamScheduleGroupDetail();
            detail.setStart(u.getStart());
            detail.setEnd(u.getEnd());
            // 时间段按间隔分割后的list
            List<ExamScheduleGroupShiftTimeVM> separators = separateTimeByInterval(u);
            detail.setCount(separators.size() * u.getCount());
            detail.setInterval(u.getInterval());
            detail.setGroup(group);
            examScheduleGroupDetailRepository.save(detail);
        });
    }

    private void saveGroupShifts(ScheduleGroup group, List<ScheduleTimeVM> times) {
        times.forEach(u -> {
            ScheduleGroupShift shift = new ScheduleGroupShift();
            shift.setStart(u.getStart());
            shift.setEnd(u.getEnd());
            // 时间段按间隔分割后的list
            List<ScheduleTimeVM> separators = separateTimeByInterval(u);
            int size = separators.size();
            shift.setInternetEach(u.getInternetCount());
            shift.setOnlineEach(u.getOnlineCount());
            shift.setPhoneEach(u.getPhoneCount());
            shift.setWindowEach(u.getWindowCount());
            shift.setInternetCount(size * u.getInternetCount());
            shift.setOnlineCount(size * u.getOnlineCount());
            shift.setPhoneCount(size * u.getPhoneCount());
            shift.setWindowCount(size * u.getWindowCount());
            shift.setInterval(u.getInterval());
            shift.setGroup(group);
            scheduleGroupShiftRepository.save(shift);
        });
    }

    private List<Integer> getCountFromGroup(ExamScheduleGroup group, List<ExamDevice> devices) {
        List<Integer> countFromGroupList = Lists.newArrayList();
        // 获取的是当前的排班计划下绑定的排班设备分配的所有渠道号源的可预约总数和已预约总数
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<ExamScheduleNumberSource> root = criteriaQuery.from(entityManager.getMetamodel().entity(ExamScheduleNumberSource.class));
        criteriaQuery.multiselect(
                criteriaBuilder.sum(root.get("count")),
                criteriaBuilder.sum(root.get("usedCount"))
        );
        List<Specification<ExamScheduleNumberSource>> sp = Lists.newArrayList();
        sp.add(Specifications.eq("examSchedule.scheduleGroup", group));
        if (devices.size() == 1) {
            sp.add(Specifications.eq("examSchedule.device", devices.get(0)));
        }
        Predicate p = Specifications.and(sp).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(p);
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        long count = 0;
        long usedCount = 0;
        for (Tuple tuple : result) {
            count += tuple.get(0, Long.class) == null ? 0 : tuple.get(0, Long.class);
            usedCount += tuple.get(1, Long.class) == null ? 0 : tuple.get(1, Long.class);
        }
        countFromGroupList.add((int) count);
        countFromGroupList.add((int) usedCount);
        return countFromGroupList;
    }

    private int getCountUseFromGroup(ExamScheduleGroup group, List<ExamDevice> devices) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<ExamScheduleUse> root = criteriaQuery.from(entityManager.getMetamodel().entity(ExamScheduleUse.class));
        criteriaQuery.multiselect(criteriaBuilder.sum(root.get("inUsedCount")),
            criteriaBuilder.sum(root.get("outUsedCount")));
        List<Specification<ExamScheduleUse>> sp = Lists.newArrayList();
        sp.add(Specifications.eq("examSchedule.scheduleGroup", group));
        if (devices.size() == 1) {
            sp.add(Specifications.eq("examSchedule.device", devices.get(0)));
        }

        Predicate p = Specifications.and(sp).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(p);
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        Object[] rs =  result.get(0).toArray();
        long count = 0;
        for (int i = 0; i < rs.length; i++) {
            if (result.get(0).get(i) != null) {
                count += result.get(0).get(i, Long.class);
            }
        }
        return (int) count;
    }

    private int getCountUseFromGroup(ScheduleGroup group) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<ScheduleUse> root = criteriaQuery.from(entityManager.getMetamodel().entity(ScheduleUse.class));
        criteriaQuery.multiselect(criteriaBuilder.sum(root.get("internetUsedCount")),
            criteriaBuilder.sum(root.get("onlineUsedCount")),
            criteriaBuilder.sum(root.get("phoneUsedCount")),
            criteriaBuilder.sum(root.get("windowUsedCount")));
        Predicate p = Specifications.<ScheduleUse>eq("schedule.group", group)
            .toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(p);
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        Object[] rs =  result.get(0).toArray();
        long count = 0;
        for (int i = 0; i < rs.length; i++) {
            if (result.get(0).get(i) != null) {
                count += result.get(0).get(i, Long.class);
            }
        }
        return (int) count;
    }

    private int getCountFromGroup(ScheduleGroup group) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Schedule> root = criteriaQuery.from(entityManager.getMetamodel().entity(Schedule.class));
        criteriaQuery.multiselect(criteriaBuilder.sum(root.get("internetCount")),
            criteriaBuilder.sum(root.get("onlineCount")),
            criteriaBuilder.sum(root.get("phoneCount")),
            criteriaBuilder.sum(root.get("windowCount")));
        criteriaQuery.where(criteriaBuilder.equal(root.get("group"), group));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        Object[] rs =  result.get(0).toArray();
        long count = 0;
        for (int i = 0; i < rs.length; i++) {
            if (result.get(0).get(i) != null) {
                count += result.get(0).get(i, Long.class);
            }
        }
        return (int) count;
    }

    private ScheduleGroup saveScheduleGroup(CreateScheduleGroupVM vm, Hospital hospital, Dept dept,
                                            NursingConsulItem consulItem, MedicalWorker worker) {
        ScheduleGroup group = new ScheduleGroup();
        group.setCreator(CurrentUser.getOrThrow());
        group.setDept(dept);
        group.setConsulItem(consulItem);
        group.setStartTime(vm.getStartTime());
        group.setEndTime(vm.getEndTime());
        group.setHospital(hospital);
        group.setMedicalWorker(worker);
        group.setModel(vm.getModel());
        group.setType(vm.getType());
        group.setRepeat(Arrays.stream(vm.getRepeat()).boxed().collect(Collectors.toList()));
        scheduleGroupRepository.save(group);
        return group;
    }

    @Override
    public List<ScheduleVM> getOnlineSchedule(User user, Hospital hospital, String query) {
        ScheduleSearch scheduleSearch = ScheduleSearch.of(query, hospital, user);
        Long doctorId = scheduleSearch.getDoctorId();
        MedicalWorker medicalWorker = medicalWorkerRepository.findById(doctorId).orElseThrow();
        DoctorVM doctor = new DoctorVM(medicalWorker, false);
        Date startDate = scheduleSearch.getStartDate();
        Date endDate = null;
        if (scheduleSearch.getEndDate() != null) {
            endDate = TimeUtils.getEndOfDay(scheduleSearch.getEndDate());
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        List<ScheduleVM> scheduleVMList;
        // 查询一段时间内的预约医生号源信息
        if (startDate != null && endDate !=  null && TimeUtils.intervalTime(startDate, endDate, TimeUtils.TimeUnit.DAY) > 1
                && StringUtils.isNotBlank(medicalWorker.getHisId())) {
            long startFilterDept = System.currentTimeMillis();
            List<SchedulingDoctorSourceInfo> schedulingDoctorSourceInfoList = businessService
                    .getSchedulingDoctorSourceInfo(hospital, TimeUtils.dateToString(startDate, "yyyyMMdd"),
                            TimeUtils.dateToString(endDate, "yyyyMMdd"), medicalWorker.getHisId(), ChannelType.ONLINE.getCode())
                    .getSchedulingDoctorSourceInfos().stream().filter(detail -> {
                        for (DeptMedicalWorker deptMedicalWorker : medicalWorker.getDeptMedicalWorkers()) {
                            if (StringUtils.equals(deptMedicalWorker.getDept().getDeptCode(), detail.getDept_id())) {
                                return true;
                            }
                        }
                        return false;
                    }).filter(detail -> StringUtils.equalsAny(detail.getScheduling_type(), "0", "1", "8", "9")).collect(Collectors.toList());
            long endFilterDept = System.currentTimeMillis();
            log.info(((endFilterDept - startFilterDept) / 1000) + "s 过滤科室和Scheduling_type=======");
            List<String> deptCodeList = schedulingDoctorSourceInfoList.stream().map(SchedulingDoctorSourceInfo::getDept_id).collect(Collectors.toList());
            Map<String, Dept> map =
                deptRepository.findByHospitalAndDeptCodeIn(hospital, deptCodeList).stream().collect(Collectors.toMap(Dept::getDeptCode, v -> v,
                                                                                  (k1, k2) -> k2));
            long endMapDept = System.currentTimeMillis();
            log.info(((endMapDept - endFilterDept) / 1000) + "s 查询科室=======");
            scheduleVMList =  schedulingDoctorSourceInfoList.stream().map(detail -> {
                ScheduleVM scheduleVM = new ScheduleVM(detail, doctor);
                if (map.containsKey(detail.getDept_id())) {
                    scheduleVM.setDept(new DeptVM(map.get(detail.getDept_id())));
                }
                return scheduleVM;
            }).collect(Collectors.toList());
            long endmapDept1 = System.currentTimeMillis();
            log.info(((endmapDept1 - endMapDept) / 1000) + "s ScheduleVM复值Dept=======");
        } else {
            Date now = new Date();
            // 查询当天医生的号源信息
            if (StringUtils.isNotBlank(medicalWorker.getHisId())) {
                List<DoctorSourceDetail> details = businessService.getCurrentDayDoctorSourceDetail(hospital, medicalWorker)
                        .stream().filter(detail -> {
                            for (DeptMedicalWorker deptMedicalWorker : medicalWorker.getDeptMedicalWorkers()) {
                                if (StringUtils.equals(deptMedicalWorker.getDept().getDeptCode(), detail.getDept_id())) {
                                    return true;
                                }
                            }
                            return false;
                        }).filter(detail -> StringUtils.equalsAny(detail.getScheduling_type(), "0", "1", "8", "9"))
                        .filter(detail -> {
                            try {
                                // 返回的号源信息时间应该都是晚于当前时间，否则号源是无效的
                                return now.before(TimeUtils.convert(detail.getEnd_time(), "yyyyMMddHHmm"));
                            } catch (Exception e) {
                                log.error(detail.getEnd_time() + ": 时间类型错误, 正确格式应该是yyyyMMddHHmm", e);
                            }
                            return false;
                        })
                        .collect(Collectors.toList());
                List<String> deptCodeList = details.stream().map(DoctorSourceDetail::getDept_id).collect(Collectors.toList());
                Map<String, Dept> map =
                    deptRepository.findByHospitalAndDeptCodeIn(hospital, deptCodeList).stream().collect(Collectors.toMap(Dept::getDeptCode, v -> v, (k1, k2) -> k2));
                scheduleVMList =  details.stream().map(detail -> {
                    ScheduleVM scheduleVM = new ScheduleVM(detail, medicalWorker);
                    if (map.containsKey(detail.getDept_id())) {
                        scheduleVM.setDept(new DeptVM(map.get(detail.getDept_id())));
                    }
                    return scheduleVM;
                }).collect(Collectors.toList());
            } else {
                scheduleVMList = Lists.newArrayList();
            }
        }
        return scheduleVMList;
    }

    @Override
    public List<ScheduleVM> getNetSchedule(User user, Hospital hospital, String query) {
        List<Specification<Schedule>> specifications = Lists.newArrayList(Specifications.eq("hospital", hospital));
        ScheduleSearch qs = ScheduleSearch.of(query, hospital, user);
        ScheduleStartTimeFilter startTimeFilter = qs.getStartTimeFilter();
        ScheduleEndTimeFilter endTimeFilter = qs.getEndTimeFilter();
        if (startTimeFilter != null && endTimeFilter != null) {
            startTimeFilter.setOperator(Operator.lt);
            startTimeFilter.setDate(TimeUtils.getEndOfDay(startTimeFilter.getDate()));
            endTimeFilter.setOperator(Operator.gt);
        }
        specifications.add(qs.toSpecification());
        Map<Long, DoctorVM> doctorMap = new HashMap<>();

        List<Schedule> ss = scheduleRepository.findAll(Specifications.and(specifications));
        List<ScheduleUse> uses = scheduleUseRepository.findAllByScheduleIn(ss);
        List<ScheduleVM> vms = ss.stream()
                .filter(u -> {
                    if (u.getMedicalWorker() != null) {
                        return u.getMedicalWorker().isOpenVisit() && u.getMedicalWorker().isEnabled();
                    }
                    return true;
                })
                .map(u -> {
                    ScheduleVM vm = new ScheduleVM(u, false, null);
                    vm.setUsedCount(uses.stream().filter(s -> s.getSchedule().getId().equals(u.getId()))
                            .findFirst().orElse(null));
                    if (u.getMedicalWorker() != null) {
                        if (!doctorMap.containsKey(u.getMedicalWorker().getId())) {
                            UserStats stats = userStatsRepository.findOneByUser(u.getMedicalWorker().getUser())
                                    .orElse(new UserStats());
                            DoctorVM doctor = new DoctorVM(u.getMedicalWorker(), false, stats);
                            doctorMap.put(u.getMedicalWorker().getId(), doctor);
                        }
                        vm.setMedicalWorker(doctorMap.get(u.getMedicalWorker().getId()));
                    }
                    return vm;
                })
                .collect(Collectors.toList());
        return vms;
    }

    @Override
    public List<ExamScheduleVM> findExamDeviceSchedulesByChannelCode(Hospital hospital, String channelCode,
                                                                     Long itemId, String query) {
        List<ExamScheduleVM> examScheduleVMS = Lists.newArrayList();
        HospitalDictionary hospitalParentDictionary = hospitalDictionaryRepository
                .findFirstByHospitalAndParentAndCode(hospital, null, "HYFPQD").orElse(null);
        if (hospitalParentDictionary != null && hospitalParentDictionary.getEnabled()) {
            HospitalDictionary hospitalDictionary = hospitalDictionaryRepository
                    .findFirstByHospitalAndParentAndCode(hospital, hospitalParentDictionary, channelCode).orElse(null);
            if (hospitalDictionary != null) {
                List<Specification<ExamSchedule>> specs = Lists.newArrayList();
                ExamScheduleSearch search = ExamScheduleSearch.of(query);
                ExamItem item = null;
                if (itemId != null) {
                    item = examItemRepository.getById(itemId);
                    specs.add(Specifications.eq("device.category", item.getCategory()));
//                    Date tomorrow = TimeUtils.getTomorrowDate(LocalDate.now());
//                    specs.add(Specifications.dateAfter("startTime", tomorrow));
//                    Date endTime = DateUtils.addDays(tomorrow, item.getCategory().getAppointmentDays());
//                    specs.add(Specifications.dateBefore("endTime", endTime));
                } else if (search.getEndTimeFilter() == null || search.getStartTimeFilter() == null) {
                    return Lists.newArrayList();
                }
                specs.add(Specifications.isTrue("device.enabled"));
                specs.add(Specifications.isFalse("device.deleted"));
                specs.add(Specifications.eq("hospital", hospital));
                specs.add(search.toSpecification());
                List<ExamSchedule> schedules = examScheduleRepository
                        .findAll(Specifications.and(specs), Sort.by(Sort.Direction.ASC, "startTime"));
                ExamItem finalItem = item;
                schedules.forEach(u -> {
                    ExamScheduleNumberSource examScheduleNumberSource = examScheduleNumberSourceRepository
                            .findByExamScheduleAndDictionary(u, hospitalDictionary).orElse(null);
                    if (examScheduleNumberSource != null) {
                        NumberSourceVM numberSourceVM = new NumberSourceVM(examScheduleNumberSource);
                        ExamScheduleVM scheduleVM = new ExamScheduleVM(u, finalItem, true);
                        int count = numberSourceVM.getCount();
                        int countUse = numberSourceVM.getCountUse();
                        scheduleVM.setNumberSourceList(Lists.newArrayList(numberSourceVM));
                        // 查看当前设备是否属于维护日中
                        List<Specification<ExamDeviceMaintenance>> maintenanceSpecs = Lists.newArrayList();
                        maintenanceSpecs.add(Specifications.eq("device", u.getDevice()));
                        maintenanceSpecs.add(Specifications.ge("startTime", search.getStartTimeFilter().getDate()));
                        maintenanceSpecs.add(Specifications.le("endTime", search.getEndTimeFilter().getDate()));
//                        List<ExamDeviceMaintenanceDTO> examDeviceMaintenanceDTOS = examDeviceMaintenanceRepository
//                                .findAll(Specifications.and(mainTenanceSpecs))
//                                .stream().map(ExamDeviceMaintenanceDTO::new).collect(Collectors.toList());
//                        scheduleVM.setMaintenanceList(examDeviceMaintenanceDTOS);
                        List<ExamDeviceMaintenance> maintenances = examDeviceMaintenanceRepository.findAll(Specifications.and(maintenanceSpecs));
                        // 比较排班日当天日期是否是设备维护日
                        boolean maintenanceFlag = maintenances.stream().anyMatch(examDeviceMaintenance ->
                                TimeUtils.isSameDay(u.getStartTime(), examDeviceMaintenance.getStartTime())
                                        && examDeviceMaintenance.getStartTime().compareTo(u.getStartTime()) <= 0
                                        && TimeUtils.isSameDay(u.getEndTime(), examDeviceMaintenance.getEndTime())
                                        && u.getEndTime().compareTo(examDeviceMaintenance.getEndTime()) <= 0);
                        // 优先级：设备维护日>停诊/已锁定
                        if (maintenanceFlag) {
                            scheduleVM.setNumberSourceStatus("7");
                        } else if (u.getStatus() != null) {
                            scheduleVM.setNumberSourceStatus(u.getStatus() == ExamSchedule.NumberSourceStatus.CLOSED ? "0" : "3");
                        } else {
                            scheduleVM.setNumberSourceStatus(count > countUse ? "1" : (count == countUse ? "2" : scheduleVM.getNumberSourceStatus()));
                        }
                        examScheduleVMS.add(scheduleVM);
                    }
                });
                examScheduleVMS.stream().sorted(Comparator.comparing(ExamScheduleVM::getStartTime)).collect(Collectors.toList());
            } else {
                log.info("未获取到代码为{}的字典信息", channelCode);
            }
        } else {
            log.info("未配置号源分配渠道字典或者渠道号源字典未启用");
        }
        return examScheduleVMS;
    }

    @Override
    public List<ExamScheduleVM> findNumberSourceSchedulesByExamDevice(Hospital hospital, long id, String query) {
        List<ExamScheduleVM> examScheduleVMS = Lists.newArrayList();
        List<Specification<ExamSchedule>> specs = Lists.newArrayList();
        ExamScheduleSearch search = ExamScheduleSearch.of(query);
        // 排班开始时间
        ExamScheduleStartTimeFilter startTimeFilter = search.getStartTimeFilter();
        // 排班结束时间
        ExamScheduleEndTimeFilter endTimeFilter = search.getEndTimeFilter();
        if (startTimeFilter == null || endTimeFilter == null) {
            log.info("起止时间必须传");
            return Lists.newArrayList();
        }
        ExamDevice examDevice = examDeviceRepository.getById(id);
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.eq("device", examDevice));
        //specs.add(Specifications.isTrue("device.enabled"));
        specs.add(Specifications.isFalse("device.deleted"));
        specs.add(search.toSpecification());
        List<ExamSchedule> schedules = examScheduleRepository
                .findAll(Specifications.and(specs), Sort.by(Sort.Direction.ASC, "startTime"));
        schedules.forEach(schedule -> {
            int count = 0;
            int usedCount = 0;
            ExamScheduleVM scheduleVM = new ExamScheduleVM(schedule, null);
            // 时间间隔 得根据排班计划的详情判断是上午/下午/晚上赋值
            List<ExamScheduleGroupDetail> scheduleGroupDetails = schedule.getScheduleGroup().getDetails();
            Optional<ExamScheduleGroupDetail> matchingDetail = scheduleGroupDetails.stream()
                    .filter(groupDetail -> groupDetail.getShift() == schedule.getShift())
                    .findFirst();
            matchingDetail.ifPresent(detail -> scheduleVM.setInterval(detail.getInterval()));
            if (schedule.getStatus() != null) {
                scheduleVM.setNumberSourceStatus(schedule.getStatus().getName());
            }
            List<Specification<ExamOrder>> examOrderSpecs = Lists.newArrayList();
            examOrderSpecs.add(Specifications.eq("service", schedule));
            examOrderSpecs.add(Specifications.or(
               Specifications.eq("status", ExamOrder.ExamOrderStatus.WAIT_CHECK),
               Specifications.eq("status", ExamOrder.ExamOrderStatus.CHECKED),
               Specifications.eq("status", ExamOrder.ExamOrderStatus.REPORTED)
            ));
            // 排班设备每个时间段下的到诊数量
            scheduleVM.setReceivedCount(examOrderRepository.findAll(Specifications.and(examOrderSpecs)).size());
            // 设备维护日
//            List<Specification<ExamDeviceMaintenance>> mainTenanceSpecs = Lists.newArrayList();
//            mainTenanceSpecs.add(Specifications.eq("device", schedule.getDevice()));
//            mainTenanceSpecs.add(Specifications.ge("startTime", startTimeFilter.getDate()));
//            mainTenanceSpecs.add(Specifications.le("endTime", endTimeFilter.getDate()));
//            List<ExamDeviceMaintenanceDTO> examDeviceMaintenanceDTOS = examDeviceMaintenanceRepository
//                    .findAll(Specifications.and(mainTenanceSpecs))
//                    .stream().map(ExamDeviceMaintenanceDTO::new).collect(Collectors.toList());
//            scheduleVM.setMaintenanceList(examDeviceMaintenanceDTOS);
            // 当前时间段的配置的渠道号源详情
            List<NumberSourceVM> numberSourceVMS = Lists.newArrayList();
            List<ExamScheduleNumberSource> numberSourceList = schedule.getNumberSourceList();
            if (CollectionUtils.isNotEmpty(numberSourceList)) {
                for (ExamScheduleNumberSource numberSource : numberSourceList) {
                    count += numberSource.getCount();
                    usedCount += numberSource.getUsedCount();
                    NumberSourceVM numberSourceVM = new NumberSourceVM(numberSource);
                    List<Specification<ExamOrder>> channelSpecs = Lists.newArrayList();
                    channelSpecs.addAll(examOrderSpecs);
                    channelSpecs.add(Specifications.eq("numberSource", numberSource));
                    // 每个渠道号源下的到诊数
                    numberSourceVM.setReceivedCount(examOrderRepository.findAll(Specifications.and(channelSpecs)).size());
                    numberSourceVMS.add(numberSourceVM);
                }
            }
            scheduleVM.setCount(count);
            scheduleVM.setUsedCount(usedCount);
            String status;
            if (schedule.getStatus() != null) {
                boolean isClosed = schedule.getStatus() == ExamSchedule.NumberSourceStatus.CLOSED;
                boolean isExpired = schedule.getEndTime().compareTo(new Date()) < 0;
                if (isExpired) {
                    status = isClosed ? "5" : "6";
                } else {
                    status = isClosed ? "0" : "3";
                }
            } else {
                boolean isExpired = schedule.getEndTime().compareTo(new Date()) < 0;
                status = isExpired ? "4" : (count > usedCount ? "1" : "2");
            }
            scheduleVM.setNumberSourceStatus(status);
            scheduleVM.setNumberSourceList(numberSourceVMS);
            examScheduleVMS.add(scheduleVM);
        });
        return examScheduleVMS.stream().sorted(Comparator.comparing(ExamScheduleVM::getStartTime)).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void operationExamSchedule(User user, long id, ExamScheduleOperationDTO param) {
        lockService.executeWithLockThrowError("schedule." + id, () -> {
            ExamSchedule examSchedule = examScheduleRepository.getById(id);
            ExamSchedule.NumberSourceStatus currentStatus = examSchedule.getStatus();
            switch (param.getStep()) {
                case STOP:
                    if (currentStatus == null) {
                        // 获取受影响的预约单
                        List<Specification<ExamOrder>> specs = Lists.newArrayList();
                        specs.add(Specifications.eq("service", examSchedule));
                        specs.add(Specifications.or(
                                Specifications.eq("status", ExamOrder.ExamOrderStatus.WAIT_SIGNIN),
                                Specifications.eq("status", ExamOrder.ExamOrderStatus.WAIT_CHECK)
                        ));
                        examSchedule.setStatus(ExamSchedule.NumberSourceStatus.CLOSED);
                        examScheduleRepository.save(examSchedule);
                        ExamOrderVM examOrderVM = new ExamOrderVM();
                        examOrderVM.setCancelReason("停诊");
                        List<ExamOrder> examOrders = examOrderRepository.findAll(Specifications.and(specs));
                        examOrders.forEach(examOrder -> AppContext.getInstance(UserCheckServiceImpl.class)
                                .updateCancelExamOrder(user, null, examOrder, examOrderVM, true));
                    } else {
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("号源状态不正确无法停诊");
                    }
                    break;
                case ADD:
                    if (currentStatus == null) {
                        HospitalDictionary hospitalDictionary = hospitalDictionaryRepository
                                .findById(param.getChannelId()).orElseThrow(() ->
                                        ErrorType.ILLEGAL_PARAMS.toProblem("未找到加号渠道信息"));
                        ExamScheduleNumberSource numberSource = examScheduleNumberSourceRepository
                                .findByExamScheduleAndDictionary(examSchedule, hospitalDictionary).orElse(new ExamScheduleNumberSource());
                        if (numberSource.isNew()) {
                            numberSource.setCount(param.getCount());
                            numberSource.setExamSchedule(examSchedule);
                            numberSource.setDictionary(hospitalDictionary);
                        } else {
                            numberSource.setCount(numberSource.getCount() + param.getCount());
                        }
                        ExamScheduleNumberSource saved = examScheduleNumberSourceRepository.save(numberSource);
                        List<ExamScheduleNumberSource> numberSourceList = examSchedule.getNumberSourceList();
                        List<Long> numberSourceIds = numberSourceList.stream().map(ExamScheduleNumberSource::getId)
                                .collect(Collectors.toList());
                        if (numberSourceIds.contains(saved.getId())) {
                            numberSourceList.set(numberSourceIds.indexOf(numberSource.getId()), saved);
                        } else {
                            numberSourceList.add(saved);
                        }
                        examSchedule.setNumberSourceList(numberSourceList);
                        examScheduleRepository.save(examSchedule);
                    } else {
                        throw ErrorType.ILLEGAL_PARAMS.toProblem("号源状态不正确无法加号");
                    }
                    break;
                case CLEAR:
                    List<ExamOrder> examOrderList = examOrderRepository.findAllByService(examSchedule);
                    if (CollectionUtils.isNotEmpty(examOrderList)) {
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("当前时间段有关联的预约单，无法清除");
                    }
                    List<ExamScheduleNumberSource> examScheduleNumberSources = examScheduleNumberSourceRepository.findAllByExamSchedule(examSchedule);
                    if (CollectionUtils.isNotEmpty(examScheduleNumberSources)) {
                        examScheduleNumberSourceRepository.deleteAll(examScheduleNumberSources);
                    }
                    examScheduleRepository.delete(examSchedule);
                    break;
                case LOCK:
                    if (currentStatus != null) {
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("号源状态不正确无法临时锁定");
                    }
                    examSchedule.setStatus(ExamSchedule.NumberSourceStatus.LOCKED);
                    examScheduleRepository.save(examSchedule);
                    break;
                case UNLOCK:
                    if (currentStatus != ExamSchedule.NumberSourceStatus.LOCKED) {
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("号源状态不正确无法恢复预约");
                    }
                    examSchedule.setStatus(null);
                    examScheduleRepository.save(examSchedule);
                    break;
                case RECOVERY:
                    if (examSchedule.getStatus() != ExamSchedule.NumberSourceStatus.CLOSED) {
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("号源状态不正确无法恢复预约");
                    }
                    examSchedule.setStatus(null);
                    examScheduleRepository.save(examSchedule);
                    break;
                default:
                    break;
            }
            return 0;
        });
    }

    @Override
    public List<ExamOrderVM> getExamOrderByExamSchedule(long id, Long channelId) {
        ExamSchedule examSchedule = examScheduleRepository.getById(id);
        List<Specification<ExamOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("service", examSchedule));
        specs.add(Specifications.or(
           Specifications.eq("status", ExamOrder.ExamOrderStatus.WAIT_SIGNIN),
           Specifications.eq("status", ExamOrder.ExamOrderStatus.WAIT_CHECK)
        ));
        if (channelId != null) {
            HospitalDictionary hospitalDictionary = hospitalDictionaryRepository.getById(channelId);
            ExamScheduleNumberSource numberSource = examScheduleNumberSourceRepository
                    .findByExamScheduleAndDictionary(examSchedule, hospitalDictionary).orElse(null);
            if (numberSource != null) {
                specs.add(Specifications.eq("numberSource", numberSource));
            }
        }
        return examOrderRepository.findAll(Specifications.and(specs))
                .stream().map(ExamOrderVM::new).collect(Collectors.toList());
    }

    @Override
    public Page<ExamScheduleGroupVM> getDeviceSchedulePlans(Hospital hospital, String query, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdDate");
        List<ExamScheduleGroupVM> examScheduleGroupVMS = Lists.newArrayList();
        List<Specification<ExamSchedule>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        ExamScheduleSearch search = ExamScheduleSearch.of(query);
        specs.add(search.toSpecification());
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        criteriaQuery.distinct(true);
        Root<ExamSchedule> root = criteriaQuery.from(entityManager.getMetamodel().entity(ExamSchedule.class));
        criteriaQuery.multiselect(
                root.get("device"),
                root.get("scheduleGroup"),
                criteriaBuilder.max(root.get("createdDate"))
        );
        List<Expression<?>> groupByExpressions = new ArrayList<>();
        groupByExpressions.add(root.get("device"));
        groupByExpressions.add(root.get("scheduleGroup"));
        criteriaQuery.groupBy(groupByExpressions);
        criteriaQuery.orderBy(criteriaBuilder.desc(criteriaBuilder.max(root.get("createdDate"))));
        Predicate where = Specifications.and(specs).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        // 设置分页参数
        TypedQuery<Tuple> queryResult = entityManager.createQuery(criteriaQuery);
        queryResult.setFirstResult((int) pageable.getOffset());
        queryResult.setMaxResults(pageable.getPageSize());
        List<Tuple> resultList = queryResult.getResultList();
        for (Tuple tuple : resultList) {
            ExamDevice examDevice = tuple.get(0, ExamDevice.class);
            ExamScheduleGroup examScheduleGroup = tuple.get(1, ExamScheduleGroup.class);
            ExamScheduleGroupVM examSchedulePlanDetail = getExamSchedulePlanDetail(examScheduleGroup, Lists.newArrayList(examDevice.getId()), false);
            examScheduleGroupVMS.add(examSchedulePlanDetail);
        }
        // 计算总数
        long total = entityManager.createQuery(criteriaQuery).getResultList().size();
        return new PageImpl<>(examScheduleGroupVMS, pageable, total);
    }
}
