package cn.taihealth.ih.service.api.nodered;

import cn.taihealth.ih.service.dto.nodeRed.ConfirmRefund;
import cn.taihealth.ih.service.vm.nodered.NodeRedResponseData;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.LockNumberResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.ReturnRegistResult;

import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.SyncInspectMRResult;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 封装okhttpClient与node-red的api交互
 */
public interface NodeRedClient {

    //1 医生字典查询
    List<DoctorInfo> getDoctorList(String hospcode, String page, String size, String dept_id, String channel_type);

    // 2 科室字典
    List<DeptInfo> getDeptList(String hospcode, String page, String size, String channel_type);

    //3 医院诊断字典查询
    List<DiagnoseInfo> getDicDiagnose(String hospcode, String page, String size);

    //8 收费项目查询
    List<ChargeItemInfo> getChargeItem(String hospcode, String channelType, String queryScope, String page,
                                       String size);
    // 9.住院科室病区信息查询
    List<DeptAndWardInfo> getDeptAndWardInfo(String hospcode, String page, String size);

    // 10.住院床位信息查询
    List<WardBedInfo> getWardBedInfo(String hospcode, String dept_id);

    //11 查询门诊患者基本信息（通过证件号码）
    List<HisPatientInfo> getOutpatientInfoByCertNo(String hospcode, String patname, String certificateNo);


    //8 查询门诊患者基本信息（通过就诊卡号）
    List<HisPatientInfo> getOutpatientInfoByCardNo(String hospcode, String patname, String cardno,
                                                   String query_type);

    //9 查询门诊患者基本信息（通过患者联系电话）
    List<HisPatientInfo> getOutpatientInfoByTelephone(String hospcode, String patname, String telephone);

    //10 查询门诊患者基本信息（通过门诊患者唯一号）
    HisPatientInfo getOutpatientInfoByPatId(String hospcode, String patname, String patid);

    //11 创建就诊卡
    MedicalCardInfo createMedicalCard(String hospcode, String patname, String certificate_no,
                                      String certificate_type,
                                      String sex,
                                      String marriage, String nation, String career, String birth,
                                      String address,
                                      String telephone);

    //12 登记就诊卡
    MedicalCardInfo registerMedicalCard(String hospcode, String patname, String card_type, String card_no,
                                        String certificate_no,
                                        String certificate_type, String sex, String marriage, String nation,
                                        String career, String birth, String address, String telephone);

    //13 患者基本信息更新
    HisPatientInfo updatePatientInfo(String hospcode, String patname, String user_source, String patid,
                                     String cardno,
                                     String address,
                                     String telephone, String memo, String contacts_name,
                                     String contacts_telephone,
                                     String contacts_certificate_no, String contacts_relationship,
                                     String certificate_no,
                                     String certificate_type, String nation, String career,
                                     String nationality,
                                     String work_unit, String domicile_address,
                                     String residence_county_address, String insurance_param,
                                     String virtual_card);

    //14 查询患者可续方处方信息
    List<RecipeInfo> getPatientRecipeListRenewable(String hospitalCode, String patientName, String patId,
                                                   String beginDate, String endDate, String insuranceParam);

    //15 查询全院当天号源信息
    List<CurrentDayAppointment> getCurrentDayAppointmentList(String hospcode, String channel_type);

    //16 查询当班出诊医生信息
    List<DoctorScheduleInfo> getCurrentDayDoctorList(String hospcode, String dept_id, String channel_type);

    //17 查询当班医生号源信息
    List<DoctorSourceDetail> getCurrentDayDoctorSourceDetail(String hospcode, String doctor_id,
                                                             String channel_type);

    //18 查询当天指定排班的号序信息
    List<SchedulingSourceNumber> getCurrentDaySchedulingSourceNumber(String hospcode, String scheduling_id,
                                                               String channel_type, String source_number);

    //19 门诊挂号预算
    PreRegistrationResult preRegister(String hospcode, PreRegisterReq preRegisterReq);

    //20 门诊挂号预算撤销
    HisGeneralResult cancelPreRegist(String hospcode, String regno, String settle_id, String patid,
                                     String insurance_param);

    //21 门诊挂号结算
    NodeRedResponseData<ConfirmRegistResult> confirmRegist(String hospcode, ConfirmRegistReq req);

    /**
     * 5.1.1.12 门诊预约签到
     * @param hospcode
     * @return
     */
    NodeRedResponseData<AppointmentSignInResult> appointmentSignIn(String hospcode, AppointmentSignInReq signInReq);

    //22 门诊挂号取消结算
    ReturnRegistResult returnRegist(String hospcode, String patid, String regno, String settle_id, String pay_type,
                                    String pay_amount, String trade_no, String refund_trade_no, String refundTime,
                                    String insuranceParam);

    //23 门诊挂号锁号
    LockNumberResult lockNumber(String hospcode, String patid, String scheduling_id, String source_number);

    //24 门诊挂号解锁
    HisGeneralResult unlock(String hospcode, String patid, String lock_number_id);

    //25 在线处方保存
    SaveRecipeResult saveRecipeOnline(String hospcode, SaveOnlineRecipeRequest recipeInfo);
    SaveMedicalCaseResult saveMedicalCaseOnline(String hospcode, SaveMedicalCaseReq medicalCaseInfo);
    SaveMedicalCaseResult saveDiagnoseOnline(String hospcode, SaveDiagnoseReq diagnoseReq);

    ReturnRegistResult deleteRecipeOnline(String hospcode, String patname, String patid, String regno,
                                        String recipe_no);

    //27 处方审核通知
    HisGeneralResult noticeForRecipeCheck(String hospcode, String patname, String patid, String regno,
                                          String recipe_no,
                                          String check_flag, String check_doctor_id, String check_doctor_name,
                                          String check_time, String check_result_desc);

    //28 门诊收费预算
    PreChargeResult preCharge(String hospcode, String patid, String card_no, String card_type, String regno,
                              List<String> recipe_no_list, String payType,
                              String hosp_account_flag, String self_flag, String insuranceParam, String extraContent);

    //29 门诊收费预算撤销
    HisGeneralResult cancelPreCharge(String hospcode, String regno,  String settle_id, String patid, String insurance_param);

    //30 门诊收费结算
    NodeRedResponseData<ConfirmChargeResult> confirmCharge(String hospcode, ConfirmChargeReq confirmChargeReq);

    //31 门诊支付结果查询
    PayResultOnline getPayResultOnline(String hospcode, String settle_id, String trade_no);

    //32 门诊收费结算取消
    CancelOutpatientSettleResult cancelOutpatientSettle(String hospcode, String patid,
                                                        String regno,
                                                        String settle_id,
                                                        List<String> tcflist, String paytype, String paymoney,
                                                        String paylsh,
                                                        String canclelsh, String receipt_account,
                                                        String insurance_param,
                                                        String refund_time);

    // 文件归档
    HisFileUploadInfo uploadFile(String hospcode, String orderId, String type,
                                 String fileUrl);

    // 查询随访列表
    List<FollowUpInfo> getFollowUpListByPatient(String hospcode, String patid, String status,
                                                String expected_execute_start_time, String expected_execute_end_time,
                                                String start_time, String end_time);

    /**
     * 查询满意度得分统计数据
     * @param hospcode
     * @param startTime
     * @param endTime
     * @return
     */
    SatisfactionGradeStatistics getSatisfactionGradeStatistics(String hospcode, Date startTime, Date endTime);

    /**
     * 查询满意度得分统计详情
     * @param hospcode
     * @param startTime
     * @param endTime
     * @return
     */
    List<SatisfactionGradeStatisticsDetail> getSatisfactionGradeStatisticsDetails(String hospcode, Date startTime, Date endTime);

    /**
     * 查询满意度绩效统计数据
     * @param hospcode
     * @param startTime
     * @param endTime
     * @return
     */
    SatisfactionPerformanceStatistics getSatisfactionPerformanceStatistics(String hospcode, Date startTime, Date endTime);

    /**
     * 查询满意度绩效统计详情
     * @param hospcode
     * @param startTime
     * @param endTime
     * @return
     */
    List<SatisfactionPerformanceStatisticsDetail> getSatisfactionPerformanceStatisticsDetails(String hospcode, Date startTime, Date endTime);

    /**
     * 查询病区绩效统计数据
     * @param hospcode
     * @param startTime
     * @param endTime
     * @return
     */
    WardPerformanceStatistics getWardPerformanceStatistics(String hospcode, Date startTime, Date endTime);

    /**
     * 查询病区绩效统计详情
     * @param hospcode
     * @param startTime
     * @param endTime
     * @return
     */
    List<WardPerformanceStatisticsDetail> getWardPerformanceStatisticsDetails(String hospcode, Date startTime, Date endTime);

    // 查询his交易账单
    BillSummary getTradeHisBill(String hCode, String bill_date, String pay_type);

    // 门诊预约登记
    SaveAppointmentResult saveAppointment(String hospitalCode, String hisPatientId, String schedulingId,
                                          String sourceNumber, String telephone, String serialNo,
                                          String opCertificateNo, String memo);
    // 门诊取消预约
    ReponseResult cancelAppointment(String code, String patientName, String hisPatientId, String appointmentId, String memo);

    // 异常账单冲正（退款）
    ReverseAbnormalBillResult reverseAbnormalBill(String code, String orderId, String hisBillCallId);

    // 上传互联网医院交易账单
    TradeIHBillResult tradeIHBill(String code, TradeIHBillSummary billSummary);

    // 住院预交金预充值
    InpatientHospCardPreChargeResult inpatientHospCardPreCharge(String hospitalCode, String patientName, String rego);

    // 住院预交金充值
    NodeRedResponseData<InpatientHospCardChargeResult> inpatientHospCardCharge(String hospitalCode, String patientName, String rego, String advanceChargeId,
                                                          String outTradeNo, String serialNo, String payType, String selfAmount,
                                                          String tradeNo, String accountId, String payTime, String port, String openId,
                                                          String openPhone, String telephone, String contactsName, String contactsCertificateNo);

    // 病人出院预算
    InpatientPreChargeResult inpatientPreCharge(String hospitalCode, String patientName, String patientHisId, String admissionNo);

    // 病人出院结算
    InpatientConfirmChargeResult inpatientConfirmCharge(String hospitalCode, String patientName, String rego, String patientHisId, String settleId,
                                                        String settleType, String selfFlag, String insuranceParam, String port, String receiptAccount);

    // 查询全院预约号源信息（含科室号源和医生号源）
    List<AppointmentInfo> getSourceDetails(String hospitalCode, String beginDate, String endDate, String channelType, String deptId);

    // 查询指定排班序号的号源信息
    List<SchedulingInfo> getSchedulingListById(String hospitalCode, String schedulingId, String sourceType, String channelType);

    // 查询预约出诊科室信息
    List<SchedulingDeptInfo> getSchedulingDeptList(String hospitalCode, String beginDate, String endDate, String channelType);
    // 查询预约出诊科室信息--测试
    List<SchedulingDeptInfo> getSchedulingDeptListTest(String hospitalCode, String beginDate, String endDate, String channelType);

    // 查询预约出诊医生信息
    List<SchedulingDoctorInfo> getSchedulingDoctorList(String hospitalCode, String deptId, String beginDate, String endDate, String channelType);

    // 查询预约科室号源信息
    List<SchedulingDeptSourceInfo> getSchedulingDeptSourceDetails(String hospitalCode, String beginDate, String endDate, String deptId, String channelType);

    // 查询预约医生号源信息
    List<SchedulingDoctorSourceInfo> getSchedulingDoctorSourceDetails(String hospitalCode, String beginDate, String endDate, String doctorId, String channelType);

    // 查询患者预约记录
    List<PatientAppointmentInfo> getPatientAppointment(String hospitalCode, String patientName, String patientId, String beginDate,
                                                       String endDate, String routeFlag, String dateType);

    // 查询患者的挂号记录（根据门诊患者唯一号）
    List<PatientRegistInfo> getRegistListByPatId(String hospitalCode, String patientName, String patientId, String beginDate, String endDate, String routeFlag);

    // 查询患者的挂号记录(根据证件号)
    List<PatientRegistInfo> getRegistListByCertificateNo(String hospitalCode, String certificateNo);

    // 查询患者挂号记录（根据门诊挂号唯一号）
    PatientRegistInfo getRegistListByRegno(String hospitalCode, String patientName, String regno, String appointmentId, String patientId, String routeFlag);

    // 查询患者当前候诊信息（通过门诊患者唯一号）
    List<PatientRegistWaitingInfo> getPatientRegistWaitingListByPatId(String hospitalCode, String patientName, String hisPatientId);

    // 查询患者取药排队信息
    List<PatientDrugWaitingInfo> getPatientDrugWaitingList(String hospitalCode, String patientName, String hisPatientId);

    // 查询门诊项目提示信息
    List<OutpatientItemHintInfo> getOutpatientItemHint(String hospitalCode, String hisPatientId, String settleId);

    // 查询患者到诊信息
    List<PatientSignInfo> getPatientSignInfo(String hospitalCode, String date);

    // 当前门诊候诊队列查询
    List<PatientWaitingInfo> getPatientWaiting(String hospitalCode, String doctorId, String deptId);

    /**
     * 查询患者处方
     * @param hospitalCode
     * @param patientName
     * @param cardNo
     * @param cardType [0]自费卡 [1]医保卡 [2]社保卡
     * @param hisPatientId
     * @param beginDate
     * @param endDate
     * @param insuranceParam
     * @param channelType
     * @return
     */
    List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(String hospitalCode, String patientName,
                                                                       String cardNo, String cardType,
                                                                       String hisPatientId, String beginDate,
                                                                       String endDate, String insuranceParam, String channelType);


    // 查询价格公示列表
    List<PricePublicityInfo> getPricePublicityList(String hospitalCode, String pricePublicityType, String keyword);

    // 查询专家特需出诊公示列表
    List<DoctorSchedules> getDoctorSchedules(String hospitalCode, String deptId, String doctorName, String beginDate, String endDate);

    // 查询专病门诊公示列表
    List<SpecialClinicInfo> getSpecialClinicList(String hospitalCode, String beginDate, String endDate);

    // 查询普通门诊公示列表
    List<GeneralClinicInfo> getGeneralClinicList(String hospitalCode, String beginDate, String endDate);

    // 查询检验报告列表（通过患者院内卡号）
    List<InspectionReport> getInspectionReportListByCardNo(String hospitalCode, String patientName, String cardNo,
                                                   String beginDate, String endDate);

    // 查询检验报告列表（通过门诊患者唯一号）
    List<InspectionReport> getInspectionReportListByPatId(String hospitalCode, String patientName, String patientId, String cardNo, String certificatNo,
                                                          String userSource, String beginDate, String endDate);

    // 查询实验室检验报告结果
    List<LaboratoryReport> getLaboratoryReportList(String hospitalCode, String reportNo, String reportTypeCode);


    // 查询微生物报告列表（通过患者院内卡号）
    List<MicroorganismReport> getMicroorganismReportsByCardNo(String hospitalCode, String patientName, String cardNo,
                                                              String beginDate, String endDate);

    // 查询微生物报告列表（通过门诊患者唯一号）
    List<MicroorganismReport> getMicroorganismReportsByPatId(String hospitalCode, String patientName,
                                                             String patientId, String userSource, String beginDate,
                                                             String endDate);

    // 查询实验室微生物报告结果
    List<LaboratoryMicroorganismsReportResult> getLaboratoryMicroorganismsReportResult(String hospitalCode, String reportNo, String reportTypeCode);

    // 查询检查报告列表（通过门诊患者卡号)
    List<RisReport> getRisReportsByCardNo(String hospitalCode, String patientName, String cardNo, String beginDate, String endDate);

    // 查询检查报告列表（通过门诊患者唯一号）
    List<RisReport> getRisReportsByPatId(String hospitalCode, String patientName, String hisPatid, String cardNo,
                                         String identity, String userSource, String beginDate, String endDate);

    // 查询检查报告结果
    List<RisReportResult> getRisReportResult(String hospitalCode, String reportNo, String reportTypeCode);

    // 查询检验危急值信息
    List<CriticalValueReport> getCriticalValueReportByPatId(String hospitalCode, String patientName, String patientId,
                                                            String userSource, String beginDate, String endDate);

    // 查询住院患者基本信息（通过病历号和患者联系电话）- 如果his不涉及病历号，可不实现这个接口)
    List<InpatientInfo> getInpatientListByHisCardNo(String hospitalCode, String patientName, String hisCardNo, String telephone);

    // 查询住院患者基本信息（通过患者院内卡号）
    List<InpatientInfo> getInpatientListByCardNo(String hospitalCode, String patientName, String cardNo);

    // 查询住院患者基本信息（通过证件号）
    List<InpatientInfo> getInpatientListByCertNo(String hospitalCode, String patientName, String certNo);

    // 查询患者住院就诊记录
    List<InpatientRecord> getInpatientRecordList(String hospitalCode, String patientName, String hisPatientId,
                                                 String hospNo, String beginDate, String endDate, String status);

    // 查询住院患者预交金汇总信息
    InpatientAdvanceCharge getInpatientAdvanceCharge(String hospitalCode, String patientName, String regno);

    // 查询住院患者预交金明细
    List<InpatientAdvanceChargeDetail> getInpatientAdvanceChargeDetailList(String hospitalCode, String patientName, String regno);

    // 住院病人入院单信息填写
    SupplyHospitalAdmissionCertificateResult saveSupplyHospitalAdmissionCertificate(String hospitalCode,
                                                                                    SupplyHospitalAdmissionCertificateReq supplyHospitalAdmissionCertificate);

    // 查询门诊患者处方列表
    List<OutpatientRecipeInfo> getOutpatientRecipeList(String hospitalCode, String patientName, String patientId,
                                                       String beginDate, String endDate, String insuranceParam);

    // 住院病人入院单信息更新
    ReponseResult updateHospitalAdmissionCertificate(String hospitalCode, HospitalAdmissionCertificateReq param);

    // 线上入院判断
    ReponseResult checkHospitalAdmissionOnline(String hospitalCode, String patientName, String regNo,
                                               String admissionNo, String patientId);

    // 查询门诊患者缴费记录
    List<OutpatientCharge> getOutpatientChargeList(String hospitalCode, String name, String hisPatid, String channelType, String beginDate, String endDate);

    // 查询门诊患者缴费详细信息
    OutpatientChargeDetail getOutpatientChargeDetails(String hospitalCode, String settleId);

    // 查询住院患者费用明细信息
    List<InpatientFeeDetail> getInpatientFeeDetails(String code, String patientName, String regNo, String beginDate, String endDate);

    // 查询住院患者一日清单
    List<InpatientDaily> getInpatientDaily(String code, String patientName, String regNo, String date, String summaryFlag);

    // 查询住院患者结算记录
    List<InpatientSettleInfo> getInpatientSettleList(String code, String patientName, String regNo);

    // 查询患者入院证信息
    List<HospitalAdmissionCertificate> getHospitalAdmissionCertificateByCardNo(String code, String patientName, String cardNo,
                                                                               String hisPatid, String identity, String admissionNo,
                                                                               String beginDate, String endDate);
    // 查询住院患者出院带药信息
    List<DischargeMedication> getDischargeMedicationList(String code, String patientName, String regNo);

    // 查询患者手术提示告知信息
    List<OperationHints> getOperationHints(String code, String patientName, String regNo);

    // 查询患者手术进展信息
    List<OperationSchedule> getOperationSchedule(String code, String patientName, String userSource, String regNo);

    // 查询患者住院病历(文本段)
    List<InpatientMedicalRecordText> getInpatientMedicalRecordText(String code, String patientName, String regNo, String recType);

    // 入院登记提交
    HospitalAdmission addHospitalAdmission(String code, HospitalAdmissionReq param);

    // 申请电子发票（异步）
    ApplyElectronicInvoice applyElectronicInvoice(String code, ApplyElectronicInvoiceReq param);

    // 电子发票文件查询
    ElectronicInvoiceFile getElectronicInvoiceFile(String code, String hisPatid, String eleInvoiceNo);

    List<ElectronicInvoiceItem> getElectronicInvoiceList(String hospitalCode, String hisPatid, String online_type, String page, String size);

    // 查询患者体检记录
    List<PhysicalExaminationRecord> getPhysicalExaminationRecord(String hospitalCode, String beginDate, String endDate,
                                                                 String patientName, String idCardNum, String mobile);

    // 查询患者体检报告
    List<PhysicalExaminationReportFile> getPhysicalExaminationReportFile(String hospitalCode, String patientName, String tjbh);

    /**
     * <pre>
     *     门诊支付结果查询
     *     第三方交易对应的订单结果状态查询是否在HIS交易成功。在订单类交易发生失败时，如网络中断等，通过外部订单号查询结果状态（包含门诊预交金充值、门诊结算、住院预交金充值、住院结算、 门诊预约登记、医保支付结果查询）
     * </pre>
     * @param hospitalCode
     * @param settleId
     * @param tradeType
     * @param tradeNo
     * @return
     */
    OutpatientPayResult getOutpatientPayResult(String hospitalCode, String settleId, String tradeType, String tradeNo);

    /**
     * 结算取消结果确认

     */
    void sendRefundResult(ConfirmRefund confirmRefund);
    ApplyElectricInvoiceForHuLiResult applyElectricInvoiceForHuLi(String hospitalCode,
                                                                  ApplyElectricInvoiceForHuLiReq req);

    // 检查类别字典
    List<CheckCategory> getCheckCategoryList(String hospitalCode, String page, String size);

    // 检查项目字典
    List<CheckItemInfo> getCheckItemList(String hospitalCode, String page, String size, String categoryCode);
    List<CheckDevices> getCheckDevicesList(String hospitalCode, String page, String size);

    CommonResult pushDictChangeMsg(String hospitalCode, String msgType, String channelCode);

    ReportTimeLimit getReportTimeLimit(String hospitalCode, String itemCode);

    /**
     * 查询患者检查申请单信息
     * @param hospitalCode
     * @param pat_name 患者姓名
     * @param apply_no 申请单号
     * @param certificate_no 身份证号、军官证号、护照号等
     * @param card_no 就诊卡号码
     * @param pat_id 患者唯一号
     * @param begin_date 开始日期
     * @param end_date 结束日期
     * @return
     */
    List<ApplicationInfo> getApplicationInfoList(String hospitalCode, String pat_name, String apply_no, String certificate_no,
                                                 String card_no, String pat_id, String patient_type, String begin_date, String end_date);

    CommonResult cancelCheckApplication(String hospitalCode, String patname, String patid, String application_no,
                                        String application_category, String message);

    /**
     * 查询检查报告结果
     * @param hospitalCode
     * @param report_no 报告单号
     * @param report_type_code 报告类别代码
     * @return
     */
    List<CheckReportResult> getCheckReportResult(String hospitalCode, String report_no, String report_type_code);


    /**
     * 查询患者医技排队候诊信息
     * @param hospitalCode
     * @param patname 患者姓名
     * @param patid 门诊患者唯一号
     * @param regist_type 急诊标志
     * @return
     */
    MedTechWaitingList getMedTechWaiting(String hospitalCode, String patname, String patid, String regist_type);

    CommonResult pushAppointmentChangeMsg(String hospitalCode, String application_id, String appointment_id,
                                          String operation_id, String operation_name,
                                          String operation_time, String channel_code, String status, String message);

    /**
     * 查询门诊患者文本病历
     * @param patid
     * @return
     */
    List<PatientMedicalRecord> patientMedicalRecords(String hospitalCode, String patid, String beginDate,
                                                     String endDate);

    SyncInspectMRResult syncInspectMR(String hospcode, String patid, String regno, boolean agreed);

    CommonResult savePreConsultation(String hospcode, Map<String, Object> body);
}
