package cn.taihealth.ih.service.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.taihealth.ih.domain.UserSignCurrent;
import cn.taihealth.ih.domain.VisitorPass;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.dict.Sign;
import cn.taihealth.ih.service.vm.UploadVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class VisitorPassDTO extends UpdatableDTO {

    @ApiModelProperty("医院信息")
    private HospitalDTO hospital;

    // 患者姓名
    @ApiModelProperty("患者姓名")
    private String patname;

    // 患者性别
    @ApiModelProperty("patsex")
    private String patsex;

    // 患者证件号
    @ApiModelProperty("患者证件号")
    private String pat_certificate_no;

    // 住院号
    @ApiModelProperty("住院号")
    private String regno;

    // 入院日期
    @ApiModelProperty("入院日期")
    private Date in_time;

    // 科室名称
    @ApiModelProperty("科室名称")
    private String dept_name;

    // 病区名称
    @ApiModelProperty("病区名称")
    private String ward_name;

    // 床位号
    @ApiModelProperty("床位号")
    private String bed_no;

    // 陪护证号
    @ApiModelProperty("陪护证号")
    private String visitor_pass_no;


    // 陪护人证件号
    @ApiModelProperty("陪护人证件号")
    private String attendant_certificate_no;

    // 陪护人姓名
    @ApiModelProperty("陪护人姓名")
    private String attendant_name;

    // 陪护人性别
    @ApiModelProperty("陪护人性别")
    private String attendant_sex;

    // 陪护人关系
    @ApiModelProperty("陪护人关系")
    private Patient.Relationship relationship;

    // 陪护人电话
    @ApiModelProperty("陪护人电话")
    private String attendant_telephone;


    // 核酸检测结果
    @ApiModelProperty("核酸检测结果")
    private Integer nucleic_acid_test_result;


    // 开始陪护日期
    @ApiModelProperty("陪护证办理日期")
    private Date start_attendant_time;

    // 开始陪护日期
    @ApiModelProperty("失效日期")
    private Date end_attendant_time;

    // 状态
    @ApiModelProperty("状态")
    private VisitorPass.VisitorPassStatusEnum status;

    // 有效天数
    @ApiModelProperty("有效天数")
    private Integer affectDays;

    @ApiModelProperty("陪护人照片")
    private UploadDTO attendantPicture;

    // 陪护人照片
    @ApiModelProperty("核酸检测报告")
    private UploadDTO testReportPicture;

    @ApiModelProperty("拒绝理由")
    private String rejectReason;

    // 陪护证证书图片
    @ApiModelProperty("陪护证证书图片")
    private UploadDTO visitorPassCertPicture;

    public VisitorPassDTO(VisitorPass visitorPass) {
        super(visitorPass);
        this.hospital = new HospitalDTO(visitorPass.getHospital());

        if (visitorPass.getAttendantPicture() != null) {
            UploadDTO attendantPicture = new UploadDTO(visitorPass.getAttendantPicture());
            this.setAttendantPicture(attendantPicture);
        }

        if (visitorPass.getTestReportPicture() != null) {
            UploadDTO testReportPicture = new UploadDTO(visitorPass.getTestReportPicture());
            this.setTestReportPicture(testReportPicture);
        }

        if (visitorPass.getVisitorPassCertPicture() != null) {
            UploadDTO visitorPassCertPicture = new UploadDTO(visitorPass.getVisitorPassCertPicture());
            this.setVisitorPassCertPicture(visitorPassCertPicture);
        }

        // 患者相关信息
        this.setRegno(visitorPass.getRegno());
        this.setPatname(visitorPass.getPatname());
        this.setPatsex(visitorPass.getPatsex());
        this.setPat_certificate_no(visitorPass.getPat_certificate_no());
        this.setDept_name(visitorPass.getDept_name());
        this.setWard_name(visitorPass.getWard_name());
        this.setBed_no(visitorPass.getBed_no());
        this.setIn_time(visitorPass.getIn_time());

        // 陪护人相关信息
        this.setAttendant_name(visitorPass.getAttendant_name());
        this.setAttendant_certificate_no(visitorPass.getAttendant_certificate_no());
        this.setAttendant_sex(visitorPass.getAttendant_sex());
        this.setRelationship(visitorPass.getRelationship());
        this.setAttendant_telephone(visitorPass.getAttendant_telephone());

        // 陪护证自身信息
        this.setVisitor_pass_no(visitorPass.getVisitor_pass_no());
        this.setStatus(visitorPass.getStatus());
        this.setNucleic_acid_test_result(visitorPass.getNucleic_acid_test_result());
        this.setAffectDays(visitorPass.getAffectDays());
        this.setStart_attendant_time(visitorPass.getStart_attendant_time());
        this.setEnd_attendant_time(visitorPass.getEnd_attendant_time());
        this.setRejectReason(visitorPass.getRejectReason());

    }
}
