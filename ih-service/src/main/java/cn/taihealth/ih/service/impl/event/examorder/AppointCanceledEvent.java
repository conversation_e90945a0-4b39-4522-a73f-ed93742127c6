package cn.taihealth.ih.service.impl.event.examorder;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.hospital.ExamOrder;
import cn.taihealth.ih.service.impl.event.AbstractEntityEvent;

import java.util.Date;

/**
 * @Author: Moon
 * @Date: 2021/1/13 上午9:42
 */
public class AppointCanceledEvent extends AbstractEntityEvent<ExamOrder> {

    private Date startTime;

    private Date endTime;

    private Patient patient;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public AppointCanceledEvent(ExamOrder entity, Patient patient, Date startTime, Date endTime) {
        super(entity);
        this.patient = patient;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
