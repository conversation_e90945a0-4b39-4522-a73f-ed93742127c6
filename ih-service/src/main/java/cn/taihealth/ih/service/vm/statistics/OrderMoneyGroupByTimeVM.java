package cn.taihealth.ih.service.vm.statistics;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * 根据订单类型展示金额
 * @Author: Moon
 * @Date: 2020/11/20 上午10:24
 */
public class OrderMoneyGroupByTimeVM {

    @ApiModelProperty("时间")
    private String time;
    @ApiModelProperty("总金额")
    private String totalMoney;

    @ApiModelProperty("服务类型分类结果集")
    private List<OrderMoneyGroupByTypeVM> orderMoneyGroupByTypeVMs;

    public OrderMoneyGroupByTimeVM(String time, String totalMoney,
                                   List<OrderMoneyGroupByTypeVM> orderMoneyGroupByTypeVMs) {
        this.time = time;
        this.totalMoney = totalMoney;
        this.orderMoneyGroupByTypeVMs = orderMoneyGroupByTypeVMs;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(String totalMoney) {
        this.totalMoney = totalMoney;
    }

    public List<OrderMoneyGroupByTypeVM> getOrderMoneyGroupByTypeVMs() {
        return orderMoneyGroupByTypeVMs;
    }

    public void setOrderMoneyGroupByTypeVMs(
        List<OrderMoneyGroupByTypeVM> orderMoneyGroupByTypeVMs) {
        this.orderMoneyGroupByTypeVMs = orderMoneyGroupByTypeVMs;
    }
}
