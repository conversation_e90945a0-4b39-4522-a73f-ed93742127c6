package cn.taihealth.ih.service.vm.business;

import cn.taihealth.ih.domain.hospital.PrescriptionOrder.PrescriptionType;
import cn.taihealth.ih.domain.enums.ClinicType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class StatisticsOrderVM {

    // 统计封装症状症状分组
    private String[] diseasesArray;

    // 统计封装症状症状分组
    private String disease;


    // 统计封装症状症状分组
    private String diseasesName;

    // 统计封装时间
    private String time;

    // 订单类型
    private ClinicType orderType;

    // 处方单类型
    private PrescriptionType prescriptionType;

}
