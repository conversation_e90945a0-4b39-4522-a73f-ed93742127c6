package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.dict.ExamDevice;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.ExamOrder.ExamOrderStatus;
import cn.taihealth.ih.repo.order.ExamOrderRepository;
import cn.taihealth.ih.repo.order.OrderExtraInfoRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.OrderQueueService;
import cn.taihealth.ih.service.api.OrderService;
import cn.taihealth.ih.service.vm.ExamOrderQueueStatusVM;
import cn.taihealth.ih.service.vm.ExamOrderVM;
import cn.taihealth.ih.service.vm.OrderQueueStatusVM;
import cn.taihealth.ih.service.vm.OrderVM;
import com.gitq.jedi.data.specification.Specifications;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Author: Moon
 * @Date: 2020/9/28 12:40 上午
 */
@Service
@Slf4j
public class OrderQueueServiceImpl implements OrderQueueService {

    private final OrderService orderService;
    private final OrderRepository orderRepository;
    private final ExamOrderRepository examOrderRepository;

    private final OrderExtraInfoRepository orderExtraInfoRepository;

    public OrderQueueServiceImpl(OrderService orderService,
                                 OrderRepository orderRepository,
                                 ExamOrderRepository examOrderRepository,
                                 OrderExtraInfoRepository orderExtraInfoRepository) {
        this.orderService = orderService;
        this.orderRepository = orderRepository;
        this.examOrderRepository = examOrderRepository;
        this.orderExtraInfoRepository = orderExtraInfoRepository;
    }

    @Override
    public OrderQueueStatusVM getQueueStatusByOrder(Order order) {
        if (order.getDoctor() == null) {
            throw ErrorType.ILLEGAL_ORDER_STATUS.toProblem("订单尚未有医生接收");
        }
        if (order.getService() != null && order.getService().getMedicalWorker() != null) {
            return getQueueStatus(order);
        } else {
            Optional<OrderExtraInfo> byOrderId = orderExtraInfoRepository.findByOrderId(order.getId());
            if (byOrderId.isPresent()) {
                OrderExtraInfo orderExtraInfo = byOrderId.get();
                log.info("查询排队信息对应的his排班类型：{} 排班号：{}", orderExtraInfo.getHisScheduleType(), orderExtraInfo.getHisScheduleId());
                return getQueueStatus(order);
            } else {
                // TODO: 没到医生的， 由于业务发射变化， 未阐明这一块的业务， 这里的代码未做适配
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("没有挂号到医生的， 由于业务发生变化， 未阐明这一块的业务， 这里的代码未做适配");
            }
        }
    }

    private OrderQueueStatusVM getQueueStatus(Order order) {
        OrderQueueStatusVM vm = new OrderQueueStatusVM();
        MedicalWorker doctor = order.getDoctor();
        int estimateWaitTime = 0;
        // 到了医生的
        List<Specification<Order>> sp = new ArrayList<>();
        sp.add(Specifications.eq("hospital", order.getHospital()));
        sp.add(Specifications.eq("doctor", doctor));
        sp.add(Specifications.or(
                Specifications.eq("status", Order.OrderStatus.STARTED),
                Specifications.and(
                        Specifications.or(Specifications.eq("status", Order.OrderStatus.ONTIME_CONFIRMED),
                                Specifications.eq("status", Order.OrderStatus.NOTONTIME_CONFIRMED),
                                Specifications.eq("status", Order.OrderStatus.REGISTERED)),
                        Specifications.lt("createdDate", order.getCreatedDate())
                )
        ));

        long submitted = orderRepository.count(Specifications.and(sp));
        vm.setOrder(new OrderVM(order));
        vm.setNo(order.getOrderQueueNumber());
        vm.setWaitingPersons((int) submitted);
        if (doctor.getTreatmentDuration() != null) {
            estimateWaitTime = (int) (submitted * doctor.getTreatmentDuration());
            vm.setEstimateWaitTime(estimateWaitTime);
        }
        return vm;
    }

    @Override
    public ExamOrderQueueStatusVM getQueueStatusByExamOrder(ExamOrder order) {
        if (order.getStatus() != ExamOrderStatus.WAIT_CHECK) {
            throw ErrorType.ILLEGAL_ORDER_STATUS.toProblem("不在排队中");
        }
        ExamSchedule schedule = order.getService();
        ExamDevice device = schedule.getDevice();
        Specification<ExamOrder> se = Specifications.and(
            Specifications.eq("service.device", device),
            Specifications.eq("status", ExamOrderStatus.WAIT_CHECK));
        ExamOrder startInOrder = examOrderRepository.findAll(se).stream().findFirst().orElse(null);

        Date startTime = schedule.getStartTime();
        Date endTime = schedule.getEndTime();
        Specification<ExamOrder> ses = Specifications.and(
            Specifications.eq("service.device", device),
            Specifications.eq("status", ExamOrderStatus.WAIT_CHECK),
            Specifications.lt("signDate", order.getSignDate()),
            Specifications.or(
                Specifications.le("service.startTime", startTime),
                Specifications.le("service.endTime", endTime)
            )
        );
        long count = examOrderRepository.count(ses);

        ExamOrderQueueStatusVM vm = new ExamOrderQueueStatusVM();
        if (startInOrder != null) {
            vm.setCurrentNo(startInOrder.getQueueNumber());
        }
        vm.setNo(order.getQueueNumber());
        vm.setWaitingPersons((int)count);
        vm.setExamOrder(new ExamOrderVM(order));

        return vm;
    }
}
