package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.util.MoreBeanUtils;
import cn.taihealth.ih.domain.Doctor<PERSON>ee;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.hospital.DoctorFeeRepository;
import cn.taihealth.ih.service.api.DoctorFeeService;
import cn.taihealth.ih.service.dto.DoctorFeeDTO;
import com.google.common.collect.ImmutableSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class DoctorFeeServiceImpl implements DoctorFeeService {

    private final DoctorFeeRepository doctorFeeRepository;
    private final DeptRepository deptRepository;

    public DoctorFeeServiceImpl(DoctorFeeRepository doctorFeeRepository, DeptRepository deptRepository) {
        this.doctorFeeRepository = doctorFeeRepository;
        this.deptRepository = deptRepository;
    }

    @Override
    @Transactional
    public void saveDoctorFee(MedicalWorker doctor, DoctorFeeDTO dto) {
        Dept dept = deptRepository.getById(dto.getDeptVM().getId());
        DoctorFee doctorFee = doctorFeeRepository.findByDoctorAndDept(doctor, dept).orElse(new DoctorFee());
        MoreBeanUtils.copyPropertiesNonNull(doctorFee, dto, ImmutableSet.of("doctor", "dept"));
        doctorFee.setDept(dept);
        doctorFee.setDoctor(doctor);
        if (doctorFee.getEnabled()) {
            doctor.getDeptMedicalWorkers().forEach(u -> {
                if (Objects.equals(dept, u.getDept())) {
                    return;
                }
                doctorFeeRepository.updateEnabledByDoctorAndDept(doctor, u.getDept());
            });
        }
        doctorFeeRepository.save(doctorFee);
    }

    @Override
    public DoctorFee getDoctorFee(MedicalWorker doctor) {
        if (doctor == null) {
            return null;
        }
        List<DoctorFee> fees = doctor.getDoctorFees()
            .stream().filter(u -> u.getEnabled() && u.getDept() != null)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fees)) {
            return null;
        }
        return fees.get(0);
    }
}
