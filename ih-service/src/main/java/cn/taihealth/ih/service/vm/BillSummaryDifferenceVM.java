package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.enums.BillChannelEnum;
import cn.taihealth.ih.domain.enums.BillOperateTypeEnum;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.domain.hospital.HisBill;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

/**
 * 账单汇总差异数据
 */
@Data
public class BillSummaryDifferenceVM {

    @ApiModelProperty("第三方支付流水号")
    private String transactionId;

    @ApiModelProperty("医保结算订单号")
    private String payOrderId;

    @ApiModelProperty("金额")
    private String amount;

    @ApiModelProperty("服务类型")
    private ProjectTypeEnum billServiceType;

    @ApiModelProperty("交易操作类型")
    private BillOperateTypeEnum billOperateType;

    @ApiModelProperty("交易操作时间")
    private String orderOperateTime;

    @ApiModelProperty("是否医保")
    private Boolean insuranceFlag;

    @ApiModelProperty("支付渠道")
    private BillChannelEnum billChannel;

    @ApiModelProperty("来源")
    private String source;

    public BillSummaryDifferenceVM(Bill bill) {
        this.transactionId = bill.getTransactionId();
        this.payOrderId = bill.getPayOrderId();
        if (BillOperateTypeEnum.PAYMENT.equals(bill.getBillOperateType())) {
            this.amount = String.valueOf(bill.getPaymentAmount());
        } else {
            this.amount = String.valueOf(bill.getRefundAmount());
        }
        this.billServiceType = bill.getBillServiceType();
        this.billOperateType = bill.getBillOperateType();
        this.orderOperateTime = TimeUtils.dateToString(bill.getOrderOperateTime(), "yyyy-MM-dd HH:mm:ss");
        this.insuranceFlag = bill.getInsuranceFlag();
        this.billChannel = bill.getBillChannel();
        this.source = "IH";
    }

    public BillSummaryDifferenceVM(HisBill hisBill) {
        this.transactionId = hisBill.getTransaction_id();
        this.payOrderId = hisBill.getPayOrderId();
        if (hisBill.getProduct_name().contains("挂号")) {
            this.billServiceType = ProjectTypeEnum.APPOINTMENT_REGISTRATION;
        } else if (hisBill.getProduct_name().contains("门诊")) {
            this.billServiceType = ProjectTypeEnum.OUTPATIENT_PAYMENT;
        }
        if ("SUCCESS".equals(hisBill.getTransaction_status())) {
            this.amount = String.valueOf(hisBill.getOrder_amount());
            this.billOperateType = BillOperateTypeEnum.PAYMENT;
        } else if ("REFUND".equals(hisBill.getTransaction_status())) {
            this.amount = String.valueOf(hisBill.getRefund_amount());
            this.billOperateType = BillOperateTypeEnum.REFUND;
        }
        this.orderOperateTime = TimeUtils.dateToString(hisBill.getTransaction_time(), "yyyy-MM-dd HH:mm:ss");
        this.insuranceFlag = BooleanUtils.isTrue(hisBill.getInsuranceFlag());
        if (1 == hisBill.getPayType()) {
            this.billChannel = BillChannelEnum.WECHAT;
        } else if (2 == hisBill.getPayType()) {
            this.billChannel = BillChannelEnum.ALIPAY;
        } else {
            this.billChannel = BillChannelEnum.OTHER;
        }
        this.source = "HIS";
    }
}
