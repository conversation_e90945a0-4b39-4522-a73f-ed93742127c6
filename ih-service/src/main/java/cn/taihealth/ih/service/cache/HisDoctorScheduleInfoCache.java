package cn.taihealth.ih.service.cache;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.DoctorScheduleInfo;
import com.gitq.jedi.context.AppContext;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class HisDoctorScheduleInfoCache {

    private final HospitalRepository hospitalRepository;

    @Cacheable(cacheNames = Constants.CACHE_HIS_DOCTOR_SCHEDULE_INFO, unless = "#hospitalCode == null", key = "#hospitalCode")
    public List<DoctorScheduleInfo> getHisDocSchedule(String hospitalCode) {
        List<DoctorScheduleInfo> list = Lists.newArrayList();
        Optional<Hospital> oneByCode = AppContext.getInstance(HospitalCache.class).getHospital(hospitalCode);
        if (oneByCode.isPresent()) {
            Hospital hospital = oneByCode.get();
            BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
            list = businessService.getCurrentDayDoctorList(hospital, null)
                    .stream()
                    .filter(e -> {
                        String schedulingType = e.getScheduling_type();
                        if (StringUtils.isNotBlank(schedulingType)) {
                            return StringUtils.equalsAny(e.getScheduling_type(), "0", "1", "8", "9");
                        } else {
                            List<DoctorScheduleInfo.SchedulingSimpleData> schedulingSimpleData = e.getScheduling_simple_data();
                            if (CollectionUtils.isNotEmpty(schedulingSimpleData)) {
                                return schedulingSimpleData
                                        .stream()
                                        .anyMatch(e1 -> StringUtils.equalsAny(e1.getScheduling_type(), "0", "1", "8", "9"));
                            }
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        }
        return list;
    }

    @CacheEvict(cacheNames = Constants.CACHE_HIS_DOCTOR_SCHEDULE_INFO, key = "#hospitalCode")
    public void evictHisDocSchedule(String hospitalCode) {
    }
    @CacheEvict(cacheNames = Constants.CACHE_HIS_DOCTOR_SCHEDULE_INFO, allEntries = true)
    public void evictAllHisDocSchedule() {
    }

}
