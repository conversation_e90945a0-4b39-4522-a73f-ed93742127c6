package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WardPerformanceStatisticsDetail {

    @ApiModelProperty("科室名称")
    private String dept_name;

    @ApiModelProperty("出院总人数")
    private int discharged_count;

    @ApiModelProperty("随访总数")
    private int survey_count;

    @ApiModelProperty("随访率")
    private double survey_rate;

    @ApiModelProperty("成功总数")
    private int successed_count;

    @ApiModelProperty("成功率")
    private double successed_rate;

    @ApiModelProperty("占线总数")
    private int zx_count;

    @ApiModelProperty("放弃总数")
    private int fq_count;

    @ApiModelProperty("停机总数")
    private int tj_count;

    @ApiModelProperty("拒绝总数")
    private int jj_count;

    @ApiModelProperty("空错号总数")
    private int kch_count;

    @ApiModelProperty("无法接通总数")
    private int wfjt_count;

    @ApiModelProperty("无人接听总数")
    private int wrjt_count;

}
