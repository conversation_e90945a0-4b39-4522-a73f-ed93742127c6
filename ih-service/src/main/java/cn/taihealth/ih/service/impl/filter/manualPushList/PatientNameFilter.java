package cn.taihealth.ih.service.impl.filter.manualPushList;

import cn.taihealth.ih.domain.ManualPushList;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class PatientNameFilter implements SearchFilter<ManualPushList> {

    private final String value;

    @Override
    public String getName() {
        return getClass().getName();
    }

    public PatientNameFilter(String value) {
        this.value = value;
    }

    @Override
    public Specification<ManualPushList> toSpecification() {
        return Specifications.like("patient.name", value);
    }

    @Override
    public String toExpression() {
        return value;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof PatientNameFilter)) {
            return false;
        }

        PatientNameFilter rhs = (PatientNameFilter) other;
        return Objects.equals(value, rhs.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
}