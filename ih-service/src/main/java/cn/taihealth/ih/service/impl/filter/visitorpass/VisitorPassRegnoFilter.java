package cn.taihealth.ih.service.impl.filter.visitorpass;

import cn.taihealth.ih.domain.VisitorPass;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class VisitorPassRegnoFilter implements SearchFilter<VisitorPass> {

    private final String regno;

    public VisitorPassRegnoFilter(String regno) {
        this.regno = regno;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<VisitorPass> toSpecification() {
        return Specifications.likeIgnoreCase("regno", regno);
    }

    @Override
    public String toExpression() {
        String str = " regno:" + regno;
        return str;
    }

    @Override
    public boolean isValid() {
        return regno != null;
    }
}
