package cn.taihealth.ih.service.impl.filter.exam.check;

import cn.taihealth.ih.domain.hospital.Checks;
import cn.taihealth.ih.domain.hospital.ExamOrder.ExamOrderStatus;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 * @Description
 * <AUTHOR>
 * @date  2020/12/28 18:56
 */
public class AppointedFilter implements SearchFilter<Checks> {

    private final String pattern;

    public     AppointedFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Checks> toSpecification() {
        List<Specification<Checks>> fliter = Lists.newArrayList();
        fliter.add(Specifications.isNotNull("examOrder"));
        fliter.add(Specifications.or(
                Specifications.eq("examOrder.status", ExamOrderStatus.WAIT_SIGNIN),
                Specifications.eq("examOrder.status", ExamOrderStatus.WAIT_CHECK)
        ));
        return Specifications.and(fliter);
    }

    @Override
    public String toExpression() {
        return "Checks:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof AppointedFilter)) {
            return false;
        }

        AppointedFilter rhs = (AppointedFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
