package cn.taihealth.ih.service.impl.filter.exam.check;

import cn.taihealth.ih.domain.hospital.Checks;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;


/**
 * @Description
 * <AUTHOR>
 * @date  2020/12/28 18:57
 */
public class CategoryFilter implements SearchFilter<Checks> {

    private final String pattern;

    public CategoryFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Checks> toSpecification() {
        return Specifications.eq("examItem.category.id", pattern);
    }

    @Override
    public String toExpression() {
        return "category:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof CategoryFilter)) {
            return false;
        }

        CategoryFilter rhs = (CategoryFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
