package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.OrderTIMMessageRel;
import cn.taihealth.ih.domain.TIMMessage;
import cn.taihealth.ih.service.dto.OrderDTO;
import cn.taihealth.ih.service.dto.TIMMessageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
@Accessors(chain = true)
public class TIMConversationVM {

    @ApiModelProperty("群组id")
    private String groupId;

    @ApiModelProperty("会话类型")
    private String type = "Group";

    @ApiModelProperty("头像用户")
    private UserVM user;

    @ApiModelProperty("订单实体")
    private OrderDTO order;

    @ApiModelProperty("最后一条消息")
    private TIMMessageDTO timMessageDTO;

    @ApiModelProperty("未读消息数")
    private Integer unreadCount;


    public TIMConversationVM(OrderTIMMessageRel orderTIMMessageRel) {
        TIMMessage timMessage = orderTIMMessageRel.getTimMessage();
        groupId = timMessage.getGroupId();
        order = new OrderDTO(orderTIMMessageRel.getMOrder());
        timMessageDTO = new TIMMessageDTO(timMessage);
        user = timMessageDTO.getUser();
    }


}
