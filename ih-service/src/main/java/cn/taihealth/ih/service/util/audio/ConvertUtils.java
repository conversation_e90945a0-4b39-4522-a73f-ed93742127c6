package cn.taihealth.ih.service.util.audio;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;

public class ConvertUtils {
    /**
     * 转换音频文件
     * @param src 需要转换的pcm音频路径
     * @param target 保存转换后wav格式的音频路径
     * @throws Exception
     */
    public static void convertPcm2Wav(String src, String target) throws Exception {
        File file = new File(src);
        try (FileInputStream fis = new FileInputStream(file);
            FileOutputStream fos = new FileOutputStream(target)) {

            int PCMSize = (int)file.length();
            //填入参数，比特率等等。这里用的是16位单声道 8000 hz
            WaveHeader header = new WaveHeader();
            //长度字段 = 内容的大小（PCMSize) + 头部字段的大小(不包括前面4字节的标识符RIFF以及fileLength本身的4字节)
            header.fileLength = PCMSize + (44 - 8);
            header.FmtHdrLeth = 16;
            header.BitsPerSample = 16;
            header.Channels = 2;
            header.FormatTag = 0x0001;
            header.SamplesPerSec = 8000;
            header.BlockAlign = (short) (header.Channels * header.BitsPerSample / 8);
            header.AvgBytesPerSec = header.BlockAlign * header.SamplesPerSec;
            header.DataHdrLeth = PCMSize;

            byte[] h = header.getHeader();
            assert h.length == 44; //WAV标准，头部应该是44字节
            // write header
            fos.write(h, 0, h.length);
            // write data stream
            byte[] buf = new byte[1024 * 4];
            int size = fis.read(buf);
            while (size != -1) {
                fos.write(buf, 0, size);
                size = fis.read(buf);
            }
        }
    }


    /**
     * wav格式转换成mp3格式
     * @param source  源文件
     * @param target 目标文件
     * @return
     */
//    public static boolean convertWav2Mp3(File source, File target) {
//        boolean succeeded = true;
//        try {
//            AudioAttributes audio = new AudioAttributes();
//            audio.setCodec("libmp3lame");
//            audio.setBitRate(8000);
//            audio.setChannels(2);
//            audio.setSamplingRate(8000);
//            audio.setVolume(256);
//
//            EncodingAttributes attrs = new EncodingAttributes();
//            attrs.setFormat("mp3");
//            attrs.setAudioAttributes(audio);
//            Encoder encoder = new Encoder();
//            encoder.encode(new MultimediaObject(source), target, attrs);
//        } catch (Exception ex) {
//            succeeded = false;
//        }
//        return succeeded;
//    }

}
