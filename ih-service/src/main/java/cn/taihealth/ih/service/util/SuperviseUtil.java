package cn.taihealth.ih.service.util;

import cn.hutool.json.JSONUtil;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.SuperviseEnum;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

@Slf4j
public class SuperviseUtil {

    public static SuperviseDto getDto(Hospital hospital) {
        SuperviseDto superviseDto = new SuperviseDto();
        SuperviseEnum superviseEnum = HospitalSettingsHelper
            .getValue(hospital, HospitalSettingKey.SUPERVISE_TYPE, SuperviseEnum.class);
        String code = HospitalSettingsHelper
            .getString(hospital, HospitalSettingKey.LIAONING_KEY);
        String secret = HospitalSettingsHelper
            .getString(hospital, HospitalSettingKey.LIAONING_SECRET);
        Boolean debugMode = HospitalSettingsHelper
            .getBoolean(hospital, HospitalSettingKey.LIAONING_DEBUG);

        String medicalInsuranceOrgCodg = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.MEDICAL_INSURANCE_ORG_CODG);

        superviseDto.setSuperviseEnum(superviseEnum);
        superviseDto.setCode(code);
        superviseDto.setSecret(secret);
        superviseDto.setDebugMode(debugMode);
        superviseDto.setHospitalCode(hospital.getCode());

        superviseDto.setHosOrgCode(medicalInsuranceOrgCodg);
        superviseDto.setHost(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SICHUAN_URL));
        log.info("医院{}获取到监管平台配置{} ", hospital.getCode(), JSONUtil.toJsonStr(superviseDto));

        if(CollectionUtils.isNotEmpty(hospital.getOfflineHospitals())) {
            OfflineHospital offlineHospital = hospital.getOfflineHospitals().get(0);
            superviseDto.setOfflineHospital(offlineHospital);
            log.info("医院{}获取到监管平台配置 线下医院id：{} name：{} ", hospital.getCode(), offlineHospital.getId(),
                     offlineHospital.getName());
        }
        return superviseDto;
    }
}
