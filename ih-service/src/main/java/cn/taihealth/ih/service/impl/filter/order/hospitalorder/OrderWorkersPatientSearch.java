package cn.taihealth.ih.service.impl.filter.order.hospitalorder;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.OrderWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import java.util.Objects;
import javax.persistence.criteria.Join;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 */
public class OrderWorkersPatientSearch implements SearchFilter<OrderWorker> {

    private final String pattern;

    public OrderWorkersPatientSearch(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OrderWorker> toSpecification() {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Join<OrderWorker, Order> orderJoin = root.join("order");
            Join<Order, Patient> patientJoin = orderJoin.join("patient");
            return criteriaBuilder.like(patientJoin.get("name"), "%" + pattern + "%");
//            Join<Order, User> userJoin = orderJoin.join("user");
//            return criteriaBuilder.like(userJoin.get("fullName"), "%" + pattern + "%");
        };
    }

    @Override
    public String toExpression() {
        return "Order:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof OrderWorkersPatientSearch)) {
            return false;
        }

        OrderWorkersPatientSearch rhs = (OrderWorkersPatientSearch) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
