package cn.taihealth.ih.service.vm;


import cn.taihealth.ih.commons.util.IdCardUtils;
import cn.taihealth.ih.commons.valid.XSSValid;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.MedicalWorker.OfflineUnitPosition;
import cn.taihealth.ih.domain.hospital.MedicalWorker.OutTitle;
import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.OfflineHospitalRepository;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.dto.OfflineHospitalDTO;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gitq.jedi.context.AppContext;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class MedicalWorkerRegisterVM extends AbstractEntityDTO {

    @ApiModelProperty("姓名")
    @Size(max = 64, message = "姓名最长64个字")
    @NotBlank(message = "名字必填")
    private String name;

    @ApiModelProperty("手机号")
    @NotNull(message = "手机号不可为空")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String phone;

    @ApiModelProperty("科室")
    private List<Long> deptIds;

    @ApiModelProperty("专业职称")
    private String title;

    @ApiModelProperty(value = "个人简介")
    @Size(max = 5000, message = "个人简介最长500个字符")
    @XSSValid
    private String introduction;

    @ApiModelProperty("专业特长")
    @Size(max = 5000, message = "专业特长最长500个字符")
    private String areasOfExpertise;

    @ApiModelProperty("头像对象")
    private UploadVM avatar;

    @ApiModelProperty("签名")
    private UploadVM signature;

    @ApiModelProperty("身份证号")
    @NotNull(message = "身份证不可为空")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String identity;

    @ApiModelProperty(value = "身份证人像面")
    private UploadVM portrait;

    @ApiModelProperty(value = "身份证国徽面")
    private UploadVM nationalEmblem;

    @ApiModelProperty("执业证书编号")
    private String practisingNumber;

    @ApiModelProperty("执业医院")
    private OfflineHospitalDTO offlineHospital;

    @ApiModelProperty("执业证照片")
    private List<UploadVM> practisingImages;

    @ApiModelProperty("资格证书编号")
    private String certificate;

    @ApiModelProperty("资格证照片")
    private List<UploadVM> certificationImages;

    @ApiModelProperty("对外称呼")
    private OutTitle outTitle;

    @ApiModelProperty("线下单位职务")
    private OfflineUnitPosition offlineUnitPosition;

    @ApiModelProperty("线下单位")
    private String offlineUnit;

    @ApiModelProperty("医助对应用户，后端使用，前端不用处理，不用关心")
    private User assistant;

    public MedicalWorker toEntity() {
        MedicalWorker medicalWorker = new MedicalWorker();
        if (getId() != null) {
            medicalWorker = AppContext.getInstance(MedicalWorkerRepository.class).getById(getId());
        }
        medicalWorker.setPhone(phone);
        medicalWorker.setTitle(title);
        medicalWorker.setIntroduction(introduction);
        medicalWorker.setAreasOfExpertise(areasOfExpertise);
        if (!ObjectUtils.isEmpty(signature)) {
            medicalWorker.setSignature(AppContext.getInstance(UploadRepository.class).getById(signature.getId()));
        }
        if (!ObjectUtils.isEmpty(identity)) {
            medicalWorker.setIdentity(identity);
            int code = Integer.parseInt(Objects.requireNonNull(IdCardUtils.isValidatedAllIdCard(identity)).getSex());
            medicalWorker.setGender(code == 0 ? Gender.FEMALE : Gender.MALE);
        }
        if (!ObjectUtils.isEmpty(offlineHospital)) {
            medicalWorker.setOfflineHospital(
                AppContext.getInstance(OfflineHospitalRepository.class).getById(offlineHospital.getId()));
        }
        medicalWorker.setCertificate(certificate);

        return medicalWorker;
    }

}
