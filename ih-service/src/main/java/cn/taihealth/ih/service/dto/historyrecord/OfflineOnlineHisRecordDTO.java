package cn.taihealth.ih.service.dto.historyrecord;

import cn.taihealth.ih.service.dto.PatientDTO;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import cn.taihealth.ih.service.dto.UploadDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 线下处方记录表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class OfflineOnlineHisRecordDTO extends UpdatableDTO {

    private Long id;

    @ApiModelProperty(value = "处方单药物", required = true)
    private String content;

    @ApiModelProperty(value = "处方单时间", required = true)
    private Date prescriptionTime;

    @ApiModelProperty(value = "就诊人", required = true)
    private PatientDTO patient;

    @ApiModelProperty(value = "上传处方单", required = true)
    private List<UploadDTO> upload = Lists.newArrayList();

    @ApiModelProperty(value = "线上处方单或者线下处方单", required = true)
    private String type;

}
