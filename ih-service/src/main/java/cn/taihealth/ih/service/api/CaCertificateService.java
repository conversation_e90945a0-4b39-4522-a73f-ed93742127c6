package cn.taihealth.ih.service.api;

import cn.taihealth.ih.ca.been.shukey.UKeySignedCallBack;
import cn.taihealth.ih.ca.enums.SignatureType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.vm.ca.*;

/**
 * ca认证，需要走ca
 */
public interface CaCertificateService {

    MedicalWorkerCaQrcodeVM loginWithQrcode(Hospital hospital, String random);

    /**
     * 信安处方签章
     * @param hospital
     * @param prescriptionOrderId
     * @param signatureType
     * @param accountId
     * @return
     */
    SignaturePdfVM prescriptionOrderSignatureXinan(Hospital hospital, User user, long prescriptionOrderId, SignatureType signatureType, String accountId);

    /**
     * 获取处方签名原文
     * @param orderId
     * @return
     */
    CaUnsignedDiagnosisVM prescriptionsToPdf(long orderId);

    /**
     * ca签章
     * @param hospital
     * @param user
     * @param prescriptionOrderId
     * @param signatureType
     * @param param
     */
    SignaturePdfVM prescriptionOrderSignatureSH(Hospital hospital, User user, long prescriptionOrderId, SignatureType signatureType, CaPrescriptionParam param);

    /**
     * 检查签章是否成功
     * @param hospital
     * @param random
     * @return
     */
    boolean checkSignatureSuccessXinan(Hospital hospital, String random);

    /**
     * 下载医生签章后的pdf
     * @param hospital
     * @param user
     * @param ticket
     */
    void uploadDoctorSignaturePdfXinan(Hospital hospital, User user, PdfTicketVM ticket);

    /**
     * 下载药师签章后的pdf
     * @param hospital
     * @param user
     * @param ticket
     */
    void uploadDoctorReviewSignaturePdfXinan(Hospital hospital, User user, PdfTicketVM ticket);

    /**
     * 信安诊断单签章
     * @param hospital
     * @param user
     * @param orderId
     * @param accountId
     * @return
     */
    SignaturePdfVM diagnosisSignatureXinan(Hospital hospital, User user, long orderId, String accountId);

    SignaturePdfVM diagnosisSignatureSH(Hospital hospital, User user, long orderId, CaPrescriptionParam caParam);

    /**
     * 上海ca诊断单签章
     * @param orderId
     * @param signedCallBack
     */
    void diagnosisSignatureSHSavePdf(long orderId, UKeySignedCallBack signedCallBack);

    /**
     * 生成未签章的诊断pdf
     * @param hospital
     * @param user
     * @param orderId
     * @return
     */
    CaUnsignedDiagnosisVM diagnosisToPdf(Hospital hospital, User user, long orderId);

    /**
     * 下载信安诊断单签章后的pdf
     * @param hospital
     * @param user
     * @param ticket
     */
    void uploadDiagnosisSignaturePdfXinan(Hospital hospital, User user, PdfTicketVM ticket);

    /**
     * 上海ca处方单开立签章
     * @param orderId
     * @param signedCallBack
     */
    void prescriptionSignatureSHSavePdf(long orderId, UKeySignedCallBack signedCallBack);

    /**
     * 上海ca处方单审核通过签章
     * @param orderId
     * @param signedCallBack
     */
    void prescriptionSignaturePassedSHSavePdf(long orderId, UKeySignedCallBack signedCallBack);
}
