package cn.taihealth.ih.service.api.medicalrecordcooyappointment;

import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointment;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointmentOperation.Step;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.service.dto.express.LogisticsRouteInfoDTO;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.UserMedicalRecordCopyAppointmentDTO;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.UserMedicalRecordCopyAppointmentUnitPriceDTO;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface MedicalRecordCopyAppointmentService {

    UserMedicalRecordCopyAppointmentDTO saveAppointment(Hospital hospital, User user, ElectronicMedicCard card, UserMedicalRecordCopyAppointmentDTO dto);
    UserMedicalRecordCopyAppointmentDTO createAppointment(Hospital hospital, User user, ElectronicMedicCard card, UserMedicalRecordCopyAppointmentDTO dto);
    UserMedicalRecordCopyAppointmentDTO appendAppointment(UserMedicalRecordCopyAppointment copyAppointment);

    void copyAppointmentPending(Long offlineOrderId);
    void appendCopyAppointmentPending(Long offlineOrderId);
    void payCopyAppointment(User user, Long offlineOrderId, WechatOrder order);
    void payCopyAppointment(User user, Long offlineOrderId, AliPayOrder order);
    void payAppendCopyAppointment(User user, Long offlineOrderId, AliPayOrder order);
    void payAppendCopyAppointment(User user, Long offlineOrderId, WechatOrder order);
    UserMedicalRecordCopyAppointmentDTO refundAppointment(User user, Long offlineOrderId, Step step, String remarks);
    OfflineOrder refundedAppointment(long offlineOrderId, WechatOrderRefund refund);
    OfflineOrder refundedAppointment(long offlineOrderId, AliPayOrderRefund refund);

    void cancelAppointment(User user, UserMedicalRecordCopyAppointment copyAppointment);
    void applyRefundAppointment(User user, UserMedicalRecordCopyAppointment copyAppointment);
    void receivedAppointment(User user, UserMedicalRecordCopyAppointment copyAppointment);
    Page<UserMedicalRecordCopyAppointmentDTO> queryAppointment(Hospital hospital, User user, String query, Pageable page);

    UserMedicalRecordCopyAppointmentDTO operateMedicalRecordAppointmentOrder(Hospital hospital, User user, UserMedicalRecordCopyAppointmentDTO userMedicalRecordCopyAppointmentDTO);

    List<LogisticsRouteInfoDTO> getLogisticsInfo(Hospital hospital, long id);

    StatisticsOrdersVM getStatisticsOrders(Hospital hospital);

    UserMedicalRecordCopyAppointmentDTO getAppointmentInfo(long copyAppointmentId);

    CopyAppointmentAccountCheckVM getCopyAppointmentAccountCheck(Hospital hospital, String query, Pageable page);

    Page<DuplicateStatisticsVM> queryDuplicateStatistics(Hospital hospital, String query, int pageNo, int size);

    UploadVM exportDuplicateStatistics(Hospital hospital, String query, HttpServletResponse response);

    DuplicateTotalStatisticsVM queryTopDuplicateTotalStatistics(Hospital hospital, String query, String filterMonth);

    List<DuplicateStatisticsVM> queryBottomDuplicateTotalStatistics(Hospital hospital, String filterMonth);

    List<CopyPurposeStatisticsVM> queryCopyPurposeStatistics(Hospital hospital, String filterMonth);

    List<PayFeeStatisticsVM> queryPayFeeStatistics(Hospital hospital, String filterYear);

    UserMedicalRecordCopyAppointmentUnitPriceDTO updateUnitPrice(long id, UserMedicalRecordCopyAppointmentUnitPriceDTO dto, Hospital hospital);

    /**
     * 查询病案复印账单
     * @param hospital
     * @param query
     * @param pageRequest
     * @return
     */
    Page<CopyAppointmentBillVM> getBills(Hospital hospital, String query, PageRequest pageRequest);

    /**
     * 查询病案复印对账单统计
     * @param hospital
     * @param query
     * @return
     */
    CopyAppointmentBillStatsVM getBillStats(Hospital hospital, String query);

    /**
     * 导出病案复印交易记录
     * @param hospital
     * @param query
     * @return
     */
    Upload exportCopyAppointmentBills(Hospital hospital, String query);
}
