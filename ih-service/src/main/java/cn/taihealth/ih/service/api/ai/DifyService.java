package cn.taihealth.ih.service.api.ai;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.dto.ai.*;
import cn.taihealth.ih.service.dto.ai.dify.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.InputStream;
import java.util.List;

public interface DifyService {

    /**
     * 获取应用信息
     * @param hospital
     * @return
     */
    SiteResponse site(Hospital hospital);

    /**
     * 获取应用配置信息
     * @param hospital
     * @return
     */
    ParametersResponse parameters(Hospital hospital);

    /**
     * 获取应用Meta信息
     * @param hospital
     * @return
     */
    MetaResponse meta(Hospital hospital);

    /**
     * 上传文件
     * @param user
     * @param hospital
     * @param file
     * @return
     */
    UploadResponse upload(String user, Hospital hospital, MultipartFile file);

    /**
     * 对话 流式
     * @param user
     * @param hospital
     * @param message
     * @return
     */
    SseEmitter chatStreaming(User user, Hospital hospital, MessageRequest message);

    /**
     * 停止响应
     * @param user
     * @param hospital
     * @param taskId
     * @return
     */
    StopResponse stop(String user, Hospital hospital, String taskId);

    /**
     * 对话
     * @param user
     * @param hospital
     * @param message
     * @return
     */
    MessageResponse chat(User user, Hospital hospital, MessageRequest message);

    /**
     * 消息反馈（点赞）
     * @param user
     * @param hospital
     * @param messageId
     * @param request
     * @return
     */
    FeedbackResponse feedbacks(String user, Hospital hospital, String messageId, FeedbackRequest request);

    /**
     * 获取下一轮建议问题列表
     * @param user
     * @param messageId
     * @return
     */
    SuggestedResponse suggested(String user, Hospital hospital, String messageId);

    /**
     * 历史对话消息
     *
     * @param user
     * @param conversationId
     * @return
     */
    HistoryMessages messages(String user, Hospital hospital, String conversationId, String limit, String firstId);

    /**
     * 会话列表
     * @param user
     * @return
     */
    HistoryConversations conversations(String user, Hospital hospital, String lastId, String limit, String pinned, String sortBy);

    /**
     * 重命名会话
     * @param current
     * @param hospital
     * @param conversationId
     * @param renameRequest
     * @return
     */
    String renameConversations(User current, Hospital hospital, String conversationId, RenameRequest renameRequest);

    /**
     * 删除会话
     * @param current
     * @param conversationId
     * @return
     */
    String deleteConversations(User current, Hospital hospital, String conversationId);

    /**
     * 获取对话变量
     * @param hospital
     * @param conversationId
     * @return
     */
    VariablesResponse variables(Hospital hospital, String conversationId);

    /**
     * 语音转文字
     * @param current
     * @param hospital
     * @param file
     * @return
     */
    AudioToTextResponse audioToText(User current, Hospital hospital, MultipartFile file);

    /**
     * 文字转语音
     * @param current
     * @param hospital
     * @param textToAudioRequest
     * @return
     */
    byte[] textToAudio(User current, Hospital hospital, TextToAudioRequest textToAudioRequest);
}
