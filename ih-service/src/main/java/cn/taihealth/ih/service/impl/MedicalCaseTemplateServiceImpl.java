package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.MedicalCaseContent;
import cn.taihealth.ih.domain.hospital.MedicalCaseTemplate;
import cn.taihealth.ih.domain.hospital.MedicalCaseTemplate.NodeType;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.repo.MedicalCaseContentRepository;
import cn.taihealth.ih.repo.MedicalCaseTemplateRepository;
import cn.taihealth.ih.service.api.MedicalCaseTemplateService;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.dto.MedicalCaseContentDTO;
import cn.taihealth.ih.service.dto.MedicalCaseTemplateDTO;
import cn.taihealth.ih.service.impl.filter.medicalcasecontent.MedicalCaseContentSearch;
import cn.taihealth.ih.service.vm.MedicalCaseTemplateVM;
import com.gitq.jedi.data.specification.Specifications;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Moon
 * @Date: 2021/2/26 上午9:52
 */
@Service
public class MedicalCaseTemplateServiceImpl implements MedicalCaseTemplateService {

    private final MedicalCaseTemplateRepository medicalCaseTemplateRepository;
    private final MedicalCaseContentRepository medicalCaseContentRepository;

    public MedicalCaseTemplateServiceImpl(
            MedicalCaseTemplateRepository medicalCaseTemplateRepository,
            MedicalCaseContentRepository medicalCaseContentRepository) {
        this.medicalCaseTemplateRepository = medicalCaseTemplateRepository;
        this.medicalCaseContentRepository = medicalCaseContentRepository;
    }

    @Override
    @Transactional
    public MedicalCaseTemplateDTO addTemplate(MedicalCaseTemplateDTO dto, Hospital hospital, User user, MedicalWorker medicalWorker) {
        MedicalCaseTemplate template;

        if (Objects.isNull(medicalWorker) && StringUtils.isBlank(dto.getName())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("模板名称不能为空");
        }
        if (dto.isNew()) {
            template = new MedicalCaseTemplate();
            AbstractEntityDTO parentDTO = dto.getParent();
            MedicalCaseTemplate parent = null;
            if (parentDTO != null && !parentDTO.isNew()) {
                parent = medicalCaseTemplateRepository.getById(parentDTO.getId());
                if (parent.getType() == NodeType.TEMPLATE) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("模板下不能创建内容");
                }
            }
            template.setParent(parent);
            template.setCreatedUser(user);

            this.checkTemplateNameUnique(dto, hospital, medicalWorker);
        } else {
            template = medicalCaseTemplateRepository.getById(dto.getId());
            if (!template.getName().equals(dto.getName())) {
                this.checkTemplateNameUnique(dto, hospital, medicalWorker);
            }
            template.setUpdatedUser(user);
        }

        template.setHospital(hospital);
        template.setOrderNumber(dto.getOrderNumber());
        template.setName(dto.getName());
        template.setType(dto.getType());
        template.setDoctor(medicalWorker);

        medicalCaseTemplateRepository.save(template);

        MedicalCaseContentDTO dto1 = dto.getContent();
        MedicalCaseContent content = null;
        if (dto1 != null) {
            content = medicalCaseContentRepository.findByTemplate(template).orElse(new MedicalCaseContent());
            content.setAllergiesHistory(dto1.getAllergiesHistory());
            content.setChecking(dto1.getChecking());
            content.setDiagnosis(dto1.getDiagnosis());
            content.setNowMedicalHistory(dto1.getNowMedicalHistory());
            content.setOldMedicalHistory(dto1.getOldMedicalHistory());
            content.setSelfSpeak(dto1.getSelfSpeak());
            content.setContent(dto1.getContent());
            content.setTemplate(template);
            medicalCaseContentRepository.save(content);
        }

        return new MedicalCaseTemplateDTO(template, content);
    }

    /**
     * 验证模板名称是否重复
     *
     * @param dto           入参
     * @param hospital      医院信息
     * @param medicalWorker 医生信息
     */
    private void checkTemplateNameUnique(MedicalCaseTemplateDTO dto, Hospital hospital, MedicalWorker medicalWorker) {
        if (Objects.nonNull(medicalWorker)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(medicalCaseTemplateRepository.
                findByHospitalAndNameAndType(hospital, dto.getName(), dto.getType()))) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("名称不能重复");
        }
    }

    @Override
    public List<MedicalCaseTemplateVM> getTemplates(Hospital hospital) {
        List<MedicalCaseTemplate> templates = medicalCaseTemplateRepository.findByHospital(hospital);
        return toTemplateVM(templates);
    }

    @Override
    @Transactional
    public void removeTemplate(MedicalCaseTemplate template) {
        List<MedicalCaseTemplate> delList = Lists.newArrayList();
        getToDeleteItems(template, delList);
        delList.forEach(this::deleteTemplateContent);
        medicalCaseTemplateRepository.delete(template);
    }

    @Override
    public Page<MedicalCaseTemplateDTO> getTemplateList(String query, Pageable request, Hospital hospital, MedicalWorker medicalWorker) {
        List<Specification<MedicalCaseContent>> specs = new LinkedList<>();
        MedicalCaseContentSearch search = MedicalCaseContentSearch.of(query);
        specs.add(search.toSpecification());
        Specification<MedicalCaseContent> templateFilter
                = (Specification<MedicalCaseContent>) (root, criteriaQuery, criteriaBuilder) -> {
            Join<MedicalCaseContent, MedicalCaseTemplate> join = root.join("template", JoinType.INNER);
            Predicate hospitalPredicate = criteriaBuilder.equal(join.get("hospital"), hospital);
            Predicate typePredicate = criteriaBuilder.equal(join.get("type"), NodeType.TEMPLATE);
            Predicate doctorPredicate = criteriaBuilder.equal(join.get("doctor"), medicalWorker);
            if (Objects.isNull(medicalWorker)) {
                doctorPredicate = criteriaBuilder.isNull(join.get("doctor"));
            }

            return criteriaBuilder.and(hospitalPredicate, doctorPredicate, typePredicate);
        };
        specs.add(templateFilter);
        Page<MedicalCaseContent> page = medicalCaseContentRepository.findAll(Specifications.and(specs), request);
        Map<MedicalCaseTemplate, MedicalCaseTemplate> templateMap = new HashMap<>();
        if (page.hasContent()) {
            List<Long> templateIds = page.getContent().stream().map(template -> template.getTemplate().getId()).collect(Collectors.toList());
            templateMap = medicalCaseTemplateRepository.findByIdIn(templateIds).stream().collect(Collectors.toMap(t -> t, t -> t));
        }

        Map<MedicalCaseTemplate, MedicalCaseTemplate> finalTemplateMap = templateMap;
        return page.map((content) -> new MedicalCaseTemplateDTO(finalTemplateMap.get(content.getTemplate()), content));
    }

    private void getToDeleteItems(MedicalCaseTemplate template, List<MedicalCaseTemplate> del) {
        del.add(template);
        List<MedicalCaseTemplate> children = template.getTemplates();
        if (!CollectionUtils.isEmpty(children)) {
            children.forEach(u -> getToDeleteItems(u, del));
        }
    }

    private void deleteTemplateContent(MedicalCaseTemplate template) {
        Optional<MedicalCaseContent> content = medicalCaseContentRepository.findByTemplate(template);
        content.ifPresent(medicalCaseContentRepository::delete);
    }

    private List<MedicalCaseTemplateVM> toTemplateVM(List<MedicalCaseTemplate> templates) {
        List<MedicalCaseTemplateVM> templateVMS = Lists.newArrayList();
        List<MedicalCaseTemplate> surplus = Lists.newArrayList();

        templates.sort(Comparator.comparingDouble(MedicalCaseTemplate::getOrderNumber));
        templates.forEach(u -> {
            if (u.getParent() == null) {
                templateVMS.add(new MedicalCaseTemplateVM(u));
            } else {
                surplus.add(u);
            }
        });
        templateVMS.forEach(u -> u.setTemplates(toTemplateVM(u, surplus)));
        return templateVMS;
    }

    private List<MedicalCaseTemplateVM> toTemplateVM(MedicalCaseTemplateVM templateVM,
                                                     List<MedicalCaseTemplate> template) {
        List<MedicalCaseTemplateVM> templateVMS = Lists.newArrayList();
        List<MedicalCaseTemplate> surplus = Lists.newArrayList();
        template.forEach(u -> {
            if (Objects.equals(u.getParent().getId(), templateVM.getId())) {
                templateVMS.add(new MedicalCaseTemplateVM(u));
            } else {
                surplus.add(u);
            }
        });
        templateVMS.forEach(u -> u.setTemplates(toTemplateVM(u, surplus)));
        return templateVMS;
    }
}
