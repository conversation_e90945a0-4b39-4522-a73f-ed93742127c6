package cn.taihealth.ih.service.impl.strategy;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.ElectronicMedicCard.CardType;
import cn.taihealth.ih.domain.MedicalCase;
import cn.taihealth.ih.domain.MedicalCaseDisease;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.dict.ExamCategory;
import cn.taihealth.ih.domain.dict.ExamDevice;
import cn.taihealth.ih.domain.dict.ExamItem;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.his.HisAdmissionInfo;
import cn.taihealth.ih.domain.his.HisElectronicInvoice;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.ChargeItem.QueryScope;
import cn.taihealth.ih.domain.hospital.Disease.DiagFlag;
import cn.taihealth.ih.domain.hospital.Disease.UseRange;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.Status;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.domain.hospital.offline.OfflineDeptMedicalWorker;
import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorker;
import cn.taihealth.ih.domain.nursing.NursingHomeChargeItemPrice.NursingChargeItemPrice;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.his.HisAdmissionInfoRepository;
import cn.taihealth.ih.repo.his.HisElectronicInvoiceRepository;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.hospital.DiseaseRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineDeptMedicalWorkerRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineDeptRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineMedicalWorkerRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.nodered.NodeRedClient;
import cn.taihealth.ih.service.cache.HisPricePublicityCache;
import cn.taihealth.ih.service.dto.CreateOfflineOrderDTO;
import cn.taihealth.ih.service.dto.ElectronicMedicCardDTO;
import cn.taihealth.ih.service.dto.OfflineOrderDTO;
import cn.taihealth.ih.service.dto.PrescriptionOrderDTO;
import cn.taihealth.ih.service.dto.hospital.offline.OfflineMedicalWorkerDTO;
import cn.taihealth.ih.service.dto.nodeRed.ConfirmRefund;
import cn.taihealth.ih.service.util.PageUtils;
import cn.taihealth.ih.service.vm.PrescriptionOrderVM;
import cn.taihealth.ih.service.vm.crm.FollowUpParam;
import cn.taihealth.ih.service.vm.nodered.NodeRedResponseData;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.ApplyElectricInvoiceForHuLiReq.ThirdAddAccountItem;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.LockNumberResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.ReturnRegistResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.SyncInspectMRResult;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.service.vm.platform.PayPlatform;
import cn.taihealth.ih.wechat.service.vm.wechat.MedicalInsuranceParam;
import com.alibaba.fastjson.JSON;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.taihealth.ih.domain.enums.ThirdOrderType.NURSING_HOME;

@Slf4j
@Service("hisBusinessService")
public class HisBusinessServiceImpl implements BusinessService {

    private final NodeRedClient nodeRedClient;
    private final DiseaseService diseaseService;
    private final ChargeItemService chargeItemService;
    private final OrderExtraInfoService orderExtraInfoService;
    private final PrescriptionService prescriptionService;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final OfflineDeptRepository offlineDeptRepository;
    private final OfflineMedicalWorkerRepository offlineMedicalWorkerRepository;
    private final DiseaseRepository diseaseRepository;
    private final DeptRepository deptRepository;
    private final OfflineDeptMedicalWorkerRepository offlineDeptMedicalWorkerRepository;
    private final MedicalCaseRepository medicalCaseRepository;
    private final ElectronicMedicCardRepository electronicMedicCardRepository;
    private final HisAdmissionInfoRepository hisAdmissionInfoRepository;
    private final HisElectronicInvoiceRepository hisElectronicInvoiceRepository;
    private final PatientRepository patientRepository;
    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final EvaluateService evaluateService;
    private final OfflineOrderService offlineOrderService;
    private final OfflineOrderRepository offlineOrderRepository;
    private final ExamCategoryRepository examCategoryRepository;
    private final ExamItemRepository examItemRepository;
    private final ExamDeviceRepository examDeviceRepository;

    private final WechatOrderRepository wechatOrderRepository;

    public HisBusinessServiceImpl(NodeRedClient nodeRedClient, DiseaseService diseaseService,
                                  ChargeItemService chargeItemService, OrderExtraInfoService orderExtraInfoService,
                                  PrescriptionService prescriptionService,
                                  MedicalWorkerRepository medicalWorkerRepository,
                                  DeptRepository deptRepository, OfflineDeptRepository offlineDeptRepository,
                                  OfflineMedicalWorkerRepository offlineMedicalWorkerRepository,
                                  DiseaseRepository diseaseRepository,
                                  OfflineDeptMedicalWorkerRepository offlineDeptMedicalWorkerRepository,
                                  MedicalCaseRepository medicalCaseRepository,
                                  DrugStoreService drugStoreService,
                                  ElectronicMedicCardRepository electronicMedicCardRepository,
                                  HisAdmissionInfoRepository hisAdmissionInfoRepository,
                                  HisElectronicInvoiceRepository hisElectronicInvoiceRepository,
                                  PatientRepository patientRepository,
                                  PrescriptionOrderRepository prescriptionOrderRepository,
                                  EvaluateService evaluateService,
                                  OfflineOrderService offlineOrderService,
                                  OfflineOrderRepository offlineOrderRepository,
                                  ExamCategoryRepository examCategoryRepository,
                                  ExamItemRepository examItemRepository,
                                  ExamDeviceRepository examDeviceRepository,
                                  WechatOrderRepository wechatOrderRepository) {
        this.nodeRedClient = nodeRedClient;
        this.diseaseService = diseaseService;
        this.chargeItemService = chargeItemService;
        this.orderExtraInfoService = orderExtraInfoService;
        this.prescriptionService = prescriptionService;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.deptRepository = deptRepository;
        this.offlineDeptRepository = offlineDeptRepository;
        this.offlineMedicalWorkerRepository = offlineMedicalWorkerRepository;
        this.diseaseRepository = diseaseRepository;
        this.offlineDeptMedicalWorkerRepository = offlineDeptMedicalWorkerRepository;
        this.medicalCaseRepository = medicalCaseRepository;
        this.electronicMedicCardRepository = electronicMedicCardRepository;
        this.hisAdmissionInfoRepository = hisAdmissionInfoRepository;
        this.hisElectronicInvoiceRepository = hisElectronicInvoiceRepository;
        this.patientRepository = patientRepository;
        this.prescriptionOrderRepository = prescriptionOrderRepository;
        this.evaluateService = evaluateService;
        this.offlineOrderService = offlineOrderService;
        this.offlineOrderRepository = offlineOrderRepository;
        this.examCategoryRepository = examCategoryRepository;
        this.examItemRepository = examItemRepository;
        this.examDeviceRepository = examDeviceRepository;
        this.wechatOrderRepository = wechatOrderRepository;
    }


    @Override
    public List<ElectronicMedicCardDTO> getElectronicMedicCard(Patient patient, Hospital hospital) {
        List<HisPatientInfo> hisPatientInfos = nodeRedClient.getOutpatientInfoByCertNo(hospital.getCode(),
                                                                                       patient.getName(),
                                                                                       patient.getIdCardNum());
        // 2023年10月20日 17点16分 Norris说这里要过滤Patid为空的数据
        return hisPatientInfos.stream()
                .filter(info -> StringUtils.isNotBlank(info.getPatid()) &&  StringUtils.isNotBlank(info.getCardno()))
                .map(ElectronicMedicCardDTO::new).collect(Collectors.toList());
    }

    @Override
    public String addPatientCards(String hospcode, Patient patient, String cardNo, CardType cardType, String patid) {
        if (StringUtils.isNotBlank(patid)) {
            return patid;
        }
        String sex;
        switch (patient.getGender()) {
            case FEMALE:
                sex = "女";
                break;
            case UNKNOWN:
                sex = "未知";
                break;
            default:
                sex = "男";
        }
        // 出生日期	Y	格式yyyyMMdd
        String birth = DataTypes.DATE.asString(patient.getBirthday(), "yyyyMMdd", Locale.getDefault());
        MedicalCardInfo medicalCard = nodeRedClient.registerMedicalCard(hospcode, patient.getName(),
                                                                        cardType.getCode(), cardNo,
                                                                        patient.getIdCardNum(),
                                                                        patient.getCardType().getCode(),
                                                                        sex, null,
                                                                        null, null, birth, null, patient.getMobile());
        return medicalCard.getPatid();
    }

    @Override
    public void syncDicDiagnose(Hospital hospital) {
        // nodeRedClient.getDicDiagnose 一次最多返回500条数据，使用线程池分批获取进行处理，直到获取完毕
        // 设置线程池大小
        int poolSize = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        // 记录总的获取数据量
        int totalCount = 0;
        // 页码
        int page = 0;
        int size = 500;
        // 处理总数
        int processedCount = 0;
        // 循环获取数据，直到获取完毕
        while (true) {
            List<Disease> diseaseList = new ArrayList<>();
            // 调用接口获取数据
            List<DiagnoseInfo> dicDiagnoseList = nodeRedClient.getDicDiagnose(hospital.getCode(),
                                                                              String.valueOf(++page),
                                                                              String.valueOf(size));
            if (CollectionUtils.isEmpty(dicDiagnoseList)) {
                break;
            }
            // 提交处理任务给线程池
            List<Future<?>> futures = new ArrayList<>();
            for (DiagnoseInfo dicDiagnose : dicDiagnoseList) {
                Future<?> future = executorService.submit(() -> {
                    // 处理每条数据的逻辑
                    String icd10 = dicDiagnose.getIcd10();
                    if (StringUtils.isNotBlank(icd10)) {
                        Disease disease = diseaseRepository.findOneByHospitalAndIcd10Code(hospital, icd10)
                            .orElse(new Disease());
                        disease.setDiseaseName(dicDiagnose.getDiagnose_name());
                        disease.setCode(dicDiagnose.getDiagnose_code());
                        disease.setHospital(hospital);
                        disease.setIcd10Code(dicDiagnose.getIcd10());
                        disease.setDiagFlag(DiagFlag.getDiagFlag(dicDiagnose.getDiag_flag()));
                        // 记录状态	Y	0使用，1停用
                        disease.setEnabled("0".equals(dicDiagnose.getRecord_status()));
                        disease.setUseRange(UseRange.getUseRange(dicDiagnose.getUse_range()));
                        disease.setInsuranceDiagnosisCode(dicDiagnose.getInsr_diagnose_code());
                        disease.setInsuranceDiagnosisName(dicDiagnose.getInsr_diagnose_name());
                        diseaseList.add(disease);
                    }
                });
                futures.add(future);
            }
            // 等待所有任务执行完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                    processedCount++;
                } catch (Exception e) {
                    // 处理异常情况
                    log.error("同步诊断字典出错: {}", e.getMessage());
                }
            }
            diseaseService.saveOrUpdateDiseases(diseaseList);
            totalCount += dicDiagnoseList.size();
            // 获取到的数据小于size，表示已获取完毕，退出循环
            if (dicDiagnoseList.size() < size) {
                break;
            }
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("{}医院同步疾病字典完成，共获取{}条数据, 同步{}条数据", hospital.getCode(), totalCount, processedCount);
    }

    @Override
    public void syncChargeItem(Hospital hospital) {
        // nodeRedClient.getChargeItem 一次最多返回500条数据，使用线程池分批获取进行处理，直到获取完毕
        // 设置线程池大小
        int poolSize = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);

        // 记录总的获取数据量
        int totalCount = 0;

        // 页码
        int page = 0;
        int size = 500;
        // 处理总数
        int processedCount = 0;

        // 循环获取数据，直到获取完毕
        while (true) {
            List<ChargeItem> chargeItems = new ArrayList<>();
            // 调用接口获取数据
            List<ChargeItemInfo> chargeItemInfos = nodeRedClient.getChargeItem(hospital.getCode(),
                                                                               ChannelType.ONLINE.getCode(),
                                                                               QueryScope.OUTPATIENT.getCode(),
                                                                               String.valueOf(++page),
                                                                               String.valueOf(size));
            if (chargeItemInfos != null) {
                // 提交处理任务给线程池
                List<Future<?>> futures = new ArrayList<>();
                for (ChargeItemInfo chargeItemInfo : chargeItemInfos) {
                    Future<?> future = executorService.submit(() -> {
                        // 处理每条数据的逻辑
                        ChargeItem item = chargeItemService.findByHospitalAndItemCode(hospital,
                                                                                      chargeItemInfo.getItem_code())
                            .orElse(new ChargeItem());
                        item.setHospital(hospital);
                        item.setItemCode(chargeItemInfo.getItem_code());
                        item.setItemName(chargeItemInfo.getItem_name());
                        item.setPrice(chargeItemInfo.getPrice());
                        item.setUnit(chargeItemInfo.getUnit());
                        item.setItemSpec(chargeItemInfo.getItem_spec());
                        item.setExpensePrice(chargeItemInfo.getExpense_price());
                        item.setNonExpensePrice(chargeItemInfo.getNon_expense_price());
                        item.setSetItemName(chargeItemInfo.getSet_item_name());
                        item.setQueryScope(QueryScope.OUTPATIENT);
                        chargeItems.add(item);
                    });
                    futures.add(future);
                }

                // 等待所有任务执行完成
                for (Future<?> future : futures) {
                    try {
                        future.get();
                        processedCount++;
                    } catch (Exception e) {
                        // 处理异常情况
                        log.error("同步收费项目字典出错: {}", e.getMessage());
                    }
                }
                chargeItemService.saveOrUpdateChargeItems(chargeItems);
                totalCount += chargeItemInfos.size();
            }
            // 获取到的数据不足500条，表示已获取完毕，退出循环
            if (chargeItemInfos != null && chargeItemInfos.size() < size) {
                break;
            }
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("同步门诊收费项目完成，共获取{}条数据, 同步{}条数据", totalCount, processedCount);
    }

//    @Override
//    public void syncDoctorOffline(Hospital hospital) {
//        // nodeRedClient.getChargeItem 一次最多返回500条数据，使用线程池分批获取进行处理，直到获取完毕
//        // 设置线程池大小
//        int poolSize = 10;
//        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
//
//        // 记录总的获取数据量
//        int totalCount = 0;
//
//        // 页码
//        int page = 0;
//        int size = 500;
//        // 处理总数
//        int processedCount = 0;
//        AtomicInteger onlineUpdateCount = new AtomicInteger();
//
//        //查询该医院所有的医生，以身份证为key，医生为value得到map
//        Map<String, List<MedicalWorker>> identityMap = new HashMap<>();
//        medicalWorkerRepository.findAllByHospital(hospital).forEach(medicalWorker -> {
//            if (StringUtils.isNotBlank(medicalWorker.getIdentity())) {
//                log.info("同步医生，收集复诊医生身份证号：{}", medicalWorker.getIdentity());
//                identityMap
//                    .computeIfAbsent(medicalWorker.getIdentity(), k -> new ArrayList<>())
//                    .add(medicalWorker);
//            }
//        });
//
//        // 循环获取数据，直到获取完毕
//        while (true) {
//            // 调用接口获取数据
//            List<DoctorInfo> doctorList = nodeRedClient.getDoctorList(hospital.getCode(),
//                                                                      String.valueOf(++page),
//                                                                      String.valueOf(size),
//                                                                      null,
//                                                                      ChannelType.WHOLE.getCode());
//            if (doctorList == null) {
//                break;
//            }
//            // 提交处理任务给线程池
//            List<Future<?>> futures = new ArrayList<>();
//            for (DoctorInfo doctorInfo : doctorList) {
//                Future<?> future = executorService.submit(() -> {
//                    Optional<OfflineMedicalWorker> mOptional = offlineMedicalWorkerRepository.findOneByHospitalAndJobNumber(
//                        hospital, doctorInfo.getCode());
//                    // 删除这个医生与科室的关系
//                    mOptional.ifPresent(offlineMedicalWorker -> offlineDeptMedicalWorkerRepository.deleteByMedicalWorkerId(offlineMedicalWorker.getId()));
//                    OfflineMedicalWorker offlineMedicalWorker = mOptional.orElse(new OfflineMedicalWorker());
//                    offlineMedicalWorker.setHospital(hospital);
//                    offlineMedicalWorker.setName(doctorInfo.getName());
//                    offlineMedicalWorker.setJobNumber(doctorInfo.getCode());
//                    if (offlineMedicalWorker.getTitle() == null || StringUtils.isNotBlank(doctorInfo.getTitle())) {
//                        offlineMedicalWorker.setTitle(doctorInfo.getTitle());
//                    }
//                    if (offlineMedicalWorker.getAreasOfExpertise() == null || StringUtils.isNotBlank(doctorInfo.getAreas_of_expertise())) {
//                        offlineMedicalWorker.setAreasOfExpertise(doctorInfo.getAreas_of_expertise());
//                    }
//                    if (offlineMedicalWorker.getIntroduction() == null || StringUtils.isNotBlank(doctorInfo.getIntroduction())) {
//                        offlineMedicalWorker.setIntroduction(doctorInfo.getIntroduction());
//                    }
//                    offlineMedicalWorkerRepository.save(offlineMedicalWorker);
//
//                    OfflineDept dept = offlineDeptRepository.findOneByHospitalAndDeptCode(
//                        hospital, doctorInfo.getDept_id()).orElse(null);
//                    if (dept != null) {
//                        OfflineDeptMedicalWorker offlineDeptMedicalWorker = new OfflineDeptMedicalWorker(offlineMedicalWorker, dept);
//                        offlineDeptMedicalWorkerRepository.save(offlineDeptMedicalWorker);
////                        OfflineDeptMedicalWorker offlineDeptMedicalWorker = offlineDeptMedicalWorkerRepository.findOneByDeptAndMedicalWorker(
////                            dept, offlineMedicalWorker).orElse(new OfflineDeptMedicalWorker(offlineMedicalWorker, dept));
////                        if (offlineDeptMedicalWorker.isNew()) {
////                            offlineDeptMedicalWorkerRepository.save(offlineDeptMedicalWorker);
////                        }
//                    }
//                    // 收集需要更新hisId的线上医生，使用身份证号匹配，匹配到的更新hisId
//                    String identity = doctorInfo.getIdentity();
//                    if (StringUtils.isNotBlank(identity)) {
//                        List<MedicalWorker> medicalWorkers = identityMap.get(identity);
//                        if (CollectionUtils.isNotEmpty(medicalWorkers)) {
//                            medicalWorkers.forEach(m -> {
//                                m.setHisId(doctorInfo.getCode());
//                                medicalWorkerRepository.save(m);
//                                onlineUpdateCount.incrementAndGet();
//                            });
//                        }
//                    }
//                });
//                futures.add(future);
//            }
//
//            // 等待所有任务执行完成
//            for (Future<?> future : futures) {
//                try {
//                    future.get();
//                    processedCount++;
//                } catch (Exception e) {
//                    // 处理异常情况
//                    log.error("同步医生字典出错: {}", e.getMessage());
//                }
//            }
//            totalCount += doctorList.size();
//
//            // 获取到的数据小于size，表示已获取完毕，退出循环
//            if (doctorList.size() < size) {
//                break;
//            }
//        }
//        // 关闭线程池
//        executorService.shutdown();
//        log.info("同步医生字典信息完成，共获取{}条数据, 同步{}条数据 {}条线上数据", totalCount, processedCount, onlineUpdateCount.get());
//    }
    @Override
    public void syncDoctorOffline(Hospital hospital) {
        // nodeRedClient.getChargeItem 一次最多返回500条数据，分批获取进行处理，直到获取完毕
        // 记录总的获取数据量
        int totalCount = 0;

        // 页码
        int page = 0;
        int size = 500;
        // 处理总数
        int processedCount = 0;
        AtomicInteger onlineUpdateCount = new AtomicInteger();

        // 查询该医院所有的医生，以身份证为key，医生为value得到map
        Map<String, List<MedicalWorker>> identityMap = new HashMap<>();
            medicalWorkerRepository.findAllByHospital(hospital).forEach(medicalWorker -> {
            if (StringUtils.isNotBlank(medicalWorker.getIdentity())) {
                identityMap
                    .computeIfAbsent(medicalWorker.getIdentity(), k -> new ArrayList<>())
                    .add(medicalWorker);
            }
        });
        Set<String> deptCodes = new HashSet<>();

        // 循环获取数据，直到获取完毕
            while (true) {
            // 调用接口获取数据
            List<DoctorInfo> doctorList = nodeRedClient.getDoctorList(hospital.getCode(),
                                                                      String.valueOf(++page),
                                                                      String.valueOf(size),
                                                                      null,
                                                                      ChannelType.WHOLE.getCode());
            if (doctorList == null) {
                break;
            }
            // 逐个处理doctorInfo
            for (DoctorInfo doctorInfo : doctorList) {
                Optional<OfflineMedicalWorker> mOptional = offlineMedicalWorkerRepository.findOneByHospitalAndJobNumber(
                    hospital, doctorInfo.getCode());
                // 删除这个医生与科室的关系
                mOptional.ifPresent(offlineMedicalWorker -> {
                    List<OfflineDeptMedicalWorker> allByMedicalWorker = offlineDeptMedicalWorkerRepository.findAllByMedicalWorker(
                        offlineMedicalWorker);
                    if (CollectionUtils.isNotEmpty(allByMedicalWorker) && !deptCodes.contains(doctorInfo.getCode())) {
                        offlineDeptMedicalWorkerRepository.deleteAll(allByMedicalWorker);

                    }
                });
                deptCodes.add(doctorInfo.getCode());
                OfflineMedicalWorker offlineMedicalWorker = mOptional.orElse(new OfflineMedicalWorker());
                offlineMedicalWorker.setHospital(hospital);
                offlineMedicalWorker.setName(doctorInfo.getName());
                offlineMedicalWorker.setJobNumber(doctorInfo.getCode());
                if (offlineMedicalWorker.getTitle() == null || StringUtils.isNotBlank(doctorInfo.getTitle())) {
                    offlineMedicalWorker.setTitle(doctorInfo.getTitle());
                }
                if (offlineMedicalWorker.getAreasOfExpertise() == null || StringUtils.isNotBlank(doctorInfo.getAreas_of_expertise())) {
                    offlineMedicalWorker.setAreasOfExpertise(doctorInfo.getAreas_of_expertise());
                }
                if (offlineMedicalWorker.getIntroduction() == null || StringUtils.isNotBlank(doctorInfo.getIntroduction())) {
                    offlineMedicalWorker.setIntroduction(doctorInfo.getIntroduction());
                }
                offlineMedicalWorkerRepository.save(offlineMedicalWorker);

                OfflineDept dept = offlineDeptRepository.findOneByHospitalAndDeptCode(
                    hospital, doctorInfo.getDept_id()).orElse(null);
                if (dept != null) {
                    Optional<OfflineDeptMedicalWorker> offlineDeptMedicalWorkerOptional = offlineDeptMedicalWorkerRepository.findOneByDeptAndMedicalWorker(
                        dept, offlineMedicalWorker);
                    if (offlineDeptMedicalWorkerOptional.isEmpty()) {
                        OfflineDeptMedicalWorker offlineDeptMedicalWorker = new OfflineDeptMedicalWorker(offlineMedicalWorker, dept);
                        offlineDeptMedicalWorkerRepository.save(offlineDeptMedicalWorker);
                    }
                }

                // 收集需要更新hisId的线上医生，使用身份证号匹配，匹配到的更新hisId
                String identity = doctorInfo.getIdentity();
                if (StringUtils.isNotBlank(identity)) {
                    List<MedicalWorker> medicalWorkers = identityMap.get(identity);
                    if (CollectionUtils.isNotEmpty(medicalWorkers)) {
                        medicalWorkers.forEach(m -> {
                            m.setHisId(doctorInfo.getCode());
                            medicalWorkerRepository.save(m);
                            onlineUpdateCount.incrementAndGet();
                        });
                    }
                }
                processedCount++;
            }

            totalCount += doctorList.size();

            // 获取到的数据小于size，表示已获取完毕，退出循环
            if (doctorList.size() < size) {
                break;
            }
        }
        log.info("同步医生字典信息完成，共获取{}条数据, 同步{}条数据 {}条线上数据", totalCount, processedCount, onlineUpdateCount.get());
    }
    @Override
    public void syncDept(Hospital hospital) {
        // nodeRedClient.getChargeItem 一次最多返回500条数据，使用线程池分批获取进行处理，直到获取完毕
        // 设置线程池大小
        int poolSize = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);

        // 页码
        int page = 0;
        int size = 500;
        // 记录总的获取数据量
        int totalCount = 0;
        // 处理总数
        int processedCount = 0;

        // 循环获取数据，直到获取完毕
        while (true) {
            List<OfflineDept> offlineDeptList = new ArrayList<>();
            List<Dept> deptList = new ArrayList<>();
            // 调用接口获取数据
            List<DeptInfo> deptInfos = nodeRedClient.getDeptList(hospital.getCode(),
                                                                      String.valueOf(++page),
                                                                      String.valueOf(size),
                                                                      ChannelType.WHOLE.getCode());
            if (deptInfos == null) {
                break;
            }

            // 通过deptInfo的channel_type区分出线上和线下科室
            List<DeptInfo> deptInfosNeedSync = deptInfos.stream().filter(u -> u.getChannel_type() != null)
                .collect(Collectors.toList());
            Set<DeptInfo> deptInfosOffline = deptInfosNeedSync.stream()
                .filter(u -> u.getChannel_type().equals(ChannelType.OFFLINE.getCode()))
                .collect(Collectors.toSet());
            Set<DeptInfo> deptInfosOnline= deptInfosNeedSync.stream()
                .filter(u -> u.getChannel_type().equals(ChannelType.ONLINE.getCode()))
                .collect(Collectors.toSet());
            Set<DeptInfo> deptInfosWhole= deptInfosNeedSync.stream()
                .filter(u -> u.getChannel_type().equals(ChannelType.WHOLE.getCode()))
                .collect(Collectors.toSet());
            deptInfosOffline.addAll(deptInfosWhole);
            deptInfosOnline.addAll(deptInfosWhole);

            // 提交处理任务给线程池
            for (DeptInfo deptInfo : deptInfosOffline) {
                // 处理每条数据的逻辑
                Optional<OfflineDept> mOptional = offlineDeptRepository.findOneByHospitalAndDeptCode(
                    hospital, deptInfo.getDept_id());
                OfflineDept dept;
                if (mOptional.isEmpty()) {
                    dept = new OfflineDept();
                    dept.setEnabled(true);
                } else {
                    dept = mOptional.get();
                }
                if (OfflineDept.OfflineDeptType.UNKNOWN == dept.getDeptType()) {
                    //his没有返回，默认设置为门诊
                    dept.setDeptType(OfflineDept.OfflineDeptType.OUT);
                }
                dept.setHospital(hospital);
                dept.setDeptCode(deptInfo.getDept_id());
                dept.setDeptName(deptInfo.getDept_name());
                dept.setDeptTypeCode(deptInfo.getDept_type());
                if (StringUtils.isNotBlank(deptInfo.getDept_introduction())) {
                    dept.setIntroduction(deptInfo.getDept_introduction());
                }
                offlineDeptList.add(dept);
            }
            offlineDeptRepository.saveAll(offlineDeptList);

            List<DeptInfo> deptInfos1 = deptInfosOnline.stream()
                    .filter(u -> Objects.equals(u.getFirst_dept_id(), u.getDept_id()))
                    .collect(Collectors.toList());
            List<DeptInfo> deptInfosw = deptInfosOnline.stream()
                    .filter(u -> !Objects.equals(u.getFirst_dept_id(), u.getDept_id()))
                    .collect(Collectors.toList());
            List<Dept> oldDept = deptRepository.findByHospital(hospital);

            List<Dept> depts1 = deptInfos1.stream().map(u -> {
                // 处理每条数据的逻辑
                Dept dept = oldDept.stream().filter(v -> v.getDeptCode().equals(u.getDept_id())).findFirst().orElse(null);
                return u.toDept(hospital, dept);
            }).collect(Collectors.toList());
            deptRepository.saveAll(depts1);

            List<Dept> depts2 = deptInfosw.stream().map(u -> {
                // 处理每条数据的逻辑
                Dept dept = oldDept.stream().filter(v -> v.getDeptCode().equals(u.getDept_id())).findFirst().orElse(null);
                Dept d = u.toDept(hospital, dept);
                depts1.stream().filter(v -> v.getDeptCode().equals(u.getFirst_dept_id())).findFirst()
                        .ifPresent(v -> d.setParentDeptId(v.getId()));
                return d;
            }).collect(Collectors.toList());
            deptRepository.saveAll(depts2);
            totalCount += deptInfos.size();

            // 获取到的数据小于size，表示已获取完毕，退出循环
            if (deptInfos.size() < size) {
                break;
            }
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("同步科室字典信息完成，共获取{}条数据, 同步{}条数据", totalCount, processedCount);
    }

    @Override
    public void syncCheckCategory(Hospital hospital) {
        // 设置线程池大小
        int poolSize = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        // 页码
        int page = 0;
        int size = 500;
        // 记录总的获取数据量
        int totalCount = 0;
        // 处理总数
        int processedCount = 0;
        // 循环获取数据，直到获取完毕
        while (true) {
            List<ExamCategory> examCategoryList = new ArrayList<>();
            // 调用接口获取数据
            List<CheckCategory> checkCategoryList = nodeRedClient.getCheckCategoryList(hospital.getCode(),
                    String.valueOf(++page), String.valueOf(size));
            if (checkCategoryList == null) {
                break;
            }
            // 提交处理任务给线程池
            List<Future<?>> futures = new ArrayList<>();
            for (CheckCategory checkCategory : checkCategoryList) {
                Future<?> future = executorService.submit(() -> {
                    // 处理每条数据的逻辑
                    Optional<ExamCategory> examCategoryOptional = examCategoryRepository
                            .findFirstByHospitalAndCode(hospital, checkCategory.getCategory_code());
                    // 如果类别代码一样覆盖
                    ExamCategory examCategory;
                    if (examCategoryOptional.isEmpty()) {
                        examCategory = new ExamCategory();
                        examCategory.setCode(checkCategory.getCategory_code());
                        examCategory.setHospital(hospital);
                    } else {
                        examCategory = examCategoryOptional.get();
                    }
                    examCategory.setName(checkCategory.getCategory_name());
                    examCategoryList.add(examCategory);
                });
                futures.add(future);
            }
            // 等待所有任务执行完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                    processedCount++;
                } catch (Exception e) {
                    // 处理异常情况
                    log.error("同步检查类别字典出错: {}", e.getMessage());
                }
            }
            examCategoryRepository.saveAll(examCategoryList);
            totalCount += checkCategoryList.size();
            // 获取到的数据小于size，表示已获取完毕，退出循环
            if (checkCategoryList.size() < size) {
                break;
            }
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("同步检查类别字典信息完成，共获取{}条数据, 同步{}条数据", totalCount, processedCount);
    }

    @Override
    public void syncCheckItem(Hospital hospital) {
        // 设置线程池大小
        int poolSize = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        // 页码
        int page = 0;
        int size = 500;
        // 记录总的获取数据量
        int totalCount = 0;
        // 处理总数
        int processedCount = 0;
        // 循环获取数据，直到获取完毕
        while (true) {
            List<ExamItem> examItemList = new ArrayList<>();
            // 调用接口获取数据
            List<CheckItemInfo> checkItemInfoList = nodeRedClient.getCheckItemList(hospital.getCode(),
                    String.valueOf(++page), String.valueOf(size), "");
            if (checkItemInfoList == null) {
                break;
            }
            // 提交处理任务给线程池
            List<Future<?>> futures = new ArrayList<>();
            for (CheckItemInfo checkItemInfo : checkItemInfoList) {
                Future<?> future = executorService.submit(() -> {
                    // 处理每条数据的逻辑
                    Optional<OfflineDept> offlineDeptOptional = offlineDeptRepository
                            .findOneByHospitalAndDeptCode(hospital, checkItemInfo.getDept_id());
                    if (offlineDeptOptional.isEmpty()) {
                        throw ErrorType.NOT_FOUND_ERROR.toProblem("检查项目关联科室:{}不存在，请先同步科室信息", checkItemInfo.getDept_name());
                    }
                    Optional<ExamCategory> examCategoryOptional = examCategoryRepository
                            .findFirstByHospitalAndCode(hospital, checkItemInfo.getCategory_code());
                    if (examCategoryOptional.isEmpty()) {
                        throw ErrorType.NOT_FOUND_ERROR.toProblem("检查项目关联检查类别:{}不存在", checkItemInfo.getCategory_code());
                    }
                    ExamCategory examCategory = examCategoryOptional.get();
                    Optional<ExamItem> examItemOptional = examItemRepository
                            .findOneByHospitalAndCategoryAndCode(hospital, examCategory, checkItemInfo.getItem_code());
                    ExamItem examItem;
                    if (examItemOptional.isEmpty()) {
                        examItem = new ExamItem();
                        examItem.setCode(checkItemInfo.getItem_code());
                        examItem.setHospital(hospital);
                    } else {
                        examItem = examItemOptional.get();
                    }
                    examItem.setName(checkItemInfo.getItem_name());
                    examItem.setOfflineDept(offlineDeptOptional.get());
                    examItem.setCategory(examCategory);
                    examItem.setPrice(Integer.parseInt(checkItemInfo.getPrice()));
                    examItem.setInsurance(StringUtils.equals(checkItemInfo.getInsurance(), "1"));
                    examItem.setNotice(checkItemInfo.getNotes());
                    //查询检查项目的报告出具时限
                    String code = examItem.getCode();
                    if (StringUtils.isBlank(examItem.getTimeLimit())) {
                        String timeLimit = "";
                        try {
                            timeLimit = nodeRedClient.getReportTimeLimit(hospital.getCode(), code).getTime_limit();
                        } catch (Exception e){
                            log.error("检查项目编码:" + code + " 查询检查项目的报告出具时限失败", e);
                        }
                        examItem.setTimeLimit(timeLimit);
                    }
                    examItemList.add(examItem);
                });
                futures.add(future);
            }
            // 等待所有任务执行完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                    processedCount++;
                } catch (Exception e) {
                    // 处理异常情况
                    log.error("同步检查项目字典出错: {}", e.getMessage());
                }
            }
            examItemRepository.saveAll(examItemList);
            totalCount += checkItemInfoList.size();
            // 获取到的数据小于size，表示已获取完毕，退出循环
            if (checkItemInfoList.size() < size) {
                break;
            }
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("同步检查项目字典信息完成，共获取{}条数据, 同步{}条数据", totalCount, processedCount);
    }

    @Override
    public void syncCheckDevices(Hospital hospital) {
        // 设置线程池大小
        int poolSize = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        // 页码
        int page = 0;
        int size = 500;
        // 记录总的获取数据量
        int totalCount = 0;
        // 处理总数
        int processedCount = 0;
        // 循环获取数据，直到获取完毕
        while (true) {
            List<ExamDevice> examDeviceList = new ArrayList<>();
            // 调用接口获取数据
            List<CheckDevices> checkDevicesList = nodeRedClient.getCheckDevicesList(hospital.getCode(),
                    String.valueOf(++page), String.valueOf(size));
            if (checkDevicesList == null) {
                break;
            }
            // 提交处理任务给线程池
            List<Future<?>> futures = new ArrayList<>();
            for (CheckDevices checkDevices : checkDevicesList) {
                Future<?> future = executorService.submit(() -> {
                    // 处理每条数据的逻辑
                    Optional<OfflineDept> offlineDeptOptional = offlineDeptRepository
                            .findOneByHospitalAndDeptCode(hospital, checkDevices.getDept_id());
                    if (offlineDeptOptional.isEmpty()) {
                        throw ErrorType.NOT_FOUND_ERROR.toProblem("检查设备关联科室:{}不存在，请先同步科室信息", checkDevices.getDept_name());
                    }
                    Optional<ExamCategory> examCategoryOptional = examCategoryRepository
                            .findFirstByHospitalAndCode(hospital, checkDevices.getCategory_code());
                    if (examCategoryOptional.isEmpty()) {
                        throw ErrorType.NOT_FOUND_ERROR.toProblem("检查项目关联检查类别:{}不存在", checkDevices.getCategory_name());
                    }
                    Optional<ExamDevice> examDeviceOptional = examDeviceRepository
                            .findOneByHospitalAndCode(hospital, checkDevices.getDevice_id());
                    ExamDevice examDevice;
                    if (examDeviceOptional.isEmpty()) {
                        examDevice = new ExamDevice();
                        examDevice.setCode(checkDevices.getDevice_id());
                        examDevice.setHospital(hospital);
                    } else {
                        examDevice = examDeviceOptional.get();
                    }
                    examDevice.setName(checkDevices.getDevice_name());
                    examDevice.setOfflineDept(offlineDeptOptional.get());
                    examDevice.setCategory(examCategoryOptional.get());
                    examDevice.setAddress(checkDevices.getDevice_site());
                    examDeviceList.add(examDevice);
                });
                futures.add(future);
            }
            // 等待所有任务执行完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                    processedCount++;
                } catch (Exception e) {
                    // 处理异常情况
                    log.error("同步检查设备字典出错: {}", e.getMessage());
                }
            }
            examDeviceRepository.saveAll(examDeviceList);
            totalCount += checkDevicesList.size();
            // 获取到的数据小于size，表示已获取完毕，退出循环
            if (checkDevicesList.size() < size) {
                break;
            }
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("同步检查设备字典信息完成，共获取{}条数据, 同步{}条数据", totalCount, processedCount);
    }

    @Override
    public void pushDictChangeMsg(Hospital hospital, String msg_type, String channel_code) {
        CommonResult commonResult = nodeRedClient.pushDictChangeMsg(hospital.getCode(), msg_type, channel_code);
        if (commonResult != null && commonResult.isSuccess()) {
            log.info("推送变更消息：msg_type：{}，channel_code：{}。成功：{}", msg_type, channel_code, commonResult);
        } else {
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("推送变更消息失败");
        }
    }

    @Override
    public ReportTimeLimit getReportTimeLimit(Hospital hospital, String item_code) {
        return nodeRedClient.getReportTimeLimit(hospital.getCode(), item_code);
    }

    @Override
    public List<ApplicationInfo> getApplicationInfoList(String hospitalCode, String pat_name, String apply_no,
                                                        String certificate_no, String card_no, String pat_id, String patient_type,
                                                        String begin_date, String end_date) {
        return nodeRedClient.getApplicationInfoList(hospitalCode, pat_name, apply_no, certificate_no, card_no, pat_id, patient_type,
                begin_date, end_date);

    }

    @Override
    public void cancelCheckApplication(String hospitalCode, String patname, String patid, String application_no,
                                       String application_category, String message) {
        CommonResult commonResult = nodeRedClient.cancelCheckApplication(hospitalCode, patname, patid, application_no,
                application_category, message);
        if (commonResult != null && commonResult.isSuccess()) {
            log.info("取消申请单：patid：{}，application_no：{}。成功：{}", patid, application_no, commonResult);
        } else {
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("取消申请单失败: " + StandardObjectMapper.stringify(commonResult));
        }
    }

    @Override
    public List<CheckReportResult> getCheckReportResult(String hospitalCode, String report_no, String report_type_code) {
        return nodeRedClient.getCheckReportResult(hospitalCode, report_no, report_type_code);
    }

    @Override
    public MedTechWaitingList getMedTechWaiting(String hospitalCode, String patname, String patid, String regist_type) {
        return nodeRedClient.getMedTechWaiting(hospitalCode, patname, patid, regist_type);
    }

    @Override
    public void pushAppointmentChangeMsg(String hospitalCode, String application_id, String appointment_id,
                                         String operation_id, String operation_name,
                                         String operation_time, String channel_code, String status, String message) {
        CommonResult commonResult = nodeRedClient.pushAppointmentChangeMsg(hospitalCode, application_id, appointment_id,
                operation_id, operation_name, operation_time, channel_code, status, message);
        if (commonResult != null && commonResult.isSuccess()) {
            log.info("推送预约信息变更消息：application_id：{}，appointment_id：{}，status：{}。成功：{}",
                    application_id, appointment_id, status, commonResult);
        } else {
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("推送预约信息变更消息失败");
        }
    }

    @Override
    public List<RecipeInfo> getPatientRecipeListRenewable(Hospital hospital, Patient patient, String beginDate,
                                                          String endDate, String insuranceParam) {
        return nodeRedClient.getPatientRecipeListRenewable(hospital.getCode(), patient.getName(),
                                                           patient.getHisPatid(), beginDate, endDate, insuranceParam);
    }

    @Override
    public List<CurrentDayAppointment> getCurrentDayAppointmentList(Hospital hospital, String channel_type) {
        return nodeRedClient.getCurrentDayAppointmentList(hospital.getCode(), channel_type);
    }


    @Override
    public MedicalCardInfo createMedicalCard(Hospital hospital, Patient patient) {
        String sex;
        switch (patient.getGender()) {
            case FEMALE:
                sex = "女";
                break;
            case UNKNOWN:
                sex = "未知";
                break;
            default:
                sex = "男";
        }
        // 出生日期	Y	格式yyyyMMdd
        String birth = DataTypes.DATE.asString(patient.getBirthday(), "yyyyMMdd", Locale.getDefault());
        MedicalCardInfo medicalCard = nodeRedClient.createMedicalCard(hospital.getCode(), patient.getName(),
                                                                      patient.getIdCardNum(),
                                                                      patient.getCardType().getCode(),
                                                                      sex, null, null,
                                                                      null, birth, null, patient.getMobile());
        medicalCard.setOnlineType(ElectronicMedicCard.OnlineType.HIS);
        return medicalCard;
    }

    @Override
    public void updatePatientInfo(Hospital hospital, Patient patient) {
        nodeRedClient.updatePatientInfo(hospital.getCode(), patient.getName(), null, patient.getHisPatid(), null,
                                        null, null, null, null, null, null, null, null, null, null, null, null, null
            , null, null, null, null);
    }

    @Override
    public List<DoctorScheduleInfo> getCurrentDayDoctorList(Hospital hospital, Dept dept) {
        return nodeRedClient.getCurrentDayDoctorList(hospital.getCode(), dept != null? dept.getDeptCode(): null,
                                                     ChannelType.ONLINE.getCode());
    }

    @Override
    public List<DoctorSourceDetail> getCurrentDayDoctorSourceDetail(Hospital hospital, MedicalWorker medicalWorker) {
        return nodeRedClient.getCurrentDayDoctorSourceDetail(hospital.getCode(), medicalWorker.getHisId(),
                                                             ChannelType.ONLINE.getCode());
    }

    @Override
    public SchedulingSourceNumber getCurrentDaySchedulingSourceNumber(Hospital hospital, String scheduleId, String source_number) {
        return nodeRedClient.getCurrentDaySchedulingSourceNumber(hospital.getCode(), scheduleId,
                ChannelType.ONLINE.getCode(), source_number).stream()
                .filter(u -> {
                    Date now = new Date();
                    //Date beginTime = TimeUtils.convert(u.getBegin_time());
                    Date endTime = TimeUtils.convert(u.getEnd_time());
                    return "0".equals(u.getStatus()) && now.before(endTime);
                })
                .findFirst().orElse(null);
    }

    @Override
    public PreRegistrationResult preRegisterOnlineOrder(Order order, OrderExtraInfo orderExtraInfo, PlatformTypeEnum platformType) {
        // 查排班号序 选择最小的状态为0的号序
        Hospital hospital = order.getHospital();
        Patient patient = order.getPatient();
        ElectronicMedicCard electronicMedicCard = order.getElectronicMedicCard();
        SchedulingSourceNumber schedulingSourceNumber = getCurrentDaySchedulingSourceNumber(hospital,
                orderExtraInfo.getHisScheduleId(), orderExtraInfo.getHisSourceNumber());
        // 锁号
        if (schedulingSourceNumber == null) {
            log.error("{}-{}-未查到可用的排班号序", hospital.getCode(), orderExtraInfo.getHisScheduleId());
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("此号源已过期，请重新预约");
        } else {
            orderExtraInfo.setHisSourceNumber(schedulingSourceNumber.getSource_number());
            orderExtraInfo.setHisSourceNumberBeginTime(schedulingSourceNumber.getBegin_time());

            LockNumberResult lockNumberResult = lockNumber(hospital, patient.getHisPatid(), orderExtraInfo);
            if (lockNumberResult == null || !lockNumberResult.isSuccess()) {
                log.error("{}-{}-锁号失败", hospital.getCode(), orderExtraInfo.getHisScheduleId());
                // 本钢虽然不锁号，但是node-red还是返回了success，所以不success或者lockNumberResult为null的情况需要抛出异常
                // 1120，20点，阜新由于没有修复，也不影响使用，暂时注释，等his修复后再打开
                // 1121，18点，阜新需要测试 打开
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("此号已被占用，请重新预约");
            } else {
                orderExtraInfo.setHisLockNumber(lockNumberResult.getLock_number_id());
            }
        }

        // 预算
        PreRegisterReq preRegisterReq = new PreRegisterReq();
        preRegisterReq.setPatid(patient.getHisPatid());
        preRegisterReq.setCardno(electronicMedicCard.getNumber());
        preRegisterReq.setCardtype(electronicMedicCard.getCardType().getCode());
        preRegisterReq.setPatname(patient.getName());
        preRegisterReq.setScheduling_id(orderExtraInfo.getHisScheduleId());
        preRegisterReq.setSource_number(orderExtraInfo.getHisSourceNumber());
        preRegisterReq.setBegin_time(orderExtraInfo.getHisSourceNumberBeginTime());
        preRegisterReq.setRegistration_fee_code(orderExtraInfo.getRegistrationFeeCode());
        preRegisterReq.setTreatment_fee_code(orderExtraInfo.getTreatmentFeeCode());
        preRegisterReq.setChildren_treatment_fee_code(orderExtraInfo.getChildrenTreatmentFeeCode());
        // TODO 这里有些入参后续可能要处理
        preRegisterReq.setHosp_account_flag("0");
        preRegisterReq.setSource_name("挂号");
        preRegisterReq.setChannel_type(ChannelType.ONLINE.getCode());
        preRegisterReq.setPay_type("1");
        if (platformType != null) {
            preRegisterReq.setPay_type(platformType.getValue());
        }
        PreRegistrationResult preRegistrationResult = nodeRedClient.preRegister(hospital.getCode(), preRegisterReq);
        orderExtraInfo.setRegistrationSerialNumber(preRegistrationResult.getRegno());
        orderExtraInfo.setSettleId(preRegistrationResult.getSettle_id());
        orderExtraInfo.setShouldPayAmount(Integer.parseInt(preRegistrationResult.getShould_pay_amount()));
        orderExtraInfo.setTotalAmount(Integer.parseInt(preRegistrationResult.getTotal_amount()));
        return preRegistrationResult;
    }

    @Override
    public PreRegistrationResult preRegisterOfflineOrder(Hospital hospital, OfflineOrder order, String registrationFeeCode,
                                                         String treatmentFeeCode, String childrenTreatmentFeeCode, MedicalInsuranceParam insuranceParam) {
        // 查排班号序 选择最小的状态为0的号序
        ElectronicMedicCard card = order.getElectronicMedicCard();
        Patient patient = card.getPatient();
        // 2024年07月12日12:24:12 不传sourceNo
        SchedulingSourceNumber schedulingSourceNumber = getCurrentDaySchedulingSourceNumber(hospital,
                order.getScheduleId(), order.getSourceNo());
        // 锁号
        if (schedulingSourceNumber == null) {
            log.error("{}-{}-未查到可用的排班号序", hospital.getCode(), order.getScheduleId());
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("此号源已过期，请重新预约");
        } else {
            order.setSourceNo(schedulingSourceNumber.getSource_number());
            if (StringUtils.isNotBlank(schedulingSourceNumber.getBegin_time())) {
                Date time = TimeUtils.convert(schedulingSourceNumber.getBegin_time());
                order.setBeginTime(DataTypes.DATE.asString(time, "yyyyMMddHHmm"));
                boolean canRegister = AppContext.getInstance(OfflineOrderService.class).checkRegistered(hospital,
                        card.getHisPatid(), time, order.getHisDeptId());
                if (!canRegister) {
                    throw ErrorType.REGISTER_COUNT_IS_FULL.toProblem("超过可挂号数量限制，请先退号");
                }
            }
            LockNumberResult lockNumberResult = nodeRedClient.lockNumber(hospital.getCode(), card.getHisPatid(),
                                                                          order.getScheduleId(),
                                                                         order.getSourceNo());
            if (lockNumberResult == null || !lockNumberResult.isSuccess()) {
                // 本钢虽然不锁号，但是node-red还是返回了success，所以不success或者lockNumberResult为null的情况需要抛出异常
                log.error("{}-{}-锁号失败", hospital.getCode(), order.getScheduleId());
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("此号已被占用，请重新预约");
            } else {
                order.setLockNo(lockNumberResult.getLock_number_id());
            }
        }
        return outPatientPreRegister(hospital, order, patient, registrationFeeCode,
                    treatmentFeeCode, childrenTreatmentFeeCode, insuranceParam);
    }

    @Override
    public void outPatientAppointment(Hospital hospital, OfflineOrder order,
                                      String registrationFeeCode, String treatmentFeeCode,
                                      String childrenTreatmentFeeCode) {
        // 查排班号序 选择最小的状态为0的号序
        ElectronicMedicCard card = order.getElectronicMedicCard();
        Patient patient = card.getPatient();
        SchedulingSourceNumber schedulingSourceNumber = getCurrentDaySchedulingSourceNumber(hospital,
                order.getScheduleId(), order.getSourceNo());

        // 锁号
        if (schedulingSourceNumber == null) {
            log.error("{}-{}-未查到可用的排班号序", hospital.getCode(), order.getScheduleId());
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("此号源已过期，请重新预约");
        } else {
            log.info("{}-{}-查到可用的排班号序 {}", hospital.getCode(),order.getScheduleId(),
                     schedulingSourceNumber.getSource_number());
            order.setSourceNo(schedulingSourceNumber.getSource_number());
            if (StringUtils.isNotBlank(schedulingSourceNumber.getBegin_time())) {
                Date time = TimeUtils.convert(schedulingSourceNumber.getBegin_time());
                order.setBeginTime(DataTypes.DATE.asString(time, "yyyyMMddHHmm"));
                boolean canRegister = AppContext.getInstance(OfflineOrderService.class).checkRegistered(hospital,
                        card.getHisPatid(), time, order.getHisDeptId());
                if (!canRegister) {
                    throw ErrorType.REGISTER_COUNT_IS_FULL.toProblem("超过可挂号数量限制，请先退号");
                }
            }

            LockNumberResult lockNumberResult = nodeRedClient.lockNumber(hospital.getCode(), card.getHisPatid(),
                    order.getScheduleId(), order.getSourceNo());
            if (lockNumberResult == null || !lockNumberResult.isSuccess()) {
                // 本钢虽然不锁号，但是node-red还是返回了success，所以不success或者lockNumberResult为null的情况需要抛出异常
                log.error("{}-{}-锁号失败", hospital.getCode(), order.getScheduleId());
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("此号已被占用，请重新预约");
            } else {
                order.setLockNo(lockNumberResult.getLock_number_id());
            }
        }
        // 2025年02月28日 普精卫预约转挂号需要卡号，his无法确定小程序绑定的哪张卡，所以卡号由平台传给his，先放在memo字段
        // 预约
        SaveAppointmentResult saveAppointmentResult = nodeRedClient.saveAppointment(hospital.getCode(),
                    card.getHisPatid(), order.getScheduleId(), order.getSourceNo(),
                    "", "", patient.getIdCardNum(), card.getNumber());
        if ("true".equals(saveAppointmentResult.getSuccess())) {
            order.setType(ProjectTypeEnum.OUTPATIENT_APPOINTMENT_REGISTRATION);
            order.setAppointmentId(saveAppointmentResult.getAppointment_id());
            order.setSettleId(saveAppointmentResult.getAppointment_id());
            order.setRegistrationFee(Integer.parseInt(saveAppointmentResult.getShould_pay_amount()));
            order.setSelfAmount(Integer.parseInt(saveAppointmentResult.getShould_pay_amount()));
        } else {
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(saveAppointmentResult.getMessage());
        }
        log.info("预约日期{}", order.getDutyDate());
    }

    @Override
    public PreRegistrationResult outPatientPreRegister(Hospital hospital, OfflineOrder order, Patient patient,
                                                       String registrationFeeCode, String treatmentFeeCode,
                                                       String childrenTreatmentFeeCode, MedicalInsuranceParam insuranceParam) {

        ElectronicMedicCard electronicMedicCard = order.getElectronicMedicCard();
        PreRegisterReq preRegisterReq = new PreRegisterReq();
        preRegisterReq.setPatid(electronicMedicCard.getHisPatid());
        preRegisterReq.setCardno(electronicMedicCard.getNumber());
        preRegisterReq.setCardtype(electronicMedicCard.getCardType().getCode());
        preRegisterReq.setPatname(patient.getName());
        preRegisterReq.setScheduling_id(order.getScheduleId());
        if (StringUtils.isNotBlank(order.getAppointmentId())) {
            preRegisterReq.setAppointment_id(order.getAppointmentId());
        }
        preRegisterReq.setSource_number(order.getSourceNo());
        preRegisterReq.setBegin_time(order.getBeginTime());
        preRegisterReq.setRegistration_fee_code(registrationFeeCode);
        preRegisterReq.setTreatment_fee_code(treatmentFeeCode);
        preRegisterReq.setChildren_treatment_fee_code(childrenTreatmentFeeCode);
        // TODO 这里有些入参后续可能要处理
        preRegisterReq.setHosp_account_flag("0");
        // 0:医保代码结算 1:自费结算
//        CardType cardType = order.getElectronicMedicCard().getCardType();
//        String selfFlag = cardType == null || cardType == CardType.SELF_PAY ? "1" : "0";

        String hisInsuranceParamStr = insuranceParam == null ? null : insuranceParam.toHisParam();
        if (hisInsuranceParamStr != null) {
            preRegisterReq.setInsurance_param(insuranceParam.toHisParam());
            preRegisterReq.setSelf_flag("0");
            order.setSelfFlag(0);
        }
        preRegisterReq.setPay_type("1");
        if (hisInsuranceParamStr != null && insuranceParam.getPlatformType() != null) {
            preRegisterReq.setPay_type(insuranceParam.getPlatformType().getValue());
        }
        preRegisterReq.setSource_name("预约挂号");
        preRegisterReq.setChannel_type(ChannelType.OFFLINE.getCode());
        PreRegistrationResult preRegistrationResult = nodeRedClient.preRegister(hospital.getCode(), preRegisterReq);
        if (StringUtils.isNotBlank(preRegistrationResult.getSettle_id())) {
            order.setRegNo(preRegistrationResult.getRegno());
            order.setSettleId(preRegistrationResult.getSettle_id());
            order.setRegistrationFee(Integer.parseInt(preRegistrationResult.getTotal_amount()));
            order.setSelfAmount(Integer.parseInt(preRegistrationResult.getSelf_pay()));
            order.setPubFee(StringUtil.parseIntOrZero(preRegistrationResult.getPub_pay()));
            order.setPubAccountFee(StringUtil.parseIntOrZero(preRegistrationResult.getPub_account_pay()));
            order.setInsuranceFee(order.getPubFee() + order.getPubAccountFee());
            order.setHisSerialNo(preRegistrationResult.getMedical_order_no());
            order.setInternetPayOrderId(preRegistrationResult.getPay_order_id());
            order.setGmtOutCreate(TimeUtils.convert(preRegistrationResult.getGmt_out_create()));
            order.setPayAuthNo(hisInsuranceParamStr);
            order.setInsuranceResult(preRegistrationResult.getInsurance_param());
            order.setShBillNo(preRegistrationResult.getBill_no());
        }
        return preRegistrationResult;
    }

    @Override
    public HisGeneralResult cancelPreRegist(Hospital hospital, Order order) {
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        Patient patient = order.getPatient();
        if (orderExtraInfo == null) {
            return null;
        }
        return nodeRedClient.cancelPreRegist(hospital.getCode(), orderExtraInfo.getRegistrationSerialNumber(),
                                             orderExtraInfo.getSettleId(), patient.getHisPatid(), null);
    }

    @Override
    public HisGeneralResult cancelPreRegist(Hospital hospital, Patient patient, String regNo, String settleId) {
        return nodeRedClient.cancelPreRegist(hospital.getCode(), regNo, settleId, patient.getHisPatid(), null);
    }

    @Override
    public NodeRedResponseData<ConfirmRegistResult> confirmRegist(Hospital hospital, Order order, HisPayParam hisPayParam) {
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
        // private String patid;                   // 门诊 patid   Y  门诊患者唯一号
        //    private String regno;                   // 挂号序号   Y  本次挂号的HIS唯一码，门诊就诊流水号。
        //    private String settle_id;               // HIS结算单号 Y  预算时HIS反馈的HIS结算收据号
        //    private String serial_no;               // 平台流水号 N  第三方平台流水号，标记唯一一次业务请求，(挂号预算HIS返回收据号为空时，此参数必填)
        //    private String appointment_id;          // 预约序号 N  如果已经预约了，则必须传预约序号
        //    private String lock_number_id;          // 锁号序号 N  由《4.3.3.查询指定排班的号序信息》返回， 当班挂号未预约时传入。如已经预约则此字段不传，预约序号必填。
        //    private String scheduling_id;           // 排班明细序号 N  和预算时一致
        //    private String source_number;           // 排班号序   N  和预算时一致
        //    private String total_amount;            // 总金额    Y  预算时返回
        //    private String should_pay_amount;       // 应付金额   Y  预算时返回
        //    private String pay_type;                   // 支付方式   Y  0 现金 1 微信支付 2支付宝支付
        //    private String pay_amount;              // 支付金额   Y  支付金额
        //    private String trade_no;                // 支付流水号 N  应为支付的唯一流水号，用于对账。支付方式不为0时，则必填。
        //    private String receipt_account;         // 账户标识 N  医院收款账户标识，默认为空，医院使用多个支付账户情况下非空，此时HIS账单接口原样传出，以便于多账户对账
        //    private String hosp_account_flag;          // 是否扣院内账户 N  与预算保持一致
        //    private String self_flag;                  // 是否自费结算 N  与预算保持一致，0根据病人医保代码结算1自费结算
        //    private String port;                    // 支付渠道   Y  特别约定时必填
        //    private String pay_card_no;             // 代币卡序号  N  多张卡支付时以|分隔
        //    private String pay_card_amount;         // 代币卡支付金额 N  多张卡支付时以|分隔
        //    private String pay_card_flag;              // 代币卡使用标志 N  使用代币卡支付时传入1，默认为空
        //    private String insurance_param;         // 医保交易入参 N  占位，具体参数格式内容需要根据当地医保确定
        //    private String commercial_insurance_param; // 商保结算信息 N  存在商保结算时需传入，具体以MAP JSON格式字符串传入
        //    private String extra_content;           // 扩展信息   N  个性化化字段扩展信息，在医院项目里单独处理
        ConfirmRegistReq req = new ConfirmRegistReq();
        Patient patient = order.getPatient();
        req.setPatid(patient.getHisPatid());
        req.setRegno(orderExtraInfo.getRegistrationSerialNumber());
        req.setSettle_id(orderExtraInfo.getSettleId());
        req.setSerial_no(order.getId().toString());
//        req.setAppointment_id(orderExtraInfo.getAppointmentId());
        req.setLock_number_id(orderExtraInfo.getHisLockNumber());
        req.setScheduling_id(orderExtraInfo.getHisScheduleId());
        req.setSource_number(orderExtraInfo.getHisSourceNumber());
        req.setTotal_amount(String.valueOf(order.getRegistrationFee()));
        req.setShould_pay_amount(String.valueOf(order.getRegistrationFee()));
        req.setPay_type("1");
        if (hisPayParam != null) {
            req.setPay_type(hisPayParam.getPayType());
            req.setTrade_no(hisPayParam.getTransactionId());
        }
        req.setPay_amount(String.valueOf(order.getRegistrationFee()));
        req.setReceipt_account(null);
        req.setHosp_account_flag("0");
        req.setSelf_flag("1");
        req.setPort(null);
        req.setPay_card_no(null);
        req.setPay_card_amount(null);
        req.setPay_card_flag(null);
        req.setInsurance_param(null);
        req.setCommercial_insurance_param(null);
        req.setExtra_content(null);
        req.setChannel_type("1");
        req.setPay_time(TimeUtils.dateToString(order.getRegisteredDate(), "yyyyMMddHHmmss"));

        return nodeRedClient.confirmRegist(hospital.getCode(), req);
    }

    @Override
    public NodeRedResponseData<ConfirmRegistResult> confirmRegist(Hospital hospital, OfflineOrder order, HisPayParam hisPayParam) {
        // private String patid;                   // 门诊 patid   Y  门诊患者唯一号
        //    private String regno;                   // 挂号序号   Y  本次挂号的HIS唯一码，门诊就诊流水号。
        //    private String settle_id;               // HIS结算单号 Y  预算时HIS反馈的HIS结算收据号
        //    private String serial_no;               // 平台流水号 N  第三方平台流水号，标记唯一一次业务请求，(挂号预算HIS返回收据号为空时，此参数必填)
        //    private String appointment_id;          // 预约序号 N  如果已经预约了，则必须传预约序号
        //    private String lock_number_id;          // 锁号序号 N  由《4.3.3.查询指定排班的号序信息》返回， 当班挂号未预约时传入。如已经预约则此字段不传，预约序号必填。
        //    private String scheduling_id;           // 排班明细序号 N  和预算时一致
        //    private String source_number;           // 排班号序   N  和预算时一致
        //    private String total_amount;            // 总金额    Y  预算时返回
        //    private String should_pay_amount;       // 应付金额   Y  预算时返回
        //    private String pay_type;                   // 支付方式   Y  0 现金 1 微信支付 2支付宝支付
        //    private String pay_amount;              // 支付金额   Y  支付金额
        //    private String trade_no;                // 支付流水号 N  应为支付的唯一流水号，用于对账。支付方式不为0时，则必填。
        //    private String receipt_account;         // 账户标识 N  医院收款账户标识，默认为空，医院使用多个支付账户情况下非空，此时HIS账单接口原样传出，以便于多账户对账
        //    private String hosp_account_flag;          // 是否扣院内账户 N  与预算保持一致
        //    private String self_flag;                  // 是否自费结算 N  与预算保持一致，0根据病人医保代码结算1自费结算
        //    private String port;                    // 支付渠道   Y  特别约定时必填
        //    private String pay_card_no;             // 代币卡序号  N  多张卡支付时以|分隔
        //    private String pay_card_amount;         // 代币卡支付金额 N  多张卡支付时以|分隔
        //    private String pay_card_flag;              // 代币卡使用标志 N  使用代币卡支付时传入1，默认为空
        //    private String insurance_param;         // 医保交易入参 N  占位，具体参数格式内容需要根据当地医保确定
        //    private String commercial_insurance_param; // 商保结算信息 N  存在商保结算时需传入，具体以MAP JSON格式字符串传入
        //    private String extra_content;           // 扩展信息   N  个性化化字段扩展信息，在医院项目里单独处理
        ConfirmRegistReq req = new ConfirmRegistReq();
        ElectronicMedicCard card = order.getElectronicMedicCard();
        req.setPatid(card.getHisPatid());
        req.setRegno(order.getRegNo());
        req.setSettle_id(order.getSettleId());
        req.setSerial_no(order.getId().toString());
        req.setAppointment_id(order.getAppointmentId());
        req.setLock_number_id(order.getLockNo());
        req.setScheduling_id(order.getScheduleId());
        req.setSource_number(order.getSourceNo());
        req.setTotal_amount(String.valueOf(order.getRegistrationFee()));
        req.setShould_pay_amount(String.valueOf(order.getSelfAmount()));
        req.setPay_type(hisPayParam.getPayType());
        req.setPay_amount(String.valueOf(order.getRegistrationFee()));
        req.setTrade_no(hisPayParam.getTransactionId());
        req.setReceipt_account(null);
        req.setHosp_account_flag("0");
        String selfFlag = order.getPayAuthNo() == null  ? "1" : "0";
        req.setSelf_flag(selfFlag);
        req.setPort(null);
        req.setPay_card_no(null);
        req.setPay_card_amount(null);
        req.setPay_card_flag(null);
        req.setInsurance_param(order.getPayAuthNo());
        req.setCommercial_insurance_param(null);
        req.setExtra_content(null);
        req.setChannel_type("0");
        req.setPay_time(TimeUtils.dateToString(hisPayParam.getPayTime(), "yyyyMMddHHmmss"));

        return nodeRedClient.confirmRegist(hospital.getCode(), req);
    }

    @Override
    public NodeRedResponseData<AppointmentSignInResult> appointmentSignIn(String hospcode, AppointmentSignInReq signInReq) {
        return nodeRedClient.appointmentSignIn(hospcode, signInReq);
    }

    @Override
    public ReturnRegistResult returnRegist(Hospital hospital, Order order, PayPlatform payPlatform) {
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
        Patient patient = order.getPatient();
        String refundTime = DataTypes.DATE.asString(order.getEndedDate(), "yyyyMMddHHmmss");
        return nodeRedClient.returnRegist(hospital.getCode(), patient.getHisPatid(), orderExtraInfo.getRegistrationSerialNumber(),
                orderExtraInfo.getSettleId(), payPlatform.getPlatformType().getValue(), String.valueOf(order.getRegistrationFee()),
                payPlatform.getTransactionId(), payPlatform.getTransactionId(), refundTime, null);
    }

    @Override
    public ReturnRegistResult returnRegist(Hospital hospital, OfflineOrder order, String transactionId, MedicalInsuranceParam insuranceParam) {
        Patient patient = patientRepository.getById(Long.valueOf(order.getPatientId()));
        String refundTime = DataTypes.DATE.asString(new Date(), "yyyyMMddHHmmss");
//        if (order.getSelfFlag() == 0 && insuranceParam == null) {
//            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("医保退款调用his退款时，不需要医保，offlineOrderId: " + order.getId());
//        }

        String insuranceParamStr = insuranceParam == null ? null : insuranceParam.toHisParam();
        String payType = insuranceParam == null || insuranceParam.getPlatformType() == null ? "1" : insuranceParam.getPlatformType().getValue();

        return nodeRedClient.returnRegist(hospital.getCode(), patient.getHisPatid(), order.getRegNo(), order.getSettleId(),
                payType, String.valueOf(order.getRegistrationFee()), transactionId, transactionId,
                refundTime, insuranceParamStr);
    }

    @Override
    public LockNumberResult lockNumber(Hospital hospital, String hisPid, OrderExtraInfo orderExtraInfo) {
        return nodeRedClient.lockNumber(hospital.getCode(), hisPid, orderExtraInfo.getHisScheduleId(),
                                        orderExtraInfo.getHisSourceNumber());
    }

    @Override
    public HisGeneralResult unlock(Hospital hospital, Order order) {
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        Patient patient = order.getPatient();
        if (orderExtraInfo == null) {
            return null;
        }
        return nodeRedClient.unlock(hospital.getCode(), patient.getHisPatid(), orderExtraInfo.getHisLockNumber());
    }

    @Override
    public SaveRecipeResult saveRecipeOnline(Hospital hospital, PrescriptionOrder prescriptionOrder) {
        SaveOnlineRecipeRequest recipeInfo = new SaveOnlineRecipeRequest();
        //    private String patid;               // 患者唯一标识 (必须)
        //    private String channel_type;       // 就诊渠道 (可选)
        //    private String regno;               // 挂号序号 (必须)
        //    private String recipe_type;        // 处方类型 (必须)
        //    private String recipe_time;         // 开方时间 (必须)
        //    private String recipe_dept_id;      // 开方科室代码 (必须)
        //    private String recipe_dept_name;    // 开方科室名称 (必须)
        //    private String recipe_doctor_id;    // 开方医生代码 (必须)
        //    private String recipe_doctor_name;  // 开方医生名称 (必须)
        //    private String check_status;       // 审核状态 (必须)
        //    private String recipe_name;         // 处方名称 (可选)
        //    private String entrust_content;     // 嘱托 (可选)
        //    private String detail_entrust_content; // 明细嘱托 (可选)
        //    private String exec_dept_id;        // 执行科室代码 (可选)
        //    private String exec_dept_name;      // 执行科室名称 (可选)
        //    private String exec_address;        // 执行地址 (可选)
        //    private String extra_content;       // 扩展信息 (可选)
        //    private String insur_flag;         // 医保标识 (可选)
        //    private String verify_flag;        // 规则校验标志 (必须)
        //    private String oneself_flag;       // 自备标识 (必须)
        //    private String delivery_type;      // 配送类型 (可选)
        //    private List<DiagnoseInfoReq> diagnose_list; // 诊断信息 (必须)
        //    private List<ItemInfoReq> item_infos;       // 药品/项目数据 (必须)
        recipeInfo.setPatid(prescriptionOrder.getPatient().getHisPatid());
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(prescriptionOrder.getOrder().getId());
        recipeInfo.setRegno(orderExtraInfo.getRegistrationSerialNumber());
        recipeInfo.setChannel_type("1");
        recipeInfo.setRecipe_type(prescriptionOrder.getType().getCode());
        recipeInfo.setRecipe_time(TimeUtils.dateToString(prescriptionOrder.getCommitedDate(), "yyyyMMddHHmmss"));
        recipeInfo.setRecipe_dept_id(prescriptionOrder.getOrder().getDept().getDeptCode());
        recipeInfo.setRecipe_dept_name(prescriptionOrder.getOrder().getDept().getDeptName());
        recipeInfo.setRecipe_doctor_id(prescriptionOrder.getDoctor().getHisId());
        recipeInfo.setRecipe_doctor_name(prescriptionOrder.getDoctor().getUser().getFullName());
        recipeInfo.setCheck_status(Status.REVIEN.getCode());
        // 验证标志 0直接保存处方
        //1仅进行是否能够开此处方的规则（包含药品库存是否充足）进行判断，具体规则由HIS进行院内规则设定，对所开具的处方是否符合院内规则进行判断校验
        //2 判断并保存（需HIS判断是否符合规则，如符合规则直接保存）
        //保存时需要锁定药品库存
        //默认0
        recipeInfo.setVerify_flag("2");
        // 0非自备(院内取药) 1自备
        recipeInfo.setOneself_flag("1");
        MedicalCase medicalCase =
            medicalCaseRepository.findByOrderId(prescriptionOrder.getOrder().getId()).get();
        List<DiagnoseInfoReq> DiagnoseInfoReqs = Lists.newArrayList();
        for (int i = 0; i < medicalCase.getDiseases().size(); i++) {
            DiagnoseInfoReq diagnoseInfoReq = new DiagnoseInfoReq();
            diagnoseInfoReq.setDiagnose_code(medicalCase.getDiseases().get(i).getDisease().getCode());
            diagnoseInfoReq.setDiagnose_name(medicalCase.getDiseases().get(i).getDisease().getDiseaseName());
            diagnoseInfoReq.setDiag_flag(medicalCase.getDiseases().get(i).getDisease().getDiagFlag().getCode());
            diagnoseInfoReq.setIcd_code(medicalCase.getDiseases().get(i).getDisease().getIcd10Code());
            // 2023年10月17日18:01:40 诊断序号修改逻辑 目前只是预案
            diagnoseInfoReq.setZdxh(String.valueOf(i));
//            if (i < 3) {
//                diagnoseInfoReq.setZdxh(String.valueOf(i));
//            } else {
//                diagnoseInfoReq.setZdxh("2");
//            }
            DiagnoseInfoReqs.add(diagnoseInfoReq);
        }

        List<ItemInfoReq> itemInfoReqs = Lists.newArrayList();

        List<Prescription> prescriptions = prescriptionOrder.getPrescription();
        prescriptions.forEach(u -> {
            /**
             * private String item_code;            // 药品、项目代码 (必须)
             *     private String item_name;            // 药品、项目名称 (必须)
             *     private String drug_flag;           // 药品标识 (必须) 1药品 2项目
             *     private String unit;                 // 药品单位 (可选)
             *     private String clinical_item_code;   // 临床项目代码 (可选)
             *     private String clinical_item_name;   // 临床项目名称 (可选)
             *     private String set_item_name;        // 收费大项目名称 (可选)
             *     private String insurance_code;       // 医保对应代码 (可选)
             *     private String drug_spec;            // 药品规格 (可选)
             *     private String dosage_form_name;     // 剂型名称 (可选)
             *     private String dosage_form_code;     // 剂型代码 (可选)
             *     private String frequency;            // 频次名称 (可选)
             *     private String frequency_code;       // 频次代码 (可选)
             *     private String usage_code;           // 用法代码 (可选)
             *     private String usage;                // 用法名称 (可选)
             *     private String dosage;              // 药品剂量 (可选)
             *     private String dosage_unit;         // 剂量单位 (可选)
             *     private String decocting_method;     // 中药煎法 (可选)
             *     private Integer use_days;            // 用药天数 (可选)
             *     private Integer self_rate;            // 自付比例 (可选)
             *     private Integer price;                // 项目单价 (必须)
             *     private Integer quantity;             // 数量 (必须)
             *     private Integer amount;               // 总金额 (必须)
             */
            // 非必填的暂时不写
            ItemInfoReq itemInfoReq = new ItemInfoReq();

            itemInfoReq.setItem_code(u.getDrugHisCode());
            itemInfoReq.setItem_name(u.getDrugName());
            itemInfoReq.setDrug_flag("1");
            itemInfoReq.setPrice(u.getPrice());
            itemInfoReq.setQuantity(u.getQuantity());
            itemInfoReq.setAmount(u.getQuantity() * u.getPrice());
            itemInfoReq.setFrequency_code(u.getUseFrequencyCode());
            itemInfoReq.setUsage_code(u.getRouteCode());
            itemInfoReq.setUnit(u.getUnit());
            itemInfoReq.setDosage(String.valueOf(u.getSingle()));
            itemInfoReq.setDosage_unit(u.getSingleUnit());
            // TODO 剂型还没存，取不出来，先不传应该没问题
//            itemInfoReq.setDosage_form_code(u.getDosageFormCode());

            itemInfoReqs.add(itemInfoReq);
        });
        recipeInfo.setDiagnose_list(DiagnoseInfoReqs);
        recipeInfo.setItem_infos(itemInfoReqs);
        return nodeRedClient.saveRecipeOnline(hospital.getCode(), recipeInfo);
    }

    @Override
    public SaveMedicalCaseResult saveMedicalCaseOnline(Hospital hospital, Order order) {
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
        Patient patient = patientRepository.getById(order.getPatient().getId());
        MedicalCase medicalCase = medicalCaseRepository.findByOrderId(order.getId()).orElseThrow();
        String diseases = medicalCase.getDiseases().stream()
                .map(MedicalCaseDisease::getDiseaseName).collect(Collectors.joining(","));
        String diagnosis = medicalCase.getDiagnosis();
        SaveMedicalCaseReq medicalCaseReq = SaveMedicalCaseReq.builder()
                .regno(orderExtraInfo.getRegistrationSerialNumber())
                .patid(patient.getHisPatid())
                .patname(patient.getName())
                .emr_id(medicalCase.getId() + "")
                .emr_done_time(TimeUtils.dateToString(medicalCase.getCreatedDate(), "yyyyMMddHHmmss"))
                .emr_type("2")
                .emr_type_name("P")
                .source_name("互联网医院")
                .build();
        StringBuilder sb = new StringBuilder();
        sb.append("患者主诉：").append(order.getDescription());
        sb.append("，既往史：").append(order.getPastHistory());
        sb.append("，家族史：").append(order.getPresentHistory());
        sb.append("，过敏史：").append(order.getAllergyHistory());
        sb.append("，在线诊断：").append(diseases);
        if (StringUtils.isNotBlank(diagnosis)) {
            sb.append("，治疗措施：").append(diagnosis);
        }
        sb.append("，总结建议：").append(medicalCase.getSummary());
        medicalCaseReq.setDetails(sb.toString());
        return nodeRedClient.saveMedicalCaseOnline(hospital.getCode(), medicalCaseReq);
    }

    @Override
    public SaveMedicalCaseResult saveDiagnoseOnline(Hospital hospital, Order order) {
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
        Patient patient = patientRepository.getById(order.getPatient().getId());
        Dept dept = deptRepository.getById(order.getDept().getId());
        MedicalWorker doctor = medicalWorkerRepository.getById(order.getDoctor().getId());
        MedicalCase medicalCase = medicalCaseRepository.findByOrderId(order.getId()).orElseThrow();
        SaveDiagnoseReq diagnoseReq = SaveDiagnoseReq.builder()
                .regno(orderExtraInfo.getRegistrationSerialNumber())
                .patid(patient.getHisPatid())
                .patname(patient.getName())
                .dept_id(dept.getDeptCode())
                .dept_name(dept.getDeptName())
                .doctor_id(doctor.getHisId())
                .doctor_name(doctor.getUser().getFullName())
                .emr_done_time(TimeUtils.dateToString(medicalCase.getCreatedDate(), "yyyyMMddHHmmss"))
                .type("2")
                .source_name("互联网医院")
                .build();
        List<SaveDiagnoseReq.DiagnoseInfo> list = Lists.newArrayList();
        for (int i = 0; i < medicalCase.getDiseases().size(); i++) {
            SaveDiagnoseReq.DiagnoseInfo diagnoseInfoReq =
                new SaveDiagnoseReq.DiagnoseInfo(medicalCase.getDiseases().get(i).getDisease().getCode(),
                                                 medicalCase.getDiseases().get(i).getDisease().getDiseaseName(), medicalCase.getDiseases().get(i).getDisease().getDiagFlag().getCode());
            diagnoseInfoReq.setIcd_code(medicalCase.getDiseases().get(i).getDisease().getIcd10Code());
            // 2023年10月17日18:01:40 诊断序号修改逻辑 目前只是预案
            diagnoseInfoReq.setDiag_no(String.valueOf(i));
//            if (i < 3) {
//                diagnoseInfoReq.setZdxh(String.valueOf(i));
//            } else {
//                diagnoseInfoReq.setZdxh("2");
//            }
            list.add(diagnoseInfoReq);
        }
        diagnoseReq.setZds(list);
        return nodeRedClient.saveDiagnoseOnline(hospital.getCode(), diagnoseReq);
    }

    @Override
    public ReturnRegistResult deleteRecipeOnline(Hospital hospital, Long orderId, String patientName, String patHisId,
                                                 String recipeId) {

        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(orderId);
        if (orderExtraInfo == null) {
            return null;
        }
        return nodeRedClient.deleteRecipeOnline(hospital.getCode(), patientName,
                                                patHisId,
                                                orderExtraInfo.getRegistrationSerialNumber(), recipeId);
    }

    @Override
    public HisGeneralResult noticeForRecipeCheck(Hospital hospital, Order order,
                                                 List<PrescriptionOrder> prescriptionOrders) {
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
//        List<RecipeInfo> recipeInfos = StandardObjectMapper.readValue(orderExtraInfo.getHisRecipeInfosJson(),
//                                                                           new TypeReference<>() {
//                                                                           });
        if (CollectionUtils.isNotEmpty(prescriptionOrders)) {
            PrescriptionOrder prescriptionOrder = prescriptionOrders.get(0);
            MedicalWorker doctorReview = prescriptionOrder.getDoctorReview();
            String reviewTime = DataTypes.DATE.asString(prescriptionOrder.getReviewTime(), "yyyyMMdd",
                                                        Locale.getDefault());

            return nodeRedClient.noticeForRecipeCheck(hospital.getCode(), order.getPatient().getName(),
                                                      order.getPatient().getHisPatid(),
                                                      prescriptionOrder.getHisRecipeNo(),
                                                      orderExtraInfo.getRegistrationSerialNumber(),
                                                      Status.REVIEN.getName(), doctorReview.getHisId(),
                                                      doctorReview.getUser().getFullName(),
                                                      reviewTime, null);
        }
        return null;
    }

    @Override
    public PreChargeResult preCharge(Hospital hospital, PrescriptionOrder prescriptionOrder, PlatformTypeEnum platformType) {
        Order order = prescriptionOrder.getOrder();
        String hisRecipeNo = prescriptionOrder.getHisRecipeNo();
        Patient patient = order.getPatient();
        ElectronicMedicCard electronicMedicCard = order.getElectronicMedicCard();
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        String payType = platformType == null ? "1" : platformType.getValue();
        PreChargeResult preChargeResult = nodeRedClient.preCharge(hospital.getCode(), patient.getHisPatid(),
                                                                  electronicMedicCard.getNumber(),
                                                                  electronicMedicCard.getCardType().getCode(),
                                                                  orderExtraInfo.getRegistrationSerialNumber(),
                                                                  Lists.newArrayList(hisRecipeNo), payType, "0", "1",
                                                                  null, null);
        orderExtraInfo.setHisRecipeSettleId(preChargeResult.getSettle_id());
        orderExtraInfoService.saveOrderExtraInfo(orderExtraInfo);
        return  preChargeResult;
    }

    @Override
    public PreChargeResult outPatientPreCharge(Hospital hospital, Patient patient, ElectronicMedicCard card, String regno,
                                               String insuranceParam, List<String> recipeNoList, String payType,
                                               String extraContent) {
        // 通过orderid 获取 orderExtraInfo

        String selfFlag = insuranceParam == null ? "1" : "0";
        PreChargeResult preChargeResult = nodeRedClient.preCharge(hospital.getCode(), patient.getHisPatid(),
                card.getNumber(),
                card.getCardType().getCode(),
                regno,
                recipeNoList, payType, "0", selfFlag,
                insuranceParam, extraContent);
        return preChargeResult;
    }

    @Override
    public PreChargeResult outPatientPreCharge(Hospital hospital, String hisPatId,
                                               String regno,
                                               String insuranceParam, List<String> recipeNoList, String payType, String extraContent) {
        // 通过orderid 获取 orderExtraInfo

        String selfFlag = insuranceParam == null ? "1" : "0";
        PreChargeResult preChargeResult = nodeRedClient.preCharge(hospital.getCode(), hisPatId,
                                                                  null,
                                                                  null,
                                                                  regno,
                                                                  recipeNoList, payType, "0", selfFlag,
                                                                  insuranceParam, extraContent);
        return preChargeResult;
    }


    @Override
    public HisGeneralResult cancelPreCharge(Hospital hospital, PrescriptionOrder prescriptionOrder) {
        Order order = prescriptionOrder.getOrder();
        Patient patient = order.getPatient();
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        return nodeRedClient.cancelPreCharge(hospital.getCode(), orderExtraInfo.getRegistrationSerialNumber(),
                                             orderExtraInfo.getHisRecipeSettleId(), patient.getHisPatid(), null);
    }

    @Override
    public NodeRedResponseData<ConfirmChargeResult> confirmCharge(Hospital hospital, PrescriptionOrder prescriptionOrder, WechatOrder wechatOrder,
                                                                  HisPayParam hisPayParam) {
        Order order = prescriptionOrder.getOrder();
        String hisRecipeNo = prescriptionOrder.getHisRecipeNo();
        Patient patient = order.getPatient();
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());

        ConfirmChargeReq confirmChargeReq = new ConfirmChargeReq();

        confirmChargeReq.setPatid(patient.getHisPatid());
        confirmChargeReq.setRegno(orderExtraInfo.getRegistrationSerialNumber());
        confirmChargeReq.setSettle_id(orderExtraInfo.getHisRecipeSettleId());
        confirmChargeReq.setSerial_no(order.getId().toString());
        confirmChargeReq.setRecipe_no_list(hisRecipeNo);
        confirmChargeReq.setTotal_amount(wechatOrder.getTotalFee());
        confirmChargeReq.setShould_pay_amount(wechatOrder.getTotalFee());
        confirmChargeReq.setPay_type("1");
        if (hisPayParam != null) {
            confirmChargeReq.setPay_type(hisPayParam.getPayType());
            confirmChargeReq.setTrade_no(hisPayParam.getTransactionId());
        }
        confirmChargeReq.setPay_amount(wechatOrder.getTotalFee());
        confirmChargeReq.setPay_time(TimeUtils.dateToString(wechatOrder.getPayTime(), "yyyyMMddHHmmss"));
        confirmChargeReq.setHosp_account_flag(0);
        confirmChargeReq.setSelf_flag("1");
        confirmChargeReq.setReceipt_account(wechatOrder.getMchId());
//        confirmChargeReq.setPort(null);
//        confirmChargeReq.setPay_card_no(null);
//        confirmChargeReq.setPay_card_amount(null);
//        confirmChargeReq.setPay_card_flag(null);
//        confirmChargeReq.setInsurance_param(null);
//        confirmChargeReq.setCommercial_insurance_param(null);
//        confirmChargeReq.setExtra_content(null);
        return nodeRedClient.confirmCharge(hospital.getCode(), confirmChargeReq);
    }

    @Override
    public NodeRedResponseData<ConfirmChargeResult> confirmCharge(Hospital hospital,
                                                                  PrescriptionOrder prescriptionOrder,
                                                                  AliPayOrder ailPayOrder) {
        Order order = prescriptionOrder.getOrder();
        String hisRecipeNo = prescriptionOrder.getHisRecipeNo();
        Patient patient = order.getPatient();
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());

        ConfirmChargeReq confirmChargeReq = new ConfirmChargeReq();
        ///**
        //     * 门诊患者唯一号 (Y)
        //     */
        //    private String patid;
        //
        //    /**
        //     * 挂号序号 (Y)
        //     */
        //    private String regno;
        //
        //    /**
        //     * HIS结算单号 (Y) 预算时HIS反馈的HIS结算收据号
        //     */
        //    private String settle_id;
        //
        //    /**
        //     * 平台流水号 (Y) 第三方平台流水号，标记唯一一次业务请求，如互联网医院平台订单号
        //     */
        //    private String serial_no;
        //
        //    /**
        //     * 划价序号合集 (Y) 处方序号合集。（格式：109928,1090899,109089）调用门诊待缴费处方，获取未收费处方的处方序号
        //     */
        //    private String recipe_no_list;
        //
        //    /**
        //     * 总金额 (Y)
        //     */
        //    private double total_amount;
        //
        //    /**
        //     * 应付金额 (Y)
        //     */
        //    private double should_pay_amount;
        //
        //    /**
        //     * 支付方式 (Y) 0 现金 1 微信支付 2支付宝支付
        //     */
        //    private int pay_type;
        //
        //    /**
        //     * 支付金额 (Y)
        //     */
        //    private double pay_amount;
        //
        //    /**
        //     * 支付流水号 (Y)
        //     */
        //    private String trade_no;
        //
        //    /**
        //     * 账户标识 (N) 医院收款账户标识，默认为空，医院使用多个支付账户情况下非空，此时HIS账单接口原样传出，以便于多账户对账
        //     */
        //    private String receipt_account;
        //
        //    /**
        //     * 支付时间 (Y) 支付方扣款时间，用于处理对账跨天问题。格式yyyyMMddHHmmss
        //     */
        //    private String pay_time;
        //
        //    /**
        //     * 是否扣院内账户 (Y) 0不从院内账户走1走院内账户
        //     */
        //    private int hosp_account_flag;
        //
        //    /**
        //     * 是否自费结算 (N) 0根据病人医保代码结算1自费结算
        //     */
        //    private Integer self_flag;
        //
        //    /**
        //     * 支付渠道 (N) 特别约定时必填
        //     */
        //    private String port;
        //
        //    /**
        //     * 代币卡序号 (N) 多张卡支付时以|分隔
        //     */
        //    private String pay_card_no;
        //
        //    /**
        //     * 代币卡支付金额 (N) 多张卡支付时以|分隔
        //     */
        //    private String pay_card_amount;
        //
        //    /**
        //     * 代币卡使用标志 (N) 使用代币卡支付时传入1，默认为空
        //     */
        //    private Integer pay_card_flag;
        //
        //    /**
        //     * 医保交易入参 (N) 占位，具体参数格式内容需要根据当地医保确定
        //     */
        //    private String insurance_param;
        //
        //    /**
        //     * 商保结算信息 (N) 存在商保结算时需传入，具体以MAP JSON格式字符串传入
        //     */
        //    private String commercial_insurance_param;
        //
        //    /**
        //     * 扩展信息 (N) 个性化字段扩展信息，根据实际项目进行处理（统一采用BASE64编码转换）
        //     */
        //    private String extra_content;
        confirmChargeReq.setPatid(patient.getHisPatid());
        confirmChargeReq.setRegno(orderExtraInfo.getRegistrationSerialNumber());
        confirmChargeReq.setSettle_id(orderExtraInfo.getHisRecipeSettleId());
        confirmChargeReq.setSerial_no(order.getId().toString());
        confirmChargeReq.setRecipe_no_list(hisRecipeNo);
        confirmChargeReq.setTotal_amount(ailPayOrder.getTotalFee());
        confirmChargeReq.setShould_pay_amount(ailPayOrder.getTotalFee());
        confirmChargeReq.setPay_type("2");
        confirmChargeReq.setPay_amount(ailPayOrder.getTotalFee());
        confirmChargeReq.setTrade_no(ailPayOrder.getTradeNo());
        confirmChargeReq.setPay_time(TimeUtils.dateToString(ailPayOrder.getGmtPayment(), "yyyyMMddHHmmss"));
        confirmChargeReq.setHosp_account_flag(0);
        confirmChargeReq.setSelf_flag("1");
        confirmChargeReq.setReceipt_account(ailPayOrder.getAppId());
//        confirmChargeReq.setPort(null);
//        confirmChargeReq.setPay_card_no(null);
//        confirmChargeReq.setPay_card_amount(null);
//        confirmChargeReq.setPay_card_flag(null);
//        confirmChargeReq.setInsurance_param(null);
//        confirmChargeReq.setCommercial_insurance_param(null);
//        confirmChargeReq.setExtra_content(null);
        return nodeRedClient.confirmCharge(hospital.getCode(), confirmChargeReq);
    }

    @Override
    public NodeRedResponseData<ConfirmChargeResult> outPatientConfirmCharge(Hospital hospital, ElectronicMedicCard card, HisPayParam hisPayParam,
                                                                            OutpatientUnChargeRecipeInfo info, HisOutpatientCharge charge) {
        ConfirmChargeReq confirmChargeReq = new ConfirmChargeReq();
        confirmChargeReq.setPatid(card.getHisPatid());
        confirmChargeReq.setRegno(charge.getRegno());
        confirmChargeReq.setSettle_id(charge.getSettleId());
        confirmChargeReq.setSerial_no(charge.getHisOutpatientChargeGroup().getId() + "");
        confirmChargeReq.setRecipe_no_list(charge.getRecipeNo());
        confirmChargeReq.setTotal_amount(charge.getAmount());
        confirmChargeReq.setShould_pay_amount(charge.getSelfAmount());
        confirmChargeReq.setPay_type("1");
        if (hisPayParam != null) {
            confirmChargeReq.setPay_type(hisPayParam.getPayType());
            confirmChargeReq.setTrade_no(hisPayParam.getTransactionId());
        }
        confirmChargeReq.setPay_amount(charge.getSelfAmount());
        confirmChargeReq.setPay_time(TimeUtils.dateToString(hisPayParam.getPayTime(), "yyyyMMddHHmmss"));
        confirmChargeReq.setHosp_account_flag(0);
        if (StringUtils.isBlank(charge.getPayAuthNo())) {
            confirmChargeReq.setSelf_flag("1");
        } else {
            confirmChargeReq.setSelf_flag("0");
        }
        confirmChargeReq.setReceipt_account(hisPayParam.getMchId());
        confirmChargeReq.setInsurance_param(charge.getPayAuthNo());

        return nodeRedClient.confirmCharge(hospital.getCode(), confirmChargeReq);
    }

    @Override
    public NodeRedResponseData<ConfirmChargeResult> outPatientConfirmCharge(Hospital hospital, String hisPatId,
HisPayParam hisPayParam,
                                                                            OutpatientUnChargeRecipeInfo info, HisOutpatientCharge charge) {
        ConfirmChargeReq confirmChargeReq = new ConfirmChargeReq();
        confirmChargeReq.setPatid(hisPatId);
        confirmChargeReq.setRegno(charge.getRegno());
        confirmChargeReq.setSettle_id(charge.getSettleId());
        confirmChargeReq.setSerial_no(charge.getHisOutpatientChargeGroup().getId() + "");
        confirmChargeReq.setRecipe_no_list(charge.getRecipeNo());
        confirmChargeReq.setTotal_amount(charge.getAmount());
        confirmChargeReq.setShould_pay_amount(charge.getSelfAmount());
        confirmChargeReq.setPay_type("1");
        if (hisPayParam != null) {
            confirmChargeReq.setPay_type(hisPayParam.getPayType());
            confirmChargeReq.setTrade_no(hisPayParam.getTransactionId());
        }
        confirmChargeReq.setPay_amount(charge.getSelfAmount());
        confirmChargeReq.setPay_time(TimeUtils.dateToString(hisPayParam.getPayTime(), "yyyyMMddHHmmss"));
        confirmChargeReq.setHosp_account_flag(0);
        if (StringUtils.isBlank(charge.getPayAuthNo())) {
            confirmChargeReq.setSelf_flag("1");
        } else {
            confirmChargeReq.setSelf_flag("0");
        }
        confirmChargeReq.setReceipt_account(hisPayParam.getMchId());
        confirmChargeReq.setInsurance_param(charge.getPayAuthNo());

        return nodeRedClient.confirmCharge(hospital.getCode(), confirmChargeReq);
    }

    @Override
    public PayResultOnline getPayResultOnline(Hospital hospital, Order order) {
        Patient patient = order.getPatient();
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        return nodeRedClient.getPayResultOnline(hospital.getCode(), orderExtraInfo.getHisRecipeSettleId(),
                                                order.getId().toString());
    }

    @Override
    public CancelOutpatientSettleResult cancelOutpatientSettle(Hospital hospital, Order order, PayPlatform payPlatform) {
        // 通过orderid 获取 orderExtraInfo
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
        Patient patient = order.getPatient();
        List<PrescriptionOrderVM> medicalCaseDetails = prescriptionService.getMedicalCaseDetails(order.getId());
        List<String> hisRecipeNoList = medicalCaseDetails.stream().map(PrescriptionOrderVM::getHisRecipeNo)
            .collect(Collectors.toList());
        // TODO: 线上处方退款时间没有存，需要处理
        String refundTime = DataTypes.DATE.asString(new Date(), "yyyyMMddHHmmss");
        String payType = payPlatform == null || payPlatform.getPlatformType() == null ? "1" : payPlatform.getPlatformType().getValue();
        return nodeRedClient.cancelOutpatientSettle(hospital.getCode(), patient.getHisPatid(),
                                                    orderExtraInfo.getRegistrationSerialNumber(),
                                                    orderExtraInfo.getHisRecipeSettleId(),
                                                    hisRecipeNoList,
                                                    payType,
                                                    String.valueOf(order.getRegistrationFee()),
                                                    payPlatform.getTransactionId(),
                                                    order.getId().toString(), payPlatform.getMchid(), null, refundTime);

    }

    @Override
    public HisFileUploadInfo uploadFile(Hospital hospital, Long orderId, String type, String url) {
        String code = AppContext.getInstance(HospitalRepository.class).getById(hospital.getId()).getCode();
        return nodeRedClient.uploadFile(code, orderId.toString(), type, url);
    }

    @Override
    public Page<FollowUpInfo> getFollowUpListByPatient(Hospital hospital, Patient patient, FollowUpParam param,
                                                       Integer pageNo, Integer size) {
        String status = param.getStatus() == null ? null : "" + param.getStatus();

        List<FollowUpInfo> followUpList = nodeRedClient.getFollowUpListByPatient(hospital.getCode(),
                        patient.getHisPatid(), status,
                        param.getExpected_execute_start_time(), param.getExpected_execute_end_time(),
                        param.getStart_time(), param.getEnd_time()).stream()
                .sorted(Comparator.comparing(FollowUpInfo::getPush_time, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());

        long total = followUpList.size();
        int start = pageNo * size;
        int end = start + size;
        followUpList = followUpList.subList(Math.min(start, followUpList.size()), Math.min(end, followUpList.size()));
        return new PageImpl<>(followUpList, PageRequest.of(pageNo, size), total);
    }

    @Override
    public Page<DeptAndWardInfo> getDeptAndWardInfo(Hospital hospital, Integer pageNo, Integer size) {
        List<DeptAndWardInfo> deptAndWardInfo = nodeRedClient.getDeptAndWardInfo(hospital.getCode(),
                                                                                 Integer.toString(pageNo),
                                                                                 Integer.toString(size));
        return new PageImpl<>(deptAndWardInfo, PageRequest.of(pageNo, size), size);
    }

    @Override
    public List<WardBedInfo> getWardBedInfo(Hospital hospital, String deptCode) {
        List<WardBedInfo> wardBedInfos = nodeRedClient.getWardBedInfo(hospital.getCode(),
                                                                      deptCode);
        return wardBedInfos;
    }

    @Override
    public BillSummary getHisBills(Hospital hospital, String billDate, String payType) {
        return nodeRedClient.getTradeHisBill(hospital.getCode(), billDate, payType);
    }

    @Override
    public OfflineOrderDTO saveAppointment(Hospital hospital, CreateOfflineOrderDTO createOfflineOrderDTO) {
        return null;
//        return nodeRedClient.saveAppointment(hospitalCode, offlineOrder., schedulingId,
//                sourceNumber, telephone, serialNo, opCertificateNo, memo);
    }

    @Override
    @Transactional
    public ReponseResult cancelAppointment(Hospital hospital, OfflineOrder offlineOrder) {
        Patient patient = patientRepository.getById(Long.valueOf(offlineOrder.getPatientId()));
        ReponseResult reponseResult = nodeRedClient.cancelAppointment(hospital.getCode(), "",
                patient.getHisPatid(), offlineOrder.getAppointmentId(), "");
        log.info("取消预约登记返回状态{}", reponseResult.isSuccess());
        return reponseResult;
    }

    @Override
    public ReverseAbnormalBillResult reverseAbnormalBill(Hospital hospital, Order order) {
        // TODO 参数不明确需确认 后端需要入库
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
        // 互联网医院订单编号 ??
        String orderId = String.valueOf(orderExtraInfo.getOrderId());
        // 接收账单的唯一标识 ??
        String hisBillCallId = null;
        return nodeRedClient.reverseAbnormalBill(hospital.getCode(), orderId, hisBillCallId);
    }


    @Override
    public TradeIHBillResult tradeIHBill(Hospital hospital, TradeIHBillSummary billSummary) {
        return nodeRedClient.tradeIHBill(hospital.getCode(), billSummary);
    }

    @Override
    public InpatientHospCardPreChargeResult inpatientHospCardPreCharge(Hospital hospital, String patientName, String rego) {
        return nodeRedClient.inpatientHospCardPreCharge(hospital.getCode(), patientName, rego);
    }

    @Override
    public NodeRedResponseData<InpatientHospCardChargeResult> inpatientHospCardCharge(Hospital hospital,
                                                                 String patientName,
                                                                 String reg_no,
                                                                 InpatientHospitalChargeReq inpatientHospitalChargeReq) {
        // 预充值流水号	N	通过住院预交金预充值时返回，如果有返回则必填 ??
        String advanceChargeId = inpatientHospitalChargeReq.getAdvance_charge_id();
        // HIS订单号	Y	医院用于唯一标识一笔缴费的凭证，全院范围内不重复(含收费、退费、预交金等等所有收费退费行为)，如预充值时返回此字段时，则此字段不得为空 ??
        String outTradeNo = inpatientHospitalChargeReq.getOut_trade_no();
        // 平台流水号	N	第三方平台流水号，标记唯一一次业务请求，(住院预交金预充值时HIS返回收据号为空时，此参数必填)
        String serialNo = inpatientHospitalChargeReq.getSerial_no();
        // 支付方式	Y	0 现金 1 微信支付 2支付宝支付
        String payType = inpatientHospitalChargeReq.getPay_type();
        // 支付金额	Y
        String selfAmount = inpatientHospitalChargeReq.getSelf_amount();
        // 支付流水号	Y	支付宝、微信或银联交易的流水号
        String tradeNo = inpatientHospitalChargeReq.getTrade_no();
        // 账户标识	N	医院收款账户标识，默认为空，医院使用多个支付账户情况下非空，此时HIS账单接口原样传出，以便于多账户对账(具体值需提前约定)
        String accountId = inpatientHospitalChargeReq.getAccount_id();
        // 支付时间	Y	格式yyyyMMddHHmmss
        String payTime = inpatientHospitalChargeReq.getPay_time();
        // 支付渠道	Y	特别约定时必填
        String port = inpatientHospitalChargeReq.getPort();
        // 微信用户标识	N
        String openId = inpatientHospitalChargeReq.getOpen_id();
        // 微信用户联系方式	N
        String openPhone = inpatientHospitalChargeReq.getOpen_phone();
        // 充值人联系电话	N	住院预交金充值人的联系电话
        String telephone = inpatientHospitalChargeReq.getTelephone();
        // 充值联系人姓名	N	住院预交金充值人的姓名
        String contactsName = inpatientHospitalChargeReq.getContacts_name();
        // 充值联系人证件号	N	住院预交金充值人的证件号
        String contactsCertificateNo = inpatientHospitalChargeReq.getContacts_certificate_no();
        return nodeRedClient.inpatientHospCardCharge(hospital.getCode(), patientName, reg_no, advanceChargeId, outTradeNo, serialNo, payType, selfAmount,
                tradeNo, accountId, payTime, port, openId, openPhone, telephone, contactsName, contactsCertificateNo);
    }

    @Override
    public InpatientPreChargeResult inpatientPreCharge(Hospital hospital, Order order) {
        // TODO 参数不明确需确认 后端需要入库
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
        Patient patient = order.getPatient();
        String patientName = patient.getName();
        String patientHisId = patient.getHisPatid();
        // 入院单号 ??
        String admissionNo = null;
        return nodeRedClient.inpatientPreCharge(hospital.getCode(), patientName, patientHisId, admissionNo);
    }

    @Override
    public InpatientConfirmChargeResult inpatientConfirmCharge(Hospital hospital, Order order) {
        // TODO 参数不明确需确认 后端需要入库
        OrderExtraInfo orderExtraInfo = orderExtraInfoService.getOrderExtraInfo(order.getId());
        if (orderExtraInfo == null) {
            return null;
        }
        Patient patient = order.getPatient();
        String patientName = patient.getName();
        String rego = orderExtraInfo.getRegistrationSerialNumber();
        String patientHisId = patient.getHisPatid();
        String settleId = orderExtraInfo.getSettleId();
        // 结算方式	Y	1在院，2出院 ??
        String settleType = String.valueOf(1);
        // 是否自费结算 Y 0根据病人医保代码结算  1自费结算（默认自费结算）??
        String selfFlag = String.valueOf(0);
        // 医保入参 N 占位，具体参数格式内容需要根据当地医保确定 ??
        String insuranceParam = null;
        // 渠道 Y ??
        String port = null;
        // 账户标识 N	医院收款账户标识，默认为空，医院使用多个支付账户情况下非空，此时HIS账单接口原样传出，以便于多账户对账(具体值需提前约定) ??
        String receiptAccount = null;
        return nodeRedClient.inpatientConfirmCharge(hospital.getCode(), patientName, rego, patientHisId, settleId,
                settleType, selfFlag, insuranceParam, port, receiptAccount);
    }

    @Override
    public List<AppointmentInfo> getSourceDetails(Hospital hospital, String beginDate, String endDate, String channelType, String deptId, String doctorName) {
        List<AppointmentInfo> sourceDetails = nodeRedClient.getSourceDetails(hospital.getCode(), beginDate, endDate, channelType, deptId);
        log.info("查询his全院预约号源结果 size: {}", sourceDetails.size());
        long start = System.currentTimeMillis();
        // 获取已经手动关闭线下挂号的科室
        List<OfflineDept> offlineDeptList = offlineDeptRepository.findAllByHospitalAndEnabled(hospital, false);
        List<String> offlineDeptCodeList = offlineDeptList.stream().map(OfflineDept::getDeptCode).collect(Collectors.toList());
        Map<String, OfflineMedicalWorkerDTO> doctorMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sourceDetails)) {
            sourceDetails = sourceDetails.stream().filter(appointmentInfo -> {
                boolean flag = StringUtils.isNotBlank(appointmentInfo.getDuty_date()) &&  StringUtils.length(appointmentInfo.getDuty_date()) > 7;
                if (StringUtils.isNotBlank(deptId)) {
                    flag = flag && deptId.equalsIgnoreCase(appointmentInfo.getDept_id());
                }
                if (StringUtils.isNotBlank(doctorName)) {
                    flag = flag &&  StringUtils.isNotBlank(appointmentInfo.getDoctor_name()) && appointmentInfo.getDoctor_name().contains(doctorName);
                }
                // 将用户在D端线下科室管理手动关闭挂号科室过滤掉
                if (CollectionUtils.isNotEmpty(offlineDeptCodeList)) {
                    flag = flag && StringUtils.isNotBlank(appointmentInfo.getDept_id()) && !offlineDeptCodeList.contains(appointmentInfo.getDept_id());
                }
                return flag;
            }).collect(Collectors.toList());
            sourceDetails = new ArrayList<>(sourceDetails.stream().collect(Collectors.toMap(
                    o -> o.getDuty_date().substring(0, 8) + o.getDoctor_id(),// 截取到年月日，以时间和医生（科室）id组合为key归约对应的号源
                    appointmentInfo -> appointmentInfo, (existing, replacement) -> {
                        existing.setCan_use_source_qty(String.valueOf(Integer.parseInt(existing.getCan_use_source_qty()) +
                                Integer.parseInt(replacement.getCan_use_source_qty())));
                        // 将end_time设置为最后一条记录的end_time
                        if (replacement.getEnd_time().compareTo(existing.getEnd_time()) > 0) {
                            existing.setEnd_time(replacement.getEnd_time());
                        }
                        return existing;
                    }, LinkedHashMap::new)).values());
            for (AppointmentInfo sourceDetail : sourceDetails) {
                if (SourceType.DOCTOR_SOURCE.getCode().equals(sourceDetail.getSource_type())) {
                    String key = hospital.getCode() + "." + sourceDetail.getDoctor_id();
                    if (doctorMap.containsKey(key)) {
                        sourceDetail.setOfflineMedicalWorkerDTO(doctorMap.get(key));
                    } else {
                        offlineMedicalWorkerRepository.findOneByHospitalAndJobNumber(hospital, sourceDetail.getDoctor_id())
                                .ifPresent(offlineMedicalWorker -> {
                                    OfflineMedicalWorkerDTO doctor = new OfflineMedicalWorkerDTO(offlineMedicalWorker);
                                    doctor.setFavorableRate(evaluateService.getFavorableRate(hospital, offlineMedicalWorker));
                                    doctor.setPatientTotal(offlineOrderService.getPatientTotal(hospital, offlineMedicalWorker));
                                    sourceDetail.setOfflineMedicalWorkerDTO(doctor);
                                    doctorMap.put(key, doctor);
                                });

                    }
                }
            }
        }
        log.info("查询后处理全院号源 耗时：{}ms", System.currentTimeMillis() - start);
        return sourceDetails;
    }

    @Override
    public List<SchedulingInfo> getSchedulingListById(Hospital hospital, String schedulingId, String sourceType, String channelType) {
        return nodeRedClient.getSchedulingListById(hospital.getCode(), schedulingId, sourceType, channelType);
    }

    @Override
    public List<SchedulingDeptInfo> getSchedulingDeptList(Hospital hospital, String beginDate, String endDate, String channelType) {
        return nodeRedClient.getSchedulingDeptList(hospital.getCode(), beginDate, endDate, channelType);
    }
    @Override
    public List<SchedulingDeptInfo> getSchedulingDeptListTest(Hospital hospital, String beginDate, String endDate, String channelType) {
        return nodeRedClient.getSchedulingDeptListTest(hospital.getCode(), beginDate, endDate, channelType);
    }

    @Override
    public List<SchedulingDoctorInfo> getSchedulingDoctorList(Hospital hospital, String deptId, String beginDate, String endDate, String channelType) {
        return nodeRedClient.getSchedulingDoctorList(hospital.getCode(), deptId, beginDate, endDate, channelType);
    }

    @Override
    public List<SchedulingDeptSourceInfo> getSchedulingDeptSourceDetails(Hospital hospital, String beginDate, String endDate, String deptId, String channelType) {
        return nodeRedClient.getSchedulingDeptSourceDetails(hospital.getCode(), beginDate, endDate, deptId, channelType);
    }

    @Override
    public List<SchedulingDoctorSourceInfo> getSchedulingDoctorSourceDetails(Hospital hospital,
                                                                             String beginDate, String endDate, String doctorId, String channelType) {

        return nodeRedClient.getSchedulingDoctorSourceDetails(hospital.getCode(), beginDate, endDate,
                doctorId, channelType);
    }

    @Override
    public SchedulingDoctorSourceDetails getSchedulingDoctorSourceInfo(Hospital hospital, String beginDate, String endDate,
                                                                       String doctorId, String channelType) {
        SchedulingDoctorSourceDetails schedulingDoctorSourceDetails = new SchedulingDoctorSourceDetails();
        if (Objects.equals(channelType, ChannelType.OFFLINE.getCode())) {
            OfflineMedicalWorker offlineMedicalWorker;
            offlineMedicalWorker = offlineMedicalWorkerRepository.findOneByHospitalAndJobNumber(hospital, doctorId)
                    .orElseThrow(() -> ErrorType.MEDICAL_WORKER_NOT_NULL.toProblem("线下医生未同步"));
            OfflineMedicalWorkerDTO offlineMedicalWorkerDTO = new OfflineMedicalWorkerDTO(offlineMedicalWorker);
            offlineMedicalWorkerDTO.setFavorableRate(evaluateService.getFavorableRate(hospital, offlineMedicalWorker));
            offlineMedicalWorkerDTO.setPatientTotal(offlineOrderService.getPatientTotal(hospital, offlineMedicalWorker));
            schedulingDoctorSourceDetails.setOfflineMedicalWorkerDTO(offlineMedicalWorkerDTO);
        }
        List<SchedulingDoctorSourceInfo> schedulingDoctorSourceInfos = nodeRedClient
                .getSchedulingDoctorSourceDetails(hospital.getCode(), beginDate, endDate, doctorId, channelType);
        schedulingDoctorSourceDetails.setSchedulingDoctorSourceInfos(schedulingDoctorSourceInfos);
        return schedulingDoctorSourceDetails;
    }

    @Override
    public List<PatientAppointmentInfo> getPatientAppointment(Hospital hospital, String patientName, String patientId, String beginDate,
                                                              String endDate, String routeFlag, String dateType) {
        return nodeRedClient.getPatientAppointment(hospital.getCode(), patientName, patientId,
                beginDate, endDate, routeFlag, dateType);
    }

    @Override
    public List<PatientRegistInfo> getRegistListByPatId(Hospital hospital, String patientName, String patId,
                                                        String beginDate,
                                                        String endDate, String routeFlag) {
        return nodeRedClient.getRegistListByPatId(hospital.getCode(), patientName, patId, beginDate, endDate, routeFlag);
    }

    @Override
    public PatientRegistInfo getRegistListByRegno(Hospital hospital, String patientName, String regno, String appointmentId,
                                                        String patientId, String routeFlag) {
        return nodeRedClient.getRegistListByRegno(hospital.getCode(), patientName, regno, appointmentId, patientId, routeFlag);
    }

    @Override
    public List<PatientRegistWaitingInfo> getPatientRegistWaitingListByPatId(Hospital hospital, String patientName, String hisPatientId) {
        return nodeRedClient.getPatientRegistWaitingListByPatId(hospital.getCode(), patientName, hisPatientId);
    }

    @Override
    public List<PatientDrugWaitingInfo> getPatientDrugWaitingList(Hospital hospital, String patientName, String hisPatientId) {
        return nodeRedClient.getPatientDrugWaitingList(hospital.getCode(), patientName, hisPatientId);
    }

    @Override
    public List<OutpatientItemHintInfo> getOutpatientItemHint(Hospital hospital, String hisPatientId, String settleId) {
        return nodeRedClient.getOutpatientItemHint(hospital.getCode(),
                hisPatientId, settleId);
    }

    @Override
    public List<PatientSignInfo> getPatientSignInfo(Hospital hospital, String date) {
        return nodeRedClient.getPatientSignInfo(hospital.getCode(), date);
    }

    @Override
    public List<PatientWaitingInfo> getPatientWaiting(Hospital hospital, MedicalWorker medicalWorker) {
        // TODO 这样写是否正确
        String deptId = String.valueOf(medicalWorker.getDeptMedicalWorkers().get(0).getDept().getId());
        return nodeRedClient.getPatientWaiting(hospital.getCode(),
                medicalWorker.getHisId(), deptId);
    }

    @Override
    public List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(Hospital hospital, Patient patient, ElectronicMedicCard card,
                                                                              String beginDate, String endDate,
                                                                              String insuranceParam, String channelType) {
        return nodeRedClient.getOutpatientUnChargeRecipeList(hospital.getCode(), patient.getName(),
                card == null ? null : card.getNumber(), card == null ? null : card.getCardType().getCode(),
                patient.getHisPatid(), beginDate, endDate, insuranceParam, channelType);
    }

    @Override
    public List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(Hospital hospital, String hisPatId,
                                                                              String patName,
                                                                              ElectronicMedicCard card,
                                                                              String beginDate, String endDate,
                                                                              String insuranceParam, String channelType) {
        return nodeRedClient.getOutpatientUnChargeRecipeList(hospital.getCode(), patName,
                                                             card == null ? null : card.getNumber(), card == null ? null : card.getCardType().getCode(),
                                                             hisPatId, beginDate, endDate, insuranceParam, channelType);
    }

    @Override
    @Cacheable(cacheNames = Constants.CACHE_HIS_PRICE_PUBLICITY, unless = "#hospital == null", key = "#hospital.id" + '.' + "#pricePublicityType")
    public List<PricePublicityInfo> getPricePublicityList(Hospital hospital, String pricePublicityType, String keyword) {
        return nodeRedClient.getPricePublicityList(hospital.getCode(), pricePublicityType, keyword);
    }

    @Override
    public Page<PricePublicityInfo> getPricePublicityPage(Hospital hospital, String pricePublicityType, String keyword, int page, int size) {
        HisPricePublicityCache publicityCache = AppContext.getInstance(HisPricePublicityCache.class);
        List<PricePublicityInfo> pricePublicityList = publicityCache.getPrice(hospital, pricePublicityType);
        if (CollectionUtils.isEmpty(pricePublicityList)) {
            return PageUtils.paginate(Lists.newArrayList(), page, size);
        }
        if (StringUtils.isNotBlank(keyword)) {
            pricePublicityList = pricePublicityList
                    .stream()
                    .filter(pricePublicityInfo -> StringUtils.containsIgnoreCase(pricePublicityInfo.getPinyin(), keyword) ||
                            StringUtils.containsIgnoreCase(pricePublicityInfo.getName(), keyword))
                    .collect(Collectors.toList());
        }
        return PageUtils.paginate(pricePublicityList, page, size);
    }

    @Override
    public List<DoctorSchedules> getDoctorSchedules(Hospital hospital, String deptId, String doctorName, String beginDate, String endDate) {
        List<DoctorSchedules> doctorSchedules = nodeRedClient.getDoctorSchedules(hospital.getCode(), deptId, doctorName, beginDate, endDate)
                .stream().sorted((s1, s2) -> Collator.getInstance(Locale.CHINA).compare(s1.getDoctor_name(), s2.getDoctor_name()))
                .collect(Collectors.toList());
        if (StringUtils.isNotBlank(doctorName)) {
            doctorSchedules = doctorSchedules.stream()
                    .filter(doctorSchedule -> doctorName.equalsIgnoreCase(doctorSchedule.getDoctor_name()))
                    .collect(Collectors.toList());
        }
        return doctorSchedules;
    }

    @Override
    public List<SpecialClinicInfo> getSpecialClinicList(Hospital hospital, String beginDate, String endDate) {
        return nodeRedClient.getSpecialClinicList(hospital.getCode(), beginDate, endDate);
    }

    @Override
    public List<GeneralClinicInfo> getGeneralClinicList(Hospital hospital, String beginDate, String endDate) {
        return nodeRedClient.getGeneralClinicList(hospital.getCode(), beginDate, endDate);
    }

    @Override
    public List<InspectionReport> getInspectionReportListByCardNo(Hospital hospital, String patientName, String cardNo,
                                                          String beginDate, String endDate) {
        return nodeRedClient.getInspectionReportListByCardNo(hospital.getCode(), patientName, cardNo, beginDate, endDate);
    }

    @Override
    public List<InspectionReport> getInspectionReportListByPatId(Hospital hospital, ElectronicMedicCard card, String userSource, String beginDate, String endDate) {
        List<InspectionReport> inspectionReportList = new ArrayList<>();
        Patient patient = card.getPatient();
        if (StringUtils.isBlank(userSource)) {
            // 查询所有的检验报告，包括门诊和住院
            List<String> sourceTypes = Arrays.asList("1", "2");
            for (String sourceType : sourceTypes) {
                List<InspectionReport> reports = nodeRedClient.getInspectionReportListByPatId(hospital.getCode(), patient.getName(), patient.getHisPatid(),
                        "", "", sourceType, beginDate, endDate);
                if (CollectionUtils.isNotEmpty(reports)) {
                    inspectionReportList.addAll(reports);
                }
            }
        } else {
            inspectionReportList = nodeRedClient.getInspectionReportListByPatId(hospital.getCode(), patient.getName(), patient.getHisPatid(),
                    "", "", userSource, beginDate, endDate);
        }
        if (CollectionUtils.isNotEmpty(inspectionReportList)) {
            inspectionReportList = inspectionReportList.stream().sorted(Comparator.comparing(InspectionReport::getCensorship_time).reversed())
                    .collect(Collectors.toList());
            inspectionReportList.forEach(u -> {
                if (StringUtils.isBlank(u.getApply_time())) {
                    u.setApply_time(u.getCensorship_time());
                }
            });
        }
        return inspectionReportList;
    }

    @Override
    public List<LaboratoryReport> getLaboratoryReportList(Hospital hospital, String reportNo, String reportTypeCode) {
        return nodeRedClient.getLaboratoryReportList(hospital.getCode(), reportNo, reportTypeCode);
    }

    @Override
    public List<MicroorganismReport> getMicroorganismReportsByCardNo(Hospital hospital, String patientName, String cardNo, String beginDate, String endDate) {
        return nodeRedClient.getMicroorganismReportsByCardNo(hospital.getCode(), patientName, cardNo, beginDate, endDate);
    }

    @Override
    public List<MicroorganismReport> getMicroorganismReportsByPatId(Hospital hospital, String patientName, Long id, Integer userSource,
                                                                    String beginDate, String endDate) {
        return nodeRedClient.getMicroorganismReportsByPatId(hospital.getCode(), patientName,
                String.valueOf(id), String.valueOf(userSource), beginDate, endDate);
    }

    @Override
    public List<LaboratoryMicroorganismsReportResult> getLaboratoryMicroorganismsReportResult(Hospital hospital, String reportNo, String reportTypeCode) {
        return nodeRedClient.getLaboratoryMicroorganismsReportResult(hospital.getCode(), reportNo, reportTypeCode);
    }

    @Override
    public List<RisReport> getRisReportsByCardNo(Hospital hospital,String patientName, String cardNo, String beginDate, String endDate) {
        return nodeRedClient.getRisReportsByCardNo(hospital.getCode(), patientName, cardNo, beginDate, endDate);
    }

    @Override
    public List<RisReport> getRisReportsByPatId(Hospital hospital, ElectronicMedicCard card, String userSource, String beginDate, String endDate) {
        Patient patient = card.getPatient();
        List<RisReport> risReportList = new ArrayList<>();
        if (StringUtils.isBlank(userSource)) {
            // 查询所有的检查报告，包括门诊和住院
            List<String> sourceTypes = Arrays.asList("1", "2");
            for (String sourceType : sourceTypes) {
                List<RisReport> reports = nodeRedClient.getRisReportsByPatId(hospital.getCode(), patient.getName(), card.getHisPatid(),
                        "", patient.getIdCardNum(), sourceType, beginDate, endDate);
                if (CollectionUtils.isNotEmpty(reports)) {
                    risReportList.addAll(reports);
                }
            }
        } else {
            risReportList = nodeRedClient.getRisReportsByPatId(hospital.getCode(), patient.getName(), card.getHisPatid(),
                    "", patient.getIdCardNum(), userSource, beginDate, endDate);
        }
        if (CollectionUtils.isNotEmpty(risReportList)) {
            risReportList = risReportList.stream().sorted(Comparator.comparing(RisReport::getApply_time).reversed()).collect(Collectors.toList());
        }
        return risReportList;
    }

    @Override
    public List<RisReportResult> getRisReportResult(Hospital hospital, String reportNo, String reportTypeCode) {
        return nodeRedClient.getRisReportResult(hospital.getCode(), reportNo, reportTypeCode);
    }

    @Override
    public List<CriticalValueReport> getCriticalValueReportByPatId(Hospital hospital, String patientName, Long id, Integer userSource,
                                                                   String beginDate, String endDate) {
        return nodeRedClient.getCriticalValueReportByPatId(hospital.getCode(), patientName,
                String.valueOf(id), String.valueOf(userSource), beginDate, endDate);
    }

    @Override
    public List<InpatientInfo> getInpatientListByHisCardNo(Hospital hospital, String patientName, String hisCardNo, String telephone) {
        return nodeRedClient.getInpatientListByHisCardNo(hospital.getCode(), patientName, hisCardNo, telephone);
    }

    @Override
    public List<InpatientInfo> getInpatientListByCardNo(Hospital hospital, String patientName, String cardNo) {
        return nodeRedClient.getInpatientListByCardNo(hospital.getCode(), patientName, cardNo);
    }

    @Override
    public List<InpatientInfo> getInpatientListByCertNo(Hospital hospital, String patientName, String certNo) {
        return nodeRedClient.getInpatientListByCertNo(hospital.getCode(), patientName, certNo);
    }

    @Override
    public List<InpatientRecord> getInpatientRecordList(Hospital hospital, String patientName, String hisPatientId, String beginDate,
                                                        String endDate, String status) {
        return nodeRedClient.getInpatientRecordList(hospital.getCode(), patientName, hisPatientId, null, beginDate, endDate, status);
    }

    @Override
    public List<InpatientRecord> getInpatientRecordList(Hospital hospital, String patientName, String hisPatientId, String hospNo, String beginDate,
                                                        String endDate, String status) {
        return nodeRedClient.getInpatientRecordList(hospital.getCode(), patientName, hisPatientId, hospNo, beginDate, endDate, status);
    }


    @Override
    public InpatientAdvanceCharge getInpatientAdvanceCharge(Hospital hospital, String patientName, String regno) {
        return nodeRedClient.getInpatientAdvanceCharge(hospital.getCode(), patientName, regno);
    }

    @Override
    public List<InpatientAdvanceChargeDetail> getInpatientAdvanceChargeDetailList(Hospital hospital, String patientName, String regno) {
        return nodeRedClient.getInpatientAdvanceChargeDetailList(hospital.getCode(), patientName, regno);
    }

    @Override
    public SupplyHospitalAdmissionCertificateResult saveSupplyHospitalAdmissionCertificate(Hospital hospital, long card_id,
                                                                                           SupplyHospitalAdmissionCertificateReq supplyHospitalAdmissionCertificate) {
        HisAdmissionInfo hisAdmissionInfo = supplyHospitalAdmissionCertificate.toEntity();
        ElectronicMedicCard medicCard = electronicMedicCardRepository.findById(card_id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        hisAdmissionInfo.setPatient_card_id(card_id + "");
        hisAdmissionInfo.setPatient_id(medicCard.getPatient().getId() + "");
        hisAdmissionInfo.setHospitalId(medicCard.getPatient().getHospital().getId());
        SupplyHospitalAdmissionCertificateResult supplyHospitalAdmissionCertificateResult = nodeRedClient.saveSupplyHospitalAdmissionCertificate(
            hospital.getCode(), supplyHospitalAdmissionCertificate);
        if (supplyHospitalAdmissionCertificateResult != null) {
            hisAdmissionInfo.setDeptName(supplyHospitalAdmissionCertificateResult.getDept_name());
        }
        hisAdmissionInfoRepository.save(hisAdmissionInfo);
        return supplyHospitalAdmissionCertificateResult;
    }

    @Override
    public List<OutpatientRecipeInfo> getOutpatientRecipeList(Hospital hospital, String patientName, String patientId,
                                                              String beginDate, String endDate, String insuranceParam) {
        List<OutpatientRecipeInfo> outpatientRecipeList = nodeRedClient.getOutpatientRecipeList(hospital.getCode(), patientName, patientId, beginDate, endDate, insuranceParam);
        List<PrescriptionOrder> prescriptionOrderList = prescriptionOrderRepository.findAllByHospitalAndHisRecipeNoIsNotNull(hospital);
        if (CollectionUtils.isNotEmpty(prescriptionOrderList) && CollectionUtils.isNotEmpty(outpatientRecipeList)) {
            prescriptionOrderList.forEach(prescriptionOrder -> {
                String hisRecipeNo = prescriptionOrder.getHisRecipeNo();
                outpatientRecipeList.stream()
                        .filter(outpatientRecipeInfo -> Arrays.asList(hisRecipeNo.split(","))
                                .contains(outpatientRecipeInfo.getRecipe_no()))
                        .forEach(outpatientRecipeInfo -> outpatientRecipeInfo
                                .setPrescriptionOrderDTO(new PrescriptionOrderDTO(prescriptionOrder)));
            });
        }
        return outpatientRecipeList;
    }

    @Override
    public ReponseResult updateHospitalAdmissionCertificate(Hospital hospital, HospitalAdmissionCertificateReq param) {
        return nodeRedClient.updateHospitalAdmissionCertificate(hospital.getCode(), param);
    }

    @Override
    public ReponseResult checkHospitalAdmissionOnline(Hospital hospital, String patientName, String regNo,
                                                      String admissionNo, String patientId) {
        return nodeRedClient.checkHospitalAdmissionOnline(hospital.getCode(), patientName, regNo, admissionNo, patientId);
    }

    @Override
    public List<OutpatientCharge> getOutpatientChargeList(Hospital hospital, Patient patient, String channelType, String beginDate, String endDate) {
        return nodeRedClient.getOutpatientChargeList(hospital.getCode(), patient.getName(), patient.getHisPatid(), channelType, beginDate, endDate);
    }

    @Override
    public OutpatientChargeDetail getOutpatientChargeDetails(Hospital hospital, String settleId) {
        OutpatientChargeDetail outpatientChargeDetail = nodeRedClient.getOutpatientChargeDetails(hospital.getCode(), settleId);
        if (outpatientChargeDetail == null) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("根据settleId：" + settleId +"未查询到该结算单");
        }
        if (StringUtils.isNotBlank(outpatientChargeDetail.getTotal_fee())) {
            outpatientChargeDetail.setTotalAmount(outpatientChargeDetail.getTotal_fee());
        } else {
            // 计算总金额
            double totalAmount = outpatientChargeDetail.getRecipe_infos().stream()
                .flatMap(recipeInfo -> recipeInfo.getItem_infos().stream())
                .mapToDouble(itemInfo -> Double.parseDouble(itemInfo.getAmount()))
                .sum();
            outpatientChargeDetail.setTotalAmount(String.valueOf(totalAmount));
        }

        if (StringUtils.isNotBlank(outpatientChargeDetail.getSelf_fee())) {
            outpatientChargeDetail.setSelfAmount(outpatientChargeDetail.getSelf_fee());
        }
        if (StringUtils.isNotBlank(outpatientChargeDetail.getInsurance_fee())) {
            outpatientChargeDetail.setInsuranceAmount(outpatientChargeDetail.getInsurance_fee());
        }
        return outpatientChargeDetail;
    }

    @Override
    public List<InpatientFeeDetail> getInpatientFeeDetails(Hospital hospital, String patientName, String regNo, String beginDate, String endDate) {
        return nodeRedClient.getInpatientFeeDetails(hospital.getCode(), patientName, regNo, beginDate, endDate);
    }

    @Override
    public List<InpatientDaily> getInpatientDaily(Hospital hospital, String patientName, String regNo, String date, String summaryFlag) {
        return nodeRedClient.getInpatientDaily(hospital.getCode(), patientName, regNo, date, summaryFlag);
    }

    @Override
    public List<InpatientSettleInfo> getInpatientSettleList(Hospital hospital, String patientName, String regNo) {
        return nodeRedClient.getInpatientSettleList(hospital.getCode(), patientName, regNo);
    }

    @Override
    public List<HospitalAdmissionCertificate> getHospitalAdmissionCertificateByCardNo(Hospital hospital, String patientName, String cardNo,
                                                                                      String hisPatid, String identity, String admissionNo,
                                                                                      String beginDate, String endDate) {
        return nodeRedClient.getHospitalAdmissionCertificateByCardNo(hospital.getCode(), patientName, cardNo, hisPatid, identity,
                admissionNo, beginDate, endDate);
    }

    @Override
    public List<DischargeMedication> getDischargeMedicationList(Hospital hospital, String patientName, String regNo) {
        return nodeRedClient.getDischargeMedicationList(hospital.getCode(), patientName, regNo);
    }

    @Override
    public List<OperationHints> getOperationHints(Hospital hospital, String patientName, String regNo) {
        return nodeRedClient.getOperationHints(hospital.getCode(), patientName, regNo);
    }

    @Override
    public List<OperationSchedule> getOperationSchedule(Hospital hospital, String patientName, String userSource, String regNo) {
        return nodeRedClient.getOperationSchedule(hospital.getCode(), patientName, userSource, regNo);
    }

    @Override
    public List<InpatientMedicalRecordText> getInpatientMedicalRecordText(Hospital hospital, String patientName, String regNo, String recType) {
        return nodeRedClient.getInpatientMedicalRecordText(hospital.getCode(), patientName, regNo, recType);
    }

    @Override
    public HospitalAdmission addHospitalAdmission(Hospital hospital, HospitalAdmissionReq param) {
        return nodeRedClient.addHospitalAdmission(hospital.getCode(), param);
    }

    @Override
    public ApplyElectronicInvoice applyElectronicInvoice(Hospital hospital, Patient patient, ApplyElectronicInvoiceReq param) {
        param.setPatid(patient.getHisPatid());
        ApplyElectronicInvoice electronicInvoice = nodeRedClient.applyElectronicInvoice(hospital.getCode(), param);
        HisElectronicInvoice hisElectronicInvoice = new HisElectronicInvoice();
        hisElectronicInvoice.setPatient_id(patient.getId() + "");
        hisElectronicInvoice.setElec_invoice_no(electronicInvoice.getElec_invoice_no());
        hisElectronicInvoiceRepository.save(hisElectronicInvoice);
        return electronicInvoice;
    }

    @Override
    public ElectronicInvoiceFile getElectronicInvoiceFile(Hospital hospital, Patient patient, String eleInvoiceNo) {
        ElectronicInvoiceFile file = nodeRedClient.getElectronicInvoiceFile(hospital.getCode(), patient.getHisPatid(), eleInvoiceNo);
        if (file == null) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("电子发票不存在");
        }
        List<Specification<HisElectronicInvoice>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("patient_id", patient.getId()));
        specs.add(Specifications.eq("elec_invoice_no", eleInvoiceNo));
        List<HisElectronicInvoice> list = hisElectronicInvoiceRepository.findAll(Specifications.and(specs));
        HisElectronicInvoice hisElectronicInvoice;
        if (CollectionUtils.isNotEmpty(list)) {
            hisElectronicInvoice = list.get(0);
        } else {
            hisElectronicInvoice = new HisElectronicInvoice();
            hisElectronicInvoice.setPatient_id(patient.getId() + "");
            hisElectronicInvoice.setElec_invoice_no(eleInvoiceNo);
        }
        hisElectronicInvoice.setElec_invoice_file(file.getElec_invoice_file());
        hisElectronicInvoice.setInvoicing_no(file.getInvoicing_no());
        hisElectronicInvoice.setInvoicing_code(file.getInvoicing_code());
        hisElectronicInvoice.setInvoicing_amount(file.getInvoicing_amount());
        hisElectronicInvoice.setInvoicing_date(file.getInvoicing_date());
        hisElectronicInvoice.setInvoicing_entity(file.getInvoicing_entity());
        hisElectronicInvoiceRepository.save(hisElectronicInvoice);
        return file;
    }

    @Override
    public Page<ElectronicInvoiceItem> getElectronicInvoiceList(Hospital hospital, String hisPatid, String online_type, String page, String size) {
        List<ElectronicInvoiceItem> list = nodeRedClient.getElectronicInvoiceList(hospital.getCode(), hisPatid, online_type, page, size);
        // Visit_time倒叙排序
//        List<ElectronicInvoiceItem> list = nodeRedClient.getElectronicInvoiceList(hospital.getCode(), hisPatid, online_type, page, size)
//                .stream()
//                .sorted(Comparator.nullsLast(Comparator.comparing(ElectronicInvoiceItem::getVisit_time).reversed()))
//                .collect(Collectors.toList());
        return new PageImpl<>(list, PageRequest.of(Integer.parseInt(page), Integer.parseInt(size)), Integer.parseInt(size));
    }

    @Override
    public Page<DoctorSchedules> getDoctorSchedulesByPage(Hospital hospital, String deptId, String doctorName, String beginDate, String endDate, Integer page, Integer size) {
        List<DoctorSchedules> doctorSchedules = nodeRedClient.getDoctorSchedules(hospital.getCode(), deptId, doctorName, beginDate, endDate)
                .stream().sorted((s1, s2) -> Collator.getInstance(Locale.CHINA).compare(s1.getDoctor_name(), s2.getDoctor_name()))
                .collect(Collectors.toList());
        if (StringUtils.isNotBlank(doctorName)) {
            doctorSchedules = doctorSchedules.stream()
                    .filter(doctorSchedule -> doctorName.equalsIgnoreCase(doctorSchedule.getDoctor_name()))
                    .collect(Collectors.toList());
        }
        return PageUtils.paginate(doctorSchedules, page, size);
    }

    @Override
    public List<PhysicalExaminationRecord> getPhysicalExaminationRecord(Hospital hospital, ElectronicMedicCard card,
                                                                        String beginDate, String endDate) {
        Patient patient = card.getPatient();
        return nodeRedClient.getPhysicalExaminationRecord(hospital.getCode(), beginDate, endDate,
                patient.getName(), patient.getIdCardNum(), patient.getMobile());
    }

    @Override
    public List<PhysicalExaminationReportFile> getPhysicalExaminationReportFile(Hospital hospital, String patientName, String tjbh) {
        return nodeRedClient.getPhysicalExaminationReportFile(hospital.getCode(), patientName, tjbh);
    }

    @Override
    public OutpatientPayResult getOutpatientPayResult(Hospital hospital, String settleId, String tradeType, String tradeNo) {
        return nodeRedClient.getOutpatientPayResult(hospital.getCode(), settleId, tradeType, tradeNo);
    }

    @Override
    public void sendRefundResultToHis(ConfirmRefund confirmRefund) {
        log.info("退款成功通知HIS: {}", JSON.toJSONString(confirmRefund));
        nodeRedClient.sendRefundResult(confirmRefund);
    }

    @Override
    public ApplyElectricInvoiceForHuLiResult applyElectricInvoiceForHuLi(Order order, OrderNursingExt ext) {
        log.info("护理到家申请电子发票 orderId: {}", order.getId());
        ApplyElectricInvoiceForHuLiReq req = new ApplyElectricInvoiceForHuLiReq();
        ElectronicMedicCard electronicMedicCard = order.getElectronicMedicCard();
        if (electronicMedicCard == null) {
            log.error("护理到家申请电子发票 orderId: {} 就诊卡为空!", order.getId());
            return new ApplyElectricInvoiceForHuLiResult();
        }
        req.setPatid(electronicMedicCard.getHisPatid());
        req.setPatname(order.getPatient().getName());
        req.setCardno(electronicMedicCard.getNumber());
        req.setCardtype(electronicMedicCard.getCardType().getCode());
        req.setRecipe_time(TimeUtils.dateToString(order.getCreatedDate(),"yyyyMMddHHmmss"));

        MedicalWorker nurse = order.getNurses().get(0).getNurse();
        Dept dept = nurse.getDeptMedicalWorkers().get(0).getDept();
        req.setRecipe_dept_id(dept.getDeptCode());
        req.setRecipe_dept_name(dept.getDeptName());
        req.setRecipe_doctor_id(nurse.getEmployeeNo());
        req.setRecipe_doctor_name(nurse.getUser().getFullName());
        req.setSerial_no(order.getId() + "");
        // 支付流水号如果有多次支付，拼起来传给His
        if (order.getPaymentMethod() == PaymentMethod.WECHAT ) {
            List<WechatOrder> wechatOrders = wechatOrderRepository.findAllByWechatOrderTypeAndProductId(
                NURSING_HOME,
                order.getId().toString());
            wechatOrders.addAll(wechatOrderRepository.findAllByWechatOrderTypeAndProductId(ThirdOrderType.NURSING_HOME_APPEND, order.getId().toString()));
            req.setPay_time(TimeUtils.dateToString(wechatOrders.get(0).getPayTime(), "yyyyMMddHHmmss"));
            req.setTrade_no(wechatOrders.stream().map(WechatOrder::getTransactionId).collect(Collectors.joining(",")));
        }
        req.setPay_amount(ext.getTotalAmount());
        req.setTraffic_amount(ext.getTrafficAmount());
        // 2024年9月25日14:38:35 目前只有微信小程序支付
        req.setPay_type("1");
        List<NursingChargeItemPrice> realChargeItems = ext.getRealChargeItems();

        List<ThirdAddAccountItem> items = realChargeItems.stream().map(u -> {
            ThirdAddAccountItem item = new ThirdAddAccountItem();
            item.setItem_code(u.getCode());
            item.setItem_name(u.getName());
            item.setPrice(u.getPrice());
            item.setQuantity(u.getCount());
            item.setAmount(u.getPrice() * u.getCount());
            req.setItem_amount(req.getItem_amount() + item.getAmount());
            return item;
        }).collect(Collectors.toList());
        req.setItem_infos(items);
        return nodeRedClient.applyElectricInvoiceForHuLi(order.getHospital().getCode(), req);
    }

    @Override
    public List<PatientMedicalRecord> patientMedicalRecords(Hospital hospital, String patId, String beginDate,
                                                            String endDate) {
        return nodeRedClient.patientMedicalRecords(hospital.getCode(), patId, beginDate, endDate);
    }

    @Override
    public SyncInspectMRResult syncInspectMR(OfflineOrder order, boolean agreed) {
        return nodeRedClient.syncInspectMR(order.getHospital().getCode(), order.getElectronicMedicCard().getHisPatid(),
                                           order.getRegNo(), agreed);
    }
}
