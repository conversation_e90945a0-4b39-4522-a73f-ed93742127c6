package cn.taihealth.ih.service.impl.filter.offline.medicalworker;

import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

/**
 */
public class OfflineMedicalWorkerTitleFilter implements SearchFilter<OfflineMedicalWorker> {

    private final String titleCode;

    public OfflineMedicalWorkerTitleFilter(String titleCode) {
        this.titleCode = titleCode;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OfflineMedicalWorker> toSpecification() {
        return Specifications.eq("title", titleCode);
    }

    @Override
    public String toExpression() {
        return "title:" + titleCode;
    }

    @Override
    public boolean isValid() {
        return titleCode != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof OfflineMedicalWorkerTitleFilter)) {
            return false;
        }

        OfflineMedicalWorkerTitleFilter rhs = (OfflineMedicalWorkerTitleFilter) other;
        return Objects.equals(titleCode, rhs.titleCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(titleCode);
    }
}
