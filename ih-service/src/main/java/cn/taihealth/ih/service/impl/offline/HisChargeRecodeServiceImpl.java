package cn.taihealth.ih.service.impl.offline;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.his.HisChargeRecord;
import cn.taihealth.ih.domain.his.HisInpatientHospitalCharge;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.OrderExtraInfo;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.repo.his.HisChargeRecordRepository;
import cn.taihealth.ih.service.api.OrderExtraInfoService;
import cn.taihealth.ih.service.api.offline.HisChargeRecordService;
import cn.taihealth.ih.service.vm.nodered.NodeRedResponseData;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.ConfirmChargeResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.ConfirmRegistResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.InpatientHospCardChargeResult;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class HisChargeRecodeServiceImpl implements HisChargeRecordService {

    private final HisChargeRecordRepository hisChargeRecordRepository;

    public HisChargeRecodeServiceImpl(HisChargeRecordRepository hisChargeRecordRepository) {
        this.hisChargeRecordRepository = hisChargeRecordRepository;
    }

    @Async
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveOutpatientConfirmChargeRecord(HisOutpatientCharge outpatientCharge, NodeRedResponseData<ConfirmChargeResult> nodeResult, String transactionId) {
        HisChargeRecord record = new HisChargeRecord();
        record.setHospitalId(outpatientCharge.getHospitalId());
        record.setProductTableName(HisOutpatientCharge.TABLE_NAME);
        record.setProductId(outpatientCharge.getId());
        record.setSettleId(outpatientCharge.getSettleId());
        record.setAmount(outpatientCharge.getAmount());
        record.setTransactionId(transactionId);
        if (nodeResult == null || nodeResult.getContent() == null || !nodeResult.getContent().isSuccess()) {
            record.setSuccess(false);
        } else {
            record.setSuccess(true);
        }
        record.setChargeBody(nodeResult == null ? null : nodeResult.getRequestBody());
        record.setChargeResult(nodeResult == null ? null : nodeResult.getResponseBody());
        hisChargeRecordRepository.save(record);
    }

    @Override
    public HisChargeRecord getOutpatientConfirmChargeRecord(HisOutpatientCharge outpatientCharge) {
        List<Specification<HisChargeRecord>> sps = Lists.newArrayList();
        sps.add(Specifications.eq("productTableName", HisOutpatientCharge.TABLE_NAME));
        sps.add(Specifications.eq("hospitalId", outpatientCharge.getHospitalId()));
        sps.add(Specifications.eq("productId", outpatientCharge.getId()));
        sps.add(Specifications.eq("settleId", outpatientCharge.getSettleId()));
        List<HisChargeRecord> records = hisChargeRecordRepository.findAll(Specifications.and(sps));
        HisChargeRecord successRecord = records.stream().filter(HisChargeRecord::isSuccess).findFirst().orElse(null);
        if (successRecord != null) {
            return successRecord;
        }
        return records.stream().max(Comparator.comparing(HisChargeRecord::getCreatedDate)).orElse(null);
    }


    @Async
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveRegisterChargeConfirmChargeRecord(OfflineOrder registerCharge, NodeRedResponseData<ConfirmRegistResult> nodeResult, String transactionId) {
        HisChargeRecord record = new HisChargeRecord();
        record.setHospitalId(registerCharge.getHospital().getId());
        record.setProductTableName(OfflineOrder.TABLE_NAME);
        record.setProductId(registerCharge.getId());
        record.setSettleId(registerCharge.getSettleId());
        record.setAmount(registerCharge.getRegistrationFee());
        record.setTransactionId(transactionId);
        if (nodeResult == null || nodeResult.getContent() == null || !nodeResult.getContent().isSuccess()) {
            record.setSuccess(false);
        } else {
            record.setSuccess(true);
        }
        record.setChargeBody(nodeResult == null ? null : nodeResult.getRequestBody());
        record.setChargeResult(nodeResult == null ? null : nodeResult.getResponseBody());
        hisChargeRecordRepository.save(record);
    }

    @Override
    public HisChargeRecord getRegisterChargeConfirmChargeRecord(OfflineOrder registerCharge) {
        List<Specification<HisChargeRecord>> sps = Lists.newArrayList();
        sps.add(Specifications.eq("productTableName", OfflineOrder.TABLE_NAME));
        sps.add(Specifications.eq("hospitalId", registerCharge.getHospital().getId()));
        sps.add(Specifications.eq("productId", registerCharge.getId()));
        if (StringUtils.isNotBlank(registerCharge.getSettleId())) {
            sps.add(Specifications.eq("settleId", registerCharge.getSettleId()));
        }
        List<HisChargeRecord> records = hisChargeRecordRepository.findAll(Specifications.and(sps));
        HisChargeRecord successRecord = records.stream().filter(HisChargeRecord::isSuccess).findFirst().orElse(null);
        if (successRecord != null) {
            return successRecord;
        }
        return records.stream().max(Comparator.comparing(HisChargeRecord::getCreatedDate)).orElse(null);
    }

    @Async
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveOnlineOutpatientConfirmChargeRecord(Hospital hospital, long orderId, NodeRedResponseData<ConfirmRegistResult> nodeResult, String transactionId) {
        OrderExtraInfo orderExtraInfo = AppContext.getInstance(OrderExtraInfoService.class).getOrderExtraInfo(orderId);
        HisChargeRecord record = new HisChargeRecord();
        record.setHospitalId(hospital.getId());
        record.setProductTableName(Order.TABLE_NAME);
        record.setProductId(orderId);
        record.setSettleId(orderExtraInfo == null ? null : orderExtraInfo.getSettleId());
        record.setAmount(orderExtraInfo == null ? null : orderExtraInfo.getTotalAmount());
        record.setTransactionId(transactionId);
        if (nodeResult == null || nodeResult.getContent() == null || !nodeResult.getContent().isSuccess()) {
            record.setSuccess(false);
        } else {
            record.setSuccess(true);
        }
        record.setChargeBody(nodeResult == null ? null : nodeResult.getRequestBody());
        record.setChargeResult(nodeResult == null ? null : nodeResult.getResponseBody());
        hisChargeRecordRepository.save(record);
    }

    @Override
    public HisChargeRecord getOnlineRegisterChargeConfirmChargeRecord(Order order) {
        OrderExtraInfo orderExtraInfo = AppContext.getInstance(OrderExtraInfoService.class).getOrderExtraInfo(order.getId());
        List<Specification<HisChargeRecord>> sps = Lists.newArrayList();
        sps.add(Specifications.eq("productTableName", Order.TABLE_NAME));
        sps.add(Specifications.eq("hospitalId", order.getHospital().getId()));
        sps.add(Specifications.eq("productId", order.getId()));
        sps.add(Specifications.eq("settleId", orderExtraInfo.getSettleId()));
        List<HisChargeRecord> records = hisChargeRecordRepository.findAll(Specifications.and(sps));
        HisChargeRecord successRecord = records.stream().filter(HisChargeRecord::isSuccess).findFirst().orElse(null);
        if (successRecord != null) {
            return successRecord;
        }
        return records.stream().max(Comparator.comparing(HisChargeRecord::getCreatedDate)).orElse(null);
    }

    @Async
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveInpatientChargeConfirmRecord(HisInpatientHospitalCharge charge, NodeRedResponseData<InpatientHospCardChargeResult> nodeResult, String transactionId) {
        HisChargeRecord record = new HisChargeRecord();
        record.setHospitalId(Long.valueOf(charge.getHospital_id()));
        record.setProductTableName(HisInpatientHospitalCharge.TABLE_NAME);
        record.setProductId(charge.getId());
        record.setSettleId(charge.getSerial_no());
        record.setAmount(Integer.valueOf(charge.getSelf_amount()));
        record.setTransactionId(transactionId);
        if (nodeResult == null || nodeResult.getContent() == null || !nodeResult.getContent().isSuccess()) {
            record.setSuccess(false);
        } else {
            record.setSuccess(true);
        }
        record.setChargeBody(nodeResult == null ? null : nodeResult.getRequestBody());
        record.setChargeResult(nodeResult == null ? null : nodeResult.getResponseBody());
        hisChargeRecordRepository.save(record);
    }

    @Override
    public HisChargeRecord getInpatientChargeConfirmRecord(HisInpatientHospitalCharge charge) {
        List<Specification<HisChargeRecord>> sps = Lists.newArrayList();
        sps.add(Specifications.eq("productTableName", HisInpatientHospitalCharge.TABLE_NAME));
        sps.add(Specifications.eq("hospitalId", charge.getHospital_id()));
        sps.add(Specifications.eq("productId", charge.getId()));
        sps.add(Specifications.eq("settleId", charge.getSerial_no()));
        List<HisChargeRecord> records = hisChargeRecordRepository.findAll(Specifications.and(sps));
        HisChargeRecord successRecord = records.stream().filter(HisChargeRecord::isSuccess).findFirst().orElse(null);
        if (successRecord != null) {
            return successRecord;
        }
        return records.stream().max(Comparator.comparing(HisChargeRecord::getCreatedDate)).orElse(null);
    }

    @Async
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void savePrescriptionOrderConfirmChargeRecord(Hospital hospital, PrescriptionOrder prescriptionOrder, int totalAmount, NodeRedResponseData<ConfirmChargeResult> nodeResult, String transactionId) {
        OrderExtraInfo orderExtraInfo = AppContext.getInstance(OrderExtraInfoService.class).getOrderExtraInfo(prescriptionOrder.getOrder().getId());
        HisChargeRecord record = new HisChargeRecord();
        record.setHospitalId(hospital.getId());
        record.setProductTableName(PrescriptionOrder.TABLE_NAME);
        record.setProductId(prescriptionOrder.getId());
        record.setSettleId(orderExtraInfo == null ? null : orderExtraInfo.getHisRecipeSettleId());
        record.setAmount(totalAmount);
        record.setTransactionId(transactionId);
        if (nodeResult == null || nodeResult.getContent() == null || !nodeResult.getContent().isSuccess()) {
            record.setSuccess(false);
        } else {
            String confirmSettleId = nodeResult.getContent().getSettle_id();
            if (StringUtils.isNotBlank(confirmSettleId) && !Objects.equals(record.getSettleId(), confirmSettleId)) {
                log.info("线上处方缴费结算settleId和门诊缴费预算settleId不同, 结算settleId=" + confirmSettleId + ", 预算settleId=" + record.getSettleId());
                record.setSettleId(confirmSettleId);
            }
            record.setSuccess(true);
        }
        record.setChargeBody(nodeResult == null ? null : nodeResult.getRequestBody());
        record.setChargeResult(nodeResult == null ? null : nodeResult.getResponseBody());
        hisChargeRecordRepository.save(record);
    }

    @Override
    public HisChargeRecord getPrescriptionOrderConfirmChargeRecord(Hospital hospital, PrescriptionOrder prescriptionOrder) {
        OrderExtraInfo orderExtraInfo = AppContext.getInstance(OrderExtraInfoService.class).getOrderExtraInfo(prescriptionOrder.getOrder().getId());
        List<Specification<HisChargeRecord>> sps = Lists.newArrayList();
        sps.add(Specifications.eq("productTableName", PrescriptionOrder.TABLE_NAME));
        sps.add(Specifications.eq("hospitalId", hospital.getId()));
        sps.add(Specifications.eq("productId", prescriptionOrder.getId()));
        sps.add(Specifications.eq("settleId", orderExtraInfo.getHisRecipeSettleId()));
        List<HisChargeRecord> records = hisChargeRecordRepository.findAll(Specifications.and(sps));
        HisChargeRecord successRecord = records.stream().filter(HisChargeRecord::isSuccess).findFirst().orElse(null);
        if (successRecord != null) {
            return successRecord;
        }
        return records.stream().max(Comparator.comparing(HisChargeRecord::getCreatedDate)).orElse(null);
    }

    @Override
    public HisChargeRecord getChargeRecord(Hospital hospital, String transactionId) {
        List<Specification<HisChargeRecord>> sps = Lists.newArrayList();
        sps.add(Specifications.eq("hospitalId", hospital.getId()));
        sps.add(Specifications.eq("transactionId", transactionId));
        List<HisChargeRecord> records = hisChargeRecordRepository.findAll(Specifications.and(sps));
        HisChargeRecord successRecord = records.stream().filter(HisChargeRecord::isSuccess).findFirst().orElse(null);
        if (successRecord != null) {
            return successRecord;
        }
        return records.stream().max(Comparator.comparing(HisChargeRecord::getCreatedDate)).orElse(null);
    }


}
