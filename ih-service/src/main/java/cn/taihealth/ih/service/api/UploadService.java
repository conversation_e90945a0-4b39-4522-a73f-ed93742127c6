package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.impl.store.FileStore;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.List;

/**
 */
public interface UploadService {

    FileStore getFileStore();

    String getUploadCallbackUrl();

    Upload upload(User user, UploadResource resource);

    Upload uploadByName(User user, UploadResource resource);

    void deleteUpload(Upload upload);

    void deleteAllByUser(User user);

    byte[] read(Upload upload);

    InputStream openStream(Upload upload);

    void read(HttpServletResponse response, Upload upload, long start);

    long getMaxFileSizeKB();

    List<String> getAllowedExtensions();

    boolean isFileAllowed(String fileName);

    /**
     * 从upload下载文件
     * @param upload
     * @param file
     */
    void download(Upload upload, File file);

}
