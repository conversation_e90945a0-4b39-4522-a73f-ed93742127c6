package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.enums.TimeZone;
import cn.taihealth.ih.domain.hospital.DoctorStopDiagnosis;
import io.swagger.annotations.ApiParam;
import java.util.Calendar;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DoctorStopDiagnosisDTO extends UpdatableDTO implements Comparable<DoctorStopDiagnosisDTO> {

    @ApiParam(name = "停诊日期")
    @NotNull
    private Date dateTime;

    @ApiParam(name = "上午/下午")
    @NotNull
    private TimeZone timeZone = TimeZone.AM;

    @ApiParam(name = "创建人")
    private UserDTO creator;

    public DoctorStopDiagnosisDTO(DoctorStopDiagnosis doctorStopDiagnosis) {
        super(doctorStopDiagnosis);
        this.dateTime = doctorStopDiagnosis.getDateTime();
        this.timeZone = doctorStopDiagnosis.getTimeZone();
        if (doctorStopDiagnosis.getCreator() != null) {
            this.creator = new UserDTO(doctorStopDiagnosis.getCreator());
        }
    }

    @Override
    public int compareTo(DoctorStopDiagnosisDTO o) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(this.dateTime);
        // 将时分秒,毫秒域清零
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(o.dateTime);
        // 将时分秒,毫秒域清零
        calendar1.set(Calendar.HOUR_OF_DAY, 0);
        calendar1.set(Calendar.MINUTE, 0);
        calendar1.set(Calendar.SECOND, 0);
        calendar1.set(Calendar.MILLISECOND, 0);

        int i = (int) ((calendar.getTime().getTime() - calendar1.getTime().getTime()) / 1000);
        if (i == 0) {
            return this.timeZone.compareTo(o.getTimeZone());
        }
        return i;
    }
}
