package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.Ticket;
import cn.taihealth.ih.domain.TicketAttachment;
import cn.taihealth.ih.domain.dict.SuggestTypeConvert;
import cn.taihealth.ih.repo.TicketAttachmentRepository;
import cn.taihealth.ih.service.vm.UploadVM;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 意见反馈，留言咨询
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class TicketDTO extends UpdatableDTO {

    @ApiModelProperty(value = "是否需要回复", example = "0-否;1-是")
    private int isNeedResponse = 1;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("电话")
    private String telephone;

    @ApiModelProperty("建议类型")
    private List<String> suggestTypes;

    @ApiModelProperty("建议类型编码-单个-临时使用")
    private String suggestTypeCode;

    @ApiModelProperty("问题和意见")
    private String content;

    @ApiModelProperty("上传的图片")
    private List<UploadVM> attachments = Lists.newArrayList();

    @ApiModelProperty(value = "回复状态", example = "0-待回复;1-已回复")
    private int responseType = 0;

    @ApiModelProperty("医院回复")
    private String responseContent;

    @ApiModelProperty(value = "区分是意见建议,就医咨询", example = "FEEDBACK：意见建议;LEAVE_MESSAGE：就医咨询")
    private Ticket.Type type = Ticket.Type.FEEDBACK ;

    public TicketDTO(Ticket ticket) {
        super(ticket);
        this.content = ticket.getContent();
        this.setName(ticket.getName());
        this.setTelephone(ticket.getTelephone());
        this.setIsNeedResponse(ticket.getIsNeedResponse() == null ? 0 : ticket.getIsNeedResponse());
        this.setResponseContent(ticket.getResponseContent());
        this.setResponseType(ticket.getResponseType() == null ? 0 : ticket.getResponseType());
        this.setSuggestTypes(SuggestTypeConvert.toList(ticket.getSuggestTypes()));
        this.setType(ticket.getType());
        if (!ticket.isNew()) {
            this.attachments = AppContext.getInstance(TicketAttachmentRepository.class)
                    .findAllByTicket(ticket)
                    .stream()
                    .map(TicketAttachment::getUpload)
                    .map(UploadVM::new)
                    .collect(Collectors.toList());
        }
    }
}
