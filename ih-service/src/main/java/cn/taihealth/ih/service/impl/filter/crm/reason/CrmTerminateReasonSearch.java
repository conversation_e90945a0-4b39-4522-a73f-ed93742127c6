package cn.taihealth.ih.service.impl.filter.crm.reason;

import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.domain.crm.CrmTerminateReason;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class CrmTerminateReasonSearch extends SearchCriteria<CrmTerminateReason> {

    public static CrmTerminateReasonSearch of(String query) {
        CrmTerminateReasonSearch articleSearch = new CrmTerminateReasonSearch();
        articleSearch.parse(query);
        return articleSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<CrmTerminateReason> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }

        switch (qualifier) {
            case SELECTED:
                return new SelectedFilter(StringUtil.stringToBoolean(value));
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<CrmTerminateReason> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        SELECTED,
    }
}

