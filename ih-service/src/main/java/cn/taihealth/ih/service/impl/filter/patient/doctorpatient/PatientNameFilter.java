package cn.taihealth.ih.service.impl.filter.patient.doctorpatient;

import cn.taihealth.ih.domain.PatientCategory;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.hospital.Category;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Objects;
import javax.persistence.criteria.Join;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class PatientNameFilter implements SearchFilter<Patient> {

    private final String name;

    public PatientNameFilter(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Patient> toSpecification() {
        return Specifications.like("name", name);
    }

    @Override
    public String toExpression() {
        return name;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof PatientNameFilter)) {
            return false;
        }

        PatientNameFilter rhs = (PatientNameFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
