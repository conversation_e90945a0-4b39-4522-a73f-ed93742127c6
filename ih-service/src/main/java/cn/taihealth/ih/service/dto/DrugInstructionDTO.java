package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.DrugInstruction;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 药品说明书
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class DrugInstructionDTO extends UpdatableDTO {

    @ApiModelProperty(value = "是否删除", example = "0-否;1-是")
    private Boolean isDeleted;

    @ApiModelProperty("药品名称")
    private String name;

    @ApiModelProperty("药品标准编码")
    private String code;

    @ApiModelProperty("医院信息")
    private HospitalDTO hospital;

    @ApiModelProperty("上传的图片")
    private UploadDTO picture;

    @ApiModelProperty("详情")
    private String detail;

    public DrugInstructionDTO(DrugInstruction drugInstruction) {
        super(drugInstruction);
        this.isDeleted = drugInstruction.isDeleted();
        this.setName(drugInstruction.getName());
        this.setCode(drugInstruction.getCode());
        this.setDetail(drugInstruction.getDetail());

        UploadDTO uploadDTO = new UploadDTO(drugInstruction.getPicture());
        this.setPicture(uploadDTO);

        this.hospital = new HospitalDTO(drugInstruction.getHospital());

    }
}
