package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.dict.ExamCategory;
import cn.taihealth.ih.domain.dict.ExamItem;
import cn.taihealth.ih.service.dto.ExamCategoryDTO;
import cn.taihealth.ih.service.dto.ExamItemDTO;

/**
 * @Author: Moon
 * @Date: 2020/12/16 下午1:54
 */
public interface ExamDictService {

    /**
     * 添加检查类别
     * @param hospital 医院
     * @param dto 数据对象
     * @return 添加后的类别
     */
    ExamCategory addCategory(Hospital hospital, ExamCategoryDTO dto);

    /**
     * 添加或修改项目
     * @param hospital 医院
     * @param category 类别
     * @param item 项目
     * @param dto 数据
     * @return 项目
     */
    ExamItem addOrUpdateItem(Hospital hospital, ExamCategory category, ExamItem item, ExamItemDTO dto);

    /**
     * 修改项目时间段
     * @param item
     * @param dto
     * @return
     */
    ExamItem addOrUpdateItemTimes(ExamItem item, ExamItemDTO dto);

    /**
     * 批量上传检查项目和检查类别
     * @param hospital 医院
     * @param uploadId 文件id
     */
    void uploadData(Hospital hospital, Long uploadId);
}
