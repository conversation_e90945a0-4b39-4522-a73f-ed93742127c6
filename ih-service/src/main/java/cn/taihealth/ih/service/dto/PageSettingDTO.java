package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.dict.PageSetting;
import cn.taihealth.ih.domain.dict.PageSetting.Group;
import cn.taihealth.ih.domain.dict.PageSetting.IconSize;
import cn.taihealth.ih.domain.dict.PageSetting.Style;
import cn.taihealth.ih.domain.dict.PageSetting.Type;
import cn.taihealth.ih.domain.enums.MenuType;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> moon
 */
public class PageSettingDTO extends UpdatableDTO {

    @ApiModelProperty("模块标识")
    private String code;

    @ApiModelProperty("名称-显示文本")
    private String name;

    @ApiModelProperty("图标url")
    private String icon;

    @ApiModelProperty("线性图标url")
    private String listIcon;

    @ApiModelProperty("选中时图标url")
    private String selectedIcon;

    @ApiModelProperty("路由url")
    private String url;

    @ApiModelProperty("等级")
    private Integer level;

    @ApiModelProperty("链接")
    private String moreUrl;

    @ApiModelProperty("是否需要先有就诊人")
    private Boolean needPatient;

    @ApiModelProperty("排序")
    private Integer orderNumber;

    @ApiModelProperty("模块样式")
    @NotNull(message = "模块样式不能为空")
    private Style style;

    @ApiModelProperty("面对用户的类型")
    @NotNull(message = "面对用户的类型不能为空")
    private Type type;

    @ApiModelProperty("图标大小")
    private PageSetting.IconSize iconSize;

    @ApiModelProperty("标签")
    @NotNull(message = "标签")
    private String tag;

    @ApiModelProperty("分组")
    @NotNull(message = "分组")
    private Group group;

    @ApiModelProperty("菜单类型")
    private MenuType menuType;

    @ApiModelProperty("备注说明")
    private String remark;

    private List<PageSettingDTO> pageSettings = Lists.newArrayList();

    public PageSettingDTO() {
    }

    public PageSettingDTO(PageSetting pageSetting) {
        super(pageSetting);
        this.code = pageSetting.getCode();
        this.name = pageSetting.getName();
        this.icon = pageSetting.getIcon();
        this.listIcon = pageSetting.getListIcon();
        this.selectedIcon = pageSetting.getSelectedIcon();
        this.moreUrl = pageSetting.getMoreUrl();
        this.url = pageSetting.getUrl();
        this.orderNumber = pageSetting.getOrderNumber();
        this.level = pageSetting.getLevel();
        this.needPatient = pageSetting.getNeedPatient();
        this.style = pageSetting.getStyle();
        this.type = pageSetting.getType();
        this.group = pageSetting.getGroup();
        this.iconSize = pageSetting.getIconSize();
        this.menuType = pageSetting.getMenuType();
        this.pageSettings = pageSetting.getPageSettings().stream().map(PageSettingDTO::new)
            .collect(Collectors.toList());
        this.remark = pageSetting.getRemark();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<PageSettingDTO> getPageSettings() {
        return pageSettings;
    }

    public void setPageSettings(List<PageSettingDTO> pageSettings) {
        this.pageSettings = pageSettings;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getListIcon() {
        return listIcon;
    }

    public void setListIcon(String listIcon) {
        this.listIcon = listIcon;
    }

    public String getSelectedIcon() {
        return selectedIcon;
    }

    public void setSelectedIcon(String selectedIcon) {
        this.selectedIcon = selectedIcon;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getMoreUrl() {
        return moreUrl;
    }

    public void setMoreUrl(String moreUrl) {
        this.moreUrl = moreUrl;
    }

    public Boolean getNeedPatient() {
        return needPatient;
    }

    public void setNeedPatient(Boolean needPatient) {
        this.needPatient = needPatient;
    }

    public Style getStyle() {
        return style;
    }

    public void setStyle(Style style) {
        this.style = style;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Group getGroup() {
        return group;
    }

    public void setGroup(Group group) {
        this.group = group;
    }

    public IconSize getIconSize() {
        return iconSize;
    }

    public void setIconSize(IconSize iconSize) {
        this.iconSize = iconSize;
    }

    public MenuType getMenuType() {
        return menuType;
    }

    public void setMenuType(MenuType menuType) {
        this.menuType = menuType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
