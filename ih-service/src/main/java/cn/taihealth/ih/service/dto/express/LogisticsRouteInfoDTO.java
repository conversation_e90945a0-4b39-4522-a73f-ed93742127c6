package cn.taihealth.ih.service.dto.express;

import cn.taihealth.ih.service.dto.express.kuaidi100.Kuaidi100LogisticsRouteInfo;
import cn.taihealth.ih.service.dto.express.shunfeng.SHUNFENGExpressRouteInfo;
import lombok.Data;

@Data
public class LogisticsRouteInfoDTO implements java.io.Serializable {

    /**
     * 时间 2021-12-15 17:19:28
     */
    private String time;
    /**
     * 内容 【杭州市】您的包裹已存放至【驿站】，记得早点来取它回家！
     */
    private String context;

    /**
     * 路由操作码 80:签收
     */
    private String opcode;

    public LogisticsRouteInfoDTO(SHUNFENGExpressRouteInfo info) {
        this.time = info.getAcceptTotaltime();
        this.context = "【" + info.getAcceptAddress() + "】" + info.getRemark();
        this.opcode = info.getOpcode();
    }

    public LogisticsRouteInfoDTO(Kuaidi100LogisticsRouteInfo info) {
        this.time = info.getTime();
        this.context = info.getContext();
    }

}
