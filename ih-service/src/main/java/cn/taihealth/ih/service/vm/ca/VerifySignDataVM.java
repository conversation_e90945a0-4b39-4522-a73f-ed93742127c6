package cn.taihealth.ih.service.vm.ca;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class VerifySignDataVM {

    @ApiModelProperty(value = "公钥证书base64编码")
    @NotNull
    private String cert;

    @ApiModelProperty(value = "签名原文")
    @NotNull
    private String inData;

    @ApiModelProperty(value = "Base64编码签名值")
    @NotNull
    private String signData;

}
