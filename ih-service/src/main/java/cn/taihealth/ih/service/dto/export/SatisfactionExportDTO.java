package cn.taihealth.ih.service.dto.export;

import cn.taihealth.ih.commons.util.MathUtils;
import cn.taihealth.ih.service.vm.hospital.HospitalVM;
import cn.taihealth.ih.service.vm.statistics.supervision.MoreTrafficRatioStats;
import cn.taihealth.ih.service.vm.statistics.supervision.MoreTrafficStats;
import cn.taihealth.ih.service.vm.statistics.supervision.SatisfactionPerformance;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.text.DecimalFormat;

@Data
@AllArgsConstructor
public class SatisfactionExportDTO {

    @ApiModelProperty("医院")
    private String hospital;

    @ApiModelProperty("科室")
    private String deptName;

    @ApiModelProperty("出院总人数")
    private int dischargedCount;

    @ApiModelProperty("随访总数")
    private int surveyCount;

    @ApiModelProperty("随访率")
    private String surveyRate;

    @ApiModelProperty("成功总数")
    private int successedCount;

    @ApiModelProperty("成功率")
    private String successedRate;

    @ApiModelProperty("占线总数")
    private int zxCount;

    @ApiModelProperty("放弃总数")
    private int fqCount;

    @ApiModelProperty("停机总数")
    private int tjCount;

    @ApiModelProperty("拒绝总数")
    private int jjCount;

    @ApiModelProperty("空错号总数")
    private int kchCount;

    @ApiModelProperty("无法接通总数")
    private int wfjtCount;

    @ApiModelProperty("无人接听总数")
    private int wrjtCount;

    private int order;

    public SatisfactionExportDTO(SatisfactionPerformance performance, int order) {
        this.hospital = performance.getHospital().getName();
        this.deptName = performance.getDeptName();
        this.dischargedCount = performance.getDischargedCount();
        this.surveyCount = performance.getSurveyCount();
        this.surveyRate = MathUtils.getRateByDouble(performance.getSurveyRate()); //
        this.successedCount = performance.getSuccessedCount();
        this.successedRate = MathUtils.getRateByDouble(performance.getSuccessedRate()); //
        this.zxCount = performance.getZxCount();
        this.fqCount = performance.getFqCount();
        this.tjCount = performance.getTjCount();
        this.jjCount = performance.getJjCount();
        this.kchCount = performance.getKchCount();
        this.wfjtCount = performance.getWfjtCount();
        this.wrjtCount = performance.getWrjtCount();
        this.order = order;
    }
}
