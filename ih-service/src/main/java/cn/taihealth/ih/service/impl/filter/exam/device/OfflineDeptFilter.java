package cn.taihealth.ih.service.impl.filter.exam.device;

import cn.taihealth.ih.domain.dict.ExamDevice;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 * <AUTHOR>
 */
public class OfflineDeptFilter implements SearchFilter<ExamDevice> {

    private final String pattern;

    public OfflineDeptFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<ExamDevice> toSpecification() {
        return Specifications.eq("offlineDept.id", pattern);

    }

    @Override
    public String toExpression() {
        return "dept:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof OfflineDeptFilter)) {
            return false;
        }

        OfflineDeptFilter rhs = (OfflineDeptFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
