package cn.taihealth.ih.service.impl.filter.hisbill;

import cn.taihealth.ih.domain.enums.BillSourceEnum;
import cn.taihealth.ih.domain.hospital.HisBill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class BillSourceFilter implements SearchFilter<HisBill> {
    private final BillSourceEnum sourceEnum;

    public BillSourceFilter(BillSourceEnum sourceEnum) {
        this.sourceEnum = sourceEnum;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<HisBill> toSpecification() {
        return Specifications.eq("billSource", sourceEnum);
    }

    @Override
    public String toExpression() {
        String str = " sourceEnum:" + sourceEnum;
        return str;
    }

    @Override
    public boolean isValid() {
        return sourceEnum != null;
    }
}
