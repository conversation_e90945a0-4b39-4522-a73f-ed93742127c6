package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.AbstractEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

/**
 */
@Setter
@Getter
public class AbstractEntityDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    public AbstractEntityDTO() {}

    public AbstractEntityDTO(AbstractEntity entity) {
        this.id = entity.getId();
    }

    @JsonIgnore
    public boolean isNew() {
        return id == null || id <= 0;
    }


    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        Class<?> thisClass = this.getClass();
        Class<?> thatClass = obj.getClass();

        if (thisClass != thatClass) {
            return false;
        }

        AbstractEntityDTO that = (AbstractEntityDTO) obj;
        return Objects.equals(this.getId(), that.getId());
    }

    /*
     * (non-Javadoc)
     *
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
