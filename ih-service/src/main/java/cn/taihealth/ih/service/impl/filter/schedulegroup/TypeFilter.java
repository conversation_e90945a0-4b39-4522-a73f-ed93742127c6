package cn.taihealth.ih.service.impl.filter.schedulegroup;

import cn.taihealth.ih.domain.hospital.Schedule.ScheduleType;
import cn.taihealth.ih.domain.hospital.ScheduleGroup;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 * @Author: Moon
 * @Date: 2021/1/28 上午9:33
 */
public class TypeFilter implements SearchFilter<ScheduleGroup> {

    private final String pattern;

    public TypeFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<ScheduleGroup> toSpecification() {
        List<Specification<ScheduleGroup>> specs = Lists.newArrayList();
        if (pattern.toUpperCase().contains("DEPT")) {
            specs.add(Specifications.eq("type", ScheduleType.NORMAL));
            specs.add(Specifications.isNull("consulItem"));
        }
        if (pattern.toUpperCase().contains("DOCTOR")) {
            specs.add(Specifications.ne("type", ScheduleType.NORMAL));
            specs.add(Specifications.isNull("consulItem"));
        }
        if (pattern.toUpperCase().contains("NURSING_CONSULTATION")) {
            specs.add(Specifications.isNotNull("consulItem"));
        }
        return Specifications.and(specs);
    }

    @Override
    public String toExpression() {
        return "type:" + pattern;
    }

    @Override
    public boolean isValid() {
        return pattern != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof TypeFilter)) {
            return false;
        }

        TypeFilter rhs = (TypeFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pattern);
    }
}
