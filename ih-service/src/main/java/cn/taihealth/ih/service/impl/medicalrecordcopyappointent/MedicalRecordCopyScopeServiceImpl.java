package cn.taihealth.ih.service.impl.medicalrecordcopyappointent;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.copyappointment.MedicalRecordCopyPurposeScopeRel;
import cn.taihealth.ih.domain.copyappointment.MedicalRecordCopyScope;
import cn.taihealth.ih.repo.medicalrecordcopyappointment.MedicalRecordCopyPurposeScopeRelRepository;
import cn.taihealth.ih.repo.medicalrecordcopyappointment.MedicalRecordCopyScopeRepository;
import cn.taihealth.ih.service.api.medicalrecordcooyappointment.MedicalRecordCopyScopeService;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.MedicalRecordCopyScopeDTO;
import cn.taihealth.ih.service.impl.filter.copyappointment.MedicalRecordCopyScopeSearch;
import com.gitq.jedi.data.specification.Specifications;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: jzs
 * @Date: 2023-10-18
 */
@Service
@AllArgsConstructor
@Slf4j
public class MedicalRecordCopyScopeServiceImpl implements MedicalRecordCopyScopeService {

    private final MedicalRecordCopyScopeRepository medicalRecordCopyScopeRepository;
    private final MedicalRecordCopyPurposeScopeRelRepository medicalRecordCopyPurposeScopeRelRepository;

    @Override
    public Page<MedicalRecordCopyScopeDTO> queryMedicalRecordCopyScopeList(Hospital hospital, String query, Pageable page) {
        List<Specification<MedicalRecordCopyScope>> specs = Lists.newArrayList();
        specs.add(MedicalRecordCopyScopeSearch.of(query).toSpecification());
        specs.add(Specifications.eq("hospital", hospital));
        return medicalRecordCopyScopeRepository.findAll(Specifications.and(specs), page).map(MedicalRecordCopyScopeDTO::new);
    }

    @Override
    @Transactional
    public MedicalRecordCopyScopeDTO createMedicalRecordCopyScope(Hospital hospital, MedicalRecordCopyScopeDTO param) {
        checkMedicalRecordCopyScope(hospital, param);
        MedicalRecordCopyScope medicalRecordCopyScope = medicalRecordCopyScopeRepository.save(param.toEntity(hospital));
        return new MedicalRecordCopyScopeDTO(medicalRecordCopyScope);
    }

    @Override
    @Transactional
    public MedicalRecordCopyScopeDTO updateMedicalRecordCopyScope(MedicalRecordCopyScopeDTO param) {
        MedicalRecordCopyScope medicalRecordCopyScope = medicalRecordCopyScopeRepository.findById(param.getId())
                .orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);
        // 2023/11/06 bug编号15775 产品确认 病案分类可以关闭但不可以编辑
        MedicalRecordCopyPurposeScopeRel medicalRecordCopyPurposeScopeRel = medicalRecordCopyPurposeScopeRelRepository
                .findFirstByCopyScopeId(param.getId()).orElse(null);
        if (medicalRecordCopyPurposeScopeRel != null && !checkMedicalRecordCopyScopeParam(param, medicalRecordCopyScope)
                && param.getEnabled()) {
            throw ErrorType.INVALID_CHOICE.toProblem("当前的病案分类正在被使用，无法进行编辑");
        }
        checkMedicalRecordCopyScope(medicalRecordCopyScope.getHospital(), param);
        medicalRecordCopyScope.setName(param.getName());
        medicalRecordCopyScope.setCode(param.getCode());
        medicalRecordCopyScope.setDescription(param.getDescription());
        medicalRecordCopyScope.setEnabled(param.getEnabled());
        return new MedicalRecordCopyScopeDTO(medicalRecordCopyScopeRepository.save(medicalRecordCopyScope));
    }

    public void checkMedicalRecordCopyScope(Hospital hospital, MedicalRecordCopyScopeDTO param) {
        Optional<MedicalRecordCopyScope> checkName;
        Optional<MedicalRecordCopyScope> checkCode;
        if (param.isNew()) {
            checkName = medicalRecordCopyScopeRepository.findFirstByHospitalAndName(hospital, param.getName());
            checkCode = medicalRecordCopyScopeRepository.findFirstByHospitalAndCode(hospital, param.getCode());
        } else {
            checkName = medicalRecordCopyScopeRepository.findFirstByHospitalAndNameAndIdNot(hospital, param.getName(), param.getId());
            checkCode = medicalRecordCopyScopeRepository.findFirstByHospitalAndCodeAndIdNot(hospital, param.getCode(), param.getId());
        }
        checkName.ifPresent(scope -> {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("分类名已存在");
        });
        checkCode.ifPresent(scope -> {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("分类代码已存在");
        });
    }

    public boolean checkMedicalRecordCopyScopeParam(MedicalRecordCopyScopeDTO medicalRecordCopyScopeDTO,
                                                    MedicalRecordCopyScope medicalRecordCopyScope) {
        return Objects.equals(medicalRecordCopyScopeDTO.getName(), medicalRecordCopyScope.getName())
                && Objects.equals(medicalRecordCopyScopeDTO.getCode(), medicalRecordCopyScope.getCode())
                && Objects.equals(medicalRecordCopyScopeDTO.getDescription(), medicalRecordCopyScope.getDescription());
    }

}
