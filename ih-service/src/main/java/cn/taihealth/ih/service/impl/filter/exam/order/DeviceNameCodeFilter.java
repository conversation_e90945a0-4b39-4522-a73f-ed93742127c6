package cn.taihealth.ih.service.impl.filter.exam.order;

import cn.taihealth.ih.domain.hospital.ExamOrder;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 * <AUTHOR>
 */
public class DeviceNameCodeFilter implements SearchFilter<ExamOrder> {

    private final String pattern;

    public DeviceNameCodeFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<ExamOrder> toSpecification() {
        List<Specification<ExamOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.likeIgnoreCase("service.device.code", pattern));
        specs.add(Specifications.likeIgnoreCase("service.device.name", pattern));
        return Specifications.or(specs);
    }

    @Override
    public String toExpression() {
        return "code/name:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof DeviceNameCodeFilter)) {
            return false;
        }

        DeviceNameCodeFilter rhs = (DeviceNameCodeFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
