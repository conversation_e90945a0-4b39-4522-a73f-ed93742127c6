package cn.taihealth.ih.service.impl.filter.order;

import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 */
public class OrderUserInfoFilter implements SearchFilter<Order> {

    private final String pattern;

    public OrderUserInfoFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Order> toSpecification() {
        return Specifications.and(Specifications.or(Specifications.like("doctor.user.fullName", pattern),
                                                    Specifications.like("patient.mobile", pattern),
                                                    Specifications.like("patient.name", pattern)));
    }

    @Override
    public String toExpression() {
        return "Order:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof OrderUserInfoFilter)) {
            return false;
        }

        OrderUserInfoFilter rhs = (OrderUserInfoFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
