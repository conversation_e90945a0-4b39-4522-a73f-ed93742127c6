package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.DoctorFee;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.service.dto.DoctorFeeDTO;

/**
 *
 */
public interface DoctorFeeService {

    /**
     * 修改医生的出诊设置
     *
     * @param doctor 医生
     * @param dto    设置
     */
    void saveDoctorFee(MedicalWorker doctor, DoctorFeeDTO dto);

    /**
     * 查询医生已开启的出诊配置 若医生为空或未配置则返回空
     *
     * @param doctor 医生
     * @return 配置
     */
    Doctor<PERSON>ee getDoctorFee(MedicalWorker doctor);
}
