package cn.taihealth.ih.service.impl.filter.hisoutpatientcharge;

import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.*;

public class PatientPatIdFilter implements SearchFilter<HisOutpatientCharge> {
    private final String value;

    public PatientPatIdFilter(String value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<HisOutpatientCharge> toSpecification() {
        return new Specification<HisOutpatientCharge>() {
            @Override
            public Predicate toPredicate(Root<HisOutpatientCharge> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
                Subquery<Long> medicCardSubquery = query.subquery(Long.class);
                Root<ElectronicMedicCard> medicCardRoot = medicCardSubquery.from(ElectronicMedicCard.class);
                medicCardSubquery.select(medicCardRoot.get("id"))
                        .where(criteriaBuilder.equal(medicCardRoot.get("hisPatid"), value));
                return criteriaBuilder.in(root.get("electronicMedicCardId")).value(medicCardSubquery);
            }
        };
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
