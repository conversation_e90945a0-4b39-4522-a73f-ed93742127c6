package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.service.LinkService;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import com.gitq.jedi.context.AppContext;
import io.swagger.annotations.ApiModelProperty;

/**
 */
public class UploadVM extends AbstractEntityDTO {

    @ApiModelProperty("文件名")
    private String originalName;

    @ApiModelProperty("文件大小")
    private int size;

    @ApiModelProperty("文件访问路径")
    private String url;

    @ApiModelProperty("图片宽度")
    private int width;

    @ApiModelProperty("图片高度")
    private int height;

    private int duration;

    public UploadVM() {}

    public UploadVM(Upload upload) {
        super(upload);

        this.originalName = upload.getOriginalName();
        this.size = upload.getSize();
        this.url = generateUrl(upload);
        this.width = upload.getWidth();
        this.height = upload.getHeight();
        this.duration = upload.getDuration();
    }

    public Upload toEntity(){
        Upload upload = new Upload();
        upload.setId(this.getId());
        upload.setOriginalName(this.getOriginalName());
        upload.setSize(this.getSize());
        upload.setUrl(this.getUrl());
        upload.setWidth(this.getWidth());
        upload.setHeight(this.getHeight());
        upload.setDuration(this.getDuration());
        return upload;
    }

    private String generateUrl(Upload upload) {
        return AppContext.getInstance(LinkService.class).urlOfUpload(upload);
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

}
