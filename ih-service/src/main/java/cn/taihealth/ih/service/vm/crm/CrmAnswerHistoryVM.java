package cn.taihealth.ih.service.vm.crm;

import cn.taihealth.ih.domain.crm.CrmTaskDetailAnswer;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import cn.taihealth.ih.service.dto.crm.CrmOptionDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 随访-问卷-问题表
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
public class CrmAnswerHistoryVM extends UpdatableDTO {

    @ApiModelProperty("答题时间")
    private Date answerTime;

    @ApiModelProperty("答案备注")
    private String result;

    @ApiModelProperty("答案选项")
    private List<CrmOptionDTO> options = Lists.newArrayList();


    public CrmAnswerHistoryVM(CrmTaskDetailAnswer answer) {
        super(answer);
        this.answerTime = answer.getUpdatedDate();
        this.result = answer.getResult();
    }

}
