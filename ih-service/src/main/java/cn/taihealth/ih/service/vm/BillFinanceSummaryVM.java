package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.TreeMap;

/**
 * 交易概况
 */
@Data
public class BillFinanceSummaryVM {


    @ApiModelProperty("医疗机构名称")
    private String hospitalName;

    @ApiModelProperty("各服务交易类型金额")
    private TreeMap<ProjectTypeEnum, Long> serviceAmount;

}
