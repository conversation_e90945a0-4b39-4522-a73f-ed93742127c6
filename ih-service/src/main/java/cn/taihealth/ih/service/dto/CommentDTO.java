package cn.taihealth.ih.service.dto;


import cn.taihealth.ih.domain.hospital.Comment;

/**
 */
public class CommentDTO extends UpdatableDTO {

    private UserDTO user;
    private Long articleId;
    private String raw;
    private String html;

    public CommentDTO() {
    }

    public CommentDTO(Comment comment) {
        super(comment);

        this.articleId = comment.getArticle().getId();
        this.user = new UserDTO(comment.getUser());
        this.raw = comment.getRaw();
        this.html = comment.getHtml();
    }

    public UserDTO getUser() {
        return user;
    }

    public void setUser(UserDTO user) {
        this.user = user;
    }

    public Long getArticleId() {
        return articleId;
    }

    public void setArticleId(Long articleId) {
        this.articleId = articleId;
    }

    public String getRaw() {
        return raw;
    }

    public void setRaw(String raw) {
        this.raw = raw;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }
}
