[{"code": "ADMIN", "name": "超级管理员", "authorities": [{"name": "超级管理员", "code": "admin_auth", "resources": [{"method": "GET", "resourcePath": "/admin/**"}, {"method": "POST", "resourcePath": "/admin/**"}, {"method": "PUT", "resourcePath": "/admin/**"}, {"method": "DELETE", "resourcePath": "/admin/**"}, {"method": "PATCH", "resourcePath": "/admin/**"}, {"method": "GET", "resourcePath": "/users"}, {"method": "GET", "resourcePath": "/users/**"}, {"method": "PATCH", "resourcePath": "/users/**"}, {"method": "PUT", "resourcePath": "/users/**"}, {"method": "POST", "resourcePath": "/users/**"}, {"method": "DELETE", "resourcePath": "/users/**"}, {"method": "GET", "resourcePath": "/wxapi/**"}, {"method": "POST", "resourcePath": "/wxapi/**"}, {"method": "PUT", "resourcePath": "/wxapi/**"}, {"method": "DELETE", "resourcePath": "/wxapi/**"}, {"method": "PATCH", "resourcePath": "/wxapi/**"}]}]}, {"code": "SUPERVISOR", "name": "监管员", "authorities": []}, {"code": "SYSTEM", "name": "系统", "authorities": [{"name": "系统权限角色", "code": "system_auth", "resources": [{"method": "POST", "resourcePath": "/api/**"}, {"method": "GET", "resourcePath": "/api/**"}]}]}, {"code": "HOSPITAL_OPT", "name": "医院运维", "authorities": []}, {"code": "USER", "name": "用户", "authorities": [{"code": "RECENT_NEWS", "name": "最新动态", "resources": [{"method": "GET", "resourcePath": "/user/notifies/**"}, {"method": "GET", "resourcePath": "/user/notifies"}, {"method": "PUT", "resourcePath": "/user/notifies/**"}, {"method": "GET", "resourcePath": "/user/orders/uncompleted"}]}, {"code": "wx_onlineConsult", "name": "在线咨询", "resources": [{"method": "POST", "resourcePath": "/user/orders"}, {"method": "PUT", "resourcePath": "/user/orders/**"}, {"method": "GET", "resourcePath": "/user/orders/**"}, {"method": "GET", "resourcePath": "/user/orders"}, {"method": "POST", "resourcePath": "/user/orders"}, {"method": "GET", "resourcePath": "/user/orders/*/order_evaluate"}, {"method": "POST", "resourcePath": "/user/orders/*/order_evaluate"}, {"method": "PUT", "resourcePath": "/user/orders/*/register"}, {"method": "GET", "resourcePath": "/orders/*/queue_status"}, {"method": "GET", "resourcePath": "/user/orders/timmessage/*"}]}, {"code": "wx_onlineAppointment", "name": "在线预约", "resources": [{"method": "POST", "resourcePath": "/user/orders"}, {"method": "PUT", "resourcePath": "/user/orders/**"}, {"method": "GET", "resourcePath": "/user/orders/**"}, {"method": "GET", "resourcePath": "/user/orders"}, {"method": "POST", "resourcePath": "/user/orders"}, {"method": "GET", "resourcePath": "/user/schedules"}, {"method": "GET", "resourcePath": "/user/days/schedules"}, {"method": "PUT", "resourcePath": "/user/orders/*/register"}, {"method": "GET", "resourcePath": "/orders/*/queue_status"}]}, {"code": "wx_outAppointment", "name": "门诊预约", "resources": [{"method": "POST", "resourcePath": "/user/orders"}, {"method": "PUT", "resourcePath": "/user/orders/**"}, {"method": "GET", "resourcePath": "/user/orders/**"}, {"method": "GET", "resourcePath": "/user/orders"}, {"method": "POST", "resourcePath": "/user/orders"}, {"method": "GET", "resourcePath": "/user/days/schedules"}, {"method": "PUT", "resourcePath": "/user/orders/*/register"}, {"method": "GET", "resourcePath": "/orders/*/queue_status"}]}, {"code": "wx_hospital_intro", "name": "医院介绍", "resources": []}, {"code": "wx_hospital_characteristic_dept", "name": "特色科室"}, {"code": "wx_hospital_service_schedule", "name": "出诊信息"}, {"code": "wx_hospital_user_guide", "name": "操作指南"}, {"code": "wx_menu_home", "name": "首页", "resources": []}, {"code": "wx_menu_health", "name": "健康管理", "resources": [{"method": "GET", "resourcePath": "/health_exams"}, {"method": "GET", "resourcePath": "/health_exams/*"}, {"method": "GET", "resourcePath": "/health_exams/*/exam_diagnosis"}]}, {"code": "wx_menu_my", "name": "我的", "resources": [{"method": "POST", "resourcePath": "/user/tickets"}, {"method": "GET", "resourcePath": "/user/tickets/*"}, {"method": "POST", "resourcePath": "/user/tickets/*/replyComplaint"}, {"method": "GET", "resourcePath": "/user/articles/byMedicalWorker"}, {"method": "GET", "resourcePath": "/user/articles/medical_worker"}]}, {"code": "wx_menu_message", "name": "消息"}, {"code": "wx_menu_message_chat", "name": "诊间对话", "resources": [{"method": "POST", "resourcePath": "/guidance/orders"}, {"method": "GET", "resourcePath": "/health_exams/*"}, {"method": "GET", "resourcePath": "/health_exams/*/exam_diagnosis"}]}, {"code": "wx_menu_message_sysmsg", "name": "系统通知"}, {"code": "wx_health_knowledge", "name": "健康知识", "resources": [{"method": "GET", "resourcePath": "/articles"}, {"method": "GET", "resourcePath": "/articles/**"}]}, {"code": "wx_patientList", "name": "就诊人管理", "resources": [{"method": "GET", "resourcePath": "/user/patients"}, {"method": "POST", "resourcePath": "/user/patients"}, {"method": "GET", "resourcePath": "/user/patients/**"}, {"method": "POST", "resourcePath": "/user/patients/**"}, {"method": "PATCH", "resourcePath": "/user/patients/**"}, {"method": "DELETE", "resourcePath": "/user/patients/**"}, {"method": "PUT", "resourcePath": "/user/patients/**"}, {"method": "GET", "resourcePath": "/sms/patient"}]}, {"code": "wx_patientHealth", "name": "健康档案", "resources": [{"method": "GET", "resourcePath": "/user/patients/*/health_records/**"}, {"method": "PATCH", "resourcePath": "/user/patients/*/health_records/**"}, {"method": "DELETE", "resourcePath": "/user/patients/*/health_records/**"}, {"method": "POST", "resourcePath": "/user/patients/*/health_records/**"}, {"method": "PUT", "resourcePath": "/user/patients/*/health_records/**"}, {"method": "POST", "resourcePath": "/user/patients/*/clinic_records"}, {"method": "POST", "resourcePath": "/user/patients/*/signs"}, {"method": "GET", "resourcePath": "/user/patients/*/signs/current"}, {"method": "GET", "resourcePath": "/user/patients/*/signs"}, {"method": "GET", "resourcePath": "/user/patients/*/prescription_records_history"}, {"method": "GET", "resourcePath": "/user/orders/*/prescription_order"}, {"method": "GET", "resourcePath": "/user/orders/prescription_order/*"}]}, {"code": "wx_addressList", "name": "地址管理", "resources": [{"method": "POST", "resourcePath": "/user/addresses"}, {"method": "GET", "resourcePath": "/user/addresses/*"}, {"method": "DELETE", "resourcePath": "/user/addresses/*"}, {"method": "GET", "resourcePath": "/user/addresses"}]}, {"code": "wx_orderList", "name": "就诊管理"}, {"code": "wx_appointmentList", "name": "我的预约"}, {"code": "wx_msg", "name": "消息中心"}, {"code": "wx_examine_sign", "name": "检查签到"}, {"code": "wx_examine_queue", "name": "排队叫号"}, {"code": "wx_examine_report", "name": "查看报告"}, {"name": "用户公共权限", "code": "user_public", "resources": [{"method": "GET", "resourcePath": "/user/**"}, {"method": "POST", "resourcePath": "/user/**"}, {"method": "PUT", "resourcePath": "/user/**"}, {"method": "DELETE", "resourcePath": "/user/**"}, {"method": "PATCH", "resourcePath": "/user/**"}, {"method": "POST", "resourcePath": "/logout"}, {"method": "POST", "resourcePath": "/login/refresh_token"}, {"method": "PATCH", "resourcePath": "/reset-new-password"}, {"method": "GET", "resourcePath": "/user"}, {"method": "PATCH", "resourcePath": "/user"}, {"method": "POST", "resourcePath": "/files/upload"}, {"method": "POST", "resourcePath": "/files/upload_with_cropping"}, {"method": "DELETE", "resourcePath": "/files/raw/*"}, {"method": "GET", "resourcePath": "/districts"}, {"method": "GET", "resourcePath": "/hospital/menus"}, {"method": "GET", "resourcePath": "/diseases"}, {"method": "POST", "resourcePath": "/guidance/question_and_answer/*"}, {"method": "GET", "resourcePath": "/guidance/question_and_answer/*"}, {"method": "POST", "resourcePath": "/dept/recommend_disease"}, {"method": "POST", "resourcePath": "/guidance/question_and_answer/done/*"}, {"method": "POST", "resourcePath": "/guidance/wait_question_and_answer/*"}, {"method": "GET", "resourcePath": "/guidance/wait_question_and_answer/*"}, {"method": "GET", "resourcePath": "/depts/*"}, {"method": "GET", "resourcePath": "/depts"}, {"method": "GET", "resourcePath": "/articles"}, {"method": "GET", "resourcePath": "/articles/**"}, {"method": "GET", "resourcePath": "/around/**"}, {"method": "GET", "resourcePath": "/sms/mobile"}, {"method": "GET", "resourcePath": "/dict/signs"}, {"method": "PUT", "resourcePath": "/wechat/order/unified_order"}, {"method": "PUT", "resourcePath": "/wechat/insurance/unified_order"}, {"method": "POST", "resourcePath": "/alipay/order/unified_order"}, {"method": "GET", "resourcePath": "/alipay/medical_insurance/param"}, {"method": "POST", "resourcePath": "/alipay/order/insurance/unified_order"}, {"method": "POST", "resourcePath": "/wechat/order/refund"}, {"method": "POST", "resourcePath": "/wechat/unbind"}, {"method": "POST", "resourcePath": "/api/prescription_order/refund"}, {"method": "GET", "resourcePath": "/drug_store/oauth/authorize"}, {"method": "PATCH", "resourcePath": "/drug_store/oauth/authorize/recipe/**/bind"}, {"method": "GET", "resourcePath": "/user/categories"}, {"method": "GET", "resourcePath": "/v1/user/*"}, {"method": "GET", "resourcePath": "/user/articles/*"}, {"method": "GET", "resourcePath": "/user/articles/*/*"}, {"method": "PUT", "resourcePath": "/user/articles/*/*"}, {"method": "POST", "resourcePath": "/user/articles/*/*"}, {"method": "GET", "resourcePath": "/user/*/index/practicingData"}, {"method": "GET", "resourcePath": "/user/*/practicingData"}, {"method": "GET", "resourcePath": "/mini/*/info"}, {"method": "POST", "resourcePath": "/user/general/bind/*"}, {"method": "POST", "resourcePath": "/user/batch/bind"}, {"method": "DELETE", "resourcePath": "/user/unfocus"}, {"method": "GET", "resourcePath": "/user/*/bind_info"}, {"method": "GET", "resourcePath": "/user/*"}, {"method": "GET", "resourcePath": "/user/patients/relationship/self"}, {"method": "GET", "resourcePath": "/user/doctorVisit/*"}, {"method": "GET", "resourcePath": "/user/doctorVisit/**"}, {"method": "GET", "resourcePath": "/user/patients"}, {"method": "GET", "resourcePath": "/user/patients/**"}, {"method": "POST", "resourcePath": "/user/patients/**"}, {"method": "PUT", "resourcePath": "/user/patients/**"}, {"method": "PATCH", "resourcePath": "/user/patients/**"}, {"method": "DELETE", "resourcePath": "/user/patients/**"}, {"method": "POST", "resourcePath": "/user/register"}, {"method": "GET", "resourcePath": "/mini/medical_worker/*/qrcode"}, {"method": "GET", "resourcePath": "/user/register/audit_info"}, {"method": "PUT", "resourcePath": "/user/register/cancel"}, {"method": "POST", "resourcePath": "/user/peritonealDialysis"}, {"method": "POST", "resourcePath": "/user/peritonealDialysis/**"}, {"method": "PUT", "resourcePath": "/user/peritonealDialysis/*"}, {"method": "PUT", "resourcePath": "/user/peritonealDialysis"}, {"method": "GET", "resourcePath": "/user/peritonealDialysis/**"}, {"method": "GET", "resourcePath": "/user/notify"}, {"method": "GET", "resourcePath": "/user/medical_workers/*/register_fees"}, {"method": "GET", "resourcePath": "/user/medical_workers/*/special_disease_fees"}, {"method": "GET", "resourcePath": "/mini/offline_hospitals"}, {"method": "GET", "resourcePath": "/user/notify/*"}, {"method": "PUT", "resourcePath": "/user/notify/*"}, {"method": "GET", "resourcePath": "/user/hospital/special_disease_platforms"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/uploads/*/practicing_certificate_no"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/uploads/*/certificate_no"}, {"method": "GET", "resourcePath": "/user/uploads/*/universal_character_recognition"}, {"method": "GET", "resourcePath": "/user/orders/im/message/un_read_count"}, {"method": "GET", "resourcePath": "/hospital/orders/medical_workers/*/im/message/un_read_count"}, {"method": "PUT", "resourcePath": "/user/orders/im/group/*/message/*/read"}, {"method": "GET", "resourcePath": "/user/bind_info"}, {"method": "POST", "resourcePath": "/user/peritonealDialysisRecord"}, {"method": "POST", "resourcePath": "/user/peritonealDialysisRecord/**"}, {"method": "GET", "resourcePath": "/user/peritonealDialysisRecord"}, {"method": "GET", "resourcePath": "/user/peritonealDialysisRecord/**"}, {"method": "GET", "resourcePath": "/user/peritoneal_schemes"}, {"method": "POST", "resourcePath": "/user/peritoneal_schemes"}, {"method": "GET", "resourcePath": "/user/peritoneal/followup_records/*/detail"}, {"method": "GET", "resourcePath": "/user/peritoneal/followup_records"}, {"method": "POST", "resourcePath": "/user/peritoneal/followup_records"}, {"method": "PUT", "resourcePath": "/user/peritoneal/followup_records/*"}, {"method": "DELETE", "resourcePath": "/user/peritoneal/followup_records/*"}, {"method": "GET", "resourcePath": "/user/body_record/**"}, {"method": "POST", "resourcePath": "/user/body_record"}, {"method": "PUT", "resourcePath": "/user/body_record"}, {"method": "GET", "resourcePath": "/user/health_trend"}, {"method": "POST", "resourcePath": "/user/peritonealRecord/**"}, {"method": "PUT", "resourcePath": "/user/peritonealRecord/**"}, {"method": "GET", "resourcePath": "/user/peritonealRecord/**"}, {"method": "POST", "resourcePath": "/users/individualVerify"}, {"method": "GET", "resourcePath": "/user/docAndDept"}, {"method": "GET", "resourcePath": "/user/return_visit/medical_workers"}, {"method": "GET", "resourcePath": "/user/template_ids"}, {"method": "DELETE", "resourcePath": "/user/orders/*"}, {"method": "GET", "resourcePath": "/user/hospital/trends/**"}, {"method": "GET", "resourcePath": "/user/orders/*/order_evaluate"}, {"method": "POST", "resourcePath": "/user/orders/*/order_evaluate"}, {"method": "DELETE", "resourcePath": "/user/orders/*/order_evaluate"}, {"method": "GET", "resourcePath": "/user/orders/order_evaluate/list"}, {"method": "GET", "resourcePath": "/user/order_evaluate/*/list"}, {"method": "GET", "resourcePath": "/user/order_evaluate_tag/*/list"}, {"method": "DELETE", "resourcePath": "/hospital/dictionary/*"}, {"method": "GET", "resourcePath": "/user/prescription_order/*/orders/detail"}, {"method": "GET", "resourcePath": "/user/settings"}, {"method": "GET", "resourcePath": "/user/settings/*"}, {"method": "PUT", "resourcePath": "/user/settings/*"}, {"method": "GET", "resourcePath": "/user/follow/list"}, {"method": "GET", "resourcePath": "/user/my_doctor/list"}, {"method": "GET", "resourcePath": "/user/follow/*"}, {"method": "PUT", "resourcePath": "/user/follow/*"}, {"method": "DELETE", "resourcePath": "/user/follow/*"}, {"method": "POST", "resourcePath": "/user/patient/followUp/list/*"}, {"method": "POST", "resourcePath": "/user/questionnaires/*"}, {"method": "GET", "resourcePath": "/user/questionnaires/*"}, {"method": "GET", "resourcePath": "/user/questionnaires/channel/*"}, {"method": "GET", "resourcePath": "/user/questionnaires"}, {"method": "GET", "resourcePath": "/answer/results"}, {"method": "GET", "resourcePath": "/answer/results/*/answer"}, {"method": "GET", "resourcePath": "/user/tencent_video_recording/*"}, {"method": "GET", "resourcePath": "/user/pat_service/patients/**"}, {"method": "POST", "resourcePath": "/user/pat_service/patients/**"}, {"method": "PUT", "resourcePath": "/user/pat_service/patients/**"}, {"method": "DELETE", "resourcePath": "/user/pat_service/patients/**"}, {"method": "GET", "resourcePath": "/user/patients/*/hisPatient"}, {"method": "POST", "resourcePath": "/user/patients/*/visitorPasses"}, {"method": "GET", "resourcePath": "/user/patients/*/visitorPasses"}, {"method": "GET", "resourcePath": "/user/patients/*/visitorPasses/*"}, {"method": "DELETE", "resourcePath": "/user/patients/*/visitorPasses/*"}, {"method": "PUT", "resourcePath": "/user/patients/*/visitorPasses/*"}, {"method": "GET", "resourcePath": "/user/studios"}, {"method": "GET", "resourcePath": "/user/studios/*"}, {"method": "GET", "resourcePath": "/user/studios/*/workers"}, {"method": "POST", "resourcePath": "/user/studios/*/tickets"}, {"method": "GET", "resourcePath": "/user/studios/tickets/*"}, {"method": "GET", "resourcePath": "/user/studios/tickets"}, {"method": "GET", "resourcePath": "/user/studios/*/articles"}, {"method": "GET", "resourcePath": "/user/studios/*/articles/*"}, {"method": "GET", "resourcePath": "/user/phyExams/companies"}, {"method": "GET", "resourcePath": "/user/phyExams/packages"}, {"method": "GET", "resourcePath": "/user/phyExams/packages/*"}, {"method": "POST", "resourcePath": "/user/phyExams"}, {"method": "DELETE", "resourcePath": "/user/phyExams/*"}, {"method": "GET", "resourcePath": "/user/phyExams/*"}, {"method": "GET", "resourcePath": "/user/phyExams"}, {"method": "GET", "resourcePath": "/user/patients/*/phyExams/reports"}, {"method": "GET", "resourcePath": "/user/discharge/information"}, {"method": "GET", "resourcePath": "/user/discharge/informationDetail"}, {"method": "GET", "resourcePath": "/user/discharge/fees"}, {"method": "GET", "resourcePath": "/user/discharge/fees/*"}, {"method": "GET", "resourcePath": "/user/discharge/fees/preSettle"}, {"method": "GET", "resourcePath": "/user/discharge/fees/settle"}, {"method": "GET", "resourcePath": "/user/medical_record_copy_appointment/orders/*/logistics/info"}, {"method": "GET", "resourcePath": "/user/medical_record_copy_appointment/enable/copy_purpose_list"}, {"method": "POST", "resourcePath": "/user/medical_record_copy_appointment/orders/patient_card/*"}, {"method": "PUT", "resourcePath": "/user/medical_record_copy_appointment/*/cancel"}, {"method": "PUT", "resourcePath": "/user/medical_record_copy_appointment/*/apply_refund"}, {"method": "PUT", "resourcePath": "/user/medical_record_copy_appointment/*/received"}, {"method": "GET", "resourcePath": "/user/medical_record_copy_appointment"}, {"method": "GET", "resourcePath": "/user/medical_record_copy_appointment/*"}, {"method": "POST", "resourcePath": "/user/outpatient/electronic/*/pre_charge"}, {"method": "POST", "resourcePath": "/user/outpatient/electronic/*/pre_charge/insurance"}, {"method": "GET", "resourcePath": "/user/outpatient/pre_charge/group/*"}, {"method": "GET", "resourcePath": "/user/outpatient/pre_charge/group/insurance/*"}, {"method": "POST", "resourcePath": "/user/pat_service/patients/*/cancel/appointment/*"}, {"method": "POST", "resourcePath": "/user/pat_service/patients/order/*/in_patient_apply"}, {"method": "POST", "resourcePath": "/user/offline/order/*/evaluate"}, {"method": "GET", "resourcePath": "/user/offline/order/evaluate/list"}, {"method": "GET", "resourcePath": "/user/offline/order/*/evaluate"}, {"method": "DELETE", "resourcePath": "/user/offline/order/evaluate/*"}, {"method": "POST", "resourcePath": "/user/offline/follow/*"}, {"method": "GET", "resourcePath": "/user/offline/follow/*"}, {"method": "GET", "resourcePath": "/user/offline/follow/list"}, {"method": "DELETE", "resourcePath": "/user/offline/follow/*"}, {"method": "GET", "resourcePath": "/user/offline/my_doctor/list"}, {"method": "GET", "resourcePath": "/user/notify/list"}, {"method": "GET", "resourcePath": "/user/notify/un_read_count"}, {"method": "GET", "resourcePath": "/user/notify/read/*"}, {"method": "GET", "resourcePath": "/user/exams/categories/*"}, {"method": "GET", "resourcePath": "/user/third_order/pay_method"}, {"method": "GET", "resourcePath": "/user/patients/*/exam_orders"}, {"method": "POST", "resourcePath": "/user/patients/*/exam_orders"}, {"method": "PUT", "resourcePath": "/user/patients/*/exam_orders/*"}, {"method": "PUT", "resourcePath": "/user/patients/exam_orders/*/sign"}, {"method": "GET", "resourcePath": "/user/patients/*/exam_orders/queue_info"}, {"method": "PUT", "resourcePath": "/user/patients/exam_orders/{id}/cancel"}, {"method": "GET", "resourcePath": "/user/exams/schedules/number_source/*"}, {"method": "GET", "resourcePath": "/user/nursing/**"}, {"method": "POST", "resourcePath": "/user/nursing/orders"}, {"method": "PUT", "resourcePath": "/user/nursing/orders/**"}, {"method": "GET", "resourcePath": "/hospital/nursing/consul_items"}, {"method": "GET", "resourcePath": "/hospital/nursing/home_items"}]}]}, {"code": "ANONYMOUS", "name": "匿名", "authorities": [{"name": "不需要权限", "code": "no_auth", "resources": [{"method": "HEAD", "resourcePath": "/public/**"}, {"method": "GET", "resourcePath": "/public/**"}, {"method": "GET", "resourcePath": "/user/system"}, {"method": "POST", "resourcePath": "/login"}, {"method": "POST", "resourcePath": "/login/by<PERSON><PERSON><PERSON>a"}, {"method": "GET", "resourcePath": "/getCaptcha/*"}, {"method": "POST", "resourcePath": "/login/sms"}, {"method": "POST", "resourcePath": "/login/sms/medical_worker"}, {"method": "POST", "resourcePath": "/login/sms/medical_worker_face"}, {"method": "POST", "resourcePath": "/signup"}, {"method": "PATCH", "resourcePath": "/reset-password"}, {"method": "GET", "resourcePath": "/files/raw/*"}, {"method": "GET", "resourcePath": "/files/rawu/*"}, {"method": "GET", "resourcePath": "/sms/signup"}, {"method": "GET", "resourcePath": "/sms/wechat"}, {"method": "GET", "resourcePath": "/sms/login"}, {"method": "GET", "resourcePath": "/avatar/u/*"}, {"method": "GET", "resourcePath": "/avatar/empty.png"}, {"method": "GET", "resourcePath": "/**/*.{js,css,png,html}"}, {"method": "GET", "resourcePath": "/wechat/**"}, {"method": "POST", "resourcePath": "/wechat/**"}, {"method": "GET", "resourcePath": "/api/wechat/**"}, {"method": "POST", "resourcePath": "/api/wechat/**"}, {"method": "GET", "resourcePath": "/api/hospital/express/**"}, {"method": "POST", "resourcePath": "/wechat/bind"}, {"method": "GET", "resourcePath": "/mp/**"}, {"method": "GET", "resourcePath": "/hospital/system"}, {"method": "GET", "resourcePath": "/dicEnum/**"}, {"method": "POST", "resourcePath": "/tim/callback"}, {"method": "GET", "resourcePath": "/source/**"}, {"method": "POST", "resourcePath": "/api/user/id_num_identity"}, {"method": "POST", "resourcePath": "/mini/auth"}, {"method": "POST", "resourcePath": "/mini/auth_new"}, {"method": "POST", "resourcePath": "/mini/auth_ca"}, {"method": "GET", "resourcePath": "/mini/sessionKey"}, {"method": "GET", "resourcePath": "/mini/*/info"}, {"method": "GET", "resourcePath": "/user/banner/*"}, {"method": "POST", "resourcePath": "/wechat/attention/callback"}, {"method": "GET", "resourcePath": "/user/articles/patient"}, {"method": "GET", "resourcePath": "/user/categories"}, {"method": "POST", "resourcePath": "/user/*/receiveRecordResult"}, {"method": "GET", "resourcePath": "/user/articles/*"}, {"method": "GET", "resourcePath": "/user/categories/*"}, {"method": "POST", "resourcePath": "/outer/callback/tencent/video_finish"}, {"method": "POST", "resourcePath": "/nodered/**"}, {"method": "GET", "resourcePath": "/nodered/**"}, {"method": "GET", "resourcePath": "/hospital/ca/qrcode"}, {"method": "POST", "resourcePath": "/hospital/ca/login/qrcode"}, {"method": "GET", "resourcePath": "/user/drug/popularSciences"}, {"method": "GET", "resourcePath": "/user/drug/popularSciences/*"}, {"method": "GET", "resourcePath": "/user/drug/instructions"}, {"method": "GET", "resourcePath": "/user/drug/instructions/*"}, {"method": "POST", "resourcePath": "/tim/user_exit_room"}, {"method": "POST", "resourcePath": "/his/B_<PERSON>ushPatientMsg"}, {"method": "POST", "resourcePath": "/his/B_PushBusinessChangeMsg"}, {"method": "POST", "resourcePath": "/his/B_ReverseAbnormalBill"}, {"method": "POST", "resourcePath": "/his/B_OrderRefunded"}, {"method": "POST", "resourcePath": "/his/B_QRCode"}, {"method": "POST", "resourcePath": "/nodered/send/*"}, {"method": "GET", "resourcePath": "/hospital/charge_items"}, {"method": "GET", "resourcePath": "/user/recommend_depts"}, {"method": "GET", "resourcePath": "/user/recommend_medical_workers_depts"}, {"method": "GET", "resourcePath": "/user/page_settings"}, {"method": "GET", "resourcePath": "/user/recommend_medical_workers"}, {"method": "GET", "resourcePath": "/user/hospital/trends/list"}, {"method": "GET", "resourcePath": "/user/hospital/trends/*/content"}, {"method": "GET", "resourcePath": "/hospital/dictionary/enabled/*"}, {"method": "GET", "resourcePath": "/user/offline/depts"}, {"method": "GET", "resourcePath": "/user/hospital/offline_hospitals"}, {"method": "GET", "resourcePath": "/docs/names/**"}, {"method": "GET", "resourcePath": "/user/hospital"}, {"method": "GET", "resourcePath": "/user/offline/hospitals"}, {"method": "GET", "resourcePath": "/user/offline/hospital/list"}, {"method": "GET", "resourcePath": "/user/offline/depts/*"}, {"method": "GET", "resourcePath": "/user/offline/medical_workers"}, {"method": "GET", "resourcePath": "/user/offline/medical_workers/*"}, {"method": "GET", "resourcePath": "/user/pat_service/patients/doctor_schedules"}, {"method": "GET", "resourcePath": "/user/pat_service/patients/scheduling_dept_list"}, {"method": "GET", "resourcePath": "/user/pat_service/patients/scheduling_dept_list_test"}, {"method": "GET", "resourcePath": "/user/pat_service/patients/price_publicity_list"}, {"method": "GET", "resourcePath": "/mini/info"}, {"method": "GET", "resourcePath": "/user/recommend/offline_doctors"}, {"method": "POST", "resourcePath": "/alipay_mini/auth"}, {"method": "POST", "resourcePath": "/alipay/apps/*/order/pay"}, {"method": "POST", "resourcePath": "/unify_appointment/*"}, {"method": "POST", "resourcePath": "/sync_appointment/*"}, {"method": "POST", "resourcePath": "/user/hboc/riskReport"}, {"method": "POST", "resourcePath": "/user/hboc/pedigree"}, {"method": "POST", "resourcePath": "/user/hboc/pedigree/context"}, {"method": "POST", "resourcePath": "/user/hboc/crf"}, {"method": "GET", "resourcePath": "/user/hboc/followup"}, {"method": "POST", "resourcePath": "/wandapay/**"}, {"method": "GET", "resourcePath": "/user/preconsult/get_register_record"}, {"method": "POST", "resourcePath": "/user/preconsult/save_pre_consultation"}, {"method": "POST", "resourcePath": "/hospital/ca/orders/*/diagnosis/signed"}, {"method": "POST", "resourcePath": "/hospital/ca/orders/*/prescriptions/signed"}, {"method": "POST", "resourcePath": "/hospital/ca/orders/*/pharmacist/signed"}, {"method": "GET", "resourcePath": "/user/dify/site"}]}]}, {"code": "PUBLIC", "name": "公共权限角色", "authorities": [{"name": "公共权限角色", "code": "public_auth", "resources": []}]}, {"code": "CUSTOM", "name": "自定义", "authorities": [{"name": "根节点", "code": "pc_root", "resources": [{"method": "GET", "resourcePath": "/hospital/app_settings"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/qrcode/mini_app"}, {"method": "GET", "resourcePath": "/hospital/offline/depts"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/self"}, {"method": "GET", "resourcePath": "/hospital/ca/**"}, {"method": "POST", "resourcePath": "/hospital/ca/**"}, {"method": "GET", "resourcePath": "/hospital/hospitals"}, {"method": "GET", "resourcePath": "/hospital/settings/categories"}, {"method": "GET", "resourcePath": "/hospital/doctor/details"}, {"method": "GET", "resourcePath": "/hospital/register/schedules"}, {"method": "GET", "resourcePath": "/hospital/doctor/self/schedules"}, {"method": "GET", "resourcePath": "/hospital/settings/*"}, {"method": "GET", "resourcePath": "/hospital/self/settings"}, {"method": "GET", "resourcePath": "/hospital/self/settings/*"}, {"method": "PUT", "resourcePath": "/hospital/self/settings/*"}, {"method": "GET", "resourcePath": "/hospital/categories"}, {"method": "GET", "resourcePath": "/hospital/categories/*"}, {"method": "GET", "resourcePath": "/hospital/articles/*"}]}, {"code": "fzpbgl", "name": "复诊排班管理", "resources": [{"method": "GET", "resourcePath": "/hospital/register/schedules"}, {"method": "GET", "resourcePath": "/hospital/register/schedules/**"}, {"method": "POST", "resourcePath": "/hospital/register/schedules/**"}, {"method": "DELETE", "resourcePath": "/hospital/register/schedules/**"}, {"method": "GET", "resourcePath": "/register/schedules/medical_workers"}, {"method": "GET", "resourcePath": "/hospital/medical_workers"}]}, {"code": "zjpb", "name": "专家排班", "resources": [{"method": "GET", "resourcePath": "/hospital/register/schedules"}, {"method": "GET", "resourcePath": "/hospital/register/schedules/**"}, {"method": "POST", "resourcePath": "/hospital/register/schedules/**"}, {"method": "DELETE", "resourcePath": "/hospital/register/schedules/**"}, {"method": "GET", "resourcePath": "/register/schedules/medical_workers"}, {"method": "GET", "resourcePath": "/hospital/medical_workers"}]}, {"code": "jyjcxmgl", "name": "检验检查项目管理", "resources": []}, {"code": "jcxmzd", "name": "检查项目字典", "resources": [{"method": "GET", "resourcePath": "/hospital/depts"}, {"method": "GET", "resourcePath": "/hospital/exams/categories/items"}, {"method": "POST", "resourcePath": "/hospital/exams/categories/items"}, {"method": "PATCH", "resourcePath": "/hospital/exams/categories/items/*"}, {"method": "DELETE", "resourcePath": "/hospital/exams/categories/items/*"}, {"method": "POST", "resourcePath": "/hospital/exams/excel/upload/*"}]}, {"code": "jclb", "name": "检查类别", "resources": [{"method": "GET", "resourcePath": "/hospital/exams/categories"}, {"method": "DELETE", "resourcePath": "/hospital/exams/categories/*"}, {"method": "PATCH", "resourcePath": "/hospital/exams/categories/*"}, {"method": "POST", "resourcePath": "/hospital/exams/categories"}]}, {"code": "jcsbgl", "name": "检查设备管理", "resources": []}, {"code": "sblb", "name": "设备列表", "resources": [{"method": "GET", "resourcePath": "/hospital/exams/devices"}, {"method": "PATCH", "resourcePath": "/hospital/exams/devices/*"}, {"method": "POST", "resourcePath": "/hospital/exams/devices"}, {"method": "PUT", "resourcePath": "/hospital/exams/devices/*/delete"}, {"method": "GET", "resourcePath": "/hospital/exams/devices/maintenance/exam_orders/effect"}, {"method": "POST", "resourcePath": "/hospital/exams/devices/maintenance"}, {"method": "GET", "resourcePath": "/hospital/exams/device/*/schedules"}, {"method": "GET", "resourcePath": "/hospital/exams/device/*/maintenances"}, {"method": "DELETE", "resourcePath": "/hospital/exams/devices/maintenance/*"}, {"method": "PUT", "resourcePath": "/hospital/exams/devices/schedule/*/operation"}, {"method": "GET", "resourcePath": "/hospital/exams/devices/schedule/*"}]}, {"code": "pbjg", "name": "排班结果", "resources": [{"method": "GET", "resourcePath": "/hospital/exams/devices/schedules"}, {"method": "GET", "resourcePath": "/hospital/exam_orders/effect"}, {"method": "POST", "resourcePath": "/hospital/exams/schedules/preview"}, {"method": "GET", "resourcePath": "/hospital/exams/schedules/*/devices"}]}, {"name": "排班计划", "code": "pbjh", "resources": [{"method": "GET", "resourcePath": "/hospital/exams/schedules/groups/*"}, {"method": "GET", "resourcePath": "/hospital/exams/schedules/groups"}, {"method": "DELETE", "resourcePath": "/hospital/exams/schedules/groups/*"}, {"method": "POST", "resourcePath": "/hospital/exams/schedules/groups"}, {"method": "PATCH", "resourcePath": "/hospital/exams/schedules/groups/*"}, {"method": "POST", "resourcePath": "/hospital/exams/schedules/groups/*"}]}, {"code": "yytssz", "name": "预约天数设置", "resources": [{"method": "PATCH", "resourcePath": "/hospital/exams/categories/*/days"}]}, {"code": "fzjggl", "name": "复诊价格管理", "resources": [{"method": "PUT", "resourcePath": "/hospital/doctor/reception_parameters"}, {"method": "GET", "resourcePath": "/hospital/doctor/reception_parameters"}]}, {"code": "ksjzsz", "name": "科室接诊设置", "resources": [{"method": "PUT", "resourcePath": "/hospital/dept/reception_parameters"}, {"method": "GET", "resourcePath": "/hospital/dept/reception_parameters"}]}, {"code": "zjjzsz", "name": "专家接诊设置", "resources": [{"method": "PUT", "resourcePath": "/hospital/doctor/reception_parameters"}, {"method": "GET", "resourcePath": "/hospital/doctor/reception_parameters"}]}, {"code": "yypt", "name": "预约平台", "resources": [{"method": "GET", "resourcePath": "/hospital/depts"}, {"method": "GET", "resourcePath": "/hospital/exams/categories"}]}, {"code": "yykb", "name": "预约看板", "resources": [{"method": "GET", "resourcePath": "/hospital/stats/exam_orders/distributes"}, {"method": "GET", "resourcePath": "/hospital/stats/exam_orders/queues"}, {"method": "GET", "resourcePath": "/hospital/stats/exam_orders/inspections"}, {"method": "GET", "resourcePath": "/hospital/stats/exam_orders/heat_map"}, {"method": "GET", "resourcePath": "/hospital/stats/exam_orders/count"}, {"method": "GET", "resourcePath": "/hospital/stats/exam_orders/devices"}, {"method": "GET", "resourcePath": "/hospital/stats/exam_orders/time"}]}, {"code": "jcyygl", "name": "检查预约管理", "resources": [{"method": "GET", "resourcePath": "/hospital/exam_orders"}, {"method": "GET", "resourcePath": "/hospital/exam_orders/*/operation"}, {"method": "PUT", "resourcePath": "/hospital/exam_orders/*"}, {"method": "PUT", "resourcePath": "/hospital/exam_orders/*/cancel"}, {"method": "GET", "resourcePath": "/hospital/exam_items/recommend"}, {"method": "GET", "resourcePath": "/hospital/exams/schedules"}, {"method": "GET", "resourcePath": "/hospital/exams/schedules/number_source/*"}, {"method": "PUT", "resourcePath": "/hospital/exam_orders/*/cancel"}, {"method": "GET", "resourcePath": "/hospital/queuing_appointed"}, {"method": "PUT", "resourcePath": "/hospital/exam_orders/*/sign"}, {"method": "GET", "resourcePath": "/hospital/queues/exam_orders"}, {"method": "POST", "resourcePath": "/hospital/exam_orders"}, {"method": "GET", "resourcePath": "/hospital/patients/*/checks/to_appoint"}, {"method": "GET", "resourcePath": "/hospital/reports"}]}, {"code": "kpxjgl", "name": "科普宣教管理", "resources": [{"method": "GET", "resourcePath": "/hospital/articles"}, {"method": "POST", "resourcePath": "/hospital/articles"}, {"method": "PUT", "resourcePath": "/hospital/articles/*"}, {"method": "PUT", "resourcePath": "/hospital/articles/draft/*"}, {"method": "PUT", "resourcePath": "/hospital/articles/*/show"}, {"method": "DELETE", "resourcePath": "/hospital/articles/*"}, {"method": "GET", "resourcePath": "/hospital/articles/**"}, {"method": "GET", "resourcePath": "/hospital/medical_workers"}, {"method": "POST", "resourcePath": "/hospital/categories"}]}, {"code": "grzx", "name": "个人中心", "resources": []}, {"code": "grxx", "name": "个人信息", "resources": []}, {"code": "zxwzsz", "name": "在线问诊设置", "resources": [{"method": "POST", "resourcePath": "/hospital/doctor/auto-reply"}, {"method": "GET", "resourcePath": "/hospital/doctor/auto-reply/content"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_infos/ids"}]}, {"code": "wdwzdd", "name": "我的问诊订单", "resources": [{"method": "GET", "resourcePath": "/hospital/orders"}, {"method": "GET", "resourcePath": "/hospital/orders/list"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_infos/ids"}]}, {"code": "ysjx", "name": "医生绩效", "resources": [{"method": "GET", "resourcePath": "/hospital/users/*/performance/stats"}, {"method": "GET", "resourcePath": "/hospital/users/performance/order/list"}, {"method": "GET", "resourcePath": "/hospital/orders/list"}]}, {"code": "wdpj", "name": "我的评价", "resources": [{"method": "GET", "resourcePath": "/hospital/doctor/order/evaluate/stats"}, {"method": "GET", "resourcePath": "/hospital/doctor/order/evaluate/list"}, {"method": "GET", "resourcePath": "/hospital/doctor/order/evaluate/*/content"}]}, {"code": "xgmm", "name": "修改密码", "resources": []}, {"code": "<PERSON><PERSON>rz", "name": "CA认证日志", "resources": []}, {"code": "ynkpxjgl", "name": "院内科普宣教管理", "resources": [{"method": "GET", "resourcePath": "/hospital/articles"}, {"method": "POST", "resourcePath": "/hospital/articles"}, {"method": "PUT", "resourcePath": "/hospital/articles/**"}, {"method": "GET", "resourcePath": "/hospital/articles/**"}, {"method": "DELETE", "resourcePath": "/hospital/articles/**"}, {"method": "POST", "resourcePath": "/hospital/articles/**"}, {"method": "GET", "resourcePath": "/hospital/medical_workers"}, {"method": "POST", "resourcePath": "/hospital/categories"}, {"method": "POST", "resourcePath": "/hospital/settings/ARTICLE_NEED_VERIFY"}, {"method": "PUT", "resourcePath": "/hospital/settings/ARTICLE_NEED_VERIFY"}]}, {"code": "ypgl", "name": "药品管理", "resources": [{"method": "POST", "resourcePath": "/hospital/dic_med/excel/upload"}, {"method": "POST", "resourcePath": "/hospital/dic_med/excel/upload/check"}, {"method": "POST", "resourcePath": "/hospital/dic_med/excel/export"}, {"method": "POST", "resourcePath": "/hospital/dic_med/excel/exportAll"}]}, {"code": "ypsms", "name": "药品说明书", "resources": [{"method": "GET", "resourcePath": "/hospital/drug/instructions"}, {"method": "GET", "resourcePath": "/hospital/drug/instructions/*"}, {"method": "POST", "resourcePath": "/hospital/drug/instructions"}, {"method": "PUT", "resourcePath": "/hospital/drug/instructions/*"}, {"method": "DELETE", "resourcePath": "/hospital/drug/instructions/*"}]}, {"code": "ypkp", "name": "用药科普", "resources": [{"method": "GET", "resourcePath": "/hospital/drug/popularSciences"}, {"method": "GET", "resourcePath": "/hospital/drug/popularSciences/*"}, {"method": "POST", "resourcePath": "/hospital/drug/popularSciences"}, {"method": "PUT", "resourcePath": "/hospital/drug/popularSciences/*"}, {"method": "DELETE", "resourcePath": "/hospital/drug/popularSciences/*"}]}, {"code": "mygzs", "name": "名医工作室", "resources": [{"method": "DELETE", "resourcePath": "/hospital/studios/*"}, {"method": "POST", "resourcePath": "/hospital/studios"}, {"method": "PUT", "resourcePath": "/hospital/studios/*"}, {"method": "GET", "resourcePath": "/hospital/studios"}, {"method": "GET", "resourcePath": "/hospital/studios/*"}, {"method": "GET", "resourcePath": "/hospital/studioWorkers"}, {"method": "POST", "resourcePath": "/hospital/studioWorkers"}, {"method": "DELETE", "resourcePath": "/hospital/studioWorkers/*"}, {"method": "GET", "resourcePath": "/hospital/studios/tickets/*"}, {"method": "POST", "resourcePath": "/hospital/studios/tickets/*/replyComplaint"}, {"method": "GET", "resourcePath": "/hospital/studios/tickets"}, {"method": "GET", "resourcePath": "/hospital/studioArticles"}, {"method": "GET", "resourcePath": "/hospital/studioArticles/*"}, {"method": "POST", "resourcePath": "/hospital/studioArticles"}, {"method": "PUT", "resourcePath": "/hospital/studioArticles/*"}, {"method": "DELETE", "resourcePath": "/hospital/studioArticles/*"}, {"method": "GET", "resourcePath": "/hospital/offline/medical_workers"}]}, {"code": "mygzsgl", "name": "名医工作室管理", "resources": []}, {"code": "mylb", "name": "名医列表", "resources": []}, {"code": "mygzskp", "name": "名医工作室科普", "resources": []}, {"code": "mygzszx", "name": "名医工作室咨询", "resources": []}, {"code": "ypzd", "name": "药品字典", "resources": [{"method": "DELETE", "resourcePath": "/hospital/dic_med/med_infos/*"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_infos"}, {"method": "PUT", "resourcePath": "/hospital/dic_med/med_infos/*"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_infos"}]}, {"code": "ypfl", "name": "药品分类", "resources": [{"method": "POST", "resourcePath": "/hospital/dic_med/categories"}, {"method": "GET", "resourcePath": "/hospital/dic_med/categories"}, {"method": "GET", "resourcePath": "/hospital/dic_med/categories/*"}, {"method": "PUT", "resourcePath": "/hospital/dic_med/categories/*"}, {"method": "POST", "resourcePath": "/hospital/dic_med/categories/*"}, {"method": "DELETE", "resourcePath": "/hospital/dic_med/categories/*"}]}, {"code": "jx", "name": "剂型", "resources": [{"method": "POST", "resourcePath": "/hospital/dic_med/dosage_forms"}, {"method": "GET", "resourcePath": "/hospital/dic_med/dosage_forms"}, {"method": "GET", "resourcePath": "/hospital/dic_med/dosage_forms/*"}, {"method": "PUT", "resourcePath": "/hospital/dic_med/dosage_forms/*"}, {"method": "POST", "resourcePath": "/hospital/dic_med/dosage_forms/*"}, {"method": "DELETE", "resourcePath": "/hospital/dic_med/dosage_forms/*"}]}, {"code": "jldw", "name": "剂量单位", "resources": [{"method": "POST", "resourcePath": "/hospital/dic_med/dosage_units"}, {"method": "GET", "resourcePath": "/hospital/dic_med/dosage_units"}, {"method": "GET", "resourcePath": "/hospital/dic_med/dosage_units/*"}, {"method": "PUT", "resourcePath": "/hospital/dic_med/dosage_units/*"}, {"method": "POST", "resourcePath": "/hospital/dic_med/dosage_units/*"}, {"method": "DELETE", "resourcePath": "/hospital/dic_med/dosage_units/*"}]}, {"code": "bzdw", "name": "包装单位", "resources": [{"method": "POST", "resourcePath": "/hospital/dic_med/package_units"}, {"method": "GET", "resourcePath": "/hospital/dic_med/package_units"}, {"method": "GET", "resourcePath": "/hospital/dic_med/package_units/*"}, {"method": "PUT", "resourcePath": "/hospital/dic_med/package_units/*"}, {"method": "POST", "resourcePath": "/hospital/dic_med/package_units/*"}, {"method": "DELETE", "resourcePath": "/hospital/dic_med/package_units/*"}]}, {"code": "g<PERSON>j", "name": "给药途径", "resources": [{"method": "POST", "resourcePath": "/hospital/dic_med/med_adm_routes"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_adm_routes"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_adm_routes/*"}, {"method": "PUT", "resourcePath": "/hospital/dic_med/med_adm_routes/*"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_adm_routes/*"}, {"method": "DELETE", "resourcePath": "/hospital/dic_med/med_adm_routes/*"}]}, {"code": "gypc", "name": "给药频次", "resources": [{"method": "POST", "resourcePath": "/hospital/dic_med/med_adm_freqs"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_adm_freqs"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_adm_freqs/*"}, {"method": "PUT", "resourcePath": "/hospital/dic_med/med_adm_freqs/*"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_adm_freqs/*"}, {"method": "DELETE", "resourcePath": "/hospital/dic_med/med_adm_freqs/*"}]}, {"code": "ypxstj", "name": "药品销售统计", "resources": [{"method": "GET", "resourcePath": "/hospital/drug/**"}]}, {"code": "xtgl", "name": "系统管理", "resources": []}, {"code": "yyxxgl", "name": "医院信息管理", "resources": []}, {"code": "ynggxx", "name": "院内公共信息", "resources": []}, {"code": "yygl", "name": "医院管理", "resources": [{"method": "GET", "resourcePath": "/hospital/offline_hospitals"}, {"method": "POST", "resourcePath": "/hospital/offline_hospitals"}, {"method": "PUT", "resourcePath": "/hospital/hospitals/*"}, {"method": "GET", "resourcePath": "/hospital/page_settings"}, {"method": "GET", "resourcePath": "/hospital/page_settings/default"}, {"method": "POST", "resourcePath": "/hospital/page_settings"}, {"method": "PATCH", "resourcePath": "/hospital/bind-supervise-platform"}, {"method": "PATCH", "resourcePath": "/hospital/unbind-supervise-platform"}, {"method": "GET", "resourcePath": "/hospital/report_apis"}, {"method": "POST", "resourcePath": "/hospital/report_apis"}, {"method": "DELETE", "resourcePath": "/hospital/report_apis/*"}, {"method": "PATCH", "resourcePath": "/hospital/report_apis/*"}, {"method": "PUT", "resourcePath": "/hospital/report_apis/*"}, {"method": "GET", "resourcePath": "/hospital/supervise_platforms"}, {"method": "GET", "resourcePath": "/hospital/platforms"}, {"method": "GET", "resourcePath": "/hospital/app_settings"}, {"method": "POST", "resourcePath": "/hospital/app_settings"}, {"method": "PUT", "resourcePath": "/hospital/app_settings/*"}, {"method": "DELETE", "resourcePath": "/hospital/app_settings/*"}, {"method": "GET", "resourcePath": "/hospital/service_settings"}, {"method": "POST", "resourcePath": "/hospital/service_settings/*"}, {"method": "PUT", "resourcePath": "/hospital/service_settings/*"}, {"method": "DELETE", "resourcePath": "/hospital/service_settings/*"}]}, {"name": "科普分类管理", "code": "kpflgl", "resources": [{"method": "POST", "resourcePath": "/hospital/categories"}, {"method": "DELETE", "resourcePath": "/hospital/categories/*"}, {"method": "PUT", "resourcePath": "/hospital/categories"}, {"method": "PUT", "resourcePath": "/hospital/categories/*"}]}, {"name": "banner管理", "code": "bannergl", "resources": [{"method": "GET", "resourcePath": "/hospital/banner"}, {"method": "POST", "resourcePath": "/hospital/banner"}, {"method": "DELETE", "resourcePath": "/hospital/banner/*"}, {"method": "PUT", "resourcePath": "/hospital/banner"}, {"method": "PUT", "resourcePath": "/hospital/banner/*"}]}, {"name": "医院公告管理", "code": "yygggl", "resources": [{"method": "GET", "resourcePath": "/hospital/trends/**"}, {"method": "POST", "resourcePath": "/hospital/trends/**"}, {"method": "DELETE", "resourcePath": "/hospital/trends/**"}]}, {"name": "患者管理", "code": "hzgl", "resources": [{"method": "GET", "resourcePath": "/hospital/userPlatformInfo"}]}, {"code": "jsgl", "name": "角色管理", "resources": [{"method": "GET", "resourcePath": "/hospital/page_settings"}, {"method": "GET", "resourcePath": "/hospital/page_settings/default"}, {"method": "POST", "resourcePath": "/hospital/roles"}, {"method": "DELETE", "resourcePath": "/hospital/roles/*"}, {"method": "PATCH", "resourcePath": "/hospital/roles/*"}, {"method": "GET", "resourcePath": "/hospital/roles"}, {"method": "GET", "resourcePath": "/hospital/roles/*"}, {"method": "PUT", "resourcePath": "/hospital/roles/*"}]}, {"name": "疾病管理", "code": "jbgl", "resources": [{"method": "GET", "resourcePath": "/hospital/diseases"}, {"method": "POST", "resourcePath": "/hospital/diseases"}, {"method": "PUT", "resourcePath": "/hospital/diseases/*"}, {"method": "DELETE", "resourcePath": "/hospital/diseases/*"}, {"method": "GET", "resourcePath": "/hospital/diseases/*"}]}, {"code": "xxksgl", "name": "线下科室管理", "resources": [{"method": "GET", "resourcePath": "/hospital/offline/depts/*"}, {"method": "POST", "resourcePath": "/hospital/offline/depts"}, {"method": "PUT", "resourcePath": "/hospital/offline/depts/*"}, {"method": "DELETE", "resourcePath": "/hospital/offline/depts/*"}, {"method": "POST", "resourcePath": "/hospital/offline/depts/syncHisDeptList"}]}, {"code": "fzksgl", "name": "复诊科室管理", "resources": [{"method": "GET", "resourcePath": "/hospital/depts"}, {"method": "POST", "resourcePath": "/hospital/depts"}, {"method": "PUT", "resourcePath": "/hospital/depts/*"}, {"method": "DELETE", "resourcePath": "/hospital/depts/*"}, {"method": "GET", "resourcePath": "/hospital/diseases"}, {"method": "GET", "resourcePath": "/hospital/depts/getSubjectCodeMap"}, {"method": "GET", "resourcePath": "/hospital/recommend_depts"}, {"method": "POST", "resourcePath": "/hospital/recommend_depts"}, {"method": "DELETE", "resourcePath": "/hospital/recommend_depts/**"}]}, {"code": "xygl", "name": "协议管理", "resources": [{"method": "GET", "resourcePath": "/hospital/docs"}, {"method": "GET", "resourcePath": "/hospital/docs/names/**"}, {"method": "GET", "resourcePath": "/hospital/docs/*"}, {"method": "POST", "resourcePath": "/hospital/docs"}, {"method": "PUT", "resourcePath": "/hospital/docs/*"}, {"method": "DELETE", "resourcePath": "/hospital/docs/*"}]}, {"code": "wjgl", "name": "文件管理", "resources": [{"method": "POST", "resourcePath": "/hospital/files/upload"}, {"method": "GET", "resourcePath": "/hospital/files"}]}, {"name": "院内参数设置", "code": "yncssz", "resources": [{"method": "GET", "resourcePath": "/hospital/settings/categories/notify"}, {"method": "GET", "resourcePath": "/hospital/settings/categories/notify"}, {"method": "GET", "resourcePath": "/hospital/settings"}, {"method": "GET", "resourcePath": "/hospital/settings/*"}, {"method": "PUT", "resourcePath": "/hospital/settings/*"}, {"method": "POST", "resourcePath": "/hospital/dictionary/**"}, {"method": "PUT", "resourcePath": "/hospital/dictionary/**"}, {"method": "DELETE", "resourcePath": "/hospital/dictionary/**"}, {"method": "GET", "resourcePath": "/hospital/dictionary/**"}]}, {"code": "xxgl", "name": "消息管理", "resources": [{"method": "GET", "resourcePath": "/hospital/settings"}, {"method": "GET", "resourcePath": "/hospital/settings/*"}, {"method": "PUT", "resourcePath": "/hospital/settings/*"}]}, {"code": "rzgl", "name": "日志管理", "resources": [{"method": "GET", "resourcePath": "/hospital/logs"}]}, {"code": "wdzs", "name": "我的诊室", "resources": [{"method": "PUT", "resourcePath": "/hospital/orders/*/uploadData"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/uploadDataP"}, {"method": "GET", "resourcePath": "/hospital/orders/uploadDataG"}, {"method": "GET", "resourcePath": "/hospital/recommend_depts"}, {"method": "GET", "resourcePath": "/hospital/orders/reception_history"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/complete_consultation"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/rush_order"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/confirmed"}, {"method": "GET", "resourcePath": "/hospital/orders/list"}, {"method": "GET", "resourcePath": "/hospital/orders/reception/total"}, {"method": "GET", "resourcePath": "/hospital/orders/*/detail"}, {"method": "POST", "resourcePath": "/hospital/orders/*/medical_record"}, {"method": "GET", "resourcePath": "/hospital/orders/*/medical_record"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_infos/ids"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_infos"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_infos/prescribe"}, {"method": "GET", "resourcePath": "/hospital/orders/*/prescriptions"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/prescription_orders/*"}, {"method": "POST", "resourcePath": "/hospital/orders/*/prescription_orders"}, {"method": "DELETE", "resourcePath": "/hospital/prescription_orders/*"}, {"method": "DELETE", "resourcePath": "/hospital/prescription_drugs/*"}, {"method": "GET", "resourcePath": "/hospital/orders/*/checks"}, {"method": "POST", "resourcePath": "/hospital/orders/*/checks"}, {"method": "DELETE", "resourcePath": "/hospital/checks/*"}, {"method": "POST", "resourcePath": "/hospital/orders/*/inspections"}, {"method": "GET", "resourcePath": "/doctor/orders/*/inspections"}, {"method": "GET", "resourcePath": "/hospital/dic_med/package_units/all"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_adm_routes/all"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_adm_freqs/all"}, {"method": "GET", "resourcePath": "/hospital/dic_med/dosage_units/all"}, {"resourcePath": "/hospital/orders/*/prescription_orders/commit", "method": "PUT"}, {"resourcePath": "/hospital/orders/*/prescription_orders/confirm", "method": "PUT"}, {"resourcePath": "/hospital/orders/*/prescription_orders/manual_send", "method": "PUT"}, {"resourcePath": "/hospital/orders/*/medical_case/sent", "method": "PUT"}, {"resourcePath": "/hospital/orders/*/prescription_orders/sent/count", "method": "GET"}, {"resourcePath": "/hospital/orders/*/prescription_orders/revien/count", "method": "GET"}, {"resourcePath": "/hospital/orders/*/prescription_orders/total", "method": "GET"}, {"resourcePath": "/hospital/orders/*/referral/*", "method": "PUT"}, {"method": "GET", "resourcePath": "/hospital/diseases/flags"}, {"method": "GET", "resourcePath": "/hospital/diseases"}, {"method": "GET", "resourcePath": "/hospital/medical_case_templates"}, {"method": "GET", "resourcePath": "/hospital/medical_case_templates/list"}, {"method": "GET", "resourcePath": "/hospital/medical_case_templates/*/content"}, {"method": "GET", "resourcePath": "/hospital/exams/categories/items"}, {"method": "GET", "resourcePath": "/hospital/patient/*/active_flag"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/exams"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/problems"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/prescriptions"}, {"method": "GET", "resourcePath": "/hospital/patients/health_records/offline_history/*"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/medical_cases"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/signs"}, {"method": "GET", "resourcePath": "/hospital/patients/*/all_signs"}, {"method": "GET", "resourcePath": "/hospital/patients/*/signs/current"}, {"method": "GET", "resourcePath": "/hospital/prescription_order/*"}, {"method": "GET", "resourcePath": "/hospital/patients/health_records/order_history_record/*"}, {"method": "GET", "resourcePath": "/hospital/patients/health_records/clinic_records/*"}, {"method": "GET", "resourcePath": "/hospital/exam_order/*/report"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/medical_case_histories"}, {"method": "GET", "resourcePath": "/hospital/patients/*/exam_order_histories"}, {"method": "GET", "resourcePath": "/hospital/patients/*/crm/histories"}, {"method": "GET", "resourcePath": "/users/patients/*/health_records/personal_info/*"}, {"method": "GET", "resourcePath": "/hospital/orders/prescription_order/*"}, {"method": "GET", "resourcePath": "/hospital/crm/plans"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/info"}, {"method": "GET", "resourcePath": "/hospital/page_settings"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/my_patients"}, {"method": "GET", "resourcePath": "/hospital/orders/consult"}, {"method": "GET", "resourcePath": "/hospital/doctor/*/register_fees"}, {"method": "POST", "resourcePath": "/hospital/doctor/*/register_fees"}, {"method": "GET", "resourcePath": "/hospital/doctor/*/special_disease_fees"}, {"method": "POST", "resourcePath": "/hospital/doctor/*/special_disease_fees"}, {"method": "POST", "resourcePath": "/hospital/doctor/*/notice_time"}, {"method": "GET", "resourcePath": "/medical_workers/*/TIM_Messages/latest"}, {"method": "GET", "resourcePath": "/hospital/special_disease_platforms"}, {"method": "PATCH", "resourcePath": "/hospital/special_disease_platforms"}, {"method": "GET", "resourcePath": "/hospital/patients/**"}, {"method": "GET", "resourcePath": "/hospital/orders/*/prescription_order"}, {"method": "GET", "resourcePath": "/hospital/orders/timmessage/list/*"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/end"}, {"method": "GET", "resourcePath": "/hospital/orders/medical_workers/*/orders_count"}, {"method": "GET", "resourcePath": "/hospital/users/*/peritoneal_schemes"}, {"method": "GET", "resourcePath": "/hospital/user/*/body_record"}, {"method": "GET", "resourcePath": "/hospital/user/*/health_trend"}, {"method": "GET", "resourcePath": "/hospital/users/*/peritoneal/followup_records"}, {"method": "GET", "resourcePath": "/hospital/peritoneal/followup_records/*/detail"}, {"method": "GET", "resourcePath": "/hospital/peritonealRecord/**"}, {"method": "GET", "resourcePath": "/hospital/peritonealDialysisRecord/**"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/faceToken"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/personFace/*"}, {"method": "GET", "resourcePath": "/hospital/assistant/patients/*"}, {"method": "GET", "resourcePath": "/hospital/doctorWorkbench/*"}, {"method": "POST", "resourcePath": "/hospital/pat_service/order/*/check_items"}, {"method": "PUT", "resourcePath": "/hospital/pat_service/order/*/check_items/send"}, {"method": "GET", "resourcePath": "/hospital/pat_service/order/*/check_items"}, {"method": "GET", "resourcePath": "/hospital/pat_service/**"}, {"method": "POST", "resourcePath": "/hospital/pat_service/**"}, {"method": "PUT", "resourcePath": "/hospital/pat_service/register/offline_order"}]}, {"code": "wdpb", "name": "我的排班", "resources": [{"method": "GET", "resourcePath": "/hospital/doctor/*/register_fees"}]}, {"code": "zxsf", "name": "在线审方", "resources": [{"method": "GET", "resourcePath": "/hospital/order_history/prescription_orders"}, {"method": "GET", "resourcePath": "/hospital/order_history/prescription_orders/review"}, {"method": "GET", "resourcePath": "/hospital/prescription_order_history/review"}, {"method": "GET", "resourcePath": "/hospital/orders/*/prescriptions"}, {"method": "GET", "resourcePath": "/hospital/orders/*/medical_record"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/prescription_orders"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/prescription_orders/*/reject"}, {"method": "GET", "resourcePath": "/hospital/order_history/prescription_orders/total"}, {"method": "GET", "resourcePath": "/hospital/prescription_order_history/operations/*"}, {"method": "GET", "resourcePath": "/hospital/prescription_order_history/count"}, {"method": "GET", "resourcePath": "/hospital/patient/*/active_flag"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/exams"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/problems"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/prescriptions"}, {"method": "GET", "resourcePath": "/user/patients/*/health_records/offline_history/*"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/medical_cases"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/signs"}, {"method": "GET", "resourcePath": "/hospital/patients/*/all_signs"}, {"method": "GET", "resourcePath": "/user/patients/*/signs/current"}, {"method": "GET", "resourcePath": "/hospital/prescription_order/*"}, {"method": "GET", "resourcePath": "/user/patients/*/health_records/order_history_record/*"}, {"method": "GET", "resourcePath": "/user/patients/*/health_records/clinic_records/*"}, {"method": "GET", "resourcePath": "/hospital/exam_order/*/report"}, {"method": "GET", "resourcePath": "/users/patient/*/health_record"}, {"method": "GET", "resourcePath": "/users/patients/*"}, {"method": "GET", "resourcePath": "/hospital/patients/*/health/medical_case_histories"}, {"method": "GET", "resourcePath": "/hospital/patients/*/exam_order_histories"}, {"method": "GET", "resourcePath": "/hospital/patients/*/crm/histories"}, {"method": "GET", "resourcePath": "/users/patients/*/health_records/personal_info/*"}, {"method": "GET", "resourcePath": "/hospital/orders/*/detail"}, {"method": "GET", "resourcePath": "/hospital/orders/*/checks"}]}, {"code": "jrsh", "name": "今日审核", "resources": [{"method": "GET", "resourcePath": "/hospital/order_history/prescription_orders"}, {"method": "GET", "resourcePath": "/hospital/order_history/prescription_orders/total"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/prescription_orders"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/prescription_orders/*/reject"}, {"method": "GET", "resourcePath": "/hospital/patients/*/crm/histories"}]}, {"code": "shls", "name": "审核历史", "resources": [{"method": "GET", "resourcePath": "/hospital/prescription_order_history/operations/*"}, {"method": "GET", "resourcePath": "/hospital/prescription_order_history/*"}, {"method": "GET", "resourcePath": "/hospital/order_history/prescription_orders/review"}]}, {"code": "ypdd", "name": "药品订单", "resources": [{"method": "GET", "resourcePath": "/hospital/prescription_order/*/orders/detail"}, {"method": "GET", "resourcePath": "/hospital/settings/*"}, {"method": "GET", "resourcePath": "/hospital/quick_order/settings/scene/*"}, {"method": "GET", "resourcePath": "/hospital/drugstore/orders/*"}, {"method": "GET", "resourcePath": "/hospital/drugstore/orders"}, {"method": "GET", "resourcePath": "/hospital/drugstore/orders/*/recipe"}, {"method": "POST", "resourcePath": "/hospital/drugstore/order/statusChange"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_infos/ids"}, {"method": "GET", "resourcePath": "/hospital/logistics/info"}]}, {"code": "ddgl", "name": "订单管理", "resources": [{"method": "GET", "resourcePath": "/hospital/orders"}, {"method": "POST", "resourcePath": "/hospital/pat_service/order/*/check_items"}, {"method": "PUT", "resourcePath": "/hospital/pat_service/order/*/check_items/send"}, {"method": "GET", "resourcePath": "/hospital/pat_service/order/*/check_items"}, {"method": "GET", "resourcePath": "/hospital/pat_service/**"}, {"method": "POST", "resourcePath": "/hospital/pat_service/**"}, {"method": "PUT", "resourcePath": "/hospital/pat_service/register/offline_order"}]}, {"code": "fzzxdd", "name": "复诊咨询订单", "resources": [{"method": "GET", "resourcePath": "/hospital/orders"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_infos/ids"}, {"method": "GET", "resourcePath": "/hospital/orders/*/prescriptions"}]}, {"code": "ypddgl", "name": "药品订单管理", "resources": [{"method": "GET", "resourcePath": "/hospital/prescription_order/*/orders/detail"}, {"method": "GET", "resourcePath": "/hospital/orders/*/medical_record"}, {"method": "GET", "resourcePath": "/hospital/orders/*/prescriptions"}, {"method": "GET", "resourcePath": "/hospital/settings/*"}, {"method": "GET", "resourcePath": "/hospital/quick_order/settings/scene/*"}, {"method": "GET", "resourcePath": "/hospital/drugstore/orders/*"}, {"method": "GET", "resourcePath": "/hospital/drugstore/orders"}, {"method": "GET", "resourcePath": "/hospital/drugstore/orders/*/recipe"}, {"method": "POST", "resourcePath": "/hospital/drugstore/order/statusChange"}, {"method": "POST", "resourcePath": "/hospital/dic_med/med_infos/ids"}, {"method": "GET", "resourcePath": "/hospital/logistics/info"}]}, {"code": "hsjcgl", "name": "核酸检测管理", "resources": [{"method": "GET", "resourcePath": "/hospital/nucleic_acid_orders"}, {"method": "GET", "resourcePath": "/hospital/nucleic_acid_orders/export"}]}, {"code": "yj<PERSON><PERSON><PERSON>l", "name": "医技预约管理", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_appointment_orders"}, {"method": "GET", "resourcePath": "/hospital/medical_appointment_orders/export"}, {"method": "GET", "resourcePath": "/hospital/depts"}]}, {"code": "zzkdgl", "name": "自助开单管理", "resources": [{"method": "GET", "resourcePath": "/hospital/self_billing"}, {"method": "GET", "resourcePath": "/hospital/self_billing/export"}]}, {"code": "xxghgl", "name": "线下挂号管理", "resources": []}, {"code": "mzjfgl", "name": "门诊缴费管理", "resources": []}, {"code": "zyyjjgl", "name": "住院预交金管理", "resources": []}, {"code": "xxzjgl", "name": "线下专家管理", "resources": [{"method": "GET", "resourcePath": "/hospital/offline/medical_workers"}, {"method": "GET", "resourcePath": "/hospital/offline/medical_workers/*"}, {"method": "POST", "resourcePath": "/hospital/offline/medical_workers"}, {"method": "PUT", "resourcePath": "/hospital/offline/medical_workers/*"}, {"method": "DELETE", "resourcePath": "/hospital/offline/medical_workers/*"}]}, {"code": "fzysgl", "name": "复诊医生管理", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_workers"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/detail"}, {"method": "GET", "resourcePath": "/hospital/recommend_medical_workers"}, {"method": "POST", "resourcePath": "/hospital/recommend_medical_workers"}, {"method": "DELETE", "resourcePath": "/hospital/recommend_medical_workers/**"}, {"method": "POST", "resourcePath": "/hospital/medical_workers"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/*"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/enabled"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/visit"}, {"method": "GET", "resourcePath": "/hospital/doctors/*/orders/opened"}, {"method": "GET", "resourcePath": "/hospital/roles"}, {"method": "GET", "resourcePath": "/hospital/depts"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/id_card"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/*/id_card"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/medical_credentials"}, {"method": "DELETE", "resourcePath": "/hospital/medical_workers/*/medical_credentials"}, {"method": "POST", "resourcePath": "/hospital/medical_workers/id_num_identity"}, {"method": "POST", "resourcePath": "/hospital/medical_workers/id_operator_identity"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/qrcode"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/qrcode/*"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/individualVerify/*"}, {"method": "GET", "resourcePath": "/hospital/practicing/*"}, {"method": "POST", "resourcePath": "/hospital/practicing"}, {"method": "DELETE", "resourcePath": "/hospital/practicing/*"}, {"method": "PUT", "resourcePath": "/hospital/practicing"}, {"method": "PUT", "resourcePath": "/hospital/practicing/*"}, {"method": "GET", "resourcePath": "/hospital/doctorVisit/*"}, {"method": "GET", "resourcePath": "/hospital/doctorVisit/**"}, {"method": "POST", "resourcePath": "/hospital/doctorVisit"}, {"method": "DELETE", "resourcePath": "/hospital/doctorVisit/*"}, {"method": "PUT", "resourcePath": "/hospital/doctorVisit"}, {"method": "POST", "resourcePath": "/hospital/doctorVisit/*"}, {"method": "DELETE", "resourcePath": "/hospital/doctorVisit/stopDiagnosis/*"}, {"method": "POST", "resourcePath": "/hospital/medical_workers/*/audit"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/uploads/*/practicing_certificate_no"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/uploads/*/certificate_no"}, {"method": "GET", "resourcePath": "/hospital/notify"}, {"method": "GET", "resourcePath": "/hospital/notify/*"}, {"method": "PUT", "resourcePath": "/hospital/notify/*"}, {"method": "POST", "resourcePath": "/hospital/replyTemplate"}, {"method": "DELETE", "resourcePath": "/hospital/replyTemplate/*"}, {"method": "PUT", "resourcePath": "/hospital/replyTemplate"}, {"method": "GET", "resourcePath": "/hospital/replyTemplate"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/special_disease_platforms"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/special_disease_platforms"}, {"method": "GET", "resourcePath": "/hospital/patients/**"}, {"method": "GET", "resourcePath": "/hospital/peritonealDialysis/**"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/end"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/confirmed"}, {"method": "POST", "resourcePath": "/hospital/doctor/*/notice_time"}, {"method": "POST", "resourcePath": "/hospital/sensitiveInformationOperationLog/*"}, {"method": "GET", "resourcePath": "/hospital/assistant/authList/*"}, {"method": "POST", "resourcePath": "/hospital/assistant/authList/*"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/*/assistant/unbind"}, {"method": "POST", "resourcePath": "/hospital/medical_workers/*/assistant/bind"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/assistant/bind"}, {"method": "GET", "resourcePath": "/hospital/prescription/check/*"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/faceRecognized/*"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/if_accepted"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/can_edit_username"}, {"method": "PATCH", "resourcePath": "/users/reset-password"}]}, {"code": "ysgl", "name": "药师管理", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_workers"}, {"method": "POST", "resourcePath": "/hospital/medical_workers"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/*"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/enabled"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/visit"}, {"method": "GET", "resourcePath": "/hospital/doctors/*/orders/opened"}, {"method": "GET", "resourcePath": "/hospital/roles"}, {"method": "GET", "resourcePath": "/hospital/depts"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/id_card"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/*/id_card"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/medical_credentials"}, {"method": "DELETE", "resourcePath": "/hospital/medical_workers/*/medical_credentials"}, {"method": "POST", "resourcePath": "/hospital/medical_workers/id_num_identity"}, {"method": "POST", "resourcePath": "/hospital/medical_workers/id_operator_identity"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/qrcode"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/qrcode/*"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/individualVerify/*"}, {"method": "GET", "resourcePath": "/hospital/practicing/*"}, {"method": "POST", "resourcePath": "/hospital/practicing"}, {"method": "DELETE", "resourcePath": "/hospital/practicing/*"}, {"method": "PUT", "resourcePath": "/hospital/practicing"}, {"method": "PUT", "resourcePath": "/hospital/practicing/*"}, {"method": "GET", "resourcePath": "/hospital/doctorVisit/*"}, {"method": "GET", "resourcePath": "/hospital/doctorVisit/**"}, {"method": "POST", "resourcePath": "/hospital/doctorVisit"}, {"method": "DELETE", "resourcePath": "/hospital/doctorVisit/*"}, {"method": "PUT", "resourcePath": "/hospital/doctorVisit"}, {"method": "POST", "resourcePath": "/hospital/doctorVisit/*"}, {"method": "DELETE", "resourcePath": "/hospital/doctorVisit/stopDiagnosis/*"}, {"method": "POST", "resourcePath": "/hospital/medical_workers/*/audit"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/uploads/*/practicing_certificate_no"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/uploads/*/certificate_no"}, {"method": "GET", "resourcePath": "/hospital/notify"}, {"method": "GET", "resourcePath": "/hospital/notify/*"}, {"method": "PUT", "resourcePath": "/hospital/notify/*"}, {"method": "POST", "resourcePath": "/hospital/replyTemplate"}, {"method": "DELETE", "resourcePath": "/hospital/replyTemplate/*"}, {"method": "PUT", "resourcePath": "/hospital/replyTemplate"}, {"method": "GET", "resourcePath": "/hospital/replyTemplate"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/special_disease_platforms"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/special_disease_platforms"}, {"method": "GET", "resourcePath": "/hospital/patients/**"}, {"method": "GET", "resourcePath": "/hospital/peritonealDialysis/**"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/end"}, {"method": "PUT", "resourcePath": "/hospital/orders/*/confirmed"}, {"method": "POST", "resourcePath": "/hospital/doctor/*/notice_time"}, {"method": "POST", "resourcePath": "/hospital/sensitiveInformationOperationLog/*"}, {"method": "GET", "resourcePath": "/hospital/assistant/authList/*"}, {"method": "POST", "resourcePath": "/hospital/assistant/authList/*"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/*/assistant/unbind"}, {"method": "POST", "resourcePath": "/hospital/medical_workers/*/assistant/bind"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/assistant/bind"}, {"method": "GET", "resourcePath": "/hospital/prescription/check/*"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/faceRecognized/*"}, {"method": "GET", "resourcePath": "/hospital/recommend_medical_workers"}, {"method": "POST", "resourcePath": "/hospital/recommend_medical_workers"}, {"method": "DELETE", "resourcePath": "/hospital/recommend_medical_workers/**"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/if_accepted"}, {"method": "PATCH", "resourcePath": "/users/reset-password"}]}, {"code": "shouye", "name": "首页", "resources": [{"method": "GET", "resourcePath": "/hospital/register/schedules"}, {"method": "GET", "resourcePath": "/hospital/users/*/stats/count"}, {"method": "GET", "resourcePath": "/hospital/users/*/stats/patient/list"}, {"method": "GET", "resourcePath": "/hospital/users/*/stats/patient/count"}, {"method": "GET", "resourcePath": "/hospital/users/*/stats/orders/count"}, {"method": "GET", "resourcePath": "/hospital/users/*/stats/dept/count"}, {"method": "GET", "resourcePath": "/hospital/users/*/stats/dept/month/count"}, {"method": "GET", "resourcePath": "/hospital/doctor/details"}, {"method": "GET", "resourcePath": "/hospital/doctor/*/register_fees"}, {"method": "GET", "resourcePath": "/hospital/doctorWorkbench/*"}]}, {"code": "cyzskgl", "name": "常用知识库管理", "resources": []}, {"code": "cyyp", "name": "常用药品", "resources": [{"method": "GET", "resourcePath": "/hospital/doctor/drug/**"}, {"method": "GET", "resourcePath": "/hospital/doctor/drugs"}, {"method": "POST", "resourcePath": "/hospital/doctor/drug/**"}, {"method": "DELETE", "resourcePath": "/hospital/doctor/drug/**"}, {"method": "GET", "resourcePath": "/hospital/dic_med/med_infos"}]}, {"code": "cybl", "name": "常用病历", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_case_templates"}, {"method": "GET", "resourcePath": "/hospital/medical_case_templates/list"}, {"method": "GET", "resourcePath": "/hospital/medical_case_templates/*/content"}, {"method": "GET", "resourcePath": "/hospital/doctor/medical_case_templates/**"}, {"method": "POST", "resourcePath": "/hospital/doctor/medical_case_templates/**"}, {"method": "DELETE", "resourcePath": "/hospital/doctor/medical_case_templates/**"}]}, {"code": "cyxx", "name": "常用消息", "resources": [{"method": "GET", "resourcePath": "/hospital/doctor/message_template/**"}, {"method": "POST", "resourcePath": "/hospital/doctor/message_template/**"}, {"method": "DELETE", "resourcePath": "/hospital/doctor/message_template/**"}]}, {"code": "yncyzskglmb", "name": "院内常用知识库管理模板", "resources": []}, {"code": "yncyxxmb", "name": "院内常用消息模板", "resources": [{"method": "GET", "resourcePath": "/hospital/message_template/**"}, {"method": "POST", "resourcePath": "/hospital/message_template/**"}, {"method": "DELETE", "resourcePath": "/hospital/message_template/**"}]}, {"code": "yncyblmb", "name": "院内常用病历模板", "resources": [{"method": "POST", "resourcePath": "/hospital/medical_case_templates"}, {"method": "DELETE", "resourcePath": "/hospital/medical_case_templates/*"}, {"method": "GET", "resourcePath": "/hospital/medical_case_templates"}, {"method": "GET", "resourcePath": "/hospital/medical_case_templates/list"}, {"method": "GET", "resourcePath": "/hospital/medical_case_templates/*/content"}]}, {"code": "hzhdgl", "name": "客服中心", "resources": []}, {"code": "yjfk", "name": "意见建议", "resources": [{"method": "POST", "resourcePath": "/hospital/tickets/*/replyComplaint"}, {"method": "GET", "resourcePath": "/hospital/tickets"}, {"method": "GET", "resourcePath": "/hospital/tickets/*"}]}, {"code": "lyzx", "name": "就医咨询", "resources": [{"method": "POST", "resourcePath": "/hospital/tickets/*/replyComplaint"}, {"method": "GET", "resourcePath": "/hospital/tickets"}, {"method": "GET", "resourcePath": "/hospital/tickets/*"}]}, {"code": "pjgl", "name": "评价管理", "resources": [{"method": "GET", "resourcePath": "/hospital/order/evaluate/stats"}, {"method": "GET", "resourcePath": "/hospital/order/evaluate/list"}, {"method": "GET", "resourcePath": "/hospital/order/evaluate/*/content"}, {"method": "PUT", "resourcePath": "/hospital/order/evaluate/*"}, {"method": "GET", "resourcePath": "/hospital/offline/order/evaluate/stats"}, {"method": "GET", "resourcePath": "/hospital/offline/order/evaluate/list"}, {"method": "PUT", "resourcePath": "/hospital/offline/order/evaluate/*"}]}, {"code": "ldsxgl", "name": "流调筛选管理", "resources": [{"method": "GET", "resourcePath": "/hospital/epidemiological_survey"}]}, {"code": "yytsjl", "name": "用药推送记录", "resources": [{"method": "GET", "resourcePath": "/hospital/his/push_messages"}]}, {"code": "hmdgl", "name": "黑名单管理", "resources": [{"method": "GET", "resourcePath": "/hospital/patients/blacklist"}, {"method": "PATCH", "resourcePath": "/hospital/patients/blacklist/*"}, {"method": "PUT", "resourcePath": "/hospital/settings/PATIENT_BLACKLIST_SETTINGS"}]}, {"code": "tshzxx", "name": "推送患者消息", "resources": [{"method": "GET", "resourcePath": "/hospital/patients"}, {"method": "GET", "resourcePath": "/hospital/notify/pushlist"}, {"method": "POST", "resourcePath": "/hospital/notify/push"}]}, {"code": "yhgl", "name": "用户管理", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_workers/*/detail"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/info"}, {"method": "GET", "resourcePath": "/hospital/medical_workers"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*"}, {"method": "POST", "resourcePath": "/hospital/medical_workers"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/*"}, {"method": "DELETE", "resourcePath": "/hospital/medical_workers/*"}, {"method": "PATCH", "resourcePath": "/users/reset-password"}]}, {"code": "zdgl", "name": "字典管理", "resources": []}, {"code": "zygl", "name": "住院管理", "resources": []}, {"code": "dzphzgl", "name": "电子陪护证管理", "resources": [{"method": "GET", "resourcePath": "/hospital/visitorPasses"}, {"method": "GET", "resourcePath": "/hospital/visitorPasses/*"}]}, {"code": "tjdd", "name": "体检订单", "resources": [{"method": "GET", "resourcePath": "/hospital/phyExams"}, {"method": "GET", "resourcePath": "/hospital/phyExams/export"}]}, {"code": "zyyygl", "name": "住院预约管理", "resources": [{"method": "GET", "resourcePath": "/hospital/pat_service/in_patient/appointment_orders"}]}, {"code": "dzgl", "name": "对账管理", "resources": []}, {"code": "jygk", "name": "交易概况", "resources": [{"method": "GET", "resourcePath": "/hospital/bills/overviewSummary"}, {"method": "GET", "resourcePath": "/hospital/bills/overview"}]}, {"code": "jymx", "name": "交易明细", "resources": [{"method": "GET", "resourcePath": "/hospital/bills/serviceSummary"}, {"method": "GET", "resourcePath": "/hospital/bills/serviceSummaryWithMedicare"}, {"method": "GET", "resourcePath": "/hospital/bills"}]}, {"code": "dzjlgl", "name": "对账记录管理", "resources": [{"method": "GET", "resourcePath": "/hospital/bills/reconciliation<PERSON><PERSON><PERSON>y"}, {"method": "GET", "resourcePath": "/hospital/bills/export"}, {"method": "GET", "resourcePath": "/hospital/bills/reconciliationSummaryWithMedicare"}, {"method": "GET", "resourcePath": "/hospital/bills/reconciliationSummaryWithMedicareNew"}, {"method": "GET", "resourcePath": "/hospital/bills/reconciliations"}, {"method": "GET", "resourcePath": "/hospital/bills/reconciliations/*"}, {"method": "GET", "resourcePath": "/hospital/bills/reset/*"}, {"method": "POST", "resourcePath": "/hospital/bills/reconciliations/*/resolve"}, {"method": "POST", "resourcePath": "/hospital/bills/reconciliations/*/flush"}, {"method": "GET", "resourcePath": "/hospital/bills/retryRefundRecords"}, {"method": "GET", "resourcePath": "/hospital/bills/raw_bills/wechat"}, {"method": "GET", "resourcePath": "/hospital/bills/raw_bills/alipay"}, {"method": "GET", "resourcePath": "/hospital/bills/summary/difference"}]}, {"code": "tkcsjl", "name": "退款重试记录", "resources": [{"method": "GET", "resourcePath": "/hospital/bills/reconciliation<PERSON><PERSON><PERSON>y"}, {"method": "GET", "resourcePath": "/hospital/bills/reconciliationSummaryWithMedicare"}, {"method": "GET", "resourcePath": "/hospital/bills/reconciliationSummaryWithMedicareNew"}, {"method": "GET", "resourcePath": "/hospital/bills/reconciliations"}, {"method": "GET", "resourcePath": "/hospital/bills/reconciliations/*"}, {"method": "POST", "resourcePath": "/hospital/bills/reconciliations/*/resolve"}, {"method": "POST", "resourcePath": "/hospital/bills/reconciliations/*/flush"}, {"method": "GET", "resourcePath": "/hospital/bills/retryRefundRecords"}]}, {"code": "myddcgl", "name": "满意度调查管理", "resources": [{"method": "POST", "resourcePath": "/hospital/crm/questionnaires"}, {"method": "PUT", "resourcePath": "/hospital/crm/questionnaires/*"}, {"method": "PUT", "resourcePath": "/hospital/crm/questionnaires/status/*"}, {"method": "PUT", "resourcePath": "/hospital/crm/questionnaires/start/*"}, {"method": "DELETE", "resourcePath": "/hospital/crm/questionnaires/*"}, {"method": "GET", "resourcePath": "/hospital/crm/questionnaires"}, {"method": "GET", "resourcePath": "/hospital/crm/questionnaires/*"}, {"method": "GET", "resourcePath": "/hospital/crm/questionnaires/answer"}, {"method": "GET", "resourcePath": "/hospital/crm/questionnaires/answer/*"}, {"method": "POST", "resourcePath": "/hospital/crm/questions"}, {"method": "PUT", "resourcePath": "/hospital/crm/questions/*"}, {"method": "PUT", "resourcePath": "/hospital/crm/questions/enabled/*"}, {"method": "DELETE", "resourcePath": "/hospital/crm/questions/*"}, {"method": "GET", "resourcePath": "/hospital/crm/questions"}, {"method": "GET", "resourcePath": "/hospital/crm/questions/defaulted"}, {"method": "POST", "resourcePath": "/hospital/crm/questionnaires/classify"}, {"method": "PUT", "resourcePath": "/hospital/crm/questionnaires/classify/*"}, {"method": "DELETE", "resourcePath": "/hospital/crm/questionnaires/classify/*"}, {"method": "GET", "resourcePath": "/hospital/crm/questionnaires/classify"}, {"method": "POST", "resourcePath": "/hospital/crm/questions/classify"}, {"method": "PUT", "resourcePath": "/hospital/crm/questions/classify/*"}, {"method": "DELETE", "resourcePath": "/hospital/crm/questions/classify/*"}, {"method": "GET", "resourcePath": "/hospital/crm/questions/classify"}, {"method": "POST", "resourcePath": "/hospital/crm/tags"}, {"method": "PUT", "resourcePath": "/hospital/crm/tags/*"}, {"method": "DELETE", "resourcePath": "/hospital/crm/tags/*"}, {"method": "GET", "resourcePath": "/hospital/crm/tags"}]}, {"code": "wjwh", "name": "问卷维护", "resources": []}, {"code": "wjsz", "name": "问卷设置", "resources": []}, {"code": "wjyy", "name": "问卷运营", "resources": [{"method": "GET", "resourcePath": "/hospital/crm/**"}, {"method": "POST", "resourcePath": "/hospital/crm/**"}, {"method": "PUT", "resourcePath": "/hospital/crm/**"}, {"method": "DELETE", "resourcePath": "/hospital/crm/**"}, {"method": "PATCH", "resourcePath": "/hospital/crm/**"}]}, {"code": "ysczb", "name": "医生出诊表", "resources": [{"method": "GET", "resourcePath": "/hospital/pat_service/doctor_schedules"}, {"method": "GET", "resourcePath": "/hospital/pat_service/scheduling_dept_list"}]}, {"code": "fygs", "name": "费用公示", "resources": [{"method": "GET", "resourcePath": "/hospital/pat_service/price_publicity_list"}]}, {"code": "bafyyygl", "name": "病案复印预约管理", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/orders"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/orders/id/*"}]}, {"code": "basy", "name": "病案首页", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/orders/statistics"}]}, {"code": "bagl", "name": "病案管理", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/orders"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/orders/*/logistics/info"}, {"method": "PUT", "resourcePath": "/hospital/medical_record_copy_appointment/orders/order_status_change"}, {"method": "GET", "resourcePath": "/hospital/quick_order/settings/scene/*"}]}, {"code": "badzgl", "name": "对账管理", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/orders/account_check"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/bills"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/bills/stats"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/bills/export"}]}, {"code": "batjfx", "name": "统计分析", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/statistics/duplicates/total/top"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/statistics/duplicates/top"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/statistics/duplicates/top/export"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/statistics/duplicates/total/bottom"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/statistics/copy_purpose"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/statistics/pay_fee"}, {"method": "GET", "resourcePath": "/hospital/statistics/operational_statistics/export"}, {"method": "GET", "resourcePath": "/hospital/statistics/financial/export"}]}, {"code": "baszgl", "name": "病案设置管理", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment_unit_price"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment_unit_price/history"}, {"method": "POST", "resourcePath": "/hospital/medical_record_copy_appointment_unit_price"}, {"method": "PUT", "resourcePath": "/hospital/medical_record_copy_appointment_unit_price"}, {"method": "PUT", "resourcePath": "/hospital/medical_record_copy_appointment_unit_price/*"}, {"method": "DELETE", "resourcePath": "/hospital/medical_record_copy_appointment_unit_price/*"}, {"method": "POST", "resourcePath": "/hospital/medical_record_copy_appointment/setting/copy_scope"}, {"method": "PUT", "resourcePath": "/hospital/medical_record_copy_appointment/setting/copy_scope"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/setting/copy_scope_list"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/setting/enable/copy_scope_list"}, {"method": "DELETE", "resourcePath": "/hospital/medical_record_copy_appointment/setting/copy_scope/*"}, {"method": "POST", "resourcePath": "/hospital/medical_record_copy_appointment/setting/copy_purpose"}, {"method": "PUT", "resourcePath": "/hospital/medical_record_copy_appointment/setting/copy_purpose"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/setting/copy_purpose_list"}, {"method": "DELETE", "resourcePath": "/hospital/medical_record_copy_appointment/setting/copy_purpose/*"}, {"method": "GET", "resourcePath": "/hospital/medical_record_copy_appointment/statistics/duplicates/top/export"}, {"method": "GET", "resourcePath": "/hospital/settings"}, {"method": "GET", "resourcePath": "/hospital/settings/*"}, {"method": "PUT", "resourcePath": "/hospital/settings/*"}]}, {"code": "hlwyytj", "name": "互联网医院统计", "resources": [{"method": "GET", "resourcePath": "/hospital/statistics/**"}, {"method": "GET", "resourcePath": "/hospital/depts"}, {"method": "GET", "resourcePath": "/hospital/medical_workers"}]}, {"code": "jgdp", "name": "监管大屏", "resources": [{"method": "GET", "resourcePath": "/hospital/statistics/**"}]}, {"code": "zljlgl", "name": "诊疗记录管理", "resources": []}, {"code": "wttjfx", "name": "问题统计分析", "resources": [{"method": "GET", "resourcePath": "/hospital/crm/putted/questionnaires"}, {"method": "GET", "resourcePath": "/hospital/crm/statistics/**"}, {"method": "GET", "resourcePath": "/hospital/crm/putted/questionnaires/questions"}, {"method": "GET", "resourcePath": "/hospital/crm/putted/hospital/questions"}]}, {"code": "wjtjfx", "name": "问卷统计分析", "resources": [{"method": "GET", "resourcePath": "/hospital/crm/putted/questionnaires"}, {"method": "GET", "resourcePath": "/hospital/crm/statistics/**"}]}, {"code": "yysjtj", "name": "运营数据统计分析", "resources": [{"method": "GET", "resourcePath": "/hospital/statistics/visits_count_total"}, {"method": "GET", "resourcePath": "/hospital/statistics/registrations_count_total"}, {"method": "GET", "resourcePath": "/hospital/statistics/hot_dept_ranking"}, {"method": "GET", "resourcePath": "/hospital/statistics/hot_doctor_ranking"}, {"method": "GET", "resourcePath": "/hospital/statistics/patient_age_distribution"}, {"method": "GET", "resourcePath": "/hospital/statistics/patient_gender_distribution"}, {"method": "GET", "resourcePath": "/hospital/statistics/satisfaction"}, {"method": "GET", "resourcePath": "/hospital/statistics/operational_statistics/export"}, {"method": "GET", "resourcePath": "/hospital/statistics/each_department_appointment"}]}, {"code": "srzhtj", "name": "收入综合统计", "resources": [{"method": "GET", "resourcePath": "/hospital/statistics/financial/list"}, {"method": "GET", "resourcePath": "/hospital/statistics/financial/export"}]}, {"code": "zljlgl", "name": "诊疗记录管理", "resources": [{"method": "GET", "resourcePath": "/hospital/chat/report/tasks"}, {"method": "GET", "resourcePath": "/hospital/orders"}, {"method": "POST", "resourcePath": "/hospital/chat/report/tasks"}, {"method": "DELETE", "resourcePath": "/hospital/chat/report/tasks/*"}]}, {"code": "wlxxpz", "name": "物流信息配置", "resources": [{"method": "GET", "resourcePath": "/hospital/quick_order/settings"}, {"method": "GET", "resourcePath": "/hospital/quick_order/settings/scene/*"}, {"method": "PUT", "resourcePath": "/hospital/quick_order/settings"}, {"method": "GET", "resourcePath": "/hospital/settings"}, {"method": "GET", "resourcePath": "/hospital/settings/*"}, {"method": "PUT", "resourcePath": "/hospital/settings/*"}, {"method": "PUT", "resourcePath": "/hospital/settings"}]}, {"name": "护士管理", "code": "hsgl", "resources": [{"method": "GET", "resourcePath": "/hospital/nursing/nurse_items"}, {"method": "POST", "resourcePath": "/hospital/nursing/nurse_items"}, {"method": "PUT", "resourcePath": "/hospital/nursing/nurse_items/*"}, {"method": "DELETE", "resourcePath": "/hospital/nursing/nurse_items/*"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/*/medical_credentials"}, {"method": "POST", "resourcePath": "/hospital/recommend_medical_workers"}, {"method": "DELETE", "resourcePath": "/hospital/recommend_medical_workers/**"}, {"method": "POST", "resourcePath": "/hospital/medical_workers"}, {"method": "PUT", "resourcePath": "/hospital/medical_workers/*"}, {"method": "PATCH", "resourcePath": "/hospital/medical_workers/*/enabled"}, {"method": "GET", "resourcePath": "/hospital/medical_workers/can_edit_username"}, {"method": "PATCH", "resourcePath": "/users/reset-password"}]}, {"name": "护理项目管理", "code": "hlxmgl", "resources": []}, {"name": "到家项目", "code": "djxm", "resources": [{"method": "GET", "resourcePath": "/hospital/nursing/item_classifies"}, {"method": "GET", "resourcePath": "/hospital/nursing/item_classifies/*"}, {"method": "POST", "resourcePath": "/hospital/nursing/item_classifies"}, {"method": "PUT", "resourcePath": "/hospital/nursing/item_classifies/*"}, {"method": "DELETE", "resourcePath": "/hospital/nursing/item_classifies/*"}, {"method": "PATCH", "resourcePath": "/hospital/nursing/item_classifies/*/showOrHide"}, {"method": "GET", "resourcePath": "/hospital/nursing/home_items"}, {"method": "POST", "resourcePath": "/hospital/nursing/home_items"}, {"method": "PUT", "resourcePath": "/hospital/nursing/home_items/*"}, {"method": "DELETE", "resourcePath": "/hospital/nursing/home_items/*"}]}, {"name": "咨询项目", "code": "zxxm", "resources": [{"method": "GET", "resourcePath": "/hospital/nursing/consul_items"}, {"method": "POST", "resourcePath": "/hospital/nursing/consul_items"}, {"method": "PUT", "resourcePath": "/hospital/nursing/consul_items/*"}, {"method": "DELETE", "resourcePath": "/hospital/nursing/consul_items/*"}, {"method": "PATCH", "resourcePath": "/hospital/nursing/consul_items/*/enabled"}, {"method": "GET", "resourcePath": "/hospital/register/schedules"}, {"method": "GET", "resourcePath": "/hospital/register/schedules/**"}, {"method": "POST", "resourcePath": "/hospital/register/schedules/**"}, {"method": "DELETE", "resourcePath": "/hospital/register/schedules/**"}, {"method": "GET", "resourcePath": "/register/schedules/medical_workers"}, {"method": "GET", "resourcePath": "/hospital/medical_workers"}, {"method": "PUT", "resourcePath": "/hospital/doctor/reception_parameters"}, {"method": "GET", "resourcePath": "/hospital/doctor/reception_parameters"}]}, {"name": "收费项目", "code": "sfxm", "resources": [{"method": "GET", "resourcePath": "/hospital/nursing/charge_items"}, {"method": "POST", "resourcePath": "/hospital/nursing/charge_items"}, {"method": "PUT", "resourcePath": "/hospital/nursing/charge_items/*"}, {"method": "DELETE", "resourcePath": "/hospital/nursing/charge_items/*"}, {"method": "PATCH", "resourcePath": "/hospital/nursing/charge_items/*/enabled"}]}, {"name": "医生端小程序", "code": "ysdxcx", "resources": []}, {"name": "护士长端", "code": "hszd", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_workers"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders/*"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders/home_count"}, {"method": "GET", "resourcePath": "/hospital/nursing/home_items/*/nurses"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/assign"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/reject"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/start"}, {"method": "GET", "resourcePath": "/hospital/orders/*/detail"}, {"method": "GET", "resourcePath": "/hospital/orders/medical_workers/*/orders_count"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/end"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/complete_consultation"}, {"method": "GET", "resourcePath": "hospital/doctor/message_template/list"}, {"method": "GET", "resourcePath": "/hospital/orders/*/medical_record"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/confirm_home"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/confirm_home/cancel"}]}, {"name": "护士端", "code": "hsd", "resources": [{"method": "GET", "resourcePath": "/hospital/medical_workers"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders/*"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders/home_count/my"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/start"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/operation"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/start_home"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/end_home"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/confirm_home"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/confirm_home/cancel"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/info"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/reject"}, {"method": "GET", "resourcePath": "/hospital/orders/*/detail"}, {"method": "GET", "resourcePath": "/hospital/orders/medical_workers/*/orders_count"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/end"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/complete_consultation"}, {"method": "GET", "resourcePath": "hospital/doctor/message_template/list"}, {"method": "GET", "resourcePath": "/hospital/orders/*/medical_record"}, {"method": "GET", "resourcePath": "/hospital/nursing/item_classifies"}]}, {"name": "智能医助医生端", "code": "znyzysd", "resources": [{"method": "GET", "resourcePath": "/hospital/hboc/**"}]}, {"name": "护理订单管理", "code": "hlddgl", "resources": []}, {"name": "护理订单", "code": "hldd", "resources": [{"method": "GET", "resourcePath": "/hospital/nursing_orders"}, {"method": "PUT", "resourcePath": "/hospital/nursing_orders/*/service_video"}, {"method": "POST", "resourcePath": "/hospital/nursing_orders/*/report_loss"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders/*"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders/*/operations"}]}, {"name": "订单对账", "code": "dddz", "resources": [{"method": "GET", "resourcePath": "/hospital/nursing_orders/bills"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders/bills/stats"}, {"method": "GET", "resourcePath": "/hospital/nursing_orders/bills/export"}]}]}]