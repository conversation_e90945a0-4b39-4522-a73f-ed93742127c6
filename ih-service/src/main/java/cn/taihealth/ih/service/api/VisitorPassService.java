package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.VisitorPass;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.dto.VisitorPassDTO;
import cn.taihealth.ih.service.vm.VisitorPassVM;

public interface VisitorPassService {


    /**
     * 添加患者陪护证
     * @param hospital
     * @param patient
     * @param vm
     */
    VisitorPassDTO saveVisitorPass(Hospital hospital, Patient patient, VisitorPassVM vm);


    /**
     * edit
     * @param orThrow
     * @param patient
     * @param visitorPassVM
     * @return
     */
    VisitorPassDTO editVisitorPass(Hospital orThrow, Patient patient, VisitorPassVM visitorPassVM);

    /**
     * 生成陪护证证书照片
     * @param visitorPass 陪护证
     * @return
     */
    Upload generateCertUpload(VisitorPass visitorPass);
}
