package cn.taihealth.ih.service.impl.medicalrecordcopyappointent;

import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.MathUtils;
import cn.taihealth.ih.commons.util.RedisUtil;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.UserAddress;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointment;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointment.CopyAppointmentOrderStatus;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointment.DeliveryMethod;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointmentOperation;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointmentOperation.Step;
import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointmentUnitPrice;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.OfflineOrder.OutPatientStatus;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.hospital.BillRepository;
import cn.taihealth.ih.repo.medicalrecordcopyappointment.*;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.express.ExpressService;
import cn.taihealth.ih.service.api.express.ExpressServiceStrategy;
import cn.taihealth.ih.service.api.medicalrecordcooyappointment.MedicalRecordCopyAppointmentService;
import cn.taihealth.ih.service.dto.export.CopyAppointmentExportDTO;
import cn.taihealth.ih.service.dto.express.ExpressData;
import cn.taihealth.ih.service.dto.express.ExpressData.EXPRESS100Config;
import cn.taihealth.ih.service.dto.express.ExpressData.SHUNFENGExpressConfig;
import cn.taihealth.ih.service.dto.express.LogisticsData;
import cn.taihealth.ih.service.dto.express.LogisticsRouteInfoDTO;
import cn.taihealth.ih.service.dto.express.shunfeng.CreateOrderRequest;
import cn.taihealth.ih.service.dto.express.shunfeng.CreateOrderResponse;
import cn.taihealth.ih.service.dto.hospital.ExpressSenderInfoDTO;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.MedicalRecordCopyScopeDTO;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.UserMedicalRecordCopyAppointmentDTO;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.UserMedicalRecordCopyAppointmentUnitPriceDTO;
import cn.taihealth.ih.service.impl.PayManager;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import cn.taihealth.ih.service.impl.filter.bill.BillSearch;
import cn.taihealth.ih.service.impl.filter.bill.CopyAppointmentFilter;
import cn.taihealth.ih.service.impl.filter.bill.OperateEndDateFilter;
import cn.taihealth.ih.service.impl.filter.bill.OperateStartDateFilter;
import cn.taihealth.ih.service.impl.filter.copyappointment.MedicalRecordCopyAppointmentSearch;
import cn.taihealth.ih.service.impl.filter.offline.order.OfflineOrderSearch;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.PageUtils;
import cn.taihealth.ih.service.util.express.shunfeng.SHUNFENGExpressUtils;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.*;
import cn.taihealth.ih.wechat.service.vm.RefundOrderVM;
import com.gitq.jedi.common.web.UrlUtils;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.criteria.*;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class MedicalRecordCopyAppointmentServiceImpl implements MedicalRecordCopyAppointmentService {

    private final UserMedicalRecordCopyAppointmentRepository userMedicalRecordCopyAppointmentRepository;
    private final UserMedicalRecordCopyAppointmentOperationRepository userMedicalRecordCopyAppointmentOperationRepository;
    private final OfflineOrderRepository offlineOrderRepository;
    private final LockService lockService;
    private final UserAddressRepository userAddressRepository;
    private final OfflineHospitalRepository offlineHospitalRepository;
    private final NoticeService noticeService;
    private final UploadRepository uploadRepository;
    private final RedisUtil redisUtil;
    private final UserMedicalRecordCopyAppointmentUnitPriceRepository userMedicalRecordCopyAppointmentUnitPriceRepository;
    private final WechatOrderRepository wechatOrderRepository;
    private final MedicalRecordCopyPurposeRepository medicalRecordCopyPurposeRepository;
    private final MedicalRecordCopyScopeRepository medicalRecordCopyScopeRepository;
    private final ExportTaskService exportTaskService;
    private final ExpressSenderInfoService expressSenderInfoService;
    private final BillRepository billRepository;
    private final PatientRepository patientRepository;
    private final NotifyService notifyService;

    public static Specification<UserMedicalRecordCopyAppointment> hasStatus() {
        List<CopyAppointmentOrderStatus> statusList = Arrays.asList(CopyAppointmentOrderStatus.WAIT_SEND, CopyAppointmentOrderStatus.WAIT_PICK_UP,
                CopyAppointmentOrderStatus.WAIT_SIGN, CopyAppointmentOrderStatus.PICK_UP, CopyAppointmentOrderStatus.DELIVER_SIGN);
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            for (CopyAppointmentOrderStatus status : statusList) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    @Override
    @Transactional
    public UserMedicalRecordCopyAppointmentDTO saveAppointment(Hospital hospital, User user, ElectronicMedicCard card, UserMedicalRecordCopyAppointmentDTO dto) {
        return createAppointment(hospital, user, card, dto);
    }

    @Override
    @Transactional
    public UserMedicalRecordCopyAppointmentDTO createAppointment(Hospital hospital, User user, ElectronicMedicCard card,
                                                                 UserMedicalRecordCopyAppointmentDTO dto) {
        UserMedicalRecordCopyAppointment copyAppointment = dto.toEntity();
        copyAppointment.setMedicalRecordCopyPurpose(medicalRecordCopyPurposeRepository.getById(dto.getCopyPurpose().getId()));
        List<Long> copyScopeIds = dto.getCopyScopes().stream()
                .map(MedicalRecordCopyScopeDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        copyAppointment.setCopyScopeIds(copyScopeIds);
        copyAppointment.setUser(user);
        copyAppointment.setHospital(hospital);
        copyAppointment.setPrePayPrice(HospitalSettingsHelper.getInt(hospital, HospitalSettingKey.COPY_APPOINTMENT_PAID_PRICE));
        if (DeliveryMethod.POSTAL_DELIVERY == dto.getDeliveryMethod()) {
            copyAppointment.setDeliveryInfo(userAddressRepository.getById(dto.getDeliveryInfo().getId()));
        }
        copyAppointment.setOfflineHospital(offlineHospitalRepository.getById(dto.getHospitalInfo().getId()));
        if (dto.getPortrait() != null && dto.getPortrait().getId() != null) {
            copyAppointment.setPortrait(uploadRepository.getById(dto.getPortrait().getId()));
        }
        if (dto.getNationalEmblem() != null && dto.getNationalEmblem().getId() != null) {
            copyAppointment.setNationalEmblem(uploadRepository.getById(dto.getNationalEmblem().getId()));
        }
        if (dto.getHoldingIdCard() != null && dto.getHoldingIdCard().getId() != null) {
            copyAppointment.setHoldingIdCard(uploadRepository.getById(dto.getHoldingIdCard().getId()));
        }
        operationCopyAppointmentOrder(user, copyAppointment, Step.SUBMIT, "用户提交订单");
        operationCopyAppointmentOrder(user, copyAppointment, Step.SUBMIT, "订单待支付");
        OfflineOrder offlineOrder = new OfflineOrder();
        offlineOrder.setHospital(hospital);
        offlineOrder.setPatientId(card.getPatient().getId() + "");
        offlineOrder.setElectronicMedicCard(card);
        offlineOrder.setStatus(OfflineOrder.OutPatientStatus.WAIT_PAY);
        offlineOrder.setType(ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT);
        offlineOrder.setRegistrationFee(HospitalSettingsHelper.getInt(hospital, HospitalSettingKey.COPY_APPOINTMENT_PAID_PRICE));
        offlineOrder.setSelfAmount(offlineOrder.getRegistrationFee());
        offlineOrder.setUserId(user.getId() + "");
        offlineOrder.setCopyAppointment(copyAppointment);
        offlineOrder.setDocumentItem("病案复印预约");
        offlineOrder.setEntryName("病案复印预约");
        offlineOrderRepository.save(offlineOrder);
        // 创建订单时设置订单支付过期时间
        redisUtil.set("medicalRecordCopyAppointment_expire:" + copyAppointment.getId(), 1, 10 * 60);
        return getUserMedicalRecordCopyAppointmentDTO(new UserMedicalRecordCopyAppointmentDTO(copyAppointment, offlineOrder.getId()), copyAppointment);
    }

    @Override
    @Transactional
    public UserMedicalRecordCopyAppointmentDTO appendAppointment(UserMedicalRecordCopyAppointment copyAppointment) {
        return lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
            List<OfflineOrder> offlineOrders = offlineOrderRepository.findByCopyAppointmentOrderByCreatedDateDesc(copyAppointment);
            if (CollectionUtils.isEmpty(offlineOrders)) {
                throw ErrorType.ILLEGAL_ORDER_STATUS.toProblem();
            }
            OfflineOrder nowOfflineOrder = offlineOrders.get(0);
            int paidPrice = copyAppointment.getPaidPrice();
            int totalPrice = copyAppointment.getTotalPrice();
            if (paidPrice >= totalPrice) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("当前订单不需要补缴");
            }
            int appendPrice = totalPrice - paidPrice;
            copyAppointment.setAppendPrice(appendPrice);
            OfflineOrder offlineOrder = new OfflineOrder();
            offlineOrder.setHospital(copyAppointment.getHospital());
            offlineOrder.setPatientId(nowOfflineOrder.getPatientId());
            offlineOrder.setElectronicMedicCard(nowOfflineOrder.getElectronicMedicCard());
            offlineOrder.setStatus(OfflineOrder.OutPatientStatus.WAIT_PAY);
            offlineOrder.setType(ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND);
            offlineOrder.setRegistrationFee(appendPrice);
            offlineOrder.setSelfAmount(appendPrice);
            offlineOrder.setUserId(nowOfflineOrder.getUserId());
            offlineOrder.setCopyAppointment(copyAppointment);
            offlineOrder.setDocumentItem("病案复印预约补缴");
            offlineOrder.setEntryName("病案复印预约补缴");
            offlineOrderRepository.save(offlineOrder);
            copyAppointment.setAppendPrice(appendPrice);
            sendNoticeCopyAppointmentMessage(copyAppointment, "您的订单已核算需补缴费用，请及时支付");
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.CALCULATE_APPEND_PAY, "订单待补缴");
            return getUserMedicalRecordCopyAppointmentDTO(new UserMedicalRecordCopyAppointmentDTO(copyAppointment, offlineOrder.getId()), copyAppointment);
        });
    }

    @Override
    @Transactional
    public void copyAppointmentPending(Long offlineOrderId) {
        lockService.executeWithLock("ih.copy_appointment." + offlineOrderId, () -> {
            OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
            if (offlineOrder.getStatus() == OutPatientStatus.PENDING) {
                return 0;
            }
            UserMedicalRecordCopyAppointment copyAppointment = offlineOrder.getCopyAppointment();
            if (offlineOrder.getStatus() == OutPatientStatus.PENDING) {
                return 0;
            }
            offlineOrder.setStatus(OutPatientStatus.PENDING);
            offlineOrderRepository.save(offlineOrder);
            // 这块有问题
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.PAY, "用户订单支付中");
            return 0;
        });
    }

    @Override
    @Transactional
    public void payCopyAppointment(User user, Long offlineOrderId, WechatOrder order) {
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        UserMedicalRecordCopyAppointment copyAppointment = offlineOrder.getCopyAppointment();
        if (offlineOrder.getStatus() == OutPatientStatus.CLOSED) {
            lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
                offlineOrder.setPayTime(order.getPayTime());
                copyAppointment.setPaidPrice(order.getTotalFee());
                copyAppointment.setPayTime(order.getPayTime());
                offlineOrderRepository.save(offlineOrder);
                userMedicalRecordCopyAppointmentRepository.save(copyAppointment);
                return 0;
            });
            log.info("病案复印预交费线下订单id: {}，订单已关闭，为用户办理退款", offlineOrderId);
            refundAppointment(user, offlineOrderId, Step.REFUND_PASS, "支付成功回调时订单已关闭，为用户办理退款");
        } else {
            lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
                offlineOrder.setPayTime(order.getPayTime());
                copyAppointment.setPaidPrice(order.getTotalFee());
                copyAppointment.setPayTime(order.getPayTime());
                userMedicalRecordCopyAppointmentRepository.save(copyAppointment);
                offlineOrder.setStatus(OfflineOrder.OutPatientStatus.REGISTERED);
                offlineOrderRepository.save(offlineOrder);
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.PAY, "订单支付成功");
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.PAY, "订单已支付待审核");
                return 0;
            });
        }
    }

    @Override
    @Transactional
    public void payCopyAppointment(User user, Long offlineOrderId, AliPayOrder order) {
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        UserMedicalRecordCopyAppointment copyAppointment = offlineOrder.getCopyAppointment();
        if (offlineOrder.getStatus() == OutPatientStatus.CLOSED) {
            lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
                offlineOrder.setPayTime(order.getGmtPayment());
                offlineOrder.setPaymentMethod(PaymentMethod.ALI_PAY);
                copyAppointment.setPaidPrice(order.getTotalFee());
                copyAppointment.setPayTime(order.getGmtPayment());
                offlineOrderRepository.save(offlineOrder);
                userMedicalRecordCopyAppointmentRepository.save(copyAppointment);
                return 0;
            });
            log.info("病案复印预交费线下订单id: {}，订单已关闭，为用户办理退款", offlineOrderId);
            refundAppointment(user, offlineOrderId, Step.REFUND_PASS, "支付成功回调时订单已关闭，为用户办理退款");
        } else {
            lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
                offlineOrder.setPayTime(order.getGmtPayment());
                offlineOrder.setPaymentMethod(PaymentMethod.ALI_PAY);
                copyAppointment.setPaidPrice(order.getTotalFee());
                copyAppointment.setPayTime(order.getGmtPayment());
                userMedicalRecordCopyAppointmentRepository.save(copyAppointment);
                offlineOrder.setStatus(OfflineOrder.OutPatientStatus.REGISTERED);
                offlineOrderRepository.save(offlineOrder);
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.PAY, "订单支付成功");
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.PAY, "订单已支付待审核");
                return 0;
            });
        }
    }

    @Override
    @Transactional
    public void appendCopyAppointmentPending(Long offlineOrderId) {
        lockService.executeWithLock("ih.copy_appointment." + offlineOrderId, () -> {
            OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
            if (offlineOrder.getStatus() == OutPatientStatus.PENDING) {
                return 0;
            }
            UserMedicalRecordCopyAppointment copyAppointment = offlineOrder.getCopyAppointment();
            offlineOrder.setStatus(OfflineOrder.OutPatientStatus.PENDING);
            offlineOrderRepository.save(offlineOrder);
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.APPEND_PAY, "用户补缴订单缺少的金额");
            return 0;
        });
    }

    @Override
    @Transactional
    public void payAppendCopyAppointment(User user, Long offlineOrderId, WechatOrder order) {
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        UserMedicalRecordCopyAppointment copyAppointment = offlineOrder.getCopyAppointment();
        lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
            offlineOrder.setStatus(OfflineOrder.OutPatientStatus.REGISTERED);
            offlineOrder.setPayTime(order.getPayTime());
            copyAppointment.setPaidPrice(order.getTotalFee() + copyAppointment.getPaidPrice());
            offlineOrderRepository.save(offlineOrder);
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.APPEND_PAY, "订单缺少的金额补缴成功");
            // 补缴成功根据邮寄的方式流转到待邮寄或者待自提状态
            if (DeliveryMethod.POSTAL_DELIVERY == copyAppointment.getDeliveryMethod()) {
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.START_SEND, "订单补缴成功等待邮寄");
            } else {
                sendNoticeCopyAppointmentMessage(copyAppointment, "您的病案已复印，请前往自提点自提");
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.START_PICK_UP, "订单补缴成功等待自提");
            }
            return 0;
        });
    }

    @Override
    @Transactional
    public void payAppendCopyAppointment(User user, Long offlineOrderId, AliPayOrder order) {
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        UserMedicalRecordCopyAppointment copyAppointment = offlineOrder.getCopyAppointment();
        lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
            offlineOrder.setStatus(OfflineOrder.OutPatientStatus.REGISTERED);
            offlineOrder.setPaymentMethod(PaymentMethod.ALI_PAY);
            offlineOrder.setPayTime(order.getGmtPayment());
            copyAppointment.setPaidPrice(order.getTotalFee() + copyAppointment.getPaidPrice());
            offlineOrderRepository.save(offlineOrder);
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.APPEND_PAY, "订单缺少的金额补缴成功");
            // 补缴成功根据邮寄的方式流转到待邮寄或者待自提状态
            if (DeliveryMethod.POSTAL_DELIVERY == copyAppointment.getDeliveryMethod()) {
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.START_SEND, "订单补缴成功等待邮寄");
            } else {
                sendNoticeCopyAppointmentMessage(copyAppointment, "您的病案已复印，请前往自提点自提");
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.START_PICK_UP, "订单补缴成功等待自提");
            }
            return 0;
        });
    }

    @Override
    @Transactional
    public UserMedicalRecordCopyAppointmentDTO refundAppointment(User user, Long offlineOrderId, Step step, String remarks) {
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        return lockService.executeWithLock("ih.copy_appointment." + offlineOrder.getCopyAppointment().getId(), () -> {
            UserMedicalRecordCopyAppointment copyAppointment = offlineOrder.getCopyAppointment();
            String remarksFinal = remarks;
            int refundAmount;
            if (Step.REFUND_PASS == step || Step.REVIEW_REJECT == step) {
                refundAmount = offlineOrder.getRegistrationFee();
                copyAppointment.setRefundType(RefundTypeEnum.REFUND);
            } else if (step == Step.REFUND_SURPLUS) {
                int totalPrice = copyAppointment.getTotalPrice();
                int paidPrice = copyAppointment.getPaidPrice();
                if (paidPrice <= totalPrice) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("当前订单不需要退差额");
                }
                refundAmount = paidPrice - totalPrice;
                copyAppointment.setRefundType(RefundTypeEnum.CHARGE_REFUND);
            } else {
                log.error("当前操作" + step +
                        "不支持退款: 找产品明确这个操作退款要变成什么退款形式 https://sunhealth.feishu.cn/docx/BZpXdwchooI0ZGxt7kucvPpIn2b 7.2对账管理");
                throw ErrorType.ILLEGAL_PARAMS.toProblem("当前操作" + step + "不支持退款");
            }
            copyAppointment.setRefundPrice(refundAmount);
            offlineOrder.setStatus(OfflineOrder.OutPatientStatus.REFUNDING);
            offlineOrderRepository.save(offlineOrder);
            operationCopyAppointmentOrder(user, copyAppointment, step, remarksFinal);

            RefundOrderVM refundVm = new RefundOrderVM();
            refundVm.setId(offlineOrderId);
            refundVm.setType(ThirdOrderType.MEDICAL_RECORD_COPY_APPOINTMENT);
            // 医保业务未实现
            boolean refund = AppContext.getInstance(PayManager.class).refund(offlineOrder.getHospital(),
                    false, offlineOrder.getPaymentMethod(), refundVm, refundAmount, offlineOrderId + "");
            if (refund) {
                log.info("退款：已向微信发起退款请求,病案复印缴费id: " + offlineOrderId);
            } else {
                log.info("退款：没有微信/支付宝订单，不需要去微信/支付宝退款，病案复印缴费id: " + offlineOrderId);
            }
            return new UserMedicalRecordCopyAppointmentDTO(copyAppointment);
        });
    }

    @Override
    @Transactional
    public OfflineOrder refundedAppointment(long offlineOrderId, WechatOrderRefund refund) {
        log.info("退款：退款成功，病案复印预交费线下订单id: " + offlineOrderId);
        OfflineOrder order = offlineOrderRepository.findByIdAndStatus(offlineOrderId, OfflineOrder.OutPatientStatus.REFUNDING)
                .orElseThrow(() -> ErrorType.BAD_REQUEST_ERROR.toProblem("退款中订单不存在"));
        UserMedicalRecordCopyAppointment copyAppointment = order.getCopyAppointment();
        order.setStatus(OfflineOrder.OutPatientStatus.REFUND);
        order.setRefundTime(new Date());
        order.setRefundTransactionId(refund.getTransactionId());
        order.setRefundFee(refund.getAmount());
        offlineOrderRepository.save(order);
        copyAppointment.setRefundPrice(refund.getAmount());
        copyAppointment.setRefundTime(new Date());
        if (CopyAppointmentOrderStatus.SURPLUS_REFUNDING == copyAppointment.getStatus()) {
            copyAppointment.setPaidPrice(copyAppointment.getPaidPrice() - refund.getAmount());
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.REFUND_SURPLUS, "订单差额退款成功");
            if (DeliveryMethod.POSTAL_DELIVERY == copyAppointment.getDeliveryMethod()) {
                //等待邮寄
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.START_SEND, "订单等待邮寄");
            } else {
                //等待自提
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.START_PICK_UP, "订单等待自提");
                sendNoticeCopyAppointmentMessage(copyAppointment, "您的病案已复印，请前往自提点自提");
            }
        } else {
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.REFUND_PASS, "订单退款成功");
        }
        return order;
    }

    @Override
    public OfflineOrder refundedAppointment(long offlineOrderId, AliPayOrderRefund refund) {
        log.info("退款：退款成功，病案复印预交费线下订单id: " + offlineOrderId);
        OfflineOrder order = offlineOrderRepository.findByIdAndStatus(offlineOrderId, OfflineOrder.OutPatientStatus.REFUNDING)
            .orElseThrow(() -> ErrorType.BAD_REQUEST_ERROR.toProblem("退款中订单不存在"));
        UserMedicalRecordCopyAppointment copyAppointment = order.getCopyAppointment();
        order.setStatus(OfflineOrder.OutPatientStatus.REFUND);
        order.setRefundTime(new Date());
        order.setRefundTransactionId(refund.getTradeNo());
        order.setRefundFee(refund.getAmount());
        offlineOrderRepository.save(order);
        copyAppointment.setRefundPrice(refund.getAmount());
        copyAppointment.setRefundTime(new Date());
        if (CopyAppointmentOrderStatus.SURPLUS_REFUNDING == copyAppointment.getStatus()) {
            copyAppointment.setPaidPrice(copyAppointment.getPaidPrice() - refund.getAmount());
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.REFUND_SURPLUS, "订单差额退款成功");
            if (DeliveryMethod.POSTAL_DELIVERY == copyAppointment.getDeliveryMethod()) {
                //等待邮寄
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.START_SEND, "订单等待邮寄");
            } else {
                //等待自提
                operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.START_PICK_UP, "订单等待自提");
                sendNoticeCopyAppointmentMessage(copyAppointment, "您的病案已复印，请前往自提点自提");
            }
        } else {
            operationCopyAppointmentOrder(copyAppointment.getUser(), copyAppointment, Step.REFUND_PASS, "订单退款成功");
        }
        return order;
    }

    @Override
    @Transactional
    public void cancelAppointment(User user, UserMedicalRecordCopyAppointment copyAppointment) {
        lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
            CopyAppointmentOrderStatus status = copyAppointment.getStatus();
            if (!CopyAppointmentOrderConstants.CAN_CANCEL_ORDER.contains(status)) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("当前订单不可取消");
            }
            List<OfflineOrder> offlineOrders = offlineOrderRepository.findByCopyAppointmentOrderByCreatedDateDesc(copyAppointment);
            if (CollectionUtils.isEmpty(offlineOrders)) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("当前订单不可取消");
            }
            OfflineOrder offlineOrder = offlineOrders.get(0);
            offlineOrder.setStatus(OfflineOrder.OutPatientStatus.CANCELLED);
            offlineOrderRepository.save(offlineOrder);
            operationCopyAppointmentOrder(user, copyAppointment, Step.CANCEL_PAY, "用户取消订单");
            return 0;
        });
    }

    @Override
    @Transactional
    public void applyRefundAppointment(User user, UserMedicalRecordCopyAppointment copyAppointment) {
        lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
            CopyAppointmentOrderStatus status = copyAppointment.getStatus();
            if (!CopyAppointmentOrderConstants.CAN_REFUND_ORDER.contains(status)) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("当前订单不可退款");
            }
            operationCopyAppointmentOrder(user, copyAppointment, Step.APPLY_REFUND, "用户申请退款");
            return 0;
        });
    }

    @Override
    @Transactional
    public void receivedAppointment(User user, UserMedicalRecordCopyAppointment copyAppointment) {
        lockService.executeWithLock("ih.copy_appointment." + copyAppointment.getId(), () -> {
            CopyAppointmentOrderStatus status = copyAppointment.getStatus();
            if (CopyAppointmentOrderConstants.CAN_RECEIVED_ORDER.contains(status)) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("订单状态不正确");
            }
            if (DeliveryMethod.POSTAL_DELIVERY == copyAppointment.getDeliveryMethod()) {
                operationCopyAppointmentOrder(user, copyAppointment, Step.RECEIPTED, "订单已自提");
            } else {
                operationCopyAppointmentOrder(user, copyAppointment, Step.RECEIPTED, "订单已签收");
            }
            return 0;
        });
    }

    @Override
    public Page<UserMedicalRecordCopyAppointmentDTO> queryAppointment(Hospital hospital, User user, String query, Pageable page) {
        List<Specification<UserMedicalRecordCopyAppointment>> specs = createSpecifications(hospital, query);
        if (user != null) {
            specs.add(Specifications.eq("user", user));
        }
        return userMedicalRecordCopyAppointmentRepository.findAll(Specifications.and(specs), page).map(recordCopyAppointment ->
                getUserMedicalRecordCopyAppointmentDTO(new UserMedicalRecordCopyAppointmentDTO(recordCopyAppointment), recordCopyAppointment));
    }

    @Override
    public CopyAppointmentAccountCheckVM getCopyAppointmentAccountCheck(Hospital hospital, String query, Pageable page) {

        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        List<Specification<OfflineOrder>> typeSpecs = Lists.newArrayList();
        typeSpecs.add(Specifications.eq("type", ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT));
        typeSpecs.add(Specifications.eq("type", ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND));
        specs.add(Specifications.or(typeSpecs));
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.orderBy(Sort.Direction.DESC, "createdDate"));
        specs.add(OfflineOrderSearch.of(query).toSpecification());
        Page<OfflineOrder> offlineOrderPage = offlineOrderRepository.findAll(Specifications.and(specs), page);
        List<ThirdOrderType> typeList = Lists.newArrayList();
        typeList.add(ThirdOrderType.MEDICAL_RECORD_COPY_APPOINTMENT);
        typeList.add(ThirdOrderType.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND);
        List<String> ids = offlineOrderPage.getContent().stream().map(o -> o.getId() + "").collect(Collectors.toList());
        Map<String, WechatOrder> map = wechatOrderRepository.findAllByWechatOrderTypeInAndProductIdIn(typeList, ids).stream()
                .filter(WechatOrder::getPayed)
                .collect(Collectors.toMap(WechatOrder::getProductId, v -> v, (k1, k2) -> k2));
        Page<UserMedicalRecordCopyAppointmentDTO> copyAppointmentDTOs = offlineOrderPage
                .map(offlineOrder -> {
                    UserMedicalRecordCopyAppointmentDTO dto = new UserMedicalRecordCopyAppointmentDTO(offlineOrder.getCopyAppointment(), offlineOrder.getId());
                    OutPatientStatus status = offlineOrder.getStatus();
                    dto.setPayStatus(status);
                    dto.setPaidPrice(offlineOrder.getRegistrationFee());
                    dto.setOfflineOrderPrice(offlineOrder.getRegistrationFee());
                    if (status == OutPatientStatus.PENDING || status == OutPatientStatus.WAIT_PAY) {
                        dto.setPayTime(null);
                        dto.setPaidPrice(0);
                    }
                    if (map.containsKey(offlineOrder.getId() + "")) {
                        dto.setOutTradeNo(map.get(offlineOrder.getId() + "").getOutTradeNo());
                    }
                    List<MedicalRecordCopyScopeDTO> medicalRecordCopyScopeDTOS = offlineOrder.getCopyAppointment()
                            .getCopyScopeIds().stream()
                            .map(scopeId -> new MedicalRecordCopyScopeDTO(medicalRecordCopyScopeRepository.getById(scopeId)))
                            .collect(Collectors.toList());
                    dto.setCopyScopes(medicalRecordCopyScopeDTOS);
                    return dto;
                });
        CopyAppointmentAccountCheckVM vm = new CopyAppointmentAccountCheckVM();

        Integer paidTotal = getTotalRegistrationFee(specs);
        Integer refundTotal = getTotalRefundFee(specs);
        long revenueTotal = paidTotal - refundTotal;
        vm.setPage(copyAppointmentDTOs);
        vm.setOrderTotal(copyAppointmentDTOs.getTotalElements());
        vm.setPaidTotal(paidTotal);
        vm.setRefundTotal(refundTotal);
        vm.setRevenueTotal(revenueTotal);
        vm.setDiffTotal(revenueTotal - (paidTotal - refundTotal));
        return vm;
    }

    private Integer getTotalRegistrationFee(List<Specification<OfflineOrder>> specs) {
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Integer> criteriaQuery = criteriaBuilder.createQuery(Integer.class);
        Root<OfflineOrder> root = criteriaQuery.from(entityManager.getMetamodel().entity(OfflineOrder.class));

        Predicate where = Specifications.and(specs).and(Specifications.isNotNull("payTime")).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        Expression<Integer> expression = criteriaBuilder.coalesce(root.get("registrationFee"), 0);
        criteriaQuery.select(criteriaBuilder.sum(expression));
        List<Integer> result = entityManager.createQuery(criteriaQuery).getResultList();
        if (result != null && !result.isEmpty()) {
            Integer sum = result.get(0);
            return sum != null ? sum : 0;
        } else {
            return 0;
        }
    }

    private Integer getTotalRefundFee(List<Specification<OfflineOrder>> specs) {
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Integer> criteriaQuery = criteriaBuilder.createQuery(Integer.class);
        Root<OfflineOrder> root = criteriaQuery.from(entityManager.getMetamodel().entity(OfflineOrder.class));

        Predicate where = Specifications.and(specs).and(Specifications.isNotNull("refundFee")).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        criteriaQuery.select(criteriaBuilder.sum(root.get("refundFee")));
        List<Integer> result = entityManager.createQuery(criteriaQuery).getResultList();
        if (result != null && !result.isEmpty()) {
            Integer sum = result.get(0);
            return sum != null ? sum : 0;
        } else {
            return 0;
        }
    }

    @Override
    @Transactional
    public UserMedicalRecordCopyAppointmentDTO operateMedicalRecordAppointmentOrder(Hospital hospital, User user, UserMedicalRecordCopyAppointmentDTO param) {
        UserMedicalRecordCopyAppointment recordCopyAppointment = userMedicalRecordCopyAppointmentRepository.getById(param.getId());
        return lockService.executeWithLock("ih.operate_appointment." + recordCopyAppointment.getId(), () -> {
            List<OfflineOrder> offlineOrders = offlineOrderRepository.findByCopyAppointmentOrderByCreatedDateDesc(recordCopyAppointment);
            if (CollectionUtils.isEmpty(offlineOrders)) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("订单状态不正确");
            }
            String remarks = null;
            switch (param.getStep()) {
                case REVIEW_PASS:
                    remarks = "已支付的订单审核通过";
                    Date now = new Date();
                    UserMedicalRecordCopyAppointmentUnitPrice unitPrice = userMedicalRecordCopyAppointmentUnitPriceRepository.
                            findFirstByHospitalOrderByCreatedDateDesc(hospital).stream()
                            .filter(price -> now.before(price.getEffectiveTimeEnd()))
                            .filter(price -> now.after(price.getEffectiveTimeStart()))
                            .filter(UserMedicalRecordCopyAppointmentUnitPrice::isEnabled)
                            .findFirst().orElseThrow(() -> ErrorType.DATA_NOT_AVAILABLE.toProblem("目前没有可用的病案复印单价，请先去设置"));
                    recordCopyAppointment.setUnitPrice(unitPrice.getUnitPrice());
                    recordCopyAppointment.setAuditUser(user);
                    recordCopyAppointment.setAuditDate(now);
                    break;
                case REVIEW_REJECT:
                    if (StringUtils.isBlank(param.getRejectReason())) {
                        throw ErrorType.ILLEGAL_PARAMS.toProblem("驳回原因不能为空");
                    }
                    recordCopyAppointment.setRejectReason(param.getRejectReason());
                    remarks = "已支付的订单驳回并退款";
                    sendNoticeCopyAppointmentMessage(recordCopyAppointment, "您的病案复印申请未通过，费用将原路退回");
                    return refundAppointment(user, offlineOrders.get(0).getId(), Step.REVIEW_REJECT, remarks);
                case REFUND_PASS:
                    sendNoticeCopyAppointmentMessage(recordCopyAppointment, "您的退款申请已通过，费用将原路退回");
                    remarks = "用户申请退款的订单同意退款";
                    return refundAppointment(user, offlineOrders.get(0).getId(), Step.REFUND_PASS, remarks);
                case REFUND_REJECT:
                    if (StringUtils.isBlank(param.getRefundRejectReason())) {
                        throw ErrorType.ILLEGAL_PARAMS.toProblem("拒绝退款原因不能为空");
                    }
                    sendNoticeCopyAppointmentMessage(recordCopyAppointment, "您的退款申请未通过，可在订单中查看详情");
                    recordCopyAppointment.setRefundRejectReason(param.getRefundRejectReason());
                    remarks = "用户申请退款的订单拒绝退款";
                    break;
                case COPY_PASS:
                    remarks = "订单复印通过";
                    break;
                case CALCULATE:
                    recordCopyAppointment.setPageTotal(param.getPageTotal());
                    recordCopyAppointment.setTotalPrice(recordCopyAppointment.getUnitPrice() * param.getPageTotal());
                    userMedicalRecordCopyAppointmentRepository.save(recordCopyAppointment);
                    if (recordCopyAppointment.getTotalPrice() == recordCopyAppointment.getPaidPrice()) {
                        if (recordCopyAppointment.getDeliveryMethod() == DeliveryMethod.POSTAL_DELIVERY) {
                            param.setStep(Step.START_SEND);
                            remarks = "订单金额刚好待邮寄";
                        } else {
                            param.setStep(Step.START_PICK_UP);
                            sendNoticeCopyAppointmentMessage(recordCopyAppointment, "您的病案已复印，请前往自提点自提");
                            remarks = "订单金额刚好待自提";
                        }
                    } else if (recordCopyAppointment.getTotalPrice() < recordCopyAppointment.getPaidPrice()) {
                        sendNoticeCopyAppointmentMessage(recordCopyAppointment, "您的订单已核算，多余费用将原路退回");
                        return refundAppointment(user, offlineOrders.get(0).getId(), Step.REFUND_SURPLUS, "发起订单差额退款");
                    } else {
                        return appendAppointment(recordCopyAppointment);
                    }
                    break;
                case DELIVER:
                    sendNoticeCopyAppointmentMessage(recordCopyAppointment, "您的病案已邮寄，请注意查收");
                    LogisticsData logisticsData = quickOrder(hospital, recordCopyAppointment, param);
                    recordCopyAppointment.setLogisticsData(JSONUtil.toJsonStr(logisticsData));
                    remarks = "订单已发货，待签收";
                    break;
                default:
                    break;
            }
            operationCopyAppointmentOrder(user, recordCopyAppointment, param.getStep(), remarks);
            return new UserMedicalRecordCopyAppointmentDTO(recordCopyAppointment);
        });
    }

    /**
     * 发送病案复印预约小程序消息
     *
     * @param recordCopyAppointment 对应的病案复印订单记录
     * @param desc                  消息内应的描述内容
     *                              <p>
     *                              其中Map对应的参数的意思是：
     *                              patient:    订单中患者的名字
     *                              hospital:   订单中医院的名字
     *                              desc:       订单中需要告知用户的内容
     *                              dept:       订单中科室的名字，产品说这里暂时置空
     *                              path:       小程序消息跳转的前端路径
     */
    private void sendNoticeCopyAppointmentMessage(UserMedicalRecordCopyAppointment recordCopyAppointment, String desc) {
        Map<String, String> detail = Maps.newHashMap();
        detail.put("patient", recordCopyAppointment.getPatientName());
        detail.put("hospital", recordCopyAppointment.getHospital().getName());
        detail.put("desc", desc);
        detail.put("dept", "-");
        detail.put("path", "subpackages/pat/archive-print/print-order/index");
        detail.put("notice_content", "您好，" + recordCopyAppointment.getPatientName() + "， " + desc);
        detail.put("product_id", recordCopyAppointment.getId() + "");
        noticeService.miniProgramNotice(recordCopyAppointment.getHospital(), recordCopyAppointment.getUser(),
                HospitalSettingKey.NOTICE_COPY_APPOINTMENT, detail);
    }

    private LogisticsData quickOrder(Hospital hospital, UserMedicalRecordCopyAppointment recordCopyAppointment, UserMedicalRecordCopyAppointmentDTO param) {
        ExpressType expressType = param.getLogisticsData().getExpressType();
        LogisticsData logisticsData = new LogisticsData();
        // 选择了快速下单并选择了顺丰速运
        if (param.isPreOrderFlg() && expressType == ExpressType.SHUNFENG) {
            ExpressData expressData = getExpressSettings(hospital, expressType);
            ExpressData.SHUNFENGExpressConfig config = expressData.getShunfengExpressConfig();
            if (config.isPreOrderFlg()) {
                // 校验寄件方参数
                if (StringUtils.isBlank(config.getProvince()) || StringUtils.isBlank(config.getCity())
                        || StringUtils.isBlank(config.getAddress()) || StringUtils.isBlank(config.getTel())) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("请检查寄件方省，市，详细地址，手机号是否已经填写");
                }
                // 收件方信息
                UserAddress deliveryInfo = recordCopyAppointment.getDeliveryInfo();
                // 校验收件方参数
                if (StringUtils.isBlank(deliveryInfo.getProvince()) || StringUtils.isBlank(deliveryInfo.getCity())
                        || StringUtils.isBlank(deliveryInfo.getAddress()) || StringUtils.isBlank(deliveryInfo.getTelephone())) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("请检查收件方省，市，详细地址，手机号是否已经填写");
                }

                CreateOrderRequest createOrderRequest = new CreateOrderRequest();
                createOrderRequest.setMerchantOrderNo(recordCopyAppointment.getId() + "");
                // 快递形式
                createOrderRequest.setExpressType(config.getExpressTypeId());
                // 问了顺丰的人，这里填0，具体原因他们也没有回，这个具体是干嘛的也不知道
                createOrderRequest.setCallFlag("0");
                createOrderRequest.setProductCode("MEDICAL_CHART");

                CreateOrderRequest.ContactInfo contactInfo = new CreateOrderRequest.ContactInfo();
                contactInfo.setSrcName(config.getName());
                contactInfo.setSrcPhone(config.getTel());
                contactInfo.setSrcProvince(config.getProvince());
                contactInfo.setSrcCity(config.getCity());
                contactInfo.setSrcDistrict(config.getCountry());
                contactInfo.setSrcAddress(config.getAddress());

                contactInfo.setDestName(deliveryInfo.getReceiverName());
                contactInfo.setDestPhone(deliveryInfo.getTelephone());
                contactInfo.setDestProvince(deliveryInfo.getProvince());
                contactInfo.setDestCity(deliveryInfo.getCity());
                contactInfo.setDestDistrict(deliveryInfo.getArea());
                contactInfo.setDestAddress(deliveryInfo.getAddress());
                createOrderRequest.setContactInfo(contactInfo);

                CreateOrderResponse result = SHUNFENGExpressUtils.createOrder(config, createOrderRequest);
                if (result != null) {
                    logisticsData.setExpressType(param.getLogisticsData().getExpressType());
                    logisticsData.setCompanyName(param.getLogisticsData().getExpressType().getName());
                    logisticsData.setLogisticsNo(result.getMailNo());
                }
            } else {
                throw ErrorType.MONTHLY_PAY_SWITCH.toProblem();
            }
        } else {
            logisticsData.setExpressType(param.getLogisticsData().getExpressType());
            logisticsData.setCompanyName(param.getLogisticsData().getExpressType().getName());
            logisticsData.setLogisticsNo(param.getLogisticsData().getLogisticsNo());
        }
        return logisticsData;
    }

    @Override
    public List<LogisticsRouteInfoDTO> getLogisticsInfo(Hospital hospital, long id) {
        UserMedicalRecordCopyAppointment orderInfo = userMedicalRecordCopyAppointmentRepository.getById(id);
        LogisticsData logisticsData = JSONUtil.toBean(orderInfo.getLogisticsData(), LogisticsData.class);
        if (StringUtils.isBlank(logisticsData.getLogisticsNo())) {
            throw ErrorType.ILLEGAL_STATE.toProblem("当前订单没有物流信息");
        }
        ExpressData expressData = getExpressSettings(hospital, logisticsData.getExpressType());
        ExpressService expressService = ExpressServiceStrategy.getInstance().getStrategy(expressData);
        return expressService.getLogisticsRouteInfos(logisticsData);
    }

    @Override
    public StatisticsOrdersVM getStatisticsOrders(Hospital hospital) {
        StatisticsOrdersVM vm = new StatisticsOrdersVM();
        assembleTodayReviewTotal(vm, hospital);
        assemblePickTotal(vm, hospital);
        assembleDeliverTotal(vm, hospital);
        assembleRefundTotal(vm, hospital);
        return vm;
    }

    @Override
    public UserMedicalRecordCopyAppointmentDTO getAppointmentInfo(long copyAppointmentId) {
        UserMedicalRecordCopyAppointment copyAppointment = userMedicalRecordCopyAppointmentRepository.getById(copyAppointmentId);
        List<OfflineOrder> offlineOrders = offlineOrderRepository.findByCopyAppointmentOrderByCreatedDateDesc(copyAppointment);
        if (CollectionUtils.isEmpty(offlineOrders)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("订单状态不正确");
        }
        return getUserMedicalRecordCopyAppointmentDTO(new UserMedicalRecordCopyAppointmentDTO(copyAppointment, offlineOrders.get(0).getId()), copyAppointment);
    }

    private void assembleTodayReviewTotal(StatisticsOrdersVM vm, Hospital hospital) {
        Date startOfDay = TimeUtils.getStartOfDay(new Date());
        //1.审核订单
        //1.1今日所提交病案复印待审核的订单数量
        List<Specification<UserMedicalRecordCopyAppointment>> waitingReviewSpecs = Lists.newArrayList();
        waitingReviewSpecs.add(Specifications.ge("createdDate", startOfDay));
        waitingReviewSpecs.add(Specifications.eq("status", CopyAppointmentOrderStatus.WAITING_REVIEW));
        waitingReviewSpecs.add(Specifications.eq("hospital", hospital));
        vm.setWaitingReviewTotal(userMedicalRecordCopyAppointmentRepository.count(Specifications.and(waitingReviewSpecs)));
        //1.2今日已审核的病案复印订单数量
        List<Specification<UserMedicalRecordCopyAppointment>> todayPassSpecs = Lists.newArrayList();
        todayPassSpecs.add(Specifications.ge("operations.createdDate", startOfDay));
        todayPassSpecs.add(Specifications.eq("operations.step", Step.REVIEW_PASS));
        todayPassSpecs.add(Specifications.eq("hospital", hospital));
        vm.setTodayPassTotal(userMedicalRecordCopyAppointmentRepository.count(Specifications.and(todayPassSpecs)));
        //1.3今日已驳回的病案复印订单数量
        List<Specification<UserMedicalRecordCopyAppointment>> todayRejectSpecs = Lists.newArrayList();
        todayRejectSpecs.add(Specifications.ge("operations.createdDate", startOfDay));
        todayRejectSpecs.add(Specifications.eq("operations.step", Step.REVIEW_REJECT));
        todayRejectSpecs.add(Specifications.eq("hospital", hospital));
        vm.setTodayRejectTotal(userMedicalRecordCopyAppointmentRepository.count(Specifications.and(todayRejectSpecs)));
    }

    private void assemblePickTotal(StatisticsOrdersVM vm, Hospital hospital) {
        // 2. 自提订单
        List<Specification<UserMedicalRecordCopyAppointment>> pickSpecs = Lists.newArrayList();
        pickSpecs.add(Specifications.eq("deliveryMethod", DeliveryMethod.SELF_PICKUP));
        pickSpecs.add(Specifications.eq("hospital", hospital));
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<UserMedicalRecordCopyAppointment> root = criteriaQuery.from(entityManager.getMetamodel()
                .entity(UserMedicalRecordCopyAppointment.class));
        criteriaQuery.groupBy(root.get("status"));
        Predicate where = Specifications.and(pickSpecs).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        criteriaQuery.multiselect(root.get("status"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        Map<CopyAppointmentOrderStatus, Long> pickMap = result.stream()
                .collect(Collectors.toMap(u -> u.get(0, CopyAppointmentOrderStatus.class), u -> u.get(1, Long.class)));
        vm.setPickWaitCopyTotal(pickMap.getOrDefault(CopyAppointmentOrderStatus.WAITING_COPY, 0L));
        vm.setPickWaitCalculateTotal(pickMap.getOrDefault(CopyAppointmentOrderStatus.WAITING_ACCOUNTING, 0L));
        vm.setPickWaitAppendTotal(pickMap.getOrDefault(CopyAppointmentOrderStatus.WAITING_APPEND, 0L));
        vm.setPickWaitPickTotal(pickMap.getOrDefault(CopyAppointmentOrderStatus.WAIT_PICK_UP, 0L));
        vm.setPickHasPickupTotal(pickMap.getOrDefault(CopyAppointmentOrderStatus.PICK_UP, 0L));
    }

    private void assembleDeliverTotal(StatisticsOrdersVM vm, Hospital hospital) {
        // 3. 邮寄订单
        List<Specification<UserMedicalRecordCopyAppointment>> deliverSpecs = Lists.newArrayList();
        deliverSpecs.add(Specifications.eq("deliveryMethod", DeliveryMethod.POSTAL_DELIVERY));
        deliverSpecs.add(Specifications.eq("hospital", hospital));
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<UserMedicalRecordCopyAppointment> root = criteriaQuery.from(entityManager.getMetamodel()
                .entity(UserMedicalRecordCopyAppointment.class));
        criteriaQuery.groupBy(root.get("status"));
        Predicate where = Specifications.and(deliverSpecs).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        criteriaQuery.multiselect(root.get("status"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        Map<CopyAppointmentOrderStatus, Long> deliverMap = result.stream()
                .collect(Collectors.toMap(u -> u.get(0, CopyAppointmentOrderStatus.class), u -> u.get(1, Long.class)));
        vm.setSendWaitCopyTotal(deliverMap.getOrDefault(CopyAppointmentOrderStatus.WAITING_COPY, 0L));
        vm.setSendWaitCalculateTotal(deliverMap.getOrDefault(CopyAppointmentOrderStatus.WAITING_ACCOUNTING, 0L));
        vm.setSendWaitAppendTotal(deliverMap.getOrDefault(CopyAppointmentOrderStatus.WAITING_APPEND, 0L));
        vm.setSendWaitSendTotal(deliverMap.getOrDefault(CopyAppointmentOrderStatus.WAIT_SEND, 0L));
        vm.setSendWaitDeliverTotal(deliverMap.getOrDefault(CopyAppointmentOrderStatus.WAIT_SIGN, 0L));
        vm.setSendHasDeliverTotal(deliverMap.getOrDefault(CopyAppointmentOrderStatus.DELIVER_SIGN, 0L));
    }

    private void assembleRefundTotal(StatisticsOrdersVM vm, Hospital hospital) {
        // 4. 退款订单
        List<Specification<UserMedicalRecordCopyAppointment>> rejectRefundSpecs = Lists.newArrayList();
        rejectRefundSpecs.add(Specifications.eq("operations.step", Step.REFUND_REJECT));
        rejectRefundSpecs.add(Specifications.eq("hospital", hospital));
        vm.setRejectRefundTotal(userMedicalRecordCopyAppointmentRepository.count(Specifications.and(rejectRefundSpecs)));
        List<Specification<UserMedicalRecordCopyAppointment>> refundSpecs = Lists.newArrayList();
        refundSpecs.add(Specifications.eq("hospital", hospital));
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<UserMedicalRecordCopyAppointment> root = criteriaQuery.from(entityManager.getMetamodel()
                .entity(UserMedicalRecordCopyAppointment.class));
        criteriaQuery.groupBy(root.get("status"));
        Predicate where = Specifications.and(refundSpecs).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        criteriaQuery.multiselect(root.get("status"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        Map<CopyAppointmentOrderStatus, Long> refundMap = result.stream()
                .collect(Collectors.toMap(u -> u.get(0, CopyAppointmentOrderStatus.class), u -> u.get(1, Long.class)));
        vm.setWaitingRefundTotal(refundMap.getOrDefault(CopyAppointmentOrderStatus.APPLY_REFUNDING, 0L));
        vm.setHasRefundTotal(refundMap.getOrDefault(CopyAppointmentOrderStatus.REFUNDED, 0L));
    }

    public void operationCopyAppointmentOrder(User user, UserMedicalRecordCopyAppointment copyAppointment,
                                              Step step, String remarks) {
        CopyAppointmentOrderStatus preStatus = copyAppointment.getStatus();
        CopyAppointmentOrderStatus nextStatus = getNextStatus(copyAppointment, step);
        copyAppointment.setStatus(nextStatus);
        userMedicalRecordCopyAppointmentRepository.save(copyAppointment);
        createOperation(user, copyAppointment, preStatus, step, nextStatus, remarks);
    }

    private void createOperation(User user, UserMedicalRecordCopyAppointment copyAppointment,
                                 CopyAppointmentOrderStatus preStatus, Step step,
                                 CopyAppointmentOrderStatus nextStatus, String remarks) {
        UserMedicalRecordCopyAppointmentOperation operation = new UserMedicalRecordCopyAppointmentOperation();
        operation.setUser(user);
        operation.setUserMedicalRecordCopyAppointment(copyAppointment);
        operation.setPreStatus(preStatus);
        operation.setStep(step);
        operation.setNextStatus(nextStatus);
        operation.setRemarks(remarks);
        userMedicalRecordCopyAppointmentOperationRepository.save(operation);
    }

    private CopyAppointmentOrderStatus getNextStatus(UserMedicalRecordCopyAppointment copyAppointment, Step step) {
        CopyAppointmentOrderStatus nextStatus = CopyAppointmentOrderStep.ACTION_STEPS.get(step).get(copyAppointment.getStatus());
        if (nextStatus == null) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("订单状态不正确");
        }
        return nextStatus;
    }

    public ExpressData getExpressSettings(Hospital hospital, ExpressType type) {
        ExpressSenderInfoDTO senderInfo = expressSenderInfoService.getSceneInfo(hospital, ExpressScene.MEDICAL_RECORD_COPY_ORDER);
        ExpressData expressData = new ExpressData();
        expressData.setExpressType(type);
        switch (type) {
            case SHUNFENG:
                String secretKey = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SHUNFENG_SECRETKEY);
                String hospitalCode = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SHUNFENG_HOSPITALCODE);
                String host = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SHUNFENG_HOST);
                String username = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SHUNFENG_USERNAME);
                String password = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SHUNFENG_PASSWORD);
                boolean preOrderFlg = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.SHUNFENG_MONTHLY_PAY_ORDER);
                String name = senderInfo.getSenderName();
                String tel = senderInfo.getSenderTel();
                String province = senderInfo.getSenderProvince();
                String city = senderInfo.getSenderCity();
                String county = senderInfo.getSenderCounty();
                String address = senderInfo.getSenderAddress();
//                String monthlyCard = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SHUNFENG_MONTHLY_CARD);
                String monthlyCard = null;
                String expressTypeId = senderInfo.getShunfengExpressId();
                SHUNFENGExpressConfig shunfengExpressConfig = new SHUNFENGExpressConfig(hospitalCode, secretKey, host, username, password,
                        preOrderFlg, name, tel, province, city, county, address, monthlyCard, expressTypeId);
                expressData.setShunfengExpressConfig(shunfengExpressConfig);
                break;
            case Express100:
                String key = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.EXPRESS_100_KEY);
                String customer = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.EXPRESS_100_CUSTOMER);
                EXPRESS100Config express100Config = new EXPRESS100Config(key, customer);
                expressData.setExpress100Config(express100Config);
                break;
            default:
        }
        return expressData;
    }

    private UserMedicalRecordCopyAppointmentDTO getUserMedicalRecordCopyAppointmentDTO(UserMedicalRecordCopyAppointmentDTO userMedicalRecordCopyAppointmentDTO,
                                                                                       UserMedicalRecordCopyAppointment copyAppointment) {
        List<MedicalRecordCopyScopeDTO> medicalRecordCopyScopeDTOS = copyAppointment.getCopyScopeIds().stream()
                .map(scopeId -> new MedicalRecordCopyScopeDTO(medicalRecordCopyScopeRepository.getById(scopeId)))
                .collect(Collectors.toList());
        userMedicalRecordCopyAppointmentDTO.setCopyScopes(medicalRecordCopyScopeDTOS);
        return userMedicalRecordCopyAppointmentDTO;
    }

    @Override
    public Page<DuplicateStatisticsVM> queryDuplicateStatistics(Hospital hospital, String query, int pageNo, int size) {
        List<Specification<UserMedicalRecordCopyAppointment>> specs = createSpecifications(hospital, query);
        specs.add(Specifications.orderBy(Sort.Direction.DESC, "createdDate"));
        specs.add(hasStatus());
        List<DuplicateStatisticsVM> duplicateStatisticsList = userMedicalRecordCopyAppointmentRepository
                .findAll(Specifications.and(specs))
                .stream()
                .map(DuplicateStatisticsVM::new)
                .collect(Collectors.toList());
        Map<List<Object>, DuplicateStatisticsVM> groupedData = duplicateStatisticsList.stream()
                .collect(Collectors.toMap(
                        record -> Arrays.asList(record.getApplyDate(), record.getDeliveryMethod()),
                        record -> record,
                        (record1, record2) -> {
                            record1.setSendShareCopies(record1.getSendShareCopies() + record2.getSendShareCopies());
                            record1.setSendFixCopies(record1.getSendFixCopies() + record2.getSendFixCopies());
                            record1.setPickUpShareCopies(record1.getPickUpShareCopies() + record2.getPickUpShareCopies());
                            record1.setPickUpFixCopies(record1.getPickUpFixCopies() + record2.getPickUpFixCopies());
                            return record1;
                        },
                        LinkedHashMap::new
                ));
        List<DuplicateStatisticsVM> result = new ArrayList<>(groupedData.values())
                .stream()
                .sorted(Comparator.comparing(DuplicateStatisticsVM::getApplyDate, Comparator.reverseOrder())
                .thenComparing(DuplicateStatisticsVM::getDeliveryMethod))
                .collect(Collectors.toList());
        return PageUtils.paginate(result, pageNo, size);
    }

    @Override
    public UploadVM exportDuplicateStatistics(Hospital hospital, String query, HttpServletResponse response) {
        List<Specification<UserMedicalRecordCopyAppointment>> specs = createSpecifications(hospital, query);
        specs.add(hasStatus());
        List<DuplicateStatisticsVM> duplicateStatisticsList = userMedicalRecordCopyAppointmentRepository
                .findAll(Specifications.and(specs)).stream()
                .map(DuplicateStatisticsVM::new)
                .collect(Collectors.toList());
        Map<List<Object>, DuplicateStatisticsVM> groupedData = duplicateStatisticsList.stream()
                .collect(Collectors.toMap(
                        record -> Arrays.asList(record.getApplyDate(), record.getDeliveryMethod()),
                        record -> record,
                        (record1, record2) -> {
                            record1.setSendShareCopies(record1.getSendShareCopies() + record2.getSendShareCopies());
                            record1.setSendFixCopies(record1.getSendFixCopies() + record2.getSendFixCopies());
                            record1.setPickUpShareCopies(record1.getPickUpShareCopies() + record2.getPickUpShareCopies());
                            record1.setPickUpFixCopies(record1.getPickUpFixCopies() + record2.getPickUpFixCopies());
                            return record1;
                        },
                        LinkedHashMap::new
                ));
        List<CopyAppointmentExportDTO> data = groupedData.values().stream()
                .sorted(Comparator.comparing(DuplicateStatisticsVM::getApplyDate, Comparator.reverseOrder())
                .thenComparing(DuplicateStatisticsVM::getDeliveryMethod))
                .map(CopyAppointmentExportDTO::new).collect(Collectors.toList());
        return exportTaskService.exportCopyAppointment(data, response);
    }

    @Override
    public DuplicateTotalStatisticsVM queryTopDuplicateTotalStatistics(Hospital hospital, String query, String filterMonth) {
        List<Specification<UserMedicalRecordCopyAppointment>> specs;
        if (StringUtils.isNotBlank(filterMonth)) {
            specs = filterDateSpecifications(hospital, TimeUtils.extractMonth(filterMonth));
        } else {
            specs = createSpecifications(hospital, query);
            specs.add(hasStatus());
        }
        List<UserMedicalRecordCopyAppointment> recordCopyAppointments = userMedicalRecordCopyAppointmentRepository
                .findAll(Specifications.and(specs));
        int totalShareCopies = recordCopyAppointments.stream().mapToInt(UserMedicalRecordCopyAppointment::getCopyCount).sum();
        int totalFixCopies = recordCopyAppointments.stream().mapToInt(UserMedicalRecordCopyAppointment::getPageTotal).sum();
        DuplicateTotalStatisticsVM duplicateTotalStatisticsVM = new DuplicateTotalStatisticsVM();
        duplicateTotalStatisticsVM.setTotalShareCopies(totalShareCopies);
        duplicateTotalStatisticsVM.setTotalFixCopies(totalFixCopies);
        return duplicateTotalStatisticsVM;
    }

    @Override
    public List<DuplicateStatisticsVM> queryBottomDuplicateTotalStatistics(Hospital hospital, String filterMonth) {
        Date[] dates = TimeUtils.extractMonth(filterMonth);
        List<String> dateList = TimeUtils.getDatetoStringList(dates[0], dates[1], true);
        List<Specification<UserMedicalRecordCopyAppointment>> specs = filterDateSpecifications(hospital, dates);
        List<DuplicateStatisticsVM> duplicateStatisticsVMS = userMedicalRecordCopyAppointmentRepository
                .findAll(Specifications.and(specs))
                .stream()
                .map(DuplicateStatisticsVM::new)
                .collect(Collectors.toList());
        Map<String, DuplicateStatisticsVM> groupedData = duplicateStatisticsVMS.stream()
                .collect(Collectors.toMap(
                        DuplicateStatisticsVM::getApplyDate,
                        p -> p,
                        (record1, record2) -> {
                            record1.setSendShareCopies(record1.getSendShareCopies() + record2.getSendShareCopies());
                            record1.setSendFixCopies(record1.getSendFixCopies() + record2.getSendFixCopies());
                            record1.setPickUpShareCopies(record1.getPickUpShareCopies() + record2.getPickUpShareCopies());
                            record1.setPickUpFixCopies(record1.getPickUpFixCopies() + record2.getPickUpFixCopies());
                            return record1;
                        })
                );
        return dateList.stream()
                .map(revenueDate -> groupedData.getOrDefault(revenueDate,
                        new DuplicateStatisticsVM(revenueDate, null, 0L, 0L, 0L, 0L)))
                .collect(Collectors.toList());
    }

    @Override
    public List<CopyPurposeStatisticsVM> queryCopyPurposeStatistics(Hospital hospital, String filterMonth) {

        Date[] dates = TimeUtils.extractMonth(filterMonth);
        List<Specification<UserMedicalRecordCopyAppointment>> specs = filterDateSpecifications(hospital, dates);
        List<CopyPurposeStatisticsVM> copyPurposeStatisticsVMS = userMedicalRecordCopyAppointmentRepository
                .findAll(Specifications.and(specs))
                .stream()
                .map(CopyPurposeStatisticsVM::new)
                .collect(Collectors.toList());
        return new ArrayList<>(copyPurposeStatisticsVMS.stream()
                .collect(Collectors.toMap(
                        item -> item.getApplyDate() + item.getPurposeName(),
                        item -> item,
                        (existing, replacement) -> {
                            existing.setSendPurposeCount(existing.getSendPurposeCount() + replacement.getSendPurposeCount());
                            existing.setPickUpPurposeCount(existing.getPickUpPurposeCount() + replacement.getPickUpPurposeCount());
                            return existing;
                        }
                )).values());
    }

    @Override
    public List<PayFeeStatisticsVM> queryPayFeeStatistics(Hospital hospital, String filterYear) {
        Date[] dates = TimeUtils.extractYear(Integer.parseInt(filterYear));
        List<PayFeeStatisticsVM> payFeeStatistics = getPayFeeStatistics(hospital, dates, true);
        List<String> dateList = TimeUtils.getDatetoStringList(dates[0], dates[1], false);
        List<PayFeeStatisticsVM> lastPayFeeStatistics = getPayFeeStatistics(hospital, TimeUtils.extractYear(Integer.parseInt(filterYear) - 1), false);
        payFeeStatistics.addAll(lastPayFeeStatistics);
        Map<String, PayFeeStatisticsVM> statisticsMap = payFeeStatistics.stream()
                .collect(Collectors.toMap(
                        PayFeeStatisticsVM::getRevenueDate,
                        p -> p,
                        (p1, p2) -> {
                            p1.setCurrentMonthlyTotalFee(p1.getCurrentMonthlyTotalFee() + p2.getCurrentMonthlyTotalFee());
                            p1.setLastMonthlyTotalFee(p1.getLastMonthlyTotalFee() + p2.getLastMonthlyTotalFee());
                            return p1;
                        }
                ));
        return dateList.stream()
                .map(revenueDate -> statisticsMap.getOrDefault(revenueDate, new PayFeeStatisticsVM(revenueDate, 0L, 0L)))
                .collect(Collectors.toList());
    }

    @Override
    public UserMedicalRecordCopyAppointmentUnitPriceDTO updateUnitPrice(long id, UserMedicalRecordCopyAppointmentUnitPriceDTO dto, Hospital hospital) {
        UserMedicalRecordCopyAppointmentUnitPrice oldPrice = userMedicalRecordCopyAppointmentUnitPriceRepository.getById(id);
        List<UserMedicalRecordCopyAppointmentUnitPrice> children = userMedicalRecordCopyAppointmentUnitPriceRepository.findAllByParent(oldPrice);
        UserMedicalRecordCopyAppointmentUnitPrice newPrice = userMedicalRecordCopyAppointmentUnitPriceRepository.save(dto.toEntity(hospital));
        oldPrice.setParent(newPrice);
        userMedicalRecordCopyAppointmentUnitPriceRepository.save(oldPrice);
        for (UserMedicalRecordCopyAppointmentUnitPrice child : children) {
            child.setParent(newPrice);
            userMedicalRecordCopyAppointmentUnitPriceRepository.save(child);
        }
        return new UserMedicalRecordCopyAppointmentUnitPriceDTO(newPrice);
    }

    List<PayFeeStatisticsVM> getPayFeeStatistics(Hospital hospital, Date[] dates, boolean yearFly) {
        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        List<Specification<OfflineOrder>> typeSpecs = Lists.newArrayList();
        typeSpecs.add(Specifications.eq("type", ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT));
        typeSpecs.add(Specifications.eq("type", ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND));
        specs.add(Specifications.or(typeSpecs));
        specs.add(Specifications.ge("createdDate", dates[0]));
        specs.add(Specifications.lt("createdDate", dates[1]));
        specs.add(Specifications.eq("hospital", hospital));
        List<OfflineOrder> offlineOrders = offlineOrderRepository.findAll(Specifications.and(specs));
        List<ThirdOrderType> typeList = Lists.newArrayList();
        typeList.add(ThirdOrderType.MEDICAL_RECORD_COPY_APPOINTMENT);
        typeList.add(ThirdOrderType.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND);
        List<String> ids = offlineOrders.stream().map(o -> o.getId() + "").collect(Collectors.toList());
        Map<String, WechatOrder> map = wechatOrderRepository.findAllByWechatOrderTypeInAndProductIdIn(typeList, ids).stream()
                .filter(WechatOrder::getPayed)
                .collect(Collectors.toMap(WechatOrder::getProductId, v -> v, (k1, k2) -> k2));
        List<PayFeeStatisticsVM> payFeeStatisticsVMList = new ArrayList<>();
        for (OfflineOrder offlineOrder : offlineOrders) {
            if (map.containsKey(String.valueOf(offlineOrder.getId()))) {
                PayFeeStatisticsVM payFeeStatisticsVM = new PayFeeStatisticsVM();
                payFeeStatisticsVM.setRevenueDate(TimeUtils.dateToString(offlineOrder.getCreatedDate(), "YYYY-MM"));
                int refundFee = offlineOrder.getRefundFee() == null ? 0 : offlineOrder.getRefundFee();
                int price = offlineOrder.getRegistrationFee() - refundFee;
                if (yearFly) {
                    payFeeStatisticsVM.setCurrentMonthlyTotalFee(price);
                } else {
                    payFeeStatisticsVM.setLastMonthlyTotalFee(price);
                }
                payFeeStatisticsVMList.add(payFeeStatisticsVM);
            }
        }
        return payFeeStatisticsVMList;
    }

    public List<Specification<UserMedicalRecordCopyAppointment>> filterDateSpecifications(Hospital hospital, Date[] dates) {
        List<Specification<UserMedicalRecordCopyAppointment>> specs = new ArrayList<>();
        specs.add(Specifications.ge("createdDate", dates[0]));
        specs.add(Specifications.lt("createdDate", dates[1]));
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(hasStatus());
        return specs;
    }

    public List<Specification<UserMedicalRecordCopyAppointment>> createSpecifications(Hospital hospital, String query) {
        List<Specification<UserMedicalRecordCopyAppointment>> specs = new ArrayList<>();
        specs.add(MedicalRecordCopyAppointmentSearch.of(query).toSpecification());
        specs.add(Specifications.eq("hospital", hospital));
        return specs;
    }

    @Override
    public Page<CopyAppointmentBillVM> getBills(Hospital hospital, String query, PageRequest pageRequest) {
        List<Specification<Bill>> sps = Lists.newArrayList();
        Specification<Bill> bs = BillSearch.of(query).toSpecification();
        sps.add(bs);
        sps.add(Specifications.eq("hospital", hospital));
        sps.add((Specification<Bill>) (root, query1, criteriaBuilder) -> root.get("billServiceType")
                .in(List.of(ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT,
                        ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND)));
        Page<Bill> bills = billRepository.findAll(Specifications.and(sps), pageRequest);
        List<OfflineOrder> offlineOrders = offlineOrderRepository.findAllById(bills.stream()
                .map(u -> Long.parseLong(u.getOrderNo())).collect(Collectors.toList()));
        // <OfflineOrderId, UserMedicalRecordCopyAppointment>
        Map<String, OfflineOrder> map = offlineOrders.stream()
                .collect(Collectors.toMap(u -> u.getId() + "", v -> v));

        Page<CopyAppointmentBillVM> vms = bills.map(bill -> {
            OfflineOrder offlineOrder = map.get(bill.getOrderNo());
            UserMedicalRecordCopyAppointment copyAppointment = offlineOrder.getCopyAppointment();
            CopyAppointmentBillVM vm = new CopyAppointmentBillVM();
            vm.setId(bill.getId());
            vm.setCopyAppointmentId(offlineOrder.getCopyAppointment().getId());
            vm.setOrderNo(bill.getTransactionId());
            vm.setPatientName(bill.getPatientName());
            vm.setIdCardNumber(copyAppointment.getIdCardNum());
            if (bill.getBillOperateType() == BillOperateTypeEnum.PAYMENT) {
                if (offlineOrder.getType() == ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT) {
                    vm.setAmount(offlineOrder.getRegistrationFee());
                } else {
                    vm.setAppendAmount(offlineOrder.getRegistrationFee());
                }
                vm.setOperationTime(bill.getOrderOperateTime());
            } else {
                if (copyAppointment.getRefundType() == RefundTypeEnum.REFUND) {
                    vm.setRefundAmount(offlineOrder.getRefundFee());
                } else {
                    vm.setChargeRefundAmount(offlineOrder.getRefundFee());
                }
                vm.setOperationTime(bill.getRefundInitiationTime());
            }
            return vm;
        });
        return vms;
    }

    @Override
    public CopyAppointmentBillStatsVM getBillStats(Hospital hospital, String query) {
        List<SearchFilter<Bill>> bs = BillSearch.of(query).getFilters();
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createQuery(Tuple.class);
        Root<OfflineOrder> root = criteriaQuery.from(entityManager.getMetamodel().entity(OfflineOrder.class));
        Join<OfflineOrder, UserMedicalRecordCopyAppointment> copyJoin = root.join("copyAppointment", JoinType.INNER);
        Predicate whereAll = criteriaBuilder.and(
                criteriaBuilder.equal(root.get("hospital").get("id"), hospital.getId()),
                root.get("type").in(List.of(ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT, ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND)),
                criteriaBuilder.isNotNull(root.get("payTime")));
        Predicate payTimePred = criteriaBuilder.conjunction();
        Predicate refundTimePred = criteriaBuilder.conjunction();

        for (SearchFilter<Bill> b : bs) {
            if (b == null) {
                continue;
            }
            if (b instanceof CopyAppointmentFilter) {
                Long id = ((CopyAppointmentFilter) b).getCopyAppointmentId();
                String idCard = ((CopyAppointmentFilter) b).getIdCard();
                if (id != null) {
                    whereAll = criteriaBuilder.and(whereAll, criteriaBuilder.equal(copyJoin.get("id"), id));
                }
                if (idCard != null) {
                    whereAll = criteriaBuilder.and(whereAll, criteriaBuilder.equal(copyJoin.get("idCardNum"), idCard));
                }
            } else if (b instanceof cn.taihealth.ih.service.impl.filter.bill.PatientNameFilter) {
                String patientName = ((cn.taihealth.ih.service.impl.filter.bill.PatientNameFilter) b).getPatientName();
                whereAll = criteriaBuilder.and(whereAll, criteriaBuilder.like(copyJoin.get("patientName"), "%" + patientName + "%"));
            } else if (b instanceof OperateStartDateFilter) {
                Date d = ((OperateStartDateFilter) b).getDate();
                payTimePred = criteriaBuilder.and(payTimePred, criteriaBuilder.greaterThanOrEqualTo(root.get("payTime"), d));
                refundTimePred = criteriaBuilder.and(refundTimePred, criteriaBuilder.greaterThanOrEqualTo(root.get("refundTime"), d));
            } else if (b instanceof OperateEndDateFilter) {
                Date d = ((OperateEndDateFilter) b).getDate();
                payTimePred = criteriaBuilder.and(payTimePred, criteriaBuilder.lessThan(root.get("payTime"), d));
                refundTimePred = criteriaBuilder.and(refundTimePred, criteriaBuilder.lessThan(root.get("refundTime"), d));
            }
        }
        CopyAppointmentBillStatsVM vm = new CopyAppointmentBillStatsVM();
        // 支付统计: 预交金总额，结算补缴总金额
        criteriaQuery.where(criteriaBuilder.and(whereAll, payTimePred));
        criteriaQuery.groupBy(root.get("type"));

        criteriaQuery.multiselect(root.get("type"), criteriaBuilder.sum(root.get("registrationFee")));
        List<Tuple> payResult = entityManager.createQuery(criteriaQuery).getResultList();
        payResult.forEach(u -> {
            Long amount = u.get(1, Long.class);
            if (amount == null) {
                return;
            }
            ProjectTypeEnum type = u.get(0, ProjectTypeEnum.class);
            if (type == ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT) {
                vm.setAmount(amount);
            }
            if (type == ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND) {
                vm.setAppendAmount(amount);
            }
        });

        // 退款统计: 结算退款总额，退预交金总额
        entityManager.clear();
        criteriaQuery.where(criteriaBuilder.and(whereAll, refundTimePred));
        criteriaQuery.groupBy(copyJoin.get("refundType"));

        criteriaQuery.multiselect(copyJoin.get("refundType"), criteriaBuilder.sum(root.get("refundFee")));
        List<Tuple> refundResult = entityManager.createQuery(criteriaQuery).getResultList();
        refundResult.forEach(u -> {
            Long amount = u.get(1, Long.class);
            if (amount == null) {
                return;
            }
            RefundTypeEnum type = u.get(0, RefundTypeEnum.class);
            if (type == RefundTypeEnum.REFUND) {
                vm.setRefundAmount(amount);
            }
            if (type == RefundTypeEnum.CHARGE_REFUND) {
                vm.setChargeRefundAmount(amount);
            }
        });
        vm.setIncomeAmount(vm.getAmount() + vm.getAppendAmount() - vm.getRefundAmount() - vm.getChargeRefundAmount());
        return vm;
    }

    @Override
    public Upload exportCopyAppointmentBills(Hospital hospital, String query) {
        Date startTime = new Date(0);
        Date endTime = new Date();
        List<SearchFilter<Bill>> bs = BillSearch.of(query).getFilters();
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createQuery(Tuple.class);
        Root<OfflineOrder> root = criteriaQuery.from(entityManager.getMetamodel().entity(OfflineOrder.class));
        Join<OfflineOrder, UserMedicalRecordCopyAppointment> copyJoin = root.join("copyAppointment", JoinType.INNER);
        Predicate whereAll = criteriaBuilder.and(
                criteriaBuilder.equal(root.get("hospital").get("id"), hospital.getId()),
                root.get("type").in(List.of(ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT, ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT_APPEND)),
                criteriaBuilder.isNotNull(root.get("payTime")));
        Predicate payTimePred = criteriaBuilder.conjunction();
        Predicate refundTimePred = criteriaBuilder.conjunction();

        for (SearchFilter<Bill> b : bs) {
            if (b == null) {
                continue;
            }
            if (b instanceof CopyAppointmentFilter) {
                Long id = ((CopyAppointmentFilter) b).getCopyAppointmentId();
                String idCard = ((CopyAppointmentFilter) b).getIdCard();
                if (id != null) {
                    whereAll = criteriaBuilder.and(whereAll, criteriaBuilder.equal(copyJoin.get("id"), id));
                }
                if (idCard != null) {
                    whereAll = criteriaBuilder.and(whereAll, criteriaBuilder.equal(copyJoin.get("idCardNum"), idCard));
                }
            } else if (b instanceof cn.taihealth.ih.service.impl.filter.bill.PatientNameFilter) {
                String patientName = ((cn.taihealth.ih.service.impl.filter.bill.PatientNameFilter) b).getPatientName();
                whereAll = criteriaBuilder.and(whereAll, criteriaBuilder.like(copyJoin.get("patientName"), "%" + patientName + "%"));
            } else if (b instanceof OperateStartDateFilter) {
                Date d = ((OperateStartDateFilter) b).getDate();
                startTime = d;
                payTimePred = criteriaBuilder.and(payTimePred, criteriaBuilder.greaterThanOrEqualTo(root.get("payTime"), d));
                refundTimePred = criteriaBuilder.and(refundTimePred, criteriaBuilder.greaterThanOrEqualTo(root.get("refundTime"), d));
            } else if (b instanceof OperateEndDateFilter) {
                Date d = ((OperateEndDateFilter) b).getDate();
                endTime = d;
                payTimePred = criteriaBuilder.and(payTimePred, criteriaBuilder.lessThan(root.get("payTime"), d));
                refundTimePred = criteriaBuilder.and(refundTimePred, criteriaBuilder.lessThan(root.get("refundTime"), d));
            }
        }

        criteriaQuery.where(criteriaBuilder.and(whereAll, criteriaBuilder.or(payTimePred, refundTimePred)));
        criteriaQuery.multiselect(
                copyJoin.get("id"), copyJoin.get("patientName"), copyJoin.get("idCardNum"),
                root.get("payTime"), root.get("type"), root.get("registrationFee"),
                copyJoin.get("refundType"), copyJoin.get("refundTime"), root.get("refundFee"));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        ApplicationProperties applicationProperties = AppContext.getInstance(ApplicationProperties.class);
        String name = "病案预约收入统计表-" + System.currentTimeMillis() + ".xlsx";

        File file = new File(UrlUtils.concatSegments(applicationProperties.getHome(), "temp", name));
        try (OutputStream out = new FileOutputStream(file);
             ExcelWriter writer = ExcelUtil.getWriterWithSheet("明细")) {
            writer.setColumnWidth(0, 18);
            writer.merge(7, "病案预约收入明细表", false);
            writer.setRowHeight(0, 30);
            CellStyle cellStyle = writer.getOrCreateRowStyle(0);
            Font cellFont = writer.createFont();
            cellFont.setFontHeightInPoints((short) 12);
            cellFont.setFontName("Microsoft YaHei UI");
            cellStyle.setFont(cellFont);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            writer.setRowStyleIfHasData(0, cellStyle);

            int appointAmount = 0;
            int appendAmount = 0;
            int refundAmount = 0;
            int chargeRefund = 0;
            List<Map<String, Object>> rows = Lists.newArrayList();
            Map<String, CopyAppointmentBillStatsVM> statsRowsMap = new HashMap<>();
            for (Tuple detail : result) {
                // 交易时间	订单编号	患者姓名	身份证号	预交金额	结算补缴金额	结算退款金额	退预交金额
                Long id = detail.get(0, Long.class);
                String patientName = detail.get(1, String.class);
                String idCardNum = detail.get(2, String.class);
                Date payTime = detail.get(3, Date.class);
                ProjectTypeEnum type = detail.get(4, ProjectTypeEnum.class);
                Integer registrationFee = detail.get(5, Integer.class);
                RefundTypeEnum refundType = detail.get(6, RefundTypeEnum.class);
                Date refundTime = detail.get(7, Date.class);
                Integer refundFee = detail.get(8, Integer.class);
                if (payTime.compareTo(startTime) > 0 && payTime.compareTo(endTime) < 0) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    if (registrationFee == null) {
                        registrationFee = 0;
                    }
                    String time = TimeUtils.dateToString(payTime, "yyyy/MM/dd");
                    CopyAppointmentBillStatsVM vm;
                    if (statsRowsMap.containsKey(time)) {
                        vm = statsRowsMap.get(time);
                    } else {
                        vm = new CopyAppointmentBillStatsVM();
                        statsRowsMap.put(time, vm);
                    }
                    // 支付
                    row.put("交易时间", payTime);
                    row.put("订单编号", id + "");
                    row.put("患者姓名", patientName);
                    row.put("身份证号", idCardNum);
                    if (type == ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT) {
                        row.put("预交金额", MathUtils.division2(registrationFee, 100));
                        row.put("结算补缴金额", "");
                        appointAmount += registrationFee;
                        vm.setAmount(vm.getAmount() + registrationFee);
                    } else {
                        row.put("预交金额", "");
                        row.put("结算补缴金额", MathUtils.division2(registrationFee, 100));
                        appendAmount += registrationFee;
                        vm.setAppendAmount(vm.getAppendAmount() + registrationFee);
                    }
                    row.put("结算退款金额", "");
                    row.put("退预交金额", "");
                    rows.add(row);
                }
                if (refundTime != null && refundTime.compareTo(startTime) > 0 && refundTime.compareTo(endTime) < 0) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    if (refundFee == null) {
                        refundFee = 0;
                    }
                    String time = TimeUtils.dateToString(refundTime, "yyyy/MM/dd");
                    CopyAppointmentBillStatsVM vm;
                    if (statsRowsMap.containsKey(time)) {
                        vm = statsRowsMap.get(time);
                    } else {
                        vm = new CopyAppointmentBillStatsVM();
                        statsRowsMap.put(time, vm);
                    }
                    // 退款
                    row.put("交易时间", refundTime);
                    row.put("订单编号", id + "");
                    row.put("患者姓名", patientName);
                    row.put("身份证号", idCardNum);
                    row.put("预交金额", "");
                    row.put("结算补缴金额", "");
                    if (refundType == RefundTypeEnum.REFUND) {
                        row.put("结算退款金额", "");
                        row.put("退预交金额", MathUtils.division2(refundFee, 100));
                        refundAmount += refundFee;
                        vm.setRefundAmount(vm.getRefundAmount() + refundFee);
                    } else {
                        row.put("结算退款金额", MathUtils.division2(refundFee, 100));
                        row.put("退预交金额", "");
                        chargeRefund += refundFee;
                        vm.setChargeRefundAmount(vm.getChargeRefundAmount() + refundFee);
                    }
                    rows.add(row);
                }
            }
            rows = rows.stream().sorted(Comparator.comparing(u -> (Date) u.get("交易时间"))).collect(Collectors.toList());

            Map<String, Object> rowTotal = new LinkedHashMap<>();
            // 退款
            rowTotal.put("交易时间", "合计");
            rowTotal.put("订单编号", "");
            rowTotal.put("患者姓名", "");
            rowTotal.put("身份证号", "");
            rowTotal.put("预交金额", MathUtils.division2(appointAmount, 100));
            rowTotal.put("结算补缴金额", MathUtils.division2(appendAmount, 100));
            rowTotal.put("结算退款金额", MathUtils.division2(chargeRefund, 100));
            rowTotal.put("退预交金额", MathUtils.division2(refundAmount, 100));
            rows.add(rowTotal);
            writer.write(rows, true);

            writer.setSheet("汇总");
            writer.setColumnWidth(0, 12);

            writer.merge(5, "病案预约收入汇总表", false);
            writer.setRowHeight(0, 30);
            writer.setRowStyleIfHasData(0, cellStyle);

            int totalAmount = 0;
            int totalAppendAmount = 0;
            int totalChargeRefundAmount = 0;
            int totalRefundAmount = 0;
            int totalIncomeAmount = 0;
            List<Map<String, Object>> statsRows = Lists.newArrayList();
            for (Map.Entry<String, CopyAppointmentBillStatsVM> entry : statsRowsMap.entrySet()) {
                String k = entry.getKey();
                CopyAppointmentBillStatsVM v = entry.getValue();
                // 交易时间段	预交金总额	结算补缴总额	结算退款总额	退预交金总额	收入总额
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("交易时间段", k);
                row.put("预交金总额", MathUtils.division2(v.getAmount(), 100));
                totalAmount += v.getAmount();
                row.put("结算补缴总额", MathUtils.division2(v.getAppendAmount(), 100));
                totalAppendAmount += v.getAppendAmount();
                row.put("结算退款总额", MathUtils.division2(v.getChargeRefundAmount(), 100));
                totalChargeRefundAmount += v.getChargeRefundAmount();
                row.put("退预交金总额", MathUtils.division2(v.getRefundAmount(), 100));
                totalRefundAmount += v.getRefundAmount();
                long incomeAmount = v.getAmount() + v.getAppendAmount() - v.getChargeRefundAmount() - v.getRefundAmount();
                row.put("收入总额", MathUtils.division2(incomeAmount, 100));
                totalIncomeAmount += incomeAmount;
                statsRows.add(row);
            }
            statsRows = statsRows.stream().sorted(Comparator.comparing(u -> (String) u.get("交易时间段"))).collect(Collectors.toList());
            Map<String, Object> statTotal = new LinkedHashMap<>();
            statTotal.put("交易时间段", "合计");
            statTotal.put("预交金总额", MathUtils.division2(totalAmount, 100));
            statTotal.put("结算补缴总额", MathUtils.division2(totalAppendAmount, 100));
            statTotal.put("结算退款总额", MathUtils.division2(totalChargeRefundAmount, 100));
            statTotal.put("退预交金总额", MathUtils.division2(totalRefundAmount, 100));
            statTotal.put("收入总额", MathUtils.division2(totalIncomeAmount, 100));
            statsRows.add(statTotal);
            writer.write(statsRows, true);
            writer.flush(out, true);
        } catch (Exception e) {
            log.error("导出失败", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("导出失败");
        }
        try {
            return AppContext.getInstance(UploadService.class).upload(AppContext.getInstance(UserService.class)
                    .getSystem(), UploadResource.of(file, UploadType.PUBLIC, null, name));
        } finally {
            file.delete();
        }

    }

}
