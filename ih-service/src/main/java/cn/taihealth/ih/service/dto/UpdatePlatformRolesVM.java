package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import cn.taihealth.ih.spring.security.PlatformRoleCodeName;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 */
@ApiModel
@Data
public class UpdatePlatformRolesVM  {

    @ApiModelProperty("用户名")
    @NotNull
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String username;

    @ApiModelProperty("用户姓名")
    @NotNull(message = "用户姓名必填")
    private String fullName;

    @ApiModelProperty("平台角色")
    private List<PlatformRoleCodeName> platformRoles = Lists.newArrayList();

}

