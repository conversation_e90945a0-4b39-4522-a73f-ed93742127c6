package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.dto.MiniAuthResult;
import cn.taihealth.ih.service.vm.MiniAuthNewVM;
import cn.taihealth.ih.service.vm.ca.HospitalCaParam;

public interface MiniService {

    HospitalCaParam miniAuthCa(MiniAuthNewVM param);

    MiniAuthResult miniAuthNew(MiniAuthNewVM param);

    String getSessionKey(String appId, String jsCode, Hospital hospital);

}
