package cn.taihealth.ih.service.impl.sms;

import cn.taihealth.ih.domain.cloud.Hospital;

import java.util.Map;

public interface SMSSender {
    /**
     * 发送模板短信
     * @param hospital
     * @param templateCode
     * @param mobile
     * @param params
     */
    void sendTemplate(Hospital hospital, String templateCode, String mobile, Map<String, String> params);

    /**
     * 发送模板短信
     * @param hospital
     * @param content
     * @param mobile
     * @param params
     */
    void sendNormal(Hospital hospital, String content, String mobile, Map<String, String> params);

}
