package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.commons.util.MoreBeanUtils;
import cn.taihealth.ih.domain.hospital.ChecksGroup;
import cn.taihealth.ih.service.vm.PatientVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 检查表DTO
 * <AUTHOR>
 */
@Data
@ToString(exclude = {"order", "patient", "doctor", "user", "dept", "checks"})
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class CheckGroupDTO extends UpdatableDTO {

    @ApiModelProperty("检查开立时间 ")
    private Date openTime;

    @ApiModelProperty("订单")
    private OrderDTO order;

    @ApiModelProperty("患者")
    private PatientVM patient;

    @ApiModelProperty("医生")
    private MedicalWorkerDTO doctor;

    @ApiModelProperty("发起人")
    private UserDTO user;

    @ApiModelProperty("科室")
    private DeptDTO dept;

    private List<ChecksDTO> checks = Lists.newArrayList();


    public CheckGroupDTO(ChecksGroup checksGroup, boolean relation) {
        super(checksGroup);
        this.openTime = checksGroup.getOpenTime();
        if (null != checksGroup.getPatient()) {
            this.patient = new PatientVM(checksGroup.getPatient(), null);
        }
        if (null != checksGroup.getDoctor()) {
            this.doctor = new MedicalWorkerDTO(checksGroup.getDoctor(), false);
        }
        if (null != checksGroup.getUser()) {
            this.user = new UserDTO(checksGroup.getUser());
        }
        if (null != checksGroup.getDept()) {
            this.dept = new DeptDTO(checksGroup.getDept(), false);
        }
        this.checks = checksGroup.getChecks().stream().map(o -> new ChecksDTO(o,false)).collect(Collectors.toList());
        if (relation) {
            this.order = new OrderDTO(checksGroup.getOrder(), false);
        }
    }

    public CheckGroupDTO(ChecksGroup checksGroup) {
        this(checksGroup, true);
    }

    public ChecksGroup toEntityCase(ChecksGroup checksGroup) {
        MoreBeanUtils.copyPropertiesNonNull(checksGroup, this);
        return checksGroup;
    }

}
