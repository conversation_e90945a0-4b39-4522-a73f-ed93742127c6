package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.enums.ScheduleModel;
import cn.taihealth.ih.domain.hospital.Schedule;
import cn.taihealth.ih.domain.hospital.Schedule.ScheduleType;
import cn.taihealth.ih.domain.hospital.ScheduleGroup;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import cn.taihealth.ih.service.dto.nursing.NursingConsulItemDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 检查预约-排班计划VM
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleGroupVM extends UpdatableDTO {

    @ApiModelProperty("专家")
    private MedicalWorkerVMForSchedule doctor;

    @ApiModelProperty("创建人")
    private UserVM creator;

    @ApiModelProperty("科室")
    private DeptVM dept;

    @ApiModelProperty("咨询项目")
    private NursingConsulItemDTO consulItem;

    @ApiModelProperty("排班类型")
    private Schedule.ScheduleType type = ScheduleType.NORMAL;

    @ApiModelProperty("模式：循环/单天")
    private ScheduleModel model = ScheduleModel.REPEAT;

    @ApiModelProperty("开始日期")
    private Date startTime;

    @ApiModelProperty("结束日期")
    private Date endTime;

    @ApiModelProperty("循环日 1:周日 7:周一")
    private List<Integer> repeat = Lists.newArrayList();

    @ApiModelProperty("整体已预约数")
    private Integer countUse;

    @ApiModelProperty("整体总可约数")
    private Integer count;

    @ApiModelProperty("计划班次详情")
    private List<ScheduleGroupShiftVM> details = Lists.newArrayList();

    public ScheduleGroupVM(ScheduleGroup group) {
        super(group);
        if (group.getCreator() != null) {
            this.creator = new UserVM(group.getCreator());
        }
        if (group.getDept() != null) {
            this.dept = new DeptVM(group.getDept());
        }
        if (group.getConsulItem() != null) {
            this.consulItem = new NursingConsulItemDTO(group.getConsulItem());
        }
        if (group.getMedicalWorker() != null) {
            this.doctor = new MedicalWorkerVMForSchedule(group.getMedicalWorker());
        }
        this.model = group.getModel();
        this.startTime = group.getStartTime();
        this.endTime = group.getEndTime();
        this.repeat = group.getRepeat();
        this.type = group.getType();
    }

    public ScheduleGroupVM(CreateScheduleGroupVM vm) {
        this.doctor = vm.getMedicalWorker();
        this.dept = vm.getDept();
        this.model = vm.getModel();
        this.type = vm.getType();
        this.startTime = vm.getStartTime();
        this.endTime = vm.getEndTime();
        this.repeat = Arrays.stream(vm.getRepeat()).boxed().collect(Collectors.toList());
        this.consulItem = vm.getConsulItem();
    }

}
