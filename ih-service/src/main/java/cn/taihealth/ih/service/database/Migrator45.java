package cn.taihealth.ih.service.database;

import cn.taihealth.ih.domain.UserMedicalWorkerRel;
import cn.taihealth.ih.repo.UserMedicalWorkerRelRepository;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class Migrator45 {

    public void run() {
        log.info("Migrator45...用户医生绑定关系表绑定null值老数据处理");
        UserMedicalWorkerRelRepository instance = AppContext.getInstance(UserMedicalWorkerRelRepository.class);
        Specification<UserMedicalWorkerRel> spec = Specifications.or(Specifications.isNull("ft"),
                                                                     Specifications.isNull("ckd"));
        List<UserMedicalWorkerRel> list = instance.findAll(spec).parallelStream().peek(elem -> {
            elem.setFt(elem.getFt() != null && elem.getFt());
            elem.setCkd(elem.getCkd() != null && elem.getCkd());
        }).collect(Collectors.toList());
        instance.saveAll(list);
    }
}
