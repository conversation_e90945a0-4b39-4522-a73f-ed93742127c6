package cn.taihealth.ih.service.vm.statistics.evaluation;

import cn.taihealth.ih.bean.statistics.BusinessResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Moon
 * @Date: 2021年03月15日11:27:10
 */
@Data
@NoArgsConstructor
public class BusinessStatisticsDeptsVM {

    @ApiModelProperty("科室")
    private String dept;

    @ApiModelProperty("接诊数")
    private int orderCount;

    @ApiModelProperty("在线门诊")
    private int out;

    @ApiModelProperty("在线咨询")
    private int consult;

    @ApiModelProperty("在线急诊")
    private int emergency;

    public BusinessStatisticsDeptsVM(BusinessResult result) {
        this.orderCount = result.getOrderCount();
        this.out = result.getOutPatient();
        this.consult = result.getConsult();
        this.emergency = result.getEmergency();
    }

}
