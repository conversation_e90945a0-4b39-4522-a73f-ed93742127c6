package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.LoginActivity;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.repo.LoginActivityRepository;
import cn.taihealth.ih.service.api.LoginActivityService;
import java.util.Date;
import java.util.Optional;
import org.lionsoul.ip2region.DataBlock;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class LoginActivityServiceImpl implements LoginActivityService {

    private final LoginActivityRepository loginActivityRepository;

    public LoginActivityServiceImpl(LoginActivityRepository loginActivityRepository) {
        this.loginActivityRepository = loginActivityRepository;
    }

//    @Async
    @Override
    @ActionLog(action = "登录")
    public LoginActivity addLoginActivity(User user, String ip, String agent, User.Source accessType) {
        LoginActivity loginActivity = new LoginActivity();
        loginActivity.setUser(user);
        loginActivity.setIp(ip);
        loginActivity.setDate(new Date());
        loginActivity.setAccessType(accessType);
        loginActivity.setDevice(agent); // request.getHeader(HttpHeaders.USER_AGENT));

        Optional<DataBlock> blockOptional = Ip2Region.MEMORY_SEARCH.search(ip);
        String location = "-1";
        if (blockOptional.isPresent()) {
            location = blockOptional.get().getRegion();
            // 中国|华东|上海市|上海市|电信
        }

        loginActivity.setLocation(location);
        loginActivityRepository.save(loginActivity);
        return loginActivity;
    }
}
