package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.cloud.TencentIM;

import java.util.Date;

/**
 */
public class TencentIMDTO extends UpdatableDTO {

//    private String uuid;

    private String username;
    private String signature;
    private Date expirationDate;

    public TencentIMDTO(TencentIM tim) {
        super(tim);

        this.username = tim.getUser().getUsername();
        this.signature = tim.getSignature();
        this.expirationDate = tim.getExpirationDate();
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }
}
