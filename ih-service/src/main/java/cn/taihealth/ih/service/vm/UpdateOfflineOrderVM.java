package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.enums.BillPayStatusEnum;
import cn.taihealth.ih.domain.enums.PaymentMethod;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.OfflineOrder.OutPatientStatus;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.AppointmentInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class UpdateOfflineOrderVM extends UpdatableDTO {
    @ApiModelProperty("就诊人")
    private PatientVM patient;
}
