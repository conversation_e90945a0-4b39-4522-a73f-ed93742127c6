package cn.taihealth.ih.service.impl.filter.crm.task;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.PatientClinic;
import cn.taihealth.ih.domain.crm.CrmTask;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.ListJoin;
import javax.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 */
public class MultiArgsFilter implements SearchFilter<CrmTask> {

    private final String pattern;

    public MultiArgsFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<CrmTask> toSpecification() {
        List<Specification<CrmTask>> specifications = Lists.newArrayList();

        specifications.add((Specification<CrmTask>) (root, criteriaQuery, criteriaBuilder) -> {
            criteriaQuery.distinct(true);
            Join<CrmTask, Patient> patient = root.join("patient");
            ListJoin<Patient, PatientClinic> disList = patient.joinList("clinics", JoinType.LEFT);

            Predicate result = criteriaBuilder.like(disList.get("result"), "%" + pattern + "%");
            Predicate idCardNum = criteriaBuilder.like(patient.get("idCardNum"), "%" + pattern + "%");
            Predicate name = criteriaBuilder.like(patient.get("name"), "%" + pattern + "%");
            Predicate mobile = criteriaBuilder.like(patient.get("mobile"), "%" + pattern + "%");
            return criteriaBuilder.or(result, idCardNum, name, mobile);
        });
        specifications.add(Specifications.likeIgnoreCase("plan.name", pattern));
        return Specifications.or(specifications);
    }

    @Override
    public String toExpression() {
        return "multiArgs:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof MultiArgsFilter)) {
            return false;
        }

        MultiArgsFilter rhs = (MultiArgsFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
