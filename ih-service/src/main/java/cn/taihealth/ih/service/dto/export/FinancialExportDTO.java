package cn.taihealth.ih.service.dto.export;

import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.text.DecimalFormat;
import java.util.TreeMap;

@Data
@AllArgsConstructor
public class FinancialExportDTO {

    private String hospitalName;
    private String appointment_registration;
    private String outpatient_payment;
    private String hospitalization_deposit;
    private String online_consultation;
    private String online_revisit;
    private String online_prescription;
    private String physical_examination_appointment;
    private String appointment_nucleic_acid;
    private String self_billing;
    private String medical_appointment;
    private String medical_record_copy_appointment;
    private String medical_record_copy_appointment_append;


    public FinancialExportDTO(String hospitalName, TreeMap<ProjectTypeEnum, Long> serviceAmountMap) {
        this.hospitalName = hospitalName;
        serviceAmountMap.forEach((k, v) -> {
            switch (k) {
                case SELF_BILLING:
                    self_billing = getDoubleString(v);
                    break;
                case APPOINTMENT_REGISTRATION:
                    appointment_registration = getDoubleString(v);
                    break;
                case OUTPATIENT_PAYMENT:
                    outpatient_payment = getDoubleString(v);
                    break;
                case HOSPITALIZATION_DEPOSIT:
                    hospitalization_deposit = getDoubleString(v);
                    break;
                case ONLINE_CONSULTATION:
                    online_consultation = getDoubleString(v);
                    break;
                case ONLINE_REVISIT:
                    online_revisit = getDoubleString(v);
                    break;
                case ONLINE_PRESCRIPTION:
                    online_prescription = getDoubleString(v);
                    break;
                case PHYSICAL_EXAMINATION_APPOINTMENT:
                    physical_examination_appointment = getDoubleString(v);
                    break;
                case APPOINTMENT_NUCLEIC_ACID:
                    appointment_nucleic_acid = getDoubleString(v);
                    break;
                case MEDICAL_APPOINTMENT:
                    medical_appointment = getDoubleString(v);
                    break;
                case MEDICAL_RECORD_COPY_APPOINTMENT:
                    medical_record_copy_appointment = getDoubleString(v);
                    break;
                case MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:
                    medical_record_copy_appointment_append = getDoubleString(v);
                    break;
            }
        });
    }

    public String getDoubleString(Long total) {
        DecimalFormat df = new DecimalFormat("0.00");
        float result = ((float) total / 100);
        return ""  + Float.parseFloat(df.format(result));
    }

}
