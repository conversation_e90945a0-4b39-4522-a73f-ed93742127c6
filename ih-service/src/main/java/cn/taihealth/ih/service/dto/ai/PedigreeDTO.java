package cn.taihealth.ih.service.dto.ai;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PedigreeDTO implements Serializable {

    @ApiModelProperty("成员虚拟ID")
    private Integer id;

    @ApiModelProperty("是否先证者")
    @JsonProperty("is_proband")
    private boolean isProband;

    @ApiModelProperty("性别")
    @JsonProperty("gender")
    private String gender;

    @ApiModelProperty("疾病类型")
    @JsonProperty("cancer")
    private String cancer;

    @ApiModelProperty("发病年龄")
    @JsonProperty("age_at_diagnosis")
    private Integer ageAtDiagnosis;

    @ApiModelProperty("是否死亡")
    @JsonProperty("deceased")
    private boolean deceased;

    @ApiModelProperty("死亡年龄")
    @JsonProperty("death_age_at")
    private Integer deathAgeAt;

    @ApiModelProperty("当前年龄")
    @JsonProperty("current_age")
    private Integer currentAge;

    @ApiModelProperty("配偶")
    @JsonProperty("spouse_id")
    private Integer spouseId;

    @ApiModelProperty("子女")
    @JsonProperty("children_ids")
    private List<Integer> childrenIds;

    @ApiModelProperty("父母")
    @JsonProperty("parent_ids")
    private List<Integer> parentIds;

    @ApiModelProperty("兄弟姐妹")
    @JsonProperty("siblings_ids")
    private List<Integer> siblingsIds;

    @ApiModelProperty("创建时间")
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
