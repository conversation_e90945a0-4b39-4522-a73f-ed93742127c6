package cn.taihealth.ih.service.dto.export;

import cn.taihealth.ih.domain.copyappointment.UserMedicalRecordCopyAppointment;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.DuplicateStatisticsVM;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class CopyAppointmentExportDTO {

    private String date;
    private String method;
    private String total1;
    private String total2;

    public CopyAppointmentExportDTO(DuplicateStatisticsVM vm) {
        date = vm.getApplyDate();
        if (vm.getDeliveryMethod() == UserMedicalRecordCopyAppointment.DeliveryMethod.POSTAL_DELIVERY) {
            method = "邮寄";
            total1 = vm.getSendShareCopies() + "";
            total2 = vm.getSendFixCopies() + "";
        } else {
            method = "自取";
            total1 = vm.getPickUpShareCopies() + "";
            total2 = vm.getPickUpFixCopies() + "";
        }
    }

}
