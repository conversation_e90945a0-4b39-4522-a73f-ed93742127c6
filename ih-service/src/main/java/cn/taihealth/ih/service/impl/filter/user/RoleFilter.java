package cn.taihealth.ih.service.impl.filter.user;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.UserRole;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import java.util.Objects;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.ListJoin;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class RoleFilter implements SearchFilter<User> {

    private final String pattern;

    public RoleFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<User> toSpecification() {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Join<User, UserRole> userRoles = root.join("userRoles");
            return criteriaBuilder.equal(userRoles.get("code"), pattern);
        };
    }

    @Override
    public String toExpression() {
        return "username:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof RoleFilter)) {
            return false;
        }

        RoleFilter rhs = (RoleFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
