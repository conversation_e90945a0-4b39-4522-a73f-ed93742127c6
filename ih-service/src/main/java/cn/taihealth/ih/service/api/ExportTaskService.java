package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.PhyExam;
import cn.taihealth.ih.domain.cloud.ExportTask;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.dto.ExportTaskDTO;
import cn.taihealth.ih.service.dto.UploadDTO;
import cn.taihealth.ih.service.dto.export.*;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.drugstore.DrugStatisticsDetailVM;
import cn.taihealth.ih.service.vm.statistics.supervision.SatisfactionStatic;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 *
 */
public interface ExportTaskService {

    Page<ExportTaskDTO> getTasks(Hospital hospital, String query, int pageNo, int size);

    UploadDTO exportDrugStatisticsDetails(List<DrugStatisticsDetailVM> detailVMS, ExportTask exportTask, User user);

    UploadVM exportNucleicAcid(List<NucleicAcidExportDTO> data, HttpServletResponse response);
    UploadVM exportSelfBilling(List<SelfBillingExportDTO> data, HttpServletResponse response);
    UploadVM exportMedicalSkillAppointment(List<MedicalSkillAppointmentExportDTO> data, HttpServletResponse response);
    UploadVM exportPhyExam(List<PhyExamExportDTO> data, HttpServletResponse response, PhyExam.Attribute attribute);
    UploadVM exportFinancial(List<FinancialExportDTO> data);
    UploadVM exportCopyAppointment(List<CopyAppointmentExportDTO> data, HttpServletResponse response);
    UploadVM exportBusinessStatistics(List<BusinessStatisticsExportDTO> data, HttpServletResponse response);
    UploadVM exportOperationalStatistics(List<OperationalNameCountExportDTO> nameCountData,
                                     List<NameCountTotal4DocExportDTO> hotDocData,
                                     List<NameCountTotal4PatientExportDTO> patientData,
                                     List<SatisfactionStatic> manyidu,
                                     List<NameCountTotal4DocExportDTO> eachDepartmentAppointmentData,
                                     HttpServletResponse response, Date startTime, Date endTime);

    UploadVM exportSatisfaction(List<SatisfactionExportDTO> ward,
                            List<SatisfactionExportDTO> performance,
                            List<SatisfactionRateExportDTO> rate, Date startTime, Date endTime, HttpServletResponse response);
}
