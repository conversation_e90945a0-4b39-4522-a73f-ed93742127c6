package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-09-30
 */
@Data
public class ElectronicInvoiceFile implements Serializable {
    @ApiModelProperty("病人唯一码	Y")
    private String patid;
    @ApiModelProperty("电子收据号	Y")
    private String elec_invoice_no;
    @ApiModelProperty("电子发票	Y	base64")
    private String elec_invoice_file;
    @ApiModelProperty("电子发票URL	Y	")
    private String elec_invoice_url;

    // 电子发票查看方式 1 H5查看，2 小程序查看
    private String elec_invoice_show_type;
    // 电子发票查看小程序appid
    private String elec_invoice_mp_appid;
    // 电子发票查看小程序路径
    private String elec_invoice_mp_path;

    @ApiModelProperty("开票机构	Y")
    private String invoicing_entity;
    @ApiModelProperty("票据金额	Y")
    private String invoicing_amount;
    @ApiModelProperty("开票日期	Y")
    private String invoicing_date;
    @ApiModelProperty("票据代码	Y")
    private String invoicing_code;
    @ApiModelProperty("票据号码	Y")
    private String invoicing_no;
    @ApiModelProperty("校验码	Y")
    private String check_code;
}
