package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.DrugPopularScience;
import cn.taihealth.ih.domain.cloud.Hospital;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DrugPopularScienceDTO extends UpdatableDTO {
    @ApiParam(name = "用药科普标题")
    private String title;

    @ApiParam(name = "用药科普封面")
    private UploadDTO picture;

    @ApiParam(name = "用药科普内容")
    private String content;

    @ApiParam(name = "是否展示")
    private boolean show;

    @ApiParam(name = "创建人")
    private UserDTO creator;

    @ApiModelProperty("医院信息")
    private HospitalDTO hospital;

    public DrugPopularScienceDTO(DrugPopularScience drugPopularScience) {
        super(drugPopularScience);
        this.title = drugPopularScience.getTitle();
        this.content = drugPopularScience.getContent();
        this.show = drugPopularScience.isShow();
        if (drugPopularScience.getPicture() != null) {
            UploadDTO uploadDTO = new UploadDTO(drugPopularScience.getPicture());
            this.setPicture(uploadDTO);
        }

        if (drugPopularScience.getCreator() != null) {
            this.creator = new UserDTO(drugPopularScience.getCreator());
        }
        this.hospital = new HospitalDTO(drugPopularScience.getHospital());
    }
}
