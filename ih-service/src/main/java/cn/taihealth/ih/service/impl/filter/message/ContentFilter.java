package cn.taihealth.ih.service.impl.filter.message;

import cn.taihealth.ih.domain.hospital.MessageTemplate;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ContentFilter implements SearchFilter<MessageTemplate> {

    private final String pattern;

    public ContentFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<MessageTemplate> toSpecification() {
        return Specifications.likeIgnoreCase("content", pattern);
    }

    @Override
    public String toExpression() {
        return "content:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof ContentFilter)) {
            return false;
        }

        ContentFilter rhs = (ContentFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
