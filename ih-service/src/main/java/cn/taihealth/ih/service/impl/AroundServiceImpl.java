package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.OfflineHospitalRepository;
import cn.taihealth.ih.service.api.AroundService;
import cn.taihealth.ih.service.dto.HospitalDTO;
import cn.taihealth.ih.service.dto.OfflineHospitalDTO;
import cn.taihealth.ih.service.impl.filter.offlinehospital.OffHospitalSearch;
import cn.taihealth.ih.service.util.GeoUtils;
import cn.taihealth.ih.service.util.LngLat;
import cn.taihealth.ih.service.util.LngLatSqureRange;
import com.gitq.jedi.data.specification.Specifications;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 */
@Service
public class AroundServiceImpl implements AroundService {

    private static final Logger log = LoggerFactory.getLogger(AroundServiceImpl.class);

    private final OfflineHospitalRepository offlineHospitalRepository;
    private final HospitalRepository hospitalRepository;

    public AroundServiceImpl(
        OfflineHospitalRepository offlineHospitalRepository,
        HospitalRepository hospitalRepository) {
        this.offlineHospitalRepository = offlineHospitalRepository;
        this.hospitalRepository = hospitalRepository;
    }

    @Override
    public List<OfflineHospitalDTO> findOfflineHospitals(double longitude, double latitude, double radius, String query, int limit) {
        LngLat start = new LngLat(longitude, latitude);
        LngLatSqureRange sr = GeoUtils.LongitudeRange(longitude, latitude, radius);
        Specification<OfflineHospital> specification = Specifications.and(
            Specifications.between("longitude", sr.getBeginLongitude(), sr.getEndLongitude()),
            Specifications.between("latitude", sr.getBeginLatitude(), sr.getEndLatitude()),
            Specifications.isTrue("enabled"),
            OffHospitalSearch.of(query).toSpecification()
        );
        List<OfflineHospitalDTO> list = offlineHospitalRepository.findAll(specification)
            .stream()
            .map(h -> {
                OfflineHospitalDTO dto = new OfflineHospitalDTO(h);
                LngLat end = new LngLat(h.getLongitude(), h.getLatitude());
                int distance = (int) GeoUtils.calculateLineDistance(start, end);
                dto.setDistance(distance);
                return dto;
            })
            .filter(u -> u.getDistance() <= radius)
            .sorted(Comparator.comparing(OfflineHospitalDTO::getDistance))
            .collect(Collectors.toList());

        if (limit > 0) {
            return list.subList(0, Math.min(limit, list.size()));
        } else {
            return list;
        }
    }

    @Override
    public List<HospitalDTO> findHospitals(double longitude, double latitude, double radius, int limit) {
        LngLat start = new LngLat(longitude, latitude);
        LngLatSqureRange sr = GeoUtils.LongitudeRange(longitude, latitude, radius);
        Specification<Hospital> specification = Specifications.and(
            Specifications.between("longitude", sr.getBeginLongitude(), sr.getEndLongitude()),
            Specifications.between("latitude", sr.getBeginLatitude(), sr.getEndLatitude()),
            Specifications.isTrue("enabled")
        );
        List<HospitalDTO> list = hospitalRepository.findAll(specification)
            .stream()
            .map(h -> {
                HospitalDTO dto = new HospitalDTO(h);
                LngLat end = new LngLat(h.getLongitude(), h.getLatitude());
                int distance = (int) GeoUtils.calculateLineDistance(start, end);
                dto.setDistance(distance);
                return dto;
            })
            .filter(u -> u.getDistance() <= radius)
            .sorted(Comparator.comparing(HospitalDTO::getDistance))
            .collect(Collectors.toList());

        if (limit > 0) {
            return list.subList(0, Math.min(limit, list.size()));
        } else {
            return list;
        }
    }

}
