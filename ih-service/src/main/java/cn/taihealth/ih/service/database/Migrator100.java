package cn.taihealth.ih.service.database;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class Migrator100 {

    private final RedisTemplate<String, Object> redisTemplate;

    public void run() {
        redisTemplate.delete(Lists.newArrayList("hibernate.cn.taihealth.ih.domain.hospital.HospitalPublicPlatform",
                "hibernate.cn.taihealth.ih.domain.hospital.UserPlatformInfo"));
    }

}
