package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.ElectronicHealthCardWD;
import cn.taihealth.ih.service.vm.UploadVM;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ElectronicHealthCardWDDTO {

    @ApiModelProperty("健康卡主键")
    private String id;

    @ApiModelProperty("健康卡Id")
    private String vuid;

    @ApiModelProperty("电子健康卡主索引")
    private String empi;

    @ApiModelProperty("卡类型 1:电子健康卡")
    private String klx;

    @ApiModelProperty("就诊人主键")
    private String patientId;

    @ApiModelProperty("就诊人姓名")
    private String patientName;

    @ApiModelProperty("电子健康卡二维码")
    private UploadVM diagnosisCode;

    public ElectronicHealthCardWDDTO(ElectronicHealthCardWD card) {
        this.id = card.getId() + "";
        this.vuid = card.getVuid();
        this.empi = card.getEmpi();
        this.klx = card.getKlx();
        this.patientId = card.getPatient().getId() + "";
        this.patientName = card.getPatient().getName();
        if (card.getDiagnosisCode() != null) {
            this.diagnosisCode = new UploadVM(card.getDiagnosisCode());
        }
    }
}
