package cn.taihealth.ih.service.vm;

import com.google.common.collect.Lists;
import java.util.List;

public class ImportMedicMessageVM {

    private boolean validityHeader = true;

    private boolean validityColumn = true;

    private boolean duplicateData = false;

    private ErrorColumn errorColumn = new ErrorColumn();

    public static class ErrorColumn {
        private int line;
        private List<String> columns = Lists.newArrayList();

        public ErrorColumn() {
        }

        public ErrorColumn(int line, List<String> columns) {
            this.line = line;
            this.columns = columns;
        }

        public int getLine() {
            return line;
        }

        public void setLine(int line) {
            this.line = line;
        }

        public List<String> getColumns() {
            return columns;
        }

        public void setColumns(List<String> columns) {
            this.columns = columns;
        }
    }

    public boolean isValidityHeader() {
        return validityHeader;
    }

    public void setValidityHeader(boolean validityHeader) {
        this.validityHeader = validityHeader;
    }

    public boolean isValidityColumn() {
        return validityColumn;
    }

    public void setValidityColumn(boolean validityColumn) {
        this.validityColumn = validityColumn;
    }

    public boolean isDuplicateData() {
        return duplicateData;
    }

    public void setDuplicateData(boolean duplicateData) {
        this.duplicateData = duplicateData;
    }

    public ErrorColumn getErrorColumn() {
        return errorColumn;
    }

    public void setErrorColumn(ErrorColumn errorColumn) {
        this.errorColumn = errorColumn;
    }
}
