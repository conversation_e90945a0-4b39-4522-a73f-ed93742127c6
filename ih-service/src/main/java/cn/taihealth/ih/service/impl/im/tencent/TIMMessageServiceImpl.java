package cn.taihealth.ih.service.impl.im.tencent;

import cn.taihealth.ih.commons.util.RedisUtil;
import cn.taihealth.ih.domain.OrderTIMMessageRel;
import cn.taihealth.ih.domain.TIMGroupMember;
import cn.taihealth.ih.domain.TIMMessage;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.UploadType;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import cn.taihealth.ih.mq.config.RabbitMQChannelConfig;
import cn.taihealth.ih.mq.entity.message.NewImMessageMsg;
import cn.taihealth.ih.repo.OrderTIMMessageRelRepository;
import cn.taihealth.ih.repo.TIMGroupMemberRepository;
import cn.taihealth.ih.repo.TIMMessageRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.cache.NewMessageCache;
import cn.taihealth.ih.service.dto.OrderDTO;
import cn.taihealth.ih.service.dto.TIMMessageDTO;
import cn.taihealth.ih.service.impl.im.tencent.MessageIm.Desc;
import cn.taihealth.ih.service.impl.im.tencent.TencentMsg.MsgContentCustom;
import cn.taihealth.ih.service.vm.TIMConversationVM;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.UserVM;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 *
 */
@AllArgsConstructor
@Service
public class TIMMessageServiceImpl implements TIMMessageService {

    private static final Logger log = LoggerFactory.getLogger(TIMMessageServiceImpl.class);
    private static final List<String> NEED_SAVE_DESC_LIST = Lists.newArrayList();

    static {
        NEED_SAVE_DESC_LIST.add(Desc.VIDEO_CHAT_START.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.VIDEO_CHAT_END.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.VISIT_STARTED_GROUP.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.TIME_OUT_ONTIME_CONFIRMED_REFUNDED.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.ONTIME_CONFIRMED_REFUNDED.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.ONTIME_CONFIRMED_DOCTOR_REFUNDED.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.VISIT_END.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.TIME_OUT_COMPLETED.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.STARTED_REFUNDED.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.NOTICE_DOCTOR_TO_CONSULT.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.KNOWLEDGE.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.SEND_PRESCRIPTION.getValue());
        NEED_SAVE_DESC_LIST.add(Desc.SEND_RECORD.getValue());
    }

    private final TIMMessageRepository timMessageRepository;
    private final OrderRepository orderRepository;
    private final OrderTIMMessageRelRepository orderTIMMessageRelRepository;
    private final TIMGroupMemberRepository timGroupMemberRepository;
    private final TencentIMService tencentIMService;
    private final RedisUtil redisUtil;
    private final LockService lockService;
    private final RabbitTemplate rabbitTemplate;
    private final NewMessageCache newMessageCache;


    @Override
    public List<TIMConversationVM> getTIMMessageByUser(Hospital hospital, User user) {
        List<Order.OrderStatus> statuses = Lists.newArrayList();
        statuses.add(OrderStatus.STARTED);
        statuses.add(OrderStatus.COMPLETED);
        List<Order> orders = orderRepository.findAllByHospitalAndUserAndStatusIn(hospital, user, statuses);

        return orders.stream().map(u -> {
            String imGroupId = u.getImGroupId();
            List<Map<String, Object>> lastOneOp = timMessageRepository
                .findLastOneByGroupId(imGroupId);
            if (lastOneOp.isEmpty()) {
                return null;
            }
            Map<String, Object> lastOne = lastOneOp.get(0);
            Object id = lastOne.get("id");
            Number idN = (Number) id;
            TIMMessage timMessage = timMessageRepository.getById(idN.longValue());
            Optional<Order> order = orderRepository.findOneByImGroupId(timMessage.getGroupId());
            UserVM userVM = new UserVM();
            OrderDTO orderDTO = new OrderDTO();
            if (order.isPresent()) {
                userVM = new UserVM(order.get().getDoctor().getUser());
                orderDTO = new OrderDTO(order.get());
            }
            TIMMessageDTO timMessageDTO = new TIMMessageDTO(timMessage);
            TIMConversationVM timConversationVM = new TIMConversationVM();
            timConversationVM.setGroupId(timMessage.getGroupId());
            timConversationVM.setTimMessageDTO(timMessageDTO);
            timConversationVM.setUser(userVM);
            timConversationVM.setOrder(orderDTO);

            return timConversationVM;
        }).filter(Objects::nonNull).sorted(
            Comparator.comparingLong(u -> u.getTimMessageDTO().getTime().getTime())
        ).collect(Collectors.toList());
    }

    @Override
    public void saveMessage(String json) {
        TencentMsg.CallbackMsg msg = StandardObjectMapper
            .readValue(json, new TypeReference<>() {
            });

        // 防止重复数据多次插入
        List<Specification<TIMMessage>> sp = Lists.newArrayList();
        sp.add(Specifications.eq("groupId", msg.GroupId));
        sp.add(Specifications.eq("sequence", msg.MsgSeq));
        if (timMessageRepository.count(Specifications.and(sp)) > 0) {
            return;
        }
        TIMMessage timMessage = new TIMMessage();
        timMessage.setGroupId(msg.GroupId);
        timMessage.setFromAccount(msg.From_Account);
        timMessage.setTime(msg.MsgTime.getTime());
        timMessage.setSequence(msg.MsgSeq);
        timMessage.setOperatorAccount(msg.Operator_Account);
        timMessage.setCloudCustomData(msg.CloudCustomData);
        timMessage.setBody(StandardObjectMapper.stringify(msg.MsgBody));
        timMessageRepository.save(timMessage);
        /*
         * 定义标记，作用：当消息类型是TIMCustomElem时，只有Desc为某些特定的类型时，
         * 才会更新ih_order_tim_message_rel表的当前消息，其他自定义消息类型不更新
         */
        AtomicBoolean flag = new AtomicBoolean(true);
        msg.MsgBody.forEach(msgBody -> {
            TIMMessage.MessageType type = TIMMessage.MessageType.valueOf(msgBody.MsgType);
            log.info("callback 文件类型： " + type.name());
            timMessage.setType(type);
            switch (type) {
                case TIMSoundElem:
                    msgBody.MsgContent = getContentSound(
                        StandardObjectMapper.stringify(msgBody.MsgContent));
                    break;
                case TIMVideoFileElem:
                    msgBody.MsgContent = getContentVideoFile(
                        StandardObjectMapper.stringify(msgBody.MsgContent));
                    break;
                case TIMFileElem:
                    msgBody.MsgContent = getContentFile(
                        StandardObjectMapper.stringify(msgBody.MsgContent));
                    break;
                case TIMImageElem:
                    msgBody.MsgContent = getContentImage(
                        StandardObjectMapper.stringify(msgBody.MsgContent));
                    break;
                case TIMCustomElem:
                    MsgContentCustom msgContent =
                        StandardObjectMapper.readValue(StandardObjectMapper.stringify(msgBody.MsgContent),
                                                       new TypeReference<>() {
                                                       });
                    if (!NEED_SAVE_DESC_LIST.contains(msgContent.Desc)) {
                        flag.set(false);
                    }
                    break;
                default:
            }
        });

        timMessage.setBody(StandardObjectMapper.stringify(msg.MsgBody));
        timMessageRepository.save(timMessage);

        callBackHandler(timMessage);
        timMessageRepository.flush();
        log.info("腾讯IM回调---------保存聊天记录");

        //更新当前最新消息
        if (flag.get()) {

            lockService.executeWithLock("lock.orderTIMMessageRel.groupId" + timMessage.getGroupId(), () -> {
                Optional<OrderTIMMessageRel> byGroupId = orderTIMMessageRelRepository.findByGroupId(
                    msg.GroupId);
                Order order = null;
                if (byGroupId.isPresent()) {
                    OrderTIMMessageRel orderTIMMessageRel = byGroupId.get();
                    orderTIMMessageRel.setTimMessage(timMessage);
                    if (msg.MsgSeq > orderTIMMessageRel.getUnreadCount())
                        orderTIMMessageRel.setUnreadCount(msg.MsgSeq);
                    orderTIMMessageRelRepository.save(orderTIMMessageRel);
                    order = orderTIMMessageRel.getMOrder();
                    log.info("腾讯IM回调---------更新当前最新消息 groupId: {} ", msg.GroupId);

                } else {
                    Optional<Order> orderOptional = orderRepository.findOneByImGroupId(msg.GroupId);
                    if (orderOptional.isPresent()) {
                        OrderTIMMessageRel orderTIMMessageRel = new OrderTIMMessageRel();
                        orderTIMMessageRel.setMOrder(orderOptional.get());
                        orderTIMMessageRel.setTimMessage(timMessage);
                        orderTIMMessageRel.setGroupId(msg.GroupId);
                        orderTIMMessageRel.setUnreadCount((int) msg.MsgSeq);
                        orderTIMMessageRelRepository.save(orderTIMMessageRel);
                        order = orderTIMMessageRel.getMOrder();
                        log.info("腾讯IM回调---------添加当前最新消息 groupId: {} ", msg.GroupId);
                    }
                }

                // 发消息提醒用户/医生端 去查未读消息
                if (order != null && order.getDoctor() != null && !StringUtils.equals(
                    order.getDoctor().getUser().getUsername(), msg.From_Account)) {
                    tencentIMService.sendMessage(order.getHospital(), order.getDoctor().getUser(),
                                                 new MessageIm(Desc.NEW_GROUP_MESSAGE,
                                                               "新群消息"));
                }
                if (order != null && !StringUtils.equals(order.getUser().getUsername(), msg.From_Account)) {
                    tencentIMService.sendMessage(order.getHospital(), order.getUser(),
                                                 new MessageIm(Desc.NEW_GROUP_MESSAGE,
                                                               "新群消息"));
                }
                return 0;
            });
        }
    }

    private void callBackHandler(TIMMessage timMessage) {
        // 以前的逻辑, 使用redis的过期模拟延迟消息, 后面减少使用
        if (!(timMessage.getFromAccount().equals("admin") || timMessage.getFromAccount().equals("system"))
            && redisUtil.get("order_reply:" + timMessage.getGroupId()) == null) {
            redisUtil.set("order_reply:" + timMessage.getGroupId(), 1, 5 * 60);
        }

        // 使用rabbitmq的延迟队列
        if (!(timMessage.getFromAccount().equals("admin") || timMessage.getFromAccount().equals("system"))
                && !newMessageCache.getNew("newMessage.doctorId." + timMessage.getGroupId())) {
            Optional<Order> order = orderRepository.findOneByImGroupId(timMessage.getGroupId());
            order.ifPresent(o -> {
                User doctor = o.getDoctor() == null ? null : o.getDoctor().getUser();
                switch (o.getOrderType()) {
                    case NURSING_CONSULT:
                        if (doctor == null) {
                            break;
                        }
                        newMessageCache.putNew("newMessage.doctorId." + timMessage.getGroupId());
                        NewImMessageMsg msg = new NewImMessageMsg(timMessage);
                        msg.setExpectedSendingTime(DateUtils.addMinutes(new Date(), 5));
                        rabbitTemplate.convertAndSend(RabbitMQChannelConfig.COMMON_DELAY_DEAD_LETTER_EXCHANGE_NAME,
                                RabbitMQChannelConfig.DELAY_ROUTING_KEY_NURSING_NEW_MESSAGE_5M, StandardObjectMapper.stringify(msg));
                        break;
                }
            });
        }
    }

    @Override
    public int getUnReadCount(List<Specification<Order>> orderSpecifications, User user) {
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<OrderTIMMessageRel> root = criteriaQuery.from(entityManager.getMetamodel().entity(OrderTIMMessageRel.class));
        Root<Order> rootOrder = criteriaQuery.from(entityManager.getMetamodel().entity(Order.class));
        Root<TIMGroupMember> groupMemberRoot = criteriaQuery.from(entityManager.getMetamodel().entity(TIMGroupMember.class));
        Predicate groupJoinb = criteriaBuilder.and(criteriaBuilder.equal(root.get("groupId"), groupMemberRoot.get("groupId")),
                criteriaBuilder.equal(groupMemberRoot.get("username"), user.getUsername()));

        Predicate orderJoinb = criteriaBuilder.equal(root.get("mOrder"), rootOrder.get("id"));

        Predicate where = criteriaBuilder.and(criteriaBuilder.isNotNull(root.get("unreadCount")),
                groupJoinb,
                orderJoinb);

        Predicate whereOrder = criteriaBuilder.and(Specifications.and(orderSpecifications).toPredicate(rootOrder, criteriaQuery, criteriaBuilder));
        criteriaQuery.where(where, whereOrder);

        criteriaQuery.multiselect(criteriaBuilder.sum(root.get("unreadCount")), criteriaBuilder.sum(groupMemberRoot.get("sequence")));
        Tuple result = entityManager.createQuery(criteriaQuery).getSingleResult();

        Long total = result.get(0, Long.class);
        Long read = result.get(1, Long.class);
        int i = (int) ((total == null ? 0 : total) - (read == null ? 0 : read));
        if (i < 0) {
            log.error("腾讯IM--用户{}未读消息数小于0, 总消息数: {}, 已读消息数: {}", user.getUsername(), total, read);
        }
        return Math.max(i, 0);
    }

    @Override
    public int getOrderMessageUnReadCount(Order order, User user) {
        String imGroupId = order.getImGroupId();
        Optional<OrderTIMMessageRel> byGroupId = orderTIMMessageRelRepository.findByGroupId(imGroupId);
        if (byGroupId.isEmpty() || byGroupId.get().getUnreadCount() == null) {
            return 0;
        }
        Integer count = byGroupId.get().getUnreadCount();
        Optional<TIMGroupMember> byGroupIdAndUsername = timGroupMemberRepository.findByGroupIdAndUsername(imGroupId,
                                                                                                          user.getUsername());
        if (byGroupIdAndUsername.isEmpty()) {
            return 0;
        }
        Integer userCount = Math.toIntExact(byGroupIdAndUsername.get().getSequence());
        int result = count - userCount;
        //log.info("orderId: {} 实际未读消息:{} 总消息:{} 已读消息:{} ",order.getId(), result, count, userCount);
        return result;
    }

    private TencentMsg.MsgContentFile getContentFile(String msgContent) {
        TencentMsg.MsgContentFile msgContentFile = StandardObjectMapper.readValue(msgContent, new TypeReference<>() {});
        log.info("save file before------ Content-file-url: {}", msgContentFile.Url);
        msgContentFile.Url = uploadTIMFileToALiOS(msgContentFile.Url);
        log.info("save file after------ Content-file-url: {}", msgContentFile.Url);
        return msgContentFile;
    }

    private TencentMsg.MsgContentSound getContentSound(String msgContent) {
        TencentMsg.MsgContentSound msgContentSound = StandardObjectMapper.readValue(msgContent, new TypeReference<>() {});
        msgContentSound.SoundUrl = uploadTIMFileToALiOS(msgContentSound.SoundUrl);
        log.info("save sound------ msgContent-url: {}", msgContentSound.SoundUrl);

        return msgContentSound;
    }

    private TencentMsg.MsgContentVideoFile getContentVideoFile(String msgContent) {
        TencentMsg.MsgContentVideoFile msgContentVideoFile = StandardObjectMapper.readValue(msgContent, new TypeReference<>() {});
        msgContentVideoFile.VideoUrl = uploadTIMFileToALiOS(msgContentVideoFile.VideoUrl);
        log.info("save video-video------ msgContent-url: {}", msgContentVideoFile.VideoUrl);
        log.info("save video-thumb------ msgContent-url: {}", msgContentVideoFile.ThumbUrl);
        return msgContentVideoFile;
    }

    private TencentMsg.MsgContentImage getContentImage(String msgContent) {
        TencentMsg.MsgContentImage msgContentImage = StandardObjectMapper.readValue(msgContent, new TypeReference<>() {});
        msgContentImage.ImageInfoArray[0].URL = uploadTIMFileToALiOS(msgContentImage.ImageInfoArray[0].URL);
        msgContentImage.ImageInfoArray[1].URL = uploadTIMFileToALiOS(msgContentImage.ImageInfoArray[1].URL);
        msgContentImage.ImageInfoArray[2].URL = uploadTIMFileToALiOS(msgContentImage.ImageInfoArray[2].URL);
        log.info("save image------ 原图url: {}", msgContentImage.ImageInfoArray[0].URL);
        log.info("save image------ 大图url: {}", msgContentImage.ImageInfoArray[1].URL);
        log.info("save image------ 缩略图url: {}", msgContentImage.ImageInfoArray[2].URL);
        return msgContentImage;
    }

    private String uploadTIMFileToALiOS(String downloadUrl) {
        log.info("save image------ 腾讯聊天原始文件url: {}", downloadUrl);
        User system = AppContext.getInstance(UserService.class).getSystem();
        Upload upload = AppContext.getInstance(UploadService.class).upload(system,
                                                                           UploadResource.of(downloadUrl,
                                                                                             UploadType.PUBLIC,
                                                                                             null));
        return new UploadVM(upload).getUrl();
    }

}
