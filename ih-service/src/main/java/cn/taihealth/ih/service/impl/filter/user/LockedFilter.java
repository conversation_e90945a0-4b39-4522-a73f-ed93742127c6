package cn.taihealth.ih.service.impl.filter.user;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.impl.filter.SearchFilter;

import com.gitq.jedi.data.specification.Specifications;

import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

/**
 */
public class LockedFilter implements SearchFilter<User> {

    private final Boolean pattern;

    public LockedFilter(Boolean pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<User> toSpecification() {
        return Specifications.eq("locked", pattern);
    }

    @Override
    public String toExpression() {
        return "locked:" + pattern;
    }

    @Override
    public boolean isValid() {
        return pattern != null;
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof LockedFilter)) {
            return false;
        }

        LockedFilter rhs = (LockedFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
