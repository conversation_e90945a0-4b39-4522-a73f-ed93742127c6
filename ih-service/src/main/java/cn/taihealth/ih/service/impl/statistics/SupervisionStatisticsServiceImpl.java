package cn.taihealth.ih.service.impl.statistics;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.MathUtils;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.MedicalCaseDisease;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.SystemLogStats;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.UserOnline;
import cn.taihealth.ih.domain.crm.CrmTaskDetailResult;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.SystemLogRepository;
import cn.taihealth.ih.repo.cloud.SystemLogStatsRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.service.api.ExportTaskService;
import cn.taihealth.ih.service.api.UploadResource;
import cn.taihealth.ih.service.api.UploadService;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.service.api.nodered.NodeRedClient;
import cn.taihealth.ih.service.api.statistics.SupervisionStatisticsService;
import cn.taihealth.ih.service.dto.export.*;
import cn.taihealth.ih.service.enums.AgeGroup;
import cn.taihealth.ih.service.impl.filter.order.hospitalorder.HospitalOrderSearch;
import cn.taihealth.ih.service.impl.filter.orderworker.OrderWorkerSearch;
import cn.taihealth.ih.service.impl.filter.prescriptionorder.PrescriptionOrderSearch;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.BillFinanceSummaryVM;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.*;
import cn.taihealth.ih.service.vm.statistics.supervision.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.common.web.UrlUtils;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class SupervisionStatisticsServiceImpl implements SupervisionStatisticsService {

    private final SystemLogRepository systemLogRepository;
    private final DataSource dataSource;
    private final NodeRedClient nodeRedClient;
    private final HospitalRepository hospitalRepository;
    private final ExportTaskService exportTaskService;
    private final OfflineOrderRepository offlineOrderRepository;
    private final SystemLogStatsRepository systemLogStatsRepository;

    private final ApplicationProperties applicationProperties;

    @Override
    public List<NameCount> visitsCountStatistics(Hospital hospital, DateUnitForSelect timeUnit, boolean isPlatform, Date startTime, Date endTime) {
        List<Specification<SystemLogStats>> sps = Lists.newArrayList();
        if (isPlatform) {
            sps.add(Specifications.isNull("hospital"));
        } else {
            if (hospital != null) {
                sps.add(Specifications.eq("hospital", hospital));
            }
        }
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        if (startTime != null) {
            sps.add(Specifications.ge("statsDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("statsDate", endTime));
        }
        sps.add(Specifications.eq("statsUnit", StatsUnit.DAY));
        sps.add(Specifications.eq("action", Constants.LogStatsAction.VISIT));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<SystemLogStats> root = criteriaQuery.from(entityManager.getMetamodel().entity(SystemLogStats.class));
        Join<SystemLogStats, Hospital> hospitalJoin = root.join("hospital", JoinType.LEFT);
        criteriaQuery.groupBy(hospitalJoin.get("code"), hospitalJoin.get("name"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(hospitalJoin.get("code"), hospitalJoin.get("name"), criteriaBuilder.sum(root.get("count")));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        List<NameCount> nameCounts = result.stream().map(u -> {
            return new NameCount(u.get(0, String.class), u.get(1, String.class), (int) (long) u.get(2, Long.class));
        }).collect(Collectors.toList());
        nameCounts = nameCounts.stream().sorted(Comparator.comparing(NameCount::getCount).reversed())
                .peek(u -> {
                    if (StringUtils.isBlank(u.getCode())) {
                        u.setCode(Constants.HEALTH_HEAD_CODE);
                        u.setName("健康总部");
                    }
                })
                .collect(Collectors.toList());
        double total = nameCounts.stream().mapToDouble(NameCount::getCount).sum();
        nameCounts.add(0, new NameCount("total", "总计", total));

        return nameCounts;
    }


    @Override
    public List<NameCount> registrationsCountStatistics(Hospital hospital, DateUnitForSelect timeUnit, boolean isPlatform, Date startTime, Date endTime) {
        List<Specification<UserPlatformInfo>> sps = Lists.newArrayList();
        if (isPlatform) {
            sps.add(Specifications.isNull("hospital"));
        } else {
            if (hospital != null) {
                sps.add(Specifications.eq("hospital", hospital));
            }
        }
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        if (startTime != null) {
            sps.add(Specifications.ge("createdDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("createdDate", endTime));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<UserPlatformInfo> root = criteriaQuery.from(entityManager.getMetamodel().entity(UserPlatformInfo.class));
        Join<UserPlatformInfo, Hospital> hospitalJoin = root.join("hospital", JoinType.LEFT);
        criteriaQuery.groupBy(hospitalJoin.get("code"), hospitalJoin.get("name"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(hospitalJoin.get("code"), hospitalJoin.get("name"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        List<NameCount> nameCounts = result.stream().map(u -> {
            return new NameCount(u.get(0, String.class), u.get(1, String.class), (int) (long) u.get(2, Long.class));
        }).collect(Collectors.toList());
        nameCounts = nameCounts.stream().sorted(Comparator.comparing(NameCount::getCount).reversed())
                .peek(u -> {
                    if (StringUtils.isBlank(u.getCode())) {
                        u.setCode(Constants.HEALTH_HEAD_CODE);
                        u.setName("健康总部");
                    }
                })
                .collect(Collectors.toList());
        double total = nameCounts.stream().mapToDouble(NameCount::getCount).sum();
        nameCounts.add(0, new NameCount("total", "总计", total));

        return nameCounts;
    }

    @Override
    public List<NameCount> realUsersCountStatistics(Hospital hospital, boolean isPlatform) {
        List<Specification<UserOnline>> sps = Lists.newArrayList();
        Date time =  DateUtils.addMinutes(new Date(), -2);
        sps.add(Specifications.ge("date", time));
        if (isPlatform) {
            sps.add(Specifications.isNull("hospital"));
        } else {
            if (hospital != null) {
                sps.add(Specifications.eq("hospital", hospital));
            }
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<UserOnline> root = criteriaQuery.from(entityManager.getMetamodel().entity(UserOnline.class));
        Join<UserOnline, Hospital> hospitalJoin = root.join("hospital", JoinType.LEFT);
        criteriaQuery.groupBy(hospitalJoin.get("code"), hospitalJoin.get("name"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(hospitalJoin.get("code"), hospitalJoin.get("name"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        List<NameCount> nameCounts = result.stream().map(u -> {
            return new NameCount(u.get(0, String.class), u.get(1, String.class), (int) (long) u.get(2, Long.class));
        }).collect(Collectors.toList());
        nameCounts = nameCounts.stream().sorted(Comparator.comparing(NameCount::getCount).reversed())
                .peek(u -> {
                    if (StringUtils.isBlank(u.getCode())) {
                        u.setCode(Constants.HEALTH_HEAD_CODE);
                        u.setName("健康总部");
                    }
                })
                .collect(Collectors.toList());
        double total = nameCounts.stream().mapToDouble(NameCount::getCount).sum();
        nameCounts.add(0, new NameCount("total", "总计", total));

        return nameCounts;
    }

    @Override
    public List<NameCount> patientGenderDistribution(Hospital hospital, boolean isPlatform, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<NameCount> nameCounts = Lists.newArrayList();
        if (!isPlatform) {
            List<Specification<ElectronicMedicCard>> sps = Lists.newArrayList();
            if (startTime != null) {
                sps.add(Specifications.ge("createdDate", startTime));
            }
            if (endTime != null) {
                sps.add(Specifications.lt("createdDate", endTime));
            }
            sps.add(Specifications.isTrue("enabled"));
            sps.add(Specifications.isTrue("patient.enabled"));
            if (hospital != null) {
                sps.add(Specifications.eq("patient.hospital", hospital));
            }

            EntityManager entityManager = AppContext.getInstance(EntityManager.class);
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
            Root<ElectronicMedicCard> root = criteriaQuery.from(entityManager.getMetamodel().entity(ElectronicMedicCard.class));
            criteriaQuery.groupBy(root.get("patient").get("gender"));

            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

            criteriaQuery.where(where);

            criteriaQuery.multiselect(root.get("patient").get("gender"), criteriaBuilder.count(root));
            List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

            nameCounts = result.stream().map(u -> {
                return new NameCount(u.get(0, Gender.class).name(), null, (int) (long) u.get(1, Long.class));
            }).collect(Collectors.toList());
        }

        return nameCounts;
    }

    @Override
    public List<NameCount> patientAgeDistribution(Hospital hospital, boolean isPlatform, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<NameCount> nameCounts = Lists.newArrayList();
        if (!isPlatform) {
            List<Specification<ElectronicMedicCard>> sps = Lists.newArrayList();
            if (startTime != null) {
                sps.add(Specifications.ge("createdDate", startTime));
            }
            if (endTime != null) {
                sps.add(Specifications.lt("createdDate", endTime));
            }
            sps.add(Specifications.isTrue("enabled"));
            sps.add(Specifications.isTrue("patient.enabled"));
            if (hospital != null) {
                sps.add(Specifications.eq("patient.hospital", hospital));
            }

            EntityManager entityManager = AppContext.getInstance(EntityManager.class);
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
            Root<ElectronicMedicCard> root = criteriaQuery.from(entityManager.getMetamodel().entity(ElectronicMedicCard.class));
            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

            for (AgeGroup ageGroup : AgeGroup.values()) {
                Date start = ageGroup.getStartTime();
                Date end = ageGroup.getEndTime();
                List<Specification<ElectronicMedicCard>> sp2 = Lists.newArrayList();
                if (start != null) {
                    sp2.add(Specifications.ge("patient.birthday", start));
                }
                if (end != null) {
                    sp2.add(Specifications.lt("patient.birthday", end));
                }
                criteriaQuery.where(where, Specifications.and(sp2).toPredicate(root, criteriaQuery, criteriaBuilder));

                criteriaQuery.multiselect(criteriaBuilder.count(root));
                Tuple result = entityManager.createQuery(criteriaQuery).getSingleResult();
                Long count = result.get(0, Long.class);
                nameCounts.add(new NameCount(ageGroup.name(), ageGroup.getName(), count == null ? 0 : (int) (long) count));

            }
        }

        return nameCounts;
    }

//    @Override
//    public TrafficStats getTrafficStatic(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
//        TrafficStats trafficStats = new TrafficStats();
//        startTime = timeUnit.getStartDate(startTime, endTime);
//        endTime = timeUnit.getEndDate(startTime, endTime);
//
//        // 在线咨询
//        List<Specification<Order>> sps = Lists.newArrayList();
//        if (hospital != null) {
//            sps.add(Specifications.eq("hospital", hospital));
//        }
//        if (startTime != null) {
//            sps.add(Specifications.ge("createdDate", startTime));
//        }
//        if (endTime != null) {
//            sps.add(Specifications.lt("createdDate", endTime));
//        }
//        sps.add(Specifications.ne("status", Order.OrderStatus.DRAFT));
//        sps.add(Specifications.eq("onlineType", OnlineType.ONLINE));
//
//        long consultCount = AppContext.getInstance(OrderRepository.class).count(Specifications.and(Specifications.and(sps),
//                Specifications.eq("orderType", ClinicType.CONSULT)));
//        trafficStats.setConsultCount((int) consultCount);
//
//        // 在线复诊
//        long returnVisitCount = AppContext.getInstance(OrderRepository.class).count(Specifications.and(Specifications.and(sps),
//                Specifications.eq("orderType", ClinicType.OUT)));
//        trafficStats.setReturnVisitCount((int) returnVisitCount);
//
//        return trafficStats;
//    }

    @Override
    public List<NameCount> getDiseaseStats(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime, int top) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<MedicalCaseDisease>> sps = Lists.newArrayList();
        if (startTime != null) {
            Date finalStartTime = startTime;
            sps.add((Specification<MedicalCaseDisease>) (root, query, criteriaBuilder) ->
                    criteriaBuilder.greaterThanOrEqualTo(root.get("medicalCase").get("order").get("createdDate"), finalStartTime));
        }
        if (endTime != null) {
            Date finalEndTime = endTime;
            sps.add((Specification<MedicalCaseDisease>) (root, query, criteriaBuilder) ->
                    criteriaBuilder.lessThan(root.get("medicalCase").get("order").get("createdDate"), finalEndTime));
        }
        if (hospital != null) {
            sps.add((Specification<MedicalCaseDisease>) (root, query, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("medicalCase").get("order").get("hospital"), hospital));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<MedicalCaseDisease> root = criteriaQuery.from(entityManager.getMetamodel().entity(MedicalCaseDisease.class));
        criteriaQuery.groupBy(root.get("diseaseCode"), root.get("diseaseName"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);
        criteriaQuery.orderBy(criteriaBuilder.desc(criteriaBuilder.count(root)));
        criteriaQuery.multiselect(root.get("diseaseCode"), root.get("diseaseName"), criteriaBuilder.count(root));

        TypedQuery<Tuple> query = entityManager.createQuery(criteriaQuery);
        if (top > 0) {
            query.setMaxResults(top);
        } else {
            query.setMaxResults(5);
        }
        List<Tuple> result = query.getResultList();

        List<NameCount> nameCounts = result.stream().map(u -> {
            return new NameCount(u.get(0, String.class), u.get(1, String.class), (int) (long) u.get(2, Long.class));
        }).collect(Collectors.toList());
        return nameCounts;
    }

    @Override
    public List<SatisfactionRate> getHisSatisfactionRate(Hospital hospital, DateUnitForSelect dateUnit, Date startTime, Date endTime) {
        List<Hospital> hospitals = getFollowUpUseHospital(hospital);

        startTime = dateUnit.getStartDate(startTime, endTime);
        endTime = dateUnit.getEndDate(startTime, endTime);
        Date finalStartTime = startTime;
        Date finalEndTime = endTime;
        return hospitals.stream().map(u -> {
            try {
                SatisfactionGradeStatistics details = nodeRedClient.getSatisfactionGradeStatistics(u.getCode(), finalStartTime, finalEndTime);
                return new SatisfactionRate(u, details);
            } catch (Exception e) {
                log.error("随访查询失败", e);
                return null;
            }
        }).filter(Objects::nonNull)
                .sorted(Comparator.comparing(SatisfactionRate::getGrade).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 如果hospital是null, 返回开启了使用医院随访的医院，
     * 如果hospital不是null，
     *      如果这个医院开启了使用医院随访的医院，返回这个医院，否则返回空数组
     * @param hospital
     * @return
     */
    private List<Hospital> getFollowUpUseHospital(Hospital hospital) {
        List<Hospital> hospitals = Lists.newArrayList();
//        hospitals.add(AppContext.getInstance(HospitalRepository.class).findOneByCode("bgzyy").get());
        if (hospital != null) {
            boolean useHospital = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.FOLLOW_UP_USE_HOSPITAL);
            if (!useHospital) {
                return Lists.newArrayList();
            }
            hospitals.add(hospital);
        } else {

            EntityManager entityManager = AppContext.getInstance(EntityManager.class);
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Hospital> criteriaQuery = criteriaBuilder.createQuery(Hospital.class);
            Root<Hospital> root = criteriaQuery.from(entityManager.getMetamodel().entity(Hospital.class));
            Subquery<Long> subquery = criteriaQuery.subquery(Long.class);
            Root<HospitalSetting> subSetting = subquery.from(HospitalSetting.class);
            subquery.where(criteriaBuilder.and(criteriaBuilder.and(
                    criteriaBuilder.equal(subSetting.get("hospital"), root),
                    criteriaBuilder.equal(subSetting.get("key"), HospitalSettingKey.FOLLOW_UP_USE_HOSPITAL),
                    criteriaBuilder.equal(subSetting.get("value"), "true"))));
            subquery.select(subSetting.get("id"));

            criteriaQuery.where(criteriaBuilder.and(
                    criteriaBuilder.exists(subquery),
                    criteriaBuilder.isTrue(root.get("enabled"))));

            hospitals = entityManager.createQuery(criteriaQuery).getResultList();
        }
        return hospitals;
    }

    @Override
    public List<SatisfactionRateDetail> getHisSatisfactionRateDetails(Hospital hospital, DateUnitForSelect dateUnit, Date startTime, Date endTime) {
        boolean useHospital = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.FOLLOW_UP_USE_HOSPITAL);
        if (!useHospital) {
            return Lists.newArrayList();
        }

        startTime = dateUnit.getStartDate(startTime, endTime);
        endTime = dateUnit.getEndDate(startTime, endTime);
        List<SatisfactionGradeStatisticsDetail> details = nodeRedClient.getSatisfactionGradeStatisticsDetails(hospital.getCode(), startTime, endTime);

        return details.stream().map(SatisfactionRateDetail::new).collect(Collectors.toList());
    }

    @Override
    public List<SatisfactionPerformance> getHisSatisfactionPerformance(Hospital hospital, DateUnitForSelect dateUnit, Date startTime, Date endTime) {
        List<Hospital> hospitals = getFollowUpUseHospital(hospital);

        startTime = dateUnit.getStartDate(startTime, endTime);
        endTime = dateUnit.getEndDate(startTime, endTime);
        Date finalStartTime = startTime;
        Date finalEndTime = endTime;
        return hospitals.stream().map(u -> {
            try {
                SatisfactionPerformanceStatistics details = nodeRedClient.getSatisfactionPerformanceStatistics(u.getCode(), finalStartTime, finalEndTime);
                return new SatisfactionPerformance(u, details);
            } catch (Exception e) {
                log.error("随访查询失败", e);
                return null;
            }
        }).filter(Objects::nonNull)
                .sorted(Comparator.comparing(SatisfactionPerformance::getSuccessedRate).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<SatisfactionPerformance> getSatisfactionPerformanceDetails(Hospital hospital, DateUnitForSelect dateUnit, Date startTime, Date endTime) {
        boolean useHospital = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.FOLLOW_UP_USE_HOSPITAL);
        if (!useHospital) {
            return Lists.newArrayList();
        }

        startTime = dateUnit.getStartDate(startTime, endTime);
        endTime = dateUnit.getEndDate(startTime, endTime);
        List<SatisfactionPerformanceStatisticsDetail> details = nodeRedClient.getSatisfactionPerformanceStatisticsDetails(hospital.getCode(), startTime, endTime);

        return details.stream().map(SatisfactionPerformance::new).collect(Collectors.toList());
    }

    @Override
    public List<SatisfactionPerformance> getWardSatisfactionPerformance(Hospital hospital, DateUnitForSelect dateUnit, Date startTime, Date endTime) {
        List<Hospital> hospitals = getFollowUpUseHospital(hospital);

        startTime = dateUnit.getStartDate(startTime, endTime);
        endTime = dateUnit.getEndDate(startTime, endTime);
        Date finalStartTime = startTime;
        Date finalEndTime = endTime;
        return hospitals.stream().map(u -> {
            try {
                WardPerformanceStatistics details = nodeRedClient.getWardPerformanceStatistics(u.getCode(), finalStartTime, finalEndTime);
                return new SatisfactionPerformance(u, details);
            } catch (Exception e) {
                log.error("随访查询失败", e);
                return null;
            }
        }).filter(Objects::nonNull)
                .sorted(Comparator.comparing(SatisfactionPerformance::getSuccessedRate).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<SatisfactionPerformance> getWardSatisfactionPerformanceDetails(Hospital hospital, DateUnitForSelect dateUnit, Date startTime, Date endTime) {
        boolean useHospital = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.FOLLOW_UP_USE_HOSPITAL);
        if (!useHospital) {
            return Lists.newArrayList();
        }

        startTime = dateUnit.getStartDate(startTime, endTime);
        endTime = dateUnit.getEndDate(startTime, endTime);
        List<WardPerformanceStatisticsDetail> details = nodeRedClient.getWardPerformanceStatisticsDetails(hospital.getCode(), startTime, endTime);

        return details.stream().map(SatisfactionPerformance::new).collect(Collectors.toList());
    }

    @Override
    public List<NameCount> orderPatientGenderDistribution(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<Order>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("registeredDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("registeredDate", endTime));
        }
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Order> root = criteriaQuery.from(entityManager.getMetamodel().entity(Order.class));
        criteriaQuery.groupBy(root.get("patient").get("gender"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(root.get("patient").get("gender"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        List<NameCount> nameCounts = result.stream().map(u -> {
            return new NameCount(u.get(0, Gender.class).name(), null, (int) (long) u.get(1, Long.class));
        }).collect(Collectors.toList());

        return nameCounts;
    }

    @Override
    public List<NameCount> orderPatientAgeDistribution(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<NameCount> nameCounts = Lists.newArrayList();
        List<Specification<Order>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("registeredDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("registeredDate", endTime));
        }
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Order> root = criteriaQuery.from(entityManager.getMetamodel().entity(Order.class));
        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        for (AgeGroup ageGroup : AgeGroup.values()) {
            Date start = ageGroup.getStartTime();
            Date end = ageGroup.getEndTime();
            List<Specification<Order>> sp2 = Lists.newArrayList();
            if (start != null) {
                sp2.add(Specifications.ge("patient.birthday", start));
            }
            if (end != null) {
                sp2.add(Specifications.lt("patient.birthday", end));
            }
            criteriaQuery.where(where, Specifications.and(sp2).toPredicate(root, criteriaQuery, criteriaBuilder));

            criteriaQuery.multiselect(criteriaBuilder.count(root));
            Tuple result = entityManager.createQuery(criteriaQuery).getSingleResult();
            Long count = result.get(0, Long.class);
            nameCounts.add(new NameCount(ageGroup.name(), ageGroup.getName(), count == null ? 0 : (int) (long) count));
        }

        return nameCounts;
    }

    @Override
    public List<NameCount> orderPatientRegionDistribution(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<Order>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("registeredDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("registeredDate", endTime));
        }
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Order> root = criteriaQuery.from(entityManager.getMetamodel().entity(Order.class));
        criteriaQuery.groupBy(root.get("patient").get("province"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(root.get("patient").get("province"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        List<NameCount> nameCounts = result.stream().map(u -> {
            String province = u.get(0, String.class);
            if (StringUtils.isBlank(province)) {
                province = null;
            }
            return new NameCount(province, province, (int) (long) u.get(1, Long.class));
        }).collect(Collectors.toList());
//        nameCounts.stream().collect(Collectors.toMap(k -> k.getCode(), (v1, v2) -> new NameCount(v1.getCode(), v1.getName(), v1.getCount() + v2.getCount())))
        nameCounts = nameCounts.stream().collect(Collectors.toMap(NameCount::getCode,
                k -> k,
                (v1, v2) -> new NameCount(v1.getCode(), v1.getName(), v1.getCount() + v2.getCount()))).values()
                .stream().sorted(Comparator.comparing(NameCount::getCount).reversed())
                .collect(Collectors.toList());


        return nameCounts;
    }

    @Override
    public Map<String, List<TimeData>> getOrderCount(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime, String query) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<Order>> sps = Lists.newArrayList();
        sps.add(HospitalOrderSearch.of(query).toSpecification());
        sps.add(Specifications.isNotNull("registeredDate"));
        if (startTime != null) {
            sps.add(Specifications.ge("registeredDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("registeredDate", endTime));
        }
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Order> root = criteriaQuery.from(entityManager.getMetamodel().entity(Order.class));

        Expression<Integer> yearExpression = criteriaBuilder.function("year", Integer.class, root.get("registeredDate"));
        Expression<Integer> monthExpression = criteriaBuilder.function("month", Integer.class, root.get("registeredDate"));
        Expression<Integer> dayExpression = criteriaBuilder.function("day", Integer.class, root.get("registeredDate"));
        Expression<Integer> hourExpression = criteriaBuilder.function("hour", Integer.class, root.get("registeredDate"));

        criteriaQuery.groupBy(yearExpression, monthExpression, dayExpression, hourExpression);

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(yearExpression, monthExpression, dayExpression, hourExpression, criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        List<StrTimeData> data = result.stream().map(u -> {
            int year = u.get(0, Integer.class);
            int month = u.get(1, Integer.class);
            int day = u.get(2, Integer.class);
            int hour = u.get(3, Integer.class);
            long count = u.get(4, Long.class);
            String strDay = year + "-" + StringUtils.leftPad(month + "", 2, "0") + "-" + StringUtils.leftPad(day + "", 2, "0");
            Calendar calendar = Calendar.getInstance();
            calendar.clear();
            calendar.set(year, month - 1, day, hour, 0, 0);
            Date time = calendar.getTime();
            return new StrTimeData(strDay, time, (int) count);
        }).collect(Collectors.toList());

        if (startTime != null && endTime != null) {
            data = supplementaryData(startTime, endTime, data, 3600);
        }
        Map<String, List<TimeData>> dataMap = new HashMap<>();
        data.stream().collect(Collectors.groupingBy(StrTimeData::getDay))
                .entrySet().forEach((en -> {
                    dataMap.put(en.getKey(), en.getValue().stream().map(v -> new TimeData(v.getTime(), v.getData())).collect(Collectors.toList()));
                }));

        return dataMap;
    }

    @Override
    public Map<String, List<TimeData>> getPrescriptionCount(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime, String query) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<PrescriptionOrder>> sps = Lists.newArrayList();
        sps.add(PrescriptionOrderSearch.of(query).toSpecification());
        if (startTime != null) {
            sps.add(Specifications.ge("createdDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("createdDate", endTime));
        }
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<PrescriptionOrder> root = criteriaQuery.from(entityManager.getMetamodel().entity(PrescriptionOrder.class));

        Expression<Integer> yearExpression = criteriaBuilder.function("year", Integer.class, root.get("createdDate"));
        Expression<Integer> monthExpression = criteriaBuilder.function("month", Integer.class, root.get("createdDate"));
        Expression<Integer> dayExpression = criteriaBuilder.function("day", Integer.class, root.get("createdDate"));
        Expression<Integer> hourExpression = criteriaBuilder.function("hour", Integer.class, root.get("createdDate"));

        criteriaQuery.groupBy(yearExpression, monthExpression, dayExpression, hourExpression);

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(yearExpression, monthExpression, dayExpression, hourExpression, criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        List<StrTimeData> data = result.stream().map(u -> {
            int year = u.get(0, Integer.class);
            int month = u.get(1, Integer.class);
            int day = u.get(2, Integer.class);
            int hour = u.get(3, Integer.class);
            long count = u.get(4, Long.class);
            String strDay = year + "-" + StringUtils.leftPad(month + "", 2, "0") + "-" + StringUtils.leftPad(day + "", 2, "0");
            Calendar calendar = Calendar.getInstance();
            calendar.clear();
            calendar.set(year, month - 1, day, hour, 0, 0);
            Date time = calendar.getTime();
            return new StrTimeData(strDay, time, (int) count);
        }).collect(Collectors.toList());

        if (startTime != null && endTime != null) {
            data = supplementaryData(startTime, endTime, data, 3600);
        }
        Map<String, List<TimeData>> dataMap = new HashMap<>();
        data.stream().collect(Collectors.groupingBy(StrTimeData::getDay))
                .entrySet().forEach((en -> {
                    dataMap.put(en.getKey(), en.getValue().stream().map(v -> new TimeData(v.getTime(), v.getData())).collect(Collectors.toList()));
                }));

        return dataMap;
    }

    @Override
    public Map<String, List<TimeData>> getOrderApplauseRate(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime, String query) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<OrderWorker>> sps = Lists.newArrayList();
        sps.add(OrderWorkerSearch.of(query).toSpecification());
        sps.add(Specifications.isNotNull("evaluateDate"));
        if (startTime != null) {
            sps.add(Specifications.ge("evaluateDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("evaluateDate", endTime));
        }
        if (hospital != null) {
            sps.add((Specification<OrderWorker>) (root, query1, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("order").get("hospital"), hospital));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<OrderWorker> root = criteriaQuery.from(entityManager.getMetamodel().entity(OrderWorker.class));

        Expression<Integer> yearExpression = criteriaBuilder.function("year", Integer.class, root.get("evaluateDate"));
        Expression<Integer> monthExpression = criteriaBuilder.function("month", Integer.class, root.get("evaluateDate"));
        Expression<Integer> dayExpression = criteriaBuilder.function("day", Integer.class, root.get("evaluateDate"));
        Expression<Integer> hourExpression = criteriaBuilder.function("hour", Integer.class, root.get("evaluateDate"));

        criteriaQuery.groupBy(yearExpression, monthExpression, dayExpression, hourExpression);

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(yearExpression, monthExpression, dayExpression, hourExpression, criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        List<StrTimeData> data = result.stream().map(u -> {
            int year = u.get(0, Integer.class);
            int month = u.get(1, Integer.class);
            int day = u.get(2, Integer.class);
            int hour = u.get(3, Integer.class);
            long count = u.get(4, Long.class);
            String strDay = year + "-" + StringUtils.leftPad(month + "", 2, "0") + "-" + StringUtils.leftPad(day + "", 2, "0");
            Calendar calendar = Calendar.getInstance();
            calendar.clear();
            calendar.set(year, month - 1, day, hour, 0, 0);
            Date time = calendar.getTime();
            return new StrTimeData(strDay, time, (int) count);
        }).collect(Collectors.toList());

        if (startTime != null && endTime != null) {
            data = supplementaryData(startTime, endTime, data, 3600);
        }
        Map<String, List<TimeData>> dataMap = new HashMap<>();
        data.stream().collect(Collectors.groupingBy(StrTimeData::getDay))
                .entrySet().forEach((en -> {
                    dataMap.put(en.getKey(), en.getValue().stream().map(v -> new TimeData(v.getTime(), v.getData())).collect(Collectors.toList()));
                }));

        return dataMap;
    }

    /**
     * 补全时间数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param data 原数据
     * @param interval 时间间隔，单位秒
     * @return
     */
    private List<StrTimeData> supplementaryData(Date startTime, Date endTime, List<StrTimeData> data, int interval) {
        Map<Date, StrTimeData> map = data.stream().collect(Collectors.toMap(StrTimeData::getTime, v -> v));
        int count = (int) ((endTime.getTime() - startTime.getTime()) / 1000 / interval);
        List<StrTimeData> list = new ArrayList<>(count * 4 / 3);
        Date date = startTime;
        Date now = new Date();
        while (date.compareTo(endTime) < 0) {
            if (map.containsKey(date)) {
                list.add(map.get(date));
            } else {
                list.add(new StrTimeData(DataTypes.DATE.asString(date, "yyyy-MM-dd"), date, 0));
            }
            date = new Date(date.getTime() + 1000L * interval);
            if (date.compareTo(now) > 0) {
                break;
            }
        }

        return list;
    }

    @Override
    public TrafficStats getTrafficStatic(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        return getTrafficStats(hospital, startTime, endTime);
    }

    private TrafficStats getTrafficStats(Hospital hospital, Date startTime, Date endTime) {
        TrafficStats trafficStats = new TrafficStats();
        List<Specification<Bill>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("orderTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("orderTime", endTime));
        }
        if (hospital != null) {
            sps.add((Specification<Bill>) (root, query1, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("hospital"), hospital));
        }
        sps.add(Specifications.ne("billPayStatus", BillPayStatusEnum.REFUNDED));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));

        criteriaQuery.groupBy(root.get("billServiceType"));
        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(root.get("billServiceType"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        result.forEach(u -> {
            ProjectTypeEnum type = u.get(0, ProjectTypeEnum.class);
            long count = u.get(1, Long.class);
            switch (type) {
                case APPOINTMENT_REGISTRATION:
                    // 预约挂号
                    trafficStats.setAppointmentCount((int) count);
                    break;
                case PHYSICAL_EXAMINATION_APPOINTMENT:
                    // 体检预约
                    trafficStats.setPhysicalAppointmentCount((int) count);
                    break;
                case OUTPATIENT_PAYMENT:
                    // 门诊缴费
                    trafficStats.setOutpatientPaymentCount((int) count);
                    break;
                case HOSPITALIZATION_DEPOSIT:
                    // 住院预交
                    trafficStats.setHospitalizationDepositCount((int) count);
                    break;
                case ONLINE_CONSULTATION:
                    // 在线咨询
                    trafficStats.setConsultCount((int) count);
                    break;
                case ONLINE_REVISIT:
                    // 在线复诊
                    trafficStats.setReturnVisitCount((int) count);
                    break;
                case MEDICAL_RECORD_COPY_APPOINTMENT:
                    // 病案复印预约
                    trafficStats.setMedicalRecordCopyingCount((int) count);
                    break;
                default:
                    break;
            }

        });
        trafficStats.setReportQueryCount((int) getLisRisReportCount(hospital, startTime, endTime));
        return trafficStats;
    }


    @Override
    public MoreTrafficRatio getMoreTrafficRatio(Hospital hospital, StatisticalMethods method, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        if (startTime == null || endTime == null) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("必须指定时间范围");
        }
        // 本期, 上期
        List<Date> start;
        List<Date> end;
        if (method == StatisticalMethods.MONTHLY_MOM) {
            start = List.of(startTime, DateUtils.addMonths(startTime, -1));
            end = List.of(endTime, DateUnitForSelect.MONTHLY.getEndDate(DateUtils.addMonths(startTime, -1), null));
        } else {
            start = List.of(startTime, DateUtils.addYears(startTime, -1));
            end = List.of(endTime, DateUnitForSelect.MONTHLY.getEndDate(DateUtils.addYears(startTime, -1), null));
        }
        MoreTrafficStats stats1 = getMoreTraffic(hospital, start.get(0), end.get(0));
        MoreTrafficStats stats2 = getMoreTraffic(hospital, start.get(1), end.get(1));

        MoreTrafficRatio ratio = new MoreTrafficRatio();
        ratio.setMethod(method);
        ratio.setDate(startTime);
        Map<String, Double> map1 = StandardObjectMapper.getInstance().convertValue(stats1, new TypeReference<>() {});
        Map<String, Double> map2 = StandardObjectMapper.getInstance().convertValue(stats2, new TypeReference<>() {});
        Map<String, Double> map = new HashMap<>();
        // MoreTrafficStats和MoreTrafficRatioStats字段只有Count和Ratio的区别
        for (Map.Entry<String, Double> ent : map1.entrySet()) {
            String key = ent.getKey();
            Double value = ent.getValue();
            if (map2.containsKey(key) && map2.get(key) != 0) {
                value = value / map2.get(key) - 1;
            } else {
                value = null;
            }
            map.put(key.replace("Count", "Ratio"), value);
        }
        ratio.setData(StandardObjectMapper.getInstance().convertValue(map, new TypeReference<>() {}));
        return ratio;
    }

    /**
     * 检查检验报告查询次数统计
     * @param hospital
     * @param startTime
     * @param endTime
     * @return
     */
    private long getLisRisReportCount(Hospital hospital, Date startTime, Date endTime) {
        List<Specification<SystemLogStats>> sps = Lists.newArrayList();
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        if (startTime != null) {
            sps.add(Specifications.ge("statsDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("statsDate", endTime));
        }
        sps.add(Specifications.eq("statsUnit", StatsUnit.DAY));
        sps.add(Specifications.eq("action", Constants.LogStatsAction.REPORT_VISIT));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<SystemLogStats> root = criteriaQuery.from(entityManager.getMetamodel().entity(SystemLogStats.class));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(criteriaBuilder.sum(root.get("count")));
        Tuple result = entityManager.createQuery(criteriaQuery).getSingleResult();
        if (result == null) {
            return 0;
        }
        Long count = result.get(0, Long.class);
        return count == null ? 0 : count;
    }

    private List<NameCount> getLisRisReportList(Hospital hospital, Date startTime, Date endTime) {
        List<Specification<SystemLogStats>> sps = Lists.newArrayList();
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        if (startTime != null) {
            sps.add(Specifications.ge("statsDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("statsDate", endTime));
        }
        sps.add(Specifications.eq("statsUnit", StatsUnit.DAY));
        sps.add(Specifications.eq("action", Constants.LogStatsAction.REPORT_VISIT));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<SystemLogStats> root = criteriaQuery.from(entityManager.getMetamodel().entity(SystemLogStats.class));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        criteriaQuery.groupBy(root.get("hospital"));

        criteriaQuery.multiselect(root.get("hospital").get("id"));
        List<Tuple> totalTuple = entityManager.createQuery(criteriaQuery).getResultList();
        if (CollectionUtils.isEmpty(totalTuple)) {
            return Lists.newArrayList();
        }
        criteriaQuery.multiselect(root.get("hospital").get("code"), root.get("hospital").get("name"),
                criteriaBuilder.sum(root.get("count")));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        return result.stream().map(u -> {
            NameCount nameCount = new NameCount();
            nameCount.setCode(u.get(0, String.class));
            nameCount.setName(u.get(1, String.class));
            nameCount.setCount(u.get(2, Long.class));
            return nameCount;
        }).collect(Collectors.toList());
    }

    /**
     * 检查检验报告查询次数统计
     * @param hospital
     * @param startTime
     * @param endTime
     * @return
     */
    private List<StrTimeData> getLisRisReportTimesCount(Hospital hospital, Date startTime, Date endTime) {
        List<Specification<SystemLogStats>> sps = Lists.newArrayList();
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        if (startTime != null) {
            sps.add(Specifications.ge("statsDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("statsDate", endTime));
        }
        sps.add(Specifications.eq("statsUnit", StatsUnit.HOUR));
        sps.add(Specifications.eq("action", Constants.LogStatsAction.REPORT_VISIT));
        List<SystemLogStats> result = systemLogStatsRepository.findAll(Specifications.and(sps));
        Map<Date, SystemLogStats> data = result.stream().collect(Collectors.toMap(SystemLogStats::getStatsDate, v -> v,
                (v1, v2) -> {
                    v1.setCount(v1.getCount() + v2.getCount());
                    return v1;
                }));

        return data.values().stream().map(u -> {
            String strDay = TimeUtils.dateToString(u.getStatsDate(), "yyyy-MM-dd");
            return new StrTimeData(strDay, u.getStatsDate(), u.getCount());
        }).collect(Collectors.toList());
    }

    @Override
    public MoreTrafficStats getMoreTraffic(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        return getMoreTraffic(hospital, startTime, endTime);
    }

    @Override
    public UploadVM exportBusinessStatistics(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime, HttpServletResponse response) {
        List<BusinessStatisticsExportDTO> data = Lists.newArrayList();
        MoreTrafficStats moreTraffic = getMoreTraffic(hospital, timeUnit, startTime, endTime);
        data.add(new BusinessStatisticsExportDTO(moreTraffic, TimeUtils.dateToString(startTime, "yyyy-MM-dd") + "至" + TimeUtils.dateToString(endTime, "yyyy-MM-dd")));

        Date preWeekToday = TimeUtils.getPreWeekToday();
        MoreTrafficRatio momMoreTrafficRatio = getMoreTrafficRatio(hospital, StatisticalMethods.MONTHLY_MOM, DateUnitForSelect.MONTHLY, preWeekToday, null);
        MoreTrafficRatio yoyMoreTrafficRatio = getMoreTrafficRatio(hospital, StatisticalMethods.MONTHLY_YOY, DateUnitForSelect.MONTHLY, preWeekToday, null);
        data.add(new BusinessStatisticsExportDTO(momMoreTrafficRatio.getData(), TimeUtils.dateToString(preWeekToday, "yyyy-MM") + "月环比增长率（月环比）"));
        data.add(new BusinessStatisticsExportDTO(yoyMoreTrafficRatio.getData(), TimeUtils.dateToString(preWeekToday, "yyyy-MM") + "月同比增长率（月同比）"));

        MoreTrafficStats moreTrafficToday = getMoreTraffic(hospital, DateUnitForSelect.DAY, null, null);
        MoreTrafficStats moreTrafficYesterday = getMoreTraffic(hospital, DateUnitForSelect.YESTERDAY, null, null);
        data.add(new BusinessStatisticsExportDTO(moreTrafficToday, TimeUtils.dateToString(new Date(), "yyyy-MM-dd") + "（当日）"));
        data.add(new BusinessStatisticsExportDTO(moreTrafficYesterday, TimeUtils.dateToString(DateUtils.addDays(new Date(), -1), "yyyy-MM-dd") + "（昨日）"));

        return exportTaskService.exportBusinessStatistics(data, response);
    }

    @Override
    public UploadVM exportSatisfaction(Hospital hospital, DateUnitForSelect dateUnit, Date startTime, Date endTime,
                                       HttpServletResponse response) {
        List<SatisfactionPerformance> ward = getWardSatisfactionPerformance(hospital, dateUnit, startTime, endTime);
        List<SatisfactionExportDTO> wardExport = Lists.newArrayList();
        for (int i = 0; i < ward.size(); i++) {
            wardExport.add(new SatisfactionExportDTO(ward.get(i), i + 1));
        }

        List<SatisfactionPerformance> performance = getHisSatisfactionPerformance(hospital, dateUnit, startTime, endTime);
        List<SatisfactionExportDTO> performanceExport = Lists.newArrayList();
        for (int i = 0; i < performance.size(); i++) {
            performanceExport.add(new SatisfactionExportDTO(performance.get(i), i + 1));
        }

        List<SatisfactionRate> rate = getHisSatisfactionRate(hospital, dateUnit, startTime, endTime);
        List<SatisfactionRateExportDTO> rateExport = Lists.newArrayList();
        for (int i = 0; i < rate.size(); i++) {
            rateExport.add(new SatisfactionRateExportDTO(rate.get(i), i + 1));
        }
        return exportTaskService.exportSatisfaction(wardExport, performanceExport, rateExport, startTime, endTime, response);
    }

    @Override
    public UploadVM exportOperationStatistics(Hospital hospital, DateUnitForSelect timeUnit, boolean platform,
                                          Date startTime, Date endTime, HttpServletResponse response) {
        List<OperationalNameCountExportDTO> data = Lists.newArrayList();
        //访问量
        List<NameCount> visitsTimeCount = visitsCountStatistics(hospital, timeUnit, platform, startTime, endTime);
        List<NameCount> visitsAll = visitsCountStatistics(hospital, DateUnitForSelect.TILL_NOW, platform, startTime, endTime);
        Map<String, NameCount> visitsMap = visitsTimeCount.stream().collect(Collectors.toMap(NameCount::getCode, v -> v));
        List<NameCountTotalExportDTO> visitTotals = visitsAll.stream().map(u -> {
            String count = visitsMap.containsKey(u.getCode()) ? ((int) visitsMap.get(u.getCode()).getCount()) + "" : "0";
            return new NameCountTotalExportDTO(u.getCode(), u.getName(), count, ((int) u.getCount()) + "");
        }).collect(Collectors.toList());
        data.add(new OperationalNameCountExportDTO(visitTotals, "访问次数"));
        //注册量
        List<NameCount> registrationsTimeCount = registrationsCountStatistics(hospital, timeUnit, platform, startTime, endTime);
        List<NameCount> registrationsAll =  registrationsCountStatistics(hospital, DateUnitForSelect.TILL_NOW, platform, startTime, endTime);
        Map<String, NameCount> registrationsMap = registrationsTimeCount.stream().collect(Collectors.toMap(NameCount::getCode, v -> v));
        List<NameCountTotalExportDTO> registrationsTotals = registrationsAll.stream().map(u -> {
            String count = registrationsMap.containsKey(u.getCode()) ? ((int) registrationsMap.get(u.getCode()).getCount()) + "" : "0";
            return new NameCountTotalExportDTO(u.getCode(), u.getName(), count, ((int) u.getCount()) + "");
        }).collect(Collectors.toList());
        data.add(new OperationalNameCountExportDTO(registrationsTotals, "注册量"));

        //热门科室
        List<DeptDoctorName> hotDeptRanking = getHotDeptRanking(hospital, timeUnit, startTime, endTime, 5);
        List<NameCountTotalExportDTO> hotDeptExports = hotDeptRanking.stream()
                .map(o -> new NameCountTotalExportDTO(o.getDeptCode(), o.getHospitalName(), o.getDeptName(), o.getCount() + ""))
                .collect(Collectors.toList());
        data.add(new OperationalNameCountExportDTO(hotDeptExports, "热门科室排名"));
        //热门医生
        List<DeptDoctorName> hotDoctorRanking = getHotDoctorRanking(hospital, timeUnit, startTime, endTime, 5);
        List<NameCountTotal4DocExportDTO> hotDoctorExports = hotDoctorRanking.stream()
                .map(o -> new NameCountTotal4DocExportDTO(o.getDoctorName(), o.getHospitalName(), o.getDeptName(), o.getCount() + ""))
                .collect(Collectors.toList());

        //患者分布
        List<NameCountTotal4PatientExportDTO> patientTotals = patientGenderDistribution(hospital, platform, timeUnit, startTime, endTime)
                .stream().map(u -> new NameCountTotal4PatientExportDTO("FEMALE".equalsIgnoreCase(u.getCode()) ? "女性":"男性" , u.getName(), "", String.valueOf((int) Math.floor(u.getCount()))))
                .collect(Collectors.toList());
        patientTotals.addAll(patientAgeDistribution(hospital, platform, timeUnit, startTime, endTime)
                .stream().map(u -> new NameCountTotal4PatientExportDTO(u.getName() , u.getName(), "", String.valueOf((int) Math.floor(u.getCount()))))
                .collect(Collectors.toList()));
        // 满意度统计调查
        List<SatisfactionStatic> satisfaction = getSatisfaction(hospital, timeUnit, startTime, endTime);
        for (SatisfactionStatic satisfactionStatic : satisfaction) {
            if (satisfactionStatic.getInpatientCount() == null) {
                satisfactionStatic.setInpatientCount(0L);
            }
            if (satisfactionStatic.getInpatientRate() == null) {
                satisfactionStatic.setInpatientRate(0D);
            }
            if (satisfactionStatic.getOutpatientCount() == null) {
                satisfactionStatic.setOutpatientCount(0L);
            }
            if (satisfactionStatic.getOutpatientRate() == null) {
                satisfactionStatic.setOutpatientRate(0D);
            }
            if (StringUtils.isBlank(satisfactionStatic.getHospitalCode())) {
                satisfactionStatic.setHospitalName("健康总部");
            }
        }
        // 各科室医生预约挂号人次
        List<DeptDoctorName> eachDepartmentAppointment = getEachDepartmentAppointment(hospital, timeUnit, startTime, endTime);
        List<NameCountTotal4DocExportDTO> eachDepartmentAppointmentExports = eachDepartmentAppointment.stream()
                .map(o -> new NameCountTotal4DocExportDTO(o.getDoctorName(), o.getHospitalName(), o.getDeptName(),
                        o.getCount() + ""))
                .collect(Collectors.toList());
        return exportTaskService.exportOperationalStatistics(data, hotDoctorExports, patientTotals, satisfaction, eachDepartmentAppointmentExports, response, startTime, endTime);
    }

    private MoreTrafficStats getMoreTraffic(Hospital hospital, Date startTime, Date endTime) {
        MoreTrafficStats trafficStats = new MoreTrafficStats();
        List<Specification<Bill>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("orderTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("orderTime", endTime));
        }
        if (hospital != null) {
            sps.add((Specification<Bill>) (root, query1, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("hospital"), hospital));
        }
        sps.add(Specifications.ne("billPayStatus", BillPayStatusEnum.REFUNDED));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));

        criteriaQuery.groupBy(root.get("billServiceType"));
        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(root.get("billServiceType"), criteriaBuilder.count(root));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        result.forEach(u -> {
            ProjectTypeEnum type = u.get(0, ProjectTypeEnum.class);
            long count = u.get(1, Long.class);
            switch (type) {
                case APPOINTMENT_REGISTRATION:
                    // 预约挂号
                    trafficStats.setAppointmentCount((double) count);
                    break;
                case PHYSICAL_EXAMINATION_APPOINTMENT:
                    // 体检预约
                    trafficStats.setPhysicalAppointmentCount((double) count);
                    break;
                case OUTPATIENT_PAYMENT:
                    // 门诊缴费
                    trafficStats.setOutpatientFeesCount((double) count);
                    break;
                case HOSPITALIZATION_DEPOSIT:
                    // 住院预交
                    trafficStats.setPrePaymentInhospitalCount((double) count);
                    break;
                case ONLINE_CONSULTATION:
                    // 在线咨询
                    trafficStats.setConsultCount((double) count);
                    break;
                case ONLINE_REVISIT:
                    // 在线复诊
                    trafficStats.setReturnVisitCount((double) count);
                    break;
                case MEDICAL_RECORD_COPY_APPOINTMENT:
                    // 病案复印预约
                    trafficStats.setMedicalRecordCopyingCount((double) count);
                    break;
                case ONLINE_PRESCRIPTION:
                    // 电子处方
                    trafficStats.setElectronicPrescriptionCount((double) count);
                    break;
                default:
                    break;
            }

        });
        trafficStats.setReportQueryCount((double) getLisRisReportCount(hospital, startTime, endTime));
        // TODO: 还缺少 检查申请人次/检验申请人次
        return trafficStats;
    }

    @Override
    public List<DeptDoctorName> getHotDeptRanking(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime, int top) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<OfflineOrder>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("createdDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("createdDate", endTime));
        }
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        sps.add(Specifications.isNotNull("hisDeptId"));
        sps.add(Specifications.eq("type", ProjectTypeEnum.APPOINTMENT_REGISTRATION));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<OfflineOrder> root = criteriaQuery.from(entityManager.getMetamodel().entity(OfflineOrder.class));
        criteriaQuery.groupBy(root.get("hospital").get("code"), root.get("hospital").get("name"),
                root.get("hisDeptId"), root.get("deptName"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);
        criteriaQuery.orderBy(criteriaBuilder.desc(criteriaBuilder.count(root)));
        criteriaQuery.multiselect(root.get("hospital").get("code"), root.get("hospital").get("name"),
                root.get("hisDeptId"), root.get("deptName"), criteriaBuilder.count(root));

        TypedQuery<Tuple> query = entityManager.createQuery(criteriaQuery);
        if (top > 0) {
            query.setMaxResults(top);
        } else {
            query.setMaxResults(5);
        }
        List<Tuple> result = query.getResultList();

        return result.stream().map(u -> {
            return new DeptDoctorName(u.get(0, String.class), u.get(1, String.class), u.get(2, String.class),
                    u.get(3, String.class), null, null, (int) (long) u.get(4, Long.class));
        }).collect(Collectors.toList());
    }

    @Override
    public List<DeptDoctorName> getHotDoctorRanking(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime, int top) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<OfflineOrder>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("createdDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("createdDate", endTime));
        }
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        sps.add(Specifications.isNotNull("doctor"));
        sps.add(Specifications.isNotNull("hisDeptId"));
        sps.add(Specifications.eq("type", ProjectTypeEnum.APPOINTMENT_REGISTRATION));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<OfflineOrder> root = criteriaQuery.from(entityManager.getMetamodel().entity(OfflineOrder.class));
        criteriaQuery.groupBy(root.get("hospital").get("code"), root.get("hospital").get("name"),
                root.get("hisDeptId"), root.get("deptName"),
                root.get("doctor").get("jobNumber"), root.get("doctor").get("name"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        criteriaQuery.orderBy(criteriaBuilder.desc(criteriaBuilder.count(root)));
        criteriaQuery.multiselect(root.get("hospital").get("code"), root.get("hospital").get("name"),
                root.get("hisDeptId"), root.get("deptName"),
                root.get("doctor").get("jobNumber"), root.get("doctor").get("name"), criteriaBuilder.count(root));

        TypedQuery<Tuple> query = entityManager.createQuery(criteriaQuery);
        if (top > 0) {
            query.setMaxResults(top);
        } else {
            query.setMaxResults(5);
        }
        List<Tuple> result = query.getResultList();

        return result.stream().map(u -> {
            return new DeptDoctorName(u.get(0, String.class), u.get(1, String.class),  u.get(2, String.class),
                    u.get(3, String.class), u.get(4, String.class), u.get(5, String.class),
                    (int) (long) u.get(6, Long.class));
        }).collect(Collectors.toList());
    }

    @Override
    public BusinessIncomeStats getBusinessIncome(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        BusinessIncomeStats businessIncomeStats = new BusinessIncomeStats();

        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<Bill>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("orderTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("orderTime", endTime));
        }
        if (hospital != null) {
            sps.add((Specification<Bill>) (root, query1, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("hospital"), hospital));
        }
        sps.add(Specifications.eq("billPayStatus", BillPayStatusEnum.PAYED));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));

        criteriaQuery.groupBy(root.get("billServiceType"));
        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

        criteriaQuery.where(where);

        criteriaQuery.multiselect(root.get("billServiceType"), criteriaBuilder.sum(root.get("paymentAmount")));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        long outpatientIncome = 0;
        long inpatientIncome = 0;
        for (Tuple u : result) {
            ProjectTypeEnum type = u.get(0, ProjectTypeEnum.class);
            long amount = u.get(1, Long.class);
            switch (type) {
                case APPOINTMENT_REGISTRATION:
                case OUTPATIENT_PAYMENT:
                case ONLINE_CONSULTATION:
                case ONLINE_REVISIT:
                case ONLINE_PRESCRIPTION:
                case PHYSICAL_EXAMINATION_APPOINTMENT:
                    outpatientIncome += amount;
                    break;
                case HOSPITALIZATION_DEPOSIT:
                case MEDICAL_RECORD_COPY_APPOINTMENT:
                case MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:
                    inpatientIncome += amount;
                    break;
                default:
            }
        }
        businessIncomeStats.setInpatientIncome(inpatientIncome);
        businessIncomeStats.setOutpatientIncome(outpatientIncome);
        return businessIncomeStats;
    }


    @Override
    public Map<String, List<TimeData>> getMoreTrafficTimeStats(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime, String project) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        List<StrTimeData> data;
        if (Objects.equals(project, "reportQuery")) {
//            报告查询
            data = getLisRisReportTimesCount(hospital, startTime, endTime);
        } else {
            ProjectTypeEnum typeEnum = toBillEnum(project);
            if (typeEnum == null) {
                return new HashMap<>();
            }
            List<Specification<Bill>> sps = Lists.newArrayList();
            if (startTime != null) {
                sps.add(Specifications.ge("orderTime", startTime));
            }
            if (endTime != null) {
                sps.add(Specifications.lt("orderTime", endTime));
            }
            if (hospital != null) {
                sps.add(Specifications.eq("hospital", hospital));
            }
            sps.add(Specifications.ne("billPayStatus", BillPayStatusEnum.REFUNDED));
            sps.add(Specifications.eq("billServiceType", typeEnum));

            EntityManager entityManager = AppContext.getInstance(EntityManager.class);
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
            Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));

            Expression<Integer> yearExpression = criteriaBuilder.function("year", Integer.class, root.get("orderTime"));
            Expression<Integer> monthExpression = criteriaBuilder.function("month", Integer.class, root.get("orderTime"));
            Expression<Integer> dayExpression = criteriaBuilder.function("day", Integer.class, root.get("orderTime"));
            Expression<Integer> hourExpression = criteriaBuilder.function("hour", Integer.class, root.get("orderTime"));

            criteriaQuery.groupBy(yearExpression, monthExpression, dayExpression, hourExpression);

            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);

            criteriaQuery.where(where);

            criteriaQuery.multiselect(yearExpression, monthExpression, dayExpression, hourExpression, criteriaBuilder.count(root));
            List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

            data = result.stream().map(u -> {
                int year = u.get(0, Integer.class);
                int month = u.get(1, Integer.class);
                int day = u.get(2, Integer.class);
                int hour = u.get(3, Integer.class);
                long count = u.get(4, Long.class);
                String strDay = year + "-" + StringUtils.leftPad(month + "", 2, "0") + "-" + StringUtils.leftPad(day + "", 2, "0");
                Calendar calendar = Calendar.getInstance();
                calendar.clear();
                calendar.set(year, month - 1, day, hour, 0, 0);
                Date time = calendar.getTime();
                return new StrTimeData(strDay, time, (int) count);
            }).collect(Collectors.toList());
        }
        if (startTime != null && endTime != null) {
            data = supplementaryData(startTime, endTime, data, 3600);
        }
        Map<String, List<TimeData>> dataMap = new HashMap<>();
        data.stream().collect(Collectors.groupingBy(StrTimeData::getDay))
                .entrySet().forEach((en -> {
                    dataMap.put(en.getKey(), en.getValue().stream().map(v -> new TimeData(v.getTime(), v.getData())).collect(Collectors.toList()));
                }));

        return dataMap;
    }

    private ProjectTypeEnum toBillEnum(String project) {
        switch (project.toLowerCase()) {
            case "appointment":
                // 预约挂号
                return ProjectTypeEnum.APPOINTMENT_REGISTRATION;
            case "physicalappointment":
                // 体检预约
                return ProjectTypeEnum.PHYSICAL_EXAMINATION_APPOINTMENT;
            case "examappointment":
                // 检查申请
                return null;
            case "inspectappointment":
                // 检验申请
                return null;
            case "consult":
                // 在线咨询
                return ProjectTypeEnum.ONLINE_CONSULTATION;
            case "returnvisit":
                // 在线复诊
                return ProjectTypeEnum.ONLINE_REVISIT;
            case "medicalrecordcopying":
                // 病案复印预约
                return ProjectTypeEnum.MEDICAL_RECORD_COPY_APPOINTMENT;
            case "outpatientfees":
                // 门诊收费支付量
                return ProjectTypeEnum.OUTPATIENT_PAYMENT;
            case "prepaymentinhospital":
                // 住院预交支付量
                return ProjectTypeEnum.HOSPITALIZATION_DEPOSIT;
            case "electronicprescription":
                // 电子处方申请量
                return ProjectTypeEnum.ONLINE_PRESCRIPTION;
            default:
                return null;
        }
    }

    @Override
    public Page<NameCount> getProjectTraffic(Hospital hospital, DateUnitForSelect timeUnit, String project, Date startTime, Date endTime, Pageable pageable) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        if (Objects.equals(project, "reportQuery")) {
            return getLisRisReportCount(hospital, startTime, endTime, pageable);
        } else {
            ProjectTypeEnum typeEnum = toBillEnum(project);
            if (typeEnum == null) {
                return new PageImpl<>(Lists.newArrayList(), pageable, 0);
            }
            List<Specification<Bill>> sps = Lists.newArrayList();
            if (startTime != null) {
                sps.add(Specifications.ge("orderTime", startTime));
            }
            if (endTime != null) {
                sps.add(Specifications.lt("orderTime", endTime));
            }
            if (hospital != null) {
                sps.add(Specifications.eq("hospital", hospital));
            }
            sps.add(Specifications.ne("billPayStatus", BillPayStatusEnum.REFUNDED));
            sps.add(Specifications.eq("billServiceType", typeEnum));

            EntityManager entityManager = AppContext.getInstance(EntityManager.class);
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
            Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);

            criteriaQuery.groupBy(root.get("hospital").get("id"));
            criteriaQuery.multiselect(root.get("hospital").get("id"));
            List<Tuple> totalTuple = entityManager.createQuery(criteriaQuery).getResultList();
            if (totalTuple == null || totalTuple.isEmpty()) {
                return new PageImpl<>(Lists.newArrayList(), pageable, 0);
            }
            long total = totalTuple.size();
            criteriaQuery.groupBy(root.get("hospital").get("code"), root.get("hospital").get("name"));
            criteriaQuery.multiselect(root.get("hospital").get("code"), root.get("hospital").get("name"), criteriaBuilder.count(root));
            TypedQuery<Tuple> query = entityManager.createQuery(criteriaQuery);
            query.setFirstResult((int) pageable.getOffset());
            query.setMaxResults(pageable.getPageSize());
            List<Tuple> result = query.getResultList();
            List<NameCount> list = result.stream().map(u -> {
                NameCount nameCount = new NameCount();
                nameCount.setCode(u.get(0, String.class));
                nameCount.setName(u.get(1, String.class));
                nameCount.setCount(u.get(2, Long.class));
                return nameCount;
            }).collect(Collectors.toList());
            return new PageImpl<>(list, pageable, total);
        }
    }

    @Override
    public UploadVM exportMoreTrafficStats(Hospital hospital, DateUnitForSelect timeUnit, String project, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        List<NameCount> data;
        String fileName;
        String columnName;
        if (Objects.equals(project, "reportQuery")) {
            data = getLisRisReportList(hospital, startTime, endTime);
            fileName = "报告查询人次";
            columnName ="报告查询人次(人次)";
        } else {
            ProjectTypeEnum typeEnum = toBillEnum(project);
            if (typeEnum == null) {
                return null;
            }
            fileName = typeEnum.name;
            columnName = getColumnByType(typeEnum);
            List<Specification<Bill>> sps = Lists.newArrayList();
            if (startTime != null) {
                sps.add(Specifications.ge("orderTime", startTime));
            }
            if (endTime != null) {
                sps.add(Specifications.lt("orderTime", endTime));
            }
            if (hospital != null) {
                sps.add(Specifications.eq("hospital", hospital));
            }
            sps.add(Specifications.ne("billPayStatus", BillPayStatusEnum.REFUNDED));
            sps.add(Specifications.eq("billServiceType", typeEnum));

            EntityManager entityManager = AppContext.getInstance(EntityManager.class);
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
            Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);

            criteriaQuery.groupBy(root.get("hospital"));
            criteriaQuery.multiselect(root.get("hospital").get("code"), root.get("hospital").get("name"),
                    criteriaBuilder.count(root));
            List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
            data = result.stream().map(u -> {
                NameCount nameCount = new NameCount();
                nameCount.setCode(u.get(0, String.class));
                nameCount.setName(u.get(1, String.class));
                nameCount.setCount(u.get(2, Long.class));
                return nameCount;
            }).collect(Collectors.toList());
        }
        File file = new File(UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                fileName + System.currentTimeMillis() + ".xlsx"));
        try (OutputStream out = new FileOutputStream(file);
             ExcelWriter writer = ExcelUtil.getWriter(true)) {
            List<Map<String, Object>> rows = Lists.newArrayList();
            NameCount total = getProjectTrafficTotal(hospital, timeUnit, project, startTime, endTime);
            Map<String, Object> totalRow = new LinkedHashMap<>();
            totalRow.put("医院机构", "合计");
            totalRow.put(columnName, MathUtils.getDoubleIntString(total.getCount()));
            rows.add(totalRow);
            for (NameCount nameCount : data) {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("医院机构", nameCount.getName());
                row.put(columnName,  MathUtils.getDoubleIntString(nameCount.getCount()));
                rows.add(row);
            }
            writer.write(rows, true);
            writer.flush(out, true);
        } catch (Exception e) {
            log.error("导出失败", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("导出失败");
        }
        Upload upload = AppContext.getInstance(UploadService.class).upload(AppContext.getInstance(UserService.class)
                .getSystem(), UploadResource.of(file, UploadType.PUBLIC, null));
        try {
            file.delete();
        } catch (Exception ignored) {}
        return new UploadVM(upload);
    }

    @Override
    public Page<NameCount> getMoreTrafficStatsByDept(Hospital hospital, DateUnitForSelect timeUnit, String project, Date startTime, Date endTime, Pageable pageable) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        ProjectTypeEnum typeEnum = toBillEnum(project);
        if (typeEnum == null) {
            return new PageImpl<>(Lists.newArrayList(), pageable, 0);
        }
        List<Specification<Bill>> sps = Lists.newArrayList();
        sps.add(Specifications.eq("hospital", hospital));
        sps.add(Specifications.ne("billPayStatus", BillPayStatusEnum.REFUNDED));
        sps.add(Specifications.eq("billServiceType", typeEnum));
        if (startTime != null) {
            sps.add(Specifications.ge("orderTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("orderTime", endTime));
        }
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);

        criteriaQuery.groupBy(root.get("deptId"), root.get("deptCode"), root.get("deptName"));
        criteriaQuery.multiselect(root.get("deptId"), root.get("deptCode"));
        List<Tuple> totalTuple = entityManager.createQuery(criteriaQuery).getResultList();
        if (totalTuple == null || totalTuple.isEmpty()) {
            return new PageImpl<>(Lists.newArrayList(), pageable, 0);
        }
        long total = totalTuple.size();
        criteriaQuery.multiselect(root.get("deptCode"), root.get("deptName"), criteriaBuilder.count(root));
        criteriaQuery.orderBy(criteriaBuilder.desc(criteriaBuilder.count(root)));
        TypedQuery<Tuple> query = entityManager.createQuery(criteriaQuery);
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());
        List<Tuple> result = query.getResultList();
        List<NameCount> list = result.stream().map(u -> {
            NameCount nameCount = new NameCount();
            nameCount.setCode(u.get(0, String.class));
            nameCount.setName(u.get(1, String.class));
            nameCount.setCount(u.get(2, Long.class));
            return nameCount;
        }).collect(Collectors.toList());
        return new PageImpl<>(list, pageable, total);
    }

    @Override
    public UploadVM exportMoreTrafficStatsByDept(Hospital hospital, DateUnitForSelect timeUnit, String project, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        ProjectTypeEnum typeEnum = toBillEnum(project);
        if (typeEnum == null) {
            return null;
        }
        String fileName = typeEnum.name;
        String columnName = getColumnByType(typeEnum);
        List<Specification<Bill>> sps = Lists.newArrayList();
        sps.add(Specifications.eq("hospital", hospital));
        sps.add(Specifications.ne("billPayStatus", BillPayStatusEnum.REFUNDED));
        sps.add(Specifications.eq("billServiceType", typeEnum));
        if (startTime != null) {
            sps.add(Specifications.ge("orderTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("orderTime", endTime));
        }
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        criteriaQuery.groupBy(root.get("deptId"), root.get("deptCode"), root.get("deptName"));
        criteriaQuery.multiselect(root.get("deptCode"), root.get("deptName"), criteriaBuilder.count(root));
        criteriaQuery.orderBy(criteriaBuilder.desc(criteriaBuilder.count(root)));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        List<NameCount> data = result.stream().map(u -> {
            NameCount nameCount = new NameCount();
            nameCount.setCode(u.get(0, String.class));
            nameCount.setName(u.get(1, String.class));
            nameCount.setCount(u.get(2, Long.class));
            return nameCount;
        }).collect(Collectors.toList());
        File file = new File(UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                fileName + System.currentTimeMillis() + ".xlsx"));
        try (OutputStream out = new FileOutputStream(file);
             ExcelWriter writer = ExcelUtil.getWriter(true)) {
            List<Map<String, Object>> rows = Lists.newArrayList();
            NameCount total = getProjectTrafficTotal(hospital, timeUnit, project, startTime, endTime);
            Map<String, Object> totalRow = new LinkedHashMap<>();
            totalRow.put("医院机构", "合计");
            totalRow.put("科室名称", "");
            totalRow.put(columnName, MathUtils.getDoubleIntString(total.getCount()));
            rows.add(totalRow);
            for (NameCount nameCount : data) {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("医院机构", hospital.getName());
                row.put("科室名称", StringUtils.isBlank(nameCount.getName()) ? "其他" : nameCount.getName());
                row.put(columnName, MathUtils.getDoubleIntString(nameCount.getCount()));
                rows.add(row);
            }
            writer.write(rows, true);
            writer.flush(out, true);
        } catch (Exception e) {
            log.error("导出失败", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("导出失败");
        }
        Upload upload = AppContext.getInstance(UploadService.class).upload(AppContext.getInstance(UserService.class)
                .getSystem(), UploadResource.of(file, UploadType.PUBLIC, null));
        try {
            file.delete();
        } catch (Exception ignored) {}
        return new UploadVM(upload);
    }

    private String getColumnByType(ProjectTypeEnum typeEnum) {
        switch (typeEnum) {
            case APPOINTMENT_REGISTRATION:
                return "预约挂号人次(人次)";
            case OUTPATIENT_PAYMENT:
                return "门诊缴费人次(人次)";
            case HOSPITALIZATION_DEPOSIT:
                return "住院预交人次(人次)";
            case ONLINE_CONSULTATION:
                return "在线咨询人次(人次)";
            case ONLINE_REVISIT:
                return "在线复诊人次(人次)";
            case ONLINE_PRESCRIPTION:
                return "电子处方开具人次(人次)";
            case PHYSICAL_EXAMINATION_APPOINTMENT:
                return "体检预约人次(人次)";
            case MEDICAL_RECORD_COPY_APPOINTMENT:
                return "病案复印预约人次(人次)";
        }
        return null;
    }

    /**
     * 检查检验报告查询次数统计
     * @param hospital
     * @param startTime
     * @param endTime
     * @return
     */
    private Page<NameCount> getLisRisReportCount(Hospital hospital, Date startTime, Date endTime, Pageable pageable) {
        List<Specification<SystemLogStats>> sps = Lists.newArrayList();
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        if (startTime != null) {
            sps.add(Specifications.ge("statsDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("statsDate", endTime));
        }
        sps.add(Specifications.eq("statsUnit", StatsUnit.DAY));
        sps.add(Specifications.eq("action", Constants.LogStatsAction.REPORT_VISIT));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<SystemLogStats> root = criteriaQuery.from(entityManager.getMetamodel().entity(SystemLogStats.class));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);
        criteriaQuery.groupBy(root.get("hospital").get("id"));

        criteriaQuery.multiselect(root.get("hospital").get("id"));
        List<Tuple> totalTuple = entityManager.createQuery(criteriaQuery).getResultList();
        if (totalTuple.isEmpty()) {
            return Page.empty();
        }
        long total = totalTuple.size();
        criteriaQuery.groupBy(root.get("hospital").get("code"), root.get("hospital").get("name"));
        criteriaQuery.multiselect(root.get("hospital").get("code"), root.get("hospital").get("name"), criteriaBuilder.sum(root.get("count")));
        TypedQuery<Tuple> query = entityManager.createQuery(criteriaQuery);
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());
        List<Tuple> result = query.getResultList();

        List<NameCount> list = result.stream().map(u -> {
            NameCount nameCount = new NameCount();
            nameCount.setCode(u.get(0, String.class));
            nameCount.setName(u.get(1, String.class));
            nameCount.setCount(u.get(2, Long.class));
            return nameCount;
        }).collect(Collectors.toList());
        return new PageImpl<>(list, pageable, total);
    }


    @Override
    public NameCount getProjectTrafficTotal(Hospital hospital, DateUnitForSelect timeUnit, String project, Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        NameCount nameCount = new NameCount();
        nameCount.setCode(project);
        if (Objects.equals(project, "reportQuery")) {
            nameCount.setCount(getLisRisReportCount(hospital, startTime, endTime));
        } else {
            ProjectTypeEnum typeEnum = toBillEnum(project);
            if (typeEnum == null) {
                return nameCount;
            }
            List<Specification<Bill>> sps = Lists.newArrayList();
            if (startTime != null) {
                sps.add(Specifications.ge("orderTime", startTime));
            }
            if (endTime != null) {
                sps.add(Specifications.lt("orderTime", endTime));
            }
            if (hospital != null) {
                sps.add(Specifications.eq("hospital", hospital));
            }
            sps.add(Specifications.ne("billPayStatus", BillPayStatusEnum.REFUNDED));
            sps.add(Specifications.eq("billServiceType", typeEnum));

            EntityManager entityManager = AppContext.getInstance(EntityManager.class);
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
            Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);

            criteriaQuery.multiselect(criteriaBuilder.count(root));
            Tuple result = entityManager.createQuery(criteriaQuery).getSingleResult();
            nameCount.setCount(result.get(0, Long.class));
        }
        return nameCount;
    }

    @Override
    public List<SatisfactionStatic> getSatisfaction(Hospital hospital, DateUnitForSelect timeUnit,
                                                    Date startTime, Date endTime) {
        List<Specification<CrmTaskDetailResult>> sps = Lists.newArrayList();
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        if (startTime != null) {
            sps.add(Specifications.ge("resultTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("resultTime", endTime));
        }
        sps.add(Specifications.eq("questionnaire.crmType", CrmType.SATISFACTION));

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<CrmTaskDetailResult> root = criteriaQuery.from(entityManager.getMetamodel().entity(CrmTaskDetailResult.class));
        Join<CrmTaskDetailResult, Hospital> hospitalJoin = root.join("hospital", JoinType.LEFT);
        criteriaQuery.groupBy(hospitalJoin.get("code"), hospitalJoin.get("name"), root.get("questionnaire").get("clinicType"));

        Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
        criteriaQuery.where(where);

        criteriaQuery.multiselect(hospitalJoin.get("code"), hospitalJoin.get("name"), root.get("questionnaire").get("clinicType"),
                criteriaBuilder.count(root), criteriaBuilder.avg(root.get("scoreRate")));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        List<SatisfactionStatic> list = Lists.newArrayList();
        Multimap<String, Tuple> map = ArrayListMultimap.create();
        result.forEach(u -> {
            map.put(u.get(0, String.class), u);
        });

        map.asMap().forEach((key, tss) -> {
            List<Tuple> ts = new ArrayList<>(tss);
            SatisfactionStatic s = new SatisfactionStatic();
            s.setHospitalCode(ts.get(0).get(0, String.class));
            s.setHospitalName(ts.get(0).get(1, String.class));
            boolean haveCount = false;

            for (Tuple t : ts) {
                CrmClinicType clinicType = t.get(2, CrmClinicType.class);
                Long count = t.get(3, Long.class);
                Double rate = t.get(4, Double.class);
                switch (clinicType) {
                    case IN:
                        s.setInpatientCount(count);
                        s.setInpatientRate(rate);
                        haveCount = true;
                        break;
                    case OUT:
                        s.setOutpatientCount(count);
                        s.setOutpatientRate(rate);
                        haveCount = true;
                        break;
                    default:
                }
            }
            if (haveCount) {
                list.add(s);
            }
        });

        return list;
    }

    @Override
    public Page<DeptDoctorName> getEachDepartmentAppointment(Hospital hospital, DateUnitForSelect timeUnit,
                                                             Date startTime, Date endTime, Pageable pageable) {
        TypedQuery<Tuple> query = getEachDepartmentAppointmentQuery(hospital, timeUnit, startTime, endTime);
        long total = query.getResultList().size();
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());
        List<Tuple> result = query.getResultList();
        List<DeptDoctorName> list = result.stream()
                .map(u -> new DeptDoctorName(u.get(0, String.class), u.get(1, String.class),
                        u.get(2, String.class), u.get(3, String.class), u.get(4, String.class),
                        u.get(5, String.class), (int) (long) u.get(6, Long.class))).collect(Collectors.toList());
        return new PageImpl<>(list, pageable, total);
    }

    public TypedQuery<Tuple> getEachDepartmentAppointmentQuery(Hospital hospital, DateUnitForSelect timeUnit,
                                                               Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);
        List<Specification<OfflineOrder>> sps = Lists.newArrayList();
        if (startTime != null) {
            sps.add(Specifications.ge("createdDate", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("createdDate", endTime));
        }
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        sps.add(Specifications.isNotNull("doctor"));
        sps.add(Specifications.isNotNull("hisDeptId"));
        sps.add(Specifications.eq("type", ProjectTypeEnum.APPOINTMENT_REGISTRATION));
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<OfflineOrder> root = criteriaQuery.from(entityManager.getMetamodel().entity(OfflineOrder.class));

        Root<OfflineDept> offlineDeptJoin = criteriaQuery.from(OfflineDept.class);
        Predicate offlineDeptJoinCondition = criteriaBuilder.equal(root.get("hisDeptId"), offlineDeptJoin.get("deptCode"));
        criteriaQuery.groupBy(
                root.get("hospital").get("code"),
                root.get("hospital").get("name"),
                root.get("doctor").get("id"),
                root.get("doctor").get("jobNumber"),
                root.get("doctor").get("name"),
                root.get("doctor").get("orderValue"),
                root.get("hisDeptId"),
                root.get("deptName"),
                offlineDeptJoin.get("orderValue"),
                offlineDeptJoin.get("id")
        );
        Predicate where = criteriaBuilder.and(Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder), offlineDeptJoinCondition);
        criteriaQuery.where(where);
        // 结果先按挂号科室进行排序（顺序同医院管理端-线下科室管理），再按挂号医生进行排序（顺序同医院管理端-线下专家管理）
        criteriaQuery.orderBy(
                criteriaBuilder.desc(offlineDeptJoin.get("orderValue")),
                criteriaBuilder.desc(offlineDeptJoin.get("id")),
                criteriaBuilder.desc(root.get("doctor").get("orderValue")),
                criteriaBuilder.desc(root.get("doctor").get("id"))
        );
        criteriaQuery.multiselect(
                root.get("hospital").get("code"),
                root.get("hospital").get("name"),
                root.get("hisDeptId"),
                root.get("deptName"),
                root.get("doctor").get("jobNumber"),
                root.get("doctor").get("name"),
                criteriaBuilder.count(root)
        );
        TypedQuery<Tuple> query = entityManager.createQuery(criteriaQuery);
        return query;
    }

    public List<DeptDoctorName> getEachDepartmentAppointment(Hospital hospital, DateUnitForSelect timeUnit,
                                                             Date startTime, Date endTime) {
        return getEachDepartmentAppointmentQuery(hospital, timeUnit, startTime, endTime).getResultList()
                .stream()
                .map(u -> new DeptDoctorName(u.get(0, String.class), u.get(1, String.class),
                        u.get(2, String.class), u.get(3, String.class), u.get(4, String.class),
                        u.get(5, String.class), (int) (long) u.get(6, Long.class))).collect(Collectors.toList());
    }

    @Override
    public TreeMap<ProjectTypeEnum, Long> getTotalAmountStats(Hospital hospital, DateUnitForSelect timeUnit,
                                                              Date startTime, Date endTime) {
        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<Bill>> sps = Lists.newArrayList();
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        } else {
            sps.add(Specifications.isTrue("hospital.enabled"));
        }
        if (startTime != null) {
            sps.add(Specifications.ge("orderTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("orderTime", endTime));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        criteriaQuery.groupBy(root.get("billServiceType"));

        if (CollectionUtils.isNotEmpty(sps)) {
            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);
        }

        criteriaQuery.multiselect(root.get("billServiceType"), criteriaBuilder.sum(root.get("paymentAmount")));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
        TreeMap<ProjectTypeEnum, Long> serviceAmountMap = getZeroAmount();
        result.forEach(u -> {
            Long amount = u.get(1, Long.class);
            if (amount == null) {
                return;
            }
            ProjectTypeEnum typeEnum = u.get(0, ProjectTypeEnum.class);
            serviceAmountMap.put(typeEnum, amount);
        });
        return serviceAmountMap;
    }

    @Override
    public Page<BillFinanceSummaryVM> getPageAmountStats(Hospital hospital, DateUnitForSelect timeUnit, Date startTime,
                                                         Date endTime, PageRequest pageRequest) {
        Page<Hospital> hospitalPage;
        if (hospital == null) {
            List<Specification<Hospital>> specs = Lists.newArrayList();
            specs.add(Specifications.ne("code", "default"));
            specs.add(Specifications.isTrue("enabled"));
            hospitalPage = hospitalRepository.findAll(Specifications.and(specs), pageRequest);
        } else {
            if (pageRequest.getOffset() > 1) {
                return new PageImpl<>(Lists.newArrayList(), pageRequest, 1);
            }
            hospitalPage = new PageImpl<>(List.of(hospital), pageRequest, 1);
        }
        List<Hospital> hospitals = hospitalPage.getContent();
        if (CollectionUtils.isEmpty(hospitals)) {
            return new PageImpl<>(Lists.newArrayList(), pageRequest, hospitalPage.getTotalElements());
        }

        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<Bill>> sps = Lists.newArrayList();
        sps.add((Specification<Bill>) (root, query, criteriaBuilder) -> root.get("hospital").in(hospitals.toArray()));
        if (startTime != null) {
            sps.add(Specifications.ge("orderTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("orderTime", endTime));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        criteriaQuery.groupBy(root.get("hospital"), root.get("billServiceType"));

        if (CollectionUtils.isNotEmpty(sps)) {
            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);
        }

        criteriaQuery.multiselect(root.get("hospital").get("id"), root.get("billServiceType"), criteriaBuilder.sum(root.get("paymentAmount")));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        // 组装数据
        Map<Long, BillFinanceSummaryVM> vms = new HashMap<>();
        result.forEach(u -> {
            Long amount = u.get(2, Long.class);
            if (amount == null) {
                return;
            }
            long hospitalId = u.get(0, Long.class);
            ProjectTypeEnum typeEnum = u.get(1, ProjectTypeEnum.class);
            BillFinanceSummaryVM vm;
            if (vms.containsKey(hospitalId)) {
                vm = vms.get(hospitalId);
                vm.getServiceAmount().put(typeEnum, amount);
            } else {
                vm = new BillFinanceSummaryVM();
                TreeMap<ProjectTypeEnum, Long> serviceAmountMap = getZeroAmount();
                serviceAmountMap.put(typeEnum, amount);
                vm.setServiceAmount(serviceAmountMap);
                vms.put(hospitalId, vm);
            }
        });

        List<BillFinanceSummaryVM> bvms = hospitals.stream().map(u -> {
            BillFinanceSummaryVM vm = vms.get(u.getId());
            if (vm == null) {
                vm = new BillFinanceSummaryVM();
                vm.setServiceAmount(getZeroAmount());
            }
            vm.setHospitalName(u.getName());
            return vm;
        }).collect(Collectors.toList());
        return new PageImpl<>(bvms, pageRequest, hospitalPage.getTotalElements());
    }

    @Override
    public List<BillFinanceSummaryVM> getAllAmountStats(Hospital hospital, DateUnitForSelect timeUnit, Date startTime, Date endTime) {
        List<Hospital> hospitals;
        if (hospital == null) {
            List<Specification<Hospital>> specs = Lists.newArrayList();
            specs.add(Specifications.ne("code", "default"));
            specs.add(Specifications.isTrue("enabled"));
            hospitals = hospitalRepository.findAll(Specifications.and(specs));
        } else {
            hospitals = List.of(hospital);
        }

        startTime = timeUnit.getStartDate(startTime, endTime);
        endTime = timeUnit.getEndDate(startTime, endTime);

        List<Specification<Bill>> sps = Lists.newArrayList();
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));

        }
        if (startTime != null) {
            sps.add(Specifications.ge("orderTime", startTime));
        }
        if (endTime != null) {
            sps.add(Specifications.lt("orderTime", endTime));
        }

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        criteriaQuery.groupBy(root.get("hospital"), root.get("billServiceType"));

        if (CollectionUtils.isNotEmpty(sps)) {
            Predicate where = Specifications.and(sps).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);
        }

        criteriaQuery.multiselect(root.get("hospital").get("id"), root.get("billServiceType"), criteriaBuilder.sum(root.get("paymentAmount")));
        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        // 组装数据
        Map<Long, BillFinanceSummaryVM> vms = new HashMap<>();
        result.forEach(u -> {
            Long amount = u.get(2, Long.class);
            if (amount == null) {
                return;
            }
            long hospitalId = u.get(0, Long.class);
            ProjectTypeEnum typeEnum = u.get(1, ProjectTypeEnum.class);
            BillFinanceSummaryVM vm;
            if (vms.containsKey(hospitalId)) {
                vm = vms.get(hospitalId);
                vm.getServiceAmount().put(typeEnum, amount);
            } else {
                vm = new BillFinanceSummaryVM();
                TreeMap<ProjectTypeEnum, Long> serviceAmountMap = getZeroAmount();
                serviceAmountMap.put(typeEnum, amount);
                vm.setServiceAmount(serviceAmountMap);
                vms.put(hospitalId, vm);
            }
        });

        return hospitals.stream().map(u -> {
            BillFinanceSummaryVM vm = vms.get(u.getId());
            if (vm == null) {
                vm = new BillFinanceSummaryVM();
                vm.setServiceAmount(getZeroAmount());
            }
            vm.setHospitalName(u.getName());
            return vm;
        }).collect(Collectors.toList());
    }

    /**
     * 初始化1个项目金额全部
     * @return
     */
    private TreeMap<ProjectTypeEnum, Long> getZeroAmount() {
        TreeMap<ProjectTypeEnum, Long> treeAmount = new TreeMap<>(Comparator.comparing(a -> a.code));
        for (ProjectTypeEnum e : ProjectTypeEnum.values()) {
            if (!treeAmount.containsKey(e)) {
                treeAmount.put(e, 0L);
            }
        }
        return treeAmount;
    }

}
