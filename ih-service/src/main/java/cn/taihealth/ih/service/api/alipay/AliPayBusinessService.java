package cn.taihealth.ih.service.api.alipay;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.ThirdOrderType;
import cn.taihealth.ih.service.vm.AliPayUnifiedOrder;
import cn.taihealth.ih.wechat.service.vm.CreateWechatOrderVM;
import cn.taihealth.ih.wechat.service.vm.RefundOrderVM;
import cn.taihealth.ih.wechat.service.vm.alipay.AliPayCheckResult;
import cn.taihealth.ih.wechat.service.vm.alipay.AliPayTemplatedData;
import cn.taihealth.ih.wechat.service.vm.wechat.MedicalInsuranceParamResult;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.Date;

public interface AliPayBusinessService {

    AlipayClient getAlipayClient(String appId, Hospital hospital);

    AliPayUnifiedOrder getUnifiedOrder(User user, String appId, CreateWechatOrderVM vm, String ip, Hospital hospital,
                                       Long userPlatformId, boolean isInsurance);

    String payOrder(AliPayCheckResult checkResult);

    String payOrderRequest(String appId, HttpServletRequest request);

    boolean refund(Hospital hospital, RefundOrderVM vm, Integer refundAmount, String refundNo);

    void refundOrder(String refundNo, Date refundTime, Hospital hospital);

    AliPayOrder getPaidOrder(ThirdOrderType type, String body);

    boolean retryRefund(long alipayRefundId);

    AlipayTradeQueryResponse getPayResult(String appId, String tradeNo, String outTradeNo, Hospital hospital);

    AlipayTradeFastpayRefundQueryResponse getRefundStats(String appId, String tradeNo, String refundNo, Hospital hospital,
                                                         String cancelBillNo, String cancelSerialNo);

    void manageData(AliPayTemplatedData data, HospitalSettingKey settingKey, String appId, Hospital hospital);

    void sendTemplateMessage(Hospital hospital, User user, AliPayTemplatedData msg);

    /**
     * 获取用户医保授权参数
     * @param hospital
     * @param appId
     * @param authCode
     * @param callUrl
     * @return
     * @throws AlipayApiException
     */
    MedicalInsuranceParamResult getMedicalInsuranceUserQuery(Hospital hospital, String appId,
                                                             String authCode, String callUrl) throws AlipayApiException;

    /**
     * 生成支付宝小程序二维码
     * @param hospital
     * @param appId
     * @param page
     * @param params
     * @param describe
     */
    String createQrCode(Hospital hospital, String appId, String page, String params, String describe);

    InputStream getTradeBillStream(AlipayClient alipayClient, Date date);
}
