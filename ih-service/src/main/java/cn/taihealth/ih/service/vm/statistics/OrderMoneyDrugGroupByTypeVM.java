package cn.taihealth.ih.service.vm.statistics;

import cn.taihealth.ih.domain.hospital.PrescriptionOrder.PrescriptionType;
import io.swagger.annotations.ApiModelProperty;

/**
 * 根据处方类型展示金额
 * @Author: Moon
 * @Date: 2020/11/20 上午10:24
 */
public class OrderMoneyDrugGroupByTypeVM {

    @ApiModelProperty("处方类型")
    private PrescriptionType prescriptionType;
    @ApiModelProperty("总金额")
    private String totalMoney;

    public OrderMoneyDrugGroupByTypeVM(PrescriptionType type, String totalMoney) {
        this.prescriptionType = type;
        this.totalMoney = totalMoney;
    }

    public PrescriptionType getPrescriptionType() {
        return prescriptionType;
    }

    public void setPrescriptionType(PrescriptionType prescriptionType) {
        this.prescriptionType = prescriptionType;
    }

    public String getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(String totalMoney) {
        this.totalMoney = totalMoney;
    }

}
