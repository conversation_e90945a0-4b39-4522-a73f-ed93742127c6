package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.enums.BillOperateTypeEnum;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.TreeMap;
import lombok.Data;

/**
 * 交易概况
 */
@Data
public class BillServiceSummaryVM1 {


    @ApiModelProperty("支付操作类型")
    private BillOperateTypeEnum operateType;

    @ApiModelProperty("总金额")
    private Long totalAmount;

    @ApiModelProperty("医保部分")
    private Long medicareAmount;

    @ApiModelProperty("个人部分")
    private Long selfAmount;

    @ApiModelProperty("各服务交易类型金额")
    private List<BillServiceProjectTypeAmountVM> serviceAmount;

}
