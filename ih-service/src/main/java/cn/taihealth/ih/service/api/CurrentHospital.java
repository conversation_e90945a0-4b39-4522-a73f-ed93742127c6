package cn.taihealth.ih.service.api;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.repo.HospitalRepository;
import com.gitq.jedi.context.AppContext;
import java.util.Optional;

public interface CurrentHospital {

    Optional<Hospital> get();

    String getHospitalCode();

    String getRequestItem();

    static String getCode() {
        return AppContext.getInstance(CurrentHospital.class).getHospitalCode();
    }

    static String getItem() {
        return AppContext.getInstance(CurrentHospital.class).getRequestItem();
    }

    static Hospital getOrNull() {
        return AppContext.getInstance(CurrentHospital.class).get().orElse(null);
    }

    static Hospital getOrThrow() {
        return AppContext.getInstance(CurrentHospital.class).get()
            .orElseThrow(ErrorType.NO_CURRENT_HOSPITAL_HOSPITAL_ERROR::toProblem);
    }

    static Hospital getOrDefault() {
        return AppContext.getInstance(CurrentHospital.class).get().orElseGet(() ->
            AppContext.getInstance(HospitalRepository.class).findOneByCode("default").orElse(null)
        );
    }

    static boolean isPlatform() {
        return Constants.HEALTH_HEAD_CODE.equalsIgnoreCase(AppContext.getInstance(CurrentHospital.class).getHospitalCode());
    }

}
