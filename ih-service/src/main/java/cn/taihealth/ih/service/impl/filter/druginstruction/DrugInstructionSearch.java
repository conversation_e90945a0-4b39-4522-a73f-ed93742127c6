package cn.taihealth.ih.service.impl.filter.druginstruction;

import cn.taihealth.ih.domain.DrugInstruction;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class DrugInstructionSearch extends SearchCriteria<DrugInstruction> {
    public static DrugInstructionSearch of (String query) {
        DrugInstructionSearch drugInstructionSearch = new DrugInstructionSearch();
        drugInstructionSearch.parse(query);
        return drugInstructionSearch;
    }
    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<DrugInstruction> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        DrugInstructionSearch.Qualifier qualifier = DrugInstructionSearch.Qualifier
                .valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case NAME:
                return new DrugInstructionNameFilter(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<DrugInstruction> createContainFilter(Set<String> contains) {
        return null;
    }
    enum Qualifier {
        NAME
    }
}
