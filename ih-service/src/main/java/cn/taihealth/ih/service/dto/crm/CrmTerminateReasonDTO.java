package cn.taihealth.ih.service.dto.crm;

import cn.taihealth.ih.domain.crm.CrmTerminateReason;
import cn.taihealth.ih.domain.enums.Position;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * 随访-问卷-问题选项
 * <AUTHOR>
 */
public class CrmTerminateReasonDTO extends UpdatableDTO {

    @ApiModelProperty("终止原因描述")
    @NotNull(message = "描述必填")
    @Length(min = 1, max = 1000, message = "终止原因必填且最长1000个字")
    private String description;

    @ApiModelProperty("终止原因顺序")
    private int terminateOrder;

    @ApiModelProperty("是否选中")
    private boolean selected;

    @ApiModelProperty("位置(不修改位置时不需要填写, ABOVE:上移, BELOW:下移)")
    private Position position = Position.UNKNOWN;

    @ApiModelProperty("是否可被编辑或删除, 添加的选项全部是true, 这个字段不可被编辑")
    private boolean canEdit = true;

    public CrmTerminateReasonDTO() {
    }

    public CrmTerminateReasonDTO(CrmTerminateReason reason) {
        super(reason);
        this.description = reason.getDescription();
        this.terminateOrder = reason.getTerminateOrder();
        this.selected = reason.isSelected();
        this.canEdit = reason.isCanEdit();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getTerminateOrder() {
        return terminateOrder;
    }

    public void setTerminateOrder(int terminateOrder) {
        this.terminateOrder = terminateOrder;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public Position getPosition() {
        return position;
    }

    public void setPosition(Position position) {
        this.position = position;
    }

    public boolean isCanEdit() {
        return canEdit;
    }

    public void setCanEdit(boolean canEdit) {
        this.canEdit = canEdit;
    }
}
