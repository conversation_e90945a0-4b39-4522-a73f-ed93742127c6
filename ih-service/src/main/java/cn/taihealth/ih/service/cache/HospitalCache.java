package cn.taihealth.ih.service.cache;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.repo.HospitalRepository;
import com.gitq.jedi.context.AppContext;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class HospitalCache {

    private final HospitalRepository hospitalRepository;

    public HospitalCache(HospitalRepository hospitalRepository) {
        this.hospitalRepository = hospitalRepository;
    }

    public Optional<Hospital> getHospital(String code) {
        if (Constants.HEALTH_HEAD_CODE.equalsIgnoreCase(code)) {
            return Optional.empty();
        }
        Long id = AppContext.getInstance(HospitalCache.class).getHospitalId(code);
        if (id == null) {
            return Optional.empty();
        } else {
            return hospitalRepository.findById(id);
        }
    }

    @Cacheable(cacheNames = Constants.CACHE_HOSPITAL, unless = "#code == null", key = "{#code.toLowerCase()}")
    public Long getHospitalId(String code) {
        if (Constants.HEALTH_HEAD_CODE.equalsIgnoreCase(code)) {
            return null;
        }
        return hospitalRepository.findOneByCode(code)
            .map(Hospital::getId)
            .orElse(null);
    }

    @CacheEvict(key = "{#code.toLowerCase()}", cacheNames = Constants.CACHE_HOSPITAL, condition="#code != null")
    public void evictCode(String code) { }

    @CacheEvict(cacheNames = Constants.CACHE_HOSPITAL, allEntries = true)
    public void evictAll() { }

}
