package cn.taihealth.ih.service.vm.statistics.supervision;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MoreTrafficRatioStats {

    @ApiModelProperty("预约挂号")
    private Double appointmentRatio;

    @ApiModelProperty("体检预约")
    private Double physicalAppointmentRatio;

    @ApiModelProperty("报告查询")
    private Double reportQueryRatio;

    @ApiModelProperty("检查申请")
    private Double examAppointmentRatio;

    @ApiModelProperty("在线咨询")
    private Double consultRatio;

    @ApiModelProperty("在线复诊")
    private Double returnVisitRatio;

    @ApiModelProperty("病案复印预约")
    private Double medicalRecordCopyingRatio;

    @ApiModelProperty("检验申请")
    private Double inspectAppointmentRatio;

    @ApiModelProperty("门诊收费支付量")
    private Double outpatientFeesRatio;

    @ApiModelProperty("住院预交支付量")
    private Double prePaymentInhospitalRatio;

    @ApiModelProperty("电子处方申请量")
    private Double electronicPrescriptionRatio;

}
