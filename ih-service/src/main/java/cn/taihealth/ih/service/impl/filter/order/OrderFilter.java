package cn.taihealth.ih.service.impl.filter.order;

import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.OrderOperation;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import java.util.Objects;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class OrderFilter implements SearchFilter<Order> {

    private final String pattern;

    public OrderFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Order> toSpecification() {
        return new Specification<Order>() {
            @Override
            public Predicate toPredicate(Root<Order> root, CriteriaQuery<?> criteriaQuery,
                                         CriteriaBuilder criteriaBuilder) {
                criteriaQuery.distinct(true);
                Join<Order, OrderOperation> listJoin = root.join("operations");
                return criteriaBuilder.equal(listJoin.get("operator").get("id"), pattern);
            }
        };

    }

    @Override
    public String toExpression() {
        return "Order:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof OrderFilter)) {
            return false;
        }

        OrderFilter rhs = (OrderFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
