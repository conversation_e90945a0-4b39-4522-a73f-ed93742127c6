package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform.PlatformForEnum;
import cn.taihealth.ih.repo.hospital.HospitalPublicPlatformRepository;
import cn.taihealth.ih.service.api.HospitalPublicPlatformService;
import cn.taihealth.ih.service.cache.HospitalCache;
import cn.taihealth.ih.service.dto.HospitalPublicPlatformDTO;
import cn.taihealth.ih.service.dto.MiniAppOfficialDTO;
import cn.taihealth.ih.service.dto.PublicPlatformDTO;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.HospitalPublicPlatformBindVM;
import cn.taihealth.ih.spring.security.jwt.JWTConfigurer;
import cn.taihealth.ih.wechat.service.api.IHWxClient;
import cn.taihealth.ih.wechat.service.vm.alipay.AliPayMiniAppConfigResult;
import com.alibaba.fastjson.JSONObject;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HospitalPublicPlatformServiceImpl implements HospitalPublicPlatformService {

    private final HospitalPublicPlatformRepository hospitalPublicPlatformRepository;

    private final IHWxClient ihWxClient;

    public HospitalPublicPlatformServiceImpl(HospitalPublicPlatformRepository hospitalPublicPlatformRepository, IHWxClient ihWxClient) {
        this.hospitalPublicPlatformRepository = hospitalPublicPlatformRepository;
        this.ihWxClient = ihWxClient;
    }


    @Override
    @Transactional
    public void bindPlatforms(Hospital hospital, HospitalPublicPlatformBindVM param) {
        HospitalPublicPlatformBindVM.PlatFormVM docOAPlatform = param.getDoctorOfficialAccountPlatform();
        List<HospitalPublicPlatformDTO> docOAPlatformDTOList = ObjectUtils.isEmpty(docOAPlatform)
            ? Lists.newArrayList() : docOAPlatform.getPlatformList();

        HospitalPublicPlatformBindVM.PlatFormVM patOAPlatform = param.getPatientOfficialAccountPlatform();
        List<HospitalPublicPlatformDTO> patOAPlatformDTOList = ObjectUtils.isEmpty(patOAPlatform)
            ? Lists.newArrayList() : patOAPlatform.getPlatformList();

        HospitalPublicPlatformBindVM.PlatFormVM docMiniPlatform = param.getDoctorMiniPlatform();
        List<HospitalPublicPlatformDTO> docMiniPlatformDTOList = ObjectUtils.isEmpty(docMiniPlatform)
            ? Lists.newArrayList() : docMiniPlatform.getPlatformList();

        HospitalPublicPlatformBindVM.PlatFormVM patMiniPlatform = param.getPatientMiniPlatform();
        List<HospitalPublicPlatformDTO> patMiniPlatformDTOList = ObjectUtils.isEmpty(patMiniPlatform)
            ? Lists.newArrayList() : patMiniPlatform.getPlatformList();

        HospitalPublicPlatformBindVM.PlatFormVM aliPayPatMiniPlatform = param.getAliPayPatientMiniPlatform();
        List<HospitalPublicPlatformDTO> aliPayPatMiniPlatformDTOList = ObjectUtils.isEmpty(aliPayPatMiniPlatform)
                ? Lists.newArrayList() : aliPayPatMiniPlatform.getPlatformList();
        //校验：公众号之间、小程序之间不可重复
        if (CollectionUtils.containsAny(docOAPlatformDTOList, patOAPlatformDTOList)
            && CollectionUtils.containsAny(docMiniPlatformDTOList, patMiniPlatformDTOList)) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("公众号之间、小程序之间不可重复");
        }
        List<HospitalPublicPlatform> docOAPlatformList = docOAPlatformDTOList.parallelStream()
            .map(elem -> elem.buildHospitalPublicPlatform(hospital,
                                                          docOAPlatform.getPlatformType(),
                                                          docOAPlatform.getPlatformFor()))
            .collect(Collectors.toList());
        List<HospitalPublicPlatform> patOAPlatformList = patOAPlatformDTOList.parallelStream()
            .map(elem -> elem.buildHospitalPublicPlatform(hospital,
                                                          patOAPlatform.getPlatformType(),
                                                          patOAPlatform.getPlatformFor()))
            .collect(Collectors.toList());
        List<HospitalPublicPlatform> docMiniPlatformList = docMiniPlatformDTOList.parallelStream()
            .map(elem -> elem.buildHospitalPublicPlatform(hospital,
                                                          docMiniPlatform.getPlatformType(),
                                                          docMiniPlatform.getPlatformFor()))
            .collect(Collectors.toList());
        List<HospitalPublicPlatform> patMiniPlatformList = patMiniPlatformDTOList.parallelStream()
            .map(elem -> elem.buildHospitalPublicPlatform(hospital,
                                                          patMiniPlatform.getPlatformType(),
                                                          patMiniPlatform.getPlatformFor()))
            .collect(Collectors.toList());

        List<HospitalPublicPlatform> aliPayPatMiniPlatformList = aliPayPatMiniPlatformDTOList.parallelStream()
                .map(elem -> elem.buildHospitalPublicPlatform(hospital,
                        aliPayPatMiniPlatform.getPlatformType(),
                        aliPayPatMiniPlatform.getPlatformFor()))
                .collect(Collectors.toList());

        docOAPlatformList.addAll(patOAPlatformList);
        docOAPlatformList.addAll(docMiniPlatformList);
        docOAPlatformList.addAll(patMiniPlatformList);
        docOAPlatformList.addAll(aliPayPatMiniPlatformList);
        if (CollectionUtils.isEmpty(docOAPlatformList)) {
            log.info("hospitalId:{} bind public platforms,selected platform is null", param.getHospitalId());
            return;
        }
        log.info("hospitalId:{} bind public platforms,selected platform is:{}", param.getHospitalId(),
                 docOAPlatformList);
        hospitalPublicPlatformRepository.saveAll(docOAPlatformList);
    }

    @Override
    public List<PublicPlatformDTO> filterBoundPublicPlatform(List<PublicPlatformDTO> allList) {
        if (CollectionUtils.isEmpty(allList)) {
            return null;
        }
        List<HospitalPublicPlatform> bindList = hospitalPublicPlatformRepository.findAll();
        if (CollectionUtils.isEmpty(bindList)) {
            return allList;
        }
        List<PublicPlatformDTO> boundList = bindList.parallelStream()
            .map(PublicPlatformDTO::new)
            .collect(Collectors.toList());
        allList.removeAll(boundList);
        return allList;
    }

    @Override
    public HospitalPublicPlatformBindVM getPlatforms(Hospital hospital) {
        HospitalPublicPlatformBindVM result = new HospitalPublicPlatformBindVM();
        Specification<HospitalPublicPlatform> spec = hospital == null ?
                Specifications.isNull("hospital") : Specifications.eq("hospital", hospital);
        Sort sort = Sort.by(Direction.ASC, "createdDate");
        List<HospitalPublicPlatform> allFormList = hospitalPublicPlatformRepository.findAll(spec, sort);
        List<HospitalPublicPlatformDTO> docOAList = allFormList.parallelStream()
            .filter(elem -> elem.getPlatformType().equals(PlatformTypeEnum.OFFICIAL_ACCOUNT)
                && elem.getPlatformFor().equals(HospitalPublicPlatform.PlatformForEnum.DOCTOR))
            .map(HospitalPublicPlatformDTO::new)
            .collect(Collectors.toList());
        List<HospitalPublicPlatformDTO> docMiniList = allFormList.parallelStream()
            .filter(elem -> elem.getPlatformType().equals(PlatformTypeEnum.MINI)
                && elem.getPlatformFor().equals(HospitalPublicPlatform.PlatformForEnum.DOCTOR))
            .map(HospitalPublicPlatformDTO::new)
            .collect(Collectors.toList());
        List<HospitalPublicPlatformDTO> patientOAList = allFormList.parallelStream()
            .filter(elem -> elem.getPlatformType().equals(PlatformTypeEnum.OFFICIAL_ACCOUNT)
                && elem.getPlatformFor().equals(HospitalPublicPlatform.PlatformForEnum.PATIENT))
            .map(HospitalPublicPlatformDTO::new)
            .collect(Collectors.toList());
        List<HospitalPublicPlatformDTO> patientMiniList = allFormList.parallelStream()
            .filter(elem -> elem.getPlatformType().equals(PlatformTypeEnum.MINI)
                && elem.getPlatformFor().equals(HospitalPublicPlatform.PlatformForEnum.PATIENT))
            .map(HospitalPublicPlatformDTO::new)
            .collect(Collectors.toList());

        List<HospitalPublicPlatformDTO> aliPayPatientMiniList = allFormList.parallelStream()
                .filter(elem -> elem.getPlatformType() == PlatformTypeEnum.ALI_PAY_MINI
                        && elem.getPlatformFor() == HospitalPublicPlatform.PlatformForEnum.PATIENT)
                .map(HospitalPublicPlatformDTO::new)
                .collect(Collectors.toList());
        if (hospital != null) {
            result.setHospitalId(hospital.getId());
        }

        HospitalPublicPlatformBindVM.PlatFormVM docOAPlatform = new HospitalPublicPlatformBindVM.PlatFormVM();
        docOAPlatform.setPlatformList(docOAList);
        docOAPlatform.setPlatformFor(HospitalPublicPlatform.PlatformForEnum.DOCTOR);
        docOAPlatform.setPlatformType(PlatformTypeEnum.OFFICIAL_ACCOUNT);
        result.setDoctorOfficialAccountPlatform(docOAPlatform);

        HospitalPublicPlatformBindVM.PlatFormVM docMiniPlatform = new HospitalPublicPlatformBindVM.PlatFormVM();
        docMiniPlatform.setPlatformList(docMiniList);
        docMiniPlatform.setPlatformFor(HospitalPublicPlatform.PlatformForEnum.DOCTOR);
        docMiniPlatform.setPlatformType(PlatformTypeEnum.MINI);
        result.setDoctorMiniPlatform(docMiniPlatform);

        HospitalPublicPlatformBindVM.PlatFormVM patientOAPlatform = new HospitalPublicPlatformBindVM.PlatFormVM();
        patientOAPlatform.setPlatformList(patientOAList);
        patientOAPlatform.setPlatformFor(HospitalPublicPlatform.PlatformForEnum.PATIENT);
        patientOAPlatform.setPlatformType(PlatformTypeEnum.OFFICIAL_ACCOUNT);
        result.setPatientOfficialAccountPlatform(patientOAPlatform);

        HospitalPublicPlatformBindVM.PlatFormVM patientMiniPlatform = new HospitalPublicPlatformBindVM.PlatFormVM();
        patientMiniPlatform.setPlatformList(patientMiniList);
        patientMiniPlatform.setPlatformFor(HospitalPublicPlatform.PlatformForEnum.PATIENT);
        patientMiniPlatform.setPlatformType(PlatformTypeEnum.MINI);
        result.setPatientMiniPlatform(patientMiniPlatform);

        HospitalPublicPlatformBindVM.PlatFormVM aliPayPatientMiniPlatform = new HospitalPublicPlatformBindVM.PlatFormVM();
        aliPayPatientMiniPlatform.setPlatformList(aliPayPatientMiniList);
        aliPayPatientMiniPlatform.setPlatformFor(HospitalPublicPlatform.PlatformForEnum.PATIENT);
        aliPayPatientMiniPlatform.setPlatformType(PlatformTypeEnum.ALI_PAY_MINI);
        result.setAliPayPatientMiniPlatform(aliPayPatientMiniPlatform);
        return result;
    }

    @Override
    public void removeBindPlatforms(Hospital hospital, String appId) {
        hospitalPublicPlatformRepository.deleteByHospitalAndAppId(hospital, appId);
    }

    @Override
    public HospitalPublicPlatform getPlatform(Hospital hospital, String appId) {
        return hospitalPublicPlatformRepository.findOneByAppIdAndHospital(appId, hospital)
            .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("无效的appId"));
    }

    @Override
    public HospitalPublicPlatform getPlatform(Hospital hospital, PlatformTypeEnum platformTypeEnum,
                                              PlatformForEnum platformForEnum) {
        List<Specification<HospitalPublicPlatform>> specifications = Lists.newArrayList();
        if (hospital == null) {
            specifications.add(Specifications.isNull("hospital"));
        } else {
            specifications.add(Specifications.eq("hospital", hospital));
        }
        specifications.add(Specifications.eq("platformType", platformTypeEnum));
        specifications.add(Specifications.eq("platformFor", platformForEnum));

        return hospitalPublicPlatformRepository.findAll(Specifications.and(specifications),
                                                        Sort.by(Direction.ASC, "createdDate")).stream().findFirst()
            .orElse(null);
    }

    @Override
    public Optional<HospitalPublicPlatform> getByAppId(String appId) {
        return hospitalPublicPlatformRepository.findOneByAppId(appId);
    }

    @Override
    public List<PublicPlatformDTO> getUnBindPlatforms(PlatformTypeEnum type, String hospitalCode, HttpServletRequest request) {
        List<PublicPlatformDTO> resultList = Lists.newArrayList();
        String token = request.getHeader(JWTConfigurer.AUTHORIZATION_HEADER);
        String host = "";
        if (StringUtils.isNotEmpty(hospitalCode)) {
            Optional<Hospital> optional = AppContext.getInstance(HospitalCache.class).getHospital(hospitalCode);
            Hospital hospital = optional.orElseThrow(() -> ErrorType.NOT_FOUND_ERROR.toProblem("hospitalCode对应的医院不存在"));
            host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        }
        String resultJsonStr = ihWxClient.getMiniAppOfficialAccount(token, type, host);
        String listStr = Objects.requireNonNull(JSONObject.parseObject(resultJsonStr, Map.class)).get("list")
                .toString();
        List<MiniAppOfficialDTO> list = JSONObject.parseArray(listStr, MiniAppOfficialDTO.class);
        if (!CollectionUtils.isEmpty(list)) {
            List<PublicPlatformDTO> allList = list.parallelStream()
                    .map(PublicPlatformDTO::new)
                    .collect(Collectors.toList());
            //过滤已与医院绑定的公众平台0
            resultList = this.filterBoundPublicPlatform(allList);
        }
        return resultList;
    }

    @Override
    public List<PublicPlatformDTO> getUnBindPlatforms(String hospitalCode, HttpServletRequest request) {
        List<PublicPlatformDTO> resultList = Lists.newArrayList();
        String token = request.getHeader(JWTConfigurer.AUTHORIZATION_HEADER);
        String host = "";
        if (StringUtils.isNotEmpty(hospitalCode)) {
            Optional<Hospital> optional = AppContext.getInstance(HospitalCache.class).getHospital(hospitalCode);
            Hospital hospital = optional.orElseThrow(() -> ErrorType.NOT_FOUND_ERROR.toProblem("hospitalCode对应的医院不存在"));
            host = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.WX_SERVER_PATH, String.class);
        }
        String resultJsonStr = ihWxClient.getAliPayMiniAppConfig(token, host);
        String listStr = Objects.requireNonNull(JSONObject.parseObject(resultJsonStr, Map.class)).get("list")
                .toString();
        List<AliPayMiniAppConfigResult> list = JSONObject.parseArray(listStr, AliPayMiniAppConfigResult.class);
        if (!CollectionUtils.isEmpty(list)) {
            List<PublicPlatformDTO> allList = list.parallelStream()
                    .map(PublicPlatformDTO::new)
                    .collect(Collectors.toList());
            //过滤已与医院绑定的公众平台0
            resultList = this.filterBoundPublicPlatform(allList);
        }
        return resultList;
    }
}
