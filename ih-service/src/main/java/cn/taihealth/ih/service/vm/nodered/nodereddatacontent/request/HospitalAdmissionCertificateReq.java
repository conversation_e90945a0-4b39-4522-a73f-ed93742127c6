package cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-09-27
 * 住院病人入院单信息更新
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class HospitalAdmissionCertificateReq implements Serializable {
    @ApiModelProperty("患者姓名	Y")
    private String patname;
    @ApiModelProperty("入院单号	N	入院单号与住院号不可同时为空")
    private String admission_no;
    @ApiModelProperty("住院号	N   入院单号与住院号不可同时为空")
    private String regno;
    @ApiModelProperty("民族	N	为空则不更新")
    private String nation;
    @ApiModelProperty("国籍	N  为空则不更新")
    private String nationality;
    @ApiModelProperty("职业	N  为空则不更新")
    private String career;
    @ApiModelProperty("婚姻状态	N 为空则不更新")
    private String marriage;
    @ApiModelProperty("联系电话	N 为空则不更新")
    private String telephone;
    @ApiModelProperty("出生地省	N 为空则不更新")
    private String csd_province;
    @ApiModelProperty("出生地市	N 为空则不更新")
    private String csd_city;
    @ApiModelProperty("出生地区县	N 为空则不更新")
    private String csd_county;
    @ApiModelProperty("籍贯省	N 为空则不更新")
    private String jg_province;
    @ApiModelProperty("籍贯市	N 为空则不更新")
    private String jg_city;
    @ApiModelProperty("籍贯区县	N 为空则不更新")
    private String jg_county;
    @ApiModelProperty("现住址省	N 为空则不更新")
    private String xzd_province;
    @ApiModelProperty("现住址市	N 为空则不更新")
    private String xzd_city;
    @ApiModelProperty("现住址区县	N 为空则不更新")
    private String xzd_county;
    @ApiModelProperty("现住址详细地址	N 为空则不更新")
    private String xzd_addr;
    @ApiModelProperty("现住址邮编	N 为空则不更新")
    private String xzd_postno;
    @ApiModelProperty("户口地址省	N 为空则不更新")
    private String hk_province;
    @ApiModelProperty("户口地址市	N 为空则不更新")
    private String hk_city;
    @ApiModelProperty("户口地址区县	N 为空则不更新")
    private String hk_county;
    @ApiModelProperty("户口地址详细地址	N 为空则不更新")
    private String hk_addr;
    @ApiModelProperty("户口地址邮编	N 为空则不更新")
    private String hk_postno;
    @ApiModelProperty("工作单位	N 为空则不更新")
    private String comp;
    @ApiModelProperty("工作单位地址	N 为空则不更新")
    private String compaddr;
    @ApiModelProperty("工作单位电话	N 为空则不更新")
    private String comptel;
    @ApiModelProperty("工作单位邮编	N 为空则不更新")
    private String compno;
    @ApiModelProperty("卡号	N 为空则不更新")
    private String cardno;
    @ApiModelProperty("联系人姓名	N 为空则不更新")
    private String contacts_name;
    @ApiModelProperty("联系人关系	N 为空则不更新")
    private String contacts_relationship;
    @ApiModelProperty("联系人电话	N 为空则不更新")
    private String contacts_telephone;
    @ApiModelProperty("联系人地址省	N 为空则不更新")
    private String contacts_province;
    @ApiModelProperty("联系人地址市	N 为空则不更新")
    private String contacts_city;
    @ApiModelProperty("联系人地址区县	N 为空则不更新")
    private String contacts_county;
    @ApiModelProperty("联系人地址	N  为空则不更新")
    private String contacts_address;
    @ApiModelProperty("医保入参	N	占位，具体参数格式内容需要根据当地医保确定")
    private String insurance_param;
    @ApiModelProperty("外部电子卡号	N")
    private String virtual_card;
    @ApiModelProperty("备注	N	患者备注或患者相关留言")
    private String memo;
}
