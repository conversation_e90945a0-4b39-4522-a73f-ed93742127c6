package cn.taihealth.ih.service.impl.filter.ticket;

import cn.taihealth.ih.domain.Ticket;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class TicketSearch extends SearchCriteria<Ticket> {

    public static TicketSearch of (String query) {
        TicketSearch ticketSearch = new TicketSearch();
        ticketSearch.parse(query);
        return ticketSearch;
    }
    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<Ticket> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier
                .valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case NAME:
                return new TicketNameFilter(value);
            case TELEPHONE:
                return new TicketTelephoneFilter(value);
            case SUGGESTTYPES:
                return new TicketSuggestTypeFilter(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<Ticket> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        NAME,
        TELEPHONE,
        SUGGESTTYPES
    }
}
