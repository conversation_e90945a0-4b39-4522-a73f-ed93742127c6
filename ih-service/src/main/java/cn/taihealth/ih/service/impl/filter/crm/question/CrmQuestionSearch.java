package cn.taihealth.ih.service.impl.filter.crm.question;

import cn.taihealth.ih.domain.crm.CrmQuestion;
import cn.taihealth.ih.domain.enums.CrmClinicType;
import cn.taihealth.ih.domain.enums.CrmType;
import cn.taihealth.ih.domain.enums.QuestionType;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

public class CrmQuestionSearch extends SearchCriteria<CrmQuestion> {

    private QuestionnaireFilter questionnaireFilter;

    private GenericFilter<CrmQuestion> idFilter;

    public static CrmQuestionSearch of(String query) {
        CrmQuestionSearch articleSearch = new CrmQuestionSearch();
        articleSearch.parse(query);
        return articleSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<CrmQuestion> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }

        switch (qualifier) {
            case CONTENT:
                return new ContentFilter(value);
            case TAG_NAME:
                return new TagFilter(value);
            case QUESTION_TYPE:
                return new GenericFilter<>("questionType", Operator.eq, Enum.valueOf(QuestionType.class, StringUtils.upperCase(value)));
            case CLINIC_TYPE:
                return new GenericFilter<>("clinicType", Operator.eq,
                    Enum.valueOf(CrmClinicType.class, StringUtils.upperCase(value)));
            case TAG_ID:
                return new TagIdFilter(value);
            case CLASSIFY_ID:
                return new ClassifyIdFilter(value);
            case CREATED_DATE:
                return new CreatedDateFilter(value);
            case ENABLED:
                return new GenericFilter<>("enabled", Operator.eq, Boolean.valueOf(value));
            case CRM_TYPE:
                return new GenericFilter<>("crmType", Operator.eq, Enum.valueOf(CrmType.class, StringUtils.upperCase(value)));
            case QUESTIONNAIRE_ID:
                this.questionnaireFilter = new QuestionnaireFilter(Long.valueOf(value));
                return questionnaireFilter;
            case QUESTION_ID:
                this.idFilter = new GenericFilter<>("id", Operator.eq, Long.valueOf(value));
                return idFilter;
            default:
                return null;
        }
    }

    @Override
    public Specification<CrmQuestion> toSpecification() {
        if (getFilters().isEmpty()) {
            return null;
        }
        List<SearchFilter<CrmQuestion>> filters = getFilters();

        List<SearchFilter<CrmQuestion>> tagIds = new ArrayList<>();
        List<SearchFilter<CrmQuestion>> others = new ArrayList<>();
        for (SearchFilter<CrmQuestion> filter : filters) {
            if (filter instanceof TagIdFilter) {
                tagIds.add(filter);
            } else {
                others.add(filter);
            }
        }
        return Specifications
            .and(Specifications.or(tagIds.stream().map(SearchFilter::toSpecification).collect(Collectors.toList())),
                Specifications.and(others.stream().map(SearchFilter::toSpecification).collect(Collectors.toList())));
    }

    @Override
    protected SearchFilter<CrmQuestion> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        /**
         * 标签名
         */
        TAG_NAME,
        /**
         * 问题
         */
        CONTENT,
        /**
         * 问题类型
         */
        QUESTION_TYPE,
        /**
         * 就诊类别
         */
        CLINIC_TYPE,
        CLASSIFY_ID,
        ENABLED,
        CRM_TYPE,
        CREATED_DATE,
        /**
         * 标签id
         */
        TAG_ID,
        QUESTIONNAIRE_ID,
        QUESTION_ID
    }

    public QuestionnaireFilter getQuestionnaireFilter() {
        return questionnaireFilter;
    }

    public GenericFilter<CrmQuestion> getIdFilter() {
        return idFilter;
    }
}
