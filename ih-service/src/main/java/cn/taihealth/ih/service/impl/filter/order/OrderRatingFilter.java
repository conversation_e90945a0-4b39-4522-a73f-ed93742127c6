package cn.taihealth.ih.service.impl.filter.order;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.OrderWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import java.util.Objects;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.ListJoin;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

/**
 * <AUTHOR>
 */
public class OrderRatingFilter implements SearchFilter<Order> {

    private final String pattern;
    private final User user;

    public OrderRatingFilter(String pattern, User user) {
        this.pattern = pattern;
        this.user = user;
    }

    public OrderRatingFilter(String pattern) {
        this.pattern = pattern;
        this.user = null;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Order> toSpecification() {
        return new Specification<Order>() {
            @Override
            public Predicate toPredicate(Root<Order> root, CriteriaQuery<?> criteriaQuery,
                                         CriteriaBuilder criteriaBuilder) {
                ListJoin<Order, OrderWorker> doctorWorker = root.joinList("workers");
                if (null == user){
                    return criteriaBuilder.and(
                        criteriaBuilder.equal(doctorWorker.get("rating"), Integer.valueOf(pattern))
                    );
                }
                return criteriaBuilder.and(
                    criteriaBuilder.equal(doctorWorker.get("user"), user),
                    criteriaBuilder.equal(doctorWorker.get("rating"), Integer.valueOf(pattern))
                );
            }
        };
    }

    @Override
    public String toExpression() {
        return "Order:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof OrderRatingFilter)) {
            return false;
        }

        OrderRatingFilter rhs = (OrderRatingFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
