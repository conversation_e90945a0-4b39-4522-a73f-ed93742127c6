package cn.taihealth.ih.service.impl.filter.epidemiologicalsurvey;


import cn.taihealth.ih.domain.EpidemiologicalSurvey;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

public class NumberFilter implements SearchFilter<EpidemiologicalSurvey> {
    private final String number;

    public NumberFilter(String number) {
        this.number = number;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<EpidemiologicalSurvey> toSpecification() {
        return Specifications.likeIgnoreCase("cardNum", number);
    }

    @Override
    public String toExpression() {
        return " number:" + number;
    }

    @Override
    public boolean isValid() {
        return number != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof NumberFilter)) {
            return false;
        }

        NumberFilter rhs = (NumberFilter) other;
        return Objects.equals(number, rhs.number);
    }

    @Override
    public int hashCode() {
        return Objects.hash(number);
    }
}
