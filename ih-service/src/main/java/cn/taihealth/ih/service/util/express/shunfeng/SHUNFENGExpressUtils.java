package cn.taihealth.ih.service.util.express.shunfeng;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import cn.taihealth.ih.service.dto.express.ExpressData.SHUNFENGExpressConfig;
import cn.taihealth.ih.service.dto.express.shunfeng.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.common.web.UrlUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class SHUNFENGExpressUtils {

    /**
     * 用于查询运单路由信息。（测试环境可通过 附录-测试环境模拟路由数据接口来产生模拟数据）
     *
     * @param trackingType   1-运单号 2-订单号
     * @param trackingNumber 运单号或订单号
     * @param phone          手机号后四位
     * @return 运单路由信息
     */
    public static List<SHUNFENGExpressRouteInfo> queryRoutes(String trackingType, String trackingNumber,
                                                             String phone, SHUNFENGExpressConfig config) {
        //trackingType	查询号类型1：根据顺丰运单号查询;2：根据客户订单号查	 	true	string
        //trackingNumber	查询号:trackingType=1,则此值为顺丰运单号,如果trackingType=2,则此值为客户订单号	 	true	string
        //phone	收件人电话后四位（trackingType=1时如果查不到路由信息，请尝试传该字段）
        Map<String, String> body = Maps.newHashMap();
        body.put("trackingType", trackingType);
        body.put("trackingNumber", trackingNumber);
        if (StringUtils.isNotBlank(phone)) {
            body.put("phone", phone);
        }

        String bodyStr = StandardObjectMapper.stringify(body);
        Map<String, String> headers = getHeaders(config.getHospitalCode(), bodyStr, config.getSecretKey());
        return postForSHUNFENG(config.getHost(), SFConstants.URI_QUERY_ROUTE, headers, null, bodyStr, new TypeReference<>() {});
    }

    /**
     * 预创建订单 客户通过传入地址信息等参数，校验是否能下单成功
     * @param config
     * @param preOrder
     * @return
     */
    public static String preOrder(SHUNFENGExpressConfig config, PreOrderDTO preOrder) {
        String bodyStr = StandardObjectMapper.stringify(preOrder);
        Map<String, String> headers = getHeaders(config.getHospitalCode(), bodyStr, config.getSecretKey());
        return postForSHUNFENG(config.getHost(), SFConstants.URI_PRE_ORDER, headers, null, bodyStr, new TypeReference<>() {});
    }

    /**
     * 预创建订单 客户通过传入地址信息等参数，校验是否能下单成功
     * @param config
     * @param createOrder
     * @return
     */
    public static CreateOrderResponse createOrder(SHUNFENGExpressConfig config, CreateOrderRequest createOrder) {
        String bodyStr = StandardObjectMapper.stringify(createOrder);
        Map<String, String> headers = getHeaders(config.getHospitalCode(), bodyStr, config.getSecretKey());
        return postForSHUNFENG(config.getHost(), SFConstants.URI_CREATE_ORDER, headers, null, bodyStr,
                new TypeReference<>() {});
    }

    /**
     * 调用顺丰医寄通接口，返回的是content
     *
     * @param path                路径
     * @param headers             请求头
     * @param params              请求参数
     * @param body                请求体
     * @param returnTypeReference 返回类型
     * @param <T>                 返回类型
     * @return
     */
    private static <T> T postForSHUNFENG(String host, String path, Map<String, String> headers, Map<String, String> params,
                                         Object body, TypeReference<T> returnTypeReference) {
        String url = UrlUtils.concatSegments(host, path);
        if (headers == null) {
            headers = Maps.newHashMap();
        }
        String bodyStr = null;
        if (body != null) {
            bodyStr = (body instanceof String) ? (String) body : StandardObjectMapper.stringify(body);
        }
        log.info("调用SHUNFENG 接口:url:{},headers:{},params:{},body:{}", url, headers, params, body);
        try {
            Response response = OkHttpUtils.post(url, Headers.of(headers), params, bodyStr);

            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                if (returnTypeReference == null) {
                    return null;
                }
                SHUNFENGResponse<T> shunfengResponse = StandardObjectMapper.readValue(
                        OkHttpUtils.getResponseBody(response).orElse(null), new TypeReference<>() {});
                if (shunfengResponse.isSuccess()) {
//                    ObjectMapper objectMapper = new ObjectMapper();
                    return StandardObjectMapper.getInstance().convertValue(shunfengResponse.getResult(),
                                                                           returnTypeReference);
//                    return objectMapper.convertValue(shunfengResponse.getResult(), returnTypeReference);
                } else {
//                    ObjectMapper objectMapper = new ObjectMapper();
                    SHUNFENGResponseErrorResult shunfengResponseErrorResult = StandardObjectMapper.getInstance().convertValue(
                        shunfengResponse.getResult(), new TypeReference<SHUNFENGResponseErrorResult>() {
                        });
                    log.info("SHUNFENG API调用成功-但返回了业务错误 code:{} message:{} result:{}", shunfengResponse.getCode(),
                             shunfengResponse.getMessage(), shunfengResponseErrorResult);

                    throw new Exception("SHUNFENG API调用成功-但返回了业务错误：" + shunfengResponseErrorResult.getMessage());
                }
            } else {
                throw new Exception("SHUNFENG API调用失败：" + response.body().string());
            }
        } catch (Exception e) {
            log.error("SHUNFENG API调用失败", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    private static Map<String, String> getHeaders(String hospitalCode, String reqBodyStr, String secretKey) {
        Map<String, String> headers = Maps.newHashMap();
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 医院编码
        headers.put("hospitalCode", hospitalCode);
        // 时间戳毫秒级
        headers.put("timestamp", timestamp);
        //签名 计算方式： SHA512(reqBody + secretKey + timestamp)
        headers.put("sign", ShaUtils.sha512(reqBodyStr + secretKey + timestamp));
        return headers;
    }
}
