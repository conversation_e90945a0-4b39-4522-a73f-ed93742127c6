package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.MoreBeanUtils;
import cn.taihealth.ih.domain.*;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.Prescription;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.domain.hospital.PrescriptionRecord;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.HealthRecordService;
import cn.taihealth.ih.service.api.UploadService;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.dto.historyrecord.OrderHistoryRecordDTO;
import cn.taihealth.ih.service.impl.filter.patient.PatientClinicSearch;
import cn.taihealth.ih.service.impl.filter.prescription.PrescriptionSearch;
import cn.taihealth.ih.service.vm.*;
import cn.taihealth.ih.service.vm.HealthRecordImageVM.ImageCount;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class HealthRecordServiceImpl implements HealthRecordService {

    private static final Set<String> IGNORE_COPY_KEYS = ImmutableSet.<String>builder()
        .add("maritalStatus").add("bloodType").add("id").add("user").add("visibility").build();
    private final HealthRecordRepository healthRecordRepository;
    private final HealthRecordImageRepository healthRecordImageRepository;
    private final ClinicRecordRepository clinicRecordRepository;
    private final ClinicRecordImageRepository clinicRecordImageRepository;
    private final UploadRepository uploadRepository;
    private final PatientClinicRepository patientClinicRepository;
    private final OfflineMedicalRepository offlineMedicalRepository;
    private final PrescriptionRepository prescriptionRepository;
    private final OrderRepository orderRepository;
    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final PrescriptionRecordRepository prescriptionRecordRepository;

    private final MedicalCaseRepository medicalCaseRepository;

    public HealthRecordServiceImpl(HealthRecordRepository healthRecordRepository,
                                   HealthRecordImageRepository healthRecordImageRepository,
                                   ClinicRecordRepository clinicRecordRepository,
                                   ClinicRecordImageRepository clinicRecordImageRepository,
                                   PatientClinicRepository patientClinicRepository,
                                   UploadRepository uploadRepository,
                                   OfflineMedicalRepository offlineMedicalRepository,
                                   PrescriptionRepository prescriptionRepository,
                                   OrderRepository orderRepository,
                                   PrescriptionOrderRepository prescriptionOrderRepository,
                                   PrescriptionRecordRepository prescriptionRecordRepository,
                                   MedicalCaseRepository medicalCaseRepository) {
        this.healthRecordRepository = healthRecordRepository;
        this.healthRecordImageRepository = healthRecordImageRepository;
        this.clinicRecordRepository = clinicRecordRepository;
        this.clinicRecordImageRepository = clinicRecordImageRepository;
        this.uploadRepository = uploadRepository;
        this.patientClinicRepository = patientClinicRepository;
        this.offlineMedicalRepository = offlineMedicalRepository;
        this.prescriptionRepository = prescriptionRepository;
        this.orderRepository = orderRepository;
        this.prescriptionOrderRepository = prescriptionOrderRepository;
        this.prescriptionRecordRepository = prescriptionRecordRepository;
        this.medicalCaseRepository = medicalCaseRepository;
    }

    @Override
    @Transactional
    public HealthRecord saveHealthRecord(Patient patient, HealthRecordDTO dto) {
        HealthRecord record = healthRecordRepository.findOneByPatient(patient)
            .orElse(new HealthRecord(patient.getUser(), patient));
        record.setAllergies(dto.getAllergies());
        record.setBloodType(dto.getBloodType());
        record.setWeight(dto.getWeight());
        record.setHeight(dto.getHeight());
        record.setMedicalConditions(dto.getMedicalConditions());
        record.setMedications(dto.getMedications());
        record.setVisibility(dto.getVisibility());
        healthRecordRepository.save(record);

        return record;
    }

    @Override
    @Transactional
    public ClinicRecord saveClinicRecord(User user, Patient patient, ClinicRecordVM dto) {
        boolean isNew = dto.getId() == null || dto.getId() <= 0;
        ClinicRecord record =
            isNew ? new ClinicRecord(user) : clinicRecordRepository.getById(dto.getId());
        PatientClinic patientClinic;
        if (isNew) {
            record.setUser(user);
            patientClinic = new PatientClinic();
        } else {
            Preconditions.checkArgument(Objects.equals(user, record.getUser()));
            patientClinic = patientClinicRepository.findOneByClinicRecord(record)
                .orElseGet(PatientClinic::new);
        }

        record.setPatient(patient);
        record.setMedicalRecord(dto.getMedicalRecord());
        record.setVisitDate(dto.getVisitDate());
        record.setHospital(dto.getHospital());
        record.setDoctor(dto.getDoctor());
        record.setType(dto.getType());
        record.setDepartment(dto.getDepartment());
        record.setResult(dto.getResult());
        record.setVisibility(dto.getVisibility());
        clinicRecordRepository.save(record);

        List<ClinicRecordImage> images = updateClinicRecordImages(record, dto.getImages());
        record.setClinicRecordImages(images);
        clinicRecordRepository.save(record);

        patientClinic.setClinicRecord(record);
        patientClinic.setPatient(patient);
        patientClinic.setType(record.getType());
        patientClinic.setSource(InputSource.MANUAL);
        patientClinic.setOnlineType(OnlineType.OFFLINE);
        patientClinic.setRealTimeStatus(RealTimeStatus.REAL_TIME);
        patientClinic.setUser(user);
        patientClinic.setVisitDate(record.getVisitDate());
        patientClinic.setResult(dto.getResult());
        patientClinicRepository.save(patientClinic);
        return record;
    }

    @Override
    @Transactional
    public PatientClinic saveClinicRecord1(User user, Patient patient,
                                           ClinicRecordVM dto) {
        boolean isNew = dto.getId() == null || dto.getId() <= 0;
        ClinicRecord record =
            isNew ? new ClinicRecord(user) : clinicRecordRepository.getById(dto.getId());
        PatientClinic patientClinic;
        if (isNew) {
            record.setUser(user);
            patientClinic = new PatientClinic();
        } else {
            Preconditions.checkArgument(Objects.equals(user, record.getUser()));
            patientClinic = patientClinicRepository.findOneByClinicRecord(record)
                .orElseGet(PatientClinic::new);
        }

        record.setPatient(patient);
        record.setMedicalRecord(dto.getMedicalRecord());
        record.setVisitDate(dto.getVisitDate());
        record.setHospital(dto.getHospital());
        record.setDoctor(dto.getDoctor());
        record.setType(dto.getType());
        record.setDepartment(dto.getDepartment());
        record.setResult(dto.getResult());
        record.setVisibility(dto.getVisibility());
        clinicRecordRepository.save(record);

        List<ClinicRecordImage> images = updateClinicRecordImages(record, dto.getImages());
        record.setClinicRecordImages(images);
        clinicRecordRepository.save(record);

        patientClinic.setClinicRecord(record);
        patientClinic.setPatient(patient);
        patientClinic.setSource(InputSource.MANUAL);
        patientClinic.setType(record.getType());
        patientClinic.setOnlineType(OnlineType.OFFLINE);
        patientClinic.setRealTimeStatus(RealTimeStatus.REAL_TIME);
        patientClinic.setUser(user);
        patientClinic.setVisitDate(record.getVisitDate());
        return patientClinicRepository.save(patientClinic);
    }

    private List<ClinicRecordImage> updateClinicRecordImages(ClinicRecord record,
                                                             List<UploadDTO> dtos) {
        List<ClinicRecordImage> exists = clinicRecordImageRepository.findByRecord(record);
        Set<Upload> uploads = dtos.stream().map(u -> uploadRepository.getById(u.getId()))
            .collect(Collectors.toSet());
        List<ClinicRecordImage> list = Lists.newArrayList();

        for (Upload each : uploads) {
            ClinicRecordImage image = clinicRecordImageRepository
                .findOneByRecordAndImage(record, each).orElse(new ClinicRecordImage(record));
            if (image.isNew()) {
                image.setImage(each);
                clinicRecordImageRepository.save(image);
            }

            list.add(image);
        }

        exists.stream().filter(old -> {
            // not exist in current list
            return list.stream().noneMatch(now -> Objects.equals(old, now));
        }).forEach(clinicRecordImageRepository::delete);
        return list;
    }

    @Override
    @Transactional
    public ClinicRecordImage addClinicRecordImage(ClinicRecord record, ClinicRecordImageDTO dto) {
        ClinicRecordImage image = dto.isNew() ? new ClinicRecordImage(record)
            : clinicRecordImageRepository.getById(dto.getId());

        Upload upload = uploadRepository.getById(dto.getImage().getId());
        Preconditions.checkArgument(Objects.equals(record.getUser(), upload.getUser()));
        image.setImage(upload);
        image.setType(dto.getType());
        clinicRecordImageRepository.save(image);
        return image;
    }

    @Override
    @Transactional
    public void deleteHealthRecord(Patient patient) {
        healthRecordRepository.findOneByPatient(patient).ifPresent(record -> {
            record.getImages().forEach(this::deleteHealthRecordImage);
            healthRecordRepository.delete(record);
        });
    }

    @Override
    @Transactional
    public void saveHealthRecordByField(
        Patient patient, HealthRecordField field,
        HealthRecordVM vm) {
        HealthRecord record = healthRecordRepository.findOneByPatient(patient)
            .orElse(new HealthRecord(patient.getUser(), patient));

        switch (field) {
            case BIRTHDAY:
                patient.setBirthday(vm.getBirthday());
                AppContext.getInstance(PatientRepository.class).save(patient);
                return;

            case MARITAL_STATUS:
                record.setMaritalStatus(vm.getMaritalStatus());
                break;

            case BLOOD_TYPE:
                record.setBloodType(vm.getBloodType());
                break;

            case OCCUPATIONAL_DISEASE:
                record.setOccupationalDiseases(vm.getOccupationalDiseases());
                if (HealthRecord.OccupationalDisease
                    .isAllIn(vm.getSelectedOccupationalDiseases())) {
                    record.setSelectedOccupationalDiseases(vm.getSelectedOccupationalDiseases());
                } else {
                    throw ErrorType.INVALID_CHOICE.toProblem();
                }
                break;

            case ALLERGY:
                record.setAllergies(vm.getAllergies());
                if (HealthRecord.Allergy.isAllIn(vm.getSelectedAllergies())) {
                    record.setSelectedAllergies(vm.getSelectedAllergies());
                } else {
                    throw ErrorType.INVALID_CHOICE.toProblem();
                }
                break;

            case FOOD_ALLERGY:
                record.setFoodAllergies(vm.getFoodAllergies());
                if (HealthRecord.FoodAllergy.isAllIn(vm.getSelectedFoodAllergies())) {
                    record.setSelectedFoodAllergies(vm.getSelectedFoodAllergies());
                } else {
                    throw ErrorType.INVALID_CHOICE.toProblem();
                }
                break;

            case GENETIC_DISEASE:
                record.setGeneticDiseases(vm.getGeneticDiseases());
                if (HealthRecord.GeneticDisease.isAllIn(vm.getSelectedGeneticDiseases())) {
                    record.setSelectedGeneticDiseases(vm.getSelectedGeneticDiseases());
                } else {
                    throw ErrorType.INVALID_CHOICE.toProblem();
                }
                break;

            default:
                MoreBeanUtils.copyPropertiesNonNull(record, vm, IGNORE_COPY_KEYS);
                break;
        }

        healthRecordRepository.save(record);
        updateHealthRecordImages(record, field, vm);
    }

    @Override
    @Transactional
    public void saveHealthRecord(Patient patient, HealthRecordVM vm) {
        HealthRecord record = healthRecordRepository.findOneByPatient(patient)
            .orElse(new HealthRecord(patient.getUser(), patient));
        patient.setBirthday(vm.getBirthday());
        AppContext.getInstance(PatientRepository.class).save(patient);
        record.setMaritalStatus(vm.getMaritalStatus());
        record.setBloodType(vm.getBloodType());
        record.setOccupationalDiseases(vm.getOccupationalDiseases());
        if (HealthRecord.OccupationalDisease
            .isAllIn(vm.getSelectedOccupationalDiseases())) {
            record.setSelectedOccupationalDiseases(vm.getSelectedOccupationalDiseases());
        } else {
            throw ErrorType.INVALID_CHOICE.toProblem();
        }
        record.setAllergies(vm.getAllergies());
        if (HealthRecord.Allergy.isAllIn(vm.getSelectedAllergies())) {
            record.setSelectedAllergies(vm.getSelectedAllergies());
        } else {
            throw ErrorType.INVALID_CHOICE.toProblem();
        }
        record.setFoodAllergies(vm.getFoodAllergies());
        if (HealthRecord.FoodAllergy.isAllIn(vm.getSelectedFoodAllergies())) {
            record.setSelectedFoodAllergies(vm.getSelectedFoodAllergies());
        } else {
            throw ErrorType.INVALID_CHOICE.toProblem();
        }
        record.setGeneticDiseases(vm.getGeneticDiseases());
        if (HealthRecord.GeneticDisease.isAllIn(vm.getSelectedGeneticDiseases())) {
            record.setSelectedGeneticDiseases(vm.getSelectedGeneticDiseases());
        } else {
            throw ErrorType.INVALID_CHOICE.toProblem();
        }
        MoreBeanUtils.copyPropertiesNonNull(record, vm, IGNORE_COPY_KEYS);
        healthRecordRepository.save(record);
        updateHealthRecordImages(record, HealthRecordField.FOOD_ALLERGY, vm);
        updateHealthRecordImages(record, HealthRecordField.OCCUPATIONAL_DISEASE, vm);
        updateHealthRecordImages(record, HealthRecordField.GENETIC_DISEASE, vm);
        updateHealthRecordImages(record, HealthRecordField.MEDICATION, vm);
        updateHealthRecordImages(record, HealthRecordField.MEDICAL_CONDITION, vm);
        updateHealthRecordImages(record, HealthRecordField.ALLERGY, vm);

    }

    private void updateHealthRecordImages(HealthRecord record,
                                          HealthRecordField field,
                                          HealthRecordVM vm) {
        if (!field.hasImage()) {
            return;
        }

        List<UploadVM> uploads;

        switch (field) {
            case BLOOD_TYPE:
                uploads = vm.getBloodTypeImages();
                break;

            case ALLERGY:
                uploads = vm.getAllergyImages();
                break;

            case FOOD_ALLERGY:
                uploads = vm.getFoodAllergyImages();
                break;

            case GENETIC_DISEASE:
                uploads = vm.getGeneticDiseasesImages();
                break;

            case MEDICATION:
                uploads = vm.getMedicationsImages();
                break;

            case MEDICAL_CONDITION:
                uploads = vm.getMedicalConditionsImages();
                break;

            case OCCUPATIONAL_DISEASE:
                uploads = vm.getOccupationalDiseasesImages();
                break;

            default:
                throw new IllegalArgumentException("Field " + field + " should have no images");
        }

        HealthRecordImage.ImageType imageType = field.getImageType();
        List<HealthRecordImage> images = getHealthRecordImages(record, imageType);
        uploads.forEach(upload -> {
            if (images.stream()
                .noneMatch(image -> Objects.equals(image.getImage().getId(), upload.getId()))) {
                HealthRecordImage image = new HealthRecordImage();
                image.setHealthRecord(record);
                image.setType(imageType);
                image.setImage(uploadRepository.getById(upload.getId()));
                healthRecordImageRepository.save(image);
                images.add(image);
            }
        });

        for (HealthRecordImage image : images) {
            if (uploads.stream()
                .noneMatch(u -> Objects.equals(image.getImage().getId(), u.getId()))) {
                deleteHealthRecordImage(image);
            }
        }
    }

    @Override
    @Transactional
    public void saveHealthRecordVisibility(Patient patient, Visibility visibility) {
        HealthRecord record = healthRecordRepository.findOneByPatient(patient)
            .orElse(new HealthRecord(patient.getUser(), patient));
        record.setVisibility(visibility);
        healthRecordRepository.save(record);
    }

    public void deleteHealthRecordImage(HealthRecordImage image) {
        Upload upload = image.getImage();
        healthRecordImageRepository.delete(image);
        AppContext.getInstance(UploadService.class).deleteUpload(upload);
    }

    @Override
    @Transactional(readOnly = true)
    public HealthRecordVM getHealthRecordByField(Patient patient, HealthRecordField field) {
        return healthRecordRepository.findOneByPatient(patient).map(record -> {
            HealthRecordVM vm = new HealthRecordVM();
            switch (field) {
                case BIRTHDAY:
                    vm.setBirthday(patient.getBirthday());
                    break;

                case HEIGHT:
                    vm.setHeight(record.getHeight());
                    break;

                case WEIGHT:
                    vm.setWeight(record.getWeight());
                    break;

                case MARITAL_STATUS:
                    vm.setMaritalStatus(record.getMaritalStatus());
                    break;

                case BLOOD_TYPE:
                    vm.setBloodType(record.getBloodType());
                    vm.setBloodTypeImages(
                        getHealthRecordUploads(record, HealthRecordImage.ImageType.BLOOD_TYPE));
                    break;

                case ALLERGY:
                    vm.setAllergies(record.getAllergies());
                    vm.setSelectedAllergies(record.getSelectedAllergies());
                    vm.setAllergyImages(
                        getHealthRecordUploads(record, HealthRecordImage.ImageType.ALLERGY));
                    break;

                case FOOD_ALLERGY:
                    vm.setFoodAllergies(record.getFoodAllergies());
                    vm.setSelectedFoodAllergies(record.getSelectedFoodAllergies());
                    vm.setFoodAllergyImages(
                        getHealthRecordUploads(record, HealthRecordImage.ImageType.FOOD_ALLERGY));
                    break;

                case OCCUPATIONAL_DISEASE:
                    vm.setOccupationalDiseases(record.getOccupationalDiseases());
                    vm.setSelectedOccupationalDiseases(record.getSelectedOccupationalDiseases());
                    vm.setOccupationalDiseasesImages(getHealthRecordUploads(record,
                                                                            HealthRecordImage.ImageType.OCCUPATIONAL_DISEASE));
                    break;

                case GENETIC_DISEASE:
                    vm.setGeneticDiseases(record.getGeneticDiseases());
                    vm.setSelectedGeneticDiseases(record.getSelectedGeneticDiseases());
                    vm.setGeneticDiseasesImages(getHealthRecordUploads(record,
                                                                       HealthRecordImage.ImageType.GENETIC_DISEASE));
                    break;

                case MEDICAL_CONDITION:
                    vm.setMedicalConditions(record.getMedicalConditions());
                    vm.setMedicalConditionsImages(getHealthRecordUploads(record,
                                                                         HealthRecordImage.ImageType.MEDICAL_CONDITION));
                    break;

                case MEDICATION:
                    vm.setMedications(record.getMedications());
                    vm.setMedicationsImages(
                        getHealthRecordUploads(record, HealthRecordImage.ImageType.MEDICATION));
                    break;

                default:
                    break;
            }

            return vm;
        }).orElse(new HealthRecordVM());
    }

    @Override
    @Transactional(readOnly = true)
    public HealthRecordVM getHealthRecord(Patient patient) {
        HealthRecordVM healthRecordVM = healthRecordRepository.findOneByPatient(patient).map(record -> {
            HealthRecordVM vm = new HealthRecordVM();
            vm.setBirthday(patient.getBirthday());
            vm.setHeight(record.getHeight());
            vm.setWeight(record.getWeight());
            vm.setMaritalStatus(record.getMaritalStatus());
            vm.setBloodType(record.getBloodType());
            vm.setBloodTypeImages(
                getHealthRecordUploads(record, HealthRecordImage.ImageType.BLOOD_TYPE));
            vm.setAllergies(record.getAllergies());
            vm.setSelectedAllergies(record.getSelectedAllergies());
            vm.setAllergyImages(
                getHealthRecordUploads(record, HealthRecordImage.ImageType.ALLERGY));
            vm.setFoodAllergies(record.getFoodAllergies());
            vm.setSelectedFoodAllergies(record.getSelectedFoodAllergies());
            vm.setFoodAllergyImages(
                getHealthRecordUploads(record, HealthRecordImage.ImageType.FOOD_ALLERGY));
            vm.setOccupationalDiseases(record.getOccupationalDiseases());
            vm.setSelectedOccupationalDiseases(record.getSelectedOccupationalDiseases());
            vm.setOccupationalDiseasesImages(getHealthRecordUploads(record,
                                                                    HealthRecordImage.ImageType.OCCUPATIONAL_DISEASE));
            vm.setGeneticDiseases(record.getGeneticDiseases());
            vm.setSelectedGeneticDiseases(record.getSelectedGeneticDiseases());
            vm.setGeneticDiseasesImages(getHealthRecordUploads(record,
                                                               HealthRecordImage.ImageType.GENETIC_DISEASE));

            vm.setMedicalConditions(record.getMedicalConditions());
            vm.setMedicalConditionsImages(getHealthRecordUploads(record,
                                                                 HealthRecordImage.ImageType.MEDICAL_CONDITION));
            vm.setMedications(record.getMedications());
            vm.setMedicationsImages(
                getHealthRecordUploads(record, HealthRecordImage.ImageType.MEDICATION));
            HealthRecordDTO healthRecordDTO = new HealthRecordDTO(record);
            vm.setId(healthRecordDTO.getId());
            vm.setCreatedDate(healthRecordDTO.getCreatedDate());
            vm.setUpdatedDate(healthRecordDTO.getUpdatedDate());
            vm.setUser(healthRecordDTO.getUser());
            return vm;
        }).orElse(new HealthRecordVM());
        List<CategoryVM> categories = Lists.newArrayList();
        healthRecordVM.setCategories(categories);
        //2023-03-08 hotfix，修复查询就诊人健康概况没有疾病标签和生日的bug
        healthRecordVM.setBirthday(patient.getBirthday());
        return healthRecordVM;
    }

    @Transactional
    @Override
    public OfflineMedicalRecords saveOfflineRecord(User user, Patient patient,
                                                   OfflineMedicalRecordsDTO offlineRecords) {
        OfflineMedicalRecords offlineMedicalRecords = offlineRecords.toEntity(null);
        offlineMedicalRecords.setPatient(patient);
        List<Upload> uploads = offlineRecords.getUpload().stream()
            .map(u -> uploadRepository.getById(u.getId()))
            .filter(upload -> upload.getUser().equals(user)).collect(Collectors.toList());
        offlineMedicalRecords.setUpload(uploads);

        offlineMedicalRecords.setPatient(patient);
        offlineMedicalRecords.setUser(user);
        //处方记录中间表
        PrescriptionRecord prescriptionRecord = new PrescriptionRecord();
        prescriptionRecord.setUser(user);
        prescriptionRecord.setPatient(patient);
        prescriptionRecord.setOfflineMedicalRecords(offlineMedicalRecords);
        prescriptionRecord.setType(PrescriptionOnlineType.OFFLINE);
        prescriptionRecord.setStartTime(offlineMedicalRecords.getPrescriptionTime());
        prescriptionRecordRepository.save(prescriptionRecord);
        return offlineMedicalRepository.save(offlineMedicalRecords);
    }

    @Override
    @Transactional
    public OfflineMedicalRecords updateOfflineRecord(User user, long id,
                                                     OfflineMedicalRecordsDTO offlineRecordDTO) {

        OfflineMedicalRecords offlineRecord = offlineMedicalRepository.getById(id);
        List<Upload> uploads = offlineRecordDTO.getUpload().stream()
            .map(u -> uploadRepository.getById(u.getId()))
            .filter(upload -> upload.getUser().equals(user)).collect(Collectors.toList());
        offlineRecord.setUpload(uploads);
        offlineRecord.setContent(offlineRecordDTO.getContent());
        offlineRecord.setRemarks(offlineRecordDTO.getRemarks());
        offlineRecord.setPrescriptionTime(offlineRecordDTO.getPrescriptionTime());
        offlineMedicalRepository.saveAndFlush(offlineRecord);
        return offlineRecord;
    }

    @Override
    @Transactional
    public Page<OfflineOnlineHistoryVM> findOfflineOnlineRecord(Patient patient, Pageable pageable) {

        List<Specification<PrescriptionRecord>> sp = Lists.newArrayList();
        sp.add(Specifications.eq("patient", patient));
        sp.add(Specifications.isNotNull("startTime"));

        Page<PrescriptionRecord> prescriptionRecords = prescriptionRecordRepository.findAll(
            Specifications.and(sp), pageable);
        return prescriptionRecords.map(u -> {
            if (u.getType() == PrescriptionOnlineType.ONLINE) {
                OfflineOnlineHistoryVM online = new OfflineOnlineHistoryVM();
                PrescriptionOrder preorder = u.getPrescriptionOrder();
                online.setId(preorder.getId());
                List<Prescription> preList = preorder.getPrescription();
                StringBuffer content = new StringBuffer(" ");
                preList.forEach(p -> {
                    content.append("  药品名称: #").append(p.getDrugName());
                });
                online.setContent(content.toString());
                online.setPrescriptionTime(preorder.getReviewTime());
                if (null != preorder.getPatient()) {
                    online.setPatient(new PatientVM(preorder.getPatient(), null));
                }
                if (null != preorder.getUpload()) {
                    online.getUpload().add(new UploadVM(preorder.getUpload()));
                }
                online.setType("online");
                return online;
            } else {
                OfflineOnlineHistoryVM offline = new OfflineOnlineHistoryVM();
                OfflineMedicalRecords off = u.getOfflineMedicalRecords();
                offline.setType("offline");
                offline.setId(off.getId());
                offline.setContent(off.getContent());
                offline.setPatient(new PatientVM(off.getPatient(), null));
                offline.setPrescriptionTime(off.getPrescriptionTime());
                offline.setUpload(off.getUpload().stream().map(UploadVM::new).collect(Collectors.toList()));
                return offline;
            }
        });
    }

    @Override
    @Transactional
    public void deleteOfflineRecord(long id) {
        OfflineMedicalRecords offlineMedicalRecords = offlineMedicalRepository.findById(id)
            .orElse(new OfflineMedicalRecords());
        offlineMedicalRepository.delete(offlineMedicalRecords);
        prescriptionRecordRepository.deleteByOfflineMedicalRecords(offlineMedicalRecords);
    }

    @Override
    @Transactional
    public Page<PrescriptionRecordDTO> prescriptionRecordHistoryList(Patient patient, Pageable pageable, String query) {
        List<Specification<PrescriptionRecord>> sp = Lists.newArrayList();
        sp.add(Specifications.eq("patient", patient));
        sp.add(Specifications.isNotNull("startTime"));
        Specification<PrescriptionRecord> prescriptionRecordSpecification = PrescriptionSearch.of(query)
            .toSpecification();
        sp.add(prescriptionRecordSpecification);
        Page<PrescriptionRecord> prescriptionRecords = prescriptionRecordRepository
            .findAll(Specifications.and(sp), pageable);
        return prescriptionRecords.map(record -> {
            PrescriptionRecordDTO prescriptionRecordDTO = new PrescriptionRecordDTO(record);
            if (PrescriptionOnlineType.ONLINE.equals(record.getType())) {
                Long orderId = record.getPrescriptionOrder().getOrder().getId();
                medicalCaseRepository.findByOrderId(orderId)
                    .ifPresent(medicalCase -> prescriptionRecordDTO.setMedicalCase(new MedicalCaseDTO(medicalCase)));
            }
            return prescriptionRecordDTO;
        });
    }

    @Override
    public OrderHistoryRecordDTO getOrderHistoryRecord(Long id) {
        PatientClinic patientClinic = patientClinicRepository.getById(id);
        OrderHistoryRecordDTO orderHistoryRecordDTO = new OrderHistoryRecordDTO(
            patientClinic.getOrderHistoryRecord());
        Order order = orderRepository.getById(orderHistoryRecordDTO.getOrderId());
        DoctorVM doctor = new DoctorVM(order.getDoctor());
        orderHistoryRecordDTO.setDoctor(doctor);
        orderHistoryRecordDTO.getOrder().setTypeImages(
            order.getImages().stream().map(OrderImageDTO::new)
                .collect(Collectors.toList())
        );
        return orderHistoryRecordDTO;
    }

    @Override
    public Page<PatientClinicDTO> getPatientClinicRecord(Patient patient, User user, String query, Pageable page) {
        PatientClinicSearch patientSearch = PatientClinicSearch.of(query);
        Specification<PatientClinic> spec = Specifications
            .and(Specifications.eq("user", user), Specifications.eq("patient", patient));
        return patientClinicRepository.findAll(spec.and(patientSearch.toSpecification()), page).map(
            PatientClinicDTO::new);
    }

    private List<HealthRecordImage> getHealthRecordImages(HealthRecord record,
                                                          HealthRecordImage.ImageType imageType) {
        return record.getImages().stream().filter(image -> image.getType() == imageType)
            .collect(Collectors.toList());
    }

    private List<UploadVM> getHealthRecordUploads(HealthRecord record,
                                                  HealthRecordImage.ImageType imageType) {
        return getHealthRecordImages(record, imageType).stream()
            .sorted(Comparator.comparing(HealthRecordImage::getUpdatedDate))
            .map(HealthRecordImage::getImage).map(UploadVM::new).collect(Collectors.toList());
    }

    /**
     * 获取就诊人健康档案, 包含2个以内图片
     *
     * @param patient
     * @return
     */
    @Override
    public HealthRecordImageVM getHealthRecordImageVM(Patient patient) {
        HealthRecord record = healthRecordRepository.findOneByPatient(patient).orElse(null);
        if (record == null) {
            return null;
        } else {
            HealthRecordImageVM vm = new HealthRecordImageVM(record);
            List<UploadVM> bloods = getHealthRecordUploads(record, HealthRecordImage.ImageType.BLOOD_TYPE);
            ImageCount bloodTypeImages = getImageCount(bloods);
            List<UploadVM> allergies = getHealthRecordUploads(record, HealthRecordImage.ImageType.ALLERGY);
            ImageCount allergyImages = getImageCount(allergies);
            List<UploadVM> foodAllergies = getHealthRecordUploads(record, HealthRecordImage.ImageType.FOOD_ALLERGY);
            ImageCount foodAllergyImages = getImageCount(foodAllergies);
            List<UploadVM> occupationalDiseases = getHealthRecordUploads(record,
                                                                         HealthRecordImage.ImageType.OCCUPATIONAL_DISEASE);
            ImageCount occupationalDiseasesImages = getImageCount(occupationalDiseases);
            List<UploadVM> geneticDiseases = getHealthRecordUploads(record,
                                                                    HealthRecordImage.ImageType.GENETIC_DISEASE);
            ImageCount geneticDiseasesImages = getImageCount(geneticDiseases);
            List<UploadVM> medications = getHealthRecordUploads(record,
                                                                HealthRecordImage.ImageType.MEDICATION);
            ImageCount medicationsImages = getImageCount(medications);
            List<UploadVM> medicalConditions = getHealthRecordUploads(record,
                                                                      HealthRecordImage.ImageType.MEDICAL_CONDITION);
            ImageCount medicalConditionsImages = getImageCount(medicalConditions);
            vm.setBloodTypeImages(bloodTypeImages);
            vm.setAllergyImages(allergyImages);
            vm.setFoodAllergyImages(foodAllergyImages);
            vm.setOccupationalDiseasesImages(occupationalDiseasesImages);
            vm.setGeneticDiseasesImages(geneticDiseasesImages);
            vm.setMedicationsImages(medicationsImages);
            vm.setMedicalConditionsImages(medicalConditionsImages);
            return vm;
        }
    }

    private ImageCount getImageCount(List<UploadVM> vms) {
        ImageCount imageCount = new ImageCount();
        imageCount.setCount(vms.size());
        imageCount.setImages((vms.size() > 2 ? vms.subList(0, 2) : vms)
                                 .stream().map(UploadVM::getUrl).collect(Collectors.toList()));
        return imageCount;
    }
}
