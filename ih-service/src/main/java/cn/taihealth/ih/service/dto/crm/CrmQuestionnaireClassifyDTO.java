package cn.taihealth.ih.service.dto.crm;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.crm.CrmQuestionnaireClassify;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;


@Data
@NoArgsConstructor
public class CrmQuestionnaireClassifyDTO extends UpdatableDTO {

    @ApiModelProperty("问题分类名称")
    @NotEmpty(message = "问题分类名称必填")
    @Size(max = 255, message = "标签名不能超过255个字符")
    private String name;
    @ApiModelProperty("编码")
    @Size(max = 2000, message = "标签描述不能超过2000个字符")
    private String code;

    public CrmQuestionnaireClassifyDTO(CrmQuestionnaireClassify crmQuestionnaireClassify) {
        super(crmQuestionnaireClassify);
        this.name = crmQuestionnaireClassify.getName();
        this.code = crmQuestionnaireClassify.getCode();
    }

    public CrmQuestionnaireClassify toEntity(Hospital hospital) {
        CrmQuestionnaireClassify classify = new CrmQuestionnaireClassify();
        classify.setName(name);
        classify.setCode(code);
        classify.setHospital(hospital);
        return classify;
    }
}
