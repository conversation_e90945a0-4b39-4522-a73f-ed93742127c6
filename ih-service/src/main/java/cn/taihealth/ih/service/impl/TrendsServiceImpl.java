package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.HospitalTrends;
import cn.taihealth.ih.repo.HospitalTrendsRepository;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.TrendsService;
import cn.taihealth.ih.service.dto.HospitalTrendsDTO;
import cn.taihealth.ih.service.impl.filter.trends.HospitalTrendsSearch;
import com.gitq.jedi.data.specification.Specifications;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: hyp
 * @Date: 2023/7/16 上午9:52
 */
@Service
@RequiredArgsConstructor
public class TrendsServiceImpl implements TrendsService {

    private final HospitalTrendsRepository hospitalTrendsRepository;

    @Override
    public HospitalTrendsDTO addTrends(HospitalTrendsDTO dto, Hospital hospital) {
        HospitalTrends trends;
        if (dto.isNew()) {
            trends = new HospitalTrends();
            trends.setHospital(hospital);
            trends.setCreatedUser(CurrentUser.getOrNull());
        } else {
            trends = hospitalTrendsRepository.getById(dto.getId());
            if (Objects.isNull(trends) || !Objects.equals(trends.getHospital(), hospital)) {
                throw ErrorType.MESSAGE_TEMPLATE_NOT_EXIST.toProblem("动态不存在");
            }
        }

        trends.setTitle(dto.getTitle());
        trends.setContent(dto.getContent());
        trends.setThumb(dto.getThumb());
        trends.setIsShow(dto.getIsShow());
        trends.setUpdatedUser(CurrentUser.getOrNull());

        this.hospitalTrendsRepository.save(trends);
        return new HospitalTrendsDTO(trends);
    }

    @Override
    public HospitalTrendsDTO getTrendsById(Long trendsId) {
        HospitalTrends trends = this.hospitalTrendsRepository.getById(trendsId);
        return Objects.nonNull(trends) ? new HospitalTrendsDTO(trends) : null;
    }

    @Override
    public void removeTrends(Hospital hospital, Long trendsId) {
        HospitalTrends trends = this.hospitalTrendsRepository.getById(trendsId);
        if (Objects.isNull(trends) || !Objects.equals(trends.getHospital(), hospital)) {
            throw ErrorType.MESSAGE_TEMPLATE_NOT_EXIST.toProblem("动态不存在");
        }
        this.hospitalTrendsRepository.delete(trends);
    }

    @Override
    public Page<HospitalTrendsDTO> getTrendsList(String query, Pageable request, Hospital hospital) {
        List<Specification<HospitalTrends>> specs = new LinkedList<>();
        HospitalTrendsSearch search = HospitalTrendsSearch.of(query);
        specs.add(search.toSpecification());
        if (hospital == null) {
            specs.add(Specifications.isNull("hospital"));
        } else {
            specs.add(Specifications.eq("hospital", hospital));
        }
        return this.hospitalTrendsRepository.findAll(Specifications.and(specs), request).map(HospitalTrendsDTO::new);
    }

}
