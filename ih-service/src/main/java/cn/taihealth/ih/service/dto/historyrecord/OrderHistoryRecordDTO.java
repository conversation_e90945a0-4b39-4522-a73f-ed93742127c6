package cn.taihealth.ih.service.dto.historyrecord;

import cn.taihealth.ih.domain.hospital.OrderHistoryRecord;
import cn.taihealth.ih.service.vm.DoctorVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Date;
import java.util.List;


@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class OrderHistoryRecordDTO {

    private Long id;
    @ApiModelProperty("订单表 ")
    private OrderHistoryDTO order;

    @ApiModelProperty("订单ID ")
    private Long orderId;

    @ApiModelProperty("user 表 ")
    private UserHistoryDTO user;

    @ApiModelProperty("患者")
    private PatientHistoryDTO patient;

    @ApiModelProperty("患者ID ")
    private Long patientId;

    @ApiModelProperty("病历表 ")
    private MedicalCaseHistoryDTO medicalCaseHistoryDTO;

    @ApiModelProperty(name = "检查表")
    private List<CheckHistoryDTO> check = Lists.newArrayList();

    @ApiModelProperty("检验表")
    private List<InspectionHistoryDTO> inspection = Lists.newArrayList();

    @ApiModelProperty("处方表")
    //private List<PrescriptionHistoryDTO> prescription = Lists.newArrayList();
    private List<PrescriptionOrdersHistoryDTO> prescription = Lists.newArrayList();

    @ApiModelProperty(name = "操作人名字")
    private String operatorName;

    @ApiModelProperty("医院名称")
    private String hospitalName;

    @ApiModelProperty(name = "科室名称")
    private String deptName;

    @ApiModelProperty(name = "医生名称")
    private String doctorName;

    private Date endedDate;

    private Date triagedDate;

    private Date registeredDate;

    private Date admissionDate;

    private Date confirmedDate;

    @ApiModelProperty(name = "备注")
    private String remarks;

    @ApiModelProperty("医生")
    private DoctorVM doctor;


    public OrderHistoryRecordDTO(OrderHistoryRecord orderHistoryRecord) {
        this.orderId = orderHistoryRecord.getOrderId();
        this.patientId = orderHistoryRecord.getPatientId();
        this.order =
            null != orderHistoryRecord.getOrder() && !orderHistoryRecord.getOrder().equals("null")
                ? new OrderHistoryDTO().init(orderHistoryRecord.getOrder()): null;
        // 后期需要改造，用对象存入数据库字段
        // StandardObjectMapper.readValue(orderHistoryRecord.getOrder(), new TypeReference<Order>() {});
        this.patient = null != orderHistoryRecord.getPatient() && !orderHistoryRecord.getPatient()
            .equals("null") ? new PatientHistoryDTO().init(orderHistoryRecord.getPatient()) : null;

        this.medicalCaseHistoryDTO =
            null != orderHistoryRecord.getMedicalCase() && !orderHistoryRecord.getMedicalCase()
                .equals("null") ? new MedicalCaseHistoryDTO()
                .init(orderHistoryRecord.getMedicalCase()) : null;

        if (null != orderHistoryRecord.getCheck() && !orderHistoryRecord.getCheck().equals("null")
            && !orderHistoryRecord.getCheck().equals("[]")) {
            JSONArray jsonList = new JSONArray(orderHistoryRecord.getCheck());
            jsonList.forEach(check -> {
                this.check.add(new CheckHistoryDTO((JSONObject) check));
            });
        }
        if (null != orderHistoryRecord.getInspection() && !orderHistoryRecord.getInspection()
            .equals("null")) {
         /*   JSONArray iHistoryList = new JSONArray(orderHistoryRecord.getInspection());
            iHistoryList.forEach(inst -> {
                this.inspection.add(new InspectionHistoryDTO((JSONObject) inst));
            });*/
        }
        if (null != orderHistoryRecord.getPrescription() && !orderHistoryRecord.getPrescription()
            .equals("null")) {
     /*       List<PrescriptionOrderDTO> pHistoryList = new Lists.newArrayList(orderHistoryRecord.getPrescription());
              //  (orderHistoryRecord.getPrescription());
            pHistoryList.forEach(pre -> {
                this.prescription.add(new PrescriptionOrdersHistoryDTO().init(pre));
            });*/
            this.prescription =new PrescriptionOrdersHistoryDTO().init(orderHistoryRecord.getPrescription());
        }
        this.hospitalName = orderHistoryRecord.getHospitalName();
        this.operatorName = orderHistoryRecord.getOperator();
        this.deptName = orderHistoryRecord.getDeptName();
        this.doctorName = orderHistoryRecord.getDoctorName();
        this.triagedDate = orderHistoryRecord.getTriagedDate();
        this.registeredDate = orderHistoryRecord.getRegisteredDate();
        this.admissionDate = orderHistoryRecord.getAdmissionDate();
        this.confirmedDate = orderHistoryRecord.getConfirmedDate();
    }
}
