package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.VideoRoom;

/**
 */
public class VideoRoomDTO extends UpdatableDTO {

    private UserDTO owner;

    private VideoRoom.Status status;

    private String roomId;

    public VideoRoomDTO() {}

    public VideoRoomDTO(VideoRoom room) {
        super(room);

        this.owner = new UserDTO(room.getOwner());
        this.status = room.getStatus();
        this.roomId = room.getRoomId();
    }

    public UserDTO getOwner() {
        return owner;
    }

    public void setOwner(UserDTO owner) {
        this.owner = owner;
    }

    public VideoRoom.Status getStatus() {
        return status;
    }

    public void setStatus(VideoRoom.Status status) {
        this.status = status;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }
}
