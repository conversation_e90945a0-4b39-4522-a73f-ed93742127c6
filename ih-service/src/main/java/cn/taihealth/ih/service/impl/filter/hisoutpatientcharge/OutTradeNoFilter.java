package cn.taihealth.ih.service.impl.filter.hisoutpatientcharge;

import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.*;

public class OutTradeNoFilter implements SearchFilter<HisOutpatientCharge> {

    private final String value;

    public OutTradeNoFilter(String value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<HisOutpatientCharge> toSpecification() {
        return new Specification<HisOutpatientCharge>() {
            @Override
            public Predicate toPredicate(Root<HisOutpatientCharge> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
                // 创建 WechatOrder 子查询
                Subquery<Long> wechatOrderSubquery = query.subquery(Long.class);
                Root<WechatOrder> wechatOrderRoot = wechatOrderSubquery.from(WechatOrder.class);
                wechatOrderSubquery.select(wechatOrderRoot.get("productId"))
                        .where(criteriaBuilder.equal(wechatOrderRoot.get("outTradeNo"), value));

                // 创建 AliPayOrder 子查询
                Subquery<Long> aliPayOrderSubquery = query.subquery(Long.class);
                Root<AliPayOrder> aliPayOrderRoot = aliPayOrderSubquery.from(AliPayOrder.class);
                aliPayOrderSubquery.select(aliPayOrderRoot.get("body"))
                        .where(criteriaBuilder.equal(aliPayOrderRoot.get("outTradeNo"), value));

                // 使用两个子查询结果构建 OR 条件
                Predicate wechatOrderPredicate = criteriaBuilder.in(root.get("hisOutpatientChargeGroup").get("id")).value(wechatOrderSubquery);
                Predicate aliPayOrderPredicate = criteriaBuilder.in(root.get("hisOutpatientChargeGroup").get("id")).value(aliPayOrderSubquery);

                // 将两个条件组合为 OR
                return criteriaBuilder.or(wechatOrderPredicate, aliPayOrderPredicate);
            }
        };
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
