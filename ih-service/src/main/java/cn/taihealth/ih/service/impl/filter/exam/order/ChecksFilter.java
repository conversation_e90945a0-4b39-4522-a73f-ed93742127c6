package cn.taihealth.ih.service.impl.filter.exam.order;

import cn.taihealth.ih.domain.hospital.ExamOrder;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Objects;

public class ChecksFilter implements SearchFilter<ExamOrder> {

    private final String pattern;

    public ChecksFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<ExamOrder> toSpecification() {
        List<Specification<ExamOrder>> fliter = Lists.newArrayList();
        fliter.add(Specifications.like("check.patName", pattern));
        fliter.add(Specifications.like("check.patientMobile", pattern));
        fliter.add(Specifications.like("check.cardNumber", pattern));
        fliter.add(Specifications.like("check.number", pattern));
        return Specifications.or(fliter);
    }

    @Override
    public String toExpression() {
        return "patName/patientMobile/cardNumber/number:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof ChecksFilter)) {
            return false;
        }

        ChecksFilter rhs = (ChecksFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
