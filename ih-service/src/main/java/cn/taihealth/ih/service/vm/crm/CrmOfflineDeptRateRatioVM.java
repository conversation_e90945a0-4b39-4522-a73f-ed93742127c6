package cn.taihealth.ih.service.vm.crm;

import cn.taihealth.ih.service.dto.hospital.offline.OfflineDeptDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CrmOfflineDeptRateRatioVM {

    @ApiModelProperty("线下科室")
    private OfflineDeptDTO dept;

    @ApiModelProperty("本期满意率")
    private Double currentRate;

    @ApiModelProperty("上期满意率")
    private Double previousRate;

    @ApiModelProperty("本期样本量")
    private Long currentCount;

    @ApiModelProperty("上期样本量")
    private Long previousCount;

}
