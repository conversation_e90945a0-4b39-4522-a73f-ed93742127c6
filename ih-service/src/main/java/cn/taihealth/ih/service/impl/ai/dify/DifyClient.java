package cn.taihealth.ih.service.impl.ai.dify;

import cn.hutool.core.util.StrUtil;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.handler.LogRequestIdConverter;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import cn.taihealth.ih.service.dto.ai.dify.*;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.common.web.UrlUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.BufferedSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class DifyClient {

    private String apiKey;

    private String difyUrl;

    public DifyClient(String apiKey, String difyUrl) {
        this.apiKey = apiKey;
        this.difyUrl = difyUrl;
    }

    /**
     * 获取应用配置信息
     * @return
     */
    public ParametersResponse parameters() {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }

        String url = UrlUtils.concatSegments(difyUrl, "/parameters");
        Map<String, String> headers = getHeaders(apiKey);

        log.info("调用Dify[获取应用配置信息] 接口:url:{},headers:{}", url, headers);
        try {
            Response response = OkHttpUtils.get(url, null, Headers.of(headers));
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[获取应用配置信息] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, ParametersResponse.class);
            }
            log.warn("调用Dify[获取应用配置信息] 接口成功，未响应数据");
            return null;
        } catch (Exception e) {
            log.error("调用Dify[获取应用配置信息] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 发送对话消息（阻塞）
     * @param request
     * @return
     */
    public MessageResponse sendMessageBlocking(MessageRequest request) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/chat-messages");
        Map<String, String> headers = getHeaders(apiKey);

        String bodyStr = null;
        if (request != null) {
            bodyStr = StandardObjectMapper.stringify(request);
        }
        log.info("调用Dify[发送对话消息] 接口:url:{},headers:{},body:{}", url, headers, bodyStr);
        try {
            Response response = OkHttpUtils.post(url, Headers.of(headers),null, bodyStr);
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[发送对话消息] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, MessageResponse.class);
            }
            log.warn("调用Dify[发送对话消息] 接口成功，未响应数据: {}", StandardObjectMapper.stringify(response));
            return null;
        } catch (Exception e) {
            log.error("调用Dify[发送对话消息] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 发送对话消息（流式）
     * @param body
     * @return
     */
    public SseEmitter sendMessageStreaming(MessageRequest body) {

        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/chat-messages");
        Map<String, String> headers = getHeaders(apiKey);
        headers.put("reqId", MDC.get(LogRequestIdConverter.REQUEST_ID));
        String bodyStr = null;
        if (body != null) {
            bodyStr = StandardObjectMapper.stringify(body);
        }
        log.info("调用Dify[发送对话消息（流式）] 接口:url:{},headers:{},body:{}", url, headers, bodyStr);

        SseEmitter emitter = new SseEmitter(60 * 1000L);
        try {
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), bodyStr);
            Request request = new Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .headers(Headers.of(headers))
                    .build();

            OkHttpClient.Builder bu = OkHttpUtils.OkHttpHolder.getClient().newBuilder();
            bu.connectTimeout(60, TimeUnit.SECONDS);
            bu.readTimeout(60, TimeUnit.SECONDS);
            bu.callTimeout(60, TimeUnit.SECONDS);

            bu.build().newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    emitter.completeWithError(e);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        emitter.completeWithError(new IOException("Unexpected code " + response));
                        return;
                    }
                    DifyStreamResponseHandler difyStreamResponseHandler = new DifyStreamResponseHandler();
                    try (ResponseBody responseBody = response.body()) {
                        if (responseBody != null) {
                            BufferedSource source = responseBody.source();
                            while (!source.exhausted()) {
                                String line = source.readUtf8LineStrict();
                                if (StrUtil.isEmpty(line.trim())) {
                                    continue;
                                }
                                difyStreamResponseHandler.processLine(line, emitter);
                            }
                        }
                    } catch (Exception e) {
                        log.error("调用Dify[发送对话消息（流式）] 接口成功，响应失败。Response: {}, Exception:{}", StandardObjectMapper.stringify(response.body()), e);
                        difyStreamResponseHandler.finishProcessing(emitter);
                    }
                }
            });
            return emitter;
        } catch (Exception e) {
            log.error("调用Dify[发送对话消息（流式）] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 上传文件
     * @return
     */
    public UploadResponse uploadFile(MultipartFile file, String user) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }

        String url = UrlUtils.concatSegments(difyUrl, "/files/upload");

        OkHttpClient.Builder bu = OkHttpUtils.OkHttpHolder.getClient().newBuilder();
        bu.connectTimeout(60, TimeUnit.SECONDS);
        bu.readTimeout(60, TimeUnit.SECONDS);
        bu.callTimeout(60, TimeUnit.SECONDS);

        MediaType mediaType = MediaType.parse("image/" + getFileExtension(file));

        try {
            RequestBody fileBody = RequestBody.create(mediaType, file.getBytes());
            MultipartBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", file.getOriginalFilename(), fileBody)
                    .addFormDataPart("user", user)
                    .build();

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .post(requestBody)
                    .build();

            try (Response response = bu.build().newCall(request).execute()) {
                if (response.isSuccessful()) {
                    if (response.body() == null) {
                        return null;
                    }
                    String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                    log.info("调用Dify[上传文件] 接口返回:{}", responseString);
                    return StandardObjectMapper.getInstance().readValue(responseString, UploadResponse.class);
                }
                return null;
            }
        } catch (Exception e) {
            log.error("调用Dify[上传文件] 接口失败 {}", StandardObjectMapper.stringify(e));
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    // 获取文件扩展名（根据文件的扩展名来设置MIME类型）
    private String getFileExtension(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (filename != null && filename.contains(".")) {
            return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        }
        return "png"; // 默认返回png
    }
    /**
     * 停止响应
     * 仅支持流式模式。
     * @param user
     * @param taskId
     * @return
     */
    public StopResponse stop(String user, String taskId) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        if (StringUtils.isEmpty(user) || StringUtils.isEmpty(taskId)) {
            log.error("用户和taskID必填");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/chat-messages/", taskId, "/stop");
        Map<String, String> headers = getHeaders(apiKey);

        Map<String, String> body = new HashMap<>();
        body.put("user", user);

        String bodyStr = null;
        if (body != null) {
            bodyStr = StandardObjectMapper.stringify(body);
        }

        log.info("调用Dify[停止响应] 接口:url:{},headers:{},body:{}", url, headers, StandardObjectMapper.stringify(bodyStr));
        try {
            Response response = OkHttpUtils.post(url, Headers.of(headers), null, bodyStr);
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[停止响应] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, StopResponse.class);
            }
            log.warn("调用Dify[停止响应] 接口成功，未响应数据");
            StopResponse result = new StopResponse();
            result.setResult("success");
            return result;
        } catch (Exception e) {
            log.error("调用Dify[停止响应] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    public FeedbackResponse feedbacks(String messageId, FeedbackRequest feedback) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        if (StringUtils.isEmpty(feedback.getUser()) || StringUtils.isEmpty(messageId)) {
            log.error("用户和messageId必填");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/messages/", messageId, "/feedbacks");
        Map<String, String> headers = getHeaders(apiKey);

        Map<String, String> body = new HashMap<>();
        body.put("user", feedback.getUser());
        body.put("content", feedback.getContent());
        body.put("rating", feedback.getRating());

        String bodyStr = null;
        if (body != null) {
            bodyStr = StandardObjectMapper.stringify(body);
        }

        log.info("调用Dify[消息反馈] 接口:url:{},headers:{},body:{}", url, headers, StandardObjectMapper.stringify(bodyStr));
        try {
            Response response = OkHttpUtils.post(url, Headers.of(headers), null, bodyStr);
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[消息反馈] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, FeedbackResponse.class);
            }
            log.warn("调用Dify[消息反馈] 接口成功，未响应数据");
            FeedbackResponse result = new FeedbackResponse();
            result.setResult("success");
            return result;
        } catch (Exception e) {
            log.error("调用Dify[消息反馈] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 获取下一轮建议问题列表
     * @param user
     * @param messageId
     * @return
     */
    public SuggestedResponse suggested(String user, String messageId) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        if (StringUtils.isEmpty(user) || StringUtils.isEmpty(messageId)) {
            log.error("用户和消息ID必填");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/messages/", messageId, "/suggested");
        Map<String, String> headers = getHeaders(apiKey);

        Map<String, String> params = new HashMap<>();
        params.put("user", user);

        log.info("调用Dify[获取下一轮建议问题列表] 接口:url:{},headers:{},params:{}", url, headers, StandardObjectMapper.stringify(params));
        try {
            Response response = OkHttpUtils.get(url, params, Headers.of(headers));
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[获取下一轮建议问题列表] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, SuggestedResponse.class);
            }
            log.warn("调用Dify[获取下一轮建议问题列表] 接口成功，未响应数据");
            SuggestedResponse result = new SuggestedResponse();
            result.setResult("success");
            result.setData(new ArrayList<>());
            return result;
        } catch (Exception e) {
            log.error("调用Dify[获取下一轮建议问题列表] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 获取会话历史消息
     * @param user
     * @param conversationId
     * @return HistoryMessages
     */
    public HistoryMessages messages(String user, String conversationId, String limit, String first) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        if (StringUtils.isEmpty(user) || StringUtils.isEmpty(conversationId)) {
            log.error("用户和会话ID必填");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/messages");
        Map<String, String> headers = getHeaders(apiKey);

        Map<String, String> params = new HashMap<>();
        params.put("user", user);
        params.put("conversation_id", conversationId);
        params.put("limit", limit);
        params.put("first_id", first);

        log.info("调用Dify[获取会话历史消息] 接口:url:{},headers:{},params:{}", url, headers, StandardObjectMapper.stringify(params));
        try {
            Response response = OkHttpUtils.get(url, params, Headers.of(headers));
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[获取会话历史消息] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, HistoryMessages.class);
            }
            log.warn("调用Dify[获取会话历史消息] 接口成功，未响应数据");
            return null;
        } catch (Exception e) {
            log.error("调用Dify[获取会话历史消息] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 获取会话列表
     * @param user
     * @return HistoryConversations
     */
    public HistoryConversations conversations(String user, String lastId, String limit, String pinned, String sortBy) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        if (StringUtils.isEmpty(user)) {
            log.error("用户必填");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/conversations");
        Map<String, String> headers = getHeaders(apiKey);

        Map<String, String> params = new HashMap<>();
        params.put("user", user);
        params.put("last_id", lastId);
        params.put("limit", limit);
        params.put("pinned", pinned);
        params.put("sort_by", sortBy);

        log.info("调用Dify[获取会话列表] 接口:url:{},headers:{},params:{}", url, headers, StandardObjectMapper.stringify(params));
        try {
            Response response = OkHttpUtils.get(url, params, Headers.of(headers));
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[获取会话列表] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, HistoryConversations.class);
            }
            log.warn("调用Dify[获取会话列表] 接口成功，未响应数据");
            return null;
        } catch (Exception e) {
            log.error("调用Dify[获取会话列表] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 会话重命名
     * @param conversationId
     * @param renameRequest
     * @return
     */
    public String renameConversations(String conversationId, RenameRequest renameRequest) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        if (StringUtils.isEmpty(conversationId)) {
            log.error("会话ID必填");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/conversations/",conversationId,"/name");
        Map<String, String> headers = getHeaders(apiKey);

        Map<String, String> body = new HashMap<>();
        body.put("user", renameRequest.getUser());
        if (StringUtils.isEmpty(renameRequest.getName())) {
            body.put("auto_generate", "true");
        } else {
            body.put("name", renameRequest.getName());
        }

        String bodyStr = StandardObjectMapper.stringify(body);

        log.info("调用Dify[会话重命名] 接口:url:{},headers:{},body:{}", url, headers, bodyStr);
        try {
            Response response = OkHttpUtils.post(url, Headers.of(headers), null, bodyStr);
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[会话重命名] 接口返回:{}",responseString);
                return responseString;
            }
            log.warn("调用Dify[会话重命名] 接口成功，未响应数据");
            return null;
        } catch (Exception e) {
            log.error("调用Dify[会话重命名] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 删除会话
     * @param user
     * @param conversationId
     * @return
     */
    public String deleteConversations(String user, String conversationId) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        if (StringUtils.isEmpty(conversationId)) {
            log.error("会话ID必填");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/conversations/",conversationId);
        Map<String, String> headers = getHeaders(apiKey);

        Map<String, String> body = new HashMap<>();
        body.put("user", user);
        String bodyStr = StandardObjectMapper.stringify(body);

        log.info("调用Dify[删除会话] 接口:url:{},headers:{},body:{}", url, headers, bodyStr);
        try {
            Response response = OkHttpUtils.delete(url, null, Headers.of(headers), bodyStr);
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[删除会话] 接口返回:{}",responseString);
                return responseString;
            }
            log.warn("调用Dify[删除会话] 接口成功，未响应数据");
            return null;
        } catch (Exception e) {
            log.error("调用Dify[删除会话] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    /**
     * 获取应用Meta信息
     * @return
     */
    public MetaResponse meta() {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }

        String url = UrlUtils.concatSegments(difyUrl, "/meta");
        Map<String, String> headers = getHeaders(apiKey);

        log.info("调用Dify[获取应用Meta信息] 接口:url:{},headers:{}", url, headers);
        try {
            Response response = OkHttpUtils.get(url, null, Headers.of(headers));
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[获取应用Meta信息] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, MetaResponse.class);
            }
            log.warn("调用Dify[获取应用Meta信息] 接口成功，未响应数据");
            return null;
        } catch (Exception e) {
            log.error("调用Dify[获取应用Meta信息] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    public VariablesResponse variables(String conversationId) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }

        String url = UrlUtils.concatSegments(difyUrl, "/conversations/", conversationId, "/variables");
        Map<String, String> headers = getHeaders(apiKey);

        log.info("调用Dify[获取对话变量] 接口:url:{},headers:{}", url, headers);
        try {
            Response response = OkHttpUtils.get(url, null, Headers.of(headers));
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[获取对话变量] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, VariablesResponse.class);
            }
            log.warn("调用Dify[获取对话变量] 接口成功，未响应数据");
            return null;
        } catch (Exception e) {
            log.error("调用Dify[获取对话变量] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    public AudioToTextResponse audioToText(String user, MultipartFile file) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/text-to-audio");

        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + apiKey);

        if (file.isEmpty()) {
            log.error("收到的语音文件为空");
            return null;
        }

        log.info("调用Dify[语音转文字] 接口:url:{},headers:{},file:{}", url, headers, file.getOriginalFilename());
        try {
            OkHttpUtils.FilePart filePart = new OkHttpUtils.FilePart(
                    file.getOriginalFilename(),
                    file.getBytes(),
                    file.getContentType()
            );

            Response response = OkHttpUtils.postMultipart(url, Headers.of(headers), Map.of("user", user), Map.of("file", filePart));
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }

                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用Dify[语音转文字] 接口返回:{}",responseString);
                return StandardObjectMapper.getInstance().readValue(responseString, AudioToTextResponse.class);
            }
            log.warn("调用Dify[语音转文字] 接口成功，未响应数据: {}", StandardObjectMapper.stringify(response));
            return null;
        } catch (Exception e) {
            log.error("调用Dify[语音转文字] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    public byte[] textToAudio(TextToAudioRequest request) {
        if (StringUtils.isEmpty(difyUrl)) {
            log.error("未配置DifyUrl");
            return null;
        }
        String url = UrlUtils.concatSegments(difyUrl, "/text-to-audio");
        Map<String, String> headers = getHeaders(apiKey);

        String bodyStr = null;
        if (request != null) {
            bodyStr = StandardObjectMapper.stringify(request);
        }
        log.info("调用Dify[文字转语音] 接口:url:{},headers:{},body:{}", url, headers, bodyStr);
        try {
            Response response = OkHttpUtils.post(url, Headers.of(headers),null, bodyStr);
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }

                byte[] audioBytes  = OkHttpUtils.getBinaryResponseBody(response).orElse(null);
                log.info("调用Dify[文字转语音] 成功返回音频:{},长度：{}",audioBytes.length);
                return audioBytes;
            }
            log.warn("调用Dify[文字转语音] 接口成功，未响应数据: {}", StandardObjectMapper.stringify(response));
            return null;
        } catch (Exception e) {
            log.error("调用Dify[文字转语音] 接口失败：", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    private Map<String, String> getHeaders(String apiKey) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + apiKey);
        headers.put("Content-Type", "application/json");
        return headers;
    }
}
