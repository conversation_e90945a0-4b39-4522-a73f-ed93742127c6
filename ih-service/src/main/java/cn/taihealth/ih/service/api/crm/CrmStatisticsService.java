package cn.taihealth.ih.service.api.crm;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.crm.CrmPlan;
import cn.taihealth.ih.service.vm.crm.CrmStatisticsPlanVM;
import cn.taihealth.ih.service.vm.crm.CrmStatisticsScoreForChartVM;
import cn.taihealth.ih.service.vm.crm.CrmStatisticsScoreForDiagramVM;
import cn.taihealth.ih.service.vm.crm.CrmStatisticsScoreVM;
import cn.taihealth.ih.service.vm.crm.CrmStatisticsTodayGroupVM;
import cn.taihealth.ih.service.vm.crm.CrmStatisticsTodayVM;
import java.util.Date;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * @Author: Moon
 * @Date: 2021/3/25 上午9:47
 */
public interface CrmStatisticsService {

    /**
     * 随访看板数据
     * @param hospital 医院
     * @param startTime 创建人
     * @param endTime 数据
     * @return vm 随访看板数据
     */
    CrmStatisticsTodayVM getBoardData(Hospital hospital, Date startTime, Date endTime);

    /**
     * 随访看板数据
     * @param hospital 医院
     * @param startTime 创建人
     * @param endTime 数据
     * @return vm 随访看板数据
     */
    List<CrmStatisticsTodayGroupVM> getBoardDataGroup(Hospital hospital, Date startTime, Date endTime);

    CrmStatisticsScoreVM getScoreData(Hospital hospital, CrmPlan plan);

    List<CrmStatisticsScoreForChartVM> getScoreList(Hospital hospital, CrmPlan plan);

    List<CrmStatisticsScoreForDiagramVM> getScoreDiagram(Hospital hospital, CrmPlan plan);

    Page<CrmStatisticsPlanVM> getPlans(Hospital hospital, Pageable page);
}
