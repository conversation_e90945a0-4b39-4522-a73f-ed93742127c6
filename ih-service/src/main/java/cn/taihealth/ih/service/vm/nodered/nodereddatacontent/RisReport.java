package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-09-07
 * 检查报告列表
 */
@Data
public class RisReport implements Serializable {
    @ApiModelProperty("报告单号	Y  如果报告未出，此处传申请单号报告已出，此字段必传")
    private String report_no;
    @ApiModelProperty("就诊流水号	Y	门诊挂号序号、住院号")
    private String regno;
    @ApiModelProperty("就诊类别	Y	1门诊 2住院")
    private String user_source;
    @ApiModelProperty("检查项目名称 Y")
    private String report_item_name;
    @ApiModelProperty("报告类别代码	N")
    private String report_type_code;
    @ApiModelProperty("报告类别名称	N")
    private String report_type_name;
    @ApiModelProperty("检查部位	N")
    private String checkpoint;
    @ApiModelProperty("申请单号	N")
    private String application_no;
    @ApiModelProperty("申请时间	Y")
    private String apply_time;
    @ApiModelProperty("报告发布时间	Y  当报告出具时，报告发布时间必填")
    private String report_time;
    @ApiModelProperty("申请科室名称	N")
    private String apply_dept_name;
    @ApiModelProperty("申请医生名称	N")
    private String apply_doctor_name;
    @ApiModelProperty("审核医生名称	N")
    private String check_doctor_name;
    @ApiModelProperty("执行科室名称	N")
    private String exec_dept_name;
    @ApiModelProperty("执行医生名称	N")
    private String exec_doctor_name;
    @ApiModelProperty("备注	N")
    private String memo;
}
