package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.TIMHistoryFile;

import java.util.Date;

/**
 *
 */

public class TIMHistoryFileDTO extends AbstractEntityDTO {

    private String groupId;
    private String uuid;
    private UploadDTO upload;
    private Date createdDate = new Date();

    public TIMHistoryFileDTO(TIMHistoryFile file) {
        super(file);

        this.groupId = file.getGroupId();
        this.uuid = file.getUuid();
        if (file.getUpload() != null) {
            this.upload = new UploadDTO(file.getUpload());
        }

        this.createdDate = upload.getCreatedDate();
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public UploadDTO getUpload() {
        return upload;
    }

    public void setUpload(UploadDTO upload) {
        this.upload = upload;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }
}
