package cn.taihealth.ih.service.impl.filter.signlog;

import cn.taihealth.ih.domain.UserSignLog;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

/**
 */
public class SignCodeFilter implements SearchFilter<UserSignLog> {

    private final String code;

    public SignCodeFilter(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<UserSignLog> toSpecification() {
        return Specifications.eq("sign.code", code);
    }

    @Override
    public String toExpression() {
        return "sign.code:" + code;
    }

    @Override
    public boolean isValid() {
        return true;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof SignCodeFilter)) {
            return false;
        }

        SignCodeFilter rhs = (SignCodeFilter) other;
        return Objects.equals(code, rhs.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code);
    }

    @Override
    public String toString() {
        return toExpression();
    }
}
