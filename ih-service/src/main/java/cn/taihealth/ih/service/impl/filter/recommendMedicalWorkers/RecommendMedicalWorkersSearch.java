package cn.taihealth.ih.service.impl.filter.recommendMedicalWorkers;

import cn.taihealth.ih.domain.hospital.RecommendMedicalWorker;
import cn.taihealth.ih.service.impl.filter.ContainsFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

public class RecommendMedicalWorkersSearch extends SearchCriteria<RecommendMedicalWorker> {

    public static RecommendMedicalWorkersSearch of(String query) {
        RecommendMedicalWorkersSearch search = new RecommendMedicalWorkersSearch();
        search.parse(query);
        return search;
    }

    @Override
    protected List<String> getQualifiers() {
        return Lists.newArrayList(Qualifier.values())
                .stream()
                .map(q -> q.name().toLowerCase())
                .collect(Collectors.toList());
    }

    @Override
    protected SearchFilter<RecommendMedicalWorker> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        boolean not = input.startsWith("-");
        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }

        switch (qualifier) {
            case DEPTID:
                return new DeptFilter(Long.parseLong(value));
            case NAME:
                return new MedicalWorkersNameFilter(value);
            case TITLE:
                return new MedicalWorkersTitleFilter(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<RecommendMedicalWorker> createContainFilter(Set<String> contains) {
        return new ContainsFilter<>(ImmutableSet.of("medicalWorker.user.username", "medicalWorker.user.fullName", "medicalWorker.user.mobile"), contains);
    }

    public enum Qualifier {
        DEPTID,
        NAME,
        TITLE
    }
}
