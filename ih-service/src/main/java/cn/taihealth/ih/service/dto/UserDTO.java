package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.User.UserType;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import cn.taihealth.ih.repo.cloud.UserRoleRepository;
import cn.taihealth.ih.service.api.HospitalService;
import cn.taihealth.ih.domain.service.LinkService;
import cn.taihealth.ih.service.cache.RoleAuthCache;
import cn.taihealth.ih.spring.security.PlatformRoleCodeName;
import cn.taihealth.ih.spring.security.UserRoleCodeName;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gitq.jedi.context.AppContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 */
@ApiModel
public class UserDTO extends UpdatableDTO {

    // 先去掉加解密，这个dto前端用到的地方实在太多，怕出问题
//    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
//    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String username;

    @ApiModelProperty("必填")
    @NotBlank(message = "手机号必填")
//    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
//    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String mobile;

    @ApiModelProperty("必填，最长128字符")
    @Size(max = 128, message = "姓名不能超过128个字符")
    @NotEmpty(message = "姓名不能为空")
    private String fullName;

    private List<UserRoleCodeName> roles = Lists.newArrayList();
    @ApiModelProperty("平台角色")
    private List<PlatformRoleCodeName> platformRoles = Lists.newArrayList();

    private boolean locked = false;

    @ApiModelProperty("最长20字符")
    @Size(max = 20, message = "身份证的长度不能超过20个字符")
    private String identity;

    @ApiModelProperty("是否已实名认证")
    private boolean identified; // 是否实名认证

    @ApiModelProperty("实名认证时间")
    private Date identifiedDate;

    @ApiModelProperty("生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty("年龄")
    private int age;

    @ApiModelProperty("性别")
    private Gender gender = Gender.UNKNOWN;

    private User.AvatarType avatarType;

    @ApiModelProperty("最长255字符")
    @Size(max = 255, message = "头像地址最长255个字符")
    private String avatarUrl;

    @ApiModelProperty("最长255字符")
    @Size(max = 255, message = "地址最长255个字符")
    private String address;

    @ApiModelProperty("个人能力，最长255字符")
    @Size(max = 255)
    private String capabilities;

    @ApiModelProperty("是否同意服务协议")
    private boolean eulaAgreed;

    @ApiModelProperty("用户类型")
    private UserType userType = UserType.USER;

    @ApiModelProperty("短信验证码，修改手机号时需要提供")
    private String smsCode;

    @ApiModelProperty("用户所属医院，没有时为空")
    private List<HospitalDTO> hospitals;

    @ApiModelProperty("来源")
    private String signupSource;

    public UserDTO() {
    }

    public UserDTO(User user) {
        super(user);

        username = user.getUsername();
        mobile = user.getMobile();
        fullName = user.getFullName();
        locked = user.isLocked();
        address = user.getAddress();
        gender = user.getGender();
        identity = user.getIdentity();
        identified = user.isIdentified();
        identifiedDate = user.getIdentifiedDate();
        avatarType = user.getAvatarType();
        avatarUrl = AppContext.getInstance(LinkService.class).urlOfUserAvatar(user);
        birthday = user.getBirthday();
        capabilities = user.getCapabilities();
        eulaAgreed = user.isEulaAgreed();
        signupSource = user.getSource();
        userType = user.getUserType();
        roles = AppContext.getInstance(RoleAuthCache.class).getRoles(username)
            .stream().filter(u -> !Objects.equals("PUBLIC", u.getCode()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(user.getPlatformRoles())) {
            platformRoles = user.getPlatformRoles()
                .stream().map(u -> new PlatformRoleCodeName(u.getCode(), u.getName()))
                .collect(Collectors.toList());
        }
        hospitals = AppContext.getInstance(HospitalService.class).getByUser(getId());
    }

    public UserDTO(User user, String hospitalCode) {
        super(user);

        username = user.getUsername();
        mobile = user.getMobile();
        fullName = user.getFullName();
        locked = user.isLocked();
        address = user.getAddress();
        gender = user.getGender();
        identity = user.getIdentity();
        identified = user.isIdentified();
        identifiedDate = user.getIdentifiedDate();
        avatarType = user.getAvatarType();
        avatarUrl = AppContext.getInstance(LinkService.class).urlOfUserAvatar(user);
        birthday = user.getBirthday();
        capabilities = user.getCapabilities();
        eulaAgreed = user.isEulaAgreed();
        userType = user.getUserType();
        signupSource = user.getSource();
        if (hospitalCode == null) {
            roles = AppContext.getInstance(RoleAuthCache.class).getRoles(username)
                .stream().filter(u -> !Objects.equals("PUBLIC", u.getCode()))
                .collect(Collectors.toList());
        } else {
            roles = AppContext.getInstance(RoleAuthCache.class)
                .getHospitalRoles(hospitalCode, username);
            AppContext.getInstance(UserRoleRepository.class).findOneByCode("USER").ifPresent(r -> {
                roles.add(new UserRoleCodeName(r.getCode(), r.getName(), r.isHeadNurse(), r.isNurse(), r.isDoctor(),
                                               r.isDoctorAssistant(),
                                               r.isPharmacist(),
                                               r.isOut(), r.isEmergency(), r.isAdmin(), null));
            });
        }
        hospitals = AppContext.getInstance(HospitalService.class).getByUser(getId());
    }

    public User toEntity() {
        User user = new User();
        user.setCapabilities(capabilities);
        user.setUsername(username);
        user.setAvatarUrl(avatarUrl);
        user.setGender(gender);
        user.setAvatarType(avatarType);
        user.setFullName(fullName);
        user.setMobile(mobile);
        user.setIdentity(identity);
        user.setAddress(address);
        user.setId(getId());
        return user;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getCapabilities() {
        return capabilities;
    }

    public void setCapabilities(String capabilities) {
        this.capabilities = capabilities;
    }

    public List<UserRoleCodeName> getRoles() {
        return roles;
    }

    public void setRoles(List<UserRoleCodeName> roles) {
        this.roles = roles;
    }

    public boolean isLocked() {
        return locked;
    }

    public void setLocked(boolean locked) {
        this.locked = locked;
    }

    public boolean isIdentified() {
        return identified;
    }

    public void setIdentified(boolean identified) {
        this.identified = identified;
    }

    public Date getIdentifiedDate() {
        return identifiedDate;
    }

    public void setIdentifiedDate(Date identifiedDate) {
        this.identifiedDate = identifiedDate;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public int getAge() {
        if (birthday == null) {
            return 0;
        }
        return TimeUtils.age(birthday);
    }

    public void setAge(int age) {
        this.age = age;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public User.AvatarType getAvatarType() {
        return avatarType;
    }

    public void setAvatarType(User.AvatarType avatarType) {
        this.avatarType = avatarType;
    }

    public boolean isEulaAgreed() {
        return eulaAgreed;
    }

    public void setEulaAgreed(boolean eulaAgreed) {
        this.eulaAgreed = eulaAgreed;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public List<HospitalDTO> getHospitals() {
        return hospitals;
    }

    public void setHospitals(List<HospitalDTO> hospitals) {
        this.hospitals = hospitals;
    }

    public String getSignupSource() {
        return signupSource;
    }

    public void setSignupSource(String signupSource) {
        this.signupSource = signupSource;
    }

    public List<PlatformRoleCodeName> getPlatformRoles() {
        return platformRoles;
    }

    public void setPlatformRoles(List<PlatformRoleCodeName> platformRoles) {
        this.platformRoles = platformRoles;
    }
}

