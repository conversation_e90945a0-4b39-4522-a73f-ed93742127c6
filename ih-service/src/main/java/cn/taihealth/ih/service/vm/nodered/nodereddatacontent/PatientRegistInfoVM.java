package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.enums.PayStatus;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-09-06 患者挂号记录
 */
@Data
public class PatientRegistInfoVM implements Serializable {

    @ApiModelProperty("挂号序号	Y	门诊就诊流水号")
    private String regno;
    @ApiModelProperty("结算收据号	N	HIS一次结算单据号(标记一次结算的唯一号)")
    private String settle_id;
    @ApiModelProperty("门诊patid	Y	门诊患者唯一号")
    private String patid;
    @ApiModelProperty("发票号	N")
    private String invoice_no;
    @ApiModelProperty("电子收据号	N	仅限有电子发票情况下输出")
    private String elec_invoice_no;
    @ApiModelProperty("病历号	N")
    private String hiscardno;
    @ApiModelProperty("卡号	N")
    private String cardno;
    @ApiModelProperty("预约序号	N")
    private String appointment_id;
    @ApiModelProperty("患者姓名	Y")
    private String patname;
    @ApiModelProperty("性别	Y	男、女、未知")
    private String sex;
    @ApiModelProperty("出生日期	Y")
    private String birth;
    @ApiModelProperty("证件号码	N")
    private String certificate_no;
    @ApiModelProperty("挂号时间	N	格式yyyyMMddHHmmss")
    private String regist_time;
    @ApiModelProperty("就诊日期	N")
    private String visit_time;
    @ApiModelProperty("就诊时间段	N")
    private String visit_time_span;
    @ApiModelProperty("科室代码	Y")
    private String dept_id;
    @ApiModelProperty("科室名称	Y")
    private String dept_name;
    @ApiModelProperty("医生代码	N	科室挂号时为空")
    private String doctor_id;
    @ApiModelProperty("医生名称	N	科室挂号时为空")
    private String doctor_name;
    @ApiModelProperty("挂号号序	N")
    private String source_number;
    @ApiModelProperty("挂号类别	Y	0:普通挂号1:急诊挂号2:专家挂号3:点名专家4:特殊挂号5:义诊6:外宾挂号")
    private String regist_type;
    @ApiModelProperty("记录状态	Y	0:正常，1:退号，2:红冲")
    private String record_status;
    @ApiModelProperty("结算标志	Y	0:未结算，1：结算")
    private String settle_status;
    @ApiModelProperty("就诊标志	N	0未就诊 3已就诊")
    private String visit_status;
    @ApiModelProperty("总金额	Y	含诊疗费、医事服务费等，本次挂号产生的总金额")
    private String total_amount;
    @ApiModelProperty("医保说明	N	医保类型说明")
    private String chargetype_name;
    @ApiModelProperty("医保代码	N	医保类型代码(根据各家医院实际情况反馈)")
    private String chargetype_code;
    @ApiModelProperty("当前操作员标志	N	0 当前渠道操作员 1 其他渠道操作员")
    private String route_flag;
    @ApiModelProperty("就诊地址	N	就诊地址")
    private String doctor_room;
    @ApiModelProperty("备注	N")
    private String memo;
    @ApiModelProperty("院区代码	N	如医院存在多院区共用排班时返回")
    private String organ_code;
    @ApiModelProperty("院区名称	N")
    private String organ_name;
    @ApiModelProperty("电子票据二维码数据	N	仅限当地有电子发票方案的医院")
    private String elec_invoice_qrcode;
    @ApiModelProperty("电子票据URL	N	仅限当地有电子发票方案的医院")
    private String elec_invoice_url;
    @ApiModelProperty("就诊渠道	N	0：线下就诊；1：线上就诊；2：60图文就诊；3：60视频就诊")
    private String channel_type;
    @ApiModelProperty("初复诊标志	N	0：初诊 1：复诊")
    private String visit_flag;
    @ApiModelProperty("主诊断代码	N")
    private String main_diagnose_code;
    @ApiModelProperty("主诊断名称	N")
    private String main_diagnose_name;
    @ApiModelProperty("1 自费订单 0 医保订单")
    private int self_flag = 1;
    @ApiModelProperty("订单状态")
    private OfflineOrder.OutPatientStatus orderStatus;
    @ApiModelProperty("his支付状态")
    private PayStatus hisPayStatus;
    @ApiModelProperty("订单id")
    private Long id;
    @ApiModelProperty("订单类型")
    private ProjectTypeEnum orderType;
    @ApiModelProperty("预约日期")
    private String dutyDate;

    @ApiModelProperty("是否为患服平台订单")
    private boolean ihOrder = false;

    public PatientRegistInfoVM(OfflineOrder order) {
        this.id = order.getId();
        this.orderStatus = order.getStatus();
        this.hisPayStatus = order.getHisPayStatus();
        this.regno = order.getRegNo();
        this.settle_id = order.getSettleId();
        ElectronicMedicCard electronicMedicCard = order.getElectronicMedicCard();
        if (electronicMedicCard != null) {
            Patient patient = electronicMedicCard.getPatient();
            this.patid = patient.getHisPatid();
            this.cardno = electronicMedicCard.getNumber();
            this.patname = patient.getName();
            this.sex = patient.getGender().getShortValue();
            this.birth = TimeUtils.dateToString(patient.getBirthday(), "yyyyMMdd");
            this.certificate_no = patient.getIdCardNum();
            this.hiscardno = patient.getHisPatid();
        }
        String sourceInfo = order.getSourceInfo();
        this.dept_name = order.getDeptName();
        this.doctor_name = order.getDoctorName();
        if (StringUtils.isNotBlank(sourceInfo)) {
            AppointmentInfo appointmentInfo = StandardObjectMapper.readValue(sourceInfo, new TypeReference<>() {});
            this.regist_time = TimeUtils.dateToString(order.getPayTime(), "yyyyMMddHHmmss");
            this.visit_time = appointmentInfo.getDuty_date();
            this.visit_time_span = TimeUtils.createVisitTimeSpan(appointmentInfo.getBegin_time(),
                                                                  appointmentInfo.getEnd_time());
            this.dept_id = appointmentInfo.getDept_id();
            this.doctor_id = appointmentInfo.getDoctor_id();
            if (this.dept_name == null) {
                this.dept_name = appointmentInfo.getDept_name();
            }
            if (this.doctor_name == null) {
                this.doctor_name = appointmentInfo.getDoctor_name();
            }
            this.doctor_room = appointmentInfo.getDoctor_room();
            this.regist_type = getRegistTypeBySchedulingType(appointmentInfo.getScheduling_type());
        }
        this.source_number = order.getSourceNo();
        this.total_amount = String.valueOf(order.getRegistrationFee());
        this.orderType = order.getType();
        this.dutyDate = order.getDutyDate();
        this.appointment_id = order.getAppointmentId();
        this.self_flag = order.getSelfFlag();
        this.ihOrder = true;
    }

    public PatientRegistInfoVM(PatientRegistInfo patientRegistInfo) {
        BeanUtils.copyProperties(patientRegistInfo, this);
    }

    //RegistType "挂号类别	Y	0:普通挂号1:急诊挂号2:专家挂号3:点名专家4:特殊挂号5:义诊6:外宾挂号"
    //scheduling_type出诊类型	Y	0普通 1专家 2特需 3远程门诊 4专病专家 5专病 6整合门诊 7中医膏方门诊 8普通医生门诊 9线上门诊 10敷贴门诊")
    private String getRegistTypeBySchedulingType(String schedulingType) {
        if (StringUtils.isBlank(schedulingType)) {
            return "0";
        }
        switch (schedulingType) {
            case "1":
                return "2";
            case "2":
                return "3";
            case "3":
                return "1";
            case "4":
                return "4";
            case "5":
                return "5";
            case "6":
                return "6";
            case "0":
            default:
                return "0";
        }
    }
}
