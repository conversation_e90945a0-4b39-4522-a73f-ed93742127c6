package cn.taihealth.ih.service.impl.filter.reportapi;

import cn.taihealth.ih.domain.cloud.ReportApi;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class ReportApiCodeFilter implements SearchFilter<ReportApi> {

    private final String name;

    public ReportApiCodeFilter(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<ReportApi> toSpecification() {
        return Specifications.likeIgnoreCase("code", name);
    }

    @Override
    public String toExpression() {
        String str = " reportApiCode:" + name;
        return str;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof ReportApiCodeFilter)) {
            return false;
        }

        ReportApiCodeFilter rhs = (ReportApiCodeFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
