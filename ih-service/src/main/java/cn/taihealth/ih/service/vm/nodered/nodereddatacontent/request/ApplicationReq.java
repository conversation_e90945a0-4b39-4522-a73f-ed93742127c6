package cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang.enums.Enum;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-10-05
 * 自助开单
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class ApplicationReq implements Serializable {
    @ApiModelProperty("预约标识	Y	0不自动预约 1自动预约")
    private String yybz;
    @ApiModelProperty("挂号序号	Y")
    private String regno;
    @ApiModelProperty("患者姓名")
    private String patname;
    @ApiModelProperty("门诊患者patid	Y	门诊患者唯一号")
    private String patid;
    @ApiModelProperty("平台流水号	N	第三方平台流水号，标记唯一一次业务请求，如交易中断，可用于反查(调用：查询外部订单结果)")
    private String serial_no;
    @ApiModelProperty("申请单详细信息  格式详见sqddetails JSON串示例")
    private Sqddetails sqddetails;

    @Getter
    @Setter
    public static class Sqddetails implements Serializable {
        @ApiModelProperty("申请单来源说明	Y	以CIS约定为准，标记来源渠道")
        private String source_name;	
        @ApiModelProperty("申请单类别	Y	1检验 2检查 5治疗")
        private String application_category;	
        @ApiModelProperty("病人类型	Y	1 门诊(目前仅支持门诊) 2 住院 3 体检 4 其它")
        private String brlx;	
        @ApiModelProperty("病人(唯一)标识	Y	门诊时传门诊regno")
        private String brbs;	
        @ApiModelProperty("挂号序号	Y")
        private String regno;	
        @ApiModelProperty("序号类型	N	1.门诊 2.住院")
        private String user_source;	
        @ApiModelProperty("住院婴儿序号	N")
        private String baby_no;	
        @ApiModelProperty("病人标识	Y")
        private String patid;	
        @ApiModelProperty("门诊号	Y	门诊患者传门诊病历号")
        private String mzh;	
        @ApiModelProperty("病历号	N")
        private String hiscardno;	
        @ApiModelProperty("患者姓名	Y")
        private String patname;	
        @ApiModelProperty("科室代码	Y")
        private String dept_id;
        @ApiModelProperty("科室名称	Y")
        private String dept_name;	
        @ApiModelProperty("病区代码	N	住院患者必填")
        private String ward_id;	
        @ApiModelProperty("病区名称	N")
        private String ward_name;	
        @ApiModelProperty("申请单模版代码	Y	需应用方和医院确认线上模板信息")
        private String mbdm;	
        @ApiModelProperty("申请单模版名称	Y")
        private String mbmc;	
        @ApiModelProperty("创建日期	Y	格式yyyyMMddHHmmss")
        private String cjrq;	
        @ApiModelProperty("创建医生代码	Y	医院内科室、医生相关字典")
        private String cre_doctor_id;	
        @ApiModelProperty("创建医生名称	Y")
        private String cre_doctor_name;	
        @ApiModelProperty("创建科室代码	Y")
        private String cre_dept_id;	
        @ApiModelProperty("创建科室名称	Y")
        private String cre_dept_name;
        @ApiModelProperty("执行科室代码	Y")
        private String exec_dept_id;	
        @ApiModelProperty("执行科室名称	Y")
        private String exec_dept_name;	
        @ApiModelProperty("申请单状态	Y	 (1 新增 2 医嘱已审核 3 医生作 废 4 医嘱已执行 5、 6保留 7 LIS全部执行 8 LIS全部作废 9 LIS 部分执行)")
        private String sqdstatus;	
        @ApiModelProperty("收费状态	Y	（1 未收费 2 部分项目已收费 3 全部项目已收费）")
        private String sfstatus;	
        @ApiModelProperty("是否同步	Y	默认0不同步")
        private String sftb;	
        @ApiModelProperty("审核日期，	Y	格式yyyyMMddHHmmss")
        private String check_time;	
        @ApiModelProperty("医生作废日期，	N	格式yyyyMMddHHmmss")
        private String yszfrq;
        @ApiModelProperty("医嘱执行日期，	N	格式yyyyMMddHHmmss")
        private String yzexec_time;	
        @ApiModelProperty("申请单加急标记	Y	0不加急 1加急")
        private String jjbz;	
        @ApiModelProperty("标本代码	N	化验申请单必填")
        private String bbdm;	
        @ApiModelProperty("标本名称	N")
        private String bbmc;	
        @ApiModelProperty("拟检查日期	N")
        private String njcrq;	
        @ApiModelProperty("模板标志	Y	1 模板 0 非模板 默认0")
        private String mbbz;	
        @ApiModelProperty("诊断代码	Y	主要诊断代码")
        private String sydiagnose_code;	
        @ApiModelProperty("诊断名称	Y	主要诊断名称")
        private String sydiagnose_name;	
        @ApiModelProperty("打印标志	Y	默认0")
        private String dybz;
        @ApiModelProperty("申请单明细信息")
        private Brsqdmxk brsqdmxk;
        @ApiModelProperty("申请单附属信息")
        private Brsqdfsxxk brsqdfsxxk;
        @ApiModelProperty("申请单收费信息")
        private Brsqddysfk brsqddysfk;

        @Getter
        @Setter
        public static class Brsqdmxk implements Serializable {
            @ApiModelProperty("是否组套	Y	0否 1是")
            private String statusbz;
            @ApiModelProperty("组套代码	Y	项目所属组套，需应用方自行和医院确认组套信息")
            private String statusdm;
            @ApiModelProperty("组套名称	Y")
            private String statusmc;
            @ApiModelProperty("项目代码	Y	如果选中是组套,则直接保存组 套")
            private String item_code;
            @ApiModelProperty("项目名称	Y")
            private String item_name;
            @ApiModelProperty("项目单价	Y")
            private String price;
            @ApiModelProperty("执行数量	Y")
            private String zxsl;
            @ApiModelProperty("加急标志	Y	0不加急 1加急")
            private String jjbz;
            @ApiModelProperty("记录类别	Y	1 新增 2 LIS已执行 3 LIS作废")
            private String jllb;
            @ApiModelProperty("补充项目标志	Y	是否是 LIS系统直接添加的项目(0否 1是)")
            private String bcmxbz;
            @ApiModelProperty("操作日期	Y	项目执行或作废的时间 格式yyyyMMddHHmmss")
            private String czrq;
            @ApiModelProperty("项目备注	N")
            private String memo;
            @ApiModelProperty("标本代码	N	化验申请单必填")
            private String bbdm;
            @ApiModelProperty("标本名称	N")
            private String bbmc;
        }

        @Getter
        @Setter
        public static class Brsqdfsxxk implements Serializable {
            @ApiModelProperty("类型代码	Y	1临床信息2诊断信息3检查目的4其他")
            private String lbdm;
            @ApiModelProperty("类别名称	Y	临床信息 诊断信息 检查目的 其他")
            private String lbmc;
            @ApiModelProperty("项目代码	Y	1临床信息 2诊断信息 3检查目的 4其他")
            private String item_code;
            @ApiModelProperty("项目名称	Y	A968BBE3-3ED3-4732-AA79-48A57E37887B 检查目的 3CC513BC-3891-46AF-929A-A59ABEEFFCEB 诊断 10 其他 CEF4B9D0-192D-4C37-89BF-B028C605DA2B 临床信息")
            private String item_name;
            @ApiModelProperty("值代码	N	LBDM=2 时必填")
            private String valuedm;
            @ApiModelProperty("值	Y")
            private String value;
            @ApiModelProperty("说明	N")
            private String memo;
        }

        @Getter
        @Setter
        public static class Brsqddysfk implements Serializable {
            @ApiModelProperty("收费项目代码	Y	申请单对应的收费项目需应用方自行和医院确认")
            private String item_code;
            @ApiModelProperty("收费项目名称	Y")
            private String item_name;
            @ApiModelProperty("收费项目单价	Y")
            private String price;
            @ApiModelProperty("收费项目类别	Y	0收费小项目 1临床收费项目")
            private String xmlb;
            @ApiModelProperty("收费项目记录状态	Y	默认1")
            private String xmrecord_status;
            @ApiModelProperty("应收费项目单价	N")
            private String ysfprice;
            @ApiModelProperty("收费序号收费标准	Y	0媒介收费 1正常收费 2打折收费")
            private String sfxh;
        }
    }
}
