package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.MedicalCase;
import cn.taihealth.ih.domain.MedicalCase.CreateType;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ToString(exclude = {"order", "patient"})
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class MedicalCaseDTO extends UpdatableDTO {

    @ApiModelProperty(value = "主诉", required = true)
    @Size(max = 255, message = "主诉最长255个字符")
    @NotBlank(message = "主诉不能为空")
    private String selfSpeak;

    @ApiModelProperty("现病史")
    @Size(max = 255, message = "现病史最长255个字符")
    @NotBlank(message = "现病史不能为空")
    private String nowMedicalHistory;

    @ApiModelProperty("既往史")
    @Size(max = 255, message = " 既往史最长255个字符")
    @NotBlank(message = "既往史不能为空")
    private String oldMedicalHistory;

    @ApiModelProperty("过敏史")
    @Size(max = 255, message = " 过敏史最长255个字符")
    @NotBlank(message = "过敏史不能为空")
    private String allergiesHistory;

    @ApiModelProperty("体格检查")
    @Size(max = 255,message = " 体格检查最长255个字符")
    private String checking;

    @ApiModelProperty("诊断")
    @Size(max = 255, message = " 诊断最长255个字符")
    private String diagnosis;

    @ApiModelProperty(value = "诊断日期",required = true)
    @NotNull(message = "诊断日期不能为空")
    private Date startTime;

    @ApiModelProperty("急诊订单")
    private OrderDTO order;

    @ApiModelProperty("患者")
    private PatientDTO patient;

    @ApiModelProperty("添加类型，个人或者是医生")
    private CreateType createType = CreateType.DOCTOR;

    @ApiModelProperty("是否已发送给患者")
    private boolean sendUser;

    /*病历正文HTML格式，监管平台标准*/
    private String medicalCaseHtml;

    @ApiModelProperty("疾病，医生写病历使用diseaseId，查询时使用diseaseName和diseaseCode")
    private List<MedicalCaseDiseaseDTO> diseases = Lists.newArrayList();

/*    @ApiModelProperty("是否复诊 默认是复诊")
    private boolean returnVisit = true;*/

    @ApiModelProperty("小结")
    private String summary;

    public MedicalCaseDTO(MedicalCase medicalCase, boolean relation) {
        super(medicalCase);
        this.selfSpeak = medicalCase.getSelfSpeak();
        this.nowMedicalHistory = medicalCase.getNowMedicalHistory();
        this.oldMedicalHistory = medicalCase.getOldMedicalHistory();
        this.allergiesHistory = medicalCase.getAllergiesHistory();
        this.checking = medicalCase.getChecking();
        this.diagnosis = medicalCase.getDiagnosis();
        this.startTime = medicalCase.getStartTime();
        this.order = new OrderDTO(medicalCase.getOrder());
        this.patient = new PatientDTO(medicalCase.getPatient(), null);
        this.createType = medicalCase.getCreateType();
        this.sendUser = medicalCase.isSendUser();
        // this.returnVisit = medicalCase.isReturnVisit();
        this.diseases = medicalCase.getDiseases()
            .stream()
            .map(MedicalCaseDiseaseDTO::new)
            .collect(Collectors.toList());
        this.summary = medicalCase.getSummary();
    }

    public MedicalCaseDTO(MedicalCase medicalCase) {
        this(medicalCase, true);
    }

}
