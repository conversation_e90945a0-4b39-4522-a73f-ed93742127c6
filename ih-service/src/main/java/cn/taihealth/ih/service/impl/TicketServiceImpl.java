package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.Ticket;
import cn.taihealth.ih.domain.TicketAttachment;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.dict.SuggestTypeConvert;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.repo.TicketAttachmentRepository;
import cn.taihealth.ih.repo.TicketRepository;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.service.api.MessageService;
import cn.taihealth.ih.service.api.TicketService;
import cn.taihealth.ih.service.dto.TicketDTO;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.SuperviseUtil;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.service.SuperviseFactory;
import cn.taihealth.ih.supervise.service.SuperviseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 *
 */
@Service
@Slf4j
public class TicketServiceImpl implements TicketService {

    private final TicketRepository ticketRepository;
    private final UploadRepository uploadRepository;
    private final TicketAttachmentRepository ticketAttachmentRepository;
    private final MessageService shortMessageService;

    public TicketServiceImpl(TicketRepository ticketRepository,
                             UploadRepository uploadRepository,
                             TicketAttachmentRepository ticketAttachmentRepository,
                             @Qualifier("shortMessageService") MessageService shortMessageService) {
        this.ticketRepository = ticketRepository;
        this.uploadRepository = uploadRepository;
        this.ticketAttachmentRepository = ticketAttachmentRepository;
        this.shortMessageService = shortMessageService;
    }

    @Override
    @Transactional
    public Ticket createTicket(User user, Hospital hospital, TicketDTO dto) {
        Ticket ticket = new Ticket();
        //如果用户勾选不需要回复,回复状态为已回复
        if (dto.getIsNeedResponse() == 0) {
            dto.setResponseType(1);
        }
        ticket.setType(dto.getType());
        ticket.setResponseType(dto.getResponseType());
        ticket.setContent(dto.getContent());
        ticket.setTelephone(dto.getTelephone());
        ticket.setName(dto.getName());
        ticket.setIsNeedResponse(dto.getIsNeedResponse());
        ticket.setUser(user);
        ticket.setHospital(hospital);
        if (CollectionUtils.isEmpty(dto.getSuggestTypes())) {
            ticket.setSuggestTypes(dto.getSuggestTypeCode());
        } else {
            ticket.setSuggestTypes(SuggestTypeConvert.listToDb(dto.getSuggestTypes()));
        }
        ticketRepository.save(ticket);
        for (UploadVM each : dto.getAttachments()) {
            Upload upload = uploadRepository.getById(each.getId());
            if (!Objects.equals(user, upload.getUser())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
            TicketAttachment attachment = new TicketAttachment(ticket, upload);
            ticketAttachmentRepository.save(attachment);
        }
        if (hospital != null) {
            // 只有医院的意见返回才会上报监管平台
            // 上报监管平台
            SuperviseDto superviseDto = SuperviseUtil.getDto(hospital);
            SuperviseService superviseService = SuperviseFactory.getSuperviseService(superviseDto.getSuperviseEnum());
            if (superviseService != null) {
                superviseService.reportComplaint(hospital, ticket.getId(), superviseDto);
            }
        }
        return ticket;
    }

    @Transactional
    @Override
    public void replyComplaint(Hospital hospital, User user, Ticket ticket, String responseContent) {
        ticket.setResponseContent(responseContent);
        ticket.setResponseType(1);
        ticket.setTransactor(user.getFullName());
        ticketRepository.save(ticket);
        // 发送短信给留言用户
        if (StringUtils.isNotBlank(ticket.getTelephone())
                && HospitalSettingsHelper.getBoolean(ticket.getHospital(), HospitalSettingKey.SMS_REPLY_ADVICE)) {
            shortMessageService.replyComplaint(ticket.getHospital(), ticket.getTelephone(), ticket.getUser().getFullName());
        }

        if (hospital != null) {
            // 上报监管平台
            SuperviseDto superviseDto = SuperviseUtil.getDto(hospital);
            SuperviseService superviseService = SuperviseFactory.getSuperviseService(superviseDto.getSuperviseEnum());
            if (superviseService != null) {
                superviseService.reportComplaint(hospital, ticket.getId(), superviseDto);
            }
        }
    }
}
