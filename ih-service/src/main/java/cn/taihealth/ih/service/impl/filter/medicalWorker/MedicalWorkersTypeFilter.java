package cn.taihealth.ih.service.impl.filter.medicalWorker;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.UserRole;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.ListJoin;
import javax.persistence.criteria.Predicate;
import java.util.Objects;

/**
 */
public class MedicalWorkersTypeFilter implements SearchFilter<MedicalWorker> {

    private final String type;

    public MedicalWorkersTypeFilter(String type) {
        this.type = type;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<MedicalWorker> toSpecification() {
        if ("onlineVisit".equalsIgnoreCase(type)) {
            Specification<MedicalWorker> medicalWorkerSpecification = (root, criteriaQuery, criteriaBuilder) -> {
                Join<MedicalWorker, User> user = root.join("user", JoinType.INNER);
                ListJoin<User, UserRole> userRoles = user.joinList("userRoles", JoinType.LEFT);
                return criteriaBuilder.isTrue(userRoles.get("doctor"));
            };
            return Specifications.and(medicalWorkerSpecification, Specifications.isTrue("onlineVisit"));
        }
        if ("doctor".equalsIgnoreCase(type)) {
            Specification<MedicalWorker> medicalWorkerSpecification = (root, criteriaQuery, criteriaBuilder) -> {
                Join<MedicalWorker, User> user = root.join("user", JoinType.INNER);
                ListJoin<User, UserRole> userRoles = user.joinList("userRoles", JoinType.LEFT);
                Predicate doctor = criteriaBuilder.isTrue(userRoles.get("doctor"));
                Predicate admin = criteriaBuilder.isFalse(userRoles.get("admin"));
                return criteriaBuilder.and(doctor, admin);
            };
            return Specifications.and(medicalWorkerSpecification, Specifications.isTrue("enabled"));
        }
        if ("special".equalsIgnoreCase(type)) {
            return Specifications.isTrue("special");
        }
        if ("expert".equalsIgnoreCase(type)) {
            return Specifications.isTrue("expert");
        }
        if ("nursingConsul".equalsIgnoreCase(type)) {
            return Specifications.isTrue("nursingConsul");
        }
        return null;
    }

    @Override
    public String toExpression() {
        return "type:" + type;
    }

    @Override
    public boolean isValid() {
        return type != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof MedicalWorkersTypeFilter)) {
            return false;
        }

        MedicalWorkersTypeFilter rhs = (MedicalWorkersTypeFilter) other;
        return Objects.equals(type, rhs.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type);
    }
}
