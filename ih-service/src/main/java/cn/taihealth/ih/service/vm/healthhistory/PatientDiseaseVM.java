package cn.taihealth.ih.service.vm.healthhistory;

import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Patient.CardType;
import cn.taihealth.ih.domain.crm.CrmPlan;
import cn.taihealth.ih.domain.crm.CrmTask;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 */
public class PatientDiseaseVM extends AbstractEntityDTO {


    @ApiModelProperty("患者姓名必填")
    @Size(min = 1, max = 64, message = "患者姓名长度1-64个字符")
    private String patientName;

    @ApiModelProperty("性别")
    @NotNull(message = "性别必填")
    private Gender gender = Gender.UNKNOWN;

    @NotNull(message = "出生日期")
    @ApiModelProperty("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthDay;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("国籍")
    private String nationality;

    @ApiModelProperty("婚姻状况")
    private String maritalStatus;

    @ApiModelProperty("证件类别")
    private CardType cardType = CardType.ID_CARD;

    @NotBlank(message = "证件号码不能为空")
    @ApiModelProperty("证件号码")
//    @Size(max = 18, message = "证件号码格式错误，目前最高18位")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String idCardNum;

    @ApiModelProperty("身份（职业）")
    private String profession;

    @ApiModelProperty("联系地址")
    private String address;

    @ApiModelProperty("联系电话")
    @Size(max = 11, message = "必须是正确的手机或电话号码！")
    private String mobile;

    @ApiModelProperty("必填")
    private Patient.Relationship relationship;

    @ApiModelProperty("是否是默认就诊人")
    private boolean isDefault;

    @ApiModelProperty("省")
    private String province;

    @ApiModelProperty("市")
    private String city;

    @ApiModelProperty("县")
    private String district;

    @ApiModelProperty("年龄")
    private int age;

    @ApiModelProperty("是否实名认证")
    private boolean identified = false;

    private List<String> diseases = Lists.newArrayList();

    private List<CrmPlanVM> crmPlans = Lists.newArrayList();

    public PatientDiseaseVM() {
    }

    public PatientDiseaseVM(Patient patient) {
        super(patient);
        this.patientName = patient.getName();
        this.mobile = patient.getMobile();
        this.relationship = patient.getRelationship();
        this.isDefault = patient.isDefault();
        this.gender = patient.getGender();
        this.idCardNum = patient.getIdCardNum();
        if (patient.getBirthday() != null) {
            this.birthDay = patient.getBirthday();
            this.age = TimeUtils.age(patient.getBirthday());
        }
        this.address = patient.getAddress();
        this.province = patient.getProvince();
        this.city = patient.getCity();
        this.district = patient.getDistrict();
        this.identified = patient.isIdentified();
        this.nation = patient.getNation();
        this.nationality = patient.getNationality();
        this.maritalStatus = patient.getMaritalStatus();
        this.cardType = patient.getCardType();
        this.profession = patient.getProfession();


    }

    public static class CrmPlanVM extends AbstractEntityDTO {
        @ApiModelProperty("随访任务ID")
        private Long taskId;
        @ApiModelProperty("计划名")
        private String name;
        @ApiModelProperty("计划描述")
        private String description;

        public CrmPlanVM() {
        }

        public CrmPlanVM(CrmTask task, CrmPlan plan) {
            super(plan);
            this.taskId = task.getId();
            this.name = plan.getName();
            this.description = plan.getDescription();
        }

        public Long getTaskId() {
            return taskId;
        }

        public void setTaskId(Long taskId) {
            this.taskId = taskId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public CardType getCardType() {
        return cardType;
    }

    public void setCardType(CardType cardType) {
        this.cardType = cardType;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Patient.Relationship getRelationship() {
        return relationship;
    }

    public void setRelationship(Patient.Relationship relationship) {
        this.relationship = relationship;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public String getIdCardNum() {
        return idCardNum;
    }

    public void setIdCardNum(String idCardNum) {
        this.idCardNum = idCardNum;
    }

    public Date getBirthDay() {
        return birthDay;
    }

    public void setBirthDay(Date birthDay) {
        this.birthDay = birthDay;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public boolean isIdentified() {
        return identified;
    }

    public void setIdentified(boolean identified) {
        this.identified = identified;
    }

    public List<String> getDiseases() {
        return diseases;
    }

    public void setDiseases(List<String> diseases) {
        this.diseases = diseases;
    }

    public List<CrmPlanVM> getCrmPlans() {
        return crmPlans;
    }

    public void setCrmPlans(List<CrmPlanVM> crmPlans) {
        this.crmPlans = crmPlans;
    }
}
