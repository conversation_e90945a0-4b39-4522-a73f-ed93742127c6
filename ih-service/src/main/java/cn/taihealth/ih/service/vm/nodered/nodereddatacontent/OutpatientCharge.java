package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-09-29
 * 门诊患者缴费记录
 */
@Data
public class OutpatientCharge implements Serializable {
    @ApiModelProperty("结算收据号	Y	HIS一次结算单据号(标记一次结算的唯一号)")
    private String settle_id;
    @ApiModelProperty("就诊渠道	Y	0线下就诊 1线上就诊 -1全部")
    private String channel_type;
    @ApiModelProperty("门诊就诊流水号	Y	门诊挂号序号")
    private String regno;
    @ApiModelProperty("发票号	N")
    private String invoice_no;
    @ApiModelProperty("电子收据号	N	仅限有电子发票情况下输出")
    private String elec_invoice_no;
    @ApiModelProperty("退收据号	N")
    private String refund_settle_id;
    @ApiModelProperty("收费类型	Y	0正常 1退费 2红冲")
    private String record_status;
    @ApiModelProperty("记录类型名称	Y	0挂号1收费")
    private String record_flag;
    @ApiModelProperty("处方、项目名称	Y	缴费处方和项目名称，多个可根据逗号合并使用")
    private String item_name;
    @ApiModelProperty("结算时间	Y")
    private String end_time;
    @ApiModelProperty("总金额	Y")
    private String total_amount;
    @ApiModelProperty("优惠金额	Y")
    private String discount_amount;
    @ApiModelProperty("个人现金支付金额	Y	个人支付的现金(包括现金、银联卡、微信支付、支付宝支付等)")
    private String cash_pay;
    @ApiModelProperty("医保统筹支付金额	N	如果医保支付时此字段必填")
    private String pub_pay;
    @ApiModelProperty("医保个人账户字符金额	N")
    private String pub_account_pay;
    @ApiModelProperty("科室代码	Y")
    private String dept_id;
    @ApiModelProperty("科室名称	Y")
    private String dept_name;
    @ApiModelProperty("开方医生名称	Y")
    private String recipe_doctor_name;
    @ApiModelProperty("发药窗口集合	N")
    private String drug_window;
    @ApiModelProperty("发药药房集合	N")
    private String drug_dept;
    @ApiModelProperty("欠费补交标记	N	0正常缴费 1欠费 2补缴 3补缴退费")
    private String charge_flag;
    @ApiModelProperty("HIS订单号	N	补缴订单号")
    private String out_trade_no;
    @ApiModelProperty("电子票据二维码数据	N	仅限当地有电子发票方案的医院")
    private String elec_invoice_qrcode;
    @ApiModelProperty("电子票据URL	N	仅限当地有电子发票方案的医院")
    private String elec_invoice_url;
    @ApiModelProperty("院区代码	N")
    private String organ_code;
    @ApiModelProperty("院区名称	N")
    private String organ_name;


    // TODO 前端需要的字段，不是二期接口文档中的字段，后期逻辑接上后，需要赋值
    @ApiModelProperty("科室位置")
    private String deptAddress;
    @ApiModelProperty("缴费日期")
    private String chargeDate;
    @ApiModelProperty("缴费状态")
    private OrderStatus chargeStatus;

    public enum OrderStatus {
        PAID, // 已支付
        REFUND; // 已退款
    }

}
