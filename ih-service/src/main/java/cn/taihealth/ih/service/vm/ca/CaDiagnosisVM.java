package cn.taihealth.ih.service.vm.ca;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CaDiagnosisVM {

    @ApiModelProperty("签章是否使用ca")
    private boolean signatureUseCa;

    @ApiModelProperty("签章pdf信息")
    private SignaturePdfVM signaturePdf;

    @ApiModelProperty("ca签名页面地址")
    private String signUrl;

    public CaDiagnosisVM() {
    }

    public CaDiagnosisVM(boolean signatureUseCa) {
        this.signatureUseCa = signatureUseCa;
    }

    public CaDiagnosisVM(SignaturePdfVM signaturePdf) {
        this.signatureUseCa = true;
        this.signaturePdf = signaturePdf;
        this.signUrl = signaturePdf.getSignUrl();
    }

}
