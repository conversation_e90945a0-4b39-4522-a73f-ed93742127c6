package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.domain.enums.UserSettingKey;
import cn.taihealth.ih.realname.SM4Utils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gitq.jedi.common.datatype.DataType;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.datatype.EnumType;
import com.gitq.jedi.common.datatype.ListType;
import io.swagger.annotations.ApiModelProperty;

/**
 */

public class UserSettingsVM {

    @ApiModelProperty("数据项，参数名称就是key本身的值 如：ROTATE_PICTURES")
    private UserSettingKey key;

    @ApiModelProperty("参数描述")
    private String name;

    @ApiModelProperty("数据类型")
    private DataType dataType;

    @ApiModelProperty("值")
    private Object value;

    @ApiModelProperty("枚举配置值")
    private Enum[] enumConstants = new Enum[]{};

    public UserSettingsVM() {
    }

    public UserSettingsVM(UserSettingKey key) {
        this.key = key;
        this.dataType = key.getDataType();
        this.value = this.dataType.typeCast(key.getDefaultValue());
        this.name = key.getName();
        if (key.getDataType() == DataTypes.PASSWORD && value instanceof String) {
            try {
                value = SM4Utils.encrypt_CBC_Padding(Constants.SM3_BASE_KEY, (String) value);
            } catch (Exception ignored) {
            }
        }
        if (key.getEnumConstants() != null && key.getEnumConstants().length > 0) {
            enumConstants = key.getEnumConstants();
        } else {
            if (this.dataType instanceof EnumType) {
                enumConstants = ((EnumType) key.getDataType()).getEnumClass().getEnumConstants();
            }
            if (this.dataType instanceof ListType
                && ((ListType) this.dataType).getItemType() instanceof EnumType) {
                enumConstants = ((EnumType) ((ListType) this.dataType).getItemType()).getEnumClass().getEnumConstants();
            }
        }
    }

    public void setDataType(Object dataType) {
        // do nothing
    }

    public void setEnumConstants(Object dataType) {
        // do nothing
    }

    public UserSettingKey getKey() {
        return key;
    }

    public void setKey(UserSettingKey key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public DataType getDataType() {
        return dataType;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public Enum[] getEnumConstants() {
        return enumConstants;
    }

    @JsonIgnore
    public void setKeyValue(Object value) {
        if (key.getDataType() == DataTypes.PASSWORD && value instanceof String) {
            try {
                this.value = SM4Utils.encrypt_CBC_Padding(Constants.SM3_BASE_KEY, (String) value);
            } catch (Exception ignored) {
            }
        } else {
            this.value = value;
        }
    }

}
