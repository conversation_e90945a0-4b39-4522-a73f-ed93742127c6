package cn.taihealth.ih.service.mq;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.wanda.WandaPayOrder;
import cn.taihealth.ih.domain.cloud.wanda.WandaPayOrderRefund;
import cn.taihealth.ih.mq.consts.CommonConst;
import cn.taihealth.ih.mq.entity.message.AliPayRefundSuccessMsg;
import cn.taihealth.ih.mq.enums.MessageTypeEnum;
import cn.taihealth.ih.mq.provider.MessageProvider;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.wanda.WandaPayOrderRepository;
import cn.taihealth.ih.service.api.alipay.AliPayBusinessService;
import cn.taihealth.ih.service.api.wanda.WandaPayBusinessService;
import cn.taihealth.ih.service.dto.hisrequest.PatientMessageDTO;
import cn.taihealth.ih.service.dto.mq.ConfimRefundDTO;
import cn.taihealth.ih.service.dto.mq.RefundRetryDTO;
import cn.taihealth.ih.service.impl.HisMessageHandler;
import cn.taihealth.ih.service.impl.PayManager;
import cn.taihealth.ih.service.impl.order.BusinessOrderManager;
import cn.taihealth.ih.service.vm.wanda.WandaQueryServiceResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MqHandler {

    @Async
    public void handleDelayMessage(JSONObject payload) {
        JSONObject msgHeader = payload.getJSONObject(CommonConst.MSG_HEADER_KEY);
        String messageType = msgHeader.getString(CommonConst.MSG_HEADER_MSG_TYPE_KEY);
        Hospital hospital;
        switch (messageType){
            case "MSG_4003":
                log.info("Receiver [MSG 4003] message: {}", payload);
                RefundRetryDTO data = payload.getJSONObject(CommonConst.MSG_BODY_KEY).toJavaObject(RefundRetryDTO.class);
                hospital = AppContext.getInstance(HospitalRepository.class).getById(data.getHospitalId());
                PatientMessageDTO message = data.getMessage();
                AppContext.getInstance(HisMessageHandler.class).handle(hospital, message);
                break;
            case "CONFIRM_REFUND":
                log.info("Receiver [CONFIRM_REFUND] message: {}", payload);
                ConfimRefundDTO confirmRefundDTO = payload.getJSONObject(CommonConst.MSG_BODY_KEY).toJavaObject(ConfimRefundDTO.class);
                AppContext.getInstance(PayManager.class).sendRefundResult(confirmRefundDTO);
                break;
            case "ALIPAY_REFUND_SUCCESS":
                log.info("Receiver [AliPayRefundSuccess] message: {}", payload);
                AliPayRefundSuccessMsg msg = payload.getJSONObject(CommonConst.MSG_BODY_KEY)
                        .toJavaObject(AliPayRefundSuccessMsg.class);
                hospital = AppContext.getInstance(HospitalRepository.class).getById(msg.getHospitalId());
                AppContext.getInstance(AliPayBusinessService.class).refundOrder(msg.getRefundNo(), msg.getRefundTime(), hospital);
                break;
            case "QUERY_REFUND_STATUS":
                log.info("Receiver [QUERY_REFUND_STATUS] message: {}", payload);
                WandaPayOrderRefund refund =
                    payload.getJSONObject(CommonConst.MSG_BODY_KEY).toJavaObject(WandaPayOrderRefund.class);
                Optional<WandaPayOrder> oneByOrderNo =
                    AppContext.getInstance(WandaPayOrderRepository.class).findOneByOrderNo(refund.getOrderNo());
                if (oneByOrderNo.isEmpty()) {
                    log.error("未找到对应的万达订单信息，退款订单id：{}", refund.getId());
                    return;
                }
                WandaPayOrder order = oneByOrderNo.get();
                boolean isRefund = AppContext.getInstance(BusinessOrderManager.class).checkOrderRefund(order.getOrderType(), order.getProductId());
                if (!isRefund) {
                    hospital = AppContext.getInstance(HospitalRepository.class).getById(order.getHospitalId());
                    WandaPayBusinessService wandaPayBusinessService = AppContext.getInstance(WandaPayBusinessService.class);
                    WandaQueryServiceResponse response = wandaPayBusinessService.getRefundResult(hospital, refund);
                    if (response != null && "1".equalsIgnoreCase(response.getStatus())) {
                        log.info("验证后已退款成功的订单，直接执行退款成功回调逻辑，订单id：{}，result：{}", order.getId(),
                                 StandardObjectMapper.stringify(response));
                        wandaPayBusinessService.refundOrder(refund.getRefundNo(), TimeUtils.convert(response.getFinishDate() + response.getFinishTime()), hospital);
                        return;
                    } else {
                        log.info("退款请求已发送-放入MQ，5s后查询退款状态。Params: {}", JSON.toJSONString(refund));
                        AppContext.getInstance(MessageProvider.class).sendDelay5sMessage(refund, MessageTypeEnum.QUERY_REFUND_STATUS);
                    }

                }
                break;
            default:
                log.info("未知类型的延迟消息");
        }
    }

}
