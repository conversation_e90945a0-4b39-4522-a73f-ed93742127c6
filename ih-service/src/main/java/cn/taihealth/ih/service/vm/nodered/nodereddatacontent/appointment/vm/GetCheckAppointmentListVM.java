package cn.taihealth.ih.service.vm.nodered.nodereddatacontent.appointment.vm;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.service.dto.ExamItemDTO;
import cn.taihealth.ih.service.dto.PatientDTO;
import cn.taihealth.ih.service.vm.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetCheckAppointmentListVM implements Serializable {
    // 申请单id
    private String application_id;
    // 预约id
    private String appointment_id;
    // 预约号
    private String appointment_no;
    // 患者姓名
    private String pat_name;
    // 患者性别
    private int gender; // 男、女、未知
    // 年龄
    private Integer age;
    // 证件号码
    private String certificate_no;
    // 就诊卡号码
    private String card_no;
    // 就诊卡类型
    private String card_type; // [0]自费卡 [1]医保卡 [2]社保卡 [3]身份证
    // 患者唯一号
    private String pat_id;
    // 患者手机号
    private String mobile;
    // 取消时间
    private String cancel_time; // 状态为已取消时必填
    // 渠道编码
    private String channel_code;
    // 预约状态
    private String appointment_status; // 参考2.2.7预约状态常量字典
    // 创建预约的渠道编码
    private String appointment_channel_code;
    // 创建预约渠道名称
    private String appointment_channel_name;
    // 预约创建人工号
    private String appointment_doctor_code;
    // 预约创建人姓名
    private String appointment_doctor_name;
    // 预约的检查科室代码
    private String appointment_dept_code;
    // 预约的检查科室名字
    private String appointment_dept_name;
    // 排班id
    private String scheduling_id;
    // 检查设备id
    private String appointment_check_device_id;
    // 检查设备名字
    private String appointment_check_device_name;
    // 检查项目代码
    private String appointment_check_code;
    // 检查项目名称
    private String appointment_check_name;
    // 检查类别代码
    private String appointment_category_code;
    // 检查类别名称
    private String appointment_category_name;
    // 预约记录备注
    private String appointment_notes;
    // 时间类型
    private String time_type;
    // 0上午 07:00:00-12:00:00
    // 1下午 12:00:00-17:00:00
    // 2晚上 17:00:00-23:59:59
    // 3全天 00:00:00-23:59:59
    // 4白天 07:00:00-17:00:00
    // 5后夜 00:00:00-07:00:00
    // 6夜间 17:00:00-08:00:00
    // 时间类型名称
    private String time_type_name; // 如上午、下午、全天或者按照小时的时间段，具体根据医院所有不同
    // 开始时间
    private String begin_time; // 排班区间的开始时间，单位yyyyMMddHHmmss
    // 结束时间
    private String end_time; // 排班区间的结束时间，单位yyyyMMddHHmmss
    // 类别代码
    private String category_code; // HIS中检查类别唯一代码
    // 类别名称
    private String category_name;
    private String attention;
    private String report_url = "";

    public GetCheckAppointmentListVM(ExamOrderVM vm) {
        ChecksVM check = vm.getCheck();
        NumberSourceVM numberSource = vm.getNumberSource();
        PatientDTO patient = check.getPatient();
        ExamScheduleVM service = vm.getService();
        ExamItemDTO examItem = vm.getExamItem();
        ExamDeviceVM examDevice = vm.getExamDevice();
        if (check != null) {
            this.application_id = check.getApplyNo();
            this.card_no = check.getCardNumber();
            this.card_type = check.getNumberType();
            if (check.getCancelTime() != null) {
                this.cancel_time = TimeUtils.dateToString(check.getCancelTime(), "yyyyMMdd");
            }
            this.appointment_notes = check.getNotes();
            this.certificate_no = check.getCertificateNo();
        }

        this.appointment_id = vm.getId() + "";
        this.appointment_no = vm.getAccessCode();

        this.pat_name = patient.getPatientName();
        this.gender = patient.getGender() == Gender.MALE ? 1 : 0;
        this.age = patient.getAge();
        this.pat_id = patient.getHisPatid();
        this.mobile = patient.getMobile();
        if (numberSource != null) {
            this.channel_code = numberSource.getCode();
            this.appointment_channel_code = numberSource.getCode();
            this.appointment_channel_name = numberSource.getName();
        }
        this.appointment_status = vm.getStatus().name();
        if (service != null) {
            if (service.getExamDevice() != null) {
                this.appointment_check_device_id = service.getExamDevice().getCode();
                this.appointment_check_device_name = service.getExamDevice().getName();
            }
            this.scheduling_id = service.getId() + "";
            if (service.getStartTime() != null) {
                this.begin_time = TimeUtils.dateToString(service.getStartTime(), "yyyyMMddHHmmss") ;
            }
            if (service.getEndTime() != null) {
                this.end_time = TimeUtils.dateToString(service.getEndTime(), "yyyyMMddHHmmss") ;
            }
            if (service.getShift() != null) {
                this.time_type = service.getShift().getName();
            }
            this.time_type_name = service.getNumberSourceStatus();
        }
        if (examDevice != null && examDevice.getOfflineDept() != null) {
            this.appointment_dept_code = examDevice.getOfflineDept().getDeptCode();
            this.appointment_dept_name = examDevice.getOfflineDept().getDeptName();
        }
        if (examItem != null) {
            this.appointment_check_code = examItem.getCode();
            this.appointment_check_name = examItem.getName();
            if (examItem.getCategory() != null) {
                this.appointment_category_code = examItem.getCategory().getCode();
                this.appointment_category_name = examItem.getCategory().getName();
            }
            this.attention = examItem.getNotice();
        }
    }
}
