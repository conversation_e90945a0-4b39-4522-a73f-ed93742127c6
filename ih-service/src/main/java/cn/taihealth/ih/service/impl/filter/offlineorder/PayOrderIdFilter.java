package cn.taihealth.ih.service.impl.filter.offlineorder;

import cn.taihealth.ih.domain.cloud.WechatInsuranceOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.*;

public class PayOrderIdFilter implements SearchFilter<OfflineOrder> {

    private final String value;

    public PayOrderIdFilter(String value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OfflineOrder> toSpecification() {
        return new Specification<OfflineOrder>() {
            @Override
            public Predicate toPredicate(Root<OfflineOrder> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
                // 创建 WechatInsuranceOrder 子查询
                Subquery<Long> wechatInsuranceOrderSubquery = query.subquery(Long.class);
                Root<WechatInsuranceOrder> wechatInsuranceOrderRoot = wechatInsuranceOrderSubquery.from(WechatInsuranceOrder.class);
                wechatInsuranceOrderSubquery.select(wechatInsuranceOrderRoot.get("billNo"))
                        .where(criteriaBuilder.equal(wechatInsuranceOrderRoot.get("payOrderId"), value));

                // 创建 AliPayOrder 子查询
                Subquery<Long> aliPayOrderSubquery = query.subquery(Long.class);
                Root<AliPayOrder> aliPayOrderRoot = aliPayOrderSubquery.from(AliPayOrder.class);
                aliPayOrderSubquery.select(aliPayOrderRoot.get("body"))
                        .where(criteriaBuilder.equal(aliPayOrderRoot.get("payOrderId"), value));

                // 使用两个子查询结果构建 OR 条件
                Predicate wechatOrderPredicate = criteriaBuilder.in(root.get("id")).value(wechatInsuranceOrderSubquery);
                Predicate aliPayOrderPredicate = criteriaBuilder.in(root.get("id")).value(aliPayOrderSubquery);

                // 将两个条件组合为 OR
                return criteriaBuilder.or(wechatOrderPredicate, aliPayOrderPredicate);
            }
        };
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
