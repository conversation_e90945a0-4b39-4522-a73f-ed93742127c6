package cn.taihealth.ih.service.dto.historyrecord;

import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.PrescriptionType;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.Status;
import cn.taihealth.ih.service.dto.PatientDTO;
import cn.taihealth.ih.service.dto.MedicalCaseDiseaseDTO;
import cn.taihealth.ih.service.dto.MedicalWorkerDTO;
import cn.taihealth.ih.service.dto.OrderDTO;
import cn.taihealth.ih.service.dto.PrescriptionDTO;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import cn.taihealth.ih.service.dto.UploadDTO;
import cn.taihealth.ih.service.dto.UserDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 处方表DTO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class PrescriptionOrdersHistoryDTO extends UpdatableDTO {

    @ApiModelProperty("急诊订单")
    private OrderDTO order;

    @ApiModelProperty("患者")
    private PatientDTO patient;

    @ApiModelProperty("医生")
    private MedicalWorkerDTO doctor;

    @ApiModelProperty("发起人")
    private UserDTO user;

    @ApiModelProperty("处方审核状态")
    private PrescriptionOrder.Status status = Status.UNREVIEWED;

    @ApiModelProperty("文件")
    private UploadDTO Upload;

    @ApiModelProperty("拒绝原因，最长255字符")
    private String rejectReason;

    @ApiModelProperty("是否发送给用户 0:false,1:true")
    private boolean sendUser;

    @ApiModelProperty("处方单类型")
    private PrescriptionOrder.PrescriptionType type = PrescriptionType.UNIVERSAL;

    @ApiModelProperty("医生提交处方审核时间")
    private Date commitedDate;

    @ApiModelProperty("审核人(药师)")
    private MedicalWorkerDTO doctorReview;

    @ApiModelProperty("审核时间")
    private Date reviewTime;

    @ApiModelProperty("关联药品")
    @Valid
    private List<PrescriptionDTO> prescriptionDTO = Lists.newArrayList();

    @ApiModelProperty("发送处方通知给用户的时间")
    private Date sendUserDate;

    @ApiModelProperty("处方是否过期，true已过期，false未过期")
    private boolean expired;

    @ApiModelProperty(name = "是否逻辑删除，0删除，1未删")
    private boolean enabled;

    //封装属性，诊断
    private String diagnosis;

    //疾病，医生写病历使用
    private List<MedicalCaseDiseaseDTO> diseases = Lists.newArrayList();

    @ApiModelProperty("记录操作日志ID")
    private Long operationsId;

    @ApiModelProperty("记录操作日志 增加orderId单独封装")
    private Long orderId;

    List<PrescriptionOrdersHistoryDTO> init(String prescriptionStr) {
        return StandardObjectMapper
            .readValue(prescriptionStr, new TypeReference<List<PrescriptionOrdersHistoryDTO>>() {
            });
    }

    @JsonIgnore
    public PrescriptionOrdersHistoryDTO toHistory(PrescriptionOrder prescriptionOrder) {
        this.setId(prescriptionOrder.getId());
        this.setCreatedDate(prescriptionOrder.getCreatedDate());
        this.setUpdatedDate(prescriptionOrder.getUpdatedDate());


        this.rejectReason = prescriptionOrder.getRejectReason();
        if (prescriptionOrder.getUpload() != null) {
            this.Upload = new UploadDTO(prescriptionOrder.getUpload());
        }
        this.status = prescriptionOrder.getStatus();
        this.rejectReason = prescriptionOrder.getRejectReason();
        this.sendUser = prescriptionOrder.isSendUser();
        this.type = prescriptionOrder.getType();
        this.reviewTime = prescriptionOrder.getReviewTime();
        this.commitedDate = prescriptionOrder.getCommitedDate();
        this.prescriptionDTO = prescriptionOrder.getPrescription().stream()
            .map(PrescriptionDTO::new)
            .peek(u -> {
                u.setUser(null);
            }).collect(Collectors.toList());
        this.sendUserDate = prescriptionOrder.getSendUserDate();
        this.enabled = prescriptionOrder.isEnabled();
        return this;
    }

}
