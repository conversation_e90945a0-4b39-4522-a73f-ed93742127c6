package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MedicalWorkerManager {

    private final MedicalWorkerRepository medicalWorkerRepository;

    public MedicalWorkerManager(MedicalWorkerRepository medicalWorkerRepository) {
        this.medicalWorkerRepository = medicalWorkerRepository;
    }

    @Cacheable(key = "#hospitalId + '.' + #userId", value = "ih.medicalWorkerEnabled.medicalWorker")
    public List<Object[]> findAllEnableByHospitalAndUser(long hospitalId, long userId) {
        return medicalWorkerRepository.findAllEnableByHospitalAndUser(hospitalId, userId);
    }

    @CacheEvict(key = "#medicalWorker.hospital.id + '.' + #medicalWorker.user.id", value = "ih.medicalWorkerEnabled.medicalWorker")
    public MedicalWorker save(MedicalWorker medicalWorker) {
        return medicalWorkerRepository.save(medicalWorker);
    }

    @CacheEvict(key = "#medicalWorker.hospital.id + '.' + #medicalWorker.user.id", value = "ih.medicalWorkerEnabled.medicalWorker")
    public void delete(MedicalWorker medicalWorker) {
        medicalWorkerRepository.delete(medicalWorker);
    }
}
