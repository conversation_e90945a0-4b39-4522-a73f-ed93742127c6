package cn.taihealth.ih.service.vm.nursing;

import cn.taihealth.ih.domain.nursing.NursingHomeChargeItem;
import cn.taihealth.ih.service.dto.nursing.NursingChargeItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@NoArgsConstructor
@Getter
@Setter
public class NursingHomeChargeItemVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("护理费用项目")
    private NursingChargeItemDTO chargeItem;

    @ApiModelProperty("是否默认")
    @NotNull(message = "是否默认")
    private boolean defaulted;

    public NursingHomeChargeItemVM(NursingHomeChargeItem entity) {
        if (entity.getChargeItem() != null) {
            this.chargeItem = new NursingChargeItemDTO(entity.getChargeItem());
        }
        this.defaulted = entity.isDefaulted();
    }

}
