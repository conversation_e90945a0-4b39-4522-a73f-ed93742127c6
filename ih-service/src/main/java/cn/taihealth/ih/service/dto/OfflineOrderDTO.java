package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.enums.PayStatus;
import cn.taihealth.ih.domain.enums.PaymentMethod;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.service.dto.hospital.offline.OfflineMedicalWorkerDTO;
import cn.taihealth.ih.service.vm.PatientVM;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.AppointmentInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class OfflineOrderDTO extends UpdatableDTO {

    @ApiModelProperty("就诊卡")
    private ElectronicMedicCardDTO electronicMedicCard;

    @ApiModelProperty("医生")
    private OfflineMedicalWorkerDTO doctor;

    @ApiModelProperty("就诊人")
    private PatientVM patient;

    @ApiModelProperty("挂号费用(总金额 单位: 分)")
    private int registrationFee;

    @ApiModelProperty("挂号费用(自费金额 单位: 分)")
    private int selfFee;

    @ApiModelProperty("医保支付金额，统筹+个人医保")
    private int insuranceFee;

    @ApiModelProperty("医保支付金额，统筹")
    private int pubFee;

    @ApiModelProperty("医保支付金额，个人")
    private int pubAccountFee;

    @ApiModelProperty("排班ID")
    private String scheduleId;

    @ApiModelProperty("排班开始时间")
    private String beginTime;

    @ApiModelProperty("门诊序号")
    private String regNo;

    @ApiModelProperty("排班序号")
    private String sourceNo;

    @ApiModelProperty("科室名称")
    private String deptName;

    @ApiModelProperty("订单状态  WAIT_PAY, // 待支付\n"
        + "        PENDING, // 支付中\n"
        + "        CANCELLED, // 已取消\n"
        + "        CLOSED, // 已关闭\n"
        + "        REGISTERED, // 待取号\n"
        + "        REFUNDING, // 退款中\n"
        + "        REFUND, // 已退款\n"
        + "        COMPLETED, // 已完成")
    private OfflineOrder.OutPatientStatus status;

    @ApiModelProperty("his支付状态")
    private PayStatus hisPayStatus;

    @ApiModelProperty("订单类型")
    private ProjectTypeEnum type;

    @ApiModelProperty("号源信息")
    private AppointmentInfo sourceInfo;

    @ApiModelProperty("付款完成时间")
    private Date payTime;

    @ApiModelProperty("退款完成时间")
    private Date refundTime;

    @ApiModelProperty("付款流水号")
    private String payTransactionId;

    @ApiModelProperty("退款流水号")
    private String refundTransactionId;

    @ApiModelProperty("0根据病人医保代码结算,1自费结算")
    private int selfFlag = 1;

    @ApiModelProperty("his接口结算是否成功")
    private Boolean isSettlementSuccessful;

    @ApiModelProperty("支付方式")
    private PaymentMethod paymentMethod;

    @ApiModelProperty("是否为患服平台订单")
    private boolean ihOrder = true;

    public OfflineOrderDTO(OfflineOrder offlineOrder) {
        super(offlineOrder);
        this.registrationFee = offlineOrder.getRegistrationFee();
        this.regNo = offlineOrder.getRegNo();
        this.scheduleId = offlineOrder.getScheduleId();
        this.electronicMedicCard = new ElectronicMedicCardDTO(offlineOrder.getElectronicMedicCard());
        if (offlineOrder.getDoctor() != null) {
            this.doctor = new OfflineMedicalWorkerDTO(offlineOrder.getDoctor());
        }
        this.status = offlineOrder.getStatus();
        this.deptName = offlineOrder.getDeptName();
        this.type = offlineOrder.getType();
        this.beginTime = offlineOrder.getBeginTime();
        if (offlineOrder.getElectronicMedicCard() != null) {
            this.patient = new PatientVM(offlineOrder.getElectronicMedicCard().getPatient(),
                    offlineOrder.getElectronicMedicCard().getOnlineType()) ;
        }
        if (offlineOrder.getSourceInfo() != null) {
            this.sourceInfo = StandardObjectMapper.readValue(offlineOrder.getSourceInfo(), new TypeReference<>() {});
            if (StringUtils.isBlank(sourceInfo.getDept_name())) {
                sourceInfo.setDept_name(offlineOrder.getDeptName());
                sourceInfo.setDept_id(offlineOrder.getHisDeptId());
            }
        }
        this.payTime = offlineOrder.getPayTime();
        this.refundTime = offlineOrder.getRefundTime();
        this.payTransactionId = offlineOrder.getPayTransactionId();
        this.refundTransactionId = offlineOrder.getRefundTransactionId();
        this.insuranceFee = offlineOrder.getInsuranceFee();
        this.pubFee = offlineOrder.getPubFee();
        this.pubAccountFee = offlineOrder.getPubAccountFee();
        this.selfFee = offlineOrder.getSelfAmount();
        this.selfFlag = offlineOrder.getSelfFlag();
        this.sourceNo = offlineOrder.getSourceNo();
        this.isSettlementSuccessful = offlineOrder.getSettlementSuccessful();
        this.paymentMethod = offlineOrder.getPaymentMethod();
        this.hisPayStatus = offlineOrder.getHisPayStatus();
    }
}
