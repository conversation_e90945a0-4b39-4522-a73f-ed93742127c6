package cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request;

import cn.taihealth.ih.domain.enums.PaymentMethod;
import cn.taihealth.ih.service.dto.ClinicRecordDTO;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.InpatientRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class InpatientHospitalPreChargeReq {
    @ApiModelProperty("就诊人id")
    private Long patientId;

    @ApiModelProperty("住院号id")
    @NotBlank
    private String reg_no;

    @ApiModelProperty("患者在his的patientid")
    private String his_patid;

    @ApiModelProperty("患者在his的patientname")
    private String his_patname;

    @ApiModelProperty("预交金额: 单位 分")
    @Min(value = 0, message = "预交金额必须大于0")
    private Integer totalAmount;

    @ApiModelProperty("充值方式    WECHAT(\"微信支付\", \"1\"),\n"
        + "    OFFLINE(\"线下支付\", \"-1\"),\n"
        + "    CASH(\"现金\", \"0\"),\n"
        + "    ALI_PAY(\"支付宝\", \"3\");，不传默认为微信支付")
    private PaymentMethod paymentMethod = PaymentMethod.WECHAT;

    @ApiModelProperty("住院就诊信息")
    private InpatientRecord inpatientRecord;

}