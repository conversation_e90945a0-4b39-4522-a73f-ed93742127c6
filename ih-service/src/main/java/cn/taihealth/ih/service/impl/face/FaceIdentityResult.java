package cn.taihealth.ih.service.impl.face;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: Moon
 * @Date: 2020/10/21 10:41 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class FaceIdentityResult {

    @JsonProperty
    private Integer errno;
    @JsonProperty
    private String err_msg;
    @JsonProperty
    private String request_id;
    @JsonProperty
    private float confidence;
    @JsonProperty
    private float[] thresholds;
    @JsonProperty
    private Integer[] rectA;
    @JsonProperty
    private Integer[] rectB;
}
