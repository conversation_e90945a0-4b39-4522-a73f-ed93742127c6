package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import lombok.Data;

import java.io.Serializable;

/**
 * 检查设备
 */
@Data
public class CheckDevices implements Serializable {
    // 设备代码	Y	设备唯一代码或者编号
    private String device_id;
    // 设备名称	Y
    private String device_name;
    // 科室代码	Y	科室唯一代码
    private String dept_id;
    // 科室名称	Y
    private String dept_name;
    // 放置地点	N	设备放置地点
    private String device_site;
    // 类别代码	Y	检查类别唯一代码
    private String category_code;
    // 类别名称	Y
    private String category_name;
    // 项目费用	Y
    private double price;
    // 注意事项	N
    private String notes;
}
