package cn.taihealth.ih.service.impl.filter.recommendDept;

import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.RecommendDept;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;


public class RecommendDeptSearch extends SearchCriteria<RecommendDept> {

    public static RecommendDeptSearch of(String query) {
        RecommendDeptSearch deptSearch = new RecommendDeptSearch();
        deptSearch.parse(query);
        return deptSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<RecommendDept> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier
                .valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case DEPTNAME:
                return new DeptNameFilter(value);
            case DEPTTYPE:
                Dept.DeptType deptEnum = Dept.DeptType.valueOf(value.toUpperCase());
                return new DeptTypeFilter(deptEnum);
            case CANREGISTER:
                return new GenericFilter<>("dept.canRegister", Operator.eq, StringUtil.stringToBoolean(value));
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<RecommendDept> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        DEPTNAME,
        CANREGISTER,
        DEPTTYPE
    }
}
