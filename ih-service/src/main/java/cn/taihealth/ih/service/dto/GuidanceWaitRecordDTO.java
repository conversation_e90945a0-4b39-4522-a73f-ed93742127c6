package cn.taihealth.ih.service.dto;

import io.swagger.annotations.ApiParam;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2020/9/27
 */
@Data
public class GuidanceWaitRecordDTO {
  private Long id;

  @ApiParam("会话id, 开始的时候不填")
  Long chatId = -1L;

  @ApiParam("订单Id")
  @NotNull
  Long orderId = -1L;

  private GuidanceWaitQuestionDTO guidanceWaitQuestionDTO;

  @ApiParam("客户回答, 开始的时候不填")
  private String answer;

  @ApiParam("该问答的序号，　前端不需要填写，供前端排序用")
  private Integer sequence;
}
