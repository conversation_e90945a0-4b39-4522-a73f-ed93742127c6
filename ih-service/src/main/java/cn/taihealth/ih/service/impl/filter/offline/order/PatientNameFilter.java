package cn.taihealth.ih.service.impl.filter.offline.order;


import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

public class PatientNameFilter implements SearchFilter<OfflineOrder> {
    private final String name;

    public PatientNameFilter(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OfflineOrder> toSpecification() {
        return Specifications.likeIgnoreCase("electronicMedicCard.patient.name", name);
    }

    @Override
    public String toExpression() {
        return " name:" + name;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof PatientNameFilter)) {
            return false;
        }

        PatientNameFilter rhs = (PatientNameFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
