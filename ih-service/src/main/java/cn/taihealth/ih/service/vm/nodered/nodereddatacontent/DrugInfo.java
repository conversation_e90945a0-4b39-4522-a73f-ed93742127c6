package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import lombok.Data;

@Data
public class DrugInfo {

    // 药品名称	Y
    private String drug_name;
    // 药品唯一号	Y	医院内药品唯一码(drug_id)
    private String drug_id;
    // 药品代码	Y
    private String drug_code;
    // 医保代码	Y	药品对应的医保代码
    private String insurance_code;
    // 医保名称	Y	药品对应的医保名称
    private String insurance_name;
    // 单价	Y
    private String price;
    // 可报单价	Y
    private String expense_price;
    // 不可报单价	Y
    private String non_expense_price;
    // 有效期	Y
    private String period_of_validity;
    // 药品类别名称	Y	西药、中成药、中草药
    private String item_category;
    // 剂量单位	Y
    private String dosage_unit;
    // 剂型代码	Y
    private String dosage_form;
    // 门诊系数	Y
    private String clinic_pack_factor;
    // 药库系数	Y
    private String store_pack_factor;
    // 药品规格	Y
    private String drug_spec;
    // 生产厂家	N
    private String manufacturer;
    // 拼音码	N
    private String inputcode;
    // 药品商品名	N
    private String trade_name;
    // 药品通用名	N
    private String common_name;
    // 门诊发票项目代码	Y
    private String clinic_inovice_item_code;
    // 门诊发票项目名称	Y
    private String clinic_inovice_item_name;
    // 住院发票项目代码	Y
    private String ward_inovice_item_code;
    // 住院发票项目名称	Y
    private String ward_inovice_item_name;
    // 库存状态	Y	0无库存，其他为具体库存数量
    private String stock_quantity;
    // 记录状态	Y	0正常 1停用
    private String record_status;
    // 门诊单位	Y
    private String clinic_unit;
    // 医保审批标志	N	针对需要医保控费的药品 1 报销 2 自费
    private String insurance_approval_flag;
    // 互联网药品标志	N	1.线下药品 2.互联网医院
    private String internet_hospital_flag;
}

