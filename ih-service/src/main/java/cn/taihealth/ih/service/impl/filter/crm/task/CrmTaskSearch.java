package cn.taihealth.ih.service.impl.filter.crm.task;

import cn.taihealth.ih.domain.crm.CrmTask;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class CrmTaskSearch extends SearchCriteria<CrmTask> {

    public static CrmTaskSearch of(String query) {
        CrmTaskSearch articleSearch = new CrmTaskSearch();
        articleSearch.parse(query);
        return articleSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<CrmTask> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }

        switch (qualifier) {
            case PLAN_ID:
                return new GenericFilter<>("plan.id", Operator.eq, value);
            case NAME_PHONE_ID_PLAN:
                return new MultiArgsFilter(value);
            case STATUS:
                return new StatusFilter(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<CrmTask> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        /**
         * 计划ID
         */
        PLAN_ID,
        /**
         * 计划名
         */
        NAME_PHONE_ID_PLAN,
        /**
         * 状态
         */
        STATUS
    }
}

