package cn.taihealth.ih.service.impl.filter.UserPlatformInfo;

import cn.taihealth.ih.domain.hospital.UserPlatformInfo;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 */
public class NameFilter implements SearchFilter<UserPlatformInfo> {

    private final String name;

    public NameFilter(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<UserPlatformInfo> toSpecification() {
        return new Specification<UserPlatformInfo>() {
            @Override
            public Predicate toPredicate(Root<UserPlatformInfo> root, CriteriaQuery<?> criteriaQuery,
                                         CriteriaBuilder criteriaBuilder) {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.like(root.get("user").get("mobile"), name));
                list.add(criteriaBuilder.like(root.get("user").get("username"), name));
                list.add(criteriaBuilder.like(root.get("user").get("fullName"), name));
                Predicate predicate = criteriaBuilder.or(list.toArray(new Predicate[list.size()]));
                return criteriaQuery.where(predicate).getRestriction();
            }
        };
    }

    @Override
    public String toExpression() {
        String str = " name:" + name;
        return str;
//        return not ? "-" + str : str;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof NameFilter)) {
            return false;
        }

        NameFilter rhs = (NameFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
