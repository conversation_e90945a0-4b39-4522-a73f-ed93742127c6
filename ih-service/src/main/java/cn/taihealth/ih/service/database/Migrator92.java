package cn.taihealth.ih.service.database;


import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.enums.ThirdOrderType;
import cn.taihealth.ih.domain.his.HisChargeRecord;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.repo.WechatOrderRepository;
import cn.taihealth.ih.repo.his.HisChargeRecordRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class Migrator92 {

    /**
     * 添加HisChargeRecord的transactionId字段
     */
    public void run() {
        log.info("修正HisChargeRecord的transactionId开始");
        HisChargeRecordRepository hisChargeRecordRepository = AppContext.getInstance(HisChargeRecordRepository.class);
        HisOutpatientChargeRepository hisOutpatientChargeRepository = AppContext.getInstance(HisOutpatientChargeRepository.class);
        PrescriptionOrderRepository prescriptionOrderRepository = AppContext.getInstance(PrescriptionOrderRepository.class);
        WechatOrderRepository wechatOrderRepository = AppContext.getInstance(WechatOrderRepository.class);
        Pageable pageable = PageRequest.of(0, 1000, Sort.by("id"));
        Page<HisChargeRecord> records = hisChargeRecordRepository.findAll(pageable);
        while (true) {
            records.forEach(u -> {
                if (u.getTransactionId() == null) {
                    String transactionId;
                    List<WechatOrder> wechatOrders;
                    switch (u.getProductTableName()) {
                        case "ih_his_outpatient_hospital_charge":
                            HisOutpatientCharge charge = hisOutpatientChargeRepository.getById(u.getProductId());
                            wechatOrders = wechatOrderRepository.findAllByProductId(charge.getHisOutpatientChargeGroup().getId() + "");
                            break;
                        case "ih_offline_orders":
                        case "ih_his_inpatient_hospital_charge":
                            wechatOrders = wechatOrderRepository.findAllByProductId(u.getProductId() + "");
                            break;
                        case "ih_orders":
                            wechatOrders = wechatOrderRepository.findAllByWechatOrderTypeAndProductId(ThirdOrderType.REGISTER,
                                                                                                      u.getProductId() + "");
                            break;
                        case "ih_prescription_orders":
                            PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(u.getProductId());
                            wechatOrders =
                                wechatOrderRepository.findAllByWechatOrderTypeAndProductId(ThirdOrderType.RECIPE, prescriptionOrder.getOrder().getId() + "");
                            break;
                        default:
                            wechatOrders = Lists.newArrayList();
                            break;
                    }

                    transactionId = wechatOrders.stream().filter(WechatOrder::getPayed).map(WechatOrder::getTransactionId).findFirst().orElse(null);
                    if (transactionId == null) {
                        log.info("已支付的transactionId不存在, hospitalId=" + u.getHospitalId() + ", settleId=" + u.getSettleId());
                    }
                    if (transactionId != null) {
                        u.setTransactionId(transactionId);
                        hisChargeRecordRepository.save(u);
                    }
                }
            });

            if (records.hasNext()) {
                pageable = pageable.next();
                records = hisChargeRecordRepository.findAll(pageable);
            } else {
                break;
            }
        }

        log.info("修正HisChargeRecord的transactionId结束");
    }
}
