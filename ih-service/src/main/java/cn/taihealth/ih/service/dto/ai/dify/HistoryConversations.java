package cn.taihealth.ih.service.dto.ai.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class HistoryConversations {

    private int limit;

    @JsonProperty("has_more")
    private boolean hasMore;

    private List<DataItem> data;

    @Data
    public static class DataItem {
        private String id;

        private String name;

        private Map<String, Object> inputs = new HashMap<>(1);

        private String status;

        @JsonProperty("created_at")
        private long createdAt;
    }
}
