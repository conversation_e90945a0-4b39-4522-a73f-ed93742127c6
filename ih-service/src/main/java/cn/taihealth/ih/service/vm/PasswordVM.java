package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 */
@ApiModel
public class PasswordVM {

    @NotNull
    @ApiModelProperty("必填")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String mobile;

    @NotNull
    @ApiModelProperty("必填，长度为6字符")
    @Size(min = 6, max = 6, message = "验证码不正确")
    private String smsCode;

    @NotNull
//    @ApiModelProperty("必填，最短6字符，最长100字符, 英文或数字开头，只能包含英文数字和特殊字符")
//    @Pattern(regexp = Constants.USER_PASSWORD_REGE, message = "密码必须包含英文、数字、和特殊字符")
//    @Size(min = Constants.USER_PASSWORD_MIN_LENGTH, max = Constants.USER_PASSWORD_MAX_LENGTH,
//        message = "密码的长度为8-20个字符")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String newPassword;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }
}
