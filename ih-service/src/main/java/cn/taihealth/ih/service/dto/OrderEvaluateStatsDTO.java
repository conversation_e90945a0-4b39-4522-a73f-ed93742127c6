package cn.taihealth.ih.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * @Author: hyp
 * @Date: 2023/07/13 1:35 下午
 */
@Data
@Accessors(chain = true)
public class OrderEvaluateStatsDTO {

    @ApiModelProperty("评论次数")
    private Integer evaluateTimes;

    @ApiModelProperty("总得分")
    private Integer ratingTotal;

    @ApiModelProperty("平均得分")
    private BigDecimal avgRating;

    @ApiModelProperty("好评次数")
    private Integer favorTimes;

    @ApiModelProperty("好评率")
    private Integer favorRate;

    public OrderEvaluateStatsDTO() {
        this.evaluateTimes = 0;
        this.ratingTotal = 0;
        this.avgRating = BigDecimal.ZERO;
        this.favorTimes = 0;
        this.favorRate = 0;
    }

    public void setRatingTotal(Integer ratingTotal) {
        if (Objects.isNull(ratingTotal) || Objects.isNull(evaluateTimes) || evaluateTimes.compareTo(1) < 0) {
            return;
        }
        this.ratingTotal = ratingTotal;
        this.avgRating = BigDecimal.valueOf(ratingTotal).divide(BigDecimal.valueOf(this.evaluateTimes), 1, RoundingMode.HALF_UP);
    }

    public void setFavorTimes(Integer favorTimes) {
        if (Objects.isNull(favorTimes) || Objects.isNull(evaluateTimes) || evaluateTimes.compareTo(1) < 0) {
            return;
        }
        this.favorTimes = favorTimes;
        this.favorRate = favorTimes * 100 / evaluateTimes;
    }

}
