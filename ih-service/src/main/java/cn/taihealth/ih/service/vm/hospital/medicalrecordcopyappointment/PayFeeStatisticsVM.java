package cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Author: jzs
 * @Date: 2023-10-23
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class PayFeeStatisticsVM {

    @ApiModelProperty("营收日期 yyyy-mm")
    private String revenueDate;

    @ApiModelProperty("每月营收总额(今年) 分")
    private long currentMonthlyTotalFee;

    @ApiModelProperty("每月营收总额(去年) 分")
    private long lastMonthlyTotalFee;

}
