package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.ElecInvoice;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.ElecInvoiceRepository;
import cn.taihealth.ih.service.api.UserElecInvoiceService;
import cn.taihealth.ih.service.vm.ElecInvoiceVM;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class UserElecInvoiceServiceImpl implements UserElecInvoiceService {

    private static final Logger log = LoggerFactory.getLogger(UserElecInvoiceServiceImpl.class);

    private ElecInvoiceRepository elecInvoiceRepository;
    private PatientRepository patientRepository;

    private ApplicationEventPublisher eventPublisher;

    public UserElecInvoiceServiceImpl(ElecInvoiceRepository elecInvoiceRepository,
                                      PatientRepository patientRepository,
                                      ApplicationEventPublisher eventPublisher) {
        this.elecInvoiceRepository = elecInvoiceRepository;
        this.patientRepository = patientRepository;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public Page<ElecInvoiceVM> listElecInvoice(User user, String query, Pageable pageRequest) {
        Specification<ElecInvoice> userStatus = Specifications.eq("user", user);
        return elecInvoiceRepository.findAll(userStatus, pageRequest).map(ElecInvoiceVM::new);
    }

    @Override
    public List<ElecInvoiceVM> listElecInvoice(User user ,Long id) {
        Patient patient = patientRepository.getById(id);
        Specification<ElecInvoice> userStatus = Specifications.eq("patient", patient);
        return elecInvoiceRepository.findAll(userStatus).stream().map(ElecInvoiceVM::new)
            .collect(Collectors.toList());
    }

    @Override
    public ElecInvoiceVM getInvoiceDetail(long id) {
        return new ElecInvoiceVM(elecInvoiceRepository.getById(id));
    }

    @Override
    public ElecInvoice saveElecInvoice(User user, Order order) {
        Optional<ElecInvoice> elec = elecInvoiceRepository.findByOrder(order);
        if (elec.isPresent()) {
            throw ErrorType.ELEC_INVOICE_ORDER_ERROR.toProblem();
        }
        ElecInvoice elecInvoice = new ElecInvoice();
        elecInvoice.setOpenDate(new Date());
        elecInvoice.setOrder(order);
        elecInvoice.setOrderType(order.getOrderType());
        elecInvoice.setAmount(order.getRegistrationFee());
        elecInvoice.setHospital(order.getHospital());
        elecInvoice.setUser(user);
        long number = System.currentTimeMillis();
        elecInvoice.setCode((number + "").substring(1,13));
        elecInvoice.setPatient(order.getPatient());
        elecInvoice.setNumber((number + 123 + "").substring(5,13));
        elecInvoiceRepository.save(elecInvoice);
        return elecInvoice;
    }

}
