package cn.taihealth.ih.service.impl.message;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.DoctorNoticeTime;
import cn.taihealth.ih.domain.ElecInvoice;
import cn.taihealth.ih.domain.SMSCode.CodeType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.alipay.AliPayBusinessService;
import cn.taihealth.ih.service.api.wechat.WechatService;
import cn.taihealth.ih.service.dto.drugorder.OrderInfoSmsDTO;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.AppointmentInfo;
import cn.taihealth.ih.wechat.service.vm.alipay.AliPayTemplatedData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 支付宝小程序消息
 */
@Service("alipayMiniMessageService")
@Transactional
@AllArgsConstructor
public class AlipayMiniMessageServiceImpl implements MessageService {

    private static final Logger log = LoggerFactory.getLogger(AlipayMiniMessageServiceImpl.class);

    private static final int DEFAULT_TOKEN_LENGTH = 6;

    private final UserService userService;
    private final SystemSettingService systemSettingService;
    private final OrderService orderService;
    private final HospitalSettingService hospitalSettingService;
    private final PatientRepository patientRepository;
    private final WechatService wechatService;
    private final AliPayBusinessService aliPayBusinessService;
    private final UserCacheFindService userCacheFindService;

    @Override
    public String sendSignupToken(Hospital hospital, String mobile) {
        return null;
    }

    @Override
    public String sendResetPasswordToken(Hospital hospital, String mobile) {
        return null;
    }

    @Override
    public String sendResetMobile(Hospital hospital, String mobile) {
        return null;
    }

    @Override
    public String sendLoginToken(Hospital hospital, String mobile) {
        return null;
    }

    @Override
    public String sendWechatMessage(Hospital hospital, String mobile) {
        return null;
    }

    @Override
    public String sendSignupMessage(Hospital hospital, String mobile, CodeType codeType) {
        return null;
    }

    @Override
    public String sendAddPatientToken(Hospital hospital, String mobile, User user) {
        return null;
    }

    @Override
    public void sendForEnableToAppoint(Hospital hospital, String mobile, Checks checks) {
    }

    @Override
    public void sendBillPaySuccess(String mobile, Order order, ElecInvoice elecInvoice) {
    }

    @Override
    public void sendDoctorAccumulateExcessiveConsultation(DoctorNoticeTime doctorNoticeTime, String size, String i, String waitTime, String overTime) {
    }

    @Override
    public void replyComplaint(Hospital hospital, String telephone, String userName) {
    }

    @Override
    public void sendByKey(Hospital hospital, String mobile, Object data, HospitalSettingKey key) {
    }

    private void sendDrugOrderConfirmed(Hospital hospital, String mobile, OrderInfoSmsDTO drugOrder) {
    }

    private void sendDrugOrderPending(Hospital hospital, String mobile, OrderInfoSmsDTO drugOrder) {
    }

    private void sendDrugOrderCanceled(Hospital hospital, String mobile, OrderInfoSmsDTO drugOrder) {
    }

    private void sendDrugOrderClosed(Hospital hospital, String mobile, OrderInfoSmsDTO drugOrder) {
    }

    private void sendDrugOrderReceived(Hospital hospital, String mobile, OrderInfoSmsDTO drugOrder) {
    }

    private void sendDrugOrderDelivered(Hospital hospital, String mobile, OrderInfoSmsDTO drugOrder) {
    }

    private void sendOrderRefundToCompletedRefunded(Hospital hospital, String mobile, Order order) {
    }

    private void sendOrderRefundToStartedRefunded(Hospital hospital, String mobile, Order order) {
    }

    private void sendDoctorInitiateVideoConsultation(Hospital hospital, String mobile, Order order) {
    }


    private void sendDoctorHangsUpVideo(Hospital hospital, String mobile, Order order) {
    }

    private void sendCallForVisitToMessageUnRead(Hospital hospital, String mobile, Order order) {
    }

    private void sendCallForConsultToMessageUnRead(Hospital hospital, String mobile, Order order) {
    }

    private void sendOrderCanceledToDoctor(Hospital hospital, String mobile, Order order) {
    }

    private void sendOrderCanceledToNurse(Hospital hospital, String mobile, Order order) {
    }

    private void sendRefundToTimeOutOnTimeConfirmed(Hospital hospital, String mobile, Order order) {
    }

    private void sendToAppointmentCheck(Hospital hospital, String mobile, ExamOrder order) {
    }

    private void sendAppointmentCheckSuccess(Hospital hospital, String mobile, ExamOrder order) {
    }

    private void sendAppointmentCheckCancel(Hospital hospital, String mobile, ExamOrder order) {
    }

    private void sendAppointmentCheckChange(Hospital hospital, String mobile, ExamOrder order) {
    }

    private void sendReportCreated(Hospital hospital, String mobile, ExamOrderReport report) {
    }

    private void sendSignSuccess(Hospital hospital, String mobile, ExamOrder order) {
    }

    private void sendEnableToAppointment(Hospital hospital, String mobile, Checks checks) {
    }

    private void sendVisitRemind(Hospital hospital, String mobile, Order order) {
    }

    private void sendVisitStart(Hospital hospital, String mobile, Order order) {
    }

    private void sendConsultVisitStart(Hospital hospital, String mobile, Order order) {
    }

    private void sendEvaluate(Hospital hospital, String mobile, Order order) {
    }

    private void sendOrderClosed(Hospital hospital, String mobile, Order order) {
    }

    private void sendOrderCanceled(Hospital hospital, String mobile, Order order) {
    }

    private void sendOrderPending(Hospital hospital, String mobile, Order order) {
    }

    private void sendOrderConfirmed(Hospital hospital, String mobile, Order order) {
    }

    @Override
    public void sendPrescriptionReady(Hospital hospital, String mobile, PrescriptionOrder prescriptionOrder, Order order) {
    }

    private void sendWaitTreatStart(Hospital hospital, String mobile, Order order) {
    }

    private void sendReWaitTreatStart(Hospital hospital, String mobile, Order order) {
    }

    @Override
    public void nursingOrderPaidNotice(Hospital hospital, List<User> users, Order order, OrderNursingExt ext) {

    }

    @Override
    public void nursingOrdersTaskAssignedNotice(Hospital hospital, List<User> users, Order order, OrderNursingExt ext) {

    }

    @Override
    public void sendSystemAlert(Hospital hospital, String telephone) {

    }

    @Override
    @Async("sendMessageExecutor")
    public void sendVisitRemind(Hospital hospital, User user, OfflineOrder offlineOrder) {
        String deptName = StringUtils.isBlank(offlineOrder.getDeptName()) ? "-" : offlineOrder.getDeptName();
        // 2024年10月10日09:40:53 就诊提醒要求加上科室地址 就诊序号等信息
        AppointmentInfo info = offlineOrder.getSourceInfo() == null ? null :
                StandardObjectMapper.readValue(offlineOrder.getSourceInfo(), new TypeReference<>() {});
        if (info != null) {
            deptName = deptName + "-" + info.getDoctor_room();
            deptName = StringUtils.substring(deptName, 0, 20);
        }
        String desc = DataTypes.DATE.asString(TimeUtils.convert(offlineOrder.getBeginTime()), "MM月dd日");
        desc = desc + " " + DataTypes.DATE.asString(TimeUtils.convert(offlineOrder.getBeginTime()), "HH:mm");
        desc = desc + "-" + offlineOrder.getSourceNo() + "号";
        String patientId = offlineOrder.getPatientId();
        Patient patient = patientRepository.getById(Long.valueOf(patientId));

        // 支付宝消息对象
        AliPayTemplatedData aliPayTemplatedData = new AliPayTemplatedData();
        aliPayTemplatedData.setUrl("subpackages/pat/register-order-detail/index?orderId=" + offlineOrder.getId() + "&pid=" + patient.getId());
        aliPayTemplatedData.addKeywords(patient.getName());
        aliPayTemplatedData.addKeywords(hospital.getName());
        aliPayTemplatedData.addKeywords(desc);
        aliPayTemplatedData.addKeywords(deptName);

        String appId = wechatService.getAppId(hospital, PlatformTypeEnum.ALI_PAY_MINI, HospitalPublicPlatform.PlatformForEnum.PATIENT);
        if (appId == null) {
            log.info("支付宝小程序appId为null，发送支付宝小程序消息失败。");
            return;
        }
        aliPayBusinessService.manageData(aliPayTemplatedData, HospitalSettingKey.NOTICE_OUT_REGISTER_VISIT, appId, hospital);
        aliPayBusinessService.sendTemplateMessage(hospital, user, aliPayTemplatedData);
    }

    @Override
    public void sendSmartFollowUp(Hospital hospital, String mobile) {

    }

    @Override
    @Async("sendMessageExecutor")
    public void outVisitEvaluateNotice(Hospital hospital, User user, OfflineOrder offlineOrder) {
        String patientId = offlineOrder.getPatientId();
        Patient patient = patientRepository.getById(Long.valueOf(patientId));
        // 支付宝消息参数
        AliPayTemplatedData aliPayTemplatedData = new AliPayTemplatedData();
        aliPayTemplatedData.setUrl("subpackages/pat/evaluation-my-detail/index?orderId=" + offlineOrder.getId() + "&isOffline=true");
        // 就诊人
        aliPayTemplatedData.addKeywords(patient.getName());
        // 就诊医生
        aliPayTemplatedData.addKeywords(offlineOrder.getDoctorName());
        // 就诊时间
        aliPayTemplatedData.addKeywords(TimeUtils.appointmentTimeSlotSpan(offlineOrder.getAppointmentTimeSlot()));
        // 备注
        aliPayTemplatedData.addKeywords("邀请您对此次就诊做出评价！");

        String appId = wechatService.getAppId(hospital, PlatformTypeEnum.ALI_PAY_MINI, HospitalPublicPlatform.PlatformForEnum.PATIENT);
        if (appId == null) {
            log.info("支付宝小程序appId为null，发送支付宝小程序消息失败。");
            return;
        }
        aliPayBusinessService.manageData(aliPayTemplatedData, HospitalSettingKey.NOTICE_OUT_REGISTER_EVALUATE, appId, hospital);
        aliPayBusinessService.sendTemplateMessage(hospital, user, aliPayTemplatedData);
    }

}
