package cn.taihealth.ih.service.database;

import cn.taihealth.ih.domain.UserMedicalWorkerRel;
import cn.taihealth.ih.domain.UserMedicalWorkerRel.FocusSource;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.repo.UserMedicalWorkerRelRepository;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;


/**
 * 用户与医生绑定关系老数据处理（一般绑定关系及CKD绑定关系）
 */
@Component
public class Migrator37 {

    public void run() {
        UserMedicalWorkerRelRepository instance = AppContext.getInstance(UserMedicalWorkerRelRepository.class);
        List<UserMedicalWorkerRel> list = instance.findAll();
        list.forEach(elem -> {
            elem.setCkd(false);
        });
        instance.saveAll(list);
        instance.flush();

        //找出所有扫码关注的数据，根据user分组
        Specification<UserMedicalWorkerRel> spec = Specifications.and(Specifications.eq("source", FocusSource.QRCODE));
        List<UserMedicalWorkerRel> allList = instance.findAll(spec);
        Map<User, List<UserMedicalWorkerRel>> map = allList.stream()
            .collect(Collectors.groupingBy(UserMedicalWorkerRel::getUser));

        List<UserMedicalWorkerRel> ckdList = new ArrayList<>();
        for (
            Entry<User, List<UserMedicalWorkerRel>> entry : map.entrySet()) {
            List<UserMedicalWorkerRel> collect = entry.getValue().stream()
                .sorted(Comparator.comparing(UserMedicalWorkerRel::getCreatedDate)).collect(
                    Collectors.toList());
            ckdList.add(collect.get(0));
        }
        ckdList.forEach(elem -> elem.setCkd(true));
        instance.saveAll(ckdList);
    }


}
