package cn.taihealth.ih.service.util;

import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.utils.UploadUtil;
import cn.taihealth.ih.service.api.UploadService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.File;

@Service
@AllArgsConstructor
public class UploadUtilHelper implements UploadUtil {

    private final UploadService uploadService;

    @Override
    public void download(Upload upload, File file) {
        uploadService.download(upload, file);
    }

}
