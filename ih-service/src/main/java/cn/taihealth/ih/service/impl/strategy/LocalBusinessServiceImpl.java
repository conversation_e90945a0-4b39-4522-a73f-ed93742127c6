package cn.taihealth.ih.service.impl.strategy;

import cn.taihealth.ih.commons.util.Snowflake64;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.ElectronicMedicCard.CardType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.service.dto.nodeRed.ConfirmRefund;
import cn.taihealth.ih.service.dto.CreateOfflineOrderDTO;
import cn.taihealth.ih.service.dto.ElectronicMedicCardDTO;
import cn.taihealth.ih.service.dto.OfflineOrderDTO;
import cn.taihealth.ih.service.vm.crm.FollowUpParam;
import cn.taihealth.ih.service.vm.nodered.NodeRedResponseData;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.LockNumberResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.ReturnRegistResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.SyncInspectMRResult;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.service.vm.platform.PayPlatform;
import cn.taihealth.ih.wechat.service.vm.wechat.MedicalInsuranceParam;
import com.google.common.collect.Lists;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service("localBusinessService")
public class LocalBusinessServiceImpl implements BusinessService {

    @Override
    public ApplyElectricInvoiceForHuLiResult applyElectricInvoiceForHuLi(Order order, OrderNursingExt ext) {
        return null;
    }

    @Override
    public List<ElectronicMedicCardDTO> getElectronicMedicCard(Patient patient, Hospital hospital) {
        List<ElectronicMedicCardDTO> canBindCard = patient.getElectronicMedicCards().stream()
            .filter(u -> u.getOnlineType() == ElectronicMedicCard.OnlineType.IH)
            .map(ElectronicMedicCardDTO::new)
            .collect(Collectors.toList());
        return canBindCard;
    }

    @Override
    public String addPatientCards(String hospcode, Patient patient, String cardNo, CardType cardType, String patid) {
        return null;
    }

    @Override
    public void syncDicDiagnose(Hospital hospital) {

    }

    @Override
    public List<RecipeInfo> getPatientRecipeListRenewable(Hospital hospital, Patient patient, String beginDate, String endDate, String insuranceParam) {
        return null;
    }

    @Override
    public List<CurrentDayAppointment> getCurrentDayAppointmentList(Hospital hospital, String channel_type) {
        return null;
    }

    @Override
    public List<DoctorScheduleInfo> getCurrentDayDoctorList(Hospital hospital, Dept dept) {
        return null;
    }

    @Override
    public void syncChargeItem(Hospital hospital) {

    }

    @Override
    public MedicalCardInfo createMedicalCard(Hospital hospital, Patient patient) {
        MedicalCardInfo medicalCardInfo = new MedicalCardInfo();
        medicalCardInfo.setPatname(patient.getName());
        medicalCardInfo.setCardno(Snowflake64.Holder.INSTANCE.nextId() + "");
        medicalCardInfo.setPatid(patient.getId() + "");
        medicalCardInfo.setCardtype("0");
        medicalCardInfo.setIsVirtual("是");
        medicalCardInfo.setOnlineType(ElectronicMedicCard.OnlineType.IH);
        return medicalCardInfo;
    }

    @Override
    public void updatePatientInfo(Hospital hospital, Patient patient) {

    }

    @Override
    public List<DoctorSourceDetail> getCurrentDayDoctorSourceDetail(Hospital hospital, MedicalWorker medicalWorker) {
        return null;
    }

    @Override
    public SchedulingSourceNumber getCurrentDaySchedulingSourceNumber(Hospital hospital, String scheduleId, String source_number) {
        return null;
    }

    @Override
    public PreRegistrationResult preRegisterOnlineOrder(Order order, OrderExtraInfo orderExtraInfo, PlatformTypeEnum platformType) {
        return null;
    }

    @Override
    public HisGeneralResult cancelPreRegist(Hospital hospital, Order order) {
        return null;
    }

    @Override
    public HisGeneralResult cancelPreRegist(Hospital hospital, Patient patient, String regNo, String settleId) {
        return null;
    }

    @Override
    public NodeRedResponseData<ConfirmRegistResult> confirmRegist(Hospital hospital, Order order, HisPayParam hisPayParam) {
        return null;
    }

    @Override
    public ReturnRegistResult returnRegist(Hospital hospital, Order order, PayPlatform payPlatform) {
        ReturnRegistResult result = new ReturnRegistResult();
        result.setSuccess(true);
        result.setRefund_settle_id(order.getId() + "");
        return result;
    }

    @Override
    public LockNumberResult lockNumber(Hospital hospital, String hisPid, OrderExtraInfo orderExtraInfo) {
        return null;
    }

    @Override
    public HisGeneralResult unlock(Hospital hospital, Order order) {
        return null;
    }

    @Override
    public SaveRecipeResult saveRecipeOnline(Hospital hospital, PrescriptionOrder order) {
        return null;
    }
    @Override
    public SaveMedicalCaseResult saveMedicalCaseOnline(Hospital hospital, Order order) {
        return null;
    }
    @Override
    public SaveMedicalCaseResult saveDiagnoseOnline(Hospital hospital, Order order) {
        return null;
    }

    @Override
    public ReturnRegistResult deleteRecipeOnline(Hospital hospital, Long orderId, String patientName, String patHisId,
                                                 String recipeId) {
        return null;
    }

    @Override
    public HisGeneralResult noticeForRecipeCheck(Hospital hospital, Order order,
                                                 List<PrescriptionOrder> prescriptionOrders) {
        return null;
    }

    @Override
    public PreChargeResult preCharge(Hospital hospital, PrescriptionOrder order, PlatformTypeEnum platformType) {
        return null;
    }

    @Override
    public PreChargeResult outPatientPreCharge(Hospital hospital, Patient patient, ElectronicMedicCard card, String regno,
                                               String insuranceParam, List<String> recipeNoList, String payType, String extraContent) {
        return null;
    }

    @Override
    public HisGeneralResult cancelPreCharge(Hospital hospital, PrescriptionOrder order) {
        return null;
    }

    @Override
    public NodeRedResponseData<ConfirmChargeResult> confirmCharge(Hospital hospital, PrescriptionOrder order, WechatOrder wechatOrder,
                                                                  HisPayParam hisPayParam) {
        return null;
    }

    @Override
    public NodeRedResponseData<AppointmentSignInResult> appointmentSignIn(String hospcode, AppointmentSignInReq signInReq) {
        return null;
    }

    @Override
    public NodeRedResponseData<ConfirmChargeResult> outPatientConfirmCharge(Hospital hospital, ElectronicMedicCard card, HisPayParam hisPayParam,
                                                                            OutpatientUnChargeRecipeInfo info, HisOutpatientCharge charge) {
        return null;
    }

    @Override
    public PayResultOnline getPayResultOnline(Hospital hospital, Order order) {
        return null;
    }

    @Override
    public CancelOutpatientSettleResult cancelOutpatientSettle(Hospital hospital, Order order, PayPlatform payPlatform) {
        CancelOutpatientSettleResult result = new CancelOutpatientSettleResult();
        result.setRefund_settle_id(order.getId() + "0");
        return result;
    }

    @Override
    public HisFileUploadInfo uploadFile(Hospital hospital, Long orderId, String type, String url) {
        return null;
    }

    @Override
    public Page<FollowUpInfo> getFollowUpListByPatient(Hospital hospital, Patient patient, FollowUpParam param,
                                                       Integer pageNo, Integer size) {
        return null;
    }

    @Override
    public void syncDept(Hospital hospital) {

    }

    @Override
    public void syncCheckCategory(Hospital hospital) {

    }

    @Override
    public void syncCheckItem(Hospital hospital) {

    }

    @Override
    public void syncCheckDevices(Hospital hospital) {

    }

    @Override
    public void pushDictChangeMsg(Hospital hospital, String msg_type, String channel_code) {

    }

    @Override
    public ReportTimeLimit getReportTimeLimit(Hospital hospital, String item_code) {
        return null;
    }

    @Override
    public List<ApplicationInfo> getApplicationInfoList(String hospitalCode, String pat_name, String apply_no, String certificate_no, String card_no, String pat_id, String pat_type, String begin_date, String end_date) {
        return null;
    }

    @Override
    public void cancelCheckApplication(String hospitalCode, String patname, String patid, String application_no, String application_category, String message) {

    }

    @Override
    public List<CheckReportResult> getCheckReportResult(String hospitalCode, String report_no, String report_type_code) {
        return null;
    }

    @Override
    public MedTechWaitingList getMedTechWaiting(String hospitalCode, String patname, String patid, String regist_type) {
        return null;
    }

    @Override
    public void pushAppointmentChangeMsg(String hospitalCode, String application_id, String appointment_id, String operation_id, String operation_name, String operation_time, String channel_code, String status, String message) {

    }

    @Override
    public Page<DeptAndWardInfo> getDeptAndWardInfo(Hospital hospital, Integer pageNo, Integer size) {
        return null;
    }

    @Override
    public List<WardBedInfo> getWardBedInfo(Hospital hospital, String deptId) {
        return null;
    }

    @Override
    public void syncDoctorOffline(Hospital hospital) {

    }

    @Override
    public BillSummary getHisBills(Hospital hospital, String billDate, String payType) {
        return null;
    }

    @Override
    public OfflineOrderDTO saveAppointment(Hospital hospital, CreateOfflineOrderDTO createOfflineOrderDTO) {
        return null;
    }

    @Override
    public ReponseResult cancelAppointment(Hospital hospital, OfflineOrder offlineOrder) {
        return null;
    }

    @Override
    public ReverseAbnormalBillResult reverseAbnormalBill(Hospital hospital, Order order) {
        return null;
    }

    @Override
    public TradeIHBillResult tradeIHBill(Hospital hospital, TradeIHBillSummary billSummary) {
        return null;
    }

    @Override
    public InpatientHospCardPreChargeResult inpatientHospCardPreCharge(Hospital hospital, String patientName, String rego) {
        return null;
    }

    @Override
    public NodeRedResponseData<InpatientHospCardChargeResult> inpatientHospCardCharge(Hospital hospital,
                                                                 String patientName,
                                                                 String reg_no,
                                                                 InpatientHospitalChargeReq inpatientHospitalChargeReq) {
        return null;
    }

    @Override
    public InpatientPreChargeResult inpatientPreCharge(Hospital hospital, Order order) {
        return null;
    }

    @Override
    public InpatientConfirmChargeResult inpatientConfirmCharge(Hospital hospital, Order order) {
        return null;
    }

    @Override
    public List<AppointmentInfo> getSourceDetails(Hospital hospital, String beginDate, String endDate, String channelType, String deptId, String doctorName) {
        return null;
    }

    @Override
    public List<SchedulingInfo> getSchedulingListById(Hospital hospital, String schedulingId, String sourceType, String channelType) {
        return null;
    }

    @Override
    public List<SchedulingDoctorInfo> getSchedulingDoctorList(Hospital hospital, String deptId, String beginDate, String endDate, String channelType) {
        return null;
    }

    @Override
    public List<SchedulingDeptSourceInfo> getSchedulingDeptSourceDetails(Hospital hospital, String beginDate, String endDate, String deptId, String channelType) {
        return null;
    }

    @Override
    public List<SchedulingDoctorSourceInfo> getSchedulingDoctorSourceDetails(Hospital hospital, String beginDate, String endDate, String doctorId, String channelType) {
        return null;
    }

    @Override
    public List<PatientAppointmentInfo> getPatientAppointment(Hospital hospital, String patientName, String patientId, String beginDate, String endDate, String routeFlag, String dateType) {
        return null;
    }

    @Override
    public List<PatientRegistInfo> getRegistListByPatId(Hospital hospital, String patientName, String patId, String beginDate, String endDate, String routeFlag) {
        return null;
    }

    @Override
    public PatientRegistInfo getRegistListByRegno(Hospital hospital, String patientName, String regno, String appointmentId, String patientId, String routeFlag) {
        return null;
    }

    @Override
    public List<PatientRegistWaitingInfo> getPatientRegistWaitingListByPatId(Hospital hospital, String patientName, String hisPatientId) {
        return null;
    }

    @Override
    public List<PatientDrugWaitingInfo> getPatientDrugWaitingList(Hospital hospital, String patientName, String hisPatientId) {
        return null;
    }

    @Override
    public List<OutpatientItemHintInfo> getOutpatientItemHint(Hospital hospital, String hisPatientId, String settleId) {
        return null;
    }

    @Override
    public List<PatientSignInfo> getPatientSignInfo(Hospital hospital, String date) {
        return null;
    }

    @Override
    public List<PatientWaitingInfo> getPatientWaiting(Hospital hospital, MedicalWorker medicalWorker) {
        return null;
    }

    @Override
    public List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(Hospital hospital, Patient patient, ElectronicMedicCard card,
                                                                              String beginDate, String endDate, String insuranceParam, String channelType) {
        return null;
    }

    @Override
    public List<PricePublicityInfo> getPricePublicityList(Hospital hospital, String pricePublicityType, String keyword) {
        return null;
    }

    @Override
    public Page<PricePublicityInfo> getPricePublicityPage(Hospital hospital, String pricePublicityType, String keyword, int page, int size) {
        return null;
    }

    @Override
    public List<DoctorSchedules> getDoctorSchedules(Hospital hospital, String deptId, String doctorName, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<SpecialClinicInfo> getSpecialClinicList(Hospital hospital, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<GeneralClinicInfo> getGeneralClinicList(Hospital hospital, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<InspectionReport> getInspectionReportListByCardNo(Hospital hospital, String patientName, String cardNo, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<InspectionReport> getInspectionReportListByPatId(Hospital hospital, ElectronicMedicCard card, String userSource, String beginDate, String endDate) {
        return Lists.newArrayList();
    }

    @Override
    public List<LaboratoryReport> getLaboratoryReportList(Hospital hospital, String reportNo, String reportTypeCode) {
        return null;
    }

    @Override
    public List<MicroorganismReport> getMicroorganismReportsByCardNo(Hospital hospital, String patientName, String cardNo, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<MicroorganismReport> getMicroorganismReportsByPatId(Hospital hospital, String patientName, Long id, Integer userSource, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<LaboratoryMicroorganismsReportResult> getLaboratoryMicroorganismsReportResult(Hospital hospital, String reportNo, String reportTypeCode) {
        return null;
    }

    @Override
    public List<RisReport> getRisReportsByCardNo(Hospital hospital, String patientName, String cardNo, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<RisReport> getRisReportsByPatId(Hospital hospital, ElectronicMedicCard card, String userSource, String beginDate, String endDate) {
        return Lists.newArrayList();
    }

    @Override
    public List<RisReportResult> getRisReportResult(Hospital hospital, String reportNo, String reportTypeCode) {
        return null;
    }

    @Override
    public List<CriticalValueReport> getCriticalValueReportByPatId(Hospital hospital, String patientName, Long id, Integer userSource, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<InpatientInfo> getInpatientListByHisCardNo(Hospital hospital, String patientName, String hisCardNo, String telephone) {
        return null;
    }

    @Override
    public List<InpatientInfo> getInpatientListByCardNo(Hospital hospital, String patientName, String cardNo) {
        return null;
    }

    @Override
    public List<InpatientInfo> getInpatientListByCertNo(Hospital hospital, String patientName, String certNo) {
        return null;
    }

    @Override
    public List<InpatientRecord> getInpatientRecordList(Hospital hospital, String patientName, String hisPatientId, String beginDate, String endDate, String status) {
        return null;
    }

    @Override
    public List<InpatientRecord> getInpatientRecordList(Hospital hospital, String patientName, String hisPatientId, String hospNo, String beginDate, String endDate, String status) {
        return null;
    }

    @Override
    public InpatientAdvanceCharge getInpatientAdvanceCharge(Hospital hospital, String patientName, String regno) {
        return null;
    }

    @Override
    public List<InpatientAdvanceChargeDetail> getInpatientAdvanceChargeDetailList(Hospital hospital, String patientName, String regno) {
        return null;
    }

    @Override
    public SupplyHospitalAdmissionCertificateResult saveSupplyHospitalAdmissionCertificate(Hospital hospital, long card_id, SupplyHospitalAdmissionCertificateReq supplyHospitalAdmissionCertificate) {
        return null;
    }

    @Override
    public List<OutpatientRecipeInfo> getOutpatientRecipeList(Hospital hospital, String patientName, String patientId, String beginDate, String endDate, String insuranceParam) {
        return null;
    }

    @Override
    public ReponseResult updateHospitalAdmissionCertificate(Hospital hospital, HospitalAdmissionCertificateReq param) {
        return null;
    }

    @Override
    public ReponseResult checkHospitalAdmissionOnline(Hospital hospital, String patientName, String regNo, String admissionNo, String patientId) {
        return null;
    }

    @Override
    public List<OutpatientCharge> getOutpatientChargeList(Hospital hospital, Patient patient, String channelType, String beginDate, String endDate) {
        return null;
    }

    @Override
    public OutpatientChargeDetail getOutpatientChargeDetails(Hospital hospital, String settleId) {
        return null;
    }

    @Override
    public List<SchedulingDeptInfo> getSchedulingDeptList(Hospital hospital, String beginDate, String endDate, String channelType) {
        return null;
    }

    @Override
    public List<SchedulingDeptInfo> getSchedulingDeptListTest(Hospital hospital, String beginDate, String endDate,
                                                              String channelType) {
        return null;
    }

    @Override
    public List<InpatientFeeDetail> getInpatientFeeDetails(Hospital hospital, String patientName, String regNo, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<InpatientDaily> getInpatientDaily(Hospital hospital, String name, String regNo, String date, String summaryFlag) {
        return null;
    }

    @Override
    public List<InpatientSettleInfo> getInpatientSettleList(Hospital hospital, String name, String regNo) {
        return null;
    }

    @Override
    public List<HospitalAdmissionCertificate> getHospitalAdmissionCertificateByCardNo(Hospital hospital, String name, String cardNo, String hisPatid, String identity, String admissionNo, String beginDate, String endDate) {
        return null;
    }

    @Override
    public List<DischargeMedication> getDischargeMedicationList(Hospital hospital, String name, String regNo) {
        return null;
    }

    @Override
    public List<OperationHints> getOperationHints(Hospital hospital, String name, String regNo) {
        return null;
    }

    @Override
    public List<OperationSchedule> getOperationSchedule(Hospital hospital, String name, String userSource, String regNo) {
        return null;
    }

    @Override
    public List<InpatientMedicalRecordText> getInpatientMedicalRecordText(Hospital hospital, String name, String regNo, String recType) {
        return null;
    }

    @Override
    public HospitalAdmission addHospitalAdmission(Hospital hospital, HospitalAdmissionReq param) {
        return null;
    }

    @Override
    public ApplyElectronicInvoice applyElectronicInvoice(Hospital hospital, Patient patient, ApplyElectronicInvoiceReq param) {
        return null;
    }

    @Override
    public ElectronicInvoiceFile getElectronicInvoiceFile(Hospital hospital, Patient patient, String eleInvoiceNo) {
        return null;
    }

    @Override
    public Page<ElectronicInvoiceItem> getElectronicInvoiceList(Hospital hospital, String hisPatid, String online_type, String page, String size) {
        return null;
    }

    @Override
    public SchedulingDoctorSourceDetails getSchedulingDoctorSourceInfo(Hospital hospital, String beginDate, String endDate, String doctorId, String channelType) {
        return null;
    }

    @Override
    public PreRegistrationResult preRegisterOfflineOrder(Hospital hospital, OfflineOrder order, String registrationFeeCode,
                                                         String treatmentFeeCode, String childrenTreatmentFeeCode, MedicalInsuranceParam insuranceParam) {
        return null;
    }

    @Override
    public void outPatientAppointment(Hospital hospital, OfflineOrder order, String registrationFeeCode,
                                      String treatmentFeeCode, String childrenTreatmentFeeCode) {
    }

    @Override
    public PreRegistrationResult outPatientPreRegister(Hospital hospital, OfflineOrder order, Patient patient,
                                                       String registrationFeeCode, String treatmentFeeCode,
                                                       String childrenTreatmentFeeCode, MedicalInsuranceParam insuranceParam) {
        return null;
    }

    @Override
    public NodeRedResponseData<ConfirmRegistResult> confirmRegist(Hospital hospital, OfflineOrder order, HisPayParam hisPayParam) {
        return null;
    }

    @Override
    public Page<DoctorSchedules> getDoctorSchedulesByPage(Hospital hospital, String deptId, String doctorName, String beginDate, String endDate, Integer pageNo, Integer size) {
        return null;
    }

    @Override
    public ReturnRegistResult returnRegist(Hospital hospital, OfflineOrder order, String transactionId, MedicalInsuranceParam insuranceParam) {
        return null;
    }

    @Override
    public List<PhysicalExaminationRecord> getPhysicalExaminationRecord(Hospital hospital, ElectronicMedicCard card, String beginDate, String endDate) {
        return Lists.newArrayList();
    }

    @Override
    public List<PhysicalExaminationReportFile> getPhysicalExaminationReportFile(Hospital hospital, String name, String tjbh) {
        return Lists.newArrayList();
    }

    @Override
    public OutpatientPayResult getOutpatientPayResult(Hospital hospital, String settleId, String tradeType, String tradeNo) {
        return null;
    }

    @Override
    public void sendRefundResultToHis(ConfirmRefund confirmRefund) {
    }

    @Override
    public NodeRedResponseData<ConfirmChargeResult> outPatientConfirmCharge(Hospital hospital, String hisPatId,
                                                                            HisPayParam hisPayParam,
                                                                            OutpatientUnChargeRecipeInfo info,
                                                                            HisOutpatientCharge charge) {
        return null;
    }

    @Override
    public NodeRedResponseData<ConfirmChargeResult> confirmCharge(Hospital hospital,
                                                                  PrescriptionOrder prescriptionOrder,
                                                                  AliPayOrder ailPayOrder) {
        return null;
    }

    @Override
    public PreChargeResult outPatientPreCharge(Hospital hospital, String hisPatId, String regno, String insuranceParam,
                                               List<String> recipeNoList, String payType, String extraContent) {
        return null;
    }

    @Override
    public List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(Hospital hospital, String hisPatId,
                                                                              String patName, ElectronicMedicCard card,
                                                                              String beginDate, String endDate,
                                                                              String insuranceParam,
                                                                              String channelType) {
        return null;
    }

    @Override
    public List<PatientMedicalRecord> patientMedicalRecords(Hospital hospital, String patId, String beginDate,
                                                            String endDate) {
        return null;
    }

    @Override
    public SyncInspectMRResult syncInspectMR(OfflineOrder order, boolean agreed) {
        return null;
    }
}
