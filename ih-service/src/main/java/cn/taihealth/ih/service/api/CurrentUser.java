package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.error.UserNotFoundException;
import com.gitq.jedi.context.AppContext;

import java.util.Optional;

/**
 */
public interface CurrentUser {

    Optional<User> get();

    static User getOrThrow() {
        return AppContext.getInstance(CurrentUser.class).get().orElseThrow(UserNotFoundException::new);
    }

    static User getOrNull() {
        return AppContext.getInstance(CurrentUser.class).get().orElse(null);
    }
}
