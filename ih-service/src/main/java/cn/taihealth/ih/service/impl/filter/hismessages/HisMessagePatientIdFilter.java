package cn.taihealth.ih.service.impl.filter.hismessages;

import cn.taihealth.ih.domain.his.FromHisMessage;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class HisMessagePatientIdFilter implements SearchFilter<FromHisMessage> {

    private final String value;

    @Override
    public String getName() {
        return getClass().getName();
    }

    public HisMessagePatientIdFilter( String value) {
        this.value = value;
    }


    @Override
    public Specification<FromHisMessage> toSpecification() {
        return Specifications.eq("patientId", value);
    }

    @Override
    public String toExpression() {
        return value;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof HisMessagePatientIdFilter)) {
            return false;
        }

        HisMessagePatientIdFilter rhs = (HisMessagePatientIdFilter) other;
        return Objects.equals(value, rhs.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
}