package cn.taihealth.ih.service;

import cn.taihealth.ih.commons.Constants;
import com.google.common.eventbus.EventBus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 */
@Configuration
@ComponentScan(basePackages = "cn.taihealth.ih.service")
public class ServiceConfiguration {

    public ServiceConfiguration() {
    }

    @Bean
    public EventBus eventBus() {
        return new EventBus(Constants.EVENTBUS_NAME);
    }
}
