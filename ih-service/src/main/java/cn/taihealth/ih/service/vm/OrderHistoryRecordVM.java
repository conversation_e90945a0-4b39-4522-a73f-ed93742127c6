package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.hospital.OrderHistoryRecord;
import cn.taihealth.ih.service.dto.historyrecord.MedicalCaseHistoryDTO;
import cn.taihealth.ih.service.dto.historyrecord.OrderHistoryDTO;
import cn.taihealth.ih.service.dto.historyrecord.PatientHistoryDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class OrderHistoryRecordVM {

    private Long id;

    @ApiModelProperty("订单ID ")
    private Long orderId;

    @ApiModelProperty("患者")
    private PatientHistoryDTO patient;

    @ApiModelProperty("患者ID ")
    private Long patientId;

    @ApiModelProperty("病历表 ")
    private MedicalCaseHistoryDTO medicalCaseHistoryDTO;

    @ApiModelProperty("操作人名字")
    private String operatorName;

    @ApiModelProperty("医院名称")
    private String hospitalName;

    @ApiModelProperty("科室名称")
    private String deptName;

    @ApiModelProperty("医生名称")
    private String doctorName;

    private Date endedDate;

    private Date triagedDate;

    private Date registeredDate;

    private Date admissionDate;

    private Date confirmedDate;

    @ApiModelProperty("疾病")
    private String[] diseases = new String[]{};

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("医生")
    private DoctorVM doctor;

    public OrderHistoryRecordVM(OrderHistoryRecord orderHistoryRecord) {
        this.orderId = orderHistoryRecord.getOrderId();
        this.patientId = orderHistoryRecord.getPatientId();
        this.patient = null != orderHistoryRecord.getPatient() && !orderHistoryRecord.getPatient()
            .equals("null") ? new PatientHistoryDTO().init(orderHistoryRecord.getPatient()) : null;
        this.medicalCaseHistoryDTO =
            null != orderHistoryRecord.getMedicalCase() && !orderHistoryRecord.getMedicalCase()
                .equals("null") ? new MedicalCaseHistoryDTO()
                .init(orderHistoryRecord.getMedicalCase()) : null;
        this.hospitalName = orderHistoryRecord.getHospitalName();
        this.operatorName = orderHistoryRecord.getOperator();
        this.deptName = orderHistoryRecord.getDeptName();
        this.doctorName = orderHistoryRecord.getDoctorName();
        this.triagedDate = orderHistoryRecord.getTriagedDate();
        this.registeredDate = orderHistoryRecord.getRegisteredDate();
        this.admissionDate = orderHistoryRecord.getAdmissionDate();
        this.confirmedDate = orderHistoryRecord.getConfirmedDate();
        OrderHistoryDTO order = null != orderHistoryRecord.getOrder() && !orderHistoryRecord.getOrder().equals("null")
                ? new OrderHistoryDTO().init(orderHistoryRecord.getOrder()): null;
        if (null != order) {
            this.diseases = order.getDiseases();
        }

    }
}
