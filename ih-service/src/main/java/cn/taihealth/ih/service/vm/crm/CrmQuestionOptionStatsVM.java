package cn.taihealth.ih.service.vm.crm;

import cn.taihealth.ih.domain.crm.CrmOption;
import cn.taihealth.ih.domain.crm.CrmQuestion;
import cn.taihealth.ih.domain.enums.QuestionType;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 随访-问卷-问题选项
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class CrmQuestionOptionStatsVM extends AbstractEntityDTO {

    @ApiModelProperty("题目内容")
    private String content;

    @ApiModelProperty(name = "描述")
    private String description;

    @ApiModelProperty(name = "回答的数量")
    private long answeredCount;

    @ApiModelProperty(name = "问题类型")
    private QuestionType questionType = QuestionType.SINGLE;

    private List<CrmOptionStatsVM> options = Lists.newArrayList();

    @Getter
    @Setter
    @NoArgsConstructor
    public static class CrmOptionStatsVM extends AbstractEntityDTO {

        @ApiModelProperty("选项内容")
        private String content;

        @Column(name = "排序")
        private int optionOrder;

        @Column(name = "数量")
        private int count;

        public CrmOptionStatsVM(CrmOption option) {
            super(option);
            this.content = option.getContent();
            this.optionOrder = option.getOptionOrder();
        }
    }

    public CrmQuestionOptionStatsVM(CrmQuestion question) {
        super(question);
        this.content = question.getContent();
        this.description = question.getDescription();
        this.questionType = question.getQuestionType();
        this.options = question.getOptions().stream().sorted(Comparator.comparing(CrmOption::getOptionOrder))
                .map(CrmOptionStatsVM::new).collect(Collectors.toList());
    }
}
