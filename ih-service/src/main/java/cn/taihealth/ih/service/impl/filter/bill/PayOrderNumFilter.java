package cn.taihealth.ih.service.impl.filter.bill;

import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class PayOrderNumFilter implements SearchFilter<Bill> {
    private final String value;

    public PayOrderNumFilter(String value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Bill> toSpecification() {
        return Specifications.eq("merchantOrderNumber", value);
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
