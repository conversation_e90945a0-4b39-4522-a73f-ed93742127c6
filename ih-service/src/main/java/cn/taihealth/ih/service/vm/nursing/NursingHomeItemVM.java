package cn.taihealth.ih.service.vm.nursing;

import cn.taihealth.ih.domain.nursing.NursingHomeChargeItem;
import cn.taihealth.ih.domain.nursing.NursingHomeItem;
import cn.taihealth.ih.domain.nursing.NursingHomeItemNurse;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import cn.taihealth.ih.service.dto.nursing.NursingItemClassifyDTO;
import cn.taihealth.ih.service.vm.UploadVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
public class NursingHomeItemVM extends UpdatableDTO {

    @ApiModelProperty("护理到家项目名称")
    private String name;

    @ApiModelProperty("排序序号")
    private int orderNo;

    @ApiModelProperty("护理到家项目图标")
    private UploadVM icon;

    @ApiModelProperty("是否可用")
    private boolean enabled = true;

    @ApiModelProperty("是否单独核算交通费")
    private boolean isTransportation = false;

    @ApiModelProperty("项目分类")
    private NursingItemClassifyDTO itemClassify;

    @ApiModelProperty("简介")
    private String introduction;

    @ApiModelProperty("预交金项目")
    private List<NursingAdvancePriceVM> nursingAdvancePrice;

    @ApiModelProperty("估计价格")
    private int estimatedPrice;

    @ApiModelProperty("收费项目")
    private List<NursingHomeChargeItemVM> chargeItems = Lists.newArrayList();

    @ApiModelProperty("负责护士")
    private List<NurseVM> nurses = Lists.newArrayList();

    public NursingHomeItemVM(NursingHomeItem homeItem) {
        super(homeItem);
        this.name = homeItem.getName();
        this.orderNo = homeItem.getOrderNo();
        this.enabled = homeItem.isEnabled();
        this.isTransportation = homeItem.isTransportation();
        this.introduction = homeItem.getIntroduction();
        this.estimatedPrice = homeItem.getEstimatedPrice();
        if (homeItem.getIcon() != null) {
            this.icon = new UploadVM(homeItem.getIcon());
        }
        if (homeItem.getItemClassify() != null) {
            this.itemClassify = new NursingItemClassifyDTO(homeItem.getItemClassify());
        }
        if (CollectionUtils.isNotEmpty(homeItem.getNursingAdvancePrice())) {
            this.nursingAdvancePrice = homeItem.getNursingAdvancePrice().stream()
                    .map(NursingAdvancePriceVM::new).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(homeItem.getChargeItems())) {
            this.chargeItems = homeItem.getChargeItems().stream()
                    .sorted(Comparator.comparing(NursingHomeChargeItem::getOrderNo))
                    .map(NursingHomeChargeItemVM::new)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(homeItem.getNurses())) {
            this.nurses = homeItem.getNurses().stream()
                    .sorted(Comparator.comparing(NursingHomeItemNurse::getOrderNo))
                    .map(u -> new NurseVM(u.getNurse()))
                    .collect(Collectors.toList());
        }
    }
}
