package cn.taihealth.ih.service.impl.filter.studioworkers;

import cn.taihealth.ih.domain.Studio;
import cn.taihealth.ih.domain.StudioWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class StudioWorkerStudioFilter implements SearchFilter<StudioWorker> {

    private final Long studioId;

    public StudioWorkerStudioFilter(Long studioId) {
        this.studioId = studioId;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<StudioWorker> toSpecification() {
        Studio studio = new Studio();
        studio.setId(studioId);
        return Specifications.eq("studio", studio);
    }

    @Override
    public String toExpression() {
        String str = " studioId:" + studioId;
        return str;
    }

    @Override
    public boolean isValid() {
        return studioId != null;
    }
}
