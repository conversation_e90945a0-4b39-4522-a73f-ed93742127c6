package cn.taihealth.ih.service.impl.certification;

import cn.taihealth.ih.commons.util.IdCardUtils;
import cn.taihealth.ih.commons.util.IdCardUtils.IdCardBean;
import cn.taihealth.ih.domain.IdCard;
import cn.taihealth.ih.service.dto.UploadDTO;
import cn.taihealth.ih.service.util.GenderUtils;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service("AliCertification")
public class AliCertificationServiceImpl implements CertificationService {

    @Override
    public IdCardBean verifyUserIdInfo(IdCard cert) {
        JSONObject data = AliOcrClient.getIdCardInfo(new UploadDTO(cert.getBackImage()).getUrl());
        AliOcrClient.getIdCardInfo(new UploadDTO(cert.getFrontImage()).getUrl());
        cert.setName(data.getString("name"));
        cert.setIdCardNo(data.getString("idNumber"));
        IdCardBean idCard = IdCardUtils.isValidate18IdCard(cert.getIdCardNo());
        cert.setGender(GenderUtils.toGender(idCard.getSex()));
        return idCard;
    }

}
