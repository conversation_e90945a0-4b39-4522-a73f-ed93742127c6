package cn.taihealth.ih.service.vm.nursing;

import cn.taihealth.ih.domain.nursing.NursingAdvancePrice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
public class NursingAdvancePriceVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("价格（分）")
    private int price;

    public NursingAdvancePriceVM(NursingAdvancePrice advancePrice) {
        this.name = advancePrice.getName();
        this.price = advancePrice.getPrice();
    }

    public NursingAdvancePrice toNursingAdvancePrice() {
        NursingAdvancePrice advancePrice = new NursingAdvancePrice();
        advancePrice.setName(name);
        advancePrice.setPrice(price);
        return advancePrice;
    }

}
