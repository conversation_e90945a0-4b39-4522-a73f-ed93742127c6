package cn.taihealth.ih.service.util;

import cn.taihealth.ih.commons.util.HttpUtil;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.spring.security.authenticated.AuthenticatedUserToken;
import cn.taihealth.ih.spring.security.jwt.JWTAuthenticationProvider;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/11/17
 */
@Slf4j
public class FileUtils {


    private static final Map<String, String> mimeTypeMap = ImmutableMap.<String, String>builder()
            .put("image/png", "png")
            .put("image/jpeg", "jpg")
            .put("image/jpg", "jpg")
            .build();
    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtils.class);

    public static String searchFile(String path, String fileName, String suffix) throws IOException {
        return searchDirectory(new File(path), String.format("%s.%s", fileName, suffix));
    }

    public static String searchDirectory(File directory, String fileNameToSearch) {
        if (directory.isDirectory()) {
            return search(directory, fileNameToSearch);
        } else {
            LOGGER.info(directory.getAbsoluteFile() + " is not a directory!");
            return null;
        }
    }

    private static String search(File file, String fileNameToSearch) {
        if (file.isDirectory()) {
//            LOGGER.info("Searching directory ... " + file.getAbsoluteFile());

            //do you have permission to read this directory?
            if (file.canRead()) {
                for (File temp : file.listFiles()) {
                    if (temp.isDirectory()) {
                        if (temp.getName().startsWith(".")) {
                            continue;
                        }
                        if (temp.getName().contains("log")) {
                            LOGGER.info("now search log folder");
                        }
                        String result = search(temp, fileNameToSearch);
                        if (!StringUtils.isEmpty(result)) {
                            return result;
                        }
                    } else {
                        if (fileNameToSearch.equals(temp.getName().toLowerCase())) {
                            return temp.getAbsolutePath().toString();
                        }
                    }
                }
            } else {
                LOGGER.info(file.getAbsoluteFile() + "Permission Denied");
                return null;
            }
        }
        return null;

    }

    public static String getFileName(String filePath) {
        String[] path = filePath.split("\\?")[0].split("[/\\\\]");
        return path[path.length - 1];
    }

    public static String mediaTypeToExtension(String mimeType) {
        return mimeTypeMap.get(mimeType);
    }

    /**
     * 删除文件，如果是文件夹，删除整个文件夹，文件或文件夹不存在，不会报错
     * @param file 文件，可以为null
     */
    public static void delete(File file) {
        if (file == null || !file.exists()) {
            return;
        }
        if (file.isFile()) {
            file.delete();
        } else {
            try {
                org.apache.commons.io.FileUtils.forceDelete(file);
            } catch (IOException ignored) {
            }
        }
    }


    /**
     * 下载证书文件
     * @param keyUrl
     * @param filePath
     * @return
     */
    public static void downLoadMerchantCertificate(String keyUrl, String filePath) {
        if (new File(filePath).exists()) {
            return;
        }
        try {
            User system = AppContext.getInstance(UserService.class).getSystem();
            AuthenticatedUserToken authenticatedUserToken =
                    new AuthenticatedUserToken(system.getUsername(), system.getPassword());
            Authentication authentication = AppContext.getInstance(AuthenticationManager.class).authenticate(authenticatedUserToken);
            SecurityContextHolder.getContext().setAuthentication(authentication);
            String token = AppContext.getInstance(JWTAuthenticationProvider.class).createAccessToken(authentication, 7200);
            Map<String, String> params = new HashMap<>();
            params.put("idToken", token);
            String url = HttpUtil.addParams(keyUrl, params);
//            String url = keyUrl;
            OkHttpUtils.download(url, new File(filePath));
        } catch (Exception e) {
            log.info("文件下载失败，尝试换一个接口", e);
            String url = keyUrl.replace("/raw/", "/rawu/");
            try {
                OkHttpUtils.download(url, new File(filePath));
            } catch (IOException ex) {
                throw new RuntimeException("文件下载失败 " + url, ex);
            }
        }
    }

}
