package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.UpdatableEntity;

import java.util.Date;

/**
 */
public abstract class UpdatableDTO extends AbstractEntityDTO {

    private Date createdDate;

    private Date updatedDate;

    public UpdatableDTO() {}

    public UpdatableDTO(UpdatableEntity entity) {
        super(entity);
        this.createdDate = entity.getCreatedDate();
        this.updatedDate = entity.getUpdatedDate();
    }


    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }
}
