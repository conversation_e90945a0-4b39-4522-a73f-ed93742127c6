package cn.taihealth.ih.service.dto.crm;

import cn.taihealth.ih.domain.crm.CrmQuestionnaireChannel;
import cn.taihealth.ih.domain.crm.CrmQuestionnaireQuestion;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.vm.crm.CrmQuestionVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;


/**
 * 随访-计划-问卷答案表
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
public class CrmQuestionnaireChannelDTO extends AbstractEntityDTO {

    @ApiModelProperty("渠道名称")
    @NotBlank(message = "渠道名称不能为空")
    private String name;

    public CrmQuestionnaireChannelDTO(CrmQuestionnaireChannel channel) {
        super(channel);
        this.name = channel.getChannelName();
    }

    public CrmQuestionnaireChannel toEntity() {
        CrmQuestionnaireChannel crmQuestionnaireChannel = new CrmQuestionnaireChannel();
        crmQuestionnaireChannel.setChannelName(this.name);
        crmQuestionnaireChannel.setDelete(false);
        return crmQuestionnaireChannel;
    }

}
