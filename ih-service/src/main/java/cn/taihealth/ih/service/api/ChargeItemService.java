package cn.taihealth.ih.service.api;


import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.ChargeItem;
import cn.taihealth.ih.service.dto.ChargeItemDTO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ChargeItemService {

    void saveOrUpdateChargeItems(List<ChargeItem> chargeItems);

    Optional<ChargeItem> findByHospitalAndItemCode(Hospital hospital, String itemCode);

    Page<ChargeItemDTO> page(Hospital hospital, Pageable pageable, String query);
}
