package cn.taihealth.ih.service.util;

import cn.taihealth.ih.domain.cloud.UserRole;
import cn.taihealth.ih.service.database.Migrator1;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.google.common.io.Resources;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UserRoleProvider {

    private static final Logger log = LoggerFactory.getLogger(UserRoleProvider.class);

    private UserRoleProvider() {
    }

    private static class ModelAndPageClass {

        private static UserRoleProvider instance = new UserRoleProvider();

    }

    public static UserRoleProvider getInstance() {
        return ModelAndPageClass.instance;
    }

    private List<UserRole> allRoles = Lists.newArrayList();

    private void init() {
        URL url = Resources.getResource(Migrator1.class, "data/auth.json");
        try (InputStreamReader inputStream = new InputStreamReader(url.openStream());
             BufferedReader bd = new BufferedReader(inputStream)) {
            allRoles = StandardObjectMapper.getInstance()
                    .readValue(bd, new TypeReference<List<UserRole>>() {});
        } catch (IOException e) {
            log.error("加载页面菜单配置信息失败", e);
        }
    }

    public List<UserRole> getAllRoles() {
        if (CollectionUtils.isEmpty(allRoles)) {
            init();
        }
        return allRoles;
    }

    public UserRole getRole(String roleCode) {
        if (CollectionUtils.isEmpty(allRoles)) {
            init();
        }
        return allRoles.stream().filter(r -> Objects.equals(roleCode, r.getCode()))
                .findFirst().orElse(null);
    }

    public void setAllRoles(List<UserRole> allRoles) {
        this.allRoles = allRoles;
    }
}
