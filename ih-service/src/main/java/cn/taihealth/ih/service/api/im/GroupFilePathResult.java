package cn.taihealth.ih.service.api.im;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 *
 */
public class GroupFilePathResult extends IMResult {

    @JsonProperty
    private String ChatType;

    @JsonProperty
    private String MsgTime;

    @JsonProperty("File")
    private GroupFile[] files;

    public String getChatType() {
        return ChatType;
    }

    public void setChatType(String chatType) {
        ChatType = chatType;
    }

    public String getMsgTime() {
        return MsgTime;
    }

    public void setMsgTime(String msgTime) {
        MsgTime = msgTime;
    }

    public GroupFile[] getFiles() {
        return files;
    }

    public void setFiles(GroupFile[] files) {
        this.files = files;
    }
}

