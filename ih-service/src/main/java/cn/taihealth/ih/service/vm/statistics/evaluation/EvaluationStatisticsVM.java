package cn.taihealth.ih.service.vm.statistics.evaluation;

import cn.taihealth.ih.bean.statistics.EvaluationResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Moon
 * @Date: 2021年03月15日11:27:10
 */
@Data
public class EvaluationStatisticsVM {

    @ApiModelProperty("科室")
    private String dept;

    @ApiModelProperty("医生")
    private String doctor;

    @ApiModelProperty("接诊数")
    private int orderCount;

    @ApiModelProperty("评价数")
    private int evaluationCount;

    @ApiModelProperty("评价总分")
    private int ratingSum;

    @ApiModelProperty("评价均分")
    private String ratingAvg;

    @ApiModelProperty("收益-接诊数")
    private String orderCountYield;

    @ApiModelProperty("收益-评价数")
    private String evaluationYield;

    @ApiModelProperty("收益-评价总分")
    private String ratingSumYield;

    @ApiModelProperty("收益-评价均分")
    private String ratingAvgYield;

    public EvaluationStatisticsVM(EvaluationResult result) {
        this.orderCount = result.getOrderCount();
        this.evaluationCount = result.getEvaluationCount();
        this.ratingSum = result.getRatingSum();
        this.ratingAvg = result.getRatingAvg();
    }


}
