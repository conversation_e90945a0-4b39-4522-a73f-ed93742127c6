package cn.taihealth.ih.service.dto.hospital;

import java.util.Collection;
import lombok.Data;

@Data
public class HospitalPage<T> {

    private Collection<T> content;

    private Boolean last = true;
    private Boolean first = true;
    private Boolean empty = false;

    private int totalElements;
    private int numberOfElements;
    private int totalPages = 1;
    private int size;
    private int number = 0;

    public HospitalPage(Collection<T> content){
        this.content = content;
        totalElements = content.size();
        numberOfElements = content.size();
        size = content.size();
    }



}
