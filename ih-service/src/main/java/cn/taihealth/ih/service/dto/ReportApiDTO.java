package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.cloud.ReportApi;
import cn.taihealth.ih.domain.cloud.SupervisePlatform;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
@Accessors(chain = true) // 链式编程写法
public class ReportApiDTO extends UpdatableDTO {

    @ApiModelProperty(value = "上报接口代码最长255个字符", required = true)
    @NotBlank(message = "上报接口代码不能为空")
    @Size(max = 255, message = "监管平台代码最长255个字符")
    private String code;

    @ApiModelProperty(value = "上报接口名称最长255个字符", required = true)
    @NotBlank(message = "上报接口名称不能为空")
    @Size(max = 255, message = "监管平台名称最长255个字符")
    private String name;

    @ApiModelProperty(value = "服务地址", required = true)
    @NotBlank(message = "服务地址不能为空")
    @Size(max = 255, message = "访问地址最长255个字符")
    private String serviceUrl;

    @ApiModelProperty(value = "是否上报", required = true)
    @NotNull(message = "是否上报必填")
    private Boolean report;

    @ApiModelProperty(value = "所属医院", required = true)
    private HospitalDTO hospital;

    public ReportApiDTO(ReportApi reportApi) {
        super(reportApi);
        this.code = reportApi.getCode();
        this.name = reportApi.getName();
        this.serviceUrl = reportApi.getServiceUrl();
        this.report = reportApi.getReport();
        this.hospital = new HospitalDTO(reportApi.getHospital());
    }
}
