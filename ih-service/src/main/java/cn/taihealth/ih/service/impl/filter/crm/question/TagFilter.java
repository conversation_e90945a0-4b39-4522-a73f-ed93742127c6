package cn.taihealth.ih.service.impl.filter.crm.question;

import cn.taihealth.ih.domain.crm.CrmQuestion;
import cn.taihealth.ih.domain.crm.CrmTag;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import java.util.Objects;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.ListJoin;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 */
public class TagFilter implements SearchFilter<CrmQuestion> {

    private final String pattern;

    public TagFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    public Specification<CrmQuestion> toSpecification() {
        return (Specification<CrmQuestion>) (root, criteriaQuery, criteriaBuilder) -> {
            ListJoin<CrmQuestion, CrmTag> tags = root.joinList("tags", JoinType.LEFT);
            return criteriaBuilder.like(tags.get("name"), "%" + pattern + "%");
        };
    }

    @Override
    public String toExpression() {
        return "tagName:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof TagFilter)) {
            return false;
        }

        TagFilter rhs = (TagFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
