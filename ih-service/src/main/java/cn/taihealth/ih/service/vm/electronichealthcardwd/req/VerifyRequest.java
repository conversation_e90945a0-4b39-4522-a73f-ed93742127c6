package cn.taihealth.ih.service.vm.electronichealthcardwd.req;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 万达电子健康卡
 *   二维码验证请求体
 */
@Data
public class VerifyRequest {

    // 二维码内容
    private String ecContent;

    private Map<String, String> med;

    public VerifyRequest() {
        med = new HashMap<>();
        // 标准科室代码 0200-全科医疗科
        med.put("medKs", "0200");
        // 刷卡终端类型编号 99-其他
        med.put("medStepCode", "99");
        // 诊疗环节代码 000000-其他
        med.put("medStepName", "000000");
        med.put("useChannel", "1");
    }
}
