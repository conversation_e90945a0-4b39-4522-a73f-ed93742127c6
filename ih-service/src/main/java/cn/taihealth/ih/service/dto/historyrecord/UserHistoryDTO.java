package cn.taihealth.ih.service.dto.historyrecord;

import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.Gender;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.LinkedHashMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 */
@ApiModel
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class UserHistoryDTO extends UpdatableDTO {

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("姓名")
    private String fullName;

    //private User.Authority authority = User.Authority.USER;

    private boolean locked = false;

    @ApiModelProperty("身份证")
    private String identity;

    @ApiModelProperty("是否已实名认证")
    private boolean identified;

    @ApiModelProperty("实名认证时间")
    private String identifiedDate;

    @ApiModelProperty("生日")
    private String birthday;

    @ApiModelProperty("性别")
    private Gender gender = Gender.UNKNOWN;

    private User.AvatarType avatarType;

    @ApiModelProperty("头像地址")
    private String avatarUrl;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("个人能力")
    private String capabilities;

    @ApiModelProperty("是否同意服务协议")
    private boolean eulaAgreed;

    @ApiModelProperty("是否有健康档案密码")
    private boolean hasHealthRecordPassword;

    UserHistoryDTO(LinkedHashMap<String, Object> user) {
        this.username = (String) user.get("username");
        this.mobile = StringUtil.getDefault(user.getOrDefault("mobile", ""));
        this.fullName = StringUtil.getDefault(user.getOrDefault("fullName", ""));
        this.identity = (String) user.get("identity");
        this.identified = (boolean) user.get("identified");
        this.identifiedDate = StringUtil.getDefault(user.getOrDefault("identifiedDate", ""));
        this.birthday = (String) user.get("birthday");
        this.gender = Gender.valueOf(user.get("gender").toString());
        this.avatarUrl = StringUtil.getDefault(user.getOrDefault("avatarUrl", ""));
        this.address = StringUtil.getDefault(user.getOrDefault("address", ""));
        this.capabilities = StringUtil.getDefault(user.getOrDefault("capabilities", ""));
        this.eulaAgreed = (boolean) user.get("eulaAgreed");
        this.hasHealthRecordPassword = (boolean) user.get("hasHealthRecordPassword");
    }


}
