package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.hospital.ArticleCategory;
import cn.taihealth.ih.domain.hospital.Category;
import cn.taihealth.ih.service.vm.UserVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
public class CategoryDTO extends UpdatableDTO {

    private String name;

    private String slug;

    @ApiModelProperty(value = "背景色RGB值，不填默认是#FFFFFF（白色）", required = true)
    private String color = "#FFFFFF";

    @ApiModelProperty(value = "文字颜色RGB值，不填默认是#000000（黑色）", required = true)
    private String textColor = "#000000";

    private List<CategoryDTO> categories = Lists.newArrayList();

    private Long parentId;

    private String keyword;

    private Boolean isUse = true;

    private Integer orderValue;

    private UserVM userVM;

    @ApiModelProperty("排序规则")
    private CategorySortEnums categorySortEnums;

    public CategoryDTO() {
    }

    public CategoryDTO(Category category) {
        this(category, false);
    }

    public CategoryDTO(Category category, Boolean isTree) {
        super(category);
        if (category.getCategories() != null) {
            Collections.sort(category.getCategories());
            if (isTree != null && isTree) {
                this.categories = category.getCategories().stream().map(u -> new CategoryDTO(u, true)).collect(Collectors.toList());
            }
        }
        this.name = category.getName();
        this.slug = category.getSlug();
        this.color = category.getColor();
        this.textColor = category.getTextColor();
        if (category.getParent() != null) {
            this.parentId = category.getParent().getId();
        }
        this.keyword = category.getKeyword();
        this.isUse = category.getIsUse();
        this.orderValue = category.getOrderValue();
        if (category.getUser() != null) {
            this.userVM = new UserVM(category.getUser());
        }
    }
    public CategoryDTO(ArticleCategory articleCategory) {
        Category category = articleCategory.getCategory();
        this.name = category.getName();
        this.slug = category.getSlug();
        this.color = category.getColor();
        this.textColor = category.getTextColor();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public List<CategoryDTO> getCategories() {
        return categories;
    }

    public void setCategories(List<CategoryDTO> categories) {
        this.categories = categories;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Boolean getIsUse() {
        return isUse;
    }

    public void setIsUse(Boolean isUse) {
        this.isUse = isUse;
    }

    public Integer getOrderValue() {
        return orderValue;
    }

    public void setOrderValue(Integer orderValue) {
        this.orderValue = orderValue;
    }

    public CategorySortEnums getCategorySortEnums() {
        return categorySortEnums;
    }

    public void setCategorySortEnums(CategorySortEnums categorySortEnums) {
        this.categorySortEnums = categorySortEnums;
    }

    public UserVM getUserVM() {
        return userVM;
    }

    public void setUserVM(UserVM userVM) {
        this.userVM = userVM;
    }

    public enum CategorySortEnums {
        /**
         * 向上移动
         */
        ABOVE,
        /**
         * 向下移动
         */
        BELOW
    }
}