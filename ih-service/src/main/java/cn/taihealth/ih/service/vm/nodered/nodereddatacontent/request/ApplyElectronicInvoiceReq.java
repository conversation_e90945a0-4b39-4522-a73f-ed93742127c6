package cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-09-30
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class ApplyElectronicInvoiceReq implements Serializable {
    @ApiModelProperty("病人唯一码	Y")
    private String patid;
    @ApiModelProperty("收据号	Y	结算时返回的收据号")
    private String settle_id;
    @ApiModelProperty("发票抬头	N")
    private String Invoice_title;
}
