package cn.taihealth.ih.service.api.express;

import cn.taihealth.ih.domain.enums.ExpressType;
import cn.taihealth.ih.service.dto.express.ExpressData;
import cn.taihealth.ih.service.dto.express.ExpressData.EXPRESS100Config;
import cn.taihealth.ih.service.dto.express.ExpressData.SHUNFENGExpressConfig;
import cn.taihealth.ih.service.impl.express.Express100ServiceImpl;
import cn.taihealth.ih.service.impl.express.SHUNFENGServiceImpl;
public class ExpressServiceStrategy {

    private static ExpressServiceStrategy instance;

    private ExpressServiceStrategy() {
        // 私有构造函数，防止外部直接实例化
    }

    public static ExpressServiceStrategy getInstance() {
        if (instance == null) {
            instance = new ExpressServiceStrategy();
        }
        return instance;
    }

    public ExpressService getStrategy(ExpressData expressData) {
        ExpressType expressType = expressData.getExpressType();
        switch (expressType) {
            case Express100:
                return new Express100ServiceImpl(expressData.getExpress100Config());
            case SHUNFENG:
                return new SHUNFENGServiceImpl(expressData.getShunfengExpressConfig());
            default:
                return null;
        }
    }
}

