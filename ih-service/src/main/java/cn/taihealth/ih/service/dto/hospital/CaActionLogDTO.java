package cn.taihealth.ih.service.dto.hospital;

import cn.taihealth.ih.domain.enums.CaActionEnum;
import cn.taihealth.ih.domain.enums.CaLogStatusEnum;
import cn.taihealth.ih.domain.hospital.CaActionLog;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class CaActionLogDTO extends UpdatableDTO {

    @ApiModelProperty("互联网医院username")
    private String username;

    @ApiModelProperty("ca用户名")
    private String caUsername;

    @ApiModelProperty("医生姓名")
    private String medicalWorkerName;

    @ApiModelProperty("操作状态")
    private CaActionEnum caAction;

    @ApiModelProperty("操作时间，操作状态operationTime对应认证时间")
    private Date operationTime;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("日志")
    private CaLogStatusEnum status;

    public CaActionLogDTO(CaActionLog entity) {
        super(entity);
        this.username = entity.getUsername();
        this.caUsername = entity.getCaUsername();
        this.medicalWorkerName = entity.getMedicalWorkerName();
        this.caAction = entity.getCaAction();
        this.operationTime = entity.getOperationTime();
        this.description = entity.getDescription();
        this.status = entity.getStatus();
    }
}

