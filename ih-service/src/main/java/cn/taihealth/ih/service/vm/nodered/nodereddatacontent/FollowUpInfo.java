package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FollowUpInfo {

    @ApiModelProperty("随访标题")
    private String followup_title;
    @ApiModelProperty("随访预计执行时间")
    private String expected_execute_time;
    @ApiModelProperty("随访实际施行时间")
    private String follwup_time;
    @ApiModelProperty("随访推送时间")
    private String push_time;
    @ApiModelProperty("随访状态1.已填写 2。未填写")
    private String status;
    @ApiModelProperty("链接地址")
    private String url;
}
