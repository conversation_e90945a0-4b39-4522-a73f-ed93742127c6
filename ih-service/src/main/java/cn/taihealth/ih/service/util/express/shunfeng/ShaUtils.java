package cn.taihealth.ih.service.util.express.shunfeng;

import java.security.MessageDigest;

public class ShaUtils {

    private ShaUtils() {
    }

    public static String sha1(String str) {
        return sha(str, "sha1");
    }

    public static String sha512(String str) {
        return sha(str, "sha-512");
    }

    public static String sha(String str, String algorithm) {
        if (str == null || str.length() == 0) {
            return null;
        }
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

        try {
            MessageDigest mdTemp = MessageDigest.getInstance(algorithm);
            mdTemp.update(str.getBytes("UTF-8"));

            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buf);
        } catch (Exception e) {

            return null;
        }
    }

}
