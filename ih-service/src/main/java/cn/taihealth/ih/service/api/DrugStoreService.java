package cn.taihealth.ih.service.api;

import cn.taihealth.ih.commons.vm.PageHelperBean;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.service.dto.drug.DoctorDrugParamDTO;
import cn.taihealth.ih.service.dto.drugorder.*;
import cn.taihealth.ih.service.dto.express.ExpressData;
import cn.taihealth.ih.wechat.service.vm.drug.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 *
 */
public interface DrugStoreService {

    /**
     * 查询药品库药品
     *
     * @param page
     * @param name
     * @return
     */
    Page<DicMedInfoDTO> getDrugDict(Pageable page, String name, String hospitalCode);

    /**
     * 添加药品类别
     *
     * @param dto
     * @return
     */
    DicCategoryDTO createDrugCategory(DicCategoryDTO dto);

    /**
     * 修改药品类别
     *
     * @param dicCategoryId
     * @param dto
     * @return
     */
    DicCategoryDTO updateDrugCategory(long dicCategoryId, DicCategoryDTO dto);

    /**
     * 药品类别page查询
     */
    Page<DicCategoryDTO> getDicCategory(Integer pageNo, Integer size, String code);

    /**
     * 药品类别ids查询
     */
    List<DicCategoryDTO> getDicCategoryByIds(List<Long> ids, String code);

    /**
     * 删除药品类别
     */
    void delDicCategoryById(Long id, String hospitalCode);

    /**
     * 查询全部药品类别
     *
     * @param code
     * @return
     */
    List<DicCategoryDTO> getAllDicCategory(String code);

    /**
     * 添加剂量规格
     */
    DicDosageFormDTO addDicDosageForm(DicDosageFormDTO dto);

    /**
     * 修改剂量规格
     */
    DicDosageFormDTO updateDicDosageForm(Long dicDosageFormId, DicDosageFormDTO dto);


    /**
     * 剂量规格page查询
     */
    Page<DicDosageFormDTO> getDicDosageForms(Integer pageNo, Integer size, String code);

    /**
     * 剂量规格ids查询
     */
    List<DicDosageFormDTO> getDicDosageFormByIds(List<Long> ids, String code);

    /**
     * 剂量规格删除
     */
    void delDicDosageFormById(Long id, String hospitalCode);

    /**
     * 剂量规格all查询
     */
    List<DicDosageFormDTO> getAllDicDosageFormDTO(String code);

    /**
     * 添加剂量单位
     */
    DicDosageUnitDTO addDicDosageUnit(DicDosageUnitDTO dto);

    /**
     * 修改剂量单位
     */
    DicDosageUnitDTO updateDicDosageUnit(long dicDosageUnitId, DicDosageUnitDTO dto);

    /**
     * 剂量单位page查询
     */
    Page<DicDosageUnitDTO> getDicDosageUnits(Integer pageNo, Integer size, String code);

    /**
     * 剂量单位ids查询
     */
    List<DicDosageUnitDTO> getDicDosageUnitDTOByIds(List<Long> ids, String code);

    /**
     * 剂量单位删除
     */
    void delDicDosageUnitById(Long id, String hospitalCode);


    /**
     * 查询剂量单位列表
     */
    List<DicDosageUnitDTO> getAllDicDosageUnit(String code);

    /**
     * 添加医药信息
     */
    DicMedInfoDTO createDicMedInfo(DicMedInfoDTO dto);

    /**
     * 修改医药信息
     */
    DicMedInfoDTO updateDicMedInfo(long dicMedInfoId, DicMedInfoDTO dto);

    /**
     * 开处方时医药信息page查询
     */
    Page<DicMedInfoDTO> searchDrugDicts(Integer pageNo, Integer size, String query, String code, Hospital hospital);

    /**
     * 医药信息ids查询
     */
    List<DrugStoreDrugDictVM> getDrugDictsByIds(List<Long> ids, Hospital hospital);

    /**
     * 医药信息id查询
     */
    DicMedInfoDTO getDrugDictsById(Long id);

    DicMedInfoDTO getDrugDictsByCode(String id,  String hospitalCode);

    /**
     * 医药信息删除
     */
    void delDicMedInfoById(Long id, String code);

    /**
     * 添加包装单位
     */
    DicPackageUnitDTO addDicPackageUnit(DicPackageUnitDTO dto);

    /**
     * 修改包装单位
     */
    DicPackageUnitDTO updateDicPackageUnit(long dicPackageUnitId, DicPackageUnitDTO dto);

    /**
     * 包装单位page查询
     */
    Page<DicPackageUnitDTO> getDicPackageUnits(Integer pageNo, Integer size, String code);

    /**
     * 查询包装单位列表
     */
    List<DicPackageUnitDTO> getDicPackageUnitsByIds(List<Long> ids, String code);

    /**
     * 删除包装单位
     */
    void delDicPackageUnitById(Long id, String code);

    /**
     * 包装单位all查询
     */
    List<DicPackageUnitDTO> getAllDicPackageUnitDTO(String code);

    DicMedAdmFreqDTO addDicMedAdmFreq(DicMedAdmFreqDTO dto);

    DicMedAdmFreqDTO updateDicMedAdmFreq(long dicMedAdmFreqId, DicMedAdmFreqDTO dto);

    Page<DicMedAdmFreqDTO> getDicMedAdmFreqs(Integer pageNo, Integer size, String code);

    List<DicMedAdmFreqDTO> getDicMedAdmFreqsByIds(List<Long> ids, String code);

    void delDicMedAdmFreqById(Long id, String code);

    List<DicMedAdmFreqDTO> getAllDicMedAdmFreqDTO(String code);

    DicMedAdmRouteDTO addDicMedAdmRoute(DicMedAdmRouteDTO dto);

    DicMedAdmRouteDTO updateDicMedAdmRoute(long dicMedAdmRouteId, DicMedAdmRouteDTO dto);

    Page<DicMedAdmRouteDTO> getDicMedAdmRoutes(Integer pageNo, Integer size, String code);

    List<DicMedAdmRouteDTO> getDicMedAdmRoutesByIds(List<Long> ids, String code);

    void delDicMedAdmRouteById(Long id, String code);

    List<DicMedAdmRouteDTO> getAllDicMedAdmRouteDTO(String code);

    Page<DicMedInfoDTO> getDoctorDrugs(Pageable page, Long doctorId, String name, Hospital hospital);

    DicMedInfoDTO getDrugDetail(String drugCode);

    void saveDoctorDrug(DoctorDrugParamDTO dto);

    void delDoctorDrug(DoctorDrugParamDTO dto);

    void toPaying(OrderStatusChangeVM changeVM);

    void paySuccess(OrderStatusChangeVM changeVM);

    void refundSuccess(OrderStatusChangeVM changeVM);

    void recipeInvalid(OrderStatusChangeVM changeVM);

    // 获取已发货，已签收的药品订单
    PageHelperBean<OrderInfoAdminDTO> getDrugOrderPage(Integer pageNo, Integer size, String hospitalCode, String query);

    void syncHisDrugInfo(Hospital hospital);

    /**
     * 根据互联网医院处方id查询药品订单
     * @param prescriptionOrderId
     * @return
     */
    OrderInfoDTO getDrugStoreOrderInfo(long prescriptionOrderId);

    /**
     * 根据id查询药品订单
     * @param drugOrderId
     * @return
     */
    OrderInfoAdminDTO getDrugOrderById(long drugOrderId);

    /**
     * 查询订单处方详情
     * @param drugOrderId
     * @return
     */
    DrugPrescriptionDTO getDrugRecipeByOrderId(long drugOrderId);

    /**
     * 药品订单状态变更
     * @param change
     * @return
     */
    DrugPrescriptionDTO statusChange(OrderStatusChangeDTO change);

    /**
     * 根据物流公司和单号查询物流信息
     * @param hospitalCode
     * @param trackingType
     * @param trackingNumber
     * @param phone
     * @param expressData
     * @return
     */
    List<LogisticsRouteInfo> getLogisticsInfo(String hospitalCode, String trackingType, String trackingNumber,
                                              String phone, ExpressData expressData);
}
