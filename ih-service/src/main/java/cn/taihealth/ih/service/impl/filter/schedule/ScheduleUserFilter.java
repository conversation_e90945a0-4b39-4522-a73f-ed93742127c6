package cn.taihealth.ih.service.impl.filter.schedule;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.Schedule;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import java.util.Objects;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class ScheduleUserFilter implements SearchFilter<Schedule> {

    private final Long id;

    public ScheduleUserFilter(Long id) {
        this.id = id;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Schedule> toSpecification() {
        return new Specification<Schedule>() {
            @Override
            public Predicate toPredicate(Root<Schedule> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                Join<Schedule, User> listJoin = root.join("medicalWorker");
                return criteriaBuilder.equal(listJoin.get("user").get("id"), id);
            }
        };
    }

    @Override
    public String toExpression() {
        return "id:" + id;
    }

    @Override
    public boolean isValid() {
        return id != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof ScheduleUserFilter)) {
            return false;
        }

        ScheduleUserFilter rhs = (ScheduleUserFilter) other;
        return Objects.equals(id, rhs.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
