package cn.taihealth.ih.service.impl.filter.systemsuperviseplatform;

import cn.taihealth.ih.domain.cloud.SupervisePlatform;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class PlatformNameFilter implements SearchFilter<SupervisePlatform> {

    private final String name;

    public PlatformNameFilter(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<SupervisePlatform> toSpecification() {
        return Specifications.likeIgnoreCase("name", name);
    }

    @Override
    public String toExpression() {
        String str = " PlatformName:" + name;
        return str;
//        return not ? "-" + str : str;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof PlatformNameFilter)) {
            return false;
        }

        PlatformNameFilter rhs = (PlatformNameFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
