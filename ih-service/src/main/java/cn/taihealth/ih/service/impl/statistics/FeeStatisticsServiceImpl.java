package cn.taihealth.ih.service.impl.statistics;

import cn.taihealth.ih.bean.statistics.FeeResult;
import cn.taihealth.ih.commons.util.MathUtils;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.dao.StatisticsDaoHibernate;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.DateUnitForSelect;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.service.api.HospitalSettingService;
import cn.taihealth.ih.service.api.statistics.FeeStatisticsService;
import cn.taihealth.ih.service.vm.statistics.evaluation.FeeStatisticsChecksVM;
import cn.taihealth.ih.service.vm.statistics.evaluation.FeeStatisticsDaysVM;
import cn.taihealth.ih.service.vm.statistics.evaluation.FeeStatisticsDaysVM.FeeDaysCountVM;
import cn.taihealth.ih.service.vm.statistics.evaluation.FeeStatisticsDrugsVM;
import cn.taihealth.ih.service.vm.statistics.evaluation.FeeStatisticsVM;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: Moon
 * @Date: 2021/3/17 上午9:15
 */
@Service
public class FeeStatisticsServiceImpl implements FeeStatisticsService {

    private final StatisticsDaoHibernate statisticsDaoHibernate;
    private final List<OrderStatus> statusForStatistics = OrderStatus.getStatusForStatistics();
    private final HospitalSettingService hospitalSettingService;
    private final DeptRepository deptRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;

    public FeeStatisticsServiceImpl(StatisticsDaoHibernate statisticsDaoHibernate,
                                    HospitalSettingService hospitalSettingService,
                                    DeptRepository deptRepository,
                                    MedicalWorkerRepository medicalWorkerRepository) {
        this.statisticsDaoHibernate = statisticsDaoHibernate;
        this.hospitalSettingService = hospitalSettingService;
        this.deptRepository = deptRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
    }

    @Override
    public FeeStatisticsVM getStatisticsToday(Hospital hospital, Date startTime, Date endTime) {

        FeeStatisticsVM statToday = getStatVM(hospital, startTime, endTime);
        FeeStatisticsVM statYesterday = getStatVM(hospital, DateUtils.addDays(startTime, -1),
            DateUtils.addDays(endTime, -1));

        String clinicY = MathUtils.subtractAndDivision(statToday.getClinicFee(), statYesterday.getClinicFee());
        String checkY = MathUtils.subtractAndDivision(statToday.getChecksFee(), statYesterday.getChecksFee());
        String drugY = MathUtils.subtractAndDivision(statToday.getDrugFee(), statYesterday.getDrugFee());
        String totalYield = MathUtils.subtractAndDivision(statToday.getTotalFee(), statYesterday.getTotalFee());

        statToday.setClinicFeeYield(clinicY);
        statToday.setChecksFeeYield(checkY);
        statToday.setDrugFeeYield(drugY);
        statToday.setTotalFeeYield(totalYield);

        return statToday;
    }

    @Override
    public List<FeeStatisticsVM> getStatisticsDept(Hospital hospital, Date startTime, Date endTime) {
        List<FeeResult> feeResults = Lists.newArrayList();
        List<FeeResult> checksFeeDept = statisticsDaoHibernate.getChecksFeeDept(startTime, endTime, hospital.getId());
        List<FeeResult> clinicFeeDept = statisticsDaoHibernate
            .getClinicFeeDept(startTime, endTime, statusForStatistics, hospital.getId());
        List<FeeResult> drugFeeDept = statisticsDaoHibernate.getDrugFeeDept(startTime, endTime, hospital.getId());

        feeResults.addAll(checksFeeDept);
        feeResults.addAll(clinicFeeDept);
        feeResults.addAll(drugFeeDept);
        Map<Long, List<FeeResult>> map = feeResults.stream().collect(Collectors.groupingBy(FeeResult::getDeptId));
        return map.keySet().stream().map(u -> {
            FeeStatisticsVM feeStatisticsVM = new FeeStatisticsVM();
            Dept dept = deptRepository.getById(u);
            feeStatisticsVM.setDept(dept.getDeptName());
            List<FeeResult> results = map.get(u);
            int checksFee = results.stream().mapToInt(FeeResult::getChecksFee).sum();
            int clinicFee = results.stream().mapToInt(FeeResult::getClinicFee).sum();
            int drugFee = results.stream().mapToInt(FeeResult::getDrugFee).sum();
            feeStatisticsVM.setChecksFee(checksFee);
            feeStatisticsVM.setClinicFee(clinicFee);
            feeStatisticsVM.setDrugFee(drugFee);
            feeStatisticsVM.setTotalFee(drugFee + checksFee + clinicFee);
            return feeStatisticsVM;
        }).sorted(Comparator.comparing(FeeStatisticsVM::getTotalFee).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<FeeStatisticsVM> getStatisticsDoctor(Hospital hospital, Date startTime, Date endTime) {
        List<FeeResult> feeResults = Lists.newArrayList();
        List<FeeResult> checksFeeDoctor = statisticsDaoHibernate
            .getChecksFeeDoctor(startTime, endTime, hospital.getId());
        List<FeeResult> clinicFeeDoctor = statisticsDaoHibernate
            .getClinicFeeDoctor(startTime, endTime, statusForStatistics, hospital.getId());
        List<FeeResult> drugFeeDoctor = statisticsDaoHibernate.getDrugFeeDoctor(startTime, endTime, hospital.getId());

        feeResults.addAll(checksFeeDoctor);
        feeResults.addAll(clinicFeeDoctor);
        feeResults.addAll(drugFeeDoctor);
        Map<Long, List<FeeResult>> map = feeResults.stream().collect(Collectors.groupingBy(FeeResult::getDoctorId));
        return map.keySet().stream().map(u -> {
            FeeStatisticsVM feeStatisticsVM = new FeeStatisticsVM();
            MedicalWorker doctor = medicalWorkerRepository.getById(u);
            feeStatisticsVM.setDoctor(doctor.getUser().getFullName());
            List<FeeResult> results = map.get(u);
            int checksFee = results.stream().mapToInt(FeeResult::getChecksFee).sum();
            int clinicFee = results.stream().mapToInt(FeeResult::getClinicFee).sum();
            int drugFee = results.stream().mapToInt(FeeResult::getDrugFee).sum();
            feeStatisticsVM.setChecksFee(checksFee);
            feeStatisticsVM.setClinicFee(clinicFee);
            feeStatisticsVM.setDrugFee(drugFee);
            feeStatisticsVM.setTotalFee(drugFee + checksFee + clinicFee);
            return feeStatisticsVM;
        }).sorted(Comparator.comparing(FeeStatisticsVM::getTotalFee).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<FeeStatisticsDrugsVM> getStatisticsDrug(Hospital hospital, Date startTime, Date endTime) {
        List<FeeResult> results = statisticsDaoHibernate.getFeeGroupByDrug(startTime, endTime, hospital.getId());
        return results.stream().map(FeeStatisticsDrugsVM::new).collect(Collectors.toList());
    }

    @Override
    public List<FeeStatisticsChecksVM> getStatisticsChecks(Hospital hospital, Date startTime, Date endTime) {
        List<FeeResult> results = statisticsDaoHibernate
            .getFeeGroupByChecks(startTime, endTime, hospital.getId());
        return results.stream().map(FeeStatisticsChecksVM::new).collect(Collectors.toList());
    }

    @Override
    public FeeStatisticsVM getStatisticsTypes(Hospital hospital, Date startTime, Date endTime) {
        return getStatVM(hospital, startTime, endTime);
    }

    @Override
    public List<FeeStatisticsDaysVM> getStatisticsGroupByDate(
        Hospital hospital, Date startTime, Date endTime,
        DateUnitForSelect dateUnit) {
        String dateFormat = getDateFormatForMysqlByArg(dateUnit, startTime, endTime);

        List<FeeResult> checksFeeDate = statisticsDaoHibernate
            .getChecksFeeDate(startTime, endTime, hospital.getId(), dateFormat);
        List<FeeResult> clinicFeeDate = statisticsDaoHibernate
            .getClinicFeeDate(startTime, endTime, statusForStatistics, hospital.getId(), dateFormat);
        List<FeeResult> drugFeeDate = statisticsDaoHibernate
            .getDrugFeeDate(startTime, endTime, hospital.getId(), dateFormat);

        List<Date> dateList;
        if (dateUnit == DateUnitForSelect.CUSTOM) {
            dateList = TimeUtils.getDateListCustom(startTime, endTime);
        } else {
            dateList = TimeUtils.getDateListByDateUnit(dateUnit.getValue());
        }
        Map<Date, Integer> dateRatingMap = Maps.newHashMap();
        dateList.forEach(u -> dateRatingMap.put(u, 0));

        List<FeeStatisticsDaysVM> vms = Lists
            .newArrayList(new FeeStatisticsDaysVM("总收入"),
                new FeeStatisticsDaysVM("诊疗收入"),
                new FeeStatisticsDaysVM("药品收入"),
                new FeeStatisticsDaysVM("检查收入"));

        vms.forEach(u -> {
            switch (u.getName()) {
                case "总收入":
                    clinicFeeDate
                        .forEach(e -> dateRatingMap.put(e.getDateLabel(), e.getClinicFee()));
                    checksFeeDate.forEach(e -> dateRatingMap
                        .put(e.getDateLabel(), dateRatingMap.get(e.getDateLabel()) + e.getChecksFee()));
                    drugFeeDate.forEach(
                        e -> dateRatingMap.put(e.getDateLabel(), dateRatingMap.get(e.getDateLabel()) + e.getDrugFee()));
                    break;
                case "诊疗收入":
                    clinicFeeDate
                        .forEach(e -> dateRatingMap.put(e.getDateLabel(), e.getClinicFee()));
                    break;
                case "药品收入":
                    drugFeeDate.forEach(e -> dateRatingMap.put(e.getDateLabel(), e.getDrugFee()));
                    break;
                case "检查收入":
                    checksFeeDate.forEach(e -> dateRatingMap.put(e.getDateLabel(), e.getChecksFee()));
                    break;
            }

            List<FeeDaysCountVM> countVMs = dateRatingMap.keySet().stream().map(k -> {
                FeeDaysCountVM countVM = new FeeDaysCountVM();
                countVM.setDateLabel(k);
                countVM.setCount(dateRatingMap.get(k));
                return countVM;
            }).sorted(Comparator.comparing(FeeDaysCountVM::getDateLabel)).collect(Collectors.toList());
            u.setData(countVMs);
            dateList.forEach(d -> dateRatingMap.put(d, 0));
        });
        return vms;
    }

    private FeeStatisticsVM getStatVM(Hospital hospital, Date startTime, Date endTime) {
        Long clinicFeeLong = statisticsDaoHibernate
            .getClinicFee(startTime, endTime, statusForStatistics, hospital.getId());
        Long drugFeeLong = statisticsDaoHibernate.getDrugFee(startTime, endTime, hospital.getId());
        Long checksFeeLong = statisticsDaoHibernate.getChecksFee(startTime, endTime, hospital.getId());
        int clinicFee = 0;
        int drugFee = 0;
        int checksFee = 0;
        if (clinicFeeLong != null) {
            clinicFee = Math.toIntExact(clinicFeeLong);
        }
        if (drugFeeLong != null) {
            drugFee = Math.toIntExact(drugFeeLong);
        }
        if (checksFeeLong != null) {
            checksFee = Math.toIntExact(checksFeeLong);
        }
        int totalFee = clinicFee + drugFee + checksFee;
        return new FeeStatisticsVM(totalFee, clinicFee, drugFee, checksFee);
    }

    private String getDateFormatForMysqlByArg(DateUnitForSelect dateUnit, Date startTime, Date endTime) {
        String dateFormat;
        switch (dateUnit) {
            case WEEK:
            case MONTH:
                dateFormat = "%Y-%m-%d";
                break;
            case YEAR:
            case OLD_YEAR:
                dateFormat = "%Y-%m-01";
                break;
            case CUSTOM:
                if (DateUtils.addDays(startTime, 31).getTime() > endTime.getTime()) {
                    dateFormat = "%Y-%m-%d";
                } else {
                    dateFormat = "%Y-%m-01";
                }
                break;
            default:
                throw new IllegalArgumentException("Unknown date");
        }
        return dateFormat;
    }


}
