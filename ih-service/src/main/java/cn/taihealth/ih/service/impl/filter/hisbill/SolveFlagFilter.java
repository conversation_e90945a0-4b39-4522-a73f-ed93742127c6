package cn.taihealth.ih.service.impl.filter.hisbill;

import cn.taihealth.ih.domain.hospital.HisBill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class SolveFlagFilter implements SearchFilter<HisBill> {
    private final Boolean value;

    public SolveFlagFilter(Boolean value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    // solveFlag
    @Override
    public Specification<HisBill> toSpecification() {
        if (value) {
            return Specifications.isTrue("solveFlag");
        } else {
            return Specifications.isFalse("solveFlag");
        }
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
