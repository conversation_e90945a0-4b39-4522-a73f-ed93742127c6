package cn.taihealth.ih.service.vm.hospital;

import cn.taihealth.ih.domain.hospital.RecommendDept;
import cn.taihealth.ih.service.dto.DeptDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class RecommendDeptVM extends DeptDTO {


    @ApiModelProperty(value = "图标标签最长3个字符")
    @Size(max = 3, message = "图标标签最长3个字符")
    private String iconTag;

    @ApiModelProperty(value = "排序序号", required = true)
    @NotBlank
    @Size(max = 64, message = "排序序号")
    private int sortNum;

    public RecommendDeptVM(RecommendDept recommendDept) {
        super(recommendDept.getDept());
        this.iconTag =recommendDept.getIconTag();
        this.sortNum =recommendDept.getSortNum();
    }
}
