package cn.taihealth.ih.service.vm.hospital;

import cn.taihealth.ih.domain.enums.HospitalAppCategory;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.enums.Source;
import cn.taihealth.ih.domain.hospital.HospitalAppSetting;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class HospitalAppSettingVM extends AbstractEntityDTO {

    @ApiModelProperty("应用编码")
    private String code;

    @ApiModelProperty("应用名称")
    @NotNull(message = "应用名称必填")
    private String name;

    @ApiModelProperty("应用来源")
    @Enumerated(EnumType.STRING)
    private Source source = Source.SYSTEM;

    @ApiModelProperty("应用分类")
    private HospitalAppCategory appCategory;

    @ApiModelProperty("应用图标")
    private String icon;

    @ApiModelProperty("跳转类型")
    private String jumpType;

    @ApiModelProperty("appId")
    private String appId;

    @ApiModelProperty("跳转url")
    private String url;

    @ApiModelProperty("排序")
    private int orderNumber;

    @ApiModelProperty("是否开通")
    private boolean enabled = false;

    @ApiModelProperty("是否显示")
    private boolean show = true;

    @ApiModelProperty("是否需要登录才能跳转")
    private boolean needAuth = true;

    @ApiModelProperty("支持的客户端平台,PlatformTypeEnum")
    private List<PlatformTypeEnum> clientPlatform = Lists.newArrayList();

    public HospitalAppSettingVM(HospitalAppSetting setting) {
        super(setting);
        this.code = setting.getCode();
        this.name = setting.getName();
        this.source = setting.getSource();
        this.appCategory = setting.getAppCategory();
        this.icon = StringUtils.isBlank(setting.getIcon()) ? setting.getDefaultIcon() : setting.getIcon();
        this.jumpType = setting.getJumpType();
        this.appId = setting.getAppId();
        this.url = setting.getUrl();
        this.orderNumber = setting.getOrderNumber();
        this.enabled = setting.isEnabled();
        this.show = setting.isShow();
        this.needAuth = setting.isNeedAuth();
        this.clientPlatform = setting.getClientPlatform();
    }

    public HospitalAppSettingVM(HospitalServiceSettingVM setting) {
        this.setId(setting.getId());
        this.code = setting.getCode();
        this.name = setting.getName();
        this.source = setting.getSource();
        this.appCategory = setting.getAppCategory();
        this.icon = setting.getIcon();
        this.jumpType = setting.getJumpType();
        this.appId = setting.getAppId();
        this.url = setting.getUrl();
        this.orderNumber = setting.getOrderNumber();
        this.enabled = true;
        this.show = setting.isShow();
        this.needAuth = setting.isNeedAuth();
        this.clientPlatform = setting.getClientPlatform();
    }

}
