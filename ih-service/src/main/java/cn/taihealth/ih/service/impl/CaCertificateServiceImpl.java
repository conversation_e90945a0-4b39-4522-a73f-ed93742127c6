package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.ca.been.CaSignParam;
import cn.taihealth.ih.ca.been.QrcodeLogin;
import cn.taihealth.ih.ca.been.shukey.UKeySignedCallBack;
import cn.taihealth.ih.ca.been.xinan.UploadPdfResponse;
import cn.taihealth.ih.ca.enums.SignatureType;
import cn.taihealth.ih.ca.service.CaService;
import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import cn.taihealth.ih.commons.util.PDFUtil;
import cn.taihealth.ih.commons.util.Snowflake64;
import cn.taihealth.ih.domain.MedicalCase;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalCAEnum;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.SettingKey;
import cn.taihealth.ih.domain.enums.UploadType;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.MedicalCaseRepository;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.repo.hospital.order.DiagnosisCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionRepository;
import cn.taihealth.ih.service.api.CaCertificateService;
import cn.taihealth.ih.service.api.LockService;
import cn.taihealth.ih.service.api.UploadResource;
import cn.taihealth.ih.service.api.UploadService;
import cn.taihealth.ih.service.util.FileUtils;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.SettingsHelper;
import cn.taihealth.ih.service.vm.ca.*;
import com.gitq.jedi.common.web.UrlUtils;
import com.gitq.jedi.context.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CaCertificateServiceImpl implements CaCertificateService {

    private final MedicalCaseRepository medicalCaseRepository;
    private final PrescriptionRepository prescriptionRepository;
    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final PrescriptionOrderCaRepository prescriptionOrderCaRepository;
    private final DiagnosisCaRepository diagnosisCaRepository;
    private final UploadRepository uploadRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final UploadService uploadService;
    private final LockService lockService;
    private final ApplicationProperties applicationProperties;

    public CaCertificateServiceImpl(MedicalCaseRepository medicalCaseRepository,
                                    MedicalWorkerRepository medicalWorkerRepository,
                                    PrescriptionOrderRepository prescriptionOrderRepository,
                                    PrescriptionOrderCaRepository prescriptionOrderCaRepository,
                                    PrescriptionRepository prescriptionRepository,
                                    DiagnosisCaRepository diagnosisCaRepository,
                                    UploadRepository uploadRepository,
                                    UploadService uploadService,
                                    LockService lockService,
                                    ApplicationProperties applicationProperties) {
        this.medicalCaseRepository = medicalCaseRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.prescriptionRepository = prescriptionRepository;
        this.applicationProperties = applicationProperties;
        this.prescriptionOrderCaRepository = prescriptionOrderCaRepository;
        this.prescriptionOrderRepository = prescriptionOrderRepository;
        this.uploadRepository = uploadRepository;
        this.uploadService = uploadService;
        this.diagnosisCaRepository = diagnosisCaRepository;
        this.lockService = lockService;
    }

    @Override
    public MedicalWorkerCaQrcodeVM loginWithQrcode(Hospital hospital, String random) {
        HospitalCAEnum caEnum = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);
        CaService caService = AppContext.getInstance(caEnum.getServiceName(), CaService.class);
        String url = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL);
        String appId = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_APP_ID);
        QrcodeLogin qrcodeLogin = caService.loginWithQrcode(url, appId, random);
        if (qrcodeLogin.isNeedRefresh()) {
            // 二维码过期
            return new MedicalWorkerCaQrcodeVM(false, false);
        }
        if (!qrcodeLogin.isLogin()) {
            return new MedicalWorkerCaQrcodeVM(false);
        }
        // 信安 username规则: 用户id_hospitalCode_PC_随机数
        String uid = qrcodeLogin.getCaUid();
        String medicalWorkerId = uid.split("_")[0];
        User user = medicalWorkerRepository.getById(Long.valueOf(medicalWorkerId)).getUser();
        return new MedicalWorkerCaQrcodeVM(user);
    }

    @Override
    public SignaturePdfVM prescriptionOrderSignatureXinan(Hospital hospital, User user, long prescriptionOrderId, SignatureType signatureType, String accountId) {
        return lockService.executeWithLockThrowError("lock.cn.taihealth.prescriptionOrderSignature.prescriptionOrderId." + prescriptionOrderId, () ->
                prescriptionOrderSignature(hospital, user, prescriptionOrderId, signatureType, accountId)
        );
    }

    private SignaturePdfVM prescriptionOrderSignature(Hospital hospital, User user, long prescriptionOrderId, SignatureType signatureType, String accountId) {
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(prescriptionOrderId);
        OrderManager orderManager = AppContext.getInstance(OrderManager.class);
        List<Prescription> prescriptionResult = prescriptionRepository.findByPrescriptionOrderId(prescriptionOrder.getId());
        int drugSize = prescriptionResult.size();
        if (drugSize == 0) {
            throw ErrorType.PRESCRIPTION_DRUG_NOT_FOND.toProblem();
        }
        PrescriptionOrderCa prescriptionOrderCa = prescriptionOrderCaRepository.findOneByPrescriptionOrderIdAndEnabled(prescriptionOrderId, true)
                .orElse(new PrescriptionOrderCa(hospital.getId(), prescriptionOrder.getOrder().getId(), prescriptionOrderId));
        CaService caService = AppContext.getInstance(HospitalCAEnum.XINAN.getServiceName(), CaService.class);
        File pdf = null;

        if (signatureType == SignatureType.DOCTOR || signatureType == null) {
            //医生签名图片
            File doctorSignatureFile = null;
            // 医生签章或一次全部签章
            // 获取/创建签署账号
            // 医生
            MedicalWorker doctor = prescriptionOrder.getDoctor();
            if (Objects.requireNonNull(doctor).getSignature() == null) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("医生没有上传签章");
            }
            //画pdf
            String html = orderManager.drawPrescriptionOrder(prescriptionOrder);
            OutputStream prescriptionOut = null;
            //还未盖过章的处方PDF
            File prescriptionFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_" + prescriptionOrder.getPatient().getName() + ".pdf");
            try {
                prescriptionOut = new FileOutputStream(prescriptionFile);
                PDFUtil.writeStringToOutputStreamAsPDF(html, prescriptionOut);
                prescriptionOut.close();

                //盖好了医生章的处方PDF
                File prescriptionDoctorFile = new File(applicationProperties.getHome(),
                        "temp/" + prescriptionOrder.getId() + "_医师章" + ".pdf");
                doctorSignatureFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_" +
                        prescriptionOrder.getDoctor().getUser().getFullName() + "." + FileUtils.mediaTypeToExtension(doctor.getSignature().getMimeType()));
                uploadService.download(doctor.getSignature(), doctorSignatureFile);
                CaSignParam signParam = new CaSignParam();
                signParam.setPdf(prescriptionFile);
                signParam.setPicture(doctorSignatureFile);
                caService.prescriptionSignature(prescriptionDoctorFile.getPath(), drugSize, SignatureType.DOCTOR, signParam);
                UploadPdfResponse uploadPdfResponse = caService.uploadPdf(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL),
                        prescriptionDoctorFile, accountId);
                Upload upload = uploadService.upload(user, UploadResource.of(prescriptionFile, UploadType.PRESCRIPTION, null));
                prescriptionOrderCa.setDoctorPdfNoCaId(upload.getId());
                prescriptionOrderCa.setDoctorId(doctor.getId());
                prescriptionOrderCa.setDoctorCaUsername(accountId);
                prescriptionOrderCa.setDoctorPdfUid(uploadPdfResponse.getRandom());
                prescriptionOrderCa.setDoctorPdfFileHash(uploadPdfResponse.getFileHash());
                prescriptionOrderCaRepository.save(prescriptionOrderCa);
                pdf = prescriptionDoctorFile;
                if (signatureType == SignatureType.DOCTOR) {
                    prescriptionDoctorFile.delete();
                    return new SignaturePdfVM(uploadPdfResponse.getImgCode(), uploadPdfResponse.getRandom(), uploadPdfResponse.getFileHash());
                }
            } catch (Exception e) {
                log.error("处方生成失败", e);
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("处方生成失败");
            } finally {
                if (prescriptionOut != null) {
                    try {
                        prescriptionOut.close();
                    } catch (IOException ignored) {
                    }
                }
                prescriptionFile.delete();
                if (doctorSignatureFile != null) {
                    doctorSignatureFile.delete();
                }
            }
        }

        if (signatureType == SignatureType.DOCTOR_REVIEW || signatureType == null) {
            // 医生签章或一次全部签章
            //获取/创建签署账号
            //医生
            MedicalWorker doctorReview = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user)
                    .orElseThrow(ErrorType.BAD_REQUEST_ERROR::toProblem);
            if (Objects.requireNonNull(doctorReview).getSignature() == null) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("药师没有上传签章");
            }
            //还未盖过章的处方PDF
            File prescriptionFile = pdf;
            //盖好了医生章的处方PDF
            File prescriptionDoctorFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_药师章" + ".pdf");
            File doctorReviewSignatureFile = null;
            //盖好了章的最终处方
            File prescriptionHospitalFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_CA_ESIGN_SUCCESS" + ".pdf");
            // 医院公章
            File commonSealFile = null;
            try {
                if (prescriptionFile == null) {
                    prescriptionFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_医师章" + ".pdf");
                    if (prescriptionOrderCa.getDoctorPdfId() == null) {
                        // 医生提交时CA开关时关闭的，药师提交时CA开关时打开的，会出现没有医生签章的情况，这个时候，使用没有签过章的pdf，如果旧数据没有签过章的pdf不存在，那么重新生成一个新的pdf
                        if (prescriptionOrder.getNoCaPdf() == null) {
                            String html = AppContext.getInstance(OrderManager.class).drawPrescriptionOrder(prescriptionOrder);
                            String pdfPath = UrlUtils.concatSegments(applicationProperties.getHome(), "pdf", Snowflake64.Holder.INSTANCE.nextId() + ".pdf");
                            try (OutputStream prescriptionOut = new FileOutputStream(pdfPath)) {
                                PDFUtil.writeStringToOutputStreamAsPDF(html, prescriptionOut);
                                Upload upload = uploadService.upload(user, UploadResource.of(prescriptionFile, UploadType.PRESCRIPTION, null));
                                prescriptionOrder.setNoCaPdf(upload);
                                prescriptionOrderRepository.save(prescriptionOrder);
                            } catch (Exception e) {
                                log.error("生成pdf失败: ", e);
                            }
                        } else {
                            uploadService.download(prescriptionOrder.getNoCaPdf(), prescriptionFile);
                        }

                    } else {
                        uploadService.download(uploadRepository.getById(prescriptionOrderCa.getDoctorPdfId()), prescriptionFile);
                    }
                }
                doctorReviewSignatureFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_" +
                        doctorReview.getUser().getFullName()+ "." + FileUtils.mediaTypeToExtension(doctorReview.getSignature().getMimeType()));
                //医生签名图片
                uploadService.download(doctorReview.getSignature(), doctorReviewSignatureFile);
                CaSignParam signParam = new CaSignParam();
                signParam.setPdf(prescriptionFile);
                signParam.setPicture(doctorReviewSignatureFile);
                caService.prescriptionSignature(prescriptionDoctorFile.getPath(), drugSize, SignatureType.DOCTOR_REVIEW, signParam);

                String commonSealUrl = prescriptionOrder.getHospital().getCommonSealUrl();
                if (StringUtils.isNotBlank(commonSealUrl)) {
                    try {
                        commonSealFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId()
                                + "_" + Snowflake64.Holder.INSTANCE.nextId() + FileUtils.getFileName(commonSealUrl));
                        OkHttpUtils.download(commonSealUrl, commonSealFile);
                        signParam.setPdf(prescriptionDoctorFile);
                        signParam.setPicture(doctorReviewSignatureFile);
                        caService.prescriptionSignature(prescriptionHospitalFile.getPath(), drugSize, SignatureType.HOSPITAL, signParam);
                    } catch (Exception e) {
                        log.info("医院公章下载失败", e);
                        prescriptionHospitalFile = prescriptionDoctorFile;
                    }
                } else {
                    prescriptionHospitalFile = prescriptionDoctorFile;
                }

                UploadPdfResponse uploadPdfResponse = caService.uploadPdf(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL),
                        prescriptionHospitalFile, accountId);

                Upload upload = uploadService.upload(user, UploadResource.of(prescriptionHospitalFile, UploadType.PRESCRIPTION, null));
                prescriptionOrderCa.setDoctorReviewNoCaId(upload.getId());
                prescriptionOrderCa.setDoctorReviewId(doctorReview.getId());
                prescriptionOrderCa.setDoctorReviewCaUsername(accountId);
                prescriptionOrderCa.setDoctorReviewPdfUid(uploadPdfResponse.getRandom());
                prescriptionOrderCa.setDoctorReviewPdfFileHash(uploadPdfResponse.getFileHash());
                prescriptionOrderCaRepository.save(prescriptionOrderCa);
                return new SignaturePdfVM(uploadPdfResponse.getImgCode(), uploadPdfResponse.getRandom(), uploadPdfResponse.getFileHash());
            } finally {
                prescriptionFile.delete();
                prescriptionDoctorFile.delete();
                if (doctorReviewSignatureFile != null) {
                    doctorReviewSignatureFile.delete();
                }
                prescriptionHospitalFile.delete();
                if (commonSealFile != null) {
                    commonSealFile.delete();
                }
            }

        }
        throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("方法未实现");
    }

    @Override
    public CaUnsignedDiagnosisVM prescriptionsToPdf(long orderId) {
        return lockService.executeWithLockThrowError("lock.cn.taihealth.diagnosisSignature.orderId." + orderId ,() -> {
            List<PrescriptionOrder> prescriptionOrders = AppContext.getInstance(PrescriptionOrderRepository.class).findByOrderIdAndEnabled(orderId, true);
            if (prescriptionOrders.isEmpty()) {
                return null;
            }
            PrescriptionOrder prescriptionOrder = prescriptionOrders.get(0);
            String signRaw = getDiagnosisSignRaw(prescriptionOrder.getPatient(), prescriptionOrder.getId());
            prescriptionOrder.setSignRaw(signRaw);
            prescriptionOrderRepository.save(prescriptionOrder);
            return new CaUnsignedDiagnosisVM(null, signRaw);
        });

    }

    /**
     * 生成处方签名原文
     * @return
     */
    private String getDiagnosisSignRaw(Patient patient, long prescriptionOrderId) {
        // 电子处方的签名原文应包含患者信息、药品信息、药品用法等关键信息。不
        // 可仅使用流水号、数据库 ID 标识等无意义的数据作为签名原文
        StringBuilder signRaw = new StringBuilder(patient.getName() + patient.getGender().getValue());
        List<Prescription> prescriptionResult = prescriptionRepository.findByPrescriptionOrderId(prescriptionOrderId);
        for (Prescription prescription : prescriptionResult) {
            signRaw.append(prescription.getDrugName());
            signRaw.append(prescription.getDosageSpec() == null ? "/" : prescription.getDosageSpec());
            signRaw.append(prescription.getQuantity());
            signRaw.append(prescription.getUnit());
            signRaw.append(prescription.getSingle());
            signRaw.append(prescription.getSingleUnit());
            signRaw.append(prescription.getUseFrequency());
            signRaw.append(prescription.getUseage());
            signRaw.append(prescription.getTimes()).append(prescription.getTreatmentUnit().getName());
        }
        return signRaw.toString();
    }

    @Override
    public SignaturePdfVM prescriptionOrderSignatureSH(Hospital hospital, User user, long prescriptionOrderId, SignatureType signatureType, CaPrescriptionParam param) {
        return lockService.executeWithLockThrowError("lock.cn.taihealth.prescriptionOrderSignature.prescriptionOrderId." + prescriptionOrderId, () ->
                prescriptionOrderSignature(hospital, user, prescriptionOrderId, signatureType, param)
        );
    }

    private SignaturePdfVM prescriptionOrderSignature(Hospital hospital, User user, long prescriptionOrderId, SignatureType signatureType, CaPrescriptionParam caParam) throws Exception {
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(prescriptionOrderId);
        OrderManager orderManager = AppContext.getInstance(OrderManager.class);
        List<Prescription> prescriptionResult = prescriptionRepository.findByPrescriptionOrderId(prescriptionOrder.getId());
        int drugSize = prescriptionResult.size();
        if (drugSize == 0) {
            throw ErrorType.PRESCRIPTION_DRUG_NOT_FOND.toProblem();
        }
        PrescriptionOrderCa prescriptionOrderCa = prescriptionOrderCaRepository.findOneByPrescriptionOrderIdAndEnabled(prescriptionOrderId, true)
                .orElse(new PrescriptionOrderCa(hospital.getId(), prescriptionOrder.getOrder().getId(), prescriptionOrderId));
        CaService caService = AppContext.getInstance(caParam.getHospitalCA().getServiceName(), CaService.class);

        if (signatureType == SignatureType.DOCTOR || signatureType == null) {
            // 医生签章或一次全部签章
            // 获取/创建签署账号
            // 医生
            MedicalWorker doctor = prescriptionOrder.getDoctor();
            if (Objects.requireNonNull(doctor).getSignature() == null) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("医生没有上传签章");
            }
            //画pdf
            String html = orderManager.drawPrescriptionOrder(prescriptionOrder);
            //还未盖过章的处方PDF
            File prescriptionFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_" + prescriptionOrder.getPatient().getName() + ".pdf");
            try {
                OutputStream prescriptionOut = new FileOutputStream(prescriptionFile);
                PDFUtil.writeStringToOutputStreamAsPDF(html, prescriptionOut);
                prescriptionOut.close();
                Upload upload = uploadService.upload(user, UploadResource.of(prescriptionFile, UploadType.PRESCRIPTION, null));
                prescriptionOrderCa.setDoctorPdfNoCaId(upload.getId());
                prescriptionOrderCa.setDoctorId(doctor.getId());
                prescriptionOrderCa.setDoctorPdfUid(Snowflake64.Holder.INSTANCE.nextId() + "");
                prescriptionOrderCa.setDoctorSign(caParam.getApiSign());
                prescriptionOrderCa.setDoctorPdfId(null);
                CaSignParam signParam = new CaSignParam();
                signParam.setHost(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SEAL_URL));
                signParam.setRedirectUrl(UrlUtils.concatSegments(SettingsHelper.getString(SettingKey.API_SERVER_URL),
                        "/hospital/ca/orders/", prescriptionOrder.getOrder().getId() + "", "/prescriptions/signed"));
                signParam.setApiKey(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_KEY));
                signParam.setApiSecret(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SECRET));
                signParam.setSealStrategyId(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_SEAL_STRATEGY_ID));
//            signParam.setApiSign(caParam.getApiSign());
                signParam.setDocumentNo(prescriptionOrderCa.getDoctorPdfUid());
                byte[] signature = uploadService.read(doctor.getSignature());
                signParam.setPictureBase64(Base64.getEncoder().encodeToString(signature));

                try (InputStream in = new FileInputStream(prescriptionFile)) {
                    signParam.setPdfBase64(Base64.getEncoder().encodeToString(in.readAllBytes()));
                } finally {
                    FileUtils.delete(prescriptionFile);
                }
                String signUrl = caService.prescriptionSignature(null, drugSize, SignatureType.DOCTOR, signParam);
                prescriptionOrderCaRepository.save(prescriptionOrderCa);
                SignaturePdfVM signaturePdf = new SignaturePdfVM();
                signaturePdf.setSignUrl(signUrl);
                return signaturePdf;
            } finally {
                prescriptionFile.delete();
            }
        }

        if (signatureType == SignatureType.DOCTOR_REVIEW) {
            MedicalWorker doctorReview = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user)
                    .orElseThrow(ErrorType.BAD_REQUEST_ERROR::toProblem);
            if (Objects.requireNonNull(doctorReview).getSignature() == null) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("药师没有上传签章");
            }
            //还未盖过章的处方PDF
            File prescriptionFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_医师章" + ".pdf");
            File prescriptionTimeFile = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_药师审核时间" + ".pdf");
            try {
                uploadService.download(uploadRepository.getById(prescriptionOrderCa.getDoctorPdfId()), prescriptionFile);

                CaSignParam signParam = new CaSignParam();
                signParam.setPdf(prescriptionFile);
                caService.prescriptionSignatureTime(prescriptionTimeFile.getPath(), drugSize, SignatureType.DOCTOR_REVIEW, signParam);
                Upload upload = uploadService.upload(user, UploadResource.of(prescriptionTimeFile, UploadType.PRESCRIPTION, null));
                prescriptionOrderCa.setDoctorReviewNoCaId(upload.getId());
                prescriptionOrderCa.setDoctorReviewId(doctorReview.getId());
                prescriptionOrderCa.setDoctorReviewPdfUid(Snowflake64.Holder.INSTANCE.nextId() + "");
                prescriptionOrderCa.setDoctorReviewPdfId(null);
                prescriptionOrderCa.setFinalPdfId(null);

                signParam.setHost(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SEAL_URL));
                signParam.setRedirectUrl(UrlUtils.concatSegments(SettingsHelper.getString(SettingKey.API_SERVER_URL),
                        "/hospital/ca/orders/", prescriptionOrder.getOrder().getId() + "", "/pharmacist/signed"));
                signParam.setApiKey(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_KEY));
                signParam.setApiSecret(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SECRET));
                signParam.setSealStrategyId(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_SEAL_STRATEGY_ID));
//            signParam.setApiSign(caParam.getApiSign());
                signParam.setDocumentNo(prescriptionOrderCa.getDoctorReviewPdfUid());
                byte[] signature = uploadService.read(doctorReview.getSignature());
                signParam.setPictureBase64(Base64.getEncoder().encodeToString(signature));
                signParam.setPdf(null);
                try (InputStream in = new FileInputStream(prescriptionTimeFile)) {
                    signParam.setPdfBase64(Base64.getEncoder().encodeToString(in.readAllBytes()));
                }
                String signUrl = caService.prescriptionSignature(null, drugSize, SignatureType.DOCTOR, signParam);
                prescriptionOrderCaRepository.save(prescriptionOrderCa);
                SignaturePdfVM signaturePdf = new SignaturePdfVM();
                signaturePdf.setSignUrl(signUrl);
            } finally {
                prescriptionFile.delete();
                prescriptionTimeFile.delete();
            }





        }

        throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("方法未实现");
    }

    /**
     * 检查签章是否成功
     * @param hospital
     * @param random
     * @return
     */
    @Override
    public boolean checkSignatureSuccessXinan(Hospital hospital, String random) {
        CaService caService = AppContext.getInstance(HospitalCAEnum.XINAN.getServiceName(), CaService.class);
        String host = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL);
        return caService.checkSignature(host, random);
    }

    @Override
    public void uploadDoctorSignaturePdfXinan(Hospital hospital, User user, PdfTicketVM ticket) {
        long prescriptionOrderId = ticket.getPrescriptionOrderId();
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(prescriptionOrderId);
        PrescriptionOrderCa prescriptionOrderCa = prescriptionOrderCaRepository.findOneByPrescriptionOrderIdAndEnabled(prescriptionOrderId,
                        true).orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);

        File file = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_医生章_1" + ".pdf");
        try {
            CaService caService = AppContext.getInstance(HospitalCAEnum.XINAN.getServiceName(), CaService.class);
            String host = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL);
            CaSignParam signParam = new CaSignParam();
            signParam.setHost(host);
            caService.downloadPdf(file, ticket.getUrl(), signParam);
            UploadResource uploadResource = UploadResource.of(file, UploadType.PRESCRIPTION, null);
            Upload upload = uploadService.upload(user, uploadResource);
            prescriptionOrderCa.setDoctorPdfId(upload.getId());
            prescriptionOrderCa.setDoctorDigestValue(ticket.getDigestValue());
            prescriptionOrderCa.setFinalPdfId(upload.getId());
            prescriptionOrderCa.setFinalDigestValue(ticket.getDigestValue());
            prescriptionOrderCaRepository.save(prescriptionOrderCa);
            prescriptionOrder.setCaPdf(upload);
            prescriptionOrderRepository.save(prescriptionOrder);
        } finally {
            file.deleteOnExit();
        }
    }

    @Override
    public void uploadDoctorReviewSignaturePdfXinan(Hospital hospital, User user, PdfTicketVM ticket) {
        long prescriptionOrderId = ticket.getPrescriptionOrderId();
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(prescriptionOrderId);
        PrescriptionOrderCa prescriptionOrderCa = prescriptionOrderCaRepository.findOneByPrescriptionOrderIdAndEnabled(prescriptionOrderId,
                        true).orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);

        File file = new File(applicationProperties.getHome(), "temp/" + prescriptionOrder.getId() + "_医师章_1" + ".pdf");
        try {
            CaService caService = AppContext.getInstance(HospitalCAEnum.XINAN.getServiceName(), CaService.class);
            String host = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL);
            CaSignParam signParam = new CaSignParam();
            signParam.setHost(host);
            caService.downloadPdf(file, ticket.getUrl(), signParam);
            UploadResource uploadResource = UploadResource.of(file, UploadType.PRESCRIPTION, null);
            Upload upload = uploadService.upload(user, uploadResource);
            prescriptionOrderCa.setDoctorReviewPdfId(upload.getId());
            prescriptionOrderCa.setFinalPdfId(upload.getId());
            prescriptionOrderCa.setDoctorReviewDigestValue(ticket.getDigestValue());
            prescriptionOrderCa.setFinalDigestValue(ticket.getDigestValue());
            prescriptionOrderCaRepository.save(prescriptionOrderCa);
            prescriptionOrder.setCaPdf(upload);
            prescriptionOrderRepository.save(prescriptionOrder);
        } finally {
            file.deleteOnExit();
        }
    }

    @Override
    public SignaturePdfVM diagnosisSignatureXinan(Hospital hospital, User user, long orderId, String accountId) {
        return lockService.executeWithLockThrowError("lock.cn.taihealth.diagnosisSignature.orderId." + orderId ,() -> {
            MedicalCase medicalCase = medicalCaseRepository.findByOrderId(orderId).orElseThrow(ErrorType.MEDICAL_CASE_BLANK::toProblem);
            Order order = medicalCase.getOrder();
            MedicalWorker doctor = order.getDoctor();
            OrderManager orderManager = AppContext.getInstance(OrderManager.class);
            String html = orderManager.drawDiagnosis(medicalCase);
            CaService caService = AppContext.getInstance(HospitalCAEnum.XINAN.getServiceName(), CaService.class);
            // 还未盖过章的诊断单
            String filePath = UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                    "diagnosis_" + order.getId() + ".pdf");
            File diagnosisFile = new File(filePath);
            // 医生签名图片
            File doctorSignatureFile = null;
            // 医院章
            File hospitalSignatureFile = null;
            String signedDiagnosisPath = UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                    "diagnosis_" + order.getId() + "_盖了医生章的.pdf");

            String signedPath = UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                    "diagnosis_" + order.getId() + "_盖了医院章的.pdf");
            File signedDiagnosisFile = new File(signedDiagnosisPath);
            File signedFile = new File(signedPath);
            try (OutputStream diagnosisFileOut = new FileOutputStream(diagnosisFile)) {
                PDFUtil.writeStringToOutputStreamAsPDF(html, diagnosisFileOut);

                doctorSignatureFile = new File(
                        UrlUtils.concatSegments(applicationProperties.getHome(),
                                "temp", "diagnosis_" + order.getId() + "_" + doctor.getId()
                                        + "." + FileUtils.mediaTypeToExtension(doctor.getSignature().getMimeType())));

                uploadService.download(doctor.getSignature(), doctorSignatureFile);
                CaSignParam signParam = new CaSignParam();
                signParam.setPicture(doctorSignatureFile);
                signParam.setPdf(diagnosisFile);
                caService.diagnosisSignature(signedDiagnosisPath, SignatureType.DOCTOR, signParam);

                String commonSealUrl = hospital.getCommonSealUrl();
                if (StringUtils.isNotBlank(commonSealUrl)) {
                    try {
                        hospitalSignatureFile = new File(
                                UrlUtils.concatSegments(applicationProperties.getHome(),
                                        "temp", "diagnosis_" + order.getId() + "_hospital" + hospital.getId()));
                        OkHttpUtils.download(commonSealUrl, hospitalSignatureFile);
                        signParam.setPicture(hospitalSignatureFile);
                        signParam.setPdf(signedDiagnosisFile);
                        caService.diagnosisSignature(signedPath, SignatureType.HOSPITAL, signParam);
                    } catch (Exception e) {
                        log.info("医院公章下载失败", e);
                        signedFile = signedDiagnosisFile;
                    }
                } else {
                    signedFile = signedDiagnosisFile;
                }

                UploadPdfResponse uploadPdfResponse = caService.uploadPdf(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL),
                        signedFile, accountId);
                Upload upload = uploadService.upload(user, UploadResource.of(signedFile, UploadType.PRESCRIPTION, null));
                DiagnosisCa diagnosisCa = diagnosisCaRepository.findOneByOrderId(orderId).orElseGet(() -> new DiagnosisCa(hospital.getId(), orderId));
                diagnosisCa.setDoctorId(doctor.getId());
                diagnosisCa.setDiagnosisPdfNoCaId(upload.getId());
                diagnosisCa.setDiagnosisPdfId(upload.getId());
                diagnosisCa.setDoctorCaUsername(accountId);
                diagnosisCa.setDiagnosisPdfUid(uploadPdfResponse.getRandom());
                diagnosisCa.setDiagnosisPdfFileHash(uploadPdfResponse.getFileHash());
                diagnosisCaRepository.save(diagnosisCa);
                return new SignaturePdfVM(uploadPdfResponse.getImgCode(), uploadPdfResponse.getRandom(), uploadPdfResponse.getFileHash());
            } catch (Exception e) {
                log.error("诊断单生成失败", e);
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("诊断单生成失败");
            } finally {
                FileUtils.delete(diagnosisFile);
                FileUtils.delete(signedFile);
                FileUtils.delete(signedDiagnosisFile);
                FileUtils.delete(doctorSignatureFile);
                FileUtils.delete(hospitalSignatureFile);
            }
        });

    }

    /**
     * 上海ca诊断单签章
     * @param hospital
     * @param user
     * @param orderId
     * @param caParam
     * @return
     */
    @Override
    public SignaturePdfVM diagnosisSignatureSH(Hospital hospital, User user, long orderId, CaPrescriptionParam caParam) {
        return lockService.executeWithLockThrowError("lock.cn.taihealth.diagnosisSignature.orderId." + orderId ,() -> {
            MedicalCase medicalCase = medicalCaseRepository.findByOrderId(orderId).orElseThrow(ErrorType.MEDICAL_CASE_BLANK::toProblem);
            DiagnosisCa diagnosisCa = diagnosisCaRepository.findOneByOrderId(orderId).orElseGet(() -> new DiagnosisCa(hospital.getId(), orderId));

            Order order = medicalCase.getOrder();
            MedicalWorker doctor = order.getDoctor();
            OrderManager orderManager = AppContext.getInstance(OrderManager.class);
            String html = orderManager.drawDiagnosis(medicalCase);
            CaService caService = AppContext.getInstance(caParam.getHospitalCA().getServiceName(), CaService.class);
            // 还未盖过章的诊断单
            String filePath = UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                    "diagnosis_" + order.getId() + ".pdf");
            File diagnosisFile = new File(filePath);
            String signRaw = getDiagnosisSignRaw(medicalCase);
            org.apache.commons.io.FileUtils.forceMkdir(diagnosisFile.getParentFile());
            try (OutputStream diagnosisFileOut = new FileOutputStream(diagnosisFile)) {
                PDFUtil.writeStringToOutputStreamAsPDF(html, diagnosisFileOut);
                Upload upload = uploadService.upload(user, UploadResource.of(diagnosisFile, UploadType.PRESCRIPTION, null));
                diagnosisCa.setDiagnosisPdfUid(Snowflake64.Holder.INSTANCE.nextId() + "");
                diagnosisCa.setDiagnosisPdfNoCaId(upload.getId());
            }
            diagnosisCa.setDoctorId(doctor.getId());
            diagnosisCa.setSignRaw(signRaw);
            diagnosisCa.setSignData(caParam.getApiSign());
            diagnosisCa.setSignSn(caParam.getKeySN());
            diagnosisCa.setDiagnosisPdfId(null);
            try (InputStream in = new FileInputStream(diagnosisFile)) {
                CaSignParam signParam = new CaSignParam();
                signParam.setHost(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SEAL_URL));
                signParam.setRedirectUrl(UrlUtils.concatSegments(SettingsHelper.getString(SettingKey.API_SERVER_URL),
                        "/hospital/ca/orders/", orderId + "", "/diagnosis/signed"));
                signParam.setApiKey(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_KEY));
                signParam.setApiSecret(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SECRET));
                signParam.setSealStrategyId(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_SEAL_STRATEGY_ID));
//                signParam.setApiSign(caParam.getApiSign());
                signParam.setDocumentNo(diagnosisCa.getDiagnosisPdfUid());
                byte[] signature = uploadService.read(doctor.getSignature());
                signParam.setPictureBase64(Base64.getEncoder().encodeToString(signature));
                signParam.setPdfBase64(Base64.getEncoder().encodeToString(in.readAllBytes()));
                String signUrl = caService.diagnosisSignature(null, SignatureType.DOCTOR, signParam);
                SignaturePdfVM signaturePdf = new SignaturePdfVM();
                signaturePdf.setSignUrl(signUrl);
                diagnosisCaRepository.save(diagnosisCa);
                return signaturePdf;
            } finally {
                FileUtils.delete(diagnosisFile);
            }
        });

    }

    @Override
    public void diagnosisSignatureSHSavePdf(long orderId, UKeySignedCallBack signedCallBack) {
        lockService.executeWithLockThrowError("lock.cn.taihealth.diagnosisSignature.orderId." + orderId ,() -> {
            diagnosisCaRepository.findOneByDiagnosisPdfUid(signedCallBack.getDocumentNo()).ifPresent(diagnosisCa -> {
                Hospital hospital = AppContext.getInstance(HospitalRepository.class).getById(diagnosisCa.getHospitalId());
                HospitalCAEnum hospitalCA = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);
                CaService caService = AppContext.getInstance(hospitalCA.getServiceName(), CaService.class);
                String signedDiagnosisPath = UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                        "diagnosis_" + orderId + "_盖了医生章的.pdf");
                File signedDiagnosisFile = new File(signedDiagnosisPath);
                CaSignParam signParam = new CaSignParam();

                signParam.setHost(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SEAL_URL));
                signParam.setApiKey(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_KEY));
                signParam.setApiSecret(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SECRET));
                signParam.setDocumentNo(signedCallBack.getDocumentNo());
                try {
                    caService.downloadPdf(signedDiagnosisFile, null,  signParam);
                    User user = medicalWorkerRepository.findById(diagnosisCa.getDoctorId()).get().getUser();
                    Upload upload = uploadService.upload(user, UploadResource.of(signedDiagnosisFile, UploadType.PRESCRIPTION, null));
                    diagnosisCa.setDiagnosisPdfId(upload.getId());
                    diagnosisCaRepository.save(diagnosisCa);
                } finally {
                    FileUtils.delete(signedDiagnosisFile);
                }
            });
            return 0;
        });

    }

    /**
     * 生成病历签名原文
     * @param medicalCase
     * @return
     */
    private String getDiagnosisSignRaw(MedicalCase medicalCase) {
        String signRaw = "";
        if (StringUtils.isNotBlank(medicalCase.getSelfSpeak())) {
            signRaw += medicalCase.getSelfSpeak();
        }
        if (StringUtils.isNotBlank(medicalCase.getNowMedicalHistory())) {
            signRaw += medicalCase.getNowMedicalHistory();
        }
        if (StringUtils.isNotBlank(medicalCase.getOldMedicalHistory())) {
            signRaw += medicalCase.getOldMedicalHistory();
        }
        if (StringUtils.isNotBlank(medicalCase.getAllergiesHistory())) {
            signRaw += medicalCase.getAllergiesHistory();
        }
        if (StringUtils.isNotBlank(medicalCase.getChecking())) {
            signRaw += medicalCase.getChecking();
        }
        if (StringUtils.isNotBlank(medicalCase.getDiagnosis())) {
            signRaw += medicalCase.getDiagnosis();
        }
        if (StringUtils.isNotBlank(medicalCase.getSummary())) {
            signRaw += medicalCase.getSummary();
        }
        if (medicalCase.getDiseases() != null && !medicalCase.getDiseases().isEmpty()) {
            signRaw += medicalCase.getDiseases().stream().map(u -> u.getDisease().getCode())
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining("|"));
        }
        if (StringUtils.isBlank(signRaw)) {
            signRaw = "没有诊断信息";
        }
        return signRaw;
    }

    @Override
    public CaUnsignedDiagnosisVM diagnosisToPdf(Hospital hospital, User user, long orderId) {
        return lockService.executeWithLockThrowError("lock.cn.taihealth.diagnosisSignature.orderId." + orderId ,() -> {
            DiagnosisCa diagnosisCa = diagnosisCaRepository.findOneByOrderId(orderId).orElseGet(() -> new DiagnosisCa(hospital.getId(), orderId));
            MedicalCase medicalCase = medicalCaseRepository.findByOrderId(orderId).orElseThrow(ErrorType.MEDICAL_CASE_BLANK::toProblem);
            Order order = medicalCase.getOrder();
            // 还未盖过章的诊断单
            String filePath = UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                    "diagnosis_" + order.getId() + ".pdf");
            File diagnosisFile = new File(filePath);
            String signRaw = getDiagnosisSignRaw(medicalCase);
//            if (Objects.equals(signRaw, diagnosisCa.getSignRaw())) {
//                    Upload upload = uploadRepository.getById(diagnosisCa.getDiagnosisPdfNoCaId());
//                    uploadService.download(upload, diagnosisFile);
//                    String base64;
//                    try (InputStream in = new FileInputStream(diagnosisFile)) {
//                        base64 = Base64.getEncoder().encodeToString(in.readAllBytes());
//                    } finally {
//                        FileUtils.delete(diagnosisFile);
//                    }
                return new CaUnsignedDiagnosisVM(null, signRaw);
//            }

//            MedicalWorker doctor = order.getDoctor();
//            OrderManager orderManager = AppContext.getInstance(OrderManager.class);
//            String html = orderManager.drawDiagnosis(medicalCase);
//
//            try (OutputStream diagnosisFileOut = new FileOutputStream(diagnosisFile)) {
//                html = PDFUtil.writeStringToOutputStreamAsPDF(html, diagnosisFileOut);
//
//                Upload upload = uploadService.upload(user, UploadResource.of(diagnosisFile, UploadType.PRESCRIPTION, null));
//                diagnosisCa.setDoctorId(doctor.getId());
//                diagnosisCa.setDiagnosisPdfNoCaId(upload.getId());
//                diagnosisCaRepository.save(diagnosisCa);
////                String base64 = Base64.getEncoder().encodeToString(html.getBytes(StandardCharsets.UTF_8));
//                return new CaUnsignedDiagnosisVM(null, signRaw);
//            } catch (Exception e) {
//                log.error("诊断单生成失败", e);
//                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("诊断单生成失败");
//            } finally {
//                FileUtils.delete(diagnosisFile);
//            }
        });

    }

    @Override
    public void uploadDiagnosisSignaturePdfXinan(Hospital hospital, User user, PdfTicketVM ticket) {
        MedicalCase medicalCase = medicalCaseRepository.findByOrderId(ticket.getOrderId()).orElseThrow(ErrorType.MEDICAL_CASE_BLANK::toProblem);
        Order order = medicalCase.getOrder();
        DiagnosisCa diagnosisCa = diagnosisCaRepository.findOneByOrderId(ticket.getOrderId()).orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);

        String signedDiagnosisPath = UrlUtils.concatSegments(applicationProperties.getHome(), "temp",
                "diagnosis_" + order.getId() + "_盖了医生章的_1.pdf");
        File file = new File(signedDiagnosisPath);
        try {
            CaService caService = AppContext.getInstance(HospitalCAEnum.XINAN.getServiceName(), CaService.class);
            String host = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL);
            CaSignParam signParam = new CaSignParam();
            signParam.setHost(host);
            caService.downloadPdf(file, ticket.getUrl(), signParam);
            UploadResource uploadResource = UploadResource.of(file, UploadType.PRESCRIPTION, null);
            Upload upload = uploadService.upload(user, uploadResource);
            diagnosisCa.setDiagnosisPdfId(upload.getId());
            diagnosisCa.setDiagnosisDigestValue(ticket.getDigestValue());
            diagnosisCaRepository.save(diagnosisCa);
        } finally {
            file.deleteOnExit();
        }
    }

    @Override
    public void prescriptionSignatureSHSavePdf(long orderId, UKeySignedCallBack signedCallBack) {
        lockService.executeWithLockThrowError("lock.cn.taihealth.diagnosisSignature.orderId." + orderId ,() -> {
            prescriptionOrderCaRepository.findOneByDoctorPdfUid(signedCallBack.getDocumentNo()).ifPresent(prescriptionCa -> {
                byte[] pdfBytes = Base64.getDecoder().decode(signedCallBack.getSignPdf());
                try (InputStream in = new ByteArrayInputStream(pdfBytes)) {
                    User user = medicalWorkerRepository.findById(prescriptionCa.getDoctorId()).get().getUser();
                    Upload upload = uploadService.upload(user, UploadResource.of(in, UploadType.PRESCRIPTION,
                            null, prescriptionCa.getId() + "_医师章" + ".pdf"));
                    prescriptionCa.setDoctorPdfId(upload.getId());
                    prescriptionCa.setFinalPdfId(upload.getId());
                    prescriptionOrderCaRepository.save(prescriptionCa);
                } catch (Exception e) {
                    log.error("下载pdf失败, pdf写入失败", e);
                    throw new RuntimeException("下载pdf失败, pdf写入失败 " + e.getMessage());
                }
            });
            return 0;
        });

    }

    @Override
    public void prescriptionSignaturePassedSHSavePdf(long orderId, UKeySignedCallBack signedCallBack) {
        lockService.executeWithLockThrowError("lock.cn.taihealth.diagnosisSignature.orderId." + orderId ,() -> {
            prescriptionOrderCaRepository.findOneByDoctorReviewPdfUid(signedCallBack.getDocumentNo()).ifPresent(prescriptionCa -> {
                User user = medicalWorkerRepository.findById(prescriptionCa.getDoctorId()).get().getUser();
                byte[] pdfBytes = Base64.getDecoder().decode(signedCallBack.getSignPdf());
                try (InputStream in = new ByteArrayInputStream(pdfBytes)) {
                    Upload upload = uploadService.upload(user, UploadResource.of(in, UploadType.PRESCRIPTION,
                            null, prescriptionCa.getId() + "_药师章" + ".pdf"));
                    prescriptionCa.setDoctorReviewPdfId(upload.getId());
                    prescriptionCa.setFinalPdfId(upload.getId());
                    prescriptionOrderCaRepository.save(prescriptionCa);
                } catch (Exception e) {
                    log.error("下载pdf失败, pdf写入失败", e);
                    throw new RuntimeException("下载pdf失败, pdf写入失败 " + e.getMessage());
                }

                File commonSealFile = null;

                Hospital hospital = AppContext.getInstance(HospitalRepository.class).getById(prescriptionCa.getHospitalId());
                String commonSealUrl = hospital.getCommonSealUrl();
                if (StringUtils.isNotBlank(commonSealUrl)) {
                    try {
                        HospitalCAEnum hospitalCA = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);

                        CaService caService = AppContext.getInstance(hospitalCA.getServiceName(), CaService.class);

                        CaSignParam signParam = new CaSignParam();

                        signParam.setHost(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SEAL_URL));
                        signParam.setApiKey(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_KEY));
                        signParam.setApiSecret(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_SECRET));
                        String documentNo = Snowflake64.Holder.INSTANCE.nextId() + "";
                        signParam.setDocumentNo(documentNo);
                        commonSealFile = new File(applicationProperties.getHome(), "temp/" + prescriptionCa.getPrescriptionOrderId()
                        + "_" + Snowflake64.Holder.INSTANCE.nextId() + FileUtils.getFileName(commonSealUrl));
                        OkHttpUtils.download(commonSealUrl, commonSealFile);
                        signParam.setPdfBase64(signedCallBack.getSignPdf());
                        signParam.setPicture(commonSealFile);

                        String hospitalPdfStr = caService.prescriptionSignature(null, 5, SignatureType.HOSPITAL, signParam);
                        byte[] inHospitalPdf = Base64.getDecoder().decode(hospitalPdfStr);
                        Upload upload = uploadService.upload(user, UploadResource.of(new ByteArrayInputStream(inHospitalPdf),
                                UploadType.PRESCRIPTION, null, prescriptionCa.getId() + "_医院章" + ".pdf"));

                        prescriptionCa.setFinalPdfId(upload.getId());
                        prescriptionCa.setFinalPdfUid(documentNo);
                        prescriptionOrderCaRepository.save(prescriptionCa);
                    } catch (Exception e) {
                        log.error("医院章盖章失败", e);
                        throw new RuntimeException("医院章盖章失败 " + e.getMessage());
                    } finally {
                        if (commonSealFile != null) {
                            FileUtils.delete(commonSealFile);
                        }
                    }
                }

            });
            return 0;
        });

    }
}
