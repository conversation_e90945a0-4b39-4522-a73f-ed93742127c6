package cn.taihealth.ih.service.impl.filter.hisbill;

import cn.taihealth.ih.domain.enums.BillChannelEnum;
import cn.taihealth.ih.domain.hospital.HisBill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class BillChannelFilter implements SearchFilter<HisBill> {
    private final BillChannelEnum billChannel;

    public BillChannelFilter(BillChannelEnum billChannel) {
        this.billChannel = billChannel;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<HisBill> toSpecification() {
        if (BillChannelEnum.WECHAT.equals(billChannel)) {
            return Specifications.or(
                    Specifications.eq("payType", 1),
                    Specifications.eq("payType", 3)
            );
        } else if (BillChannelEnum.ALIPAY.equals(billChannel)) {
            return Specifications.eq("payType", 2);
        }
        return Specifications.or(
                Specifications.eq("payType", 1),
                Specifications.eq("payType", 3));
    }

    @Override
    public String toExpression() {
        String str = " payType:" + billChannel;
        return str;
    }

    @Override
    public boolean isValid() {
        return billChannel != null;
    }
}
