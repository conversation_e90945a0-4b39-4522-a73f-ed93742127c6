package cn.taihealth.ih.service.impl.filter.reportapi;

import cn.taihealth.ih.domain.cloud.ReportApi;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class ReportApiNameFilter implements SearchFilter<ReportApi> {

    private final String name;

    public ReportApiNameFilter(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<ReportApi> toSpecification() {
        return Specifications.likeIgnoreCase("name", name);
    }

    @Override
    public String toExpression() {
        String str = " reportApi:" + name;
        return str;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof ReportApiNameFilter)) {
            return false;
        }

        ReportApiNameFilter rhs = (ReportApiNameFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
