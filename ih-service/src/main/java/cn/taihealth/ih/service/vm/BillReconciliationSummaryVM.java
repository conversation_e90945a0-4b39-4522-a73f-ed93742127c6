package cn.taihealth.ih.service.vm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 交易对账明细
 */
@Getter
@Setter
public class BillReconciliationSummaryVM implements Serializable {

    @ApiModelProperty("三方支付总金额")
    private long thirdTotalIncome;
    @ApiModelProperty("三方退款总金额")
    private long thirdTotalRefund;
    @ApiModelProperty("三方已退款总金额")
    private long refundedTotalAmount;
    @ApiModelProperty("三方退款中总金额")
    private long refundingTotalAmount;
    @ApiModelProperty("三方净收入")
    private long thirdNetIncome;

    @ApiModelProperty("his支付总金额")
    private long hisTotalIncome;
    @ApiModelProperty("his")
    private long hisTotalRefund;
    @ApiModelProperty("his交易汇总金额")
    private long hisNetIncome;

}
