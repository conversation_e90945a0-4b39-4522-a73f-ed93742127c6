package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.enums.TimeZone;
import cn.taihealth.ih.domain.hospital.DoctorStopDiagnosis;
import cn.taihealth.ih.domain.hospital.DoctorVisit;
import cn.taihealth.ih.domain.hospital.DoctorVisitTime;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DoctorVisitDTO extends UpdatableDTO {

    @ApiParam(name = "医院")
    @NotBlank
    private String hospital;

    @ApiParam(name = "省")
    private String province;

    @ApiParam(name = "市")
    private String city;

    @ApiParam(name = "医生")
    @NotNull
    private MedicalWorkerDTO medicalWorkerDTO;

    @ApiParam(name = "科室")
    @NotBlank
    private String dept;

    @ApiParam(name = "开始时间")
    @NotNull
    private Date beginDate;

    @ApiParam(name = "结束时间")
    @NotNull
    private Date endDate;

    @ApiParam(name = "创建人")
    private UserDTO creator;

    @ApiParam(name = "每周出诊时间详情")
    private List<DoctorVisitTimeDTO> doctorVisitTimes = Lists.newArrayList();

    @ApiParam(name = "每周停诊时间详情")
    private List<DoctorStopDiagnosisDTO> doctorStopDiagnosisList = Lists.newArrayList();

    @ApiParam(name = "出诊停诊日期详情")
    private List<DoctorVisitDateDTO> doctorVisitDateList = Lists.newArrayList();

    public DoctorVisitDTO(DoctorVisit doctorVisit, Date beginTime, Date endTime) {
        super(doctorVisit);
        this.dept = doctorVisit.getDept();
        this.beginDate = doctorVisit.getBeginDate();
        this.endDate = doctorVisit.getEndDate();
        this.hospital = doctorVisit.getHospital();
        this.province = doctorVisit.getProvince();
        this.city = doctorVisit.getCity();
        if (doctorVisit.getMedicalWorker() != null) {
            this.medicalWorkerDTO = new MedicalWorkerDTO(doctorVisit.getMedicalWorker());
        }
        if (doctorVisit.getCreator() != null) {
            this.creator = new UserDTO(doctorVisit.getCreator());
        }
        List<DoctorVisitTime> list = doctorVisit.getDoctorVisitTimes();
        for (DoctorVisitTime doctorVisitTime : list) {
            this.doctorVisitTimes.add(new DoctorVisitTimeDTO(doctorVisitTime));
        }
        List<DoctorStopDiagnosis> doctorStopDiagnoses = doctorVisit.getDoctorStopDiagnoses();
        for (DoctorStopDiagnosis diagnosis : doctorStopDiagnoses) {
            this.getDoctorStopDiagnosisList().add(new DoctorStopDiagnosisDTO(diagnosis));
        }
        if (doctorVisit.getEndDate().after(new Date()) && beginTime != null && endTime != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String begin = sdf.format(beginTime);
            if (beginTime.before(beginDate)) {
                begin = sdf.format(beginDate);
            }
            String end = sdf.format(endTime);
            if (endTime.after(endDate)) {
                end = sdf.format(endDate);
            }
            String finalBegin = begin;
            String finalEnd = end;
            Date begin1;
            Date end1;
            Date ss;
            try {
                begin1 = new SimpleDateFormat("yyyy-MM-dd").parse(finalBegin);
                end1 = new SimpleDateFormat("yyyy-MM-dd").parse(finalEnd);
                long ssl = begin1.getTime();
                long eel = end1.getTime();

                Long oneDay = 1000 * 60 * 60 * 24l;
                while (ssl <= eel) {
                    ss = new Date(ssl);
                    DoctorVisitDateDTO doctorVisitDateDTO = new DoctorVisitDateDTO();
                    DoctorVisitDateDTO doctorVisitDateDTO1 = new DoctorVisitDateDTO();
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(ss);
                    doctorVisitDateDTO.setDayOfWeek(getDayOfWeek(cal));
                    doctorVisitDateDTO1.setDayOfWeek(getDayOfWeek(cal));
                    doctorVisitDateDTO.setTimeZone(TimeZone.AM);
                    doctorVisitDateDTO.setDateTime(ss);
                    doctorVisitDateDTO1.setDateTime(ss);
                    this.doctorVisitDateList.add(doctorVisitDateDTO);
                    doctorVisitDateDTO1.setTimeZone(TimeZone.PM);
                    this.doctorVisitDateList.add(doctorVisitDateDTO1);
                    ssl += oneDay;
                }
            } catch (ParseException e) {
                throw ErrorType.DATA_NOT_AVAILABLE.toProblem("日期错误，转换不成功");
            }

            for (DoctorVisitTime doctorVisitTime : list) {
                List<Date> dates = getDayOfWeekWithinDateInterval(finalBegin, finalEnd,
                                                                  doctorVisitTime.getDayOfWeek());
                for (Date date : dates) {
                    DoctorVisitDateDTO doctorVisitDateDTO = new DoctorVisitDateDTO();
                    doctorVisitDateDTO.setTimeZone(doctorVisitTime.getTimeZone());
                    doctorVisitDateDTO.setDateTime(date);
                    doctorVisitDateDTO.setDayOfWeek(doctorVisitTime.getDayOfWeek());
                    doctorVisitDateDTO.setOpen(true);
                    Calendar dateCalendar = Calendar.getInstance();
                    dateCalendar.setTime(date);
                    int difference = (int) ((date.getTime() - begin1.getTime()) / 86400000);
                    if (doctorVisitTime.getTimeZone().equals(TimeZone.PM)) {
                        this.doctorVisitDateList.remove(2 * difference + 1);
                        this.doctorVisitDateList.add(2 * difference + 1, doctorVisitDateDTO);
                    } else {
                        this.doctorVisitDateList.remove(2 * difference);
                        this.doctorVisitDateList.add(2 * difference, doctorVisitDateDTO);
                    }
                }

            }

            for (DoctorStopDiagnosis doctorStopDiagnosis : doctorStopDiagnoses) {
                Calendar call = Calendar.getInstance();
                call.setTime(doctorStopDiagnosis.getDateTime());
                call.set(Calendar.HOUR_OF_DAY, 0);
                call.set(Calendar.MINUTE, 0);
                call.set(Calendar.SECOND, 0);
                call.set(Calendar.MILLISECOND, 0);
                if (call.getTime().after(beginTime) && !call.getTime()
                    .after(endTime)) {
                    DoctorVisitDateDTO doctorVisitDateDTO = new DoctorVisitDateDTO();
                    doctorVisitDateDTO.setTimeZone(doctorStopDiagnosis.getTimeZone());
                    doctorVisitDateDTO.setDateTime(call.getTime());
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(call.getTime());
                    doctorVisitDateDTO.setDayOfWeek(getDayOfWeek(cal));
                    doctorVisitDateDTO.setOpen(false);
                    int difference = (int) ((call.getTime().getTime() - begin1.getTime())
                        / 86400000);
                    if (doctorStopDiagnosis.getTimeZone().equals(TimeZone.PM)) {
                        this.doctorVisitDateList.remove(2 * difference + 1);
                        this.doctorVisitDateList.add(2 * difference + 1, doctorVisitDateDTO);
                    } else {
                        this.doctorVisitDateList.remove(2 * difference);
                        this.doctorVisitDateList.add(2 * difference, doctorVisitDateDTO);
                    }
                }
            }
            if (!doctorVisitDateList.isEmpty()) {
                Collections.sort(doctorVisitDateList);
            }
            if (!doctorVisitTimes.isEmpty()) {
                Collections.sort(doctorVisitTimes);
            }
            if (!doctorStopDiagnosisList.isEmpty()) {
                Collections.sort(doctorStopDiagnosisList);
            }
        }
    }

    /**
     * 获取某段时间内的周一（二等等）的日期
     *
     * @param dataBegin 开始日期
     * @param dataEnd   结束日期
     * @param weekDays  获取周几，1－6代表周一到周六。0代表周日
     * @return 返回日期List
     */


    public static List<Date> getDayOfWeekWithinDateInterval(String dataBegin, String dataEnd, int weekDays) {
        List<Date> dateResult = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        String[] dateInterval = {dataBegin, dataEnd};
        Date[] dates = new Date[dateInterval.length];
        for (int i = 0; i < dateInterval.length; i++) {
            String[] ymd = dateInterval[i].split("[^\\d]+");
            cal.set(Integer.parseInt(ymd[0]), Integer.parseInt(ymd[1]) - 1, Integer.parseInt(ymd[2]));
            dates[i] = cal.getTime();

        }
        for (Date date = dates[0]; date.compareTo(dates[1]) <= 0; ) {
            cal.setTime(date);
            if (cal.get(Calendar.DAY_OF_WEEK) - 1 == weekDays) {
                dateResult.add(date);
            }
            cal.add(Calendar.DATE, 1);
            date = cal.getTime();

        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        List<Date> list = new ArrayList<>();
        for (Date date : dateResult) {
            try {
                Date parse = format.parse(format.format(date));
                list.add(parse);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return list;

    }


    int getDayOfWeek(Calendar calendar) {              // 计算日期是星期几的方法
/*        int y = calendar.get(Calendar.YEAR);             // 0 为周末 1为周一 ......以此类推
        int m = calendar.get(Calendar.MONTH) + 1;
        int data = calendar.get(Calendar.DAY_OF_YEAR) + 1;
        if (m == 1 || m == 2) {       // 若为一二月分时，年减一 月加上12个月
            y--;
            m += 12;
        }
        return (y + y / 4 - y / 100 + y / 400 + (13 * m + 8) / 5 + data) % 7;*/
        int week_index = calendar.get(Calendar.DAY_OF_WEEK) - 1;

        if (week_index < 0) {

            week_index = 0;

        }
        return week_index;
    }
}
