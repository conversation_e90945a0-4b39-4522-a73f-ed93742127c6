package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.MathUtils;
import cn.taihealth.ih.commons.util.RedisUtil;
import cn.taihealth.ih.commons.util.Snowflake64;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.his.HisChargeRecord;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.OfflineOrder.OutPatientStatus;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorker;
import cn.taihealth.ih.domain.hospital.offline.OfflineOrderQueue;
import cn.taihealth.ih.repo.ElectronicMedicCardRepository;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineDeptRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineMedicalWorkerRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineOrderQueueRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.offline.HisChargeRecordService;
import cn.taihealth.ih.service.api.wechat.WechatService;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.dto.export.NucleicAcidExportDTO;
import cn.taihealth.ih.service.dto.hospital.offline.OfflineMedicalWorkerDTO;
import cn.taihealth.ih.service.impl.order.BusinessOrderManager;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.JpaUtils;
import cn.taihealth.ih.service.util.LngLat;
import cn.taihealth.ih.service.util.PageUtils;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.nodered.NodeRedResponseData;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request.AppointmentSignInReq;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.ReturnRegistResult;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.service.vm.offline.HisRefundParam;
import cn.taihealth.ih.service.vm.platform.PayPlatform;
import cn.taihealth.ih.wechat.service.vm.RefundOrderVM;
import cn.taihealth.ih.wechat.service.vm.WechatTemplatedData;
import cn.taihealth.ih.wechat.service.vm.alipay.AliPayTemplatedData;
import cn.taihealth.ih.wechat.service.vm.wechat.MedicalInsuranceParam;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class OfflineOrderServiceImpl implements OfflineOrderService {

    private final ElectronicMedicCardRepository electronicMedicCardRepository;
    private final OfflineMedicalWorkerRepository offlineMedicalWorkerRepository;
    private final OfflineOrderRepository offlineOrderRepository;
    private final OfflineDeptRepository offlineDeptRepository;
    private final PatientRepository patientRepository;
    private final NoticeService noticeService;
    private final RedisUtil redisUtil;
    private final ExportTaskService exportTaskService;
    private final HisChargeRecordService hisChargeRecordService;
    private final EvaluateService evaluateService;
    private final OfflineHospitalService offlineHospitalService;
    private final LockService lockService;
    private final OfflineOrderQueueRepository offlineOrderQueueRepository;
    private final PayManager payManager;

    @Override
    @Transactional
    public OfflineOrderDTO createOfflineOrder(Hospital hospital, CreateOfflineOrderDTO createDTO) {
        ElectronicMedicCard card = electronicMedicCardRepository.getById(createDTO.getElectronicMedicCard().getId());
//        if (createDTO.getInsuranceParam() != null && StringUtils.isNotBlank(createDTO.getInsuranceParam().getPayAuthNo()) &&
//                (card.getPatient().getRelationship() != Patient.Relationship.SELF || card.getCardType() == ElectronicMedicCard.CardType.SELF_PAY)) {
//            throw ErrorType.VALIDATION_FAILED.toProblem("医保结算只能使用自己的医保卡");
//        }
        OfflineOrder offlineOrder = new OfflineOrder();
        offlineOrder.setHospital(hospital);
        offlineOrder.setPatientId(card.getPatient().getId() + "");
        offlineOrder.setElectronicMedicCard(card);
        offlineOrder.setStatus(OutPatientStatus.WAIT_PAY);
        offlineOrder.setPaymentMethod(createDTO.getPaymentMethod());
        offlineOrder.setType(createDTO.getType());
        offlineOrder.setDeptName(createDTO.getDeptName());
        offlineOrder.setHisDeptId(createDTO.getHisDeptId());
        offlineOrder.setSourceNo(createDTO.getSourceNumber());
        MedicalInsuranceParam insuranceParam = createDTO.getInsuranceParam();
        String insuranceParamStr = insuranceParam == null ? null : insuranceParam.toHisParam();
        offlineOrder.setSelfFlag(insuranceParamStr == null ? 1 : 0);
        if (BooleanUtils.toBoolean(createDTO.getOldRegister())) {
            offlineOrder.setOldRegister(true);
        }
        SchedulingDeptSourceInfo schedulingDeptSourceInfo = createDTO.getSchedulingDeptSourceInfo();
        SchedulingDoctorSourceInfo schedulingDoctorSourceInfo = createDTO.getSchedulingDoctorSourceInfo();
        String registrationFeeCode;
        String treatmentFeeCode;
        String childrenTreatmentFeeCode;
        if (createDTO.getOfflineMedicalWorker() != null) {
            if (schedulingDoctorSourceInfo == null) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("医生号源信息不能为空");
            }
            OfflineMedicalWorker medicalWorker = offlineMedicalWorkerRepository.getById(
                createDTO.getOfflineMedicalWorker().getId());
            offlineOrder.setDoctor(offlineMedicalWorkerRepository.getById(createDTO.getOfflineMedicalWorker().getId()));
            offlineOrder.setDoctorName(medicalWorker.getName());
            // 科室信息
            offlineOrder.setHisDeptId(schedulingDoctorSourceInfo.getDept_id());
            offlineOrder.setDeptName(schedulingDoctorSourceInfo.getDept_name());
            offlineOrder.setScheduleId(schedulingDoctorSourceInfo.getScheduling_id());
            offlineOrder.setSourceInfo(StandardObjectMapper.stringify(schedulingDoctorSourceInfo));
            offlineOrder.setAppointmentTimeSlot(schedulingDoctorSourceInfo.getBegin_time() + "-" + schedulingDoctorSourceInfo.getEnd_time());
            registrationFeeCode = schedulingDoctorSourceInfo.getRegistration_fee_code();
            treatmentFeeCode = schedulingDoctorSourceInfo.getTreatment_fee_code();
            childrenTreatmentFeeCode = schedulingDoctorSourceInfo.getChildren_treatment_fee_code();
        } else {
            if (schedulingDeptSourceInfo == null) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("科室号源信息不能为空");
            }
            offlineOrder.setSourceInfo(StandardObjectMapper.stringify(schedulingDeptSourceInfo));
            offlineOrder.setScheduleId(schedulingDeptSourceInfo.getScheduling_id());
            offlineOrder.setAppointmentTimeSlot(schedulingDeptSourceInfo.getBegin_time() + "-" + schedulingDeptSourceInfo.getEnd_time());
            registrationFeeCode = schedulingDeptSourceInfo.getRegistration_fee_code();
            treatmentFeeCode = schedulingDeptSourceInfo.getTreatment_fee_code();
            childrenTreatmentFeeCode = schedulingDeptSourceInfo.getChildren_treatment_fee_code();
        }

        // 1.查号序 2.锁号 3.预算
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        PreRegistrationResult preRegistrationResult = businessService.preRegisterOfflineOrder(hospital, offlineOrder, registrationFeeCode,
                                                                                              treatmentFeeCode, childrenTreatmentFeeCode, createDTO.getInsuranceParam());

        boolean isWanda = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.WANDA_PAY_ENABLED);
        if (isWanda) {
            offlineOrder.setAggregatePayment(AggregatePayment.WANDA);
        }
        offlineOrderRepository.save(offlineOrder);

        BigDecimal totalAmount = new BigDecimal(preRegistrationResult.getTotal_amount());
        if (offlineOrder.getSelfFlag() == 1 && BigDecimal.ZERO.compareTo(totalAmount) == 0) {
            HisPayParam hisPayParam = new HisPayParam();
            hisPayParam.setTransactionId(offlineOrder.getId()+"");
            hisPayParam.setPayTime(new Date());
            payOutpatientRegisterChargeSelf(offlineOrder, hisPayParam);
        }

        return new OfflineOrderDTO(offlineOrder);
    }

    @Override
    @Transactional
    public OfflineOrderDTO outPatientRreRegister(Hospital hospital, Long orderId, MedicalInsuranceParam vm) {
        OfflineOrder offlineOrder = offlineOrderRepository.findById(orderId)
                .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("订单不存在"));
        Patient patient = patientRepository.getById(Long.valueOf(offlineOrder.getPatientId()));
        String registrationFeeCode = null;
        String treatmentFeeCode = null;
        String childrenTreatmentFeeCode = null;
        if (offlineOrder.getSourceInfo() != null) {
            SchedulingDoctorSourceInfo sourceInfo = StandardObjectMapper.readValue(offlineOrder.getSourceInfo(), new TypeReference<>() {});
            registrationFeeCode = sourceInfo.getRegistration_fee_code();
            treatmentFeeCode= sourceInfo.getTreatment_fee_code();
            childrenTreatmentFeeCode = sourceInfo.getChildren_treatment_fee_code();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        PreRegistrationResult preRegistrationResult = businessService.outPatientPreRegister(hospital, offlineOrder, patient,
                registrationFeeCode, treatmentFeeCode, childrenTreatmentFeeCode, vm);
        if (StringUtils.isNotBlank(preRegistrationResult.getSettle_id())
                && offlineOrder.getType() == ProjectTypeEnum.OUTPATIENT_APPOINTMENT_REGISTRATION) {
            offlineOrder.setType(ProjectTypeEnum.APPOINTMENT_REGISTRATION);
            offlineOrderRepository.save(offlineOrder);
        } else {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单类型不正确");
        }

        // 挂号费0元，直接结算
        BigDecimal totalAmount = new BigDecimal(preRegistrationResult.getTotal_amount());
        if (offlineOrder.getSelfFlag() == 1 && BigDecimal.ZERO.compareTo(totalAmount) == 0) {
            HisPayParam hisPayParam = new HisPayParam();
            hisPayParam.setTransactionId(offlineOrder.getId()+"");
            hisPayParam.setPayTime(new Date());
            payOutpatientRegisterChargeSelf(offlineOrder, hisPayParam);
        }

        return new OfflineOrderDTO(offlineOrder);
    }

    @Override
    @Transactional
    public OfflineOrderDTO addOutPatientAppointmentRegister(Hospital hospital, CreateOfflineOrderDTO createOfflineOrderDTO) {
        ElectronicMedicCard card = electronicMedicCardRepository.getById(createOfflineOrderDTO.getElectronicMedicCard().getId());
        OfflineOrder offlineOrder = new OfflineOrder();
        offlineOrder.setDetailType(ProjectDetailTypeEnum.APPOINTMENT);
        offlineOrder.setHospital(hospital);
        offlineOrder.setPatientId(card.getPatient().getId() + "");
        offlineOrder.setElectronicMedicCard(card);
        offlineOrder.setStatus(OutPatientStatus.WAIT_PAY);
        offlineOrder.setPaymentMethod(createOfflineOrderDTO.getPaymentMethod());
        offlineOrder.setType(createOfflineOrderDTO.getType());
        offlineOrder.setDeptName(createOfflineOrderDTO.getDeptName());
        offlineOrder.setHisDeptId(createOfflineOrderDTO.getHisDeptId());
        offlineOrder.setSourceNo(createOfflineOrderDTO.getSourceNumber());
        if (BooleanUtils.toBoolean(createOfflineOrderDTO.getOldRegister())) {
            offlineOrder.setOldRegister(true);
        }
        SchedulingDeptSourceInfo schedulingDeptSourceInfo = createOfflineOrderDTO.getSchedulingDeptSourceInfo();
        SchedulingDoctorSourceInfo schedulingDoctorSourceInfo = createOfflineOrderDTO.getSchedulingDoctorSourceInfo();
        String registrationFeeCode;
        String treatmentFeeCode;
        String childrenTreatmentFeeCode;
        if (createOfflineOrderDTO.getOfflineMedicalWorker() != null) {
            if (schedulingDoctorSourceInfo == null) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("医生号源信息不能为空");
            }
            OfflineMedicalWorker medicalWorker = offlineMedicalWorkerRepository.getById(
                    createOfflineOrderDTO.getOfflineMedicalWorker().getId());
            offlineOrder.setDoctor(offlineMedicalWorkerRepository.getById(createOfflineOrderDTO.getOfflineMedicalWorker().getId()));
            offlineOrder.setDoctorName(medicalWorker.getName());
            // 科室信息
            offlineOrder.setDutyDate(schedulingDoctorSourceInfo.getDuty_date());
            offlineOrder.setHisDeptId(schedulingDoctorSourceInfo.getDept_id());
            offlineOrder.setDeptName(schedulingDoctorSourceInfo.getDept_name());
            offlineOrder.setScheduleId(schedulingDoctorSourceInfo.getScheduling_id());
            offlineOrder.setSourceInfo(StandardObjectMapper.stringify(schedulingDoctorSourceInfo));
            offlineOrder.setAppointmentTimeSlot(schedulingDoctorSourceInfo.getBegin_time() + "-" + schedulingDoctorSourceInfo.getEnd_time());
            registrationFeeCode = schedulingDoctorSourceInfo.getRegistration_fee_code();
            treatmentFeeCode = schedulingDoctorSourceInfo.getTreatment_fee_code();
            childrenTreatmentFeeCode = schedulingDoctorSourceInfo.getChildren_treatment_fee_code();
        } else {
            if (schedulingDeptSourceInfo == null) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("科室号源信息不能为空");
            }
            offlineOrder.setSourceInfo(StandardObjectMapper.stringify(schedulingDeptSourceInfo));
            offlineOrder.setScheduleId(schedulingDeptSourceInfo.getScheduling_id());
            offlineOrder.setDutyDate(schedulingDeptSourceInfo.getDuty_date());
            offlineOrder.setAppointmentTimeSlot(schedulingDeptSourceInfo.getBegin_time() + "-" + schedulingDeptSourceInfo.getEnd_time());
            registrationFeeCode = schedulingDeptSourceInfo.getRegistration_fee_code();
            treatmentFeeCode = schedulingDeptSourceInfo.getTreatment_fee_code();
            childrenTreatmentFeeCode = schedulingDeptSourceInfo.getChildren_treatment_fee_code();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        // 1.查号序 2.锁号 3.预约
        businessService.outPatientAppointment(hospital, offlineOrder, registrationFeeCode, treatmentFeeCode,
                                              childrenTreatmentFeeCode);
        offlineOrderRepository.save(offlineOrder);
        sendNoticeOutpatientRegisterMessage(offlineOrder, card.getPatient());
        return new OfflineOrderDTO(offlineOrder);
    }

    @Override
    @Transactional
    public OfflineOrderQueue offlineOrderSignIn(Hospital hospital, User user, long offlineOrderId, LngLat lngLat) {
        return lockService.executeWithLock("lock.offlineOrder.signIn." + offlineOrderId, () -> {

            // 签到
            boolean useOfflineSignIn = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.USE_OFFLINE_SIGN_IN);
            if (!useOfflineSignIn) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("在线签到未开启，不需要签到");
            }

            OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
            if (offlineOrder.getStatus() != OutPatientStatus.REGISTERED) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确");
            }
            Date beginTime = TimeUtils.convert(offlineOrder.getBeginTime());
            if (beginTime != null && System.currentTimeMillis() < beginTime.getTime() - 7200_000) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("请在就诊开始前2小时内进行签到");
            }

            int signInRange = HospitalSettingsHelper.getInt(hospital, HospitalSettingKey.SIGN_IN_RANGE);
            int distance = offlineHospitalService.addressDistance(hospital, lngLat);
            if (distance > signInRange) {
                throw ErrorType.OUT_OF_RANGE.toProblem();
            }

            offlineOrder.setStatus(OutPatientStatus.WAITTREAT);
            offlineOrderRepository.save(offlineOrder);

            // 调用his签到
            BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
            AppointmentSignInReq signInReq = new AppointmentSignInReq();
            Patient patient = patientRepository.getById(Long.parseLong(offlineOrder.getPatientId()));
            signInReq.setRegno(offlineOrder.getRegNo());
            signInReq.setPatname(patient.getName());
            signInReq.setPatid(patient.getHisPatid());
            NodeRedResponseData<AppointmentSignInResult> result = businessService.appointmentSignIn(hospital.getCode(), signInReq);

            OfflineOrderQueue offlineOrderQueue = offlineOrderQueueRepository.findOneByOfflineOrder(offlineOrder)
                    .orElseGet(() -> new OfflineOrderQueue(hospital, offlineOrder, offlineOrder.getDept()));
            offlineOrderQueue.setQueueNumber(result.getContent().getQueueNumber());
            offlineOrderQueueRepository.save(offlineOrderQueue);
            return offlineOrderQueue;
        });
    }
    //    @Override
//    public OfflineOrderVM updateOfflineOrder(Hospital hospital, UpdateOfflineOrderVM offlineOrderVM) {
//        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderVM.getId());
//        List<OutPatientStatus> enableToUpdateOrders = OutPatientStatus.getEnableToUpdateOrders();
//        if (!enableToUpdateOrders.contains(offlineOrder.getStatus())) {
//            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确，无法切换就诊人");
//        }
//        Patient patient = patientRepository.getById(offlineOrderVM.getPatient().getId());
//        offlineOrder.setPatientId(patient.getId().toString());
//        return new OfflineOrderVM(offlineOrderRepository.save(offlineOrder));
//    }

    @Override
    @Transactional
    public void orderPending(long offlineOrderId) {
        log.info("统一下单后，offlineOrder订单改为支付中 Id {}", offlineOrderId);
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        if (offlineOrder.getStatus() == OutPatientStatus.PENDING) {
            return;
        }

        if (offlineOrder.getStatus() != OutPatientStatus.WAIT_PAY) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("订单状态不正确");
        }
        offlineOrder.setStatus(OutPatientStatus.PENDING);
        offlineOrderRepository.save(offlineOrder);
        try {
            Patient patient = patientRepository.getById(Long.valueOf(offlineOrder.getPatientId()));
            Map<String, String> params = new HashMap<>();
            params.put("patientName", patient.getName());
            params.put("expenseType", "挂号费");
            params.put("price", MathUtils.division2(offlineOrder.getRegistrationFee(), 100));
            params.put("deptName", StringUtils.isNotBlank(offlineOrder.getDeptName()) ? offlineOrder.getDeptName() : "-");
            params.put("desc", "请及时支付，逾期未支付将取消挂号");
            params.put("path", "subpackages/pat/orders/index?type=register");
            noticeService.miniProgramNotice(offlineOrder.getHospital(), patient.getUser(), HospitalSettingKey.NOTICE_OUT_REGISTER_WAIT_PAY, params);
        } catch (Exception e) {
            log.error("组装微信模板预约挂号待支付通知消息失败", e);
        }
    }

    @Override
    @Transactional
    public void payOutpatientRegisterCharge(long offlineOrderId, HisPayParam hisPayParam) {
        log.info("支付门诊挂号费用 offlineOrderId: {}", offlineOrderId);
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);

        // 处理状态为CANCELLED的订单
        if (offlineOrder.getStatus() == OutPatientStatus.CANCELLED) {
            log.info("支付门诊挂号费用 offlineOrderId: {}，订单已取消，为客户办理退费", offlineOrderId);
            refundingAppointmentRegisterCharge(offlineOrderId, InsurancePayMethod.ALL, null, null);
        }

        // 查看是否已经支付了
        if (offlineOrder.getStatus() == OutPatientStatus.WAIT_PAY || offlineOrder.getStatus() == OutPatientStatus.PENDING
                || (offlineOrder.getStatus() == OutPatientStatus.REGISTERED && offlineOrder.getPayTime() == null)) {
            if (offlineOrder.getSelfFlag() == 1) {
                // 自费
                payOutpatientRegisterChargeSelf(offlineOrder, hisPayParam);
            } else {
                // 医保
                payOutpatientRegisterChargeInsurance(offlineOrder, hisPayParam);
            }
        }
    }

    /**
     * 自费结算
     * @param hisPayParam
     * @param offlineOrder
     */
    private void payOutpatientRegisterChargeSelf(OfflineOrder offlineOrder, HisPayParam hisPayParam) {
        //再到记录表里确认一下是不是已经结算过了
        boolean success = false;
        HisChargeRecord confirmChargeRecord = hisChargeRecordService.getRegisterChargeConfirmChargeRecord(offlineOrder);
        if (confirmChargeRecord != null && confirmChargeRecord.isSuccess()) {
            // 已结算成功过
            log.info("门诊挂号费用 offlineOrderId: {}已经结算过了", offlineOrder.getId());
            success = true;
        } else {
            //到这里才是真正的结算
            NodeRedResponseData<ConfirmRegistResult> nodeResult = null;
            try {
                BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
                nodeResult = businessService.confirmRegist(offlineOrder.getHospital(), offlineOrder, hisPayParam);
                success = nodeResult != null && nodeResult.getContent() != null && nodeResult.getContent().isSuccess();
            } catch (Exception e) {
                log.error("门诊预约挂号结算失败 settleId: " + offlineOrder.getSettleId(), e);
            }
            hisChargeRecordService.saveRegisterChargeConfirmChargeRecord(offlineOrder, nodeResult, hisPayParam.getTransactionId());
        }
        offlineOrder.setSettlementSuccessful(true);
        offlineOrder.setStatus(OutPatientStatus.REGISTERED);
        offlineOrder.setPayTime(new Date());
        offlineOrder.setPayTransactionId(hisPayParam.getTransactionId());
        if ("2".equals(hisPayParam.getPayType())) {
            offlineOrder.setPaymentMethod(PaymentMethod.ALI_PAY);
        } else {
            offlineOrder.setPaymentMethod(PaymentMethod.WECHAT);
        }
        offlineOrderRepository.save(offlineOrder);
        if (!success) {

            // 2024年12月13日 根据结算失败费用不回滚需求http://81.70.195.70:8911/zentao/story-view-8825.html
            // 预约挂号 结算失败，不进行自动退款

//            log.error("预约挂号结算失败，为用户退款");

//            RefundOrderVM refundVm = new RefundOrderVM();
//            refundVm.setId(offlineOrder.getId());
//            refundVm.setType(ThirdOrderType.OUTPATIENT_REGISTER_FEE);
//
//            boolean refundedFromWechat = AppContext.getInstance(PayManager.class).refund(offlineOrder.getHospital(),
//                    false, offlineOrder.getPaymentMethod(), refundVm, offlineOrder.getRegistrationFee(), null);
//
//            if (refundedFromWechat) {
//                log.info("退款：已向微信发起退款请求,offlineOrderId: " + offlineOrder.getId());
//                offlineOrder.setStatus(OutPatientStatus.REFUNDING);
//            } else {
//                log.info("退款：没有微信订单，不需要去微信退款，offlineOrderId: " + offlineOrder.getId());
//                offlineOrder.setStatus(OutPatientStatus.REFUND);
//                offlineOrder.setRefundTime(new Date());
//            }
//            offlineOrder.setRefundSource(OrderRefundSource.IH);
//            offlineOrderRepository.save(offlineOrder);

            // 2025年01月06日 根据配置控制结算失败是否自动退款 需求http://81.70.195.70:8911/zentao/story-view-9493.html
//            log.error("预约挂号结算失败，等待后台退款");
//            offlineOrder.setRefundSource(OrderRefundSource.IH);
//            offlineOrder.setSettlementSuccessful(false);
//            offlineOrderRepository.save(offlineOrder);

            offlineOrder.setSettlementSuccessful(false);
            offlineOrder.setRefundSource(OrderRefundSource.IH);
            boolean autoRefund = HospitalSettingsHelper.getBoolean(offlineOrder.getHospital(),
                                                                 HospitalSettingKey.AUTO_REFUND_AFTER_CHARGE_CONFIRM_FAILED);
            if (autoRefund) {
                log.error("预约挂号结算失败，为用户退款");

                RefundOrderVM refundVm = new RefundOrderVM();
                refundVm.setId(offlineOrder.getId());
                refundVm.setType(ThirdOrderType.OUTPATIENT_REGISTER_FEE);
                refundVm.setAggregatePayment(offlineOrder.getAggregatePayment());

                boolean refundedFromWechat = AppContext.getInstance(PayManager.class).refund(offlineOrder.getHospital(),
                        false, offlineOrder.getPaymentMethod(), refundVm, offlineOrder.getRegistrationFee(), null);

                if (refundedFromWechat) {
                    log.info("退款：已向微信发起退款请求,offlineOrderId: " + offlineOrder.getId());
                    offlineOrder.setStatus(OutPatientStatus.REFUNDING);
                } else {
                    log.info("退款：没有微信订单，不需要去微信退款，offlineOrderId: " + offlineOrder.getId());
                    offlineOrder.setStatus(OutPatientStatus.REFUND);
                    offlineOrder.setRefundTime(new Date());
                }

            }
            offlineOrderRepository.save(offlineOrder);
            return;
        } else {
            // 这里查询his那里的订单信息，更新到offlineOrder中
            if (offlineOrder.getSourceInfo() != null) {
                saveOrderInfo(offlineOrder);
            }
        }
        sendNoticeOutpatientRegisterMessage(offlineOrder, patientRepository.getById(Long.valueOf(offlineOrder.getPatientId())));
    }

    /**
     * <pre>
     *     医保结算
     *     我侧和his侧有一方失败时，认为是失败
     *     我侧和his侧都成功时，认为是成功
     *     我侧和his侧有一方在支付中时，认为是支付中，等待下次轮训
     *     his侧超过指定时间还是支付中时，认为是失败
     * </pre>
     * @param hisPayParam
     * @param offlineOrder
     */
    private void payOutpatientRegisterChargeInsurance(OfflineOrder offlineOrder, HisPayParam hisPayParam) {
        PayStatus payStatus = PayStatus.PAYING;
        if (hisPayParam.getPaySource() == PaySource.IH) {
            offlineOrder.setPayTransactionId(hisPayParam.getTransactionId());
            // 互联网侧发起的判断
            if (hisPayParam.getPayStatus() == PayStatus.SUCCESS) {
                offlineOrder.setInternetPayStatus(PayStatus.SUCCESS);
                switch (offlineOrder.getHisPayStatus()) {
                    case SUCCESS:
                        // HIS已成功
                        payStatus = PayStatus.SUCCESS;
                        break;
                    case FAIL:
                        // HIS已失败
                        payStatus = PayStatus.FAIL;
                        break;
                    default:
                        // HIS还没出结果，直接查询HIS结果
                        try {
                            BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
                            OutpatientPayResult nodeResult = businessService.getOutpatientPayResult(offlineOrder.getHospital(), offlineOrder.getSettleId(), "0", null);
                            switch (nodeResult.getStatus()) {
                                case "0":
                                    offlineOrder.setHisPayStatus(PayStatus.SUCCESS);
                                    payStatus = PayStatus.SUCCESS;
                                    break;
                                case "1":
                                    // HIS已失败
                                    offlineOrder.setHisPayStatus(PayStatus.FAIL);
                                    payStatus = PayStatus.FAIL;
                                    break;
                                default:

                            }
                        } catch (Exception e) {
                            log.error("门诊预约挂号查询his侧医保支付失败 settleId: " + offlineOrder.getSettleId(), e);
                        }
                        break;
                }
            } else {
                offlineOrder.setInternetPayStatus(PayStatus.PAYING);
            }
        } else {
            offlineOrder.setShBillNo(hisPayParam.getInternetShBillNo());
            // HIS侧发起的判断
            if (hisPayParam.getPayStatus() == PayStatus.SUCCESS) {
                // HIS侧成功
                offlineOrder.setHisPayStatus(PayStatus.SUCCESS);
                // 我如果侧未成功，这里不处理，等待我侧轮训微信结果
                if (offlineOrder.getInternetPayStatus() == PayStatus.SUCCESS) {
                    // 我侧已成功
                    payStatus = PayStatus.SUCCESS;
                }
            } else if (hisPayParam.getPayStatus() == PayStatus.FAIL) {
                // HIS侧失败
                offlineOrder.setHisPayStatus(PayStatus.FAIL);
                offlineOrder.setSettlementSuccessful(false);
                payStatus = PayStatus.FAIL;
            }
        }
        switch (payStatus) {
            case SUCCESS:
                // 互联网侧或HIS侧都成功，这里处理业务流程
                offlineOrder.setSettlementSuccessful(true);
                offlineOrder.setStatus(OutPatientStatus.REGISTERED);
                offlineOrder.setPayTime(hisPayParam.getPayTime());
                offlineOrderRepository.save(offlineOrder);
                // 这里查询his那里的订单信息，更新到offlineOrder中
                if (offlineOrder.getSourceInfo() != null) {
                    saveOrderInfo(offlineOrder);
                }
                sendNoticeOutpatientRegisterMessage(offlineOrder, patientRepository.getById(Long.valueOf(offlineOrder.getPatientId())));
                break;
            case FAIL:
                // 互联网侧或HIS侧有一方确认失败的
                if (offlineOrder.getInternetPayStatus() == PayStatus.SUCCESS) {
                    Date payTime = AppContext.getInstance(PayManager.class).getPayTime(PaymentMethod.WECHAT,
                            true, ThirdOrderType.OUTPATIENT_FEE, offlineOrder.getId());
                    offlineOrder.setPayTime(payTime);
                    offlineOrder.setHisPayStatus(PayStatus.FAIL);
                    offlineOrder.setSettlementSuccessful(false);
                    offlineOrderRepository.save(offlineOrder);
                }
                break;
            default:
                Hospital hospital = offlineOrder.getHospital();

                // 互联网侧或HIS侧有一方在等待结果的状态
                // 超过指定时间一直不成功的
                int expireTime = HospitalSettingsHelper.getInt(hospital, HospitalSettingKey.MEDICAL_INSURANCE_TOKEN_EXPIRE_TIME);
                if (hisPayParam.getPayStartTime() != null && DateUtils.addMinutes(hisPayParam.getPayStartTime(), expireTime).compareTo(new Date()) < 0) {
                    log.info("没支付成功的医保订单，并且过了token过期时间，订单id：{}", offlineOrder.getId());
                    if (offlineOrder.getInternetPayStatus() == PayStatus.SUCCESS) {
                        Date payTime = AppContext.getInstance(PayManager.class).getPayTime(PaymentMethod.WECHAT, true, ThirdOrderType.OUTPATIENT_FEE, offlineOrder.getId());
                        offlineOrder.setPayTime(payTime);
                        offlineOrder.setHisPayStatus(PayStatus.FAIL);
                    }
                    offlineOrder.setSettlementSuccessful(false);
                    offlineOrderRepository.save(offlineOrder);
                }
        }
    }

    /**
     * 医保结算失败的进行全额退款
     * @param order
     */
    public void refundFailedOutpatientInsurance(OfflineOrder order) {
        order.setCancelSettleId("IH" + Snowflake64.Holder.INSTANCE.nextId());
        order.setCancelSerialNo("IH" + Snowflake64.Holder.INSTANCE.nextId());
        RefundOrderVM refundVm = new RefundOrderVM();
        refundVm.setId(order.getId());
        refundVm.setType(ThirdOrderType.OUTPATIENT_REGISTER_FEE);

        // 医保支付只能单笔
        refundVm.setCancelBillNo(order.getCancelSettleId());
        refundVm.setCancelSerialNo(order.getCancelSerialNo());
        refundVm.setRefReason("医保挂号缴费医保退款");
        refundVm.setSelfRefundAmount(order.getSelfAmount());
        refundVm.setInsuranceRefundAmount(order.getInsuranceFee());
        refundVm.setRefundType(InsurancePayMethod.ALL);
        boolean refundedFromWechat = AppContext.getInstance(WechatService.class)
                .insuranceRefund(order.getHospital(), refundVm, Lists.newArrayList(order.getId() + ""));

        if (refundedFromWechat) {
            log.info("退款：退款请求,医保挂号缴费id: " + order.getId());
            order.setStatus(OutPatientStatus.REFUNDING);
        } else {
            log.info("退款：没有支付订单，不需要退款，医保挂号缴费id: " + order.getId());
            order.setStatus(OutPatientStatus.REFUND);
        }
        order.setStatus(OutPatientStatus.REFUNDING);
        order.setRefundSource(OrderRefundSource.IH);
        offlineOrderRepository.save(order);
    }

    private void saveOrderInfo(OfflineOrder offlineOrder) {
        AppointmentInfo sourceInfo = StandardObjectMapper.readValue(offlineOrder.getSourceInfo(), new TypeReference<>() {});
        if (sourceInfo.getDept_id() == null || sourceInfo.getDoctor_id() == null) {
            String patientId = offlineOrder.getPatientId();
            Patient patient = patientRepository.getById(Long.valueOf(patientId));
            try {
                PatientRegistInfo patientRegistInfo = BusinessServiceStrategy.getInstance().getStrategy(true)
                        .getRegistListByRegno(offlineOrder.getHospital(), patient.getName(), offlineOrder.getRegNo(), null,
                                patient.getHisPatid(), "1");
                if (patientRegistInfo != null) {
                    String doctor_id = patientRegistInfo.getDoctor_id();
                    String dept_id = patientRegistInfo.getDept_id();
                    String doctor_name = patientRegistInfo.getDoctor_name();
                    String dept_name = patientRegistInfo.getDept_name();
                    sourceInfo.setDept_id(dept_id);
                    sourceInfo.setDoctor_id(doctor_id);
                    if (StringUtils.isNotBlank(doctor_id)) {
                        // 查询医生信息
                        OfflineMedicalWorker offlineMedicalWorker = offlineMedicalWorkerRepository
                                .findOneByHospitalAndJobNumber(offlineOrder.getHospital(), doctor_id).get();
                        offlineOrder.setDoctor(offlineMedicalWorker);
                    }
                    sourceInfo.setDept_name(dept_name);
                    sourceInfo.setDoctor_name(doctor_name);
                    offlineOrder.setSourceInfo(StandardObjectMapper.stringify(sourceInfo));
                    offlineOrder.setDoctorName(doctor_name);
                    offlineOrder.setDeptName(dept_name);
                    offlineOrder.setHisDeptId(dept_id);
                    offlineOrderRepository.save(offlineOrder);
                }
            } catch (Exception e) {
                log.error("patientId：" + patientId + " RegNo:" + offlineOrder.getRegNo() + " 挂号记录查询失败", e);
            }
        }
    }

    private void sendNoticeOutpatientRegisterMessage(OfflineOrder offlineOrder, Patient patient) {
        try {
            ProjectTypeEnum offlineOrderType = offlineOrder.getType();
            ProjectDetailTypeEnum offlineOrderDetailType = offlineOrder.getDetailType();
            String beginTime = offlineOrder.getBeginTime();
            // 预约发过消息支付成功之后不会再发消息
            if ((offlineOrderType == ProjectTypeEnum.OUTPATIENT_APPOINTMENT_REGISTRATION
                    && offlineOrderDetailType == ProjectDetailTypeEnum.APPOINTMENT)
                    || (offlineOrderType == ProjectTypeEnum.APPOINTMENT_REGISTRATION
                    && offlineOrderDetailType == ProjectDetailTypeEnum.OTHER)) {
                if (TimeUtils.getEndOfDay(new Date()).after(TimeUtils.convert(beginTime))) {
                    log.info("就诊提醒消息发送2-------------------" + patient.getUser().getFullName());
                    noticeService.visitRemindNotice(offlineOrder.getHospital(), patient.getUser(), offlineOrder);
                }
            }
        } catch (Exception e) {
            log.error("组装小程序消息参数错误: " + e.getMessage(), e);
        }
    }

    @Override
    public void refundingAppointmentRegisterCharge(long offlineOrderId, InsurancePayMethod insurancePayMethod, MedicalInsuranceParam insuranceParam,
                                                   String cancelSettleId) {
        refundingAppointmentRegisterCharge(offlineOrderId, OrderRefundSource.IH, insurancePayMethod, insuranceParam,
                cancelSettleId);
    }

//    @Override
//    public boolean stopAppointmentRegisterCharge(long offlineOrderId, OrderRefundSource orderRefundSource, InsurancePayMethod insurancePayMethod) {
//        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
//        if (offlineOrder.getStatus() == OutPatientStatus.WAIT_PAY || offlineOrder.getStatus() == OutPatientStatus.PENDING) {
//            // 未支付的，变更为已取消
//            offlineOrder.setStatus(OutPatientStatus.CANCELLED);
//            offlineOrderRepository.save(offlineOrder);
//            return true;
//        } else if (offlineOrder.getStatus() == OutPatientStatus.COMPLETED || offlineOrder.getStatus() == OutPatientStatus.CANCELLED
//                || offlineOrder.getStatus() == OutPatientStatus.CLOSED) {
//            // 已结束的不处理
//            return false;
//        }
//        refundingAppointmentRegisterCharge(offlineOrderId, orderRefundSource, insurancePayMethod, null);
//        return true;
//    }

    @Override
    public void refundingAppointmentRegisterCharge(long offlineOrderId, OrderRefundSource orderRefundSource, InsurancePayMethod insurancePayMethod,
                                                   MedicalInsuranceParam insuranceParam, String cancelSettleId) {
        lockService.executeWithLockThrowError("lock.offinle.appointment.refunding:" + offlineOrderId, () -> {
            String returnNo = null;
            OfflineOrder order = offlineOrderRepository.getById(offlineOrderId);
            if (order.getSelfFlag() == 1 && !order.getSettlementSuccessful() && orderRefundSource == OrderRefundSource.IH) {
                // 自费his结算失败的,不能主动退款
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("请联系医院窗口处理");
            }
            // 去his退号
            // OrderRefundSource 为IH时是线上退款 需要通知his。OrderRefundSource是HIS时，his通知我们退款的 不需要再通知his了
            if (order.getPayTime() != null) {
                if (orderRefundSource != OrderRefundSource.HIS) {
                    PayPlatform payPlatform = AppContext.getInstance(BusinessOrderManager.class).getPaySuccessTransactionId(order);
                    String transactionId = offlineOrderId + "";
                    if (payPlatform == null) {
                        log.info("预约挂号退款：payPlatform为null, offlineOrderId=" + offlineOrderId);
                    } else {
                        if (insuranceParam != null) {
                            if (insuranceParam.getPlatformType() != payPlatform.getPlatformType()) {
                                // A渠道医保支付的挂号订单，在B渠道申请退号时应该报错：您在其他渠道做的医保支付不能在此渠道退号
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("您在其他渠道做的医保支付不能在此渠道退号");
                            }
                            insuranceParam.setPlatformType(payPlatform.getPlatformType());
                        }
                        transactionId = payPlatform.getTransactionId();
                    }
                    ReturnRegistResult returnRegistResult;
                    try {
                        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
                        returnRegistResult = businessService.returnRegist(order.getHospital(), order, transactionId, insuranceParam);
                    } catch (Exception e) {
                        log.error("his取消挂号结算失败", e);
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("his取消挂号结算失败: " + e.getMessage());
                    }
                    if (returnRegistResult == null || !returnRegistResult.getSuccess()) {
                        log.error("预约挂号 OfflineOrderId: " + order.getId() + "his取消挂号结算失败-----------");
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("his取消挂号结算失败");
                    }
                    order.setCancelSettleId(returnRegistResult.getRefund_settle_id());
                    returnNo = returnRegistResult.getRefund_settle_id();
                } else {
                    if (StringUtils.isBlank(cancelSettleId)) {
                        order.setCancelSettleId(order.getSettleId());
                    } else {
                        order.setCancelSettleId(cancelSettleId);
                        returnNo = cancelSettleId;
                    }
                }
            }
            order.setCancelSerialNo(order.getHisSerialNo());

            RefundOrderVM refundVm = new RefundOrderVM();
            refundVm.setId(order.getId());
            refundVm.setType(ThirdOrderType.OUTPATIENT_REGISTER_FEE);

            if (StringUtils.isBlank(returnNo)) {
                returnNo = offlineOrderId + "";
            }
            if (order.getSelfFlag() == 0) {
                // 医保支付只能单笔
                refundVm.setPayOrdId(order.getInternetPayOrderId());
                refundVm.setCancelBillNo(order.getCancelSettleId());
                refundVm.setCancelSerialNo(order.getCancelSerialNo());
                refundVm.setRefReason("挂号缴费医保退款");
                refundVm.setSelfRefundAmount(order.getSelfAmount());
                refundVm.setInsuranceRefundAmount(order.getInsuranceFee());
                refundVm.setRefundType(insurancePayMethod);
            }
            refundVm.setAggregatePayment(order.getAggregatePayment());
            boolean refundedFromWechat = AppContext.getInstance(PayManager.class).refund(order.getHospital(),
                    order.getSelfFlag() == 0, order.getPaymentMethod(), refundVm, order.getRegistrationFee(), returnNo);

            if (refundedFromWechat) {
                log.info("退款：已经向{}发起退款请求,设置退款状态为退款中 offlineOrderId: {}", order.getPaymentMethod().getName(),
                         order.getId());
                order.setStatus(OutPatientStatus.REFUNDING);
            } else {
                log.info("退款：没有支付订单，不需要退款，将订单状态直接改为REFUND 挂号缴费id: " + order.getId());
                if (!OutPatientStatus.REFUND.equals(order.getStatus())) {
                    // 0元退号
                    refundedAppointmentRegisterCharge(order, returnNo);
                }
            }
            order.setRefundSource(orderRefundSource);
            offlineOrderRepository.save(order);
            return 0;
        });
    }

    @Override
    public void refundingAppointmentRegisterCharge(long offlineOrderId,InsurancePayMethod insurancePayMethod) {
        lockService.executeWithLockThrowError("lock.offinle.appointment.refunding:" + offlineOrderId, () -> {
            String returnNo = null;
            OfflineOrder order = offlineOrderRepository.getById(offlineOrderId);
            // 去his退号
            // OrderRefundSource 为IH时是线上退款 需要通知his。OrderRefundSource是HIS时，his通知我们退款的 不需要再通知his了
            if (order.getPayTime() != null) {
                if (order.getSettlementSuccessful()) {
                    PayPlatform payPlatform = AppContext.getInstance(BusinessOrderManager.class).getPaySuccessTransactionId(order);
                    String transactionId = offlineOrderId + "";
                    if (payPlatform == null) {
                        log.info("预约挂号退款：payPlatform为null, offlineOrderId=" + offlineOrderId);
                    } else {
                        transactionId = payPlatform.getTransactionId();
                    }
                    ReturnRegistResult returnRegistResult;
                    try {
                        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
                        returnRegistResult = businessService.returnRegist(order.getHospital(), order, transactionId,
                                                                          null);
                    } catch (Exception e) {
                        log.error("his取消挂号结算失败", e);
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("his取消挂号结算失败: " + e.getMessage());
                    }
                    if (returnRegistResult == null || !returnRegistResult.getSuccess()) {
                        log.error("预约挂号 OfflineOrderId: " + order.getId() + "his取消挂号结算失败-----------");
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("his取消挂号结算失败");
                    }
                    order.setCancelSettleId(returnRegistResult.getRefund_settle_id());
                    order.setCancelSerialNo(order.getHisSerialNo());
                    returnNo = returnRegistResult.getRefund_settle_id();
                } else {

                    order.setCancelSettleId(order.getSettleId());
                    order.setCancelSerialNo(order.getHisSerialNo());
                }
            }

            RefundOrderVM refundVm = new RefundOrderVM();
            refundVm.setId(order.getId());
            refundVm.setType(ThirdOrderType.OUTPATIENT_REGISTER_FEE);

            if (StringUtils.isBlank(returnNo)) {
                returnNo = offlineOrderId + "";
            }
            if (order.getSelfFlag() == 0) {
                // 医保支付只能单笔
                refundVm.setPayOrdId(order.getInternetPayOrderId());
                refundVm.setCancelBillNo(order.getCancelSettleId());
                refundVm.setCancelSerialNo(order.getCancelSerialNo());
                refundVm.setRefReason("挂号缴费医保退款");
                refundVm.setSelfRefundAmount(order.getSelfAmount());
                refundVm.setInsuranceRefundAmount(order.getInsuranceFee());
                refundVm.setRefundType(insurancePayMethod);
            }
            boolean refundedFromWechat = AppContext.getInstance(PayManager.class).refund(order.getHospital(),
                                                                                         order.getSelfFlag() == 0, order.getPaymentMethod(), refundVm, order.getRegistrationFee(), returnNo);

            if (refundedFromWechat) {
                log.info("退款：已经向{}发起退款请求,设置退款状态为退款中 offlineOrderId: {}", order.getPaymentMethod().getName(),
                         order.getId());
                order.setStatus(OutPatientStatus.REFUNDING);
            } else {
                log.info("退款：没有支付订单，不需要退款，将订单状态直接改为REFUND 挂号缴费id: " + order.getId());
                if (!OutPatientStatus.REFUND.equals(order.getStatus())) {
                    // 0元退号
                    refundedAppointmentRegisterCharge(order, returnNo);
                }
            }
            order.setRefundSource(OrderRefundSource.IH);
            offlineOrderRepository.save(order);
            return 0;
        });
    }

    // 删除offlineOrder
    @Override
    public void deleteOfflineOrder(long offlineOrderId) {
        Optional<OfflineOrder> byId = offlineOrderRepository.findById(offlineOrderId);
        if (byId.isEmpty()) {
            return;
        }
        OfflineOrder order = byId.get();
        if (!OutPatientStatus.getEnableToDeleteOrders().contains(order.getStatus())) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确");
        }
        order.setEnabled(false);
        offlineOrderRepository.save(order);
    }
    @Override
    @Transactional
    public OfflineOrder refundedAppointmentRegisterCharge(long offlineOrderId, HisRefundParam refund) {
        log.info("退款：退款成功，挂号缴费id: {}", offlineOrderId);
        OfflineOrder order = offlineOrderRepository.getById(offlineOrderId);
        if (order.getPayTime() == null) {
            order.setPayTime(new Date());
        }
        order.setSelfRefundAmount(order.getSelfAmount());
        order.setInsuranceRefundFee(order.getInsuranceFee());
        order.setStatus(OutPatientStatus.REFUND);
        order.setRefundTime(refund.getRefundTime());
        order.setRefundTransactionId(refund.getTransactionId());
        offlineOrderRepository.save(order);
        log.info("退款：退款成功，挂号缴费id: {} order saved", offlineOrderId);
        JpaUtils.executeInTransaction(() -> {
            HospitalRepository hospitalRepository = AppContext.getInstance(HospitalRepository.class);
            sendRefundedMessage(order, refund, offlineOrderId, hospitalRepository.getById(order.getHospital().getId()));
            return 0;
        });
        return order;
    }

    private void sendRefundedMessage(OfflineOrder order, HisRefundParam refund, long offlineOrderId, Hospital hospital) {
        try {
            redisUtil.del("out_patient_register:" + order.getId());
            String patientId = order.getPatientId();
            Patient patient = patientRepository.getById(Long.valueOf(patientId));
            Map<String, String> registerVisitDetail = Maps.newHashMap();
            registerVisitDetail.put("patient", patient.getName());
            registerVisitDetail.put("hospital", hospital.getName());
            registerVisitDetail.put("desc", "退款金额：" + MathUtils.division2(refund.getAmount(), 100) + "元 (预约挂号)");
            registerVisitDetail.put("dept", StringUtils.isNotBlank(order.getDeptName()) ? order.getDeptName(): "-");
            registerVisitDetail.put("path", "/subpackages/pat/register-order-detail/index?orderId=" + offlineOrderId + "&pid=" + patientId);
            registerVisitDetail.put("product_id", offlineOrderId + "");
            registerVisitDetail.put("notice_content", String.format("您好，%s，医院已退款【%s】元（【预约挂号】），请注意查收",
                                                                    patient.getName(), MathUtils.division2(refund.getAmount(), 100)));

            String url = AppContext.getInstance(WechatService.class).getAddHospitalUrl(hospital
                , HospitalSettingsHelper.getString(hospital, HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                , "/subpackages/pat/register-order-detail/index?orderId=" + order.getId());

            // 微信公众号消息对象
            WechatTemplatedData data = new WechatTemplatedData();
            data.setUrl(url);
            // 患者姓名
            data.addKeywordsStr(patient.getName());
            // 医疗机构
            data.addKeywordsStr(hospital.getName());
            // 退费金额
            data.addKeywordsStr(MathUtils.division2(refund.getAmount(), 100));
            // 退款时间
            data.addKeywordsStr(TimeUtils.dateToString(order.getRefundTime(), "yyyy-MM-dd HH:mm:ss"));

            AliPayTemplatedData aliPayTemplatedData = new AliPayTemplatedData();
            aliPayTemplatedData.setUrl("/subpackages/pat/register-order-detail/index?orderId=" + offlineOrderId + "&pid=" + patientId);
            // 患者姓名
            aliPayTemplatedData.addKeywords(patient.getName());
            // 退费金额
            aliPayTemplatedData.addKeywords(MathUtils.division2(refund.getAmount(), 100));
            // 退款时间
            aliPayTemplatedData.addKeywords(TimeUtils.dateToString(order.getRefundTime(), "yyyy-MM-dd HH:mm:ss"));

            // 消息中心对象
            MessageCenterDTO mcDTO  = new MessageCenterDTO();
            mcDTO.setTittle("退款成功通知");
            mcDTO.setContent(patient.getName() + ",医院已退款" + MathUtils.division2(refund.getAmount(), 100) + "元（预约挂号），请注意查收。");
            mcDTO.setPatient(patient);
            mcDTO.setPatientName(patient.getName());
            mcDTO.setDeptName(order.getDeptName());
            mcDTO.setProductId(order.getId());
            mcDTO.setH5Path(AppContext.getInstance(WechatService.class).getAddHospitalUrl(hospital
                , HospitalSettingsHelper.getString(hospital, HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                , "/subpackages/pat/register-order-detail/index?orderId=" + order.getId() + "&pid=" + patient.getId()));
            mcDTO.setMiniPath("/subpackages/pat/register-order-detail/index?orderId=" + order.getId() + "&pid=" + patient.getId());

            noticeService.miniProgramNotice(hospital, patient.getUser(),
                                            HospitalSettingKey.NOTICE_OUT_REGISTER_REFUNDED, registerVisitDetail, mcDTO, data, aliPayTemplatedData);
        } catch (Exception e) {
            log.error("组装退款小程序推送消息失败", e);
        }
    }

    // 0元退号
    private void refundedAppointmentRegisterCharge(OfflineOrder order, String returnNo) {
        order.setStatus(OutPatientStatus.REFUND);
        Date refundDate = new Date();
        order.setRefundTime(refundDate);
        String refundTime = TimeUtils.dateToString(refundDate, "yyyy-MM-dd HH:mm:ss");
        // 通知HIS退款成功
        payManager.refundSuccess(
                order.getElectronicMedicCard().getHisPatid(),
                Lists.newArrayList(returnNo),
                "",
                "",
                0,
                refundTime,
                order.getHospital().getId(),
                ThirdOrderType.OUTPATIENT_REGISTER_FEE);
        offlineOrderRepository.save(order);
        // 通知用户
        try {
            redisUtil.del("out_patient_register:" + order.getId());
            String patientId = order.getPatientId();
            Patient patient = patientRepository.getById(Long.valueOf(patientId));
            Map<String, String> registerVisitDetail = Maps.newHashMap();
            registerVisitDetail.put("patient", patient.getName());
            registerVisitDetail.put("hospital", order.getHospital().getName());
            registerVisitDetail.put("desc", "退款金额：0元 (预约挂号)");
            registerVisitDetail.put("dept", StringUtils.isNotBlank(order.getDeptName()) ? order.getDeptName(): "-");
            registerVisitDetail.put("path", "/subpackages/pat/register-order-detail/index?orderId=" + order.getId() + "&pid=" + patientId);
            registerVisitDetail.put("product_id", order.getDoctorName() + "");
            registerVisitDetail.put("notice_content", String.format("您好，%s，医院已退款【%s】元（【预约挂号】），请注意查收",
                    patient.getName(), 0));

            String url = AppContext.getInstance(WechatService.class).getAddHospitalUrl(order.getHospital()
                    , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                    , "/subpackages/pat/register-order-detail/index?orderId=" + order.getId());

            // 微信公众号消息对象
            WechatTemplatedData data = new WechatTemplatedData();
            data.setUrl(url);
            // 患者姓名
            data.addKeywordsStr(patient.getName());
            // 医疗机构
            data.addKeywordsStr(order.getHospital().getName());
            // 退费金额
            data.addKeywordsStr("0");
            // 退款时间
            data.addKeywordsStr(TimeUtils.dateToString(order.getRefundTime(), "yyyy-MM-dd HH:mm:ss"));

            AliPayTemplatedData aliPayTemplatedData = new AliPayTemplatedData();
            aliPayTemplatedData.setUrl("/subpackages/pat/register-order-detail/index?orderId=" + order.getId() + "&pid=" + patientId);
            // 患者姓名
            aliPayTemplatedData.addKeywords(patient.getName());
            // 退费金额
            aliPayTemplatedData.addKeywords("0");
            // 退款时间
            aliPayTemplatedData.addKeywords(TimeUtils.dateToString(order.getRefundTime(), "yyyy-MM-dd HH:mm:ss"));

            // 消息中心对象
            MessageCenterDTO mcDTO  = new MessageCenterDTO();
            mcDTO.setContent(patient.getName() + ",医院已退款0元（预约挂号），请注意查收。");
            mcDTO.setPatient(patient);
            mcDTO.setPatientName(patient.getName());
            mcDTO.setDeptName(order.getDeptName());
            mcDTO.setProductId(order.getId());
            mcDTO.setH5Path(AppContext.getInstance(WechatService.class).getAddHospitalUrl(order.getHospital()
                    , HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.NOTIFY_USR_URL_PREFIX)
                    , "/subpackages/pat/register-order-detail/index?orderId=" + order.getId() + "&pid=" + patient.getId()));
            mcDTO.setMiniPath("/subpackages/pat/register-order-detail/index?orderId=" + order.getId() + "&pid=" + patient.getId());

            noticeService.miniProgramNotice(order.getHospital(), patient.getUser(),
                    HospitalSettingKey.NOTICE_OUT_REGISTER_REFUNDED, registerVisitDetail, mcDTO, data, aliPayTemplatedData);
        } catch (Exception e) {
            log.error("组装退款小程序推送消息失败", e);
        }
    }

    @Override
    @Transactional
    public NucleicAcidOrderDTO createNucleicAcidOrder(Hospital hospital, CreateNucleicAcidOrderDTO createNucleicAcidOrderDTO) {
        String patientId = createNucleicAcidOrderDTO.getPatient().getId().toString();
        Date date = new Date();
        Date startOfDay = TimeUtils.getStartOfDay(date);
        Date endOfDay = TimeUtils.getEndOfDay(date);
        Optional<OfflineOrder> firstOfflineOrder = offlineOrderRepository.findFirstByHospitalAndPatientIdAndTypeAndCreatedDateBetween(hospital, patientId,
                createNucleicAcidOrderDTO.getType(), startOfDay, endOfDay);
        if (firstOfflineOrder.isPresent()) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("每天每个患者只可预约一次，请明天再来预约");
        }
        OfflineOrder offlineOrder = new OfflineOrder();
        offlineOrder.setHospital(hospital);
        offlineOrder.setPatientId(patientId);
        offlineOrder.setElectronicMedicCard(electronicMedicCardRepository.getById(createNucleicAcidOrderDTO.getElectronicMedicCard().getId()));
        offlineOrder.setStatus(OutPatientStatus.WAIT_PAY);
        offlineOrder.setPaymentMethod(createNucleicAcidOrderDTO.getPaymentMethod());
        offlineOrder.setType(createNucleicAcidOrderDTO.getType());
        // TODO his内的固定医生 需要确定
        OfflineMedicalWorker offlineDoctor = offlineMedicalWorkerRepository.findFirstByHospital(hospital).orElse(null);
        if (offlineDoctor == null) {
            throw ErrorType.NOT_FOUND_ERROR.toProblem("开单医生不存在");
        }
        // TODO his内的固定科室 需要确定
        OfflineDept offlineDept = offlineDeptRepository.findFirstByHospital(hospital).orElse(null);
        if (offlineDept == null) {
            throw ErrorType.NOT_FOUND_ERROR.toProblem("开单科室不存在");
        }
        offlineOrder.setDoctor(offlineDoctor);
        offlineOrder.setDept(offlineDept);
        offlineOrder.setDocumentItem("核酸检验预约");
        // TODO 项目名称 his获取
        offlineOrder.setEntryName("新冠病毒核酸检测");
        // TODO 预约收费 his获取 先给1分钱
        offlineOrder.setRegistrationFee(1);
        offlineOrder.setSelfAmount(1);
        offlineOrderRepository.save(offlineOrder);
        // TODO 接入his时走门诊收费预算
        // businessService.preRegister(hospital, offlineOrder, registrationFeeCode, treatmentFeeCode, childrenTreatmentFeeCode);
        return new NucleicAcidOrderDTO(offlineOrder);
    }

    @Override
    @Transactional
    public void nucleicAcidOrderPending(long offlineOrderId) {
        log.info("用户核酸预约订单支付中offlineOrderId {}", offlineOrderId);
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        if (offlineOrder.getStatus() == OutPatientStatus.PENDING) {
            return;
        }
        if (offlineOrder.getStatus() != OutPatientStatus.WAIT_PAY && offlineOrder.getStatus() != OutPatientStatus.PENDING) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("订单状态不正确");
        }
        if (offlineOrder.getStatus() == OutPatientStatus.WAIT_PAY) {
            offlineOrder.setStatus(OutPatientStatus.PENDING);
            offlineOrderRepository.save(offlineOrder);
        }
    }

    @Override
    public void payNucleicAcidCharge(User user, long offlineOrderId, WechatOrder wechatOrder) {
        log.info("用户{}支付核酸预约费用 offlineOrderId: {}", user.getId(), offlineOrderId);
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        if (offlineOrder.getStatus() != OutPatientStatus.PENDING) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("订单状态不正确");
        }
        // 与病案复印逻辑相同，不走his结算逻辑，通过申请电子发票接口通知his写入数据

        offlineOrder.setStatus(OutPatientStatus.COMPLETED);
        offlineOrderRepository.save(offlineOrder);
    }

    @Override
    @Transactional
    public Page<OfflineOrder> findOfflineOrderListByType(Hospital hospital, String patientId, String status, String orderId,
                                                         String type, String cardNo, Integer pageNum, Integer pageSize) {
        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        Patient patient = null;
        if (StringUtils.isNotBlank(patientId)) {
            patient = patientRepository.findById(Long.valueOf(patientId)).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
            specs.add(Specifications.eq("patientId", patientId));
        }
        if (StringUtils.isNotBlank(status)) {
            List<Specification<OfflineOrder>> orderStatusSpecs = Lists.newArrayList();
            String[] orderStatusContains = status.split(" ");
            for (String orderStatusContain : orderStatusContains) {
                orderStatusSpecs.add(Specifications.eq("status", OutPatientStatus.valueOf(orderStatusContain.toUpperCase())));
            }
            specs.add(Specifications.or(orderStatusSpecs));
            // 1.如果刚进来的时候就诊人没有医技预约订单,创建一单医技预约订单
            if (ProjectTypeEnum.valueOf(type.toUpperCase()) == ProjectTypeEnum.MEDICAL_APPOINTMENT
                    && orderStatusContains.length == 1 && OutPatientStatus.valueOf(status.toUpperCase()) == OutPatientStatus.APPOINTMENT) {
                List<OfflineOrder> offlineOrders = offlineOrderRepository.findAllByHospitalAndPatientIdAndTypeAndStatus(hospital, patientId,
                        ProjectTypeEnum.MEDICAL_APPOINTMENT, OutPatientStatus.APPOINTMENT);
                if (CollectionUtils.isEmpty(offlineOrders)) {
                    OfflineOrder offlineOrder = new OfflineOrder();
                    offlineOrder.setHospital(hospital);
                    offlineOrder.setPatientId(patientId);
                    offlineOrder.setAppointmentDate(null);
                    ElectronicMedicCard card = electronicMedicCardRepository.findByNumberAndCardTypeAndPatientAndOnlineType(cardNo, ElectronicMedicCard.CardType.SELF_PAY,
                            patient, ElectronicMedicCard.OnlineType.HIS).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
                    offlineOrder.setElectronicMedicCard(card);
                    offlineOrder.setStatus(OutPatientStatus.APPOINTMENT);
                    offlineOrder.setPaymentMethod(PaymentMethod.WECHAT);
                    offlineOrder.setType(ProjectTypeEnum.MEDICAL_APPOINTMENT);
                    offlineOrder.setRegistrationFee(7800);
                    offlineOrder.setSelfAmount(7800);
                    offlineOrder.setDeptName("门诊CT");
                    offlineOrder.setEntryName("头部CT");
                    offlineOrder.setConsultationNumber("10030304");
                    // 开单时间为再次创建时间-2小时
                    offlineOrder.setCreatedDate(TimeUtils.getDateBeforeHours(2));
                    offlineOrderRepository.save(offlineOrder);
                }
            }
        }
        if (StringUtils.isNotBlank(orderId)) {
            specs.add(Specifications.eq("id", Long.valueOf(orderId)));
        }
        specs.add(Specifications.eq("type", ProjectTypeEnum.valueOf(type.toUpperCase())));
        specs.add(Specifications.orderBy(Sort.Direction.DESC, "createdDate"));
        return offlineOrderRepository.findAll(Specifications.and(specs), PageRequest.of(pageNum, pageSize));
    }

    @Override
    public UploadVM exportOfflineOrderListByType(Hospital hospital, String status, String orderId, String type, HttpServletResponse response) {
        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        if (StringUtils.isNotBlank(status)) {
            List<Specification<OfflineOrder>> orderStatusSpecs = Lists.newArrayList();
            String[] orderStatusContains = status.split(" ");
            for (String orderStatusContain : orderStatusContains) {
                orderStatusSpecs.add(Specifications.eq("status", OutPatientStatus.valueOf(orderStatusContain.toUpperCase())));
            }
            specs.add(Specifications.or(orderStatusSpecs));
        }
        if (StringUtils.isNotBlank(orderId)) {
            specs.add(Specifications.eq("id", Long.valueOf(orderId)));
        }
        specs.add(Specifications.eq("type", ProjectTypeEnum.valueOf(type.toUpperCase())));
        specs.add(Specifications.orderBy(Sort.Direction.DESC, "createdDate"));
        List<NucleicAcidExportDTO> data = offlineOrderRepository.findAll(Specifications.and(specs)).stream()
                .map(NucleicAcidExportDTO::new).collect(Collectors.toList());
        return exportTaskService.exportNucleicAcid(data, response);
    }

    @Override
    @Transactional
    public void updateMedicalAppointmentOrder(Hospital hospital, CreateMedicalAppointmentOrderDTO param) {
        OfflineOrder medicalAppointmentOrder = offlineOrderRepository.getById(param.getId());
        medicalAppointmentOrder.setRegistrationFee(7800);
        medicalAppointmentOrder.setSelfAmount(7800);
        medicalAppointmentOrder.setConsultingRoom(param.getConsultingRoom());
        medicalAppointmentOrder.setConsultationNumber("10030304");
        medicalAppointmentOrder.setAppointmentDate(new Date());
        medicalAppointmentOrder.setAppointmentTimeSlot(param.getAppointmentTimeSlot());
        medicalAppointmentOrder.setStatus(OutPatientStatus.RESERVED);
        offlineOrderRepository.save(medicalAppointmentOrder);
        List<OfflineOrder> offlineOrders = offlineOrderRepository.findAllByHospitalAndPatientIdAndTypeAndStatus(hospital, String.valueOf(param.getPatient().getId()),
                ProjectTypeEnum.MEDICAL_APPOINTMENT, OutPatientStatus.RESERVED);
        // 如果患者是第一次状态变成已预约，创建两单已完成和已经过期的预约订单
        if (offlineOrders.size() == 1) {
            // 创建已医技预约订单
            OfflineOrder completedOfflineOrder = new OfflineOrder();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            completedOfflineOrder.setHospital(hospital);
            completedOfflineOrder.setPatientId(String.valueOf(param.getPatient().getId()));
            completedOfflineOrder.setElectronicMedicCard(electronicMedicCardRepository.getById(param.getElectronicMedicCard().getId()));
            completedOfflineOrder.setAppointmentDate(TimeUtils.getDateBeforeHours(72));
            completedOfflineOrder.setAppointmentTimeSlot(sdf.format(TimeUtils.getDateBeforeHours(48)) + " 09:00:00-09:30:00");
            completedOfflineOrder.setStatus(OutPatientStatus.COMPLETED);
            completedOfflineOrder.setPaymentMethod(PaymentMethod.WECHAT);
            completedOfflineOrder.setType(ProjectTypeEnum.MEDICAL_APPOINTMENT);
            createOrder(completedOfflineOrder);
            completedOfflineOrder.setCreatedDate(TimeUtils.getDateBeforeHours(74));
            offlineOrderRepository.save(completedOfflineOrder);

            // 创建已过期医技预约订单
            OfflineOrder expiredOfflineOrder = new OfflineOrder();
            expiredOfflineOrder.setHospital(hospital);
            expiredOfflineOrder.setPatientId(String.valueOf(param.getPatient().getId()));
            expiredOfflineOrder.setElectronicMedicCard(electronicMedicCardRepository.getById(param.getElectronicMedicCard().getId()));
            expiredOfflineOrder.setAppointmentDate(TimeUtils.getDateBeforeHours(168));
            expiredOfflineOrder.setAppointmentTimeSlot(sdf.format(TimeUtils.getDateBeforeHours(144)) + " 09:00:00-09:30:00");
            expiredOfflineOrder.setStatus(OutPatientStatus.EXPIRED);
            expiredOfflineOrder.setPaymentMethod(PaymentMethod.WECHAT);
            expiredOfflineOrder.setType(ProjectTypeEnum.MEDICAL_APPOINTMENT);
            createOrder(expiredOfflineOrder);
            expiredOfflineOrder.setCreatedDate(TimeUtils.getDateBeforeHours(170));
            offlineOrderRepository.save(expiredOfflineOrder);
        }
    }

    private OfflineOrder createOrder(OfflineOrder offlineOrder) {
        offlineOrder.setRegistrationFee(7800);
        offlineOrder.setSelfAmount(7800);
        offlineOrder.setDeptName("门诊CT");
        offlineOrder.setConsultingRoom("CT二室");
        offlineOrder.setEntryName("头部CT");
        offlineOrder.setConsultationNumber("10030304");
        return offlineOrder;
    }

    @Override
    public OfflineOrderDTO getOfflineOrderDetail(long offlineOrderId) {
        // 查询his订单详请，如果offlineOrder中的科室医生信息没有就先更新
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        return new OfflineOrderDTO(offlineOrder);
    }

    @Override
    public OfflineOrderDTO getOfflineOrderDetailWithInsurance(long offlineOrderId) {
        // 查询his订单详请，如果offlineOrder中的科室医生信息没有就先更新
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        if (offlineOrder.getSelfFlag() != 0 || OutPatientStatus.REGISTERED.equals(offlineOrder.getStatus())) {
            return new OfflineOrderDTO(offlineOrder);
        }

        if (System.currentTimeMillis() - offlineOrder.getUpdatedDate().getTime() < 120_000) {
            Hospital hospital = CurrentHospital.getOrThrow();
            offlineOrder = AppContext.getInstance(BusinessOrderManager.class).checkAndPay(hospital, offlineOrder);
        }
        return new OfflineOrderDTO(offlineOrder);
    }

    @Override
    public int getPatientTotal(Hospital hospital, OfflineMedicalWorker offlineMedicalWorker) {
        // 服务人数为累计挂号总人数（通过就医服务渠道的挂号-线上记录的所有状态，在平台挂过号的）
        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.eq("doctor", offlineMedicalWorker));
        specs.add(Specifications.eq("type", ProjectTypeEnum.APPOINTMENT_REGISTRATION));
        long patientTotal = offlineOrderRepository.count(Specifications.and(specs));
        return (int) patientTotal;
    }

    @Override
    public Page<OfflineMedicalWorkerDTO> getOfflineMedicalWorkerList(Hospital hospital, User user, Integer pageNo, Integer size) {
        List<Specification<OfflineOrder>> specs = Arrays.asList(
                Specifications.eq("hospital", hospital),
                Specifications.eq("electronicMedicCard.patient.user", user),
                Specifications.eq("type", ProjectTypeEnum.APPOINTMENT_REGISTRATION),
                Specifications.isNotNull("doctor"),
                Specifications.orderBy(Sort.Direction.DESC, "createdDate")
        );
        List<OfflineOrder> distinctOrders = new ArrayList<>(offlineOrderRepository.findAll(Specifications.and(specs))
                .stream()
                .collect(Collectors.toMap(OfflineOrder::getDoctor, order -> order, (order1, order2) -> order1, LinkedHashMap::new))
                .values());
        Page<OfflineOrder> offlineOrdersPage = PageUtils.paginate(distinctOrders, pageNo, size);
        return offlineOrdersPage.map(offlineOrder -> {
            OfflineMedicalWorker offlineMedicalWorker = offlineOrder.getDoctor();
            OfflineMedicalWorkerDTO offlineMedicalWorkerDTO = new OfflineMedicalWorkerDTO(offlineMedicalWorker);
            offlineMedicalWorkerDTO.setFavorableRate(evaluateService.getFavorableRate(hospital, offlineMedicalWorker));
            offlineMedicalWorkerDTO.setPatientTotal(getPatientTotal(hospital, offlineMedicalWorker));
            return offlineMedicalWorkerDTO;
        });
    }

    @Override
    public void cancelAppointment(Hospital hospital, long appointmentId) {
        OfflineOrder offlineOrder = offlineOrderRepository.getById(appointmentId);
        if (offlineOrder.getSelfFlag() == 0 && offlineOrder.getInternetPayStatus() == PayStatus.SUCCESS
                && offlineOrder.getHisPayStatus() != PayStatus.FAIL) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单结算确认中, 请稍候再试");
        }

        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        ReponseResult result = businessService.cancelAppointment(hospital, offlineOrder);
        if (result.isSuccess()) {
            offlineOrder.setStatus(OfflineOrder.OutPatientStatus.CANCELLED);
            offlineOrderRepository.save(offlineOrder);
        } else {
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(result.getMessage());
        }

    }

    @Override
    public void cancelRegister(Hospital hospital, Patient patient, long appointmentId) {
        OfflineOrder offlineOrder = offlineOrderRepository.getById(appointmentId);
        if (offlineOrder.getType() != ProjectTypeEnum.APPOINTMENT_REGISTRATION) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单类型不正确");
        }
//        if (offlineOrder.getSelfFlag() == 0) {
//            //医保挂号
//            if (offlineOrder.getStatus() != OfflineOrder.OutPatientStatus.WAIT_PAY) {
//                throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确，无法取消预约");
//            }
//        }
//        if (offlineOrder.getSelfFlag() == 1) {
//            //自费挂号
//            if (offlineOrder.getStatus() != OfflineOrder.OutPatientStatus.WAIT_PAY && offlineOrder.getStatus() != OutPatientStatus.PENDING) {
//                throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确");
//            }
//        }
        if (offlineOrder.getStatus() != OfflineOrder.OutPatientStatus.WAIT_PAY && offlineOrder.getStatus() != OutPatientStatus.PENDING) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确");
        }

        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        if (StringUtils.isNotBlank(offlineOrder.getSettleId())) {
            businessService.cancelPreRegist(hospital, patient, offlineOrder.getRegNo(), offlineOrder.getSettleId());
        }
        if (offlineOrder.getDetailType() == ProjectDetailTypeEnum.APPOINTMENT) {
            businessService.cancelAppointment(hospital, offlineOrder);
        }
        offlineOrder.setStatus(OfflineOrder.OutPatientStatus.CANCELLED);
        offlineOrderRepository.save(offlineOrder);
    }

    @Override
    public boolean checkRegistered(Hospital hospital, String hisPatid, Date time, String hisDeptId) {
        int dailyOfflineCount = HospitalSettingsHelper.getInt(hospital, HospitalSettingKey.DAILY_OFFLINE_ORDER_COUNT);
        List<Specification<OfflineOrder>> sps = new ArrayList<>();
        sps.add(Specifications.eq("hospital", hospital));
        sps.add((Specification<OfflineOrder>) (root, query, criteriaBuilder) -> criteriaBuilder.in(root.get("type")).value(
                Lists.newArrayList(ProjectTypeEnum.APPOINTMENT_REGISTRATION, ProjectTypeEnum.OUTPATIENT_APPOINTMENT_REGISTRATION)));
        sps.add((Specification<OfflineOrder>) (root, query, criteriaBuilder) -> criteriaBuilder.in(root.get("status"))
                .value(Lists.newArrayList(OutPatientStatus.WAIT_PAY, OutPatientStatus.PENDING,
                        OutPatientStatus.REGISTERED, OutPatientStatus.REFUNDING, OutPatientStatus.APPOINTMENT,
                        OutPatientStatus.RESERVED, OutPatientStatus.CONFIRMED, OutPatientStatus.WAITTREAT)));
        sps.add(Specifications.eq("electronicMedicCard.hisPatid", hisPatid));
        sps.add(Specifications.between("beginTime", DataTypes.DATE.asString(TimeUtils.getStartOfDay(time), "yyyyMMdd"),
                DataTypes.DATE.asString(TimeUtils.getEndOfDay(time), "yyyyMMdd")));
        sps.add(Specifications.eq("hisDeptId", hisDeptId));
        List<OfflineOrder> offlineOrders = offlineOrderRepository.findAll(Specifications.and(sps));
        return offlineOrders.size() < dailyOfflineCount;
    }

    @Override
    public OfflineOrderDTO preRegister(Hospital hospital, OfflineOrder order, MedicalInsuranceParam insuranceParam) {
        // 查排班号序 选择最小的状态为0的号序
        ElectronicMedicCard card = order.getElectronicMedicCard();
        Patient patient = card.getPatient();
        String registrationFeeCode = null;
        String treatmentFeeCode = null;
        String childrenTreatmentFeeCode = null;
        try {
            SchedulingDoctorSourceInfo schedulingDoctorSourceInfo = StandardObjectMapper.readValue(order.getSourceInfo(),
                    new TypeReference<>() {});
            registrationFeeCode = schedulingDoctorSourceInfo.getRegistration_fee_code();
            treatmentFeeCode = schedulingDoctorSourceInfo.getTreatment_fee_code();
            childrenTreatmentFeeCode = schedulingDoctorSourceInfo.getChildren_treatment_fee_code();
        } catch (Exception e) {
            log.error("挂号参数错误请重新挂号，" + order.getSourceInfo(), e);
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("挂号参数错误请重新挂号");
        }
        if (StringUtils.isBlank(registrationFeeCode) && StringUtils.isBlank(treatmentFeeCode) && StringUtils.isBlank(childrenTreatmentFeeCode)) {
            log.error("挂号参数错误请重新挂号，" + order.getSourceInfo());
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("挂号参数错误请重新挂号");
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        businessService.outPatientPreRegister(hospital, order, patient, registrationFeeCode,
//                treatmentFeeCode, childrenTreatmentFeeCode, insuranceParam);
        businessService.preRegisterOfflineOrder(hospital, order, registrationFeeCode,
                                              treatmentFeeCode, childrenTreatmentFeeCode, insuranceParam);
        boolean isWanda = HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.WANDA_PAY_ENABLED);
        if (isWanda) {
            order.setAggregatePayment(AggregatePayment.WANDA);
        }
        offlineOrderRepository.save(order);
        return new OfflineOrderDTO(order);
    }

    /**
     * 查询用户挂号列表
     * @return
     */
    @Override
    public List<PatientRegistInfoVM> getPatientRegistInfo(Patient patient, String orderStatusContains, Long cardId,
                                                        Hospital hospital) {

        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("patientId", patient.getId()));
        List<Specification<OfflineOrder>> typeSpecs = Lists.newArrayList();
        typeSpecs.add(Specifications.eq("type", ProjectTypeEnum.APPOINTMENT_REGISTRATION));
        typeSpecs.add(Specifications.eq("type", ProjectTypeEnum.OUTPATIENT_APPOINTMENT_REGISTRATION));
        specs.add(Specifications.or(typeSpecs));
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.isTrue("enabled"));
        if (cardId != null) {
            specs.add(Specifications.eq("electronicMedicCard.id", cardId));
        }
        // 如果orderStatusContains不是空字符串，就将orderStatusContains按照空格分隔成List，然后将List中的每个元素转换成OfflineOrder
        // .OutPatientStatus枚举加入查询条件
        if (StringUtils.isNotBlank(orderStatusContains)) {
            List<Specification<OfflineOrder>> orSpecs = Lists.newArrayList();
            String[] split = orderStatusContains.split(" ");
            for (String s : split) {
                orSpecs.add(Specifications.eq("status" ,OfflineOrder.OutPatientStatus.valueOf(s)));
            }
            specs.add(Specifications.or(orSpecs));
        }
        List<OfflineOrder> offlineOrderList = offlineOrderRepository.findAll(Specifications.and(specs), Sort.by(Sort.Direction.DESC, "id"));
        List<PatientRegistInfoVM> ihOrders = offlineOrderList.stream().map(PatientRegistInfoVM::new)
            .collect(Collectors.toList());

        if (StringUtils.isNotBlank(orderStatusContains) 
                && orderStatusContains.contains("REGISTERED") && orderStatusContains.contains("COMPLETED")
                && HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.QUERY_HIS_ORDERS)) {
            ElectronicMedicCard electronicMedicCard = electronicMedicCardRepository.getById(cardId);
            BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);

            List<PatientRegistInfo> registListByPatId = businessService.getRegistListByPatId(hospital,
                                                                                             patient.getName(),
                                                                                             electronicMedicCard.getHisPatid(),
                                                                                             TimeUtils.dateToString(
                                                                                                 new Date(0)),
                                                                                             TimeUtils.dateToString(
                                                                                                 DateUtils.addMonths(new Date(), 1)), null);
            List<PatientRegistInfoVM> hisOrders = registListByPatId.stream().map(PatientRegistInfoVM::new)
                .collect(Collectors.toList());
            // 最终合并结果
            List<PatientRegistInfoVM> result = new ArrayList<>(ihOrders);

            // 获取ihOrders中所有regno的集合
            Set<String> ihRegnos = ihOrders.stream()
                .map(PatientRegistInfoVM::getRegno)
                .collect(Collectors.toSet());

            // 过滤hisOrders中不存在的regno并合并
            hisOrders.stream()
                .filter(order -> !ihRegnos.contains(order.getRegno()))
                .forEach(result::add);
            result.sort(Comparator.comparing(PatientRegistInfoVM::getVisit_time, Comparator.nullsFirst(Comparator.reverseOrder()))); // 第二排序字段
            return result;
        }

        return ihOrders;
    }

    @Override
    public void changeAggregatePayment(OfflineOrder order, AggregatePayment aggregatePayment) {
        order.setAggregatePayment(aggregatePayment);
        offlineOrderRepository.save(order);
    }

}

