package cn.taihealth.ih.service.impl.filter.billNew;

import cn.taihealth.ih.domain.hospital.BillNew;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Date;
import org.springframework.data.jpa.domain.Specification;

public class RefundEndDateFilter implements SearchFilter<BillNew> {
    private final Date date;

    public RefundEndDateFilter(Date date) {
        this.date = date;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<BillNew> toSpecification() {
        return Specifications.lt("refundInitiationTime", date);
    }

    @Override
    public String toExpression() {
        String str = " date:" + date;
        return str;
    }

    @Override
    public boolean isValid() {
        return date != null;
    }
}
