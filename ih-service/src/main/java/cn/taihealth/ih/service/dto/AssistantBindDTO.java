package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.service.vm.MedicalWorkerVM;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@ApiModel(description = "医助绑定医生参数模型")
@Data
public class AssistantBindDTO {

    @ApiModelProperty("需要绑定的医生id列表")
    @NotNull(message = "医生id列表不可为空")
    private List<MedicalWorkerVM> doctors;
}
