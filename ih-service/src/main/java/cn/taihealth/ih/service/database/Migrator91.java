package cn.taihealth.ih.service.database;

import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.repo.order.OrderExtraInfoRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import com.gitq.jedi.context.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class Migrator91 {

    public void run() {
        OrderExtraInfoRepository orderExtraInfoRepository = AppContext.getInstance(OrderExtraInfoRepository.class);
        OrderRepository orderRepository = AppContext.getInstance(OrderRepository.class);
        orderExtraInfoRepository.findAll().forEach(o -> {
            if (o.getHospital() == null) {
                Order order = orderRepository.findById(o.getOrderId()).orElse(null);
                if (order != null) {
                    o.setHospital(order.getHospital());
                    orderExtraInfoRepository.save(o);
                }
            }
        });

    }
}
