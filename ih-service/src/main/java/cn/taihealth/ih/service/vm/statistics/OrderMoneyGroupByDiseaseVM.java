package cn.taihealth.ih.service.vm.statistics;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * 根据订单类型展示金额
 * @Author: Moon
 * @Date: 2020/11/20 上午10:24
 */
public class OrderMoneyGroupByDiseaseVM {

    @ApiModelProperty("疾病")
    private String disease;
    @ApiModelProperty("总金额")
    private String totalMoney;

    @ApiModelProperty("服务类型分类结果集")
    private List<OrderMoneyGroupByTypeVM> orderMoneyGroupByTypeVMs;

    public OrderMoneyGroupByDiseaseVM(String disease, String totalMoney,
                                      List<OrderMoneyGroupByTypeVM> orderMoneyGroupByTypeVMs) {
        this.disease = disease;
        this.totalMoney = totalMoney;
        this.orderMoneyGroupByTypeVMs = orderMoneyGroupByTypeVMs;
    }

    public String getDisease() {
        return disease;
    }

    public void setDisease(String disease) {
        this.disease = disease;
    }

    public String getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(String totalMoney) {
        this.totalMoney = totalMoney;
    }

    public List<OrderMoneyGroupByTypeVM> getOrderMoneyGroupByTypeVMs() {
        return orderMoneyGroupByTypeVMs;
    }

    public void setOrderMoneyGroupByTypeVMs(
        List<OrderMoneyGroupByTypeVM> orderMoneyGroupByTypeVMs) {
        this.orderMoneyGroupByTypeVMs = orderMoneyGroupByTypeVMs;
    }
}

