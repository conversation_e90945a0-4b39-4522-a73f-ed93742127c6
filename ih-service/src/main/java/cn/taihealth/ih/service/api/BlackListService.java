package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.service.dto.BlackListDTO;
import org.springframework.data.domain.Page;


public interface BlackListService {

    /**
     * 查询黑名单
     * @param hospital
     * @param pageNo
     * @param size
     * @return
     */
    Page<BlackListDTO> getBlackList(Hospital hospital, Integer pageNo, Integer size, String query);
    /**
     * 解除黑名单
     */
    BlackListDTO updateOneBlackList(Hospital hospital, Long id, BlackListDTO dto);
}
