package cn.taihealth.ih.service.vm.nodered.nodereddatacontent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jzs
 * @Date: 2023-09-11
 * 住院患者预交金汇总信息
 */
@Data
public class InpatientAdvanceCharge implements Serializable {

    @ApiModelProperty("就诊流水号	Y	住院号")
    private String regno;
    @ApiModelProperty("预交金额累计充值	Y")
    private String advance_total_amount;
    @ApiModelProperty("预交金余额	Y")
    private String advance_account_amount;
    @ApiModelProperty("费用总金额	Y")
    private String total_amount;
    @ApiModelProperty("费用自付金额	N")
    private String self_amount;

}
