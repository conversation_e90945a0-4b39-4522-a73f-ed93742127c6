package cn.taihealth.ih.service.impl.filter.offlinehospital;

import cn.taihealth.ih.domain.cloud.OfflineHospital;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 */
public class GradeFilter implements HospitalFilter {
    private final OfflineHospital.Grade grade;

    public GradeFilter(OfflineHospital.Grade grade) {
        this.grade = grade;
    }

    @Override
    public boolean matches(OfflineHospital offlineHospital) {
        return offlineHospital.getGrade() == grade;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OfflineHospital> toSpecification() {
        return Specifications.eq("grade", grade);
    }

    @Override
    public String toExpression() {
        return "grade:" + grade;
    }

    @Override
    public boolean isValid() {
        return grade != null;
    }
}
