package cn.taihealth.ih.service.vm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@ApiModel(description = "微信回调参数")
@Data
public class WechatCallbackVM {

    @ApiModelProperty("openId")
    @NotBlank(message = "openId不可为空")
    private String openId;

    @ApiModelProperty("unionId")
    @NotBlank(message = "unionId不可为空")
    private String unionId;

    @ApiModelProperty("appId")
    @NotBlank(message = "appId不可为空")
    private String appId;

    @ApiModelProperty("医生id")
    private Long doctorId;
}
