package cn.taihealth.ih.service.vm;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * 每个时间段下的分配的号源信息
 * <AUTHOR> jzs
 * @Date : 2024-05-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamDeviceTimeSlotNumberSourceVM {

    @ApiModelProperty("开始时间 8点 08:00 如果是解绑的设备不用传")
    private String start;

    @ApiModelProperty("结束时间 8点 08:00 如果是解绑的设备不用传")
    private String end;

    @ApiModelProperty("渠道分配的号源信息 如果是绑定设备号源信息必传，如果是解绑的设备不用传")
    private List<NumberSourceVM> numberSourceList = Lists.newArrayList();

}
