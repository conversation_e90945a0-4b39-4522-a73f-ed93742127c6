package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.Inspection;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 检验表DTO
 * <AUTHOR>
 */
@Data
@ToString(exclude = {"order", "patient", "user"})
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class InspectionDTO extends UpdatableDTO {


    @ApiModelProperty("检查时间 ")
    private Date checkTime;

    @ApiModelProperty("项目名称 ")
    private String projectName;

    @ApiModelProperty("数量 ")
    private int quantity;

    @ApiModelProperty("检验地点 ")
    private String checkAddress;

    @ApiModelProperty("价格 ")
    private int price;

    @ApiModelProperty("急诊订单")
    private AbstractEntityDTO order = new AbstractEntityDTO();

    @ApiModelProperty("患者")
    private PatientDTO patient;

    @ApiModelProperty("医生")
    private MedicalWorkerDTO doctor;

    @ApiModelProperty("发起人")
    private UserDTO user;


    public InspectionDTO(Inspection inspection, boolean relation) {
        super(inspection);
        this.checkTime = inspection.getCheckTime();
        this.projectName = inspection.getProjectName();
        this.quantity = inspection.getQuantity();
        this.checkAddress = inspection.getCheckAddress();
        this.price = inspection.getPrice();
        this.order.setId(inspection.getOrder().getId());
        this.patient = new PatientDTO(inspection.getPatient(), null);
        this.doctor = new MedicalWorkerDTO(inspection.getDoctor());
        this.user = new UserDTO(inspection.getUser());
        if (relation) {
      /*      this.depts = medicalWorker.getDeptMedicalWorkers()
                .stream()
                .map(u -> new DeptDTO(u.getDept()))
                .collect(Collectors.toList());*/
        }
    }

    public InspectionDTO(Inspection inspection) {
        this(inspection, true);
    }

    public Inspection toEntityCase(InspectionDTO medicalCaseDTO) {
        Inspection mc = new Inspection();
        mc.setCheckTime(medicalCaseDTO.getCheckTime());
        mc.setProjectName(medicalCaseDTO.getProjectName());
        mc.setQuantity(medicalCaseDTO.getQuantity());
        mc.setCheckAddress(medicalCaseDTO.getCheckAddress());
        mc.setPrice(medicalCaseDTO.getPrice());
        return mc;
    }

}
