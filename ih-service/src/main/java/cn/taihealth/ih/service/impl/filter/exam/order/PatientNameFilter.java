package cn.taihealth.ih.service.impl.filter.exam.order;

import cn.taihealth.ih.domain.hospital.ExamOrder;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

public class PatientNameFilter implements SearchFilter<ExamOrder> {

    private final String pattern;

    public PatientNameFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<ExamOrder> toSpecification() {
        return Specifications.like("check.patient.name", pattern);
    }

    @Override
    public String toExpression() {
        return "patName:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof PatientNameFilter)) {
            return false;
        }

        PatientNameFilter rhs = (PatientNameFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
