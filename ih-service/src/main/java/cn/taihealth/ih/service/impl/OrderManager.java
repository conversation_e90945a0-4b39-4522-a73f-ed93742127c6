package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.RandomUtils;
import cn.taihealth.ih.commons.util.RedisUtil;
import cn.taihealth.ih.commons.util.Snowflake64.Holder;
import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.DoctorFee;
import cn.taihealth.ih.domain.MedicalCase;
import cn.taihealth.ih.domain.MedicalCaseDisease;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.his.HisChargeRecord;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.Dept.DeptType;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform.PlatformForEnum;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import cn.taihealth.ih.domain.hospital.OrderOperation.Step;
import cn.taihealth.ih.domain.hospital.OrderQueue.Type;
import cn.taihealth.ih.domain.hospital.Schedule.ScheduleType;
import cn.taihealth.ih.repo.MedicalCaseRepository;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionRepository;
import cn.taihealth.ih.repo.order.OrderOperationRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.repo.order.OrderWorkerRepository;
import cn.taihealth.ih.repo.schedule.ScheduleRepository;
import cn.taihealth.ih.repo.schedule.ScheduleUseRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.offline.HisChargeRecordService;
import cn.taihealth.ih.service.api.wechat.WechatService;
import cn.taihealth.ih.service.dto.OrderDTO;
import cn.taihealth.ih.service.dto.PatientDTO;
import cn.taihealth.ih.service.impl.event.*;
import cn.taihealth.ih.service.impl.im.tencent.MessageIm;
import cn.taihealth.ih.service.impl.im.tencent.MessageIm.Desc;
import cn.taihealth.ih.service.impl.nursing.NursingHomeOrderStep;
import cn.taihealth.ih.service.impl.order.BusinessOrderManager;
import cn.taihealth.ih.service.impl.order.OrderActionStep;
import cn.taihealth.ih.service.impl.order.OrderActionStep.Action;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.SuperviseUtil;
import cn.taihealth.ih.service.vm.EndOrderVM;
import cn.taihealth.ih.service.vm.RegisterVM;
import cn.taihealth.ih.service.vm.TriageOrderVM;
import cn.taihealth.ih.service.vm.nodered.NodeRedResponseData;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.ConfirmRegistResult;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.result.ReturnRegistResult;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.service.vm.offline.HisRefundParam;
import cn.taihealth.ih.service.vm.platform.PayPlatform;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.service.SuperviseFactory;
import cn.taihealth.ih.supervise.service.SuperviseService;
import cn.taihealth.ih.wechat.service.vm.RefundOrderVM;
import cn.taihealth.ih.wechat.service.vm.WechatTemplatedData;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage.MiniProgram;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OrderManager {

    private static final Logger log = LoggerFactory.getLogger(OrderManager.class);

    private final OrderRepository orderRepository;
    private final OrderOperationRepository orderOperationRepository;
    private final OrderWorkerRepository orderWorkerRepository;
    private final ScheduleRepository scheduleRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final DeptRepository deptRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final TencentIMService tencentIMService;
    private final ScheduleUseRepository scheduleUseRepository;
    private final PatientRepository patientRepository;
    private final QueueService queueService;
    private final UserElecInvoiceService userElecInvoiceService;
    private final DoctorFeeService doctorFeeService;
    private final RedisUtil redisUtil;
    private final PrescriptionRepository prescriptionRepository;
    private final BusinessOrderManager businessOrderManager;
    private final MedicalCaseRepository medicalCaseRepository;

    public OrderManager(OrderRepository orderRepository,
                        OrderOperationRepository orderOperationRepository,
                        ScheduleRepository scheduleRepository,
                        OrderWorkerRepository orderWorkerRepository,
                        ApplicationEventPublisher eventPublisher, DeptRepository deptRepository,
                        MedicalWorkerRepository medicalWorkerRepository,
                        ScheduleUseRepository scheduleUseRepository,
                        TencentIMService tencentIMService,
                        QueueService queueService, PatientRepository patientRepository,
                        UserElecInvoiceService userElecInvoiceService,
                        DoctorFeeService doctorFeeService, RedisUtil redisUtil,
                        PrescriptionRepository prescriptionRepository,
                        BusinessOrderManager businessOrderManager,
                        MedicalCaseRepository medicalCaseRepository) {
        this.orderWorkerRepository = orderWorkerRepository;
        this.orderRepository = orderRepository;
        this.orderOperationRepository = orderOperationRepository;
        this.scheduleRepository = scheduleRepository;
        this.eventPublisher = eventPublisher;
        this.deptRepository = deptRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.tencentIMService = tencentIMService;
        this.scheduleUseRepository = scheduleUseRepository;
        this.patientRepository = patientRepository;
        this.queueService = queueService;
        this.userElecInvoiceService = userElecInvoiceService;
        this.doctorFeeService = doctorFeeService;
        this.redisUtil = redisUtil;
        this.prescriptionRepository = prescriptionRepository;
        this.medicalCaseRepository = medicalCaseRepository;
        this.businessOrderManager = businessOrderManager;
    }

    /**
     * 开始分诊，锁定一个护士
     *
     * @param current
     * @param order
     * @return
     */
    public Order startTriageOrder(User current, Order order) {
        canTriage(current, order);
        MedicalWorker nurse = medicalWorkerRepository
            .findOneByHospitalAndUser(order.getHospital(), current).orElse(null);
        order.setNurse(nurse);
        operationOrder(current, nurse, order, Step.START_TRIAGE, "护士开始分诊");
        userAddToOrder(current, order);
        return order;
    }

    public void userAddToOrder(User user, Order order) {
        Optional<OrderWorker> workerOptional = orderWorkerRepository
            .findOneByUserAndOrder(user, order);
        OrderWorker worker;
        if (workerOptional.isPresent()) {
            worker = workerOptional.get();
            worker.setStatus(OrderWorker.Status.JOINED);
        } else {
            worker = new OrderWorker(user, order);
            worker.setJoinedDate(new Date());
            worker.setStatus(OrderWorker.Status.JOINED);
        }
        orderWorkerRepository.save(worker);
    }

    private void userQuitToOrder(User user, Order order) {
        Optional<OrderWorker> workerOptional = orderWorkerRepository
            .findOneByUserAndOrder(user, order);
        OrderWorker worker;
        if (workerOptional.isPresent()) {
            worker = workerOptional.get();
            worker.setStatus(OrderWorker.Status.QUITED);
            orderWorkerRepository.save(worker);
        }
    }

    private void canTriage(User current, Order order) {
        List<OrderWorker> workers = order.getWorkers();
        // order没人参与分诊或当前护士正在分诊的，可以进行分诊
        if (order.getOrderType() != ClinicType.EMERGENCY
            || !(order.getStatus() == Order.OrderStatus.SUBMITTED
            || order.getStatus() == Order.OrderStatus.TRIAGING && workers.stream().anyMatch(
            u -> Objects.equals(u.getUser(), current)
                && u.getStatus() == OrderWorker.Status.JOINED))) {
            throw ErrorType.TRIAGE_ERROR.toProblem();
        }
    }

    /**
     * 记录操作记录(订单前后状态不变)
     *
     * @param operator 操作人
     * @param order    订单
     * @param step     操作步骤
     * @param remark   备注
     */
    public void createOperation(User operator, Order order, OrderOperation.Step step,
                                String remark) {
        OrderOperation operation = new OrderOperation(operator, order, step, remark);
        operation.setPreviousStatus(order.getStatus());
        operation.setNextStatus(order.getStatus());
        orderOperationRepository.save(operation);
    }

    /**
     * 记录操作记录(订单前后状态改变)
     *
     * @param operator 操作人
     * @param medicalWorker 操作人
     * @param order    订单
     * @param step     操作步骤
     * @param remark   备注
     */
    public void createOperation(User operator, MedicalWorker medicalWorker, Order order, OrderOperation.Step step,
                                String remark, Order.OrderStatus previousStatus, Order.OrderStatus nextStatus) {
        OrderOperation operation = new OrderOperation(operator, order, step, remark);
        if (medicalWorker != null) {
            operation.setMedicalWorker(medicalWorker);
        }
        operation.setPreviousStatus(previousStatus);
        operation.setNextStatus(nextStatus);
        orderOperationRepository.save(operation);
    }

    /**
     * 修改订单状态, 这里只是用于修改订单状态，不要加业务逻辑处理
     *
     * @param operator
     * @param order
     * @param step
     * @param remark
     * @return 订单
     */
    public Order operationOrder(User operator, MedicalWorker medicalWorker, Order order, OrderOperation.Step step,
                                String remark) {
        Action action;
        if (step == Step.REFUND) {
            action = OrderActionStep.ACTION_STEPS.get(step).get(OrderStatus.REFUNDING);
        } else {
            log.info("operationOrder id: {} status: {} step: {}", order.getId(), order.getStatus(), step.name());
            action = OrderActionStep.ACTION_STEPS.get(step).get(order.getStatus());
        }
        if (action == null) {
            throw ErrorType.GRAB_ORDER_TIP.toProblem("订单状态不正确");
        }
        // 2023年09月30日17:29:30  debug退款回调收不到的bug
        if (action.getPreStepStatus() != null) {
            // 查询orderOperation表中最新的一条记录
            Optional<OrderOperation> optional = orderOperationRepository
                    .findFirstByOrderIdOrderByCreatedDateDesc(order.getId());
            optional.ifPresent(
                    orderOperation -> {
                        OrderStatus after = action.getPreStepStatus().get(orderOperation.getStep());
                        if (after != null) {
                            action.setAfterStatus(after);
                        }
                    }
            );
        }

        Order.OrderStatus nowStatus = order.getStatus();
        Order.OrderStatus nextStatus = action.getAfterStatus();
        if (nowStatus != action.getAfterStatus()) {
            if (step != Step.REFUND) {
                order.setPreviousStatus(order.getStatus());
            }
            if (step == Step.PAY) {
                if (order.getService() != null && order.getService().getMedicalWorker() != null) {
                    nextStatus = OrderStatus.ONTIME_CONFIRMED;
                }
                if (order.getService() == null && HospitalSettingsHelper.getBoolean(order.getHospital(),
                                                                                     HospitalSettingKey.HIS_SYNC_SCHEDULE)) {
                    nextStatus = OrderStatus.ONTIME_CONFIRMED;
                }
            }
            order.setStatus(nextStatus);
            order.setOperator(operator);
        }
        // 更新预约数量,咨询数量
        if (order.getDoctor() != null && (step == Step.FINISH || step == Step.PAY
            || (step == Step.REGISTER && order.getPaymentMethod() == PaymentMethod.OFFLINE)
            || (step == Step.TIME_OUT && nowStatus == OrderStatus.STARTED))) {
            AppContext.getInstance(UserService.class).addVisitCount(order);
        }
        // 更新医生的预约数量
        if (order.getDoctor() != null && step == Step.REFUND) {
            AppContext.getInstance(UserService.class).removeVisitCount(order);
        }
        if (nowStatus.equals(OrderStatus.ONTIME_CONFIRMED)) {
            // 成功接诊后，删除redis中的候诊倒计时
            redisUtil.del("order_longTime:" + order.getId());
        }
        orderRepository.save(order);
        // 这里直接刷新状态，防止IM收到消息时，逻辑没走完，导致前端查询状态还是原状态
        orderRepository.flush();
        if (nowStatus != action.getAfterStatus()) {
            createOperation(operator, medicalWorker, order, step, remark, nowStatus, nextStatus);
        }
        return order;
    }

    /**
     * 修改订单状态, 这里只是用于修改订单状态，不要加业务逻辑处理
     * @param operator
     * @param medicalWorker
     * @param order
     * @param step
     * @param remark
     */
    public Order operationNursingOrder(User operator, MedicalWorker medicalWorker, Order order,
                                       OrderOperation.Step step, String remark) {
        Order.OrderStatus nextStatus = NursingHomeOrderStep.ACTION_STEPS.get(step).get(order.getStatus());
        log.info("operationNursingOrder id: {} status: {} step: {}", order.getId(), order.getStatus(), step.name());
        if (nextStatus == null) {
            throw ErrorType.GRAB_ORDER_TIP.toProblem("订单状态不正确");
        }

        Order.OrderStatus nowStatus = order.getStatus();
        if (nowStatus != nextStatus) {
            order.setPreviousStatus(nowStatus);
            order.setStatus(nextStatus);
            order.setOperator(operator);
        }
        orderRepository.save(order);
        orderRepository.flush();
        if (nowStatus != nextStatus) {
            createOperation(operator, medicalWorker, order, step, remark, nowStatus, nextStatus);
        }
        return order;
    }


    /**
     * 新业务不要使用这个方法
     *
     * @param operator
     * @param order
     * @param step
     * @param remark
     * @return 订单
     */
    @Deprecated
    public Order operationOrderSendMessage(User operator, MedicalWorker medicalWorker, Order order, OrderOperation.Step step,
                                           String remark) {
        Order order1 = operationOrder(operator, medicalWorker, order, step, remark);
        sendCustomMessage(order1, step);
        return order;
    }

    /**
     * 统一发送消息，已废弃，发送消息以后写在各个业务部分
     * @param order
     * @param step
     */
    @Deprecated
    private void sendCustomMessage(Order order, Step step) {
        // 处理自定义消息
        log.info("===处理自定义消息时，此时的订单状态： {}===", order.getStatus());
        Hospital hospital = order.getHospital();
        String content;
        Desc desc;
        MessageIm messageIm;
        WechatService wechatService = AppContext.getInstance(WechatService.class);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        WechatTemplatedData data;
        WechatTemplatedData doctorData;
        float fee;
        List<UserPlatformInfo> list;
        List<UserPlatformInfo> doctorList;
        String pagePath;
        MiniProgram miniProgram;
        switch (order.getStatus()) {
            case REFUNDING:
                switch (step) {
                    case TIME_OUT_ONTIME_CONFIRMED_REFUNDED:
                        HospitalSettingKey notifyOrderRefundToTimeOutOnTimeConfirmed = HospitalSettingKey.NOTIFY_ORDER_REFUND_TO_TIME_OUT_ONTIME_CONFIRMED;
                        // 咨询订单候诊中医生未接诊退款，发送短信给患者
                        sendMessageToUser(order.getPatient().getUser(), order, notifyOrderRefundToTimeOutOnTimeConfirmed, null);
                        content = "十分抱歉，医生因为工作安排未能接诊，本次咨询已退款，费用将在1-3个工作日退还，请选择其他医生咨询";
                        desc = Desc.valueOf(step.name());
                        messageIm = new MessageIm(desc, content, order.getId().toString());
                        tencentIMService.sendGroupMessage(hospital, order.getImGroupId(), messageIm);
                        if (order.getDoctor() != null) {
                            tencentIMService.sendMessage(hospital, order.getDoctor().getUser(), messageIm);
                        }
                        if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                            //发消息给患者
                            list = wechatService.screenOfficialAccount(order.getHospital(),
                                                                       order.getPatient().getUser(),
                                                                       PlatformForEnum.PATIENT);
                            pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                            miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                                       PlatformForEnum.PATIENT,
                                                                       pagePath);
                            data = new WechatTemplatedData();
                            data.setFirstStr("");
                            data.addKeywordsStr("退款中，" + "十分抱歉，医生" + order.getDoctor().getUser().getFullName() + "因故未接诊，本次"
                                                    + order.getVisitType().getName()
                                                    + "咨询已退款，费用将在1-3个工作日退还，请选择其他医生咨询");
                            data.addKeywordsStr("微信支付");
                            data.addKeywordsStr(order.getId().toString());
                            data.addKeywordsStr(format.format(order.getPayOrderCreatedDate()));
                            fee = (float) (order.getRegistrationFee() / 100.00);
                            data.addKeywordsStr(fee + "元");
                            data.setMiniProgram(miniProgram);
                            data.setRemarkStr("点击查看详情");
                            wechatService.sendWechatNotify(list, data, notifyOrderRefundToTimeOutOnTimeConfirmed, order.getHospital());
                        }
                        //发消息给医生
                        doctorList = wechatService.screenOfficialAccount(order.getHospital(),
                                                                         order.getDoctor().getUser(),
                                                                         PlatformForEnum.DOCTOR);
                        pagePath = "subpackages/doc/order-detail/index?orderId=" + order.getId();
                        miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                                   PlatformForEnum.DOCTOR,
                                                                   pagePath);
                        doctorData = new WechatTemplatedData();
                        DecimalFormat df = new DecimalFormat("0.0");
                        //指定四舍五入规则HALF_UP
                        df.setRoundingMode(RoundingMode.HALF_UP);
                        float hours = (float) HospitalSettingsHelper.getInt(order.getHospital(),
                                                                            HospitalSettingKey.WAITING_ROOM_LIMIT_TIME);
                        doctorData.setFirstStr("");
                        doctorData.addKeywordsStr(
                            "十分抱歉，由于您未在" + df.format(hours / 60) + "小时内接诊，患者" + order.getPatient().getName()
                                + "的订单因超时未接诊自动退款");
                        doctorData.addKeywordsStr("微信支付");
                        doctorData.addKeywordsStr(order.getId().toString());
                        doctorData.addKeywordsStr(format.format(order.getPayOrderCreatedDate()));
                        fee = (float) (order.getRegistrationFee() / 100.00);
                        doctorData.addKeywordsStr(fee + "元");
                        doctorData.setMiniProgram(miniProgram);
                        doctorData.setRemarkStr("点击查看详情");
                        wechatService.sendWechatNotify(doctorList, doctorData, HospitalSettingKey.NOTIFY_ORDER_REFUND_DOCTOR,
                                                       order.getHospital());
                        break;
                    case ONTIME_CONFIRMED_REFUNDED:
                        content = "患者已取消咨询，本次咨询已退款，费用将在1-3个工作日退还，请选择其他医生咨询";
                        desc = Desc.valueOf(step.name());
                        messageIm = new MessageIm(desc, content, order.getId().toString());
                        tencentIMService.sendGroupMessage(hospital, order.getImGroupId(), messageIm);
                        if (order.getDoctor() != null) {
                            tencentIMService.sendMessage(hospital, order.getDoctor().getUser(), messageIm);
                        }
                        if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                            list = wechatService.screenOfficialAccount(order.getHospital(),
                                                                       order.getPatient().getUser(),
                                                                       PlatformForEnum.PATIENT);
                            pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                            miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                                       PlatformForEnum.PATIENT,
                                                                       pagePath);
                            data = new WechatTemplatedData();
                            data.setFirstStr("");
                            data.addKeywordsStr("患者" + order.getPatient().getName());
                            data.addKeywordsStr(format.format(new Date()));
                            data.addKeywordsStr(order.getId().toString());
                            data.addKeywordsStr("您已取消候诊中的" + order.getVisitType().getName()
                                                    + "咨询订单！退款会在1-3个工作日内返还至您的支付账户，请注意查收");
                            data.setMiniProgram(miniProgram);
                            data.setRemarkStr("点击查看详情");
                            wechatService.sendWechatNotify(list, data, HospitalSettingKey.NOTIFY_ORDER_CANCELED_PAY_TO_PATIENT,
                                                           order.getHospital());
                        }

                        //发消息给医生
                        doctorList = wechatService.screenOfficialAccount(order.getHospital(),
                                                                         order.getDoctor().getUser(),
                                                                         PlatformForEnum.DOCTOR);
                        pagePath = "subpackages/doc/order-detail/index?orderId=" + order.getId();
                        miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                                   PlatformForEnum.DOCTOR,
                                                                   pagePath);
                        doctorData = new WechatTemplatedData();
                        doctorData.setFirstStr("");
                        doctorData.addKeywordsStr(
                            "患者" + order.getPatient().getName() + "已取消候诊中的" + order.getVisitType().getName()
                                + "咨询订单！");
                        doctorData.addKeywordsStr(format.format(new Date()));
                        doctorData.addKeywordsStr(order.getId().toString());
                        doctorData.addKeywordsStr("患者主动取消候诊中订单");
                        doctorData.setMiniProgram(miniProgram);
                        doctorData.setRemarkStr("点击查看详情");
                        wechatService.sendWechatNotify(doctorList, doctorData, HospitalSettingKey.NOTIFY_ORDER_CANCELED_DOCTOR,
                                                       order.getHospital());
                        break;
                    case ONTIME_CONFIRMED_DOCTOR_REFUNDED:
                        HospitalSettingKey notifyOrderCanceledToDoctor = HospitalSettingKey.NOTIFY_ORDER_CANCELED_TO_DOCTOR;
                        // 咨询订单候诊中医生退诊,发送短信给患者
                        sendMessageToUser(order.getPatient().getUser(), order, notifyOrderCanceledToDoctor, null);
                        content = "十分抱歉，医生因为工作安排无法接诊，本次咨询已退款，费用将在1-3个工作日退还，请选择其他医生咨询";
                        desc = Desc.valueOf(step.name());
                        messageIm = new MessageIm(desc, content, order.getId().toString());
                        tencentIMService.sendGroupMessage(hospital, order.getImGroupId(), messageIm);
                        if (order.getDoctor() != null) {
                            tencentIMService.sendMessage(hospital, order.getDoctor().getUser(), messageIm);
                        }
                        if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                            list = wechatService.screenOfficialAccount(order.getHospital(),
                                                                       order.getPatient().getUser(),
                                                                       PlatformForEnum.PATIENT);
                            pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                            miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                                       PlatformForEnum.PATIENT,
                                                                       pagePath);
                            data = new WechatTemplatedData();
                            data.setFirstStr("");
                            data.addKeywordsStr("医生" + order.getDoctor().getUser().getFullName());
                            data.addKeywordsStr(format.format(new Date()));
                            data.addKeywordsStr(order.getId().toString());
                            data.addKeywordsStr("十分抱歉，医生" + order.getDoctor().getUser().getFullName() + "因故无法接诊，本次"
                                                    + order.getVisitType().getName()
                                                    + "咨询已退款，费用将在1-3个工作日退还，请选择其他医生咨询");
                            data.setMiniProgram(miniProgram);
                            data.setRemarkStr("点击查看详情");
                            wechatService.sendWechatNotify(list, data, notifyOrderCanceledToDoctor, order.getHospital());
                        }
                        break;
                    case STARTED_REFUNDED:
                        HospitalSettingKey notifyOrderRefundToStartedRefunded = HospitalSettingKey.NOTIFY_ORDER_REFUND_TO_STARTED_REFUNDED;
                        // 咨询订单进行中医生退款,发送短信给患者
                        sendMessageToUser(order.getPatient().getUser(), order, notifyOrderRefundToStartedRefunded, null);
                        content = "十分抱歉，医生因为工作安排临时结束咨询，本次咨询已退款，费用将在1-3个工作日退还，请选择其他医生咨询";
                        desc = Desc.valueOf(step.name());
                        messageIm = new MessageIm(desc, content, order.getId().toString());
                        tencentIMService.sendGroupMessage(hospital, order.getImGroupId(), messageIm);
                        if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                            list = wechatService.screenOfficialAccount(order.getHospital(),
                                                                       order.getPatient().getUser(),
                                                                       PlatformForEnum.PATIENT);
                            pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                            miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                                       PlatformForEnum.PATIENT,
                                                                       pagePath);
                            data = new WechatTemplatedData();
                            data.setFirstStr("");
                            data.addKeywordsStr("退款中，" + "十分抱歉，医生" + order.getDoctor().getUser().getFullName() + "因故结束本次"
                                                    + order.getVisitType().getName()
                                                    + "咨询并退款，费用将在1-3个工作日退还，请选择其他医生咨询");
                            data.addKeywordsStr("微信支付");
                            data.addKeywordsStr(order.getId().toString());
                            data.addKeywordsStr(format.format(order.getPayOrderCreatedDate()));
                            fee = (float) (order.getRegistrationFee() / 100.00);
                            data.addKeywordsStr(fee + "元");
                            data.setMiniProgram(miniProgram);
                            data.setRemarkStr("点击查看详情");
                            wechatService.sendWechatNotify(list, data, notifyOrderRefundToStartedRefunded, order.getHospital());
                        }
                        break;
                    case COMPLETED_REFUNDED:
                        HospitalSettingKey notifyOrderRefundToCompletedRefunded = HospitalSettingKey.NOTIFY_ORDER_REFUND_TO_COMPLETED_REFUNDED;
                        // 咨询订单已完成医生退款,发送短信给患者
                        sendMessageToUser(order.getPatient().getUser(), order, notifyOrderRefundToCompletedRefunded, null);
                        if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                            list = wechatService.screenOfficialAccount(order.getHospital(),
                                                                       order.getPatient().getUser(),
                                                                       PlatformForEnum.PATIENT);
                            pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                            miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                                       PlatformForEnum.PATIENT,
                                                                       pagePath);
                            data = new WechatTemplatedData();
                            data.setFirstStr("");
                            data.addKeywordsStr(
                                "退款中，" + "医生" + order.getDoctor().getUser().getFullName() + "退还本次" + order.getVisitType()
                                    .getName()
                                    + "咨询费用，费用将在1-3个工作日退还");
                            data.addKeywordsStr("微信支付");
                            data.addKeywordsStr(order.getId().toString());
                            data.addKeywordsStr(format.format(order.getPayOrderCreatedDate()));
                            fee = (float) (order.getRegistrationFee() / 100.00);
                            data.addKeywordsStr(fee + "元");
                            data.setMiniProgram(miniProgram);
                            data.setRemarkStr("点击查看详情");
                            wechatService.sendWechatNotify(list, data, notifyOrderRefundToCompletedRefunded, order.getHospital());
                        }
                        break;
                    case REFUND:
                        content = "患者已取消本次挂号本次就诊已退款，费用将在1-3个工作日退还，请选择其他医生就诊";
                        desc = Desc.valueOf("REFUNDED");
                        messageIm = new MessageIm(desc, content, order.getId().toString());
                        tencentIMService.sendMessage(hospital, order.getUser(), messageIm);
                    default:
                        break;
                }
                break;
            case TIME_OUT_COMPLETED:
                content = "当前咨询已经超过48小时，还请休息一下，当前咨询自动结束，祝早日康复";
                desc = Desc.valueOf(order.getStatus().name());
                messageIm = new MessageIm(desc, content, order.getId().toString());
                tencentIMService.sendGroupMessage(hospital, order.getImGroupId(), messageIm);
                redisUtil.set("order_completed:" + order.getId(), 1, 10 * 60);
                break;
            case COMPLETED:
                // 咨询、问诊结束后10分钟后推送
                redisUtil.set("order_completed:" + order.getId(), 1, 10 * 60);
                break;
            case TIME_OUT_PAY_CLOSED:
                HospitalSettingKey notifyOrderClosed = HospitalSettingKey.NOTIFY_ORDER_CLOSED;
                // 咨询订单超时未支付时主动关闭,发送短信给患者
                sendMessageToUser(order.getPatient().getUser(), order, notifyOrderClosed, null);
                if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                    list = wechatService.screenOfficialAccount(order.getHospital(),
                                                               order.getPatient().getUser(),
                                                               PlatformForEnum.PATIENT);
                    pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                    miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                               PlatformForEnum.PATIENT,
                                                               pagePath);
                    data = new WechatTemplatedData();
                    data.setFirstStr("");
                    data.addKeywordsStr(order.getId().toString());
                    data.addKeywordsStr(format.format(order.getCreatedDate()));
                    data.addKeywordsStr("您有未支付的订单已关闭，原因：订单超时未支付");
                    data.addKeywordsStr(format.format(order.getEndedDate()));
                    data.setMiniProgram(miniProgram);
                    data.setRemarkStr("点击查看详情");
                    wechatService.sendWechatNotify(list, data,notifyOrderClosed, order.getHospital());

                }
                break;
            case PENDING_CANCELLED:
                // 咨询订单未支付患者主动取消,发送短信给患者
                HospitalSettingKey notifyOrderCanceled = HospitalSettingKey.NOTIFY_ORDER_CANCELED;
                sendMessageToUser(order.getPatient().getUser(), order, notifyOrderCanceled, null);
                if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                    list = wechatService.screenOfficialAccount(order.getHospital(),
                                                               order.getPatient().getUser(),
                                                               PlatformForEnum.PATIENT);
                    pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                    miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                               PlatformForEnum.PATIENT,
                                                               pagePath);
                    data = new WechatTemplatedData();
                    data.setFirstStr("");
                    data.addKeywordsStr("患者" + order.getPatient().getName());
                    data.addKeywordsStr(format.format(new Date()));
                    data.addKeywordsStr(order.getId().toString());
                    data.addKeywordsStr("您主动取消未支付" + order.getVisitType().getName() + "订单");
                    data.setMiniProgram(miniProgram);
                    data.setRemarkStr("点击查看详情");
                    wechatService.sendWechatNotify(list, data, HospitalSettingKey.NOTIFY_ORDER_CANCELED_UNPAID_TO_PATIENT,
                                                   order.getHospital());
                }
                break;
            case REGISTERED:
                if (order.getDoctor() != null) {
                    if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                        list = wechatService.screenOfficialAccount(order.getHospital(), order.getPatient().getUser(),
                                                                   PlatformForEnum.PATIENT);
                        pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                        miniProgram = wechatService.getMiniProgram(order.getHospital(),
                                                                   PlatformForEnum.PATIENT,
                                                                   pagePath);
                        data = new WechatTemplatedData();
                        data.addKeywordsStr(order.getId().toString());
                        if (order.getVisitType().name().equals(VisitType.GRAPHIC.name())) {
                            data.setFirstStr("");
                            data.addKeywordsStr("您已成功预约" + order.getDoctor().getUser().getFullName()
                                                    + "医生的图文咨询，请您尽快完成助手的候诊问题，并注意医生给您的咨询");
                        } else {
                            data.setFirstStr(
                                "您已成功预约" + order.getDoctor().getUser().getFullName()
                                    + "医生的视频咨询，请您尽快完成助手的候诊问题，并在预约时段时刻注意医生的来电消息");
                            data.addKeywordsStr("视频咨询（医生" + order.getDoctor().getUser().getFullName() + "）");
                        }
                        fee = (float) (order.getRegistrationFee() / 100.00);
                        data.addKeywordsStr(fee + "元");
                        data.setMiniProgram(miniProgram);
                        data.setRemarkStr("点击查看详情");
                        HospitalSettingKey notifyOrderConfirmed = HospitalSettingKey.NOTIFY_ORDER_CONFIRMED;
                        wechatService.sendWechatNotify(list, data, notifyOrderConfirmed, order.getHospital());
                    }
                }
                break;
            case ONTIME_CONFIRMED:
                int anInt = HospitalSettingsHelper.getInt(order.getHospital(),
                                                          HospitalSettingKey.WAITING_ROOM_NOTICE_TIME);
                redisUtil.set("order_longTime:" + order.getId(), 1, anInt * 60);
                break;
            case STARTED:
                int anInt1 = HospitalSettingsHelper.getInt(order.getHospital(),
                                                           HospitalSettingKey.CONSULT_TOTAL_LIMIT_TIME);
                redisUtil.set("order_start:" + order.getId(), 1, anInt1 * 60L - 3);
                log.info("订单就诊开始：order: {}  data: {}, startDate: {}", order.getId(), new Date(),
                         order.getAdmissionDate());
                break;
            default:
        }
    }

    /**
     * 查询今天当前时间之后的排班情况
     *
     * @param hospital 医院
     * @param dept     科室
     * @return 排班
     */
    public List<Schedule> findSchedules(Hospital hospital, Dept dept) {
        Date now = new Date();
        Date date = new Date(DateUtils.truncate(now, Calendar.DAY_OF_MONTH).getTime() + Constants.ONE_DAY_MILLIONS);
        List<Specification<Schedule>> ls = Lists
            .newArrayList(Specifications.eq("hospital", hospital), Specifications.eq("dept", dept),
                          Specifications.lt("startTime", date), Specifications.ge("endTime", now));
        Specification<Schedule> s = Specifications.and(ls);
        return scheduleRepository.findAll(s);
    }

    /**
     * 1.如果分诊护士指定排班，使用指定的排班 (已取消这个逻辑) 2.如果急诊科医生存在排班，使用排到医生的排班 3.如果急诊科科室存在排班，使用科室排班 如果找不到合适的排班，抛出急诊科未排班
     *
     * @param current 当前用户
     * @param order   订单
     * @param vm
     * @return
     */
    public Order triageOrder(User current, Order order, TriageOrderVM vm) {
        canTriage(current, order);
        // 1.如果分诊护士指定排班，使用指定的排班 (已取消这个逻辑)
        //   2.如果急诊科医生存在排班，使用排到医生的排班
        //    3. 如果急诊科科室存在排班，使用科室排班
        // 如果找不到合适的排班，抛出急诊科未排班
        Schedule schedule = null;
        Dept dept = deptRepository.findAllByHospitalAndDeptType(order.getHospital(), DeptType.EMERGENCY)
            .stream().findFirst()
            .orElseThrow(() -> ErrorType.BAD_REQUEST_ERROR.toProblem("急诊科未排班", "急诊科未排班"));
        List<Schedule> schedules = findSchedules(order.getHospital(), order.getDept())
            .stream().filter(s -> s.getMedicalWorker() == null).collect(Collectors.toList());
        if (schedules.isEmpty() || schedules.get(0) == null) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem(dept.getDeptName() + "未排班");
        }
        order.setSupplementDescription(vm.getSupplementDescription());
        order.setSupplementDisease(
            StringUtil.joinWithDoubleSeparator(vm.getSupplementDiseases(), "|"));
        order.setExamination(vm.getExamination());
        order.setMedicine(vm.getMedicine());
        order.setTriagedDate(new Date());
        order.setDept(dept);
        order.setService(schedule);
        MedicalWorker nurse = medicalWorkerRepository.findOneByHospitalAndUser(order.getHospital(), current).orElse(null);
        operationOrder(current, nurse, order, Step.TRIAGE, "护士分诊");
        userAddToOrder(current, order);
        userQuitToOrder(current, order);
        return order;
    }

    @Transactional
    public Order endOrder(User user, MedicalWorker medicalWorker, Order order, EndOrderVM vm) {
        Hospital hospital = order.getHospital();
        Schedule schedule = order.getService();
        Optional<ScheduleUse> sUse = scheduleUseRepository.findOneBySchedule(schedule);
        // 退号逻辑
        if (vm.getEndType().canReturnNumber() && order.getOrderType() != ClinicType.UNKNOWN
            && order.getService() != null && sUse.isPresent()) {
            // 急诊退号不需要修改数量
            if (order.getOrderType() != ClinicType.EMERGENCY) {
                if (order.getPaymentMethod() == PaymentMethod.OFFLINE) {
                    throw ErrorType.REGISTER_CAN_NOT_CANCEL.toProblem("线下支付的订单不能取消");
                }
                // 取消或超时可以退号
                if (order.getStatus() == OrderStatus.PENDING) {
                    ScheduleUse use = sUse.get();
                    if (order.getOnlineType() == OnlineType.ONLINE) {
                        use.setOnlineUsedCount(use.getOnlineUsedCount() - 1);
                        scheduleUseRepository.save(use);
                    } else {
                        use.setInternetUsedCount(use.getInternetUsedCount() - 1);
                        scheduleUseRepository.save(use);
                    }
                }
            }
        }

        order = orderRepository.getById(order.getId());
        order.setEndedDate(new Date());
        order.setEndedReason(vm.getReason());
        order.setDiagnosisCa(vm.getDiagnosisCa());

        RefundOrderVM refundVm = new RefundOrderVM();
        refundVm.setId(order.getId());
        refundVm.setType(ThirdOrderType.REGISTER);

        String returnNo = null;
        if (vm.getEndType().canReturnNumber() && order.getPaymentStatus() == PaymentStatus.PAID && vm.getSource() != OrderRefundSource.HIS) {
            PayPlatform payPlatform = businessOrderManager.getPaySuccessTransactionId(order);
            log.info("his退款, transactionId= " + StandardObjectMapper.stringify(payPlatform));
            // his这里撤销结算
            boolean aBoolean = HospitalSettingsHelper.getBoolean(order.getHospital(),
                    HospitalSettingKey.HIS_SYNC_SCHEDULE);
            if (payPlatform != null) {
                ReturnRegistResult returnRegistResult = BusinessServiceStrategy.getInstance().getStrategy(aBoolean)
                    .returnRegist(order.getHospital(), order, payPlatform);
                returnNo = returnRegistResult.getRefund_settle_id();
            } else {
                if (aBoolean) {
                    log.error("没有找到已支付账单，his退款失败");
                    throw ErrorType.BAD_REQUEST_ERROR.toProblem("没有找到已支付账单，his退款失败");
                }
            }
        }

        if (StringUtils.isBlank(returnNo)) {
            returnNo = order.getId() + "";
        }
        // 退款
        if (vm.getEndType().canReturnNumber() && order.getPaymentStatus() == PaymentStatus.PAID) {
            if (vm.getEndType() == EndOrderVM.EndType.HOSPITAL_STOP && order.getStatus() == OrderStatus.COMPLETED
                    || vm.getEndType() == EndOrderVM.EndType.TIME_OUT_COMPLETED) {
                // 停诊操作，如果是正常结束的，不继续退款操作
                return order;
            }
            // 医保业务未实现
            boolean refunded = AppContext.getInstance(PayManager.class).refund(hospital,
                    false, order.getPaymentMethod(), refundVm, order.getRegistrationFee(), returnNo);
            if (refunded) {
                log.info("退款：已发起退款请求 orderId: {}", order.getId());
                // 订单状态改为退款中
                operationOrderSendMessage(user, medicalWorker, order, vm.getEndType().getOrderStep(), vm.getReason());
            } else {
                log.info("退款：没有支付订单，直接修改订单状态为REFUND orderId: {}", order.getId());
                // 提前将状态改为退款中，再经过
                order.setStatus(OrderStatus.REFUNDING);
                operationOrderSendMessage(user, medicalWorker, order, vm.getEndType().getOrderStep(), null);
            }
        } else {
            switch (order.getPaymentMethod()) {
                case WECHAT:
                    // 不能向微信发起退款
                    log.info("退款：不能向微信发起退款 orderId: {}", order.getId());
                    // 微信方面订单取消，使用户无法完成支付
                    AppContext.getInstance(WechatService.class).closeWechatOrder(hospital, refundVm.getType(), order.getId());
                    operationOrderSendMessage(user, medicalWorker, order, vm.getEndType().getOrderStep(), vm.getReason());
                    break;
                case ALI_PAY:
                    // 支付宝支付的订单，不支持退款
                    log.info("退款：不能向支付宝发起退款 orderId: {}", order.getId());
                    // 支付宝方面订单取消，使用户无法完成支付
                    operationOrderSendMessage(user, medicalWorker, order, vm.getEndType().getOrderStep(), vm.getReason());
                    break;
//                case OFFLINE:
//                    // 线下支付的订单，不支持退款
//                    log.info("退款：线下支付的订单，不支持退款 orderId: {}", order.getId());
//                    operationOrder(user, order, vm.getEndType().getOrderStep(), vm.getReason());
//                    break;
                default:
                    break;
            }
        }
        if (order.getStatus() == OrderStatus.PENDING &&
                (vm.getEndType().getOrderStep() == Step.CANCEL || vm.getEndType().getOrderStep() == Step.TIME_OUT)) {
            // 预算撤销
            pendingCancelOrder(order);
        }

        // 订单结束逻辑放在operationOrder发消息之后
        eventPublisher.publishEvent(new OrderEndedEvent(order, null));
        return order;
    }

    /**
     * 退款成功，将order状态改为REFUND
     */
    public Order refundedOrder(User user, Order order, Step step, HisRefundParam refundParam) {
        order.setOperator(user);
        order.setPaymentStatus(PaymentStatus.REFUND);
        if (refundParam.getRefundTime() == null) {
            order.setRefundedDate(new Date());
        } else {
            order.setRefundedDate(refundParam.getRefundTime());
        }
        if (step != null) {
            operationOrderSendMessage(user, null, order, step, "退款成功");
        } else {
            operationOrderSendMessage(user, null, order, Step.REFUND, "退款成功");
        }
        orderRepository.save(order);

        try {
            // 退款成功后，发送微信模板消息给患者
            if (AppContext.getInstance(NoticeService.class).shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
                WechatService wechatService = AppContext.getInstance(WechatService.class);
                List<UserPlatformInfo> list = wechatService.screenOfficialAccount(order.getHospital(),
                        order.getPatient().getUser(),
                        PlatformForEnum.PATIENT);
                String pagePath = "subpackages/pat/order-detail/index?orderId=" + order.getId();
                MiniProgram miniProgram = wechatService.getMiniProgram(order.getHospital(),
                        PlatformForEnum.PATIENT,
                        pagePath);
                WechatTemplatedData data = new WechatTemplatedData();
                data.setFirstStr("");
                data.addKeywordsStr(
                        "已退款，" + "医生" + order.getDoctor().getUser().getFullName() + "的" + order.getVisitType().getName()
                                + "咨询费用已成功退款，请注意查收");
                data.addKeywordsStr("微信支付");
                data.addKeywordsStr(order.getId().toString());
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                data.addKeywordsStr(format.format(order.getPayOrderCreatedDate()));
                float fee = (float) (order.getRegistrationFee() / 100.00);
                data.addKeywordsStr(fee + "元");
                data.setMiniProgram(miniProgram);
                data.setRemarkStr("点击查看详情");
                wechatService.sendWechatNotify(list, data, HospitalSettingKey.NOTIFY_ORDER_REFUND_OVER, order.getHospital());
            }
        } catch (Exception e) {
            log.error("发送微信小程序消息失败", e);
        }
        ScheduleUse use = scheduleUseRepository.findOneBySchedule(order.getService()).orElse(null);
        // 急诊退号不需要修改数量
        // 除了急诊外, 其他的类型必定有数据
        if (order.getOrderType() != ClinicType.EMERGENCY && use != null) {
            if (order.getOnlineType() == OnlineType.ONLINE) {
                use.setOnlineUsedCount(use.getOnlineUsedCount() - 1);
            } else {
                use.setInternetUsedCount(use.getInternetUsedCount() - 1);
            }
            scheduleUseRepository.save(use);
        }

        return order;
    }

    public void checkRegister(ClinicType orderType, OnlineType onlineType, RealTimeStatus realTimeStatus) {
        if (onlineType == OnlineType.ONLINE) {
            if (orderType == ClinicType.EMERGENCY && realTimeStatus == RealTimeStatus.APPOINTMENT) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("不支持的挂号类型");
            }
        } else {
            if (orderType != ClinicType.OUT || realTimeStatus != RealTimeStatus.APPOINTMENT) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("不支持的挂号类型");
            }
        }

    }

    @Transactional
    public Order registerOrder(User user, Order order, RegisterVM vm) {
        // 2023/4/11 sprint14 订单创建需要判断就诊人的实名信息
        AppContext.getInstance(PatientManager.class).checkPatientIdentified(order.getPatient());
        // 判断该订单能否挂号
        checkRegister(vm.getOrderType(), vm.getOnlineType(), vm.getRealTimeStatus());

        if (order.getOrderType() == ClinicType.UNKNOWN) {
            order.setOrderType(vm.getOrderType());
            order.setOnlineType(vm.getOnlineType());
            order.setRealTimeStatus(vm.getRealTimeStatus());
        }

        Schedule schedule = order.getService();
        if (schedule != null) {
            ScheduleUse use = scheduleUseRepository.findOneBySchedule(schedule)
                .orElseGet(() -> new ScheduleUse(schedule));
            if (canRegister(schedule, vm.getOrderType(), vm.getOnlineType(),
                            vm.getRealTimeStatus(), use, true)) {
                scheduleUseRepository.save(use);
            } else {
                throw ErrorType.REGISTER_COUNT_IS_FULL.toProblem();
            }
            if (schedule.getMedicalWorker() != null) {
                order.setDoctor(schedule.getMedicalWorker());
            }
        }

        if (order.getRegistrationFee() == -1) {
            int fee;
            if (schedule == null) {
                MedicalWorker doctor = order.getDoctor();
                DoctorFee doctorFee = doctorFeeService.getDoctorFee(doctor);
                if (doctorFee == null) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem(
                        doctor.getUser().getFullName() + "医生未开放就诊，无法提交订单");
                }
                if (doctorFee.getDept() == null) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem(
                        doctor.getUser().getFullName() + "医生就诊数据有误，无法提交订单");
                }
                if (order.getVisitType() == VisitType.GRAPHIC && !doctorFee.getGraphicConsultEnabled()) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem(
                        doctor.getUser().getFullName() + "医生未开放图文咨询，无法提交订单");
                }
                if (order.getVisitType() == VisitType.VIDEO && !doctorFee.getVideoConsultEnabled()) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem(
                        doctor.getUser().getFullName() + "医生未开放视频咨询，无法提交订单");
                }
                fee = order.getVisitType() == VisitType.GRAPHIC ? doctorFee.getGraphicConsultFee() :
                    doctorFee.getVideoConsultFee();
            } else {
                fee = order.calcRegistrationFee();
            }
            order.setRegistrationFee(fee);
        }

        if (HospitalSettingsHelper.getBoolean(order.getHospital(), HospitalSettingKey.PAY_ONE_FEN)) {
            order.setRegistrationFee(1);
        }
        order.setPayOrderCreatedDate(new Date());
        order.setAccessCode(RandomUtils.generateRandomNumbers(8));
        order.setVisitId("" + Holder.INSTANCE.nextId());

        if (vm.getPaymentMethod() == PaymentMethod.OFFLINE) {
            if (vm.getOrderType() == ClinicType.OUT && vm.getOnlineType() == OnlineType.OFFLINE
                && vm.getRealTimeStatus() == RealTimeStatus.APPOINTMENT) {
                order.setPaymentStatus(PaymentStatus.UNPAID);
                order.setPaymentMethod(PaymentMethod.OFFLINE);
                operationOrderSendMessage(user, null, order, Step.REGISTER, "患者挂号, 线下支付");
            } else {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("不支持的支付方式");
            }
        } else {
            order.setPaymentMethod(vm.getPaymentMethod());
            order.setPaymentStatus(PaymentStatus.UNPAID);
            if (Objects.equals(order.getRegistrationFee(), 0)
                || !HospitalSettingsHelper.getBoolean(order.getHospital(), HospitalSettingKey.OPEN_WECHAT_PAY)) {
                order.setRegistrationFee(0);
                payOrder(user, order, new Date(), new HisPayParam(order.getId() + ""));
            } else {
                operationOrderSendMessage(user, null, order, Step.REGISTER, "患者挂号");
                int anInt = HospitalSettingsHelper.getInt(order.getHospital(), HospitalSettingKey.PAY_ORDER_LIMIT_TIME);

                redisUtil.set("order:" + order.getId(), 1, anInt * 60);
            }
        }

        return order;
    }

    /**
     * 是否能挂号, 如果use==null, 已挂号数量取0,
     *
     * @param schedule
     * @param type
     * @param use
     * @param setCount 是否将计算数量写回use, 如果use为null, 将不写回
     * @return
     */
    public boolean canRegister(Schedule schedule, ClinicType type, OnlineType onlineType,
                               RealTimeStatus realTimeStatus, ScheduleUse use, boolean setCount) {
        int count;
        if (realTimeStatus == RealTimeStatus.REAL_TIME) {
            if (onlineType == OnlineType.ONLINE) {
                if (isOutDays(schedule, false)) {
                    throw ErrorType.REGISTER_IS_NOT_ON_TIME.toProblem();
                }
                count = use == null ? 0 : use.getOnlineUsedCount();
                if (schedule.getOnlineCount() <= count) {
                    return false;
                }
                if (use != null && setCount) {
                    use.setOnlineUsedCount(count + 1);
                }
            } else {
                return false;
            }
        } else if (onlineType == OnlineType.ONLINE) {
            if (type == ClinicType.EMERGENCY) {
                return false;
            }
            if (isOutDays(schedule, true)) {
                throw ErrorType.REGISTER_IS_NOT_ON_TIME.toProblem();
            }
            count = use == null ? 0 : use.getOnlineUsedCount();
            if (schedule.getOnlineCount() <= count) {
                return false;
            }
            if (use != null && setCount) {
                use.setOnlineUsedCount(count + 1);
            }
        } else {
            if (type != ClinicType.OUT) {
                return false;
            }
            if (isOutDays(schedule, true)) {
                throw ErrorType.REGISTER_IS_NOT_ON_TIME.toProblem();
            }
            count = use == null ? 0 : use.getInternetUsedCount();
            if (schedule.getInternetCount() <= count) {
                return false;
            }
            if (use != null && setCount) {
                use.setInternetUsedCount(count + 1);
            }
        }
        return true;
    }

    /**
     * 开始日期=明天，结束时间=开始时间+普通号可预约天数-1， 比如：今天是2020-12-1，普通号可预约天数为15天，那么开始时间=2020-12-2 结束时间= 2020-12-16
     *
     * @param schedule          排班班次
     * @param isOnlyAppointment 是否只是预约
     * @return
     */
    public boolean isOutDays(Schedule schedule, boolean isOnlyAppointment) {
        long now = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH).getTime();
        long start = isOnlyAppointment ? Constants.ONE_DAY_MILLIONS : 0;
        if (schedule.getEndTime().getTime() - now <= start) {
            return true;
        }
        if (schedule.getType() == ScheduleType.NORMAL) {
            int days = HospitalSettingsHelper.getInt(schedule.getHospital(),
                                                     HospitalSettingKey.NORMAL_REGISTER_DAYS);
            long time = (days + 1) * Constants.ONE_DAY_MILLIONS;
            return schedule.getStartTime().getTime() - now >= time;
        } else if (schedule.getType() == ScheduleType.ONLINE) {
            int days = HospitalSettingsHelper.getInt(schedule.getHospital(),
                                                     HospitalSettingKey.ONLINE_REGISTER_DAYS);
            long time = (days + 1) * Constants.ONE_DAY_MILLIONS;
            return schedule.getStartTime().getTime() - now >= time;
        } else {
            int days = HospitalSettingsHelper.getInt(schedule.getHospital(),
                                                     HospitalSettingKey.SPECIAL_REGISTER_DAYS);
            long time = (days + 1) * Constants.ONE_DAY_MILLIONS;
            return schedule.getStartTime().getTime() - now >= time;
        }
    }

    /**
     *
     * @param user
     * @param order
     * @param payTime
     * @return
     */
    // fixing
    public Order payOrder(User user, Order order, Date payTime, HisPayParam hisPayParam) {
        // 如果这个订单在his已经结算过，则不再进行重复结算
        boolean useHis = HospitalSettingsHelper.getBoolean(order.getHospital(), HospitalSettingKey.HIS_SYNC_SCHEDULE);
        boolean isCharge = false;
        boolean chargeSuccess = true;
        if (useHis) {
            // 判断his是否已经结算过，结算过的，不进行结算处理
            HisChargeRecord chargeRecord = AppContext.getInstance(HisChargeRecordService.class).getOnlineRegisterChargeConfirmChargeRecord(order);
            if (chargeRecord != null && chargeRecord.isSuccess()) {
                isCharge = true;
                log.info("his已结算，后续跳过his结算 orderId:" + order.getId() + ", settleId: " + chargeRecord.getSettleId());
            }
        }
        if (order.getPaymentStatus() == PaymentStatus.UNPAID) {
            // 只有未支付过的才进去
            order.setOperator(user);
            order.setPaymentStatus(PaymentStatus.PAID);
            order.setRegisteredDate(payTime);
            operationOrderSendMessage(user, null, order, Step.PAY, "患者挂号，支付费用");
            // 用户挂号完成
            int queueNumber = queueService.createOrderQueue(order.getHospital(), order.getDept(), Type.ORDER);
            order.setOrderQueueNumber(queueNumber);
            if (order.getStatus() == OrderStatus.ONTIME_CONFIRMED && order.getConfirmedDate() == null) {
                order.setConfirmedDate(new Date());
            }
            orderRepository.save(order);
        }

        try {
            if (HospitalSettingsHelper.getBoolean(order.getHospital(), HospitalSettingKey.ELEC_INVOICE_ENABLE)) {
                userElecInvoiceService.saveElecInvoice(order.getUser(), order);
            }
        } catch (Exception e) {
            log.error("生成电子处方失败", e);
        }

        if (StringUtils.isBlank(hisPayParam.getTransactionId())) {
            log.info("没有支付账单，跳过his结算");
        } else {
            // 记录his结算结果
            NodeRedResponseData<ConfirmRegistResult> hisResult = null;

            try {
                if (useHis && !isCharge) {
                    // 订单支付成功后，调用his结算接口将账单传递给his
                    hisResult = BusinessServiceStrategy.getInstance().getStrategy(useHis)
                            .confirmRegist(order.getHospital(), order, hisPayParam);
                    if (hisResult == null || hisResult.getContent() == null || !hisResult.getContent().isSuccess()) {
                        log.error("线上门诊挂号结算失败 orderId: " + order.getId() + ", result: " + (hisResult == null ? null : hisResult.getResponseBody()));
                        chargeSuccess = false;
                    } else {
                        order.setSettleDate(new Date());
                    }
                }
            } catch (Exception e) {
                log.error("his结算失败 orderId: " + order.getId() + e);
                chargeSuccess = false;
            }
            if (!isCharge && useHis) {
                AppContext.getInstance(HisChargeRecordService.class).saveOnlineOutpatientConfirmChargeRecord(order.getHospital(),
                        order.getId(), hisResult, hisPayParam.getTransactionId());
            }
            if (!chargeSuccess) {
                // 2024年12月13日 根据结算失败费用不回滚需求http://81.70.195.70:8911/zentao/story-view-8825.html
                // 线上挂号 结算失败，不进行退款，修改状态为退款中。
//                // his结算失败，进行退款
//                RefundOrderVM refundVm = new RefundOrderVM();
//                refundVm.setId(order.getId());
//                refundVm.setType(ThirdOrderType.REGISTER);
//                // 医保业务未实现
//                boolean refundedFromWechat = AppContext.getInstance(PayManager.class).refund(order.getHospital(),
//                        false, order.getPaymentMethod(), refundVm, order.getRegistrationFee(), null);
//                if (refundedFromWechat) {
//                    log.info("his结算失败退款：已向微信发起退款请求 orderId: {}", order.getId());
//                    // 订单状态改为退款中
//                    operationOrderSendMessage(user, null, order, Step.REFUND, "his结算失败，自动退款");
//                } else {
//                    log.info("his结算失败退款：没有微信订单，不需要去微信退款，直接修改订单状态为REFUND orderId: {}", order.getId());
//                    refundedOrder(user, order, EndOrderVM.EndType.REFUND.getOrderStep(), null);
//                }
//                return order;

                // 2025年01月06日 根据配置控制结算失败是否自动退款 需求http://81.70.195.70:8911/zentao/story-view-9493.html

//                operationOrderSendMessage(user, null, order, Step.REFUND, "his结算失败，自动退款");
//                return order;

                boolean autoRefund = HospitalSettingsHelper.getBoolean(order.getHospital(),
                                                                       HospitalSettingKey.AUTO_REFUND_AFTER_CHARGE_CONFIRM_FAILED);
                if (autoRefund) {
                    // his结算失败，进行退款
                    RefundOrderVM refundVm = new RefundOrderVM();
                    refundVm.setId(order.getId());
                    refundVm.setType(ThirdOrderType.REGISTER);
                    // 医保业务未实现
                    boolean refundedFromWechat = AppContext.getInstance(PayManager.class).refund(order.getHospital(),
                                                                                                 false, order.getPaymentMethod(), refundVm, order.getRegistrationFee(), null);
                    if (refundedFromWechat) {
                        log.info("his结算失败退款：已向微信发起退款请求 orderId: {}", order.getId());
                        // 订单状态改为退款中
                        operationOrderSendMessage(user, null, order, Step.REFUND, "his结算失败，自动退款");
                    } else {
                        log.info("his结算失败退款：没有微信订单，不需要去微信退款，直接修改订单状态为REFUND orderId: {}", order.getId());
                        refundedOrder(user, order, EndOrderVM.EndType.REFUND.getOrderStep(), null);
                    }
                }

                return order;
            }

        }
        eventPublisher.publishEvent(new OrderConfirmedEvent(order, null));
        // 1.提醒医生抢单
        if (order.getOnlineType() == OnlineType.ONLINE
            && order.getRealTimeStatus() == RealTimeStatus.REAL_TIME) {
            eventPublisher.publishEvent(
                new RegisteredEvent(order, null, HospitalSettingKey.NOTIFY_EMERGENCY_VISIT_ACCEPT));

            SuperviseDto dto = SuperviseUtil.getDto(order.getHospital());
            SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
            if (superviseService != null){
                // 上报预约单
                superviseService.appointmentInfoUpload(order.getHospital(), order.getId(), dto);
            }
        }
        return order;
    }

    public Order waitingOrder(User current, Order order, User assistant) {
        order.setAdmissionDate(new Date());
        userAddToOrder(current, order);
        VisitStartedEvent visitStartedEvent;
        MedicalWorker medicalWorker = medicalWorkerRepository.findOneByHospitalAndUser(order.getHospital(), current).orElse(null);
        operationOrderSendMessage(current, medicalWorker, order, Step.START, "医生接诊");
        visitStartedEvent = new VisitStartedEvent(order);

        //发送模板消息给患者
        NoticeService noticeService = AppContext.getInstance(NoticeService.class);
        if (noticeService.shouldSendUser(order.getHospital(), order.getPatient().getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
            WechatService wechatService = AppContext.getInstance(WechatService.class);
            List<UserPlatformInfo> list = wechatService.screenOfficialAccount(order.getHospital(),
                    order.getPatient().getUser(),
                    PlatformForEnum.PATIENT);
            String pagePath = "pages/common/chat-room/index?orderId=" + order.getId();
            MiniProgram miniProgram = wechatService.getMiniProgram(order.getHospital(), PlatformForEnum.PATIENT, pagePath);
            WechatTemplatedData data = new WechatTemplatedData();
            data.setFirstStr("");
            data.addKeywordsStr(order.getDept().getDeptName());
            data.addKeywordsStr(current.getFullName() + "医生已接诊，请及时与医生沟通您的病情");
            data.setMiniProgram(miniProgram);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            data.addKeywordsStr(format.format(new Date()) + "接诊");
            data.setRemarkStr("点击详情");
            wechatService.sendWechatNotify(list, data, HospitalSettingKey.NOTIFY_CALL_FOR_VISIT_TO_RECEIVED,
                    order.getHospital());

            List<String> methods = HospitalSettingsHelper.getListString(order.getHospital(),
                    HospitalSettingKey.NOTIFY_CALL_FOR_VISIT_TO_RECEIVED);
            if (methods.contains(HospitalSettingKey.NotificationMethod.SMS.name())) {
                //发短信给患者
                //TODO 区分H5/小程序
//                SMSServiceImpl aliSMSService = AppContext.getInstance(SMSServiceImpl.class);
//                aliSMSService.sendMessage(order.getHospital(),
//                        HospitalSettingKey.SMS_NOTIFY_START,
//                        order.getPatient().getUser().getMobile(),
//                        ImmutableMap.<String, String>builder()
//                                .put("PatientName", order.getPatient().getName())
//                                .put("DoctorName", order.getDoctor().getUser().getFullName())
//                                .put("ConsultType", order.getVisitType().getName() + "咨询").build());
            }
        }
        Map<String, String> detail = Maps.newHashMap();
        String visitType = order.getVisitType().getName();
        String orderType = order.getOrderType().getName();
        detail.put("type", visitType + orderType);
        detail.put("doctor", order.getDoctor().getUser().getFullName());
        detail.put("date", TimeUtils.dateToString(new Date(), "yyyy年M月dd日 HH:mm:ss"));
        detail.put("patient", order.getPatient().getName());
        detail.put("desc", "医生已接诊，请及时与医生沟通您的病情");
        detail.put("orderId", "" + order.getId());
        detail.put("product_id", detail.get("orderId"));
        detail.put("dept", order.getDept().getDeptName());
        detail.put("notice_content", String.format("您好，%s的%s医生已接诊您的问诊，请%s尽快就诊",
                order.getVisitType().getName() + order.getOrderType().getName(),
                order.getDoctor().getUser().getFullName(), order.getPatient().getName()));
        noticeService.miniProgramNotice(order.getHospital(), order.getPatient(), HospitalSettingKey.NOTICE_PATIENT_DOCTOR_RECEIVED, detail);
        //开始就诊
        eventPublisher.publishEvent(visitStartedEvent);
        return order;
    }


    public Order updateGrabAnOrder(User current, Order order) {
        if (!Objects.equals(order.getStatus(), OrderStatus.REGISTERED)) {
            throw ErrorType.GRAB_ORDER_TIP.toProblem();
        }
        MedicalWorker doctor = medicalWorkerRepository
            .findOneByHospitalAndUser(order.getHospital(), current).orElse(null);
        order.setDoctor(doctor);
        order.setConfirmedDate(new Date());
        userAddToOrder(current, order);
        operationOrderSendMessage(current, doctor, order, Step.CONFIRM, "抢单");
        // 抢单成功
        eventPublisher.publishEvent(new PatientReceivedEvent(order));
        return order;
    }

    public void sentPrescriptionByIm(User sender, User receiver,
                                     PrescriptionOrder prescriptionOrder) {
        if (prescriptionOrder.isSendUser()) {
            log.info("发送处方给用户  处方单状态---{}", prescriptionOrder.getStatus());
            Order order = prescriptionOrder.getOrder();
            if (prescriptionOrder.getStatus() == PrescriptionOrder.Status.SENT) {
                Hospital hospital = prescriptionOrder.getHospital();
                MessageIm messageIm = new MessageIm(Desc.SEND_PRESCRIPTION, prescriptionOrder,
                                                    order);
                tencentIMService.sendGroupMessage(hospital, order.getImGroupId(), sender.getUsername(), messageIm, null);
            }
        }
    }

    /**
     * 修改订单
     *
     * @param order 订单
     * @param dto   订单数据对象
     * @return
     */
    public Order updateOrder(Order order, OrderDTO dto) {
        if (order.getOrderType() != ClinicType.EMERGENCY && order.getStatus() == OrderStatus.DRAFT) {
            Schedule schedule = null;
            if (order.getRealTimeStatus() == RealTimeStatus.APPOINTMENT) {
                schedule = scheduleRepository.getById(dto.getService().getId());
            } else {
                Dept dept = deptRepository.getById(dto.getDept().getId());
                if (order.getDoctor() == null) {
                    List<Schedule> schedules = findSchedules(order.getHospital(), dept)
                        .stream().filter(u -> u.getMedicalWorker() == null)
                        .collect(Collectors.toList());
                    if (schedules.isEmpty()) {
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem(dept.getDeptName() + "7天内无在线出诊计划");
                    }
                    schedule = schedules.get(0);
                }
            }
            if (order.getDoctor() == null && !Objects.equals(schedule, order.getService())) {
                ScheduleUse use = scheduleUseRepository.findOneBySchedule(schedule).orElse(null);
                canRegister(schedule, order.getOrderType(), order.getOnlineType(),
                            order.getRealTimeStatus(), use, false);
                order.setService(schedule);
                order.setDept(schedule.getDept());
            }

            if (schedule != null && schedule.getMedicalWorker() != null) {
                order.setDoctor(schedule.getMedicalWorker());
            }
        }

        PatientDTO patientDTO = dto.getPatient();
        if (patientDTO != null) {
            order.setPatient(patientRepository.getById(patientDTO.getId()));
        }
        order.setDisease(StringUtil.joinWithDoubleSeparator(dto.getDiseases(), "|"));
        order.setDescription(dto.getDescription());
        order.setMedicine(dto.getMedicine());
        order.setMedicineDuration(dto.getMedicineDuration());
        order.setIllness(dto.getIllness());

//        addRecordToOrder(order, dto.getPatientClinics(), dto.getPatientPrescriptions());

        return orderRepository.save(order);
    }

//    public void addRecordToOrder(Order order, List<PatientClinicDTO> patientClinicsDTOs,
//                                 List<PrescriptionRecordDTO> patientPrescriptionDTOs) {
//        List<PatientClinic> patientClinics = Lists.newArrayList();
//        patientClinicsDTOs.forEach(
//            u -> patientClinics.add(patientClinicRepository.getById(u.getId()))
//        );
//        order.setPatientClinics(patientClinics);
//
//        if (CollectionUtils.isNotEmpty(patientPrescriptionDTOs)) {
//            PrescriptionRecordDTO prescriptionRecordDTO = patientPrescriptionDTOs.get(0);
//            if (!prescriptionRecordDTO.isNew()) {
//                List<PrescriptionRecord> prescriptionRecords = Lists.newArrayList();
//                patientPrescriptionDTOs.forEach(
//                    u -> prescriptionRecords.add(prescriptionRecordRepository.getById(u.getId()))
//                );
//                order.setPatientPrescriptions(prescriptionRecords);
//            } else {
//                order.setDrugHistory(StandardObjectMapper.stringify(patientPrescriptionDTOs));
//            }
//        }
//    }

    private boolean checkWeChatOnOff(HospitalSettingKey key, Hospital hospital) {
        List<String> methods = HospitalSettingsHelper.getListString(hospital, key);
        return methods.contains(HospitalSettingKey.NotificationMethod.WECHAT.name());
    }

    private boolean checkMessageCenterOnOff(HospitalSettingKey key, Hospital hospital) {
        List<String> methods = HospitalSettingsHelper.getListString(hospital, key);
        return methods.contains(HospitalSettingKey.NotificationMethod.MESSAGE_CENTER.name());
    }

    private void sendMessageToUser(User user, Order order, HospitalSettingKey key, Desc desc) {
        AppContext.getInstance(NoticeService.class).notice(user, order, key, desc);
    }

    public String drawPrescriptionOrder(PrescriptionOrder prescriptionOrder) {

        Map<String, Object> data = Maps.newHashMap();
        data.put("title", prescriptionOrder.getOrder().getHospital().getName());
        data.put("patientName", prescriptionOrder.getPatient().getName());
        data.put("gender", prescriptionOrder.getPatient().getGender().getValue());
        Date ageStr = Optional
            .ofNullable(prescriptionOrder.getOrder().getPatient().getBirthday())
            .orElse(new Date());
        data.put("age", TimeUtils.age(ageStr) + " 岁");
        data.put("deptName", prescriptionOrder.getOrder().getDept().getDeptName());
        MedicalCase medicalCase = medicalCaseRepository
            .findByOrderId(prescriptionOrder.getOrder().getId()).orElse(null);
        String diag = "";
        if (null != medicalCase && null != medicalCase.getDiseases()) {
            List<String> DiseaseName = org.apache.commons.compress.utils.Lists.newArrayList();
            for (MedicalCaseDisease mc : medicalCase.getDiseases()) {
                DiseaseName.add(mc.getDiseaseName());
            }
            diag = StringUtils.join(DiseaseName, "/");
        }
        data.put("diagnosis", diag);

//        if (StringUtils.isNotBlank(diag)) {
//            if (StringUtils.isNotBlank(medicalCase.getDiagnosis())) {
//                data.put("diagnosis", diag + "/" + medicalCase.getDiagnosis());
//            } else {
//                data.put("diagnosis", diag);
//            }
//        } else {
//            data.put("diagnosis", medicalCase.getDiagnosis());
//        }

        data.put("url", "#");
        data.put("doctorNameUrl", "");
        data.put("doctorReviewNameUrl", "");
        data.put("createTime", DataTypes.DATE.asString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        data.put("reviewTime", "");
        data.put("date", DataTypes.DATE.asString(new Date(), "yyyy-MM-dd"));
        data.put("prescriptionId", prescriptionOrder.getId());

        //查询单个处方的药品
        List<Prescription> prescriptionResult = prescriptionRepository
            .findByPrescriptionOrderId(prescriptionOrder.getId());
        //每个药品名称和用法和说明
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<String> remarks = Lists.newArrayList();
        prescriptionResult.forEach(prescription -> {
            Map<String, Object> drug = Maps.newHashMap();
            drug.put("drugName", prescription.getDrugName());
            drug.put("packingSpecification", prescription.getDosageSpec() == null ? "/" : prescription.getDosageSpec());
            drug.put("quantity", prescription.getQuantity());
            drug.put("unit", prescription.getUnit());
            drug.put("single", prescription.getSingle());
            drug.put("singleUnit", prescription.getSingleUnit());
            drug.put("useFrequency", prescription.getUseFrequency());
            drug.put("useage", prescription.getUseage());
            drug.put("courseTreatment", prescription.getTimes() + prescription.getTreatmentUnit().getName());
            if (StringUtils.isNotBlank(prescription.getRemark())) {
                remarks.add(prescription.getRemark());
            }
            listMap.add(drug);
        });
        data.put("drugs", listMap);
        if (remarks.isEmpty()) {
            data.put("remarks", " / ");
        } else {
            data.put("remarks", StringUtils.join(remarks, " / "));
        }

        Context context = new Context();
        context.setVariables(data);
        return AppContext.getInstance(TemplateEngine.class).process("prescription.html", context);
    }


    public String drawDiagnosis(MedicalCase medicalCase) {
        Order order = medicalCase.getOrder();
        Hospital hospital = order.getHospital();
        Dept dept = order.getDept();
        Patient patient = order.getPatient();
        MedicalWorker doctor = order.getDoctor();

        Context context = new Context();
        context.setVariable("hospitalName", hospital.getName());
        context.setVariable("deptName", dept.getDeptName());
        context.setVariable("orderId", order.getId());
        context.setVariable("patientName", patient.getName());
        context.setVariable("patientSex", patient.getGender().getShortValue());
        context.setVariable("patientAge", TimeUtils.age(patient.getBirthday()));

        String diagnosis = "";
        if (medicalCase.getDiseases() != null) {
            List<String> DiseaseName = org.apache.commons.compress.utils.Lists.newArrayList();
            for (MedicalCaseDisease mc : medicalCase.getDiseases()) {
                DiseaseName.add(mc.getDiseaseName());
            }
            diagnosis = StringUtils.join(DiseaseName, "/");
        }
        context.setVariable("diagnosis", diagnosis);
        context.setVariable("summary", medicalCase.getSummary());
        context.setVariable("doctorName", "");

        return AppContext.getInstance(TemplateEngine.class).process("diagnosis.html", context);
    }

    private void pendingCancelOrder(Order order) {
        BusinessService businessService = BusinessServiceStrategy.getInstance()
            .getStrategy(HospitalSettingsHelper.getBoolean(order.getHospital(),
                                                           HospitalSettingKey.HIS_SYNC_SCHEDULE));
        businessService.cancelPreRegist(order.getHospital(), order);
    }
}
