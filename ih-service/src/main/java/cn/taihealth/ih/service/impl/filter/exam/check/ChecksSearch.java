package cn.taihealth.ih.service.impl.filter.exam.check;

import cn.taihealth.ih.domain.hospital.Checks;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

/**
 * @Description
 * <AUTHOR>
 * @date 2020/12/28 18:57
 */
public class ChecksSearch extends SearchCriteria<Checks> {

    private final String AWAITRESERVATION = "awaitReservation";
    private final String COMPLETERESERVATION = "completeReservation";

    public static ChecksSearch of(String query) {
        ChecksSearch checksSearch = new ChecksSearch();
        checksSearch.parse(query);
        return checksSearch;
    }


    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<Checks> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        boolean not = input.startsWith("-");
        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case ORDERTYPE:
                if (AWAITRESERVATION.equals(value)) {
                    return new AwaitFilter(value);
                } else {
                    return new AppointedFilter(value);
                }
            case USERCARDPHONE:
                return new UserCardPhoneFilter(value);
            case DEPT:
                return new DeptFilter(value);
            case CATEGORY:
                return new CategoryFilter(value);
            case EXAMDEVICEID:
                return new ExamDeviceFilter(value);
            case MIX_NAME_PHONE_NO:
                return new MixNameCodePhoneNoFilter(value);
            case CHECKTYPE:
                return new CheckReportFilter(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<Checks> createContainFilter(Set<String> contains) {
        return null;
    }


    enum Qualifier {
        ORDERTYPE,
        USERCARDPHONE,
        DEPT,
        CATEGORY,
        EXAMDEVICEID,
        MIX_NAME_PHONE_NO,
        CHECKTYPE
    }
}
