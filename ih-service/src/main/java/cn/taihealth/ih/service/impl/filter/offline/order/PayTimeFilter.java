package cn.taihealth.ih.service.impl.filter.offline.order;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.service.impl.filter.CreatedDateFilter;
import cn.taihealth.ih.service.impl.filter.DateFilter;
import com.gitq.jedi.data.specification.Operator;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Date;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.jpa.domain.Specification;

/**
 */
public class PayTimeFilter<T> extends DateFilter<T> {

    public PayTimeFilter(String param) {
        super("payTime", param);
    }

    @Override
    public Specification<T> toSpecification() {
        Operator operator = super.getOperator();
        Date date = super.getDate();
        String field = super.getField();
        if (date == null || operator == null) {
            return null;
        }

        switch (operator) {
            case eq:
                return Specifications.between(field, date, DateUtils.addDays(date, 1));

            case ge:
                return Specifications.ge(field, TimeUtils.getStartOfDay(date));

            case gt:
                return Specifications.gt(field, TimeUtils.getStartOfDay(date));

            case le:
                return Specifications.le(field, TimeUtils.getEndOfDay(date));

            case lt:
                return Specifications.lt(field, TimeUtils.getEndOfDay(date));
        }
        return null;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public boolean equals(Object other) {
        if (!(other instanceof CreatedDateFilter)) {
            return false;
        }

        return super.equals(other);
    }
}
