package cn.taihealth.ih.service.impl.event;

import cn.taihealth.ih.domain.ElectronicHealthCard;
import lombok.Getter;
import lombok.Setter;
import org.aspectj.lang.ProceedingJoinPoint;

import java.util.Map;

/**
 * <AUTHOR> jzs
 * @Date : 2024-06-20
 */
@Getter
@Setter
public class ElectronicHealthCardEvent extends AbstractEntityEvent<ProceedingJoinPoint> {

    private String appId;
    private Object result;
    private Map<String, String> paramMap;

//    private ElectronicHealthCard electronicHealthCard;
//    private String scene;
//    private String department;
//    private String cardType;
//    private String cardChannel;
//    private String cardCostTypes;

    public ElectronicHealthCardEvent(ProceedingJoinPoint point, String appId, Map<String, String> paramMap, Object result) {
        super(point);
        this.appId = appId;
        this.paramMap = paramMap;
        this.result = result;
    }
}
