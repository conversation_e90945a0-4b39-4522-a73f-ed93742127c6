package cn.taihealth.ih.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.Snowflake64.Holder;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.commons.util.ZipUtil;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.domain.cloud.wanda.WandaPayOrder;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.alipay.AliPayOrderRefundRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRepository;
import cn.taihealth.ih.repo.hospital.BillRepository;
import cn.taihealth.ih.repo.hospital.HisBillRepository;
import cn.taihealth.ih.repo.hospital.HospitalPublicPlatformRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.repo.wanda.WandaPayOrderRepository;
import cn.taihealth.ih.service.api.BillService;
import cn.taihealth.ih.service.api.alipay.AliPayBusinessService;
import cn.taihealth.ih.service.api.job.BillInsuranceJobService;
import cn.taihealth.ih.service.api.job.BillJobService;
import cn.taihealth.ih.service.api.job.BillNewInsuranceJobService;
import cn.taihealth.ih.service.api.job.BillNewJobService;
import cn.taihealth.ih.service.api.wanda.WandaPayBusinessService;
import cn.taihealth.ih.service.api.wechat.WechatService;
import cn.taihealth.ih.service.dto.BillFlushDTO;
import cn.taihealth.ih.service.impl.filter.bill.BillSearch;
import cn.taihealth.ih.service.vm.*;
import cn.taihealth.ih.wechat.service.vm.RefundOrderVM;
import com.alipay.api.AlipayClient;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

import static cn.taihealth.ih.domain.enums.ProjectTypeEnum.*;

/**
 *
 */
@Slf4j
@Service
@AllArgsConstructor
public class BillServiceImpl implements BillService {

    private final BillRepository billRepository;
    private final HisBillRepository hisBillRepository;
    private final WechatOrderRepository wechatOrderRepository;
    private final WechatOrderRefundRepository wechatOrderRefundRepository;
    private final AliPayOrderRepository aliPayOrderRepository;
    private final AliPayOrderRefundRepository aliPayOrderRefundRepository;
    private final WechatInsuranceOrderRefundRepository wechatInsuranceOrderRefundRepository;
    private final WechatService wechatService;
    private final AliPayBusinessService aliPayBusinessService;
    private final WechatInsuranceOrderRepository wechatInsuranceOrderRepository;
    private final WandaPayOrderRepository wandaPayOrderRepository;
    private final WandaPayBusinessService wandaPayBusinessService;

    @Override
    public BillReconciliationDetailHeadVM getBillDetails(long billId) {
        Bill bill = billRepository.findById(billId).orElseThrow();

        BillReconciliationDetailHeadVM headVM = new BillReconciliationDetailHeadVM();
        headVM.setReconciliationResult(bill.getReconciliationResult());
        //2024年12月25日17:07:40 结算失败不退款  为不影响对账逻辑，凡是平过账的账单一律视为平账，无论重新对账后的结果如何
        List<BillBalanceRecord> billBalanceRecords = AppContext.getInstance(BillBalanceRecordRepository.class)
            .findAllByTransactionId(bill.getTransactionId());
        if (CollectionUtils.isNotEmpty(billBalanceRecords)) {
            headVM.setReconciliationResult(ReconciliationResultEnum.BALANCING);
        }

        List<BillReconciliationDetailVM> list = new ArrayList<>();
        headVM.setContent(list);

        if (bill.getTransactionId() == null) {
            list.add(new BillReconciliationDetailVM(bill));
            return headVM;
        }
        if (bill.getBillOperateType() == BillOperateTypeEnum.PAYMENT) {
            if (bill.getInsuranceFlag() != null && bill.getInsuranceFlag()) {
                //医保支付
                setInsurancePayBillDetails(bill, list);
            } else {
                // 自费支付
                setSelfPayBillDetails(bill, list);
            }
        } else {
            if (bill.getInsuranceFlag() != null && bill.getInsuranceFlag()) {
                //医保退款
                setInsuranceRefundBillDetails(bill, list);
            } else {
                // 自费退款
                setSelfRefundBillDetails(bill, list);
            }
        }
        return headVM;
    }

    // 自费支付对账明细
    private void setSelfPayBillDetails(Bill bill, List<BillReconciliationDetailVM> list) {
        // 支付的，使用bill表
        list.add(new BillReconciliationDetailVM(bill));
        // 把冲正退款的内容，也算进来
        Specification<Bill> bSpec = Specifications.eq("transactionId", bill.getTransactionId());
        List<Bill> bills = billRepository.findAll(bSpec);

        bills.stream().filter(b -> !Objects.equals(b.getId(), bill.getId()))
                .filter(b -> BillRefundTypeEnum.PAYMENT == b.getRefundType())
                .forEach(b -> list.add(new BillReconciliationDetailVM(b)));

        // 根据transaction_id去hisbill中查询
        List<Specification<HisBill>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("transaction_id", bill.getTransactionId()));
        specs.add(Specifications.eq("hospital", bill.getHospital()));
        specs.add(Specifications.eq("transaction_status", "SUCCESS"));
        List<HisBill> hisBills = hisBillRepository.findAll(Specifications.and(specs));
        hisBills.stream().forEach(hb -> {
            BillReconciliationDetailVM detailVM = new BillReconciliationDetailVM(hb, bill);
            list.add(detailVM);
        });
    }

    // 医保支付对账明细
    private void setInsurancePayBillDetails(Bill bill,List<BillReconciliationDetailVM> list) {
        list.add(new BillReconciliationDetailVM(bill));
        // 把冲正退款的内容，也算进来
        Specification<Bill> bSpec = Specifications.eq("transactionId", bill.getTransactionId());
        List<Bill> bills = billRepository.findAll(bSpec);

        bills.stream().filter(b -> !Objects.equals(b.getId(), bill.getId()))
                .filter(b -> BillRefundTypeEnum.PAYMENT == b.getRefundType())
                .forEach(b -> list.add(new BillReconciliationDetailVM(b)));

        // 根据payOrderId去hisbill中查询
        List<Specification<HisBill>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("payOrderId", bill.getPayOrderId()));
        specs.add(Specifications.eq("hospital", bill.getHospital()));
        specs.add(Specifications.eq("transaction_status", "SUCCESS"));
        List<HisBill> hisBills = hisBillRepository.findAll(Specifications.and(specs));
        hisBills.stream().forEach(hb -> {
            BillReconciliationDetailVM detailVM = new BillReconciliationDetailVM(hb, bill);
            list.add(detailVM);
        });
    }

    // 自费退款对账明细
    private void setSelfRefundBillDetails(Bill bill, List<BillReconciliationDetailVM> list) {
        if ((bill.getBillServiceType() == OUTPATIENT_PAYMENT || bill.getBillServiceType() == HOSPITALIZATION_DEPOSIT) && StringUtils.isNotEmpty(bill.getOutRefundNo())) {
            // 门诊缴费/住院预交金的退款不合并（退款唯一标识不为空，说明是退款不合并的情况下产生的对账单）
            if (BillChannelEnum.WECHAT.equals(bill.getBillChannel())) {
                Optional<WechatOrderRefund> refundOptional = wechatOrderRefundRepository.findOneByOutRefundNo(bill.getOutRefundNo());
                if (refundOptional.isPresent()) {
                    BillReconciliationDetailVM vm = new BillReconciliationDetailVM(refundOptional.get(), bill);
                    list.add(vm);
                }
            }
            if (BillChannelEnum.ALIPAY.equals(bill.getBillChannel())) {
                Optional<AliPayOrderRefund> refundOptional = aliPayOrderRefundRepository.findOneByRefundNo(bill.getOutRefundNo());
                if (refundOptional.isPresent()) {
                    BillReconciliationDetailVM vm = new BillReconciliationDetailVM(refundOptional.get(), bill);
                    list.add(vm);
                }
            }
            List<Specification<HisBill>> specs = Lists.newArrayList();
            // 在退款不合并对账策略下settleId有可能是业务表主键（结算失败，直接退款），不影响明细查询（查不到数据）
            specs.add(Specifications.eq("settleId", bill.getSettleId()));
            specs.add(Specifications.eq("hospital", bill.getHospital()));
            specs.add(Specifications.eq("transaction_status", "REFUND"));
            List<HisBill> hisBills = hisBillRepository.findAll(Specifications.and(specs));
            hisBills.stream().forEach(hb -> {
                BillReconciliationDetailVM detailVM = new BillReconciliationDetailVM(hb, bill);
                list.add(detailVM);
            });
        } else {
            /**
             * 这里处理以下几种退款对账单
             * 1.门诊缴费/住院预交金的退款合并（outRefundNo为空）
             * 2.除门诊缴费/住院预交金以外的其他业务
             * 3.HIS单边账（退款类型）
             */
            if (BillChannelEnum.WECHAT.equals(bill.getBillChannel())) {
                List<WechatOrderRefund> refunds = wechatOrderRefundRepository.findAllByTransactionId(bill.getTransactionId());
                list.addAll(refunds.stream().map(u -> new BillReconciliationDetailVM(u, bill)).collect(Collectors.toList()));
            }
            if (BillChannelEnum.ALIPAY.equals(bill.getBillChannel())) {
                List<AliPayOrderRefund> refunds = aliPayOrderRefundRepository.findAllByTradeNo(bill.getTransactionId());
                list.addAll(refunds.stream().map(u -> new BillReconciliationDetailVM(u, bill)).collect(Collectors.toList()));
            }
            List<Specification<HisBill>> specs = Lists.newArrayList();
            specs.add(Specifications.eq("transaction_id", bill.getTransactionId()));
            specs.add(Specifications.eq("hospital", bill.getHospital()));
            specs.add(Specifications.eq("transaction_status", "REFUND"));
            List<HisBill> hisBills = hisBillRepository.findAll(Specifications.and(specs));
            hisBills.stream().forEach(hb -> {
                BillReconciliationDetailVM detailVM = new BillReconciliationDetailVM(hb, bill);
                list.add(detailVM);
            });
        }
    }

    // 医保退款对账明细
    private void setInsuranceRefundBillDetails(Bill bill,List<BillReconciliationDetailVM> list) {
        if (BillChannelEnum.WECHAT.equals(bill.getBillChannel())) {
            Optional<WechatInsuranceOrderRefund> refundOptional = wechatInsuranceOrderRefundRepository.findOneByPayOrdId(bill.getPayOrderId());
            if (refundOptional.isPresent()) {
                BillReconciliationDetailVM vm = new BillReconciliationDetailVM(refundOptional.get(), bill);
                list.add(vm);
            } else {
                refundOptional = wechatInsuranceOrderRefundRepository.findOneByShBillNo(bill.getPayOrderId());
                if (refundOptional.isPresent()) {
                    BillReconciliationDetailVM vm = new BillReconciliationDetailVM(refundOptional.get(), bill);
                    list.add(vm);
                }
            }
        }
        if (BillChannelEnum.ALIPAY.equals(bill.getBillChannel())) {
            Optional<AliPayOrderRefund> refundOptional = aliPayOrderRefundRepository.findOneByPayOrderId(bill.getPayOrderId());
            if (refundOptional.isPresent()) {
                BillReconciliationDetailVM vm = new BillReconciliationDetailVM(refundOptional.get(), bill);
                list.add(vm);
            }
        }
        // 医保退款可使用payOrderId去匹配hisbill
        List<Specification<HisBill>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("payOrderId", bill.getPayOrderId()));
        specs.add(Specifications.eq("hospital", bill.getHospital()));
        specs.add(Specifications.eq("transaction_status", "REFUND"));
        List<HisBill> hisBills = hisBillRepository.findAll(Specifications.and(specs));
        hisBills.stream().forEach(hb -> {
            BillReconciliationDetailVM detailVM = new BillReconciliationDetailVM(hb, bill);
            list.add(detailVM);
        });
    }

    @Override
    public BillReconciliationSummaryVM reconciliationSummary(Hospital hospital, String query) {

        List<Specification<Bill>> specs = Lists.newArrayList();
        Specification<Bill> spec = Specifications.and(Specifications.eq("hospital", hospital),
                BillSearch.of(query).toSpecification(),
                Specifications.or(getTypeSpec())
        );

        specs.add(spec);

        BillReconciliationSummaryVM summaryVM = new BillReconciliationSummaryVM();

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        criteriaQuery.groupBy(root.get("billPayStatus"));

        if (CollectionUtils.isNotEmpty(specs)) {
            Predicate where = Specifications.and(specs).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);
        }
        criteriaQuery.multiselect(root.get("billPayStatus"), criteriaBuilder.sum(root.get("paymentAmount")), criteriaBuilder.sum(root.get("refundAmount")), criteriaBuilder.sum(root.get("hisAmount")), criteriaBuilder.sum(root.get("refundedAmount")));

        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        // 三方支付总金额
        long thirdPayTotalAmount = 0;
        // 三方退款总金额
        long thirdRefundTotalAmount = 0;
        // 三方已退款总金额
        long thirdRefundedTotalAmount = 0;

        // his支付总金额
        long hisPayTotalAmount = 0;
        // his退款总金额
        long hisRefundTotalAmount = 0;

        for (Tuple tuple : result) {
            BillPayStatusEnum payStatusEnum = tuple.get(0, BillPayStatusEnum.class);
            Long paymentAmount = tuple.get(1, Long.class);
            Long refundAmount = tuple.get(2, Long.class);
            Long hisAmount = tuple.get(3, Long.class);
            Long refundedAmount = tuple.get(4, Long.class);
            if (BillPayStatusEnum.PAYED == payStatusEnum) {
                if (paymentAmount != null) {
                    thirdPayTotalAmount = paymentAmount;
                }
                if (hisAmount != null) {
                    hisPayTotalAmount = hisAmount;
                }
            } else if (BillPayStatusEnum.REFUNDED == payStatusEnum) {
                if (refundAmount != null) {
                    thirdRefundTotalAmount = refundAmount;
                }
                if (refundedAmount != null) {
                    thirdRefundedTotalAmount = refundedAmount;
                }
            }
        }
        summaryVM.setThirdTotalIncome(thirdPayTotalAmount);
        summaryVM.setThirdTotalRefund(thirdRefundTotalAmount);
        summaryVM.setRefundedTotalAmount(thirdRefundedTotalAmount);
        summaryVM.setRefundingTotalAmount(thirdRefundTotalAmount - thirdRefundedTotalAmount);
        summaryVM.setThirdNetIncome(thirdPayTotalAmount - thirdRefundTotalAmount);
        summaryVM.setHisTotalIncome(hisPayTotalAmount);
        summaryVM.setHisTotalRefund(hisRefundTotalAmount);
        summaryVM.setHisNetIncome(hisPayTotalAmount - hisRefundTotalAmount);
        return summaryVM;
    }

    @Override
    public BillReconciliationSummaryVM1 reconciliationSummary1(Hospital hospital, String query) {
        BillReconciliationSummaryVM1 summaryVM = new BillReconciliationSummaryVM1();
        List<BillReconciliationSummaryDetailVM> summaryDetailVMList = Lists.newArrayList();

        BillReconciliationSummaryDetailVM detailWX = new BillReconciliationSummaryDetailVM();
        detailWX.setBillType(BillTypeEnum.WECHAT);

        BillReconciliationSummaryDetailVM detailHis = new BillReconciliationSummaryDetailVM();
        detailHis.setBillType(BillTypeEnum.HIS);

        BillReconciliationSummaryDetailVM detailAli = new BillReconciliationSummaryDetailVM();
        detailAli.setBillType(BillTypeEnum.ALI_PAY);

        // 2024年03月21日15:16:16 不加额外条件，目前查出的数据都是微信的，支付宝默认为0
        List<Specification<Bill>> specs = Lists.newArrayList();
        Specification<Bill> spec = Specifications.and(Specifications.eq("hospital", hospital),
                                                      BillSearch.of(query).toSpecification(),
                                                      Specifications.or(getTypeSpec())
        );

        specs.add(spec);
        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        criteriaQuery.groupBy(root.get("billPayStatus"));

        if (CollectionUtils.isNotEmpty(specs)) {
            Predicate where = Specifications.and(specs).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);
        }
        criteriaQuery.multiselect(root.get("billPayStatus"),
                                  criteriaBuilder.sum(root.get("paymentAmount")),
                                  criteriaBuilder.sum(root.get("medicarePaymentAmount")),
                                  criteriaBuilder.sum(root.get("refundAmount")),
                                  criteriaBuilder.sum(root.get("medicareRefundAmount")),
                                  criteriaBuilder.sum(root.get("refundedAmount")),
                                  criteriaBuilder.sum(root.get("medicareRefundedAmount")),
                                  criteriaBuilder.sum(root.get("hisAmount")),
                                  criteriaBuilder.sum(root.get("hisMedicareAmount"))
        );

        List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

        for (Tuple tuple : result) {
            BillPayStatusEnum payStatusEnum = tuple.get(0, BillPayStatusEnum.class);
            Long paymentAmount = tuple.get(1, Long.class);
            Long medicarePaymentAmount = tuple.get(2, Long.class);
            Long refundAmount = tuple.get(3, Long.class);
            Long medicareRefundAmount = tuple.get(4, Long.class);
            Long refundedAmount = tuple.get(5, Long.class);
            Long medicareRefundedAmount = tuple.get(6, Long.class);
            Long hisAmount = tuple.get(7, Long.class);
            Long hisMedicareAmount = tuple.get(8, Long.class);
            if (BillPayStatusEnum.PAYED == payStatusEnum) {
                // 微信支付 总收入
                if (paymentAmount != null) {
                    detailWX.setTotalIncome(paymentAmount);
                }
                if (medicarePaymentAmount != null) {
                    detailWX.setMedicareTotalIncome(medicarePaymentAmount);
                }
                detailWX.setSelfTotalIncome(detailWX.getTotalIncome() - detailWX.getMedicareTotalIncome());
                // his  总收入
                if (hisAmount != null) {
                    detailHis.setTotalIncome(hisAmount);
                }
                if (hisMedicareAmount != null) {
                    detailHis.setMedicareTotalIncome(hisMedicareAmount);
                }
                detailHis.setSelfTotalIncome(detailHis.getTotalIncome() - detailHis.getMedicareTotalIncome());


            } else if (BillPayStatusEnum.REFUNDED == payStatusEnum) {
                // 微信支付 已退款
                if (refundedAmount != null) {
                    detailWX.setRefundedTotalAmount(refundedAmount);
                }
                if (medicareRefundedAmount != null) {
                    detailWX.setMedicareRefundedTotalAmount(medicareRefundedAmount);
                }
                detailWX.setSelfRefundedTotalAmount(detailWX.getRefundedTotalAmount() - detailWX.getMedicareRefundedTotalAmount());
                // 微信支付 退款中
                if (refundAmount != null) {
                    detailWX.setRefundingTotalAmount(refundAmount - detailWX.getRefundedTotalAmount());
                }
                if (medicareRefundAmount != null) {
                    detailWX.setMedicareRefundingTotalAmount(medicareRefundAmount - detailWX.getMedicareRefundedTotalAmount());
                }
                detailWX.setSelfRefundingTotalAmount(detailWX.getRefundingTotalAmount() - detailWX.getMedicareRefundingTotalAmount());
                // his 已退款
                if (hisAmount != null) {
                    detailHis.setRefundedTotalAmount(hisAmount);
                }
                if (hisMedicareAmount != null) {
                    detailHis.setMedicareRefundedTotalAmount(hisMedicareAmount);
                }
                detailHis.setSelfRefundedTotalAmount(detailHis.getRefundedTotalAmount() - detailHis.getMedicareRefundedTotalAmount());
                // his 退款中均为0
            }


        }
        // 微信支付 净收入
        detailWX.setNetIncome(detailWX.getTotalIncome() - detailWX.getRefundingTotalAmount() - detailWX.getRefundedTotalAmount());
        detailWX.setMedicareNetIncome(detailWX.getMedicareTotalIncome() - detailWX.getMedicareRefundedTotalAmount() - detailWX.getMedicareRefundingTotalAmount());
        detailWX.setSelfNetIncome(detailWX.getNetIncome() - detailWX.getMedicareNetIncome());

        // his 净收入
        detailHis.setNetIncome(detailHis.getTotalIncome() - detailHis.getRefundingTotalAmount() - detailHis.getRefundedTotalAmount());
        detailHis.setMedicareNetIncome(detailHis.getMedicareTotalIncome() - detailHis.getMedicareRefundedTotalAmount() - detailHis.getMedicareRefundingTotalAmount());
        detailHis.setSelfNetIncome(detailHis.getNetIncome() - detailHis.getMedicareNetIncome());


        summaryDetailVMList.add(detailWX);
        summaryDetailVMList.add(detailAli);
        summaryDetailVMList.add(detailHis);
        summaryVM.setSummaryDetailVMList(summaryDetailVMList);
        return summaryVM;
    }


    private static List<Specification<Bill>> getTypeSpec() {
        List<Specification<Bill>> type = Lists.newArrayList();
        type.add(Specifications.eq("billServiceType", APPOINTMENT_REGISTRATION));
        type.add(Specifications.eq("billServiceType", OUTPATIENT_PAYMENT));
        type.add(Specifications.eq("billServiceType", HOSPITALIZATION_DEPOSIT));
        type.add(Specifications.eq("billServiceType", ONLINE_CONSULTATION));
        type.add(Specifications.eq("billServiceType", ONLINE_REVISIT));
        type.add(Specifications.eq("billServiceType", ONLINE_PRESCRIPTION));
        return type;
    }

    @Override
    public BillServiceSummaryVM getBillServiceSummary(Hospital hospital, BillOperateTypeEnum operateType, String query) {
        List<Specification<Bill>> specs = Lists.newArrayList();
        Specification<Bill> spec = Specifications.and(Specifications.eq("hospital", hospital), Specifications.eq("billOperateType", operateType), BillSearch.of(query).toSpecification());
        specs.add(spec);

        BillServiceSummaryVM serviceSummaryVM = new BillServiceSummaryVM();

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        criteriaQuery.groupBy(root.get("billServiceType"));

        if (CollectionUtils.isNotEmpty(specs)) {
            Predicate where = Specifications.and(specs).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);
        }

        if (operateType == BillOperateTypeEnum.PAYMENT) {
            serviceSummaryVM.setOperateType(BillOperateTypeEnum.PAYMENT);
            criteriaQuery.multiselect(root.get("billServiceType"), criteriaBuilder.sum(root.get("paymentAmount")));
            List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

            TreeMap<ProjectTypeEnum, Long> serviceAmountMap = getZeroAmount();
            Long total = 0L;
            for (Tuple tuple : result) {
                Long amount = tuple.get(1, Long.class);
                ProjectTypeEnum typeEnum = tuple.get(0, ProjectTypeEnum.class);
                serviceAmountMap.put(typeEnum, amount);
                total += amount;
            }
            serviceSummaryVM.setTotalAmount(total);
            serviceSummaryVM.setServiceAmount(serviceAmountMap);
        } else {
            serviceSummaryVM.setOperateType(BillOperateTypeEnum.REFUND);
            criteriaQuery.multiselect(root.get("billServiceType"), criteriaBuilder.sum(root.get("refundAmount")));
            List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

            TreeMap<ProjectTypeEnum, Long> serviceAmountMap = getZeroAmount();
            Long total = 0L;
            for (Tuple tuple : result) {
                Long amount = tuple.get(1, Long.class);
                ProjectTypeEnum typeEnum = tuple.get(0, ProjectTypeEnum.class);
                serviceAmountMap.put(typeEnum, amount);
                total += amount;
            }
            serviceSummaryVM.setTotalAmount(total);
            serviceSummaryVM.setServiceAmount(serviceAmountMap);
        }
        return serviceSummaryVM;
    }

    @Override
    public BillServiceSummaryVM1 getBillServiceSummary1(Hospital hospital, BillOperateTypeEnum operateType,
                                                        String query) {
        List<Specification<Bill>> specs = Lists.newArrayList();
        Specification<Bill> spec = Specifications.and(Specifications.eq("hospital", hospital), Specifications.eq("billOperateType", operateType), BillSearch.of(query).toSpecification());
        specs.add(spec);

        BillServiceSummaryVM1 serviceSummaryVM = new BillServiceSummaryVM1();

        EntityManager entityManager = AppContext.getInstance(EntityManager.class);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<Bill> root = criteriaQuery.from(entityManager.getMetamodel().entity(Bill.class));
        criteriaQuery.groupBy(root.get("billServiceType"));

        if (CollectionUtils.isNotEmpty(specs)) {
            Predicate where = Specifications.and(specs).toPredicate(root, criteriaQuery, criteriaBuilder);
            criteriaQuery.where(where);
        }
        Long total = 0L;
        Long medicareTotal = 0L;
        List<BillServiceProjectTypeAmountVM> vms = Lists.newArrayList();

        if (operateType == BillOperateTypeEnum.PAYMENT) {
            serviceSummaryVM.setOperateType(BillOperateTypeEnum.PAYMENT);
            criteriaQuery.multiselect(root.get("billServiceType"), criteriaBuilder.sum(root.get("paymentAmount")),
                                      criteriaBuilder.sum( root.get("medicarePaymentAmount")));
            List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();

            for (Tuple tuple : result) {
                Long amount = tuple.get(1, Long.class);
                if (amount == null) {
                    amount = 0L;
                }
                ProjectTypeEnum typeEnum = tuple.get(0, ProjectTypeEnum.class);
                Long mAmount = tuple.get(2, Long.class);
                BillServiceProjectTypeAmountVM vm = new BillServiceProjectTypeAmountVM();


                total += amount;
                if (mAmount == null) {
                    mAmount = 0L;
                }
                vm.setOperateType(typeEnum);
                vm.setTotalAmount(amount);
                vm.setMedicareAmount(mAmount);
                medicareTotal += mAmount;
                vm.setSelfAmount(amount - mAmount);

                vms.add(vm);
            }
        } else {
            serviceSummaryVM.setOperateType(BillOperateTypeEnum.REFUND);
            criteriaQuery.multiselect(root.get("billServiceType"), criteriaBuilder.sum(root.get("refundAmount")),
                                      criteriaBuilder.sum(root.get("medicareRefundAmount")));
            List<Tuple> result = entityManager.createQuery(criteriaQuery).getResultList();
            for (Tuple tuple : result) {
                Long amount = tuple.get(1, Long.class);
                if (amount == null) {
                    amount = 0L;
                }
                ProjectTypeEnum typeEnum = tuple.get(0, ProjectTypeEnum.class);
                Long mAmount = tuple.get(2, Long.class);

                BillServiceProjectTypeAmountVM vm = new BillServiceProjectTypeAmountVM();


                total += amount;
                if (mAmount == null) {
                    mAmount = 0L;
                }
                vm.setOperateType(typeEnum);
                vm.setTotalAmount(amount);
                vm.setMedicareAmount(mAmount);
                medicareTotal += mAmount;
                vm.setSelfAmount(amount - mAmount);

                vms.add(vm);
            }

        }
        serviceSummaryVM.setTotalAmount(total);
        serviceSummaryVM.setMedicareAmount(medicareTotal);
        serviceSummaryVM.setSelfAmount(total - medicareTotal);
        serviceSummaryVM.setServiceAmount(vms);
        return serviceSummaryVM;
    }


    private TreeMap<ProjectTypeEnum, Long> getZeroAmount() {
        TreeMap<ProjectTypeEnum, Long> treeAmount = new TreeMap<>(Comparator.comparing(a -> a.code));
        for (ProjectTypeEnum e : ProjectTypeEnum.values()) {
            if (!treeAmount.containsKey(e)) {
                treeAmount.put(e, 0L);
            }
        }
        return treeAmount;
    }

    @Override
    public void downloadRawBills(HttpServletResponse response, Hospital hospital, Date startDate, Date endDate) {
        // 下载微信非医保账单
        List<HospitalPublicPlatform> publicPlatforms = AppContext.getInstance(HospitalPublicPlatformRepository.class)
                .findAllByHospital(hospital);
        HospitalPublicPlatform platform = publicPlatforms.stream().filter(u -> u.getPlatformFor() == HospitalPublicPlatform.PlatformForEnum.PATIENT)
                .findFirst().orElse(null);

        String name = TimeUtils.dateToString(startDate, "yyyy-MM-dd") + "到" + TimeUtils.dateToString(endDate, "yyyy-MM-dd") + "微信账单信息.zip";
        try {
            name = URLEncoder.encode(name, StandardCharsets.UTF_8);
        } catch (Exception ex) {
            log.warn("文件名编码错误");
        }
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, String.format("%s;filename=%s", "attachment", name));

        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            if (platform != null) {
                for (Date date : TimeUtils.getBetweenDates(startDate, endDate)) {
                    try (InputStream in = AppContext.getInstance(WechatService.class).getFundFlowBillStream(hospital, platform.getAppId(), date)) {
                        if (in != null) {
                            ZipUtil.writeFile(in, "微信/资金账单/" + TimeUtils.dateToString(date, "yyyy-MM-dd") + ".csv", zipOut);
                        }
                    }
                    try (InputStream in = AppContext.getInstance(WechatService.class).getTradeBillStream(hospital, platform.getAppId(), date)) {
                        if (in != null) {
                            ZipUtil.writeFile(in, "微信/交易账单/" + TimeUtils.dateToString(date, "yyyy-MM-dd") + ".csv", zipOut);
                        }
                    }
                    try (InputStream in = AppContext.getInstance(WechatService.class).getInsuranceBillStream(hospital, platform.getAppId(), date)) {
                        if (in != null) {
                            ZipUtil.writeFile(in, "微信医保/" + TimeUtils.dateToString(date, "yyyy-MM-dd") + ".csv", zipOut, true);
                        }
                    }
                }
            }
        } catch (IOException e) {
            response.setContentLengthLong(-1);
            log.error("文件输出失败", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("文件输出失败", e);
            response.setContentLengthLong(-1);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void downloadAliPayRawBills(HttpServletResponse response, Hospital hospital, Date startDate, Date endDate) {
        String appId = AppContext.getInstance(WechatService.class).getAppId(hospital, PlatformTypeEnum.ALI_PAY_MINI, HospitalPublicPlatform.PlatformForEnum.PATIENT);
        AlipayClient alipayClient = AppContext.getInstance(AliPayBusinessService.class).getAlipayClient(appId, hospital);
        String name = TimeUtils.dateToString(startDate, "yyyy-MM-dd") + "到" + TimeUtils.dateToString(endDate, "yyyy-MM-dd") + "支付宝账单信息.zip";
        try {
            name = URLEncoder.encode(name, StandardCharsets.UTF_8);
        } catch (Exception ex) {
            log.warn("文件名编码错误");
        }
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, String.format("%s;filename=%s", "attachment", name));

        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            if (alipayClient != null) {
                for (Date date : TimeUtils.getBetweenDates(startDate, endDate)) {
                    try (InputStream in = AppContext.getInstance(AliPayBusinessService.class).getTradeBillStream(alipayClient, date)) {
                        if (in != null) {
                            ZipUtil.writeFile(in, "支付宝/交易账单/" + TimeUtils.dateToString(date, "yyyy-MM-dd") + ".zip", zipOut);
                        }
                    }
                }
            }
        } catch (IOException e) {
            response.setContentLengthLong(-1);
            log.error("文件输出失败", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("文件输出失败", e);
            response.setContentLengthLong(-1);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void billReset(long billId) {
        Bill bill = billRepository.findById(billId).orElseThrow();
        BillChannelEnum billChannel = bill.getBillChannel();
        BillOperateTypeEnum billOperateType = bill.getBillOperateType();
        Boolean insuranceFlag = bill.getInsuranceFlag();
        if (BillChannelEnum.WECHAT.equals(billChannel) && BillOperateTypeEnum.PAYMENT.equals(billOperateType) & !insuranceFlag) {
            // 微信+支付+自费
            Optional<WechatOrder> wechatOrderOptional = wechatOrderRepository.findByTransactionId(bill.getTransactionId());
            wechatOrderOptional.ifPresent(wechatOrder -> {
                AppContext.getInstance(BillJobService.class).groupGenerateBill(List.of(wechatOrder));
                AppContext.getInstance(BillNewJobService.class).checkWeChatPayedBills(List.of(wechatOrder));
            });
            return;
        }
        if (BillChannelEnum.WECHAT.equals(billChannel) && BillOperateTypeEnum.REFUND.equals(billOperateType) & !insuranceFlag) {
            // 微信+退款+自费
            List<WechatOrderRefund> refundList = wechatOrderRefundRepository.findAllByTransactionId(bill.getTransactionId());
            if (CollectionUtil.isNotEmpty(refundList)) {
                AppContext.getInstance(BillJobService.class).groupGenerateRefundBill(refundList);
                AppContext.getInstance(BillNewJobService.class).checkWeChatRefundBills(refundList);
            }
            return;
        }
        if (BillChannelEnum.WECHAT.equals(billChannel) && BillOperateTypeEnum.PAYMENT.equals(billOperateType) & insuranceFlag) {
            // 微信+支付+医保
            Optional<WechatInsuranceOrder> wechatInsuranceOrderOptional = AppContext.getInstance(WechatInsuranceOrderRepository.class).findOneByHospOutTradeNo(bill.getMerchantOrderNumber());
            wechatInsuranceOrderOptional.ifPresent(wechatInsuranceOrder -> {
                AppContext.getInstance(BillInsuranceJobService.class).checkWeChatInsurancePayedBills(List.of(wechatInsuranceOrder));
                AppContext.getInstance(BillNewInsuranceJobService.class).checkWeChatInsurancePayedBills(List.of(wechatInsuranceOrder));
            });
            return;
        }
        if (BillChannelEnum.WECHAT.equals(billChannel) && BillOperateTypeEnum.REFUND.equals(billOperateType) & insuranceFlag) {
            // 微信+退款+医保
            Optional<WechatInsuranceOrderRefund> wechatInsuranceOrderRefundOptionalOptional = AppContext.getInstance(WechatInsuranceOrderRefundRepository.class).findOneByOutRefundNo(bill.getOutRefundNo());
            wechatInsuranceOrderRefundOptionalOptional.ifPresent(wechatInsuranceOrderRefund -> {
                AppContext.getInstance(BillInsuranceJobService.class).checkWeChatInsuranceRefundBills(List.of(wechatInsuranceOrderRefund));
                AppContext.getInstance(BillNewInsuranceJobService.class).checkWeChatInsuranceRefundBills(List.of(wechatInsuranceOrderRefund));
            });
            return;
        }
        if (BillChannelEnum.ALIPAY.equals(billChannel) && BillOperateTypeEnum.PAYMENT.equals(billOperateType)) {
            // 支付宝+支付+自费/医保
            Optional<AliPayOrder> aliPayOrderOptional = aliPayOrderRepository.findOneByOutTradeNo(bill.getMerchantOrderNumber());
            aliPayOrderOptional.ifPresent(aliPayOrder -> {
                AppContext.getInstance(BillJobService.class).groupGenerateAliPayBill(List.of(aliPayOrder));
                AppContext.getInstance(BillNewJobService.class).checkAliPayPayedBills(List.of(aliPayOrder));
            });
            return;
        }
        if (BillChannelEnum.ALIPAY.equals(billChannel) && BillOperateTypeEnum.PAYMENT.equals(billOperateType)) {
            // 支付宝+退款+自费/医保
            Optional<AliPayOrderRefund> aliPayOrderRefundOptional = aliPayOrderRefundRepository.findOneByRefundNo(bill.getOutRefundNo());
            aliPayOrderRefundOptional.ifPresent(aliPayOrderRefund -> {
                AppContext.getInstance(BillJobService.class).groupGenerateRefundAliPayBill(List.of(aliPayOrderRefund));
                AppContext.getInstance(BillNewJobService.class).checkAliPayRefundBills(List.of(aliPayOrderRefund));
            });
            return;
        }
        // 走到这里就是HIS单边账
        if (!insuranceFlag) {
            // 自费
            // 支付
            List<Specification<HisBill>> specs = Lists.newArrayList();
            specs.add(Specifications.eq("transaction_id", bill.getTransactionId()));
            List<HisBill> hisBillList = AppContext.getInstance(HisBillRepository.class).findAll(Specifications.and(specs));
            if (CollectionUtil.isNotEmpty(hisBillList)) {
                AppContext.getInstance(BillJobService.class).groupGenerateHisBill(hisBillList);
                AppContext.getInstance(BillNewJobService.class).checkHisBill(hisBillList);
            }
        } else {
            // 医保
            List<Specification<HisBill>> specs = Lists.newArrayList();
            specs.add(Specifications.eq("payOrderId", bill.getPayOrderId()));
            List<HisBill> hisBillList = AppContext.getInstance(HisBillRepository.class).findAll(Specifications.and(specs));
            if (CollectionUtil.isNotEmpty(hisBillList)) {
                AppContext.getInstance(BillInsuranceJobService.class).groupGenerateHisBill(hisBillList);
                AppContext.getInstance(BillNewInsuranceJobService.class).groupGenerateHisBill(hisBillList);
            }
        }
    }

    @Override
    public List<BillSummaryDifferenceVM> getSummaryDifference(Hospital hospital, Date startDate, Date endDate) {
        List<Specification<Bill>> billSpecs = Lists.newArrayList();
        billSpecs.add(Specifications.eq("hospital", hospital));
        billSpecs.add(Specifications.between("orderOperateTime", startDate, TimeUtils.getEndOfDay(endDate)));
        billSpecs.add(Specifications.or(getTypeSpec()));
        List<Bill> bills = billRepository.findAll(Specifications.and(billSpecs));

        List<Specification<HisBill>> hisBillSpecs = Lists.newArrayList();
        hisBillSpecs.add(Specifications.eq("hospital", hospital));
        hisBillSpecs.add(Specifications.between("transaction_time", startDate, TimeUtils.getEndOfDay(endDate)));
        List<HisBill> hisBills = hisBillRepository.findAll(Specifications.and(hisBillSpecs));

        // IH支付账单
        List<Bill> ihPayList = bills.stream().filter(b -> BillOperateTypeEnum.PAYMENT.equals(b.getBillOperateType())).collect(Collectors.toList());
        Map<String, List<Bill>> ihPayMap =ihPayList.stream().collect(Collectors.groupingBy(b -> BooleanUtils.isTrue(b.getInsuranceFlag()) ? b.getPayOrderId() : b.getTransactionId()));
        // IH退款账单
        List<Bill> ihRefundList = bills.stream().filter(b -> BillOperateTypeEnum.REFUND.equals(b.getBillOperateType())).collect(Collectors.toList());
        Map<String, List<Bill>> ihRefundMap = ihRefundList.stream().collect(Collectors.groupingBy(b -> BooleanUtils.isTrue(b.getInsuranceFlag()) ? b.getPayOrderId() : b.getTransactionId()));

        // HIS支付账单
        List<HisBill> hisPayList = hisBills.stream().filter(b -> "SUCCESS".equals(b.getTransaction_status()) && b.getOrder_amount() != 0).collect(Collectors.toList());
        Map<String, List<HisBill>> hisPayMap = hisPayList.stream().collect(Collectors.groupingBy(b -> BooleanUtils.isTrue(b.getInsuranceFlag()) ? b.getPayOrderId() : b.getTransaction_id()));

        // HIS退款账单
        List<HisBill> hisRefundList = hisBills.stream().filter(b -> "REFUND".equals(b.getTransaction_status()) && b.getRefund_amount() != 0).collect(Collectors.toList());
        Map<String, List<HisBill>> hisRefundMap = hisRefundList.stream().collect(Collectors.groupingBy(b -> BooleanUtils.isTrue(b.getInsuranceFlag()) ? b.getPayOrderId() : b.getTransaction_id()));

        // ih和his金额匹配不上的数据
        List<HisBill> notMatchList = new ArrayList<>();
        // IH有，HIS没有/都有但是金额对不上  支付ih一对多his
        List<Bill> onlyIHPay = ihPayList.stream().filter(b -> {
            String key = BooleanUtils.isTrue(b.getInsuranceFlag()) ? b.getPayOrderId() : b.getTransactionId();
            if (hisPayMap.containsKey(key)) {
                int sum = hisPayMap.get(key).stream().mapToInt(HisBill::getOrder_amount).sum();
                if (sum != b.getPaymentAmount()) {
                    notMatchList.addAll(hisPayMap.get(key));
                    return true;
                }
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        // IH没有HIS有 ,支付his多对一ih，无需判断金额，上一步已经判断过了
        List<HisBill> onlyHisPay = hisPayList.stream().filter(b -> {
            String key = BooleanUtils.isTrue(b.getInsuranceFlag()) ? b.getPayOrderId() : b.getTransaction_id();
            return !ihPayMap.containsKey(key);
        }).collect(Collectors.toList());

        // IH有，HIS没有，退款ih多对多his
        List<Bill> onlyIHRefund = ihRefundList.stream().filter(b -> {
            String key = BooleanUtils.isTrue(b.getInsuranceFlag()) ? b.getPayOrderId() : b.getTransactionId();
            if (hisRefundMap.containsKey(key)) {
                List<HisBill> hisBillList = hisRefundMap.get(key);
                if (hisBillList.size() > 1) {
                    // 如果有多条就找到对应的那条进行判断(部分退款)
                    Map<String, Integer> map = hisBillList.stream().collect(Collectors.toMap(HisBill::getSettleId, HisBill::getRefund_amount));
                    if (map.containsKey(b.getSettleId()) && Objects.equals(map.get(b.getSettleId()), b.getRefundAmount())) {
                        return false;
                    }
                } else {
                    if (b.getRefundAmount().equals(hisBillList.get(0).getRefund_amount())) {
                        return false;
                    }
                    notMatchList.add(hisBillList.get(0));
                }
            }
            return true;
        }).collect(Collectors.toList());
        // IH没有HIS有,退款 无需判断金额，上一步已经判断过了
        List<HisBill> onlyHisRefund = hisRefundList.stream().filter(b -> {
            String key = BooleanUtils.isTrue(b.getInsuranceFlag()) ? b.getPayOrderId() : b.getTransaction_id();
            if (ihRefundMap.containsKey(key)) {
                List<Bill> billList = ihRefundMap.get(key);
                if (billList.size() == 1) {
                    return false;
                }
                // 如果有多条就找到对应的那条进行判断(部分退款)
                List<String> settleIds = billList.stream().map(Bill::getSettleId).collect(Collectors.toList());
                if (settleIds.contains(b.getSettleId())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());

        // 去除IH结算失败进行退款的数据
        List<Bill> onlyIHPayTemp = new ArrayList<>(onlyIHPay);
        List<Bill> onlyIHRefundTemp = new ArrayList<>(onlyIHRefund);
        for (Bill b : onlyIHPayTemp) {
            onlyIHRefundTemp.stream()
                    .filter(rb -> rb.getTransactionId().equals(b.getTransactionId()) && rb.getRefundAmount().equals(b.getPaymentAmount()))
                    .findFirst()
                    .ifPresent(rb -> {
                        onlyIHPay.remove(b);
                        onlyIHRefund.remove(rb);
                    });
        }

        List<BillSummaryDifferenceVM> result = new ArrayList<>();

        List<BillSummaryDifferenceVM> onlyIHPayBill = onlyIHPay.stream()
                .map(BillSummaryDifferenceVM::new)
                .collect(Collectors.toList());
        result.addAll(onlyIHPayBill);

        List<BillSummaryDifferenceVM> onlyIHRefundBill = onlyIHRefund.stream()
                .map(BillSummaryDifferenceVM::new)
                .collect(Collectors.toList());
        result.addAll(onlyIHRefundBill);

        List<BillSummaryDifferenceVM> onlyHisPayBill = onlyHisPay.stream()
                .map(BillSummaryDifferenceVM::new)
                .collect(Collectors.toList());
        result.addAll(onlyHisPayBill);

        List<BillSummaryDifferenceVM> onlyHisRefundBill = onlyHisRefund.stream()
                .map(BillSummaryDifferenceVM::new)
                .collect(Collectors.toList());
        result.addAll(onlyHisRefundBill);

        List<BillSummaryDifferenceVM> notMatchBill = notMatchList.stream()
                .map(BillSummaryDifferenceVM::new)
                .collect(Collectors.toList());
        result.addAll(notMatchBill);

        return result;
    }

    @Override
    @Transactional
    public void flushReconciliation(Bill bill, BillFlushDTO billFlushDTO) {
        // 平账的账单，无法冲正
        if (bill.getReconciliationResult() != ReconciliationResultEnum.DOUBT) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("此账单正常，无法冲正");
        }
        ProjectTypeEnum serviceType = bill.getBillServiceType();
        if (serviceType == ProjectTypeEnum.ONLINE_PRESCRIPTION) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("线上处方，无法冲正");
        }
        Bill generatedBill = null;
        Specification<Bill> specification = Specifications.and(Specifications.eq("transactionId", bill.getTransactionId())
            , Specifications.eq("billOperateType", bill.getBillOperateType())
        );
        List<Bill> billList = billRepository.findAll(specification);
        if (CollectionUtil.isEmpty(billList)) {
            return;
        }

        if (billFlushDTO.getBalanceType() == BillBalanceTypeEnum.REFUND) { // 退款冲正平账
            // 先根据业务订单状态判断能不能退款
            switch (serviceType) {
                case APPOINTMENT_REGISTRATION:
                    String orderNo = bill.getOrderNo();
                    OfflineOrder offlineOrder = AppContext.getInstance(OfflineOrderRepository.class)
                        .getById(Long.parseLong(orderNo));
                    if (offlineOrder.getPayTime() == null) {
                        throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单对应的订单未支付，无法退款冲正");
                    }
                default:
            }
            //2024年12月13日 根据结算失败费用不回滚需求http://81.70.195.70:8911/zentao/story-view-8825.html
            if (BooleanUtil.isTrue(bill.getInsuranceFlag())) {
                // 医保订单退自费部分
                BillOperateTypeEnum operateType = bill.getBillOperateType();
                // ih总额
                Integer ihTotal = billList.stream().map(b -> BillOperateTypeEnum.PAYMENT == operateType ?
                    Optional.ofNullable(b.getSelfPaymentAmount()).orElse(0) : Optional.ofNullable(b.getRefundAmount())
                    .orElse(0)).reduce(Integer::sum).orElse(0);
                // his总额
                Integer hisTotal = billList.stream().map(b -> Optional.ofNullable(b.getHisSelfAmount()).orElse(0))
                    .reduce(Integer::sum).orElse(0);

                if ((ihTotal > hisTotal && bill.getBillOperateType() == BillOperateTypeEnum.PAYMENT)
                || (ihTotal < hisTotal && bill.getBillOperateType() == BillOperateTypeEnum.REFUND)) {
                    RefundOrderVM refundVM = new RefundOrderVM();
                    boolean refund;
                    Integer refundAmount = bill.getBillOperateType() == BillOperateTypeEnum.PAYMENT ?
                        (ihTotal - hisTotal) : (hisTotal - ihTotal);
                    // 根据支付渠道查询wechatorder 或 alipayorder
                    switch (bill.getBillChannel()) {
                        case WECHAT:
                            Optional<WechatInsuranceOrder> wechatOrderOptional =
                                wechatInsuranceOrderRepository.findOneByMedTransId(bill.getTransactionId());

                            if (wechatOrderOptional.isEmpty()) {
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单未找到对应的微信医保支付订单，无法退款冲正 ");
                            }
                            WechatInsuranceOrder wechatOrder = wechatOrderOptional.get();
                            log.info("冲正时微信医保订单支付时间：payorderId: {}, time: {}",wechatOrder.getPayOrderId(),
                                     wechatOrder.getPayTime());
                            if (wechatOrder.getPayTime() == null) {
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单对应的微信医保订单未支付，无法退款冲正 ");
                            }

                            refundVM.setId(Long.parseLong(wechatOrderOptional.get().getProductId()));
                            refundVM.setType(wechatOrderOptional.get().getWechatOrderType());
                            String cancelSettleId = "IH" + Holder.INSTANCE.nextId();
                            String cancelSerialNo = "IH" + Holder.INSTANCE.nextId();
                            refundVM.setCancelBillNo(cancelSettleId);
                            refundVM.setCancelSerialNo(cancelSerialNo);
                            refundVM.setPayOrdId(bill.getPayOrderId());
                            refundVM.setRefReason("冲正退款平账");
                            refundVM.setSelfRefundAmount(refundAmount);

                            // 医保这里只退现金部分
                            refundVM.setRefundType(InsurancePayMethod.CASH_ONLY);

                            log.info("微信医保退款开始，参数 {} 退款金额 {}，订单号 {}", JSONUtil.toJsonStr(refundVM), refundAmount,
                                     bill.getOrderNo());
                            refund = wechatService.insuranceRefund(bill.getHospital(), refundVM,
                                                          Lists.newArrayList(bill.getOrderNo()));
                            log.info("微信医保退款结束，返回结果 {}", refund);

                            // 生成新的冲正退款账单
                            generatedBill = generateFlushBillWx(bill);
                            break;
                        case ALIPAY:
                        default:
                            break;
                    }

                } else {
                    throw ErrorType.BAD_REQUEST_ERROR.toProblem("此账单金额异常，无法冲正");
                }

            } else {
                BillOperateTypeEnum operateType = bill.getBillOperateType();
                // ih总额
                Integer ihTotal = billList.stream().map(b -> BillOperateTypeEnum.PAYMENT == operateType ? Optional.ofNullable(b.getPaymentAmount()).orElse(0) : Optional.ofNullable(b.getRefundAmount())
                    .orElse(0)).reduce(Integer::sum).orElse(0);
                // his总额
                Integer hisTotal = billList.stream().map(b -> Optional.ofNullable(b.getHisAmount()).orElse(0))
                    .reduce(Integer::sum).orElse(0);

                // ih总额 >  his总额, 可以退款
                if ((ihTotal > hisTotal && bill.getBillOperateType() == BillOperateTypeEnum.PAYMENT)
                    || (ihTotal < hisTotal && bill.getBillOperateType() == BillOperateTypeEnum.REFUND)) {
                    RefundOrderVM refundVM = new RefundOrderVM();
                    boolean refund;
                    Integer refundAmount = bill.getBillOperateType() == BillOperateTypeEnum.PAYMENT ?
                        (ihTotal - hisTotal) : (hisTotal - ihTotal);
                    // 根据支付渠道查询wechatorder 或 alipayorder
                    switch (bill.getBillChannel()) {
                        case WECHAT:
                            Optional<WechatOrder> wechatOrderOptional =
                                wechatOrderRepository.findByTransactionId(bill.getTransactionId());

                            if (wechatOrderOptional.isEmpty()) {
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单未找到对应的微信支付订单，无法退款冲正 ");
                            }
                            WechatOrder wechatOrder = wechatOrderOptional.get();
                            log.info("冲正时微信订单支付时间：tid: {}, time: {}",wechatOrder.getTransactionId(),
                                     wechatOrder.getPayTime());
                            if (wechatOrder.getPayTime() == null) {
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单对应的微信订单未支付，无法退款冲正 ");
                            }

                            refundVM.setId(Long.parseLong(wechatOrderOptional.get().getProductId()));
                            refundVM.setType(wechatOrderOptional.get().getWechatOrderType());

                            log.info("微信退款开始，参数 {} 退款金额 {}，订单号 {}", JSONUtil.toJsonStr(refundVM), refundAmount,
                                     bill.getOrderNo());
                            refund = wechatService.refund(bill.getHospital(), refundVM, refundAmount,
                                                          Lists.newArrayList(bill.getOrderNo()));
                            log.info("微信退款结束，返回结果 {}", refund);

                            // 生成新的冲正退款账单
                            generatedBill = generateFlushBillWx(bill);
                            break;
                        case ALIPAY:
                            Optional<AliPayOrder> aliPayOrderOptional = aliPayOrderRepository.findOneByOutTradeNo(
                                bill.getMerchantOrderNumber());

                            if (aliPayOrderOptional.isEmpty()) {
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单未找到对应的支付宝支付订单 ");
                            }
                            AliPayOrder aliPayOrder = aliPayOrderOptional.get();
                            log.info("冲正时微信订单支付时间：tid: {}, time: {}",aliPayOrder.getTradeNo(),
                                     aliPayOrder.getGmtPayment());
                            if (aliPayOrder.getGmtPayment() == null) {
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单对应的支付宝订单未支付，无法退款冲正 ");
                            }
                            refundVM.setId(Long.parseLong(aliPayOrder.getBody()));
                            refundVM.setType(aliPayOrder.getOrderType());

                            log.info("支付宝退款开始，参数 {} 退款金额 {}，订单号 {}", JSONUtil.toJsonStr(refundVM), refundAmount,
                                     bill.getOrderNo());
                            refund = aliPayBusinessService.refund(bill.getHospital(), refundVM, refundAmount,
                                                                  bill.getOrderNo());
                            log.info("支付宝退款结束，返回结果 {}", refund);

                            // 生成新的冲正退款账单
                            generatedBill = generateFlushBillAli(bill);
                            break;
                        case WANDA:
                            Optional<WandaPayOrder> wandaPayOrderOptional = wandaPayOrderRepository.findOneByOrderNo(
                                bill.getTransactionId());

                            if (wandaPayOrderOptional.isEmpty()) {
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单未找到对应的万达链支付订单 ");
                            }
                            WandaPayOrder wandaPayOrder = wandaPayOrderOptional.get();
                            log.info("冲正时微信订单支付时间：tid: {}, time: {}",wandaPayOrder.getOrderNo(),
                                     wandaPayOrder.getPayTime());
                            if (wandaPayOrder.getPayTime() == null) {
                                throw ErrorType.BAD_REQUEST_ERROR.toProblem("该账单对应的万达链支付订单未支付，无法退款冲正 ");
                            }
                            refundVM.setId(Long.parseLong(wandaPayOrder.getBody()));
                            refundVM.setType(wandaPayOrder.getOrderType());

                            log.info("万达链支付退款开始，参数 {} 退款金额 {}，订单号 {}", JSONUtil.toJsonStr(refundVM), refundAmount,
                                     bill.getOrderNo());
                            refund = wandaPayBusinessService.refund(bill.getHospital(), refundVM, refundAmount,
                                                                  bill.getOrderNo());
                            log.info("万达链支付退款结束，返回结果 {}", refund);

                            // 生成新的冲正退款账单
                            generatedBill = generateFlushBillAli(bill);
                            break;
                        default:
                            break;
                    }

                } else {
                    throw ErrorType.BAD_REQUEST_ERROR.toProblem("此账单金额异常，无法冲正");
                }
            }
        }
        // 将一系列账单标记为已处理
        billList.forEach(b -> {
            b.setRemark(bill.getRemark());
            b.setSolveFlag(true);
            b.setReconciliationResult(ReconciliationResultEnum.BALANCING);
            billRepository.save(b);
            log.info("账单{}冲正处理完成", b.getId());
        });

        // 平账留痕
        BillBalanceRecord billBalanceRecord = new BillBalanceRecord();
        billBalanceRecord.setBalanceType(billFlushDTO.getBalanceType());
        billBalanceRecord.setTransactionId(bill.getTransactionId());
        billBalanceRecord.setRemark(billFlushDTO.getRemark());
        if (generatedBill != null) {
            String outRefundNo = generatedBill.getOutRefundNo();
            billBalanceRecord.setOutRefundNo(outRefundNo);
        }
        AppContext.getInstance(BillBalanceRecordRepository.class).save(billBalanceRecord);
    }

    private Bill generateFlushBillWx(Bill existBill) {
        List<WechatOrderRefund> wechatOrderRefunds =
            wechatOrderRefundRepository.findAllByOutTradeNo(existBill.getMerchantOrderNumber());

        // 获取最新的退款
        WechatOrderRefund newestRefund = wechatOrderRefunds.stream()
            .max(Comparator.comparing(UpdatableEntity::getCreatedDate)).orElse(null);


        if (newestRefund == null) {
            return null;
        }

        // 根据最新的退款生成账单

        Bill refundBill = BeanUtil.copyProperties(existBill, Bill.class, "id");
        refundBill.setBillOperateType(BillOperateTypeEnum.REFUND);
        refundBill.setRefundType(existBill.getBillOperateType() == BillOperateTypeEnum.PAYMENT ? BillRefundTypeEnum.PAYMENT : BillRefundTypeEnum.REFUND);
        refundBill.setPaymentAmount(null);
        refundBill.setPaymentTime(null);
        refundBill.setTransactionId(existBill.getTransactionId());
        refundBill.setBillPayStatus(BillPayStatusEnum.REFUNDED);
        refundBill.setBillOperateType(BillOperateTypeEnum.REFUND);
        refundBill.setRefundInitiationTime(new Date());
        refundBill.setHisAmount(null);
        refundBill.setRefundAmount(newestRefund.getAmount());
        refundBill.setOutRefundNo(newestRefund.getOutRefundNo());
        refundBill.setSolveFlag(true);
        refundBill.setReconciliationResult(ReconciliationResultEnum.BALANCING);
        refundBill.setCreatedDate(new Date());
        refundBill.setUpdatedDate(new Date());
        billRepository.save(refundBill);
        return refundBill;
    }

    private Bill generateFlushBillAli(Bill existBill) {
        List<AliPayOrderRefund> wechatOrderRefunds =
            aliPayOrderRefundRepository.findAllByOutTradeNo(existBill.getMerchantOrderNumber());

        // 获取最新的退款
        AliPayOrderRefund newestRefund = wechatOrderRefunds.stream()
            .max(Comparator.comparing(UpdatableEntity::getCreatedDate)).orElse(null);


        if (newestRefund == null) {
            return null;
        }

        // 根据最新的退款生成账单

        Bill refundBill = BeanUtil.copyProperties(existBill, Bill.class, "id");
        refundBill.setBillOperateType(BillOperateTypeEnum.REFUND);
        refundBill.setRefundType(existBill.getBillOperateType() == BillOperateTypeEnum.PAYMENT ? BillRefundTypeEnum.PAYMENT : BillRefundTypeEnum.REFUND);
        refundBill.setPaymentAmount(null);
        refundBill.setPaymentTime(null);
        refundBill.setTransactionId(existBill.getTransactionId());
        refundBill.setBillPayStatus(BillPayStatusEnum.REFUNDED);
        refundBill.setBillOperateType(BillOperateTypeEnum.REFUND);
        refundBill.setRefundInitiationTime(new Date());
        refundBill.setHisAmount(null);
        refundBill.setRefundAmount(newestRefund.getAmount());
        refundBill.setOutRefundNo(newestRefund.getRefundNo());
        refundBill.setSolveFlag(true);
        refundBill.setReconciliationResult(ReconciliationResultEnum.BALANCING);
        refundBill.setCreatedDate(new Date());
        refundBill.setUpdatedDate(new Date());
        billRepository.save(refundBill);
        return refundBill;
    }
}
