package cn.taihealth.ih.service.impl;

import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.ElectronicHealthCard;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.UploadType;
import cn.taihealth.ih.domain.utils.HealthCardUtils;
import cn.taihealth.ih.repo.ElectronicMedicCardRepository;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.hospital.ElectronicHealthCardRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.dto.ElectronicHealthCardDTO;
import cn.taihealth.ih.service.dto.ElectronicMedicCardDTO;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.electronichealthcard.ElectronicHealthCardUtil;
import cn.taihealth.ih.service.vm.ElectronicMedicCardVM;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.electronichealthcard.ElectronicHealthCardInfo;
import cn.taihealth.ih.service.vm.electronichealthcard.rep.AddHealthCardParams;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.MedicalCardInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.Lists;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> jzs
 * @Date : 2024-06-17
 */
@Service
@AllArgsConstructor
@Slf4j
public class ElectronicHealthCardServiceImpl implements ElectronicHealthCardService {

    private ElectronicHealthCardRepository electronicHealthCardRepository;
    private PatientService patientService;
    private PatientRepository patientRepository;
    private ElectronicMedicCardRepository electronicMedicCardRepository;
    private final PatientManager patientManager;
    private final LockService lockService;
    private final ApplicationProperties applicationProperties;

    private String userHealthCardLock(User user) {
        return "lock.user.healthCard.lock.userId." + user.getId();
    }

    @Override
    @Transactional
    public void addElectronicHealthCard(Hospital hospital, User user, AddHealthCardParams addHealthCardParams) {
        lockService.executeWithLock(userHealthCardLock(user), () -> {
            List<ElectronicHealthCardInfo> electronicHealthCardInfoList = ElectronicHealthCardUtil
                    .getUserInfoByUserCode(hospital, addHealthCardParams);
            // 获取当前用户的所有就诊人
            List<Patient> patients = patientRepository.findAllByUserAndHospitalAndEnabled(user, hospital, true);
            // 因为注册电子健康卡跳转到宜建通小程序可以可以对电子健康卡逻辑删除，所以获取健康卡信息的时候需要确定具体哪些卡已经在宜建通那边进行了逻辑删除
            if (CollectionUtils.isNotEmpty(electronicHealthCardInfoList)) {
                List<ElectronicHealthCard> healthCards = electronicHealthCardRepository.findAllByUser(user);
                electronicHealthCardInfoList.forEach(electronicHealthCardInfo -> {
                    // 健康卡id一样的
                    Optional<ElectronicHealthCard> electronicHealthCardOptional = healthCards.stream()
                            .filter(u -> Objects.equals(u.getHealthCardId(), electronicHealthCardInfo.getHealthCardId()))
                            .findFirst();
                    // 证件号一样的
                    Optional<ElectronicHealthCard> healthCardOIdNo = healthCards.stream()
                            .filter(u -> Objects.equals(u.getIdNumber(), electronicHealthCardInfo.getIdNumber()))
                            .findFirst();
                    // 如果证件号相同，健康卡id不同，说明有多张健康卡证件号相同的卡，这里当做更换健康卡处理，将旧的健康卡删除
                    if (healthCardOIdNo.isPresent() && !Objects.equals(healthCardOIdNo.get().getHealthCardId(), electronicHealthCardInfo.getHealthCardId())) {
                        ElectronicHealthCard deleteCard = healthCardOIdNo.get();
                        deleteCard.setYjtDeleted(true);
                        electronicHealthCardRepository.save(deleteCard);
                    }
                    if (electronicHealthCardOptional.isEmpty()) {
                        // 更新就诊人(为了兼容老数据，有就创建没有就更新)
                        Patient patient = patientService.addPatientByElectronicHealthCard(hospital, user, electronicHealthCardInfo);
                        patients.removeIf(currentPatient -> Objects.equals(currentPatient.getId(), patient.getId()));
                        ElectronicHealthCard entity = toEntity(electronicHealthCardInfo, user, patient);
                        boolean enabled = HospitalSettingsHelper.getBoolean(patient.getHospital(), HospitalSettingKey.HIS_SYNC_PATIENT_CARD);
                        if (!enabled) {
                            List<ElectronicMedicCard> electronicMedicCards = patient.getElectronicMedicCards()
                                    .stream()
                                    .filter(card -> ElectronicMedicCard.OnlineType.IH.equals(card.getOnlineType()))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(electronicMedicCards)) {
                                throw ErrorType.CARD_EXISTED.toProblem();
                            }
                            // 查看当前就诊人是否在ih内有就诊卡
                            List<ElectronicMedicCard> electronicMedicCardList = electronicMedicCardRepository
                                    .findAllByOnlineTypeAndPatient(ElectronicMedicCard.OnlineType.IH, patient);
                            if (CollectionUtils.isEmpty(electronicMedicCardList)) {
                                BusinessServiceStrategy.getInstance().getStrategy(false).createMedicalCard(hospital, patient);
                            }
                        } else {
                            // 查看当前就诊人是否有就诊卡
                            List<ElectronicMedicCard> electronicMedicCardList = electronicMedicCardRepository
                                    .findAllByOnlineTypeAndPatient(ElectronicMedicCard.OnlineType.HIS, patient);
                            electronicMedicCardList = electronicMedicCardList.stream().filter(u -> u.getCardType() == ElectronicMedicCard.CardType.SELF_PAY)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(electronicMedicCardList)) {
                                // 通过his根据证件号码查询门诊患者基本信息
                                BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
                                List<ElectronicMedicCardDTO> cardList = businessService.getElectronicMedicCard(patient, hospital);
                                cardList = cardList.stream().filter(u -> u.getCardType() == ElectronicMedicCard.CardType.SELF_PAY)
                                        .collect(Collectors.toList());

                                if (CollectionUtils.isEmpty(cardList)) {
                                    // 通过his创建就诊卡
                                    MedicalCardInfo medicalCard = BusinessServiceStrategy.getInstance().getStrategy(true)
                                            .createMedicalCard(hospital, patient);
                                    if (!"0".equals(medicalCard.getCardtype())) {
                                        throw ErrorType.ILLEGAL_PARAMS.toProblem("健康卡不支持绑定医保卡");
                                    }
                                    if (StringUtils.isNotBlank(medicalCard.getPatid())) {
                                        patient.setHisPatid(medicalCard.getPatid());
                                        // 将院内ID赋值给电子健康卡
                                        entity.setPatid(medicalCard.getPatid());
                                        patientRepository.save(patient);
                                    }
                                    // 创建电子健康卡，此时卡是灰色的
                                    ElectronicHealthCard electronicHealthCard = electronicHealthCardRepository.save(entity);
                                    // 将就诊卡入库，此时就诊卡尚未启用
                                    ElectronicMedicCardVM vm = new ElectronicMedicCardVM(medicalCard);
                                    Optional<ElectronicMedicCard> byNumberAndPatient = electronicMedicCardRepository
                                            .findByNumberAndCardTypeAndPatient(vm.getNumber(), vm.getCardType(), patient);
                                    if (byNumberAndPatient.isPresent() && byNumberAndPatient.get().isEnabled()) {
                                        throw ErrorType.CARD_ALREADY_BOUND.toProblem();
                                    }
                                    ElectronicMedicCard electronicMedicCard = patientService.bindElectronicMedicCard(patient, vm, hospital, false);
                                    // 将电子健康卡关联上就诊卡
                                    electronicMedicCard.setElectronicHealthCard(electronicHealthCard);
                                    electronicMedicCard.setHisPatid(medicalCard.getPatid());
                                    electronicMedicCardRepository.save(electronicMedicCard);
                                } else {
                                    entity.setPatid(cardList.get(0).getHisPid());
                                    // 创建电子健康卡，此时卡是灰色的
                                    ElectronicHealthCard electronicHealthCard = electronicHealthCardRepository.save(entity);
                                    // 说明是多个用户创建了相同身份证号的就诊人，在his内已经存在了就诊卡，需要在我侧创建就诊卡，并且将就诊卡与电子健康卡关联起来
                                    List<ElectronicMedicCard> electronicMedicCards = Lists.newArrayList();
                                    for (ElectronicMedicCardDTO electronicMedicCardDTO : cardList) {
                                        ElectronicMedicCard card = new ElectronicMedicCard();
                                        card.setNumber(electronicMedicCardDTO.getNumber());
                                        card.setPatient(patient);
                                        card.setCardType(electronicMedicCardDTO.getCardType());
                                        card.setEnabled(electronicHealthCard.isBind());
                                        card.setOnlineType(ElectronicMedicCard.OnlineType.HIS);
                                        // 二维码
                                        Upload upload = patientService.generateQrCode(card.getNumber(), Color.BLACK.getRGB(), Color.WHITE.getRGB());
                                        if (upload != null) {
                                            card.setDiagnosisCode(upload);
                                            electronicMedicCardDTO.setDiagnosisCode(new UploadVM(upload));
                                        }
                                        // 条形码
                                        Upload uploadBar = patientService.generateQrCode(electronicMedicCardDTO.getHisPid(), Color.BLACK.getRGB(),
                                                Color.WHITE.getRGB(), BarcodeFormat.CODE_128);
                                        if (uploadBar != null) {
                                            card.setDiagnosisBarCode(uploadBar);
                                            electronicMedicCardDTO.setDiagnosisBarCode(new UploadVM(uploadBar));
                                        }
                                        card.setHisPatid(electronicMedicCardDTO.getHisPid());
                                        card.setElectronicHealthCard(electronicHealthCard);
                                        electronicMedicCards.add(card);
                                    }
                                    electronicMedicCardRepository.saveAll(electronicMedicCards);
                                }
                            } else {
                                // 将院内ID赋值给电子健康卡
                                List<ElectronicMedicCard> collect = electronicMedicCardList.stream()
                                        .filter(c -> StringUtils.isNotBlank(c.getHisPatid()) && c.isEnabled())
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(collect)) {
                                    collect = electronicMedicCardList.stream()
                                            .filter(c -> StringUtils.isNotBlank(c.getHisPatid()))
                                            .collect(Collectors.toList());
                                }
                                if (CollectionUtils.isNotEmpty(collect)) {
                                    entity.setPatid(collect.get(0).getHisPatid());
                                }
                                // 创建电子健康卡，此时卡是灰色的
                                ElectronicHealthCard electronicHealthCard = electronicHealthCardRepository.save(entity);
                                for (ElectronicMedicCard electronicMedicCard : electronicMedicCardList) {
                                    electronicMedicCard.setElectronicHealthCard(electronicHealthCard);
                                }
                                // 将电子健康卡与就诊卡关联
                                electronicMedicCardRepository.saveAll(electronicMedicCardList);
                            }
                        }
                    } else {
                        // 之前已注册过，又修改了电子健康卡的部分信息或者已经逻辑删除了电子健康卡
                        ElectronicHealthCard electronicHealthCard = electronicHealthCardOptional.get();
                        Patient patient = electronicHealthCard.getPatient();
                        patients.removeIf(currentPatient -> Objects.equals(currentPatient.getId(), patient.getId()));
                        if (electronicHealthCard.isYjtDeleted()) {
                            // 重新激活电子健康卡
                            electronicHealthCard.setYjtDeleted(false);
                        }
                        if (!patient.isEnabled()) {
                            patient.setEnabled(true);
                            patientRepository.save(patient);
                        }
                        // 修改就诊人的关系
                        if (StringUtils.isNotBlank(electronicHealthCardInfo.getRelation())) {
                            patient.setRelationship(HealthCardUtils.getRelationship(electronicHealthCardInfo.getRelation()));
                            patientRepository.save(patient);
                        }
                        // 患者有可能会改身份证的姓名，同样也可能会在宜健通上更新电子健康卡姓名
                        electronicHealthCard.setName(electronicHealthCardInfo.getName());
                        if (!patient.getName().equals(electronicHealthCardInfo.getName())) {
                            patient.setName(electronicHealthCardInfo.getName());
                            patientRepository.save(patient);
                        }
                        if (StringUtils.isNotBlank(electronicHealthCardInfo.getAddress())) {
                            electronicHealthCard.setAddress(electronicHealthCardInfo.getAddress());
                        }
                        if (StringUtils.isNotBlank(electronicHealthCardInfo.getExt())) {
                            ElectronicHealthCardInfo.Ext ext = StandardObjectMapper
                                    .readValue(electronicHealthCardInfo.getExt(), new TypeReference<>() {
                                    });
                            if (ext != null) {
                                ElectronicHealthCardInfo.AddressInfo addressInfo = ext.getAddressInfo();
                                if (addressInfo != null) {
                                    electronicHealthCard.setProvince(addressInfo.getProvince());
                                    electronicHealthCard.setCity(addressInfo.getCity());
                                    electronicHealthCard.setDistrict(addressInfo.getDistrict());
                                    electronicHealthCard.setAddressCode(addressInfo.getAddressCode());
                                }
                            }
                        }
                        electronicHealthCardRepository.save(electronicHealthCard);
                        // 2024年07月10日，因norris要求，在宜健通解绑了的电子健康卡，就诊卡需要解绑，
                        // 这个时候，如果在宜健通重新绑定了健康卡，需要将就诊卡重新绑定
                        // 问题
                        // 用户a在A微信上登录，绑定他的电子就诊卡，显示卡1，用户a在B微信上登录，绑定他的电子就诊卡，显示卡2，用户a重新在A微信上登录，需要显示卡1，希望是绑定状态（这个状态处理逻辑存在问题）
                        // 目前区分不出来是用户手动解绑的，还是由于切换微信导致的解绑，因此在宜健通上重新绑定后，不进行自动绑定
                        // 切换微信导致获取到的健康卡不同，和直接在宜健通上进行绑定解绑健康卡导致的健康卡不同，这2中情况无法区分
                    }
                });
                deleteHealthCard(patients);
            } else {
                deleteHealthCard(patients);
            }
            return 0;
        });
    }

    // 逻辑删除电子健康卡，就诊人管理中还会显示，解绑之后不会显示出来
    private void deleteHealthCard(List<Patient> patients) {
        if (CollectionUtils.isNotEmpty(patients)) {
            List<ElectronicHealthCard> healthCardList = Lists.newArrayList();
            List<ElectronicMedicCard> electronicMedicCards = Lists.newArrayList();
            // 电子健康卡是否在宜建通那边已经逻辑删除了
            for (Patient patient : patients) {
                List<ElectronicHealthCard> cards = electronicHealthCardRepository.findByPatient(patient);
                healthCardList.addAll(cards.stream().filter(u -> !u.isYjtDeleted())
                        .peek(u -> u.setYjtDeleted(true)).collect(Collectors.toList()));
                electronicMedicCards.addAll(patient.getElectronicMedicCards()
                        .stream().filter(u -> u.getCardType() == ElectronicMedicCard.CardType.SELF_PAY)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(healthCardList)) {
                electronicHealthCardRepository.saveAll(healthCardList);
            }
            if (!electronicMedicCards.isEmpty()) {
                // 2024年07月10日，因norris要求，在宜健通解绑了的电子健康卡，就诊卡需要解绑
                electronicMedicCards.forEach(u -> u.setEnabled(false));
                electronicMedicCardRepository.saveAll(electronicMedicCards);
            }
            // 保证电子健康卡和就诊人数据一致
            patients.forEach(u -> u.setEnabled(false));
            patients.forEach(u -> log.info("就诊人enabled更新为false card_no:{} name: {}", u.getIdCardNum(), u.getName()));
            patientRepository.saveAll(patients);
        }
    }

    public ElectronicHealthCard toEntity(ElectronicHealthCardInfo healthCardInfo, User user, Patient patient) {
        ElectronicHealthCard healthCard = new ElectronicHealthCard();
        healthCard.setUser(user);
        healthCard.setPatient(patient);
        healthCard.setQrCodeText(healthCardInfo.getQrCodeText());
        // 生成电子健康卡二维码
        Upload upload = generateQrCode(healthCardInfo.getQrCodeText(), Color.BLACK.getRGB(), Color.WHITE.getRGB());
        if (upload != null) {
            healthCard.setDiagnosisCode(upload);
        }
        healthCard.setName(healthCardInfo.getName());
        healthCard.setGender(healthCardInfo.getGender());
        healthCard.setNation(healthCardInfo.getNation());
        if (StringUtils.isNotBlank(healthCardInfo.getBirthday())) {
            healthCard.setBirthday(healthCardInfo.getBirthday());
        }
        healthCard.setIdNumber(healthCardInfo.getIdNumber());
        healthCard.setIdType(healthCardInfo.getIdType());
        if (StringUtils.isNotBlank(healthCardInfo.getAddress())) {
            healthCard.setAddress(healthCardInfo.getAddress());
        }
        healthCard.setPhone1(healthCardInfo.getPhone1());
        if (StringUtils.isNotBlank(healthCardInfo.getPhone2())) {
            healthCard.setPhone2(healthCardInfo.getPhone2());
        }
        healthCard.setPhid(healthCardInfo.getPhid());
        if (StringUtils.isNotBlank(healthCardInfo.getPatid())) {
            healthCard.setPatid(healthCardInfo.getPatid());
        }
        healthCard.setHealthCardId(healthCardInfo.getHealthCardId());
        ElectronicHealthCardInfo.ChildInfo childInfo = healthCardInfo.getChildInfo();
        if (childInfo != null) {
            healthCard.setFatherPhone(childInfo.getFatherPhone());
            healthCard.setMotherNation(childInfo.getMotherNation());
            healthCard.setFatherBirthDay(childInfo.getFatherBirthDay());
            healthCard.setFatherName(childInfo.getFatherName());
            healthCard.setFatherCountry(childInfo.getFatherCountry());
            healthCard.setMotherBirthDay(childInfo.getMotherBirthDay());
            healthCard.setMotherCountry(childInfo.getMotherCountry());
            healthCard.setMotherName(childInfo.getMotherName());
            healthCard.setMotherPhone(childInfo.getMotherPhone());
            healthCard.setBirthTime(childInfo.getBirthTime());
            healthCard.setFatherAddress(childInfo.getFatherAddress());
            healthCard.setParity(childInfo.getParity());
            healthCard.setFatherIdNumber(childInfo.getFatherIdNumber());
            healthCard.setMotherIdNumber(childInfo.getMotherIdNumber());
            healthCard.setMotherAddress(childInfo.getMotherAddress());
            healthCard.setMotherIdType(childInfo.getMotherIdType());
            healthCard.setMultipleBirthsFlag(childInfo.getMultipleBirthsFlag());
            healthCard.setFatherIdType(childInfo.getFatherIdType());
            healthCard.setFatherNation(childInfo.getFatherNation());
        }
        if (StringUtils.isNotBlank(healthCardInfo.getExt())) {
            ElectronicHealthCardInfo.Ext ext = StandardObjectMapper.readValue(healthCardInfo.getExt(), new TypeReference<>() {});
            if (ext != null) {
                healthCard.setMarry(ext.getMarry());
                healthCard.setRelationship(ext.getRelationship());
                healthCard.setCareer(ext.getCareer());
                healthCard.setEducation(ext.getEducation());
                healthCard.setNationality(ext.getNationality());
                ElectronicHealthCardInfo.AddressInfo addressInfo = ext.getAddressInfo();
                if (addressInfo != null) {
                    healthCard.setProvince(addressInfo.getProvince());
                    healthCard.setCity(addressInfo.getCity());
                    healthCard.setDistrict(addressInfo.getDistrict());
                    healthCard.setAddressCode(addressInfo.getAddressCode());
                }
                healthCard.setEmergencyContactName(ext.getEmergencyContactName());
                healthCard.setEmergencyContactPhone(ext.getEmergencyContactPhone());
                healthCard.setEmergencyContactRelation(ext.getEmergencyContactRelation());
            }
        }
        return healthCard;
    }

    @Override
    @Transactional
    public ElectronicHealthCardDTO bindElectronicHealthCard(User user, String healthCardId) {
        List<ElectronicHealthCard> cards = electronicHealthCardRepository.findByUserAndHealthCardId(user, healthCardId);
        ElectronicHealthCard electronicHealthCard = cards.stream().filter(u -> !u.isYjtDeleted()).findFirst().orElse(null);
        if (electronicHealthCard == null) {
            electronicHealthCard = cards.stream().findFirst().orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);
        }
        if (!electronicHealthCard.isBind() || !electronicHealthCard.isBound()) {
            Hospital hospital = electronicHealthCard.getPatient().getHospital();
            boolean bindResult = ElectronicHealthCardUtil.bindElectronicHealthCard(hospital, electronicHealthCard);
            if (bindResult) {
                electronicHealthCard.setBind(true);
                electronicHealthCard.setBound(true);
                electronicHealthCardRepository.save(electronicHealthCard);
            }
        }
        String patId = electronicHealthCard.getPatid();
        // 将关联的电子就诊卡与院内id相同的hisPid激活
        ElectronicMedicCard medicCard = electronicHealthCard.getElectronicMedicCardList()
                .stream()
                .filter(u ->  u.isEnabled() && u.getHisPatid().equals(patId)
                        && u.getCardType() == ElectronicMedicCard.CardType.SELF_PAY)
                .findFirst().orElse(null);
        if (medicCard == null) {
            // his会出现一个pid修改了卡号的情况，具体原因位置，现象是pid相同卡号不同，如果这里disabled了，无法判断出来要绑哪一个
            medicCard = electronicHealthCard.getElectronicMedicCardList()
                    .stream()
                    .filter(u -> u.getHisPatid().equals(patId) && u.getCardType() == ElectronicMedicCard.CardType.SELF_PAY)
                    .findFirst().orElse(null);
        }
        if (medicCard != null) {
            medicCard.setEnabled(true);
            electronicMedicCardRepository.save(medicCard);
        }
        return new ElectronicHealthCardDTO(electronicHealthCard);
    }

    @Override
    @Transactional
    public void unBindElectronicHealthCard(User user, Patient patient, String healthCardId) {
        lockService.executeWithLock(patientLock(patient), () -> {
            List<ElectronicHealthCard> cards = electronicHealthCardRepository.findByUserAndHealthCardId(user, healthCardId);
            ElectronicHealthCard electronicHealthCard = cards.stream().filter(u -> !u.isYjtDeleted()).findFirst().orElse(null);
            if (electronicHealthCard == null) {
                electronicHealthCard = cards.stream().findFirst().orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);
            }
            if (!electronicHealthCard.isBind()) {
                return 0;
            }
            patientManager.turnEnableElectronicHealthCard(electronicHealthCard, false);
            return 0;
        });
    }

    private String patientLock(Patient patient) {
        return "lock.patient." + patient.getId();
    }

    // 根据需求生成特定的电子健康卡二维码
    private Upload generateQrCode(String content, int color, int background) {
        if (StringUtils.isBlank(content)) {
            log.error("内容为空，无法生成二维码");
            return null;
        }
        int width = 300;
        int height = 300;
        File outputFile = null;
        try {
            User system = AppContext.getInstance(UserService.class).getSystem();
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);
            hints.put(EncodeHintType.MARGIN, 0);
            BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    image.setRGB(x, y, bitMatrix.get(x, y) ? color : background);
                }
            }
            String uuid = UUID.randomUUID().toString().replace("-", "");
            outputFile = new File(applicationProperties.getHome(), "temp/" + content + uuid + "_二维码" + ".png");
            FileUtils.forceMkdir(outputFile);
            ImageIO.write(image, "png", outputFile);
            return AppContext.getInstance(UploadService.class).upload(system, UploadResource.of(outputFile, UploadType.PUBLIC, null));
        } catch (Exception e) {
            throw ErrorType.CARD_NUMBER_TO_QRCODE_ERROR.toProblem();
        } finally {
            if (outputFile != null) {
                outputFile.delete();
            }
        }
    }

}
