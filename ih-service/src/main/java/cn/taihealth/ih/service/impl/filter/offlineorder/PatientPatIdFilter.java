package cn.taihealth.ih.service.impl.filter.offlineorder;

import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

public class PatientPatIdFilter implements SearchFilter<OfflineOrder> {
    private final String value;

    public PatientPatIdFilter(String value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OfflineOrder> toSpecification() {
        return Specifications.eq("electronicMedicCard.hisPatid", value);
    }

    @Override
    public String toExpression() {
        String str = " value:" + value;
        return str;
    }

    @Override
    public boolean isValid() {
        return value != null;
    }
}
