package cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment;

import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.UserMedicalRecordCopyAppointmentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.domain.Page;

@Data
public class CopyAppointmentAccountCheckVM {
    @ApiModelProperty("订单数")
    private long orderTotal;
    @ApiModelProperty("营收总收入")
    private long revenueTotal;
    @ApiModelProperty("退款总额")
    private long refundTotal;
    @ApiModelProperty("支付总额")
    private long paidTotal;
    @ApiModelProperty("差额")
    private long diffTotal;

    @ApiModelProperty("列表对应的数据")
    private Page<UserMedicalRecordCopyAppointmentDTO> page;
}
