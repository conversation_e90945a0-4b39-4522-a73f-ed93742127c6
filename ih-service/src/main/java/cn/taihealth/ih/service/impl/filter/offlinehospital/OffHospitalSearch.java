package cn.taihealth.ih.service.impl.filter.offlinehospital;

import cn.taihealth.ih.commons.util.StringUtil;
import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.crm.Province;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.IdFilter;
import cn.taihealth.ih.service.impl.filter.Logic;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 */
public class OffHospitalSearch extends SearchCriteria<OfflineHospital> implements HospitalFilter {

    private RecommendFilter recommendFilter;

    public static OffHospitalSearch of(String query) {
        OffHospitalSearch search = new OffHospitalSearch();
        search.parse(query);
        return search;
    }

    @Override
    protected List<String> getQualifiers() {
        return Lists.newArrayList(Qualifier.values())
            .stream()
            .map(q -> q.name().toLowerCase())
            .collect(Collectors.toList());
    }

    @Override
    protected SearchFilter<OfflineHospital> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        boolean not = input.startsWith("-");
        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }

        switch (qualifier) {
            case ID:
                return new IdFilter<>(Long.valueOf(value));

            case CAPABILITY:
                return new CapabilityFilter(OfflineHospital.Capability.fromName(value));

            case PROVINCE:
                return new ProvinceFilter(Province.fromFullName(value));

            case DEPARTMENT:
                return new DepartmentFilter(value);

            case LEVEL:
                return new LevelFilter(value);

            case RANK:
                return new RankFilter(OfflineHospital.Rank.valueOf(value.toUpperCase()));

            case GRADE:
                return new GradeFilter(OfflineHospital.Grade.valueOf(value.toUpperCase()));

            case CITY:
                return new CityFilter(value);

            case DISTRICT:
                return new DistrictFilter(value);
            case NAME:
                return new NameFilter(value);
            case ENABLED:
                return new GenericFilter<>("enabled", Operator.eq, StringUtil.stringToBoolean(value));
            case RECOMMENDED:
                this.recommendFilter = new RecommendFilter(StringUtil.stringToBoolean(value));
                return recommendFilter;
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<OfflineHospital> createContainFilter(Set<String> contains) {
//        return new ContainsFilter<>(ImmutableSet.of("name", "alias"), contains);
        return null;
    }

    @Override
    public boolean matches(OfflineHospital offlineHospital) {
        Logic logic = getLogic();

        Boolean b = null;
        for (SearchFilter each : getFilters()) {
            HospitalFilter filter = (HospitalFilter) each;
            if (b == null) {
                b = filter.matches(offlineHospital);
            } else {
                b = logic == Logic.AND ?
                    b & filter.matches(offlineHospital) :
                    b || filter.matches(offlineHospital);
            }
        }

        return b == null ? true : b;
    }

    public RecommendFilter getRecommendFilter() {
        return recommendFilter;
    }

    public enum Qualifier {
        ID,
        CAPABILITY,
        PROVINCE,
        LEVEL,
        DEPARTMENT,
        GRADE,
        RANK,
        CITY,
        DISTRICT,
        NAME,
        ENABLED,
        // 是否推荐
        RECOMMENDED
    }
}
