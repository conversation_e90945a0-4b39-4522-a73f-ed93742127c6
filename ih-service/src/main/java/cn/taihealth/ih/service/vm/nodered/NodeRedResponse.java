package cn.taihealth.ih.service.vm.nodered;

import lombok.Data;

@Data
public class NodeRedResponse<T> implements java.io.Serializable {

    // 0	调用成功
    //4xx	客户端错误
    //5xx	服务器错误
    //100x	业务逻辑错误
    private String code;
    private String msg;
    private NodeRedResponseData<T> data;
    private String message;
    public boolean isSuccess() {
        return "0".equals(code);
    }

    public NodeRedResponse() {
    }


    public NodeRedResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public NodeRedResponse(String code, String msg, NodeRedResponseData<T> data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

}
