package cn.taihealth.ih.service.impl.ai.dify;

import cn.taihealth.ih.commons.error.ErrorType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class DifyClientFactory {

    private final Map<String, DifyClient> clientCache = new ConcurrentHashMap<>();

    private final String difyUrl;

    public DifyClientFactory(@Value("${dify.base-url:}") String difyUrl) {
        this.difyUrl = difyUrl;
    }

    public DifyClient createClient(String apiKey) {
        if (StringUtils.isEmpty(apiKey)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("Dify ApiKey未配置");
        }
        return clientCache.computeIfAbsent(apiKey, key -> {
            return new DifyClient(apiKey, difyUrl);
        });
    }
}
