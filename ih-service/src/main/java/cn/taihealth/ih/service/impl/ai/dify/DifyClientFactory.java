package cn.taihealth.ih.service.impl.ai.dify;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.DifyAppConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class DifyClientFactory {

    private final Map<String, DifyClient> clientCache = new ConcurrentHashMap<>();

    private final String difyUrl;

    public DifyClientFactory(@Value("${dify.base-url:}") String difyUrl) {
        this.difyUrl = difyUrl;
    }

    /**
     * 根据API Key创建客户端（使用默认URL）
     *
     * @param apiKey API密钥
     * @return DifyClient实例
     */
    public DifyClient createClient(String apiKey) {
        if (StringUtils.isEmpty(apiKey)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("Dify ApiKey未配置");
        }
        return clientCache.computeIfAbsent(apiKey, key -> {
            return new DifyClient(apiKey, difyUrl);
        });
    }

    /**
     * 根据DifyAppConfig创建客户端
     *
     * @param config Dify应用配置
     * @return DifyClient实例
     */
    public DifyClient createClient(DifyAppConfig config) {
        if (config == null) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("Dify应用配置未找到");
        }
        if (StringUtils.isEmpty(config.getApiKey())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("Dify ApiKey未配置");
        }
        if (StringUtils.isEmpty(config.getDifyUrl())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("Dify URL未配置");
        }

        // 使用appId+apiKey作为缓存key，确保不同应用的客户端不会冲突
        String cacheKey = config.getAppId() + ":" + config.getApiKey();
        return clientCache.computeIfAbsent(cacheKey, key -> {
            return new DifyClient(config.getApiKey(), config.getDifyUrl());
        });
    }

    /**
     * 清除客户端缓存
     *
     * @param apiKey API密钥
     */
    public void evictClient(String apiKey) {
        clientCache.remove(apiKey);
    }

    /**
     * 清除指定应用的客户端缓存
     *
     * @param config Dify应用配置
     */
    public void evictClient(DifyAppConfig config) {
        if (config != null && StringUtils.isNotEmpty(config.getAppId()) && StringUtils.isNotEmpty(config.getApiKey())) {
            String cacheKey = config.getAppId() + ":" + config.getApiKey();
            clientCache.remove(cacheKey);
        }
    }
}
