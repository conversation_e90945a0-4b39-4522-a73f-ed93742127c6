package cn.taihealth.ih.service.vm.ca;

import cn.taihealth.ih.ca.been.Qrcode;
import cn.taihealth.ih.spring.security.jwt.JWTToken;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class QrcodeTokenVM {

    @ApiModelProperty("二维码是否登录，已登录，使用jwtToken")
    private boolean isLogin;

    @ApiModelProperty("二维码是否需要刷新")
    private boolean needRefresh;

    private JWTToken jwtToken;

    public QrcodeTokenVM(boolean isLogin) {
        this.isLogin = isLogin;
    }

    public QrcodeTokenVM(boolean isLogin, boolean needRefresh) {
        this.isLogin = isLogin;
        this.needRefresh = needRefresh;
    }

    public QrcodeTokenVM(JWTToken jwtToken) {
        this.isLogin = true;
        this.jwtToken = jwtToken;
    }

}
