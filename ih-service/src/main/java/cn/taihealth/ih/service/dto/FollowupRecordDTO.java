package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.hospital.FollowupRecord;
import cn.taihealth.ih.service.vm.UploadVM;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FollowupRecordDTO extends UpdatableDTO {

    @ApiModelProperty("必填,就诊日期")
    @NotNull(message = "就诊日期必填")
    private Date visitDate;

    @ApiModelProperty("下一次就诊日期")
    @NotNull(message = "下一次就诊日期必填")
    private Date furtherVisitDate;

    @ApiModelProperty("就诊类型")
    @NotNull(message = "就诊类型必填")
    private FollowupRecord.VisitType visitType;

    @ApiModelProperty("医院名称必填")
    @NotBlank(message = "医院名词必填")
    private String hospitalName;

    @ApiModelProperty("科室名称")
    private String deptName;

    @ApiModelProperty("医生名称")
    private String doctorName;

    @ApiModelProperty("诊断结果")
    private String diagnosisResult;

    @ApiModelProperty("收缩压（高压）")
    private Integer highPressure;

    @ApiModelProperty("舒张压（低压）")
    private Integer lowPressure;

    @ApiModelProperty("体重")
    private Float weight;

    @ApiModelProperty("血糖")
    private Float bloodSugar;

    @ApiModelProperty("心率")
    private Integer heartRate;

    @ApiModelProperty("化验单照片")
    private List<UploadVM> uploads = Lists.newArrayList();

    public FollowupRecordDTO(FollowupRecord followupRecord) {
        super(followupRecord);
        visitDate = followupRecord.getVisitDate();
        furtherVisitDate = followupRecord.getFurtherVisitDate();
        visitType = followupRecord.getVisitType();
        hospitalName = followupRecord.getHospitalName();
        deptName = followupRecord.getDeptName();
        doctorName = followupRecord.getDoctorName();
        diagnosisResult = followupRecord.getDiagnosisResult();
        highPressure = followupRecord.getHighPressure();
        lowPressure = followupRecord.getLowPressure();
        weight = followupRecord.getWeight();
        bloodSugar = followupRecord.getBloodSugar();
        heartRate = followupRecord.getHeartRate();
        uploads = followupRecord.getUploads().stream().map(UploadVM::new).collect(Collectors.toList());
    }


}
