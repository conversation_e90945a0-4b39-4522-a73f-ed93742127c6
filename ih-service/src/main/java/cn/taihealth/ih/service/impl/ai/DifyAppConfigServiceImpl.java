package cn.taihealth.ih.service.impl.ai;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.domain.cloud.DifyAppConfig;
import cn.taihealth.ih.repo.DifyAppConfigRepository;
import cn.taihealth.ih.service.api.ai.DifyAppConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Dify应用配置服务实现类
 */
@Service
@AllArgsConstructor
@Slf4j
@CacheConfig(cacheNames = "dify-app-config")
public class DifyAppConfigServiceImpl implements DifyAppConfigService {

    private final DifyAppConfigRepository difyAppConfigRepository;

    @Override
    @Cacheable(key = "#appId", unless = "#result == null || !#result.isPresent()")
    public Optional<DifyAppConfig> findByAppId(String appId) {
        log.debug("查询Dify应用配置，appId: {}", appId);
        return difyAppConfigRepository.findByAppId(appId);
    }

    @Override
    public Optional<DifyAppConfig> findByHospitalIdAndAppType(Long hospitalId, String appType) {
        log.debug("查询Dify应用配置，hospitalId: {}, appType: {}", hospitalId, appType);
        return difyAppConfigRepository.findByHospitalIdAndAppType(hospitalId, appType);
    }

    @Override
    public List<DifyAppConfig> findByHospitalId(Long hospitalId) {
        log.debug("查询医院所有Dify应用配置，hospitalId: {}", hospitalId);
        return difyAppConfigRepository.findByHospitalId(hospitalId);
    }

    @Override
    public List<DifyAppConfig> findByHospitalIdAndEnabled(Long hospitalId, Boolean enabled) {
        log.debug("查询医院启用的Dify应用配置，hospitalId: {}, enabled: {}", hospitalId, enabled);
        return difyAppConfigRepository.findByHospitalIdAndEnabled(hospitalId, enabled);
    }

    @Override
    @Transactional
    @CacheEvict(key = "#config.appId")
    public DifyAppConfig save(DifyAppConfig config) {
        log.info("保存Dify应用配置，appId: {}", config.getAppId());
        return difyAppConfigRepository.save(config);
    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        log.info("删除Dify应用配置，id: {}", id);
        // 先查询配置以获取appId用于清除缓存
        Optional<DifyAppConfig> config = difyAppConfigRepository.findById(id);
        if (config.isPresent()) {
            evictCache(config.get().getAppId());
        }
        difyAppConfigRepository.deleteById(id);
    }

    @Override
    @CacheEvict(key = "#appId")
    public void evictCache(String appId) {
        log.debug("清除Dify应用配置缓存，appId: {}", appId);
    }
}
