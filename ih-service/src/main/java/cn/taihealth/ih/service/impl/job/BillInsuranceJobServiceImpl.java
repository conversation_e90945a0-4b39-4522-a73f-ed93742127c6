package cn.taihealth.ih.service.impl.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.WechatInsuranceOrder;
import cn.taihealth.ih.domain.cloud.WechatInsuranceOrderRefund;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.his.HisOutpatientChargeGroup;
import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.domain.hospital.HisBill;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.WechatInsuranceOrderRefundRepository;
import cn.taihealth.ih.repo.WechatInsuranceOrderRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRefundRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeGroupRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeRepository;
import cn.taihealth.ih.repo.hospital.BillRepository;
import cn.taihealth.ih.repo.hospital.HisBillRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineDeptRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.service.api.job.BillInsuranceJobService;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 医保对账定时任务业务实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class BillInsuranceJobServiceImpl implements BillInsuranceJobService {

    // 对账汇总表
    private final BillRepository billRepository;
    // 互联网医院表
    private final HospitalRepository hospitalRepository;
    // 就诊人表
    private final PatientRepository patientRepository;
    // 门诊缴费合并表
    private final HisOutpatientChargeGroupRepository hisOutpatientChargeGroupRepository;
    // 门诊缴费明细表
    private final HisOutpatientChargeRepository hisOutpatientChargeRepository;
    // 线下订单表-预约挂号
    private final OfflineOrderRepository offlineOrderRepository;
    // his账单表
    private final HisBillRepository hisBillRepository;
    // 线下科室表
    private final OfflineDeptRepository offlineDeptRepository;
    // 微信医保支付订单表
    private final WechatInsuranceOrderRepository wechatInsuranceOrderRepository;
    // 微信医保退款订单表
    private final WechatInsuranceOrderRefundRepository wechatInsuranceOrderRefundRepository;
    // 支付宝订单表
    private final AliPayOrderRepository aliPayOrderRepository;
    // 支付宝退款表
    private final AliPayOrderRefundRepository aliPayOrderRefundRepository;

    /**
     * 微信医保-支付对账
     */
    @Override
    public void checkWeChatInsurancePayedBills(List<WechatInsuranceOrder> wechatInsuranceOrderList) {
        if (CollectionUtils.isEmpty(wechatInsuranceOrderList)) {
            return;
        }
        int count = 0;
        for (int i = 0; i < wechatInsuranceOrderList.size(); i++) {
            generateWeChatInsurancePayedBill(wechatInsuranceOrderList.get(i));
            count++;
            if (count % 500 == 0) {
                log.info("微信医保支付订单汇总bill账单，已处理{}条数据", count);
            }
        }
        log.info("微信医保支付订单汇总bill账单结束，总计处理{}条数据", count);
    }

    /**
     * 微信医保-退款对账
     */
    @Override
    public void checkWeChatInsuranceRefundBills(List<WechatInsuranceOrderRefund> refunds) {
        if (CollectionUtils.isEmpty(refunds)) {
            return;
        }
        int count = 0;
        for (int i = 0; i < refunds.size(); i++) {
            WechatInsuranceOrderRefund wechatInsuranceOrderRefund = refunds.get(i);
            WechatInsuranceOrder wechatInsuranceOrder= wechatInsuranceOrderRepository.findOneByHospOutTradeNo(wechatInsuranceOrderRefund.getOutTradeNo()).orElse(null);
            if (wechatInsuranceOrder == null) {
                log.info("处理微信医保退款bill，该退款账单没有微信医保支付订单，跳过记录");
                return;
            }
            generateWeChatInsuranceRefundBill(wechatInsuranceOrderRefund, wechatInsuranceOrder);
            count++;
            if (count % 100 == 0) {
                log.info("处理微信医保退款bill账单，退款账单已处理{}条数据", count);
            }
        }
    }

    /**
     * 支付宝医保-支付对账
     */
    @Override
    public void checkAliPayInsurancePayedBills(List<AliPayOrder> aliPayOrderList) {
        if (CollectionUtils.isEmpty(aliPayOrderList)) {
            return;
        }
        int count = 0;
        for (int i = 0; i < aliPayOrderList.size(); i++) {
            generateAliPayInsurancePayedBill(aliPayOrderList.get(i));
            count++;
            if (count % 500 == 0) {
                log.info("支付宝医保支付订单汇总bill账单，已处理{}条数据", count);
            }
        }
        log.info("支付宝医保支付订单汇总bill账单结束，总计处理{}条数据", count);
    }

    /**
     * 支付宝医保-退款对账
     */
    @Override
    public void checkAliPayInsuranceRefundBills(List<AliPayOrderRefund> refunds) {
        if (CollectionUtils.isEmpty(refunds)) {
            return;
        }
        int count = 0;
        for (int i = 0; i < refunds.size(); i++) {
            AliPayOrderRefund aliPayRefund = refunds.get(i);
            AliPayOrder aliPayOrder = aliPayOrderRepository.findOneByOutTradeNo(aliPayRefund.getOutTradeNo())
                    .orElse(null);
            if (aliPayOrder == null) {
                log.info("处理支付宝医保退款bill，该退款账单没有支付宝医保支付订单，跳过记录");
                return;
            }
            generateAliPayInsuranceRefundBill(aliPayRefund, aliPayOrder);
            count++;
            if (count % 100 == 0) {
                log.info("处理支付宝医保退款bill账单，退款账单已处理{}条数据", count);
            }
        }
    }
    /**
     * HIS单边账根据支付类型分类
     */
    @Override
    public void groupGenerateHisBill(List<HisBill> hisBills) {
        // 微信账单
        List<HisBill> wechatList = hisBills.stream()
                .filter(b -> b.getPayType() == 1 || b.getPayType() == 3)
                .filter(b -> StringUtils.isNotEmpty(b.getPayOrderId())) //PayOrderId为空无法确定是否是单边账
                .filter(b -> wechatUnilateral(b))
                .collect(Collectors.toList());
        log.info("Bill-HIS单边账（微信医保）数量：{}", wechatList.size());
        wechatList.forEach(this::generateHisUnilateralBill);

        // 支付宝账单
        List<HisBill> alipayList = hisBills.stream()
                .filter(b -> b.getPayType() == 2)
                .filter(b -> StringUtils.isNotEmpty(b.getPayOrderId())) //PayOrderId为空无法确定是否是单边账
                .filter(b -> alipayUnilateral(b))
                .collect(Collectors.toList());
        log.info("Bill-HIS单边账（支付宝医保）数量：{}", alipayList.size());
        alipayList.forEach(this::generateHisUnilateralBill);
    }


    // ----------------------------------------------微信医保对账开始------------------------------------------------------
    /**
     * 微信医保-生成支付账单
     */
    private Bill generateWeChatInsurancePayedBill(WechatInsuranceOrder wechatInsuranceOrder) {
        // 没有医保支付订单,直接返回
        if (wechatInsuranceOrder == null) {
            return null;
        }
        if (!wechatInsuranceOrder.getPayed()) {
            return null;
        }

        // 查询该对账单是否已生成
        Bill bill = billRepository.findFirstByMerchantOrderNumberAndBillOperateType(
                wechatInsuranceOrder.getHospOutTradeNo(), BillOperateTypeEnum.PAYMENT);

        if (bill == null) {
            // 新建对账单
            bill = new Bill();
            bill.setReconciliationDoubt(true);
            bill.setReconciliationResult(ReconciliationResultEnum.DOUBT);

            Hospital hospital = null;
            Patient patient = null;
            String patientName = null;
            String patientMobile = null;
            ProjectTypeEnum billServiceType = null;
            Long deptId = null;
            String deptCode = null;
            String deptName = null;
            OnlineType deptOnlineType = null;
            // his结算单据号
            String settleId = null;
            switch (wechatInsuranceOrder.getWechatOrderType()) {
                /*
                 * 以下业务可能会涉及到医保结算，暂不处理
                 * 1.线上问诊、咨询
                 * 2.线上处方
                 * 3.住院预交
                 */
                case OUTPATIENT_FEE:
                    // 门诊缴费
                    Optional<HisOutpatientChargeGroup> group = hisOutpatientChargeGroupRepository.findById(
                            Long.parseLong(wechatInsuranceOrder.getProductId()));
                    if (group.isPresent()) {
                        if (group.get().getHospitalId() != null) {
                            hospital = hospitalRepository.getById(group.get().getHospitalId());
                        }
                        if (group.get().getPatientId() != null) {
                            patient = patientRepository.getById(group.get().getPatientId());
                            patientName = patient.getName();
                            patientMobile = patient.getMobile();
                        }
                        deptId = group.get().getOfflineDeptId();
                        if (deptId != null) {
                            OfflineDept offlineDept = offlineDeptRepository.getById(deptId);
                            deptCode = offlineDept.getDeptCode();
                            deptName = offlineDept.getDeptName();
                            deptOnlineType = OnlineType.OFFLINE;
                        }
                        List<HisOutpatientCharge> hisOutpatientChargeList = hisOutpatientChargeRepository.findAllByHisOutpatientChargeGroupId(group.get().getId());
                        if (hisOutpatientChargeList != null && hisOutpatientChargeList.size() == 1) {
                            // 医保不会合并支付，hisOutpatientChargeList只会有一条
                            settleId = hisOutpatientChargeList.get(0).getSettleId();
                        }
                    }
                    billServiceType = ProjectTypeEnum.OUTPATIENT_PAYMENT;
                    break;
                // 线下挂号
                case OUTPATIENT_REGISTER_FEE:
                    OfflineOrder offlineOrder = offlineOrderRepository.findById(Long.parseLong(wechatInsuranceOrder.getProductId())).orElse(null);
                    if (offlineOrder != null) {
                        hospital = offlineOrder.getHospital();
                        if (offlineOrder.getPatientId() != null) {
                            patient = patientRepository.getById(Long.valueOf(offlineOrder.getPatientId()));
                            patientName = patient.getName();
                            patientMobile = patient.getMobile();
                        }
                        if (StringUtils.isNotBlank(offlineOrder.getHisDeptId())) {
                            Optional<OfflineDept> deptOptional = offlineDeptRepository
                                    .findOneByHospitalAndDeptCode(hospital, offlineOrder.getHisDeptId());
                            if (deptOptional.isPresent()) {
                                deptId = deptOptional.get().getId();
                                deptCode = deptOptional.get().getDeptCode();
                                deptName = deptOptional.get().getDeptName();
                                deptOnlineType = OnlineType.OFFLINE;
                            }
                        }
                        settleId = offlineOrder.getSettleId();
                    }
                    billServiceType = ProjectTypeEnum.APPOINTMENT_REGISTRATION;
                    break;
                default:
            }

            if (patient == null || hospital == null) {
                log.info("同步bill账异常,wechat insurance order  关联订单失败  参数{}", JSONUtil.toJsonStr(wechatInsuranceOrder));
                return null;
            }
            hospital = hospitalRepository.getById(hospital.getId());
            bill.setHospital(hospital);
            //科室相关
            bill.setDeptId(deptId);
            bill.setDeptCode(deptCode);
            bill.setDeptName(deptName);
            bill.setDeptOnlineType(deptOnlineType);
            // 患者相关
            bill.setPatientId(patient.getId());
            bill.setPatientName(patientName);
            bill.setPatientMobile(patientMobile);
            bill.setBillServiceType(billServiceType);
            // 结算单据号（对账使用）
            bill.setSettleId(settleId);
        }

        if (Optional.ofNullable(bill.getSolveFlag()).orElse(false)) {
            log.info("bill账单{} 已标记为处理完成，无需再次处理", bill.getId());
            return null;
        }

        // 订单相关
        // 医保标识
        bill.setInsuranceFlag(true);
        bill.setTransactionId(wechatInsuranceOrder.getMedTransId());
        bill.setOrderNo(wechatInsuranceOrder.getProductId());
        // 医保结算单据号
        bill.setPayOrderId(StringUtils.isBlank(wechatInsuranceOrder.getPayOrderId()) ? wechatInsuranceOrder.getShBillNo() : wechatInsuranceOrder.getPayOrderId());
        // 商户号是wechatInsuranceOrder的HospOutTradeNo 是wechatInsuranceRefundOrder的outTradeNo
        bill.setMerchantOrderNumber(wechatInsuranceOrder.getHospOutTradeNo());

        // 金额相关
        // 总金额
        bill.setPaymentAmount(wechatInsuranceOrder.getTotalFee());
        // 自费金额
        bill.setSelfPaymentAmount(wechatInsuranceOrder.getCashFee());
        // 医保金额
        bill.setMedicarePaymentAmount(wechatInsuranceOrder.getInsuranceFee());

        // 枚举值赋值
        bill.setBillSource(BillSourceEnum.USER);

        if (wechatInsuranceOrder.getPayed()) {
            bill.setBillPayStatus(BillPayStatusEnum.PAYED);
        }

        bill.setBillOperateType(BillOperateTypeEnum.PAYMENT);

        bill.setBillChannel(BillChannelEnum.WECHAT);

        // 时间相关
        bill.setOrderTime(wechatInsuranceOrder.getCreatedDate());
        bill.setPaymentTime(wechatInsuranceOrder.getPayTime());
        bill.setOrderOperateTime(wechatInsuranceOrder.getPayTime());
        bill.setUpdatedDate(new Date());

        // 是否存疑
        compareWithHisBill(bill);

        billRepository.save(bill);
        return bill;
    }

    /**
     * 微信医保-生成退款账单
     */
    private void generateWeChatInsuranceRefundBill(WechatInsuranceOrderRefund wechatInsuranceOrderRefund, WechatInsuranceOrder wechatInsuranceOrder) {
        try {
            // 找出对账表里的之前生成的对应的支付对账记录，用于退款记录的填充，再去修改
            Bill payBill = billRepository.findFirstByMerchantOrderNumberAndBillOperateType(
                    wechatInsuranceOrder.getHospOutTradeNo(), BillOperateTypeEnum.PAYMENT);
            if (payBill == null && wechatInsuranceOrderRefund != null) {
                // 没有过支付账单，就产生一下
                Bill bill = generateWeChatInsurancePayedBill(wechatInsuranceOrder);
                log.info("bill退款账单对账未找到支付对账单并且无法生成支付对账单，支付流水号：{}", payBill.getTransactionId());
                if (bill == null) {
                    return;
                } else {
                    payBill = bill;
                }
            }
            // 查询退款账单是否已生成
            Bill refundBill = billRepository.findFirstByHospitalIdAndOutRefundNoAndBillOperateType(
                    wechatInsuranceOrder.getHospitalId(),
                    wechatInsuranceOrderRefund.getOutRefundNo(), BillOperateTypeEnum.REFUND);

            if (refundBill == null) {
                if (payBill.getHospital()== null) {
                    log.info("bill支付账单没有医院信息，支付流水号：{}", payBill.getTransactionId());
                    return;
                }
                refundBill = BeanUtil.copyProperties(payBill, Bill.class, "id", "hospital","settle_id");
                refundBill.setHospital(hospitalRepository.getById(payBill.getHospital().getId()));
                refundBill.setId(null);
                // 退款账单将支付的三个金额置为null
                refundBill.setPaymentAmount(null);
                refundBill.setSelfPaymentAmount(null);
                refundBill.setMedicarePaymentAmount(null);
                refundBill.setPaymentTime(null);
                // 账单类型
                refundBill.setInsuranceFlag(true);
                // 处理为存疑，后续处理是否存疑时重新赋值
                refundBill.setReconciliationDoubt(true);
                refundBill.setReconciliationResult(ReconciliationResultEnum.DOUBT);
                // 获取取消结算单据号（cancel_settle_id）
                switch (wechatInsuranceOrder.getWechatOrderType()) {
                    /*
                     * 1.线上问诊、咨询
                     * 2.线上处方
                     * 3.住院预交
                     * 以上业务可能会涉及到医保结算(暂不处理)
                     */
                    case OUTPATIENT_FEE:
                        // 门诊缴费
                        // 根据微信医保的支付订单的productid（门诊缴费，对应的是groupId）找到对应的业务表
                        List<HisOutpatientCharge> hisOutpatientChargeList = hisOutpatientChargeRepository.findAllByHisOutpatientChargeGroupId(Long.parseLong(wechatInsuranceOrder.getProductId()));
                        if (hisOutpatientChargeList != null && hisOutpatientChargeList.size() == 1) {
                            // 医保不会合并支付，所以只会有一条HisOutpatientCharge数据
                            refundBill.setSettleId(hisOutpatientChargeList.get(0).getCancelSettleId());
                        }
                        break;
                    // 线下挂号
                    case OUTPATIENT_REGISTER_FEE:
                        OfflineOrder offlineOrder = offlineOrderRepository.findById(Long.parseLong(wechatInsuranceOrder.getProductId())).orElseGet(null);
                        if (offlineOrder != null) {
                            refundBill.setSettleId(offlineOrder.getCancelSettleId());
                        }
                        break;
                    default:
                }
                // 查询HIS单边账
                Bill hisBill = billRepository.findFirstByHospitalIdAndPayOrderIdAndBillOperateType(wechatInsuranceOrder.getHospitalId(), wechatInsuranceOrderRefund.getPayOrdId(), BillOperateTypeEnum.REFUND);
                if (hisBill != null && hisBill.getHisAmount() != 0 && hisBill.getRefundAmount() == 0) {
                    /*
                     * 当refundBill为null,根据settleId能查询到hisBill的时候，有两种情况：
                     * 1.这条hisBill是单边账
                     * 2.取消结算单据号传的不对，比如两条退款订单的取消结算单据号都是0，那这里第二条数据对账，查出来的hisbill是第一条对账数据
                     * 所以需要判断hisBill是不是his退款金额不为0，并且ih退款金额为0
                     * 如果refundBill不为null，说明该笔退款肯定没有单边账
                     */
                    refundBill.setId(hisBill.getId());
                }
            }

            // 新增字段，微信退款唯一标识
            refundBill.setOutRefundNo(wechatInsuranceOrderRefund.getOutRefundNo());
            refundBill.setUpdatedDate(new Date());
            refundBill.setTransactionId(wechatInsuranceOrderRefund.getTransactionId());
            refundBill.setPayOrderId(payBill.getPayOrderId());
            refundBill.setBillPayStatus(BillPayStatusEnum.REFUNDED);
            refundBill.setBillOperateType(BillOperateTypeEnum.REFUND);
            refundBill.setRefundInitiationTime(wechatInsuranceOrderRefund.getCreatedDate());
            if (StringUtils.isNotBlank(wechatInsuranceOrderRefund.getSuccessTime())) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                refundBill.setOrderOperateTime(format.parse(wechatInsuranceOrderRefund.getSuccessTime()));
            } else {
                refundBill.setOrderOperateTime(wechatInsuranceOrderRefund.getUpdatedDate());
            }
            // 医保订单仅支持全额退款
            // 应退款金额
            refundBill.setRefundAmount(wechatInsuranceOrder.getTotalFee());
            refundBill.setSelfRefundAmount(wechatInsuranceOrder.getCashFee());
            refundBill.setMedicareRefundAmount(wechatInsuranceOrder.getInsuranceFee());
            // 已退款金额
            if (Objects.equals(wechatInsuranceOrderRefund.getStatus(), "SUCCESS")) {
                refundBill.setRefundedAmount(wechatInsuranceOrder.getTotalFee());
                refundBill.setSelfRefundedAmount(wechatInsuranceOrder.getCashFee());
                refundBill.setMedicareRefundedAmount(wechatInsuranceOrder.getInsuranceFee());
            } else {
                refundBill.setRefundedAmount(0);
                refundBill.setSelfRefundedAmount(0);
                refundBill.setMedicareRefundedAmount(0);
            }

            // 总金额是否退款完成
            boolean totalRefunded = Objects.equals(refundBill.getRefundAmount(), refundBill.getRefundedAmount());
            // 自费金额是否退款完成
            boolean selfRefunded = Objects.equals(refundBill.getSelfRefundAmount(), refundBill.getSelfRefundedAmount());
            // 医保金额是否退款完成
            boolean medicareRefunded = Objects.equals(refundBill.getMedicareRefundAmount(), refundBill.getMedicareRefundedAmount());

            // 是否退款完成
            refundBill.setSolveFlag(totalRefunded && selfRefunded && medicareRefunded);

            // 应退金额一定大于等于已退金额，大于既是未处理完成，等于既是处理完成
            if (refundBill.getSolveFlag()) {
                // 是否存疑
                compare(payBill, refundBill);
            } else {
                // 退款处理未完成
                refundBill.setReconciliationResult(ReconciliationResultEnum.REFUNDING);
                payBill.setReconciliationDoubt(true);
            }
            billRepository.save(refundBill);
        } catch (Exception e) {
            log.error("bill生成医保退款账单异常，退款订单号：{}, {}", wechatInsuranceOrderRefund.getOutTradeNo(), e);
        }
    }
    // ----------------------------------------------微信医保对账结束------------------------------------------------------


    // ----------------------------------------------支付宝医保对账开始-----------------------------------------------------
    /**
     * 支付宝医保-生成支付账单
     */
    private Bill generateAliPayInsurancePayedBill(AliPayOrder aliPayOrder) {
        // 没有支付,就返回
        if (aliPayOrder == null) {
            return null;
        }
        if (!aliPayOrder.getPayed()) {
            return null;
        }

        // 查询单号是否已生成
        Bill bill = billRepository.findFirstByMerchantOrderNumberAndBillOperateType(
                aliPayOrder.getOutTradeNo(), BillOperateTypeEnum.PAYMENT);

        if (bill == null) {
            bill = new Bill();
            bill.setReconciliationDoubt(true);
            bill.setReconciliationResult(ReconciliationResultEnum.DOUBT);

            Hospital hospital = null;
            Patient patient = null;
            String patientName = null;
            String patientMobile = null;
            ProjectTypeEnum billServiceType = null;
            Long deptId = null;
            String deptCode = null;
            String deptName = null;
            OnlineType deptOnlineType = null;
            // his结算单据号
            String settleId = null;
            switch (aliPayOrder.getOrderType()) {
                /*
                 * 1.线上问诊、咨询
                 * 2.线上处方
                 * 3.住院预交
                 * 以上业务可能会涉及到医保结算(暂不处理)
                 */
                case OUTPATIENT_FEE:
                    // 门诊缴费
                    Optional<HisOutpatientChargeGroup> group = hisOutpatientChargeGroupRepository.findById(
                            Long.parseLong(aliPayOrder.getBody()));
                    if (group.isPresent()) {
                        if (group.get().getHospitalId() != null) {
                            hospital = hospitalRepository.getById(group.get().getHospitalId());
                        }
                        if (group.get().getPatientId() != null) {
                            patient = patientRepository.getById(group.get().getPatientId());
                            patientName = patient.getName();
                            patientMobile = patient.getMobile();
                        }
                        deptId = group.get().getOfflineDeptId();
                        if (deptId != null) {
                            OfflineDept offlineDept = offlineDeptRepository.getById(deptId);
                            deptCode = offlineDept.getDeptCode();
                            deptName = offlineDept.getDeptName();
                            deptOnlineType = OnlineType.OFFLINE;
                        }
                        List<HisOutpatientCharge> hisOutpatientChargeList = hisOutpatientChargeRepository.findAllByHisOutpatientChargeGroupId(group.get().getId());
                        if (hisOutpatientChargeList != null && hisOutpatientChargeList.size() == 1) {
                            // 医保不会合并支付，hisOutpatientChargeList只会有一条
                            settleId = hisOutpatientChargeList.get(0).getSettleId();
                        }
                    }
                    billServiceType = ProjectTypeEnum.OUTPATIENT_PAYMENT;
                    break;
                // 线下挂号
                case OUTPATIENT_REGISTER_FEE:
                    OfflineOrder offlineOrder = offlineOrderRepository.findById(Long.parseLong(aliPayOrder.getBody())).orElse(null);
                    if (offlineOrder != null) {
                        hospital = offlineOrder.getHospital();
                        if (offlineOrder.getPatientId() != null) {
                            patient = patientRepository.getById(Long.valueOf(offlineOrder.getPatientId()));
                            patientName = patient.getName();
                            patientMobile = patient.getMobile();
                        }
                        if (StringUtils.isNotBlank(offlineOrder.getHisDeptId())) {
                            Optional<OfflineDept> deptOptional = offlineDeptRepository
                                    .findOneByHospitalAndDeptCode(hospital, offlineOrder.getHisDeptId());
                            if (deptOptional.isPresent()) {
                                deptId = deptOptional.get().getId();
                                deptCode = deptOptional.get().getDeptCode();
                                deptName = deptOptional.get().getDeptName();
                                deptOnlineType = OnlineType.OFFLINE;
                            }
                        }
                        settleId = offlineOrder.getSettleId();
                    }
                    billServiceType = ProjectTypeEnum.APPOINTMENT_REGISTRATION;
                    break;
                default:
            }

            if (patient == null || hospital == null) {
                log.info("同步bill账异常,wechat order  关联订单失败  参数{}", JSONUtil.toJsonStr(aliPayOrder));
                return null;
            }
            hospital = hospitalRepository.getById(hospital.getId());
            bill.setHospital(hospital);
            //科室相关
            bill.setDeptId(deptId);
            bill.setDeptCode(deptCode);
            bill.setDeptName(deptName);
            bill.setDeptOnlineType(deptOnlineType);
            // 患者相关
            bill.setPatientId(patient.getId());
            bill.setPatientName(patientName);
            bill.setPatientMobile(patientMobile);
            bill.setBillServiceType(billServiceType);
            // 结算单据号（对账使用）
            bill.setSettleId(settleId);
        }

        if (Optional.ofNullable(bill.getSolveFlag()).orElse(false)) {
            log.info("bill账单{} 已标记为处理完成，无需再次处理", bill.getId());
            return null;
        }

        // 订单相关
        // 医保标识
        bill.setInsuranceFlag(true);
        bill.setTransactionId(aliPayOrder.getTradeNo());
        bill.setOrderNo(aliPayOrder.getBody());
        // 医保结算单据号
        bill.setPayOrderId(aliPayOrder.getPayOrderId());
        bill.setMerchantOrderNumber(aliPayOrder.getOutTradeNo());

        // 金额相关
        // 总金额
        bill.setPaymentAmount(aliPayOrder.getTotalFee());
        // 自费金额
        bill.setSelfPaymentAmount(aliPayOrder.getCashFee());
        // 医保金额
        bill.setMedicarePaymentAmount(aliPayOrder.getInsuranceFee());

        // 枚举值赋值
        bill.setBillSource(BillSourceEnum.USER);

        if (aliPayOrder.getPayed()) {
            bill.setBillPayStatus(BillPayStatusEnum.PAYED);
        }

        bill.setBillOperateType(BillOperateTypeEnum.PAYMENT);

        bill.setBillChannel(BillChannelEnum.ALIPAY);

        // 时间相关
        bill.setOrderTime(aliPayOrder.getCreatedDate());
        bill.setPaymentTime(aliPayOrder.getGmtPayment());
        bill.setOrderOperateTime(aliPayOrder.getGmtPayment());
        bill.setUpdatedDate(new Date());

        // 是否存疑
        compareWithHisBill(bill);

        billRepository.save(bill);
        return bill;
    }

    /**
     * 支付宝医保-退款对账
     */
    private void generateAliPayInsuranceRefundBill(AliPayOrderRefund aliPayOrderRefund, AliPayOrder aliPayOrder) {
        try {
            // 找出对账表里的之前生成的对应的支付对账记录，用于退款记录的填充，再去修改
            Bill payBill = billRepository.findFirstByMerchantOrderNumberAndBillOperateType(
                    aliPayOrderRefund.getOutTradeNo(), BillOperateTypeEnum.PAYMENT);
            if (payBill == null && aliPayOrderRefund != null) {
                // 没有过支付账单，就产生一下
                Bill bill = generateAliPayInsurancePayedBill(aliPayOrder);
                if (bill == null) {
                    log.info("bill该退款账单没有生成过支付账单，并且没有支付订单数据，支付时间：{}, 退款金额：{}, 退款时间：{}, 流水号：{}", aliPayOrder.getGmtPayment(), aliPayOrderRefund.getAmount(), aliPayOrderRefund.getSuccessTime(), aliPayOrderRefund.getTradeNo());
                    return;
                } else {
                    payBill = bill;
                }
            }
            // 查询退款账单是否已生成
            Bill refundBill = billRepository.findFirstByHospitalIdAndOutRefundNoAndBillOperateType(
                    aliPayOrder.getHospitalId(),
                    aliPayOrderRefund.getRefundNo(), BillOperateTypeEnum.REFUND);

            if (refundBill == null) {
                if (payBill.getHospital()== null) {
                    log.info("bill支付账单没有医院信息，支付流水号：{}", payBill.getTransactionId());
                    return;
                }
                refundBill = BeanUtil.copyProperties(payBill, Bill.class, "id", "hospital","settle_id");
                refundBill.setHospital(hospitalRepository.getById(payBill.getHospital().getId()));
                refundBill.setId(null);
                // 退款账单将支付的三个金额置为null
                refundBill.setPaymentAmount(null);
                refundBill.setSelfPaymentAmount(null);
                refundBill.setMedicarePaymentAmount(null);
                refundBill.setPaymentTime(null);
                // 处理为存疑，后续处理是否存疑时重新赋值
                refundBill.setReconciliationDoubt(true);
                refundBill.setReconciliationResult(ReconciliationResultEnum.DOUBT);
                // 获取取消结算单据号（cancel_settle_id）
                switch (aliPayOrder.getOrderType()) {
                    /*
                     * 1.线上问诊、咨询
                     * 2.线上处方
                     * 3.住院预交
                     * 以上业务可能会涉及到医保结算(暂不处理)
                     */
                    case OUTPATIENT_FEE:
                        // 门诊缴费
                        // 根据支付宝医保的支付订单的body（门诊缴费，对应的是groupId）找到对应的业务表
                        Optional<HisOutpatientChargeGroup> group = hisOutpatientChargeGroupRepository.findById(
                                Long.parseLong(aliPayOrder.getBody()));
                        if (group.isPresent()) {
                            List<HisOutpatientCharge> hisOutpatientChargeList = hisOutpatientChargeRepository.findAllByHisOutpatientChargeGroupId(group.get().getId());
                            if (hisOutpatientChargeList != null && hisOutpatientChargeList.size() == 1) {
                                // 医保不会合并支付，所以只会有一条HisOutpatientCharge数据
                                refundBill.setSettleId(hisOutpatientChargeList.get(0).getCancelSettleId());
                            }
                        }
                        break;
                    // 线下挂号
                    case OUTPATIENT_REGISTER_FEE:
                        OfflineOrder offlineOrder = offlineOrderRepository.findById(Long.parseLong(aliPayOrder.getBody())).orElseGet(null);
                        if (offlineOrder != null) {
                            refundBill.setSettleId(offlineOrder.getCancelSettleId());
                        }
                        break;
                    default:
                }
                // 查询HIS单边账
                Bill hisBill = billRepository.findFirstByHospitalIdAndPayOrderIdAndBillOperateType(aliPayOrder.getHospitalId(), aliPayOrderRefund.getPayOrderId(), BillOperateTypeEnum.REFUND);
                if (hisBill != null && hisBill.getHisAmount() != 0 && hisBill.getRefundAmount() == 0) {
                    /*
                     * 当refundBill为null,根据settleId能查询到hisBill的时候，有两种情况：
                     * 1.这条hisBill是单边账
                     * 2.取消结算单据号传的不对，比如两条退款订单的取消结算单据号都是0，那这里第二条数据对账，查出来的hisbill是第一条对账数据
                     * 所以需要判断hisBill是不是his退款金额不为0，并且ih退款金额为0
                     * 如果refundBill不为null，说明该笔退款肯定没有单边账
                     */
                    refundBill.setId(hisBill.getId());
                }
            }

            // 新增字段，支付宝退款唯一标识
            refundBill.setOutRefundNo(aliPayOrderRefund.getRefundNo());
            refundBill.setUpdatedDate(new Date());
            refundBill.setTransactionId(aliPayOrderRefund.getTradeNo());
            refundBill.setPayOrderId(payBill.getPayOrderId());
            refundBill.setBillPayStatus(BillPayStatusEnum.REFUNDED);
            refundBill.setBillOperateType(BillOperateTypeEnum.REFUND);
            refundBill.setRefundInitiationTime(aliPayOrderRefund.getCreatedDate());
            if (StringUtils.isNotBlank(aliPayOrderRefund.getSuccessTime())) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                refundBill.setOrderOperateTime(format.parse(aliPayOrderRefund.getSuccessTime()));
            } else {
                refundBill.setOrderOperateTime(aliPayOrderRefund.getUpdatedDate());
            }
            // 医保订单仅支持全额退款
            // 应退款金额
            refundBill.setRefundAmount(aliPayOrder.getTotalFee());
            refundBill.setSelfRefundAmount(aliPayOrder.getCashFee());
            refundBill.setMedicareRefundAmount(aliPayOrder.getInsuranceFee());
            // 已退款金额
            if (Objects.equals(aliPayOrderRefund.getStatus(), "SUCCESS")) {
                refundBill.setRefundedAmount(aliPayOrder.getTotalFee());
                refundBill.setSelfRefundedAmount(aliPayOrder.getCashFee());
                refundBill.setMedicareRefundedAmount(aliPayOrder.getInsuranceFee());
            } else {
                refundBill.setRefundedAmount(0);
                refundBill.setSelfRefundedAmount(0);
                refundBill.setMedicareRefundedAmount(0);
            }

            // 总金额是否退款完成
            boolean totalRefunded = Objects.equals(refundBill.getRefundAmount(), refundBill.getRefundedAmount());
            // 自费金额是否退款完成
            boolean selfRefunded = Objects.equals(refundBill.getSelfRefundAmount(), refundBill.getSelfRefundedAmount());
            // 医保金额是否退款完成
            boolean medicareRefunded = Objects.equals(refundBill.getMedicareRefundAmount(), refundBill.getMedicareRefundedAmount());

            // 是否退款完成
            refundBill.setSolveFlag(totalRefunded && selfRefunded && medicareRefunded);

            // 应退金额一定大于等于已退金额，大于既是未处理完成，等于既是处理完成
            if (refundBill.getSolveFlag()) {
                // 是否存疑
                compare(payBill, refundBill);
            } else {
                // 退款处理未完成
                refundBill.setReconciliationResult(ReconciliationResultEnum.REFUNDING);
                payBill.setReconciliationDoubt(true);
            }
            billRepository.save(refundBill);
        } catch (Exception e) {
            log.error("bill生成退款账单异常，退款订单号：{}, {}", aliPayOrderRefund.getOutTradeNo(), e);
        }
    }

    // ----------------------------------------------支付宝医保对账结束-----------------------------------------------------

    /**
     * 是否存疑
     */
    private void compare(Bill payBill, Bill refundBill) {
        // 退款处理完成
        // 查询对应的HIS账单
        List<HisBill> hisBills = compareWithHisBill(refundBill);
        if (CollectionUtils.isEmpty(hisBills)) {
            // 如果his退款账单不存在，有可能是单边账，有可能是结算失败后直接退款的
            // 如果支付账单his结算金额不为0，判断为单边账，原因可能如下：
            //   1.刚退款，拉取his账单的定时任务还没执行(非问题)
            //   2.支付时his返回结算失败，但实际是结算成功了(his的原因)
            //   3.支付时his结算成功，业务中有退款的，退款时，his退款失败，但我侧进行了退款(我方原因)
            //   4.支付时his结算成功，收到his的退款消息或我侧进行退款，退款成功，查询his账单时未查找到退款账单(his的原因)
            //   5.以上4个都发生过，其他未知原因
            //   医保没有结算这一步，所以需要主动去查询his的医保支付结果或者等待his的回调，只有his成功ih才会将业务表的his_pay_status设为成功

            // 医保超时退款（相当于自费的结算失败）的判断逻辑：支付账单的hisAmount=0，并且支付金额等于退款账单的退款金额（这种情况需要标记为平账）
            if (payBill.getHisAmount() == 0 && Objects.equals(payBill.getPaymentAmount(), refundBill.getRefundedAmount())) {
                switch (refundBill.getBillServiceType()) {
                    /*
                     * 1.线上问诊、咨询
                     * 2.线上处方
                     * 3.住院预交
                     * 以上业务可能会涉及到医保结算(暂不处理)
                     */
                    // 门诊缴费
                    case OUTPATIENT_PAYMENT:
                        HisOutpatientChargeGroup group = hisOutpatientChargeGroupRepository.getById(Long.parseLong(refundBill.getOrderNo()));
                        if (group != null && PayStatus.UNKNOWN.equals(group.getHisPayStatus())) {
                            // HisPayStatus-未处理，支付超时退款
                            refundBill.setReconciliationResult(ReconciliationResultEnum.BALANCING);
                            refundBill.setReconciliationDoubt(false);
                            // 将对应的支付账单也改为平账（支付账单最开始一定是存疑状态）
                            if (payBill.getReconciliationResult() == ReconciliationResultEnum.DOUBT) {
                                payBill.setReconciliationResult(ReconciliationResultEnum.BALANCING);
                            }
                            if (payBill.getReconciliationDoubt()) {
                                payBill.setReconciliationDoubt(false);
                            }
                        }
                        break;
                    // 预约挂号
                    case APPOINTMENT_REGISTRATION:
                        OfflineOrder offlineOrder = offlineOrderRepository.getById(Long.valueOf(refundBill.getOrderNo()));
                        if (offlineOrder != null && PayStatus.UNKNOWN.equals(offlineOrder.getHisPayStatus())) {
                            // HisPayStatus-未处理，支付超时退款
                            refundBill.setReconciliationResult(ReconciliationResultEnum.BALANCING);
                            refundBill.setReconciliationDoubt(false);
                            // 将对应的支付账单也改为平账（支付账单最开始一定是存疑状态）
                            if (payBill.getReconciliationResult() == ReconciliationResultEnum.DOUBT) {
                                payBill.setReconciliationResult(ReconciliationResultEnum.BALANCING);
                            }
                            if (payBill.getReconciliationDoubt()) {
                                payBill.setReconciliationDoubt(false);
                            }
                        }
                        break;
                    default:
                }
                billRepository.save(payBill);
            }
        }
    }
    /**
     * 是否存疑
     */
    private List<HisBill> compareWithHisBill(Bill bill) {
        // PayOrderId有可能为空，这里处理为存疑
        if (StringUtils.isEmpty(bill.getPayOrderId())) {
            // his方的金额全是0
            bill.setHisAmount(0);
            bill.setHisSelfAmount(0);
            bill.setHisMedicareAmount(0);
            // 设为存疑
            bill.setReconciliationResult(ReconciliationResultEnum.DOUBT);
            bill.setReconciliationDoubt(true);
            // 返回空集合
            return Lists.newArrayList();
        }

        boolean payBill = Objects.equals(bill.getBillOperateType(), BillOperateTypeEnum.PAYMENT);
        List<Specification<HisBill>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("payOrderId", bill.getPayOrderId()));
        specs.add(Specifications.eq("hospital", bill.getHospital().getId()));
        specs.add(Specifications.eq("transaction_status", payBill ? "SUCCESS" : "REFUND"));
        List<HisBill> hisBills = hisBillRepository.findAll(Specifications.and(specs));

        // 总金额
        Integer hisAmount = 0;
        // 自费
        Integer hisSelfAmount = 0;
        // 医保
        Integer hisMedicareAmount = 0;
        if (!CollectionUtil.isEmpty(hisBills)) {
            // 医保不会合并支付，所以hisBills只会有一条数据
            hisAmount = hisBills.stream()
                    .map(hb -> payBill ? hb.getOrder_amount() : hb.getRefund_amount())
                    .reduce(Integer::sum).orElse(0);
            hisSelfAmount = hisBills.stream()
                    .map(hb -> payBill ? hb.getSelfOrderAmount() : hb.getSelfRefundAmount())
                    .reduce(Integer::sum).orElse(0);
            hisMedicareAmount = hisBills.stream()
                    .map(hb -> payBill ? hb.getMedicareOrderAmount() : hb.getMedicareRefundAmount())
                    .reduce(Integer::sum).orElse(0);
        }

        if (payBill) {
            // 支付
            boolean payBalance = Objects.equals(hisAmount, bill.getPaymentAmount());
            boolean paySelfBalance = Objects.equals(hisSelfAmount, bill.getSelfPaymentAmount());
            boolean payMedicareBalance = Objects.equals(hisMedicareAmount, bill.getMedicarePaymentAmount());
            /**
             * 下面判断防止下面情况：
             * 第一步：支付对账ih和his金额不一致，导致支付账单存疑。
             * 第二步：退款对账时，发现该退款账单属于支付超时直接退款的情况，his自然没数据，
             *       于是该笔退款账单被标记为平账，并找到对应的支付账单也改为平账。
             * 第三步：第二次对账定时任务开启时，又将支付账单标记为存疑（因为和his不平账），处理退款对账时又走第二步，导致状态一直在变。
             *
             * 加上下面的判断就可以防止第二次定时任务处理支付账单时再将状态改为存疑
             */
            if (bill.getReconciliationResult() != ReconciliationResultEnum.BALANCING || bill.getHisAmount() != 0) {
                if (payBalance && paySelfBalance && payMedicareBalance) {
                    bill.setReconciliationResult(ReconciliationResultEnum.BALANCING);
                } else {
                    bill.setReconciliationResult(ReconciliationResultEnum.DOUBT);
                }
            }
            if (bill.getReconciliationDoubt() || bill.getHisAmount() != 0) {
                // 总额、自费、医保都一致才算平账
                bill.setReconciliationDoubt(!(payBalance && paySelfBalance && payMedicareBalance));
            }
        } else {
            //退款账单处理，能到这里的退款账单都是退款完成的（不会是退款中）
            // 已退款总金额是否相等
            boolean balance = Objects.equals(hisAmount, bill.getRefundedAmount());
            // 已退款自费金额是否相等
            boolean selfBalance = Objects.equals(hisSelfAmount, bill.getSelfRefundedAmount());
            // 已退款医保金额是否相等
            boolean medicareBalance = Objects.equals(hisMedicareAmount, bill.getMedicareRefundedAmount());
            // 退款金额全部相等
            if (balance && selfBalance && medicareBalance) {
                bill.setReconciliationResult(ReconciliationResultEnum.BALANCING);
                bill.setReconciliationDoubt(false);
            } else {
                bill.setReconciliationResult(ReconciliationResultEnum.DOUBT);
                bill.setReconciliationDoubt(true);
            }
        }
        bill.setHisAmount(hisAmount);
        bill.setHisSelfAmount(hisSelfAmount);
        bill.setHisMedicareAmount(hisMedicareAmount);
        return hisBills;
    }

    //--------------------------------------------HIS单边账对账开始--------------------------------------------------
    // 判断是否是HIS单边账-微信账单
    private boolean wechatUnilateral(HisBill hisBill) {
        if ("SUCCESS".equals(hisBill.getTransaction_status())) {
            // 支付
            List<Specification<WechatInsuranceOrder>> specs = Lists.newArrayList();
            specs.add(Specifications.eq("payOrderId", hisBill.getPayOrderId()));
            List<WechatInsuranceOrder> orders = wechatInsuranceOrderRepository.findAll(Specifications.and(specs));
            return CollectionUtils.isEmpty(orders);
        }
        if ("REFUND".equals(hisBill.getTransaction_status())) {
            // 退款
            List<Specification<WechatInsuranceOrderRefund>> specs = Lists.newArrayList();
            specs.add(Specifications.eq("payOrdId", hisBill.getPayOrderId()));
            List<WechatInsuranceOrderRefund> orders = wechatInsuranceOrderRefundRepository.findAll(Specifications.and(specs));
            return CollectionUtils.isEmpty(orders);
        }
        return false;
    }

    // 判断是否是HIS单边账-支付宝账单
    private boolean alipayUnilateral(HisBill hisBill) {
        if ("SUCCESS".equals(hisBill.getTransaction_status())) {
            // 支付
            List<Specification<AliPayOrder>> specs = Lists.newArrayList();
            specs.add(Specifications.eq("payOrderId", hisBill.getPayOrderId()));
            specs.add(Specifications.eq("isInsurance", true));
            List<AliPayOrder> orders = aliPayOrderRepository.findAll(Specifications.and(specs));
            return CollectionUtils.isEmpty(orders);
        }
        if ("REFUND".equals(hisBill.getTransaction_status())) {
            // 退款
            List<Specification<AliPayOrderRefund>> specs = Lists.newArrayList();
            specs.add(Specifications.eq("payOrderId", hisBill.getPayOrderId()));
            specs.add(Specifications.eq("isInsurance", true));
            List<AliPayOrderRefund> orders = aliPayOrderRefundRepository.findAll(Specifications.and(specs));
            return CollectionUtils.isEmpty(orders);
        }
        return false;
    }

    // 生成HIS单边账
    private void generateHisUnilateralBill(HisBill hisBill) {
        BillOperateTypeEnum billOperateTypeEnum = "SUCCESS".equals(hisBill.getTransaction_status()) ? BillOperateTypeEnum.PAYMENT : BillOperateTypeEnum.REFUND;
        // 查询单号是否已生成
        Bill bill = billRepository.findFirstByMerchantOrderNumberAndBillOperateType(hisBill.getMerchant_refund_number(), billOperateTypeEnum);

        if (bill != null) {
            log.info("该笔HIS单边帐已有对账单：BillId-{}", bill.getId());
            return;
        }
        bill = new Bill();
        bill.setReconciliationDoubt(true);
        bill.setReconciliationResult(ReconciliationResultEnum.DOUBT);
        bill.setHospital(hisBill.getHospital());
        bill.setDeptOnlineType(OnlineType.OFFLINE);
        // 表结构要求该字段不能为null
        bill.setPatientName("");

        if ("预约挂号".equals(hisBill.getProduct_name())) {
            bill.setBillServiceType(ProjectTypeEnum.APPOINTMENT_REGISTRATION);
        } else if ("门诊缴费".equals(hisBill.getProduct_name())) {
            bill.setBillServiceType(ProjectTypeEnum.OUTPATIENT_PAYMENT);
        } else if ("预交金".equals(hisBill.getProduct_name())) {
            bill.setBillServiceType(ProjectTypeEnum.HOSPITALIZATION_DEPOSIT);
        }

        bill.setSolveFlag(false);
        // 订单相关
        bill.setInsuranceFlag(true);
        bill.setTransactionId(hisBill.getTransaction_id());
        bill.setMerchantOrderNumber(hisBill.getMerchant_refund_number());
        bill.setSettleId(hisBill.getSettleId());
        bill.setPayOrderId(hisBill.getPayOrderId());

        if ("SUCCESS".equals(hisBill.getTransaction_status())) {
            bill.setHisAmount(hisBill.getOrder_amount());
            bill.setHisSelfAmount(hisBill.getSelfOrderAmount());
            bill.setHisMedicareAmount(hisBill.getMedicareOrderAmount());
            bill.setBillPayStatus(BillPayStatusEnum.PAYED);
            bill.setPaymentTime(hisBill.getTransaction_time());
        } else {
            bill.setHisAmount(hisBill.getRefund_amount());
            bill.setHisSelfAmount(hisBill.getSelfRefundAmount());
            bill.setHisMedicareAmount(hisBill.getMedicareRefundAmount());
            bill.setBillPayStatus(BillPayStatusEnum.REFUNDED);
            bill.setRefundInitiationTime(hisBill.getTransaction_time());
        }

        bill.setOrderTime(hisBill.getTransaction_time());
        bill.setOrderOperateTime(hisBill.getTransaction_time());
        bill.setUpdatedDate(new Date());

        // 枚举值赋值
        bill.setBillSource(BillSourceEnum.USER);

        bill.setBillOperateType(billOperateTypeEnum);

        if (hisBill.getPayType() == 1 || hisBill.getPayType() == 3) {
            bill.setBillChannel(BillChannelEnum.WECHAT);
        } else if (hisBill.getPayType() == 2) {
            bill.setBillChannel(BillChannelEnum.ALIPAY);
        }

        billRepository.save(bill);
        log.info("HIS单边账对账完成,bill 账单号 {}", bill.getId());
    }
    //--------------------------------------------HIS单边账对账结束-------------------------------------------------
}
