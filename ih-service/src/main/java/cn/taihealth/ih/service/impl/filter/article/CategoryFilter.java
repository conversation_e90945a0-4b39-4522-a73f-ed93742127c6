package cn.taihealth.ih.service.impl.filter.article;

import cn.taihealth.ih.domain.hospital.Article;
import cn.taihealth.ih.domain.hospital.ArticleCategory;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import java.util.Objects;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

/**
 *
 */
public class CategoryFilter implements SearchFilter<Article> {

    private final String name;

    public CategoryFilter(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Article> toSpecification() {
        return new Specification<Article>() {
            @Override
            public Predicate toPredicate(Root<Article> root, CriteriaQuery<?> criteriaQuery,
                                         CriteriaBuilder criteriaBuilder) {
                Join<Article, ArticleCategory> listJoin = root.join("articleCategories");
//                return not ? criteriaBuilder.notEqual(listJoin.get("category").get("name"), name)
                return criteriaBuilder.equal(listJoin.get("category").get("name"), name);
            }
        };
    }

    @Override
    public String toExpression() {
        String str = "category:" + name;
        return str;
//        return not ? "-" + str : str;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof CategoryFilter)) {
            return false;
        }

        CategoryFilter rhs = (CategoryFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
