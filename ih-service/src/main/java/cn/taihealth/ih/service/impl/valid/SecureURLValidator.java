package cn.taihealth.ih.service.impl.valid;

import cn.taihealth.ih.domain.enums.SettingKey;
import cn.taihealth.ih.service.util.SettingsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class SecureURLValidator implements ConstraintValidator<SecureURLValid, String> {

    public SecureURLValidator() {
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        URL uri;
        try {
            uri = new URL(value);
        } catch (MalformedURLException e) {
            return false;
        }
        String protocolUrl = uri.getProtocol() + "://" + uri.getHost();
        if (uri.getPort() > 0) {
            protocolUrl += (":" + uri.getPort());
        }
        protocolUrl += "/";
        List<String> domainList = SettingsHelper.getListString(SettingKey.SECURE_DOMAIN_WHITELIST);
        List<String> formatDomainList = formatDomain(domainList);
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        for (String formatDomain : formatDomainList) {
            if (antPathMatcher.match(formatDomain, protocolUrl)) {
                return true;
            }
        }
        return false;
    }

    private List<String> formatDomain(List<String> domains) {
        List<String> formatDomains = new ArrayList<>();
        for (String domain : domains) {
            if (!domain.endsWith("/")) {
                domain = domain + "/";
            }
            if (!domain.contains("://")) {
               // 不包含 :// 拼接http和https
                formatDomains.add("http://" + domain);
                formatDomains.add("https://" + domain);
            } else {
                formatDomains.add(domain);
            }
        }
        return formatDomains;
    }
}