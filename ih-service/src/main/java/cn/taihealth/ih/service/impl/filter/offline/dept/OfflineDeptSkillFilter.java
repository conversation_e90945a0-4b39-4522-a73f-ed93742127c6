package cn.taihealth.ih.service.impl.filter.offline.dept;

import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

/**
 */
public class OfflineDeptSkillFilter implements SearchFilter<OfflineDept> {

    private final String name;

    public OfflineDeptSkillFilter(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<OfflineDept> toSpecification() {
        return Specifications.eq("deptTypeCode", name);
    }

    @Override
    public String toExpression() {
        return "deptName:" + name;
    }

    @Override
    public boolean isValid() {
        return name != null;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof OfflineDeptSkillFilter)) {
            return false;
        }

        OfflineDeptSkillFilter rhs = (OfflineDeptSkillFilter) other;
        return Objects.equals(name, rhs.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
