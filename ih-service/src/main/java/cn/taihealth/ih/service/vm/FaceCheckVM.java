package cn.taihealth.ih.service.vm;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;

/**
 * View Model object for storing a user's credentials.
 */
public class FaceCheckVM {

    @NotNull
    @ApiModelProperty("必填")
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}

