package cn.taihealth.ih.service.impl.filter.offline.order;

import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.service.impl.filter.CreatedDateFilter;
import cn.taihealth.ih.service.impl.filter.GenericFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

/**
 * <AUTHOR>
 */
public class OfflineOrderSearch extends SearchCriteria<OfflineOrder> {

    public static OfflineOrderSearch of(String query) {
        OfflineOrderSearch search = new OfflineOrderSearch();
        search.parse(query);
        return search;
    }
    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q: Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<OfflineOrder> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }
        switch (qualifier) {
            case DEPT_NAME:
                return new DeptNameFilter(value);
            case ID:
                return new NumberFilter(value);
            case COPY_APPOINTMENT_ID:
                return new CopyAppointmentIdFilter(value);
            case COPY_APPOINTMENT_ID_CARD_NUM:
                return new CopyAppointmentIdCardNumFilter(value);
            case COPY_APPOINTMENT_PATIENT_NAME:
                return new CopyAppointmentIdPatientNameFilter(value);
            case PATIENT_NAME:
                return new PatientNameFilter(value);
            case MOBILE:
                return new MobileFilter(value);
            case STATUS:
            case PAY_STATUS:
                return new GenericFilter<>("status", Operator.eq, OfflineOrder.OutPatientStatus.valueOf(value));
            case CREATE_TIME:
                return new CreatedDateFilter<>(value);
            case PAY_TIME:
                return new PayTimeFilter<>(value);
            case APPOINTMENT_TIME:
                return new AppointmentDateFilter<>(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<OfflineOrder> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        DEPT_NAME,
        ID,
        COPY_APPOINTMENT_ID,
        COPY_APPOINTMENT_ID_CARD_NUM,
        COPY_APPOINTMENT_PATIENT_NAME,
        PAY_TIME,
        PATIENT_NAME,
        MOBILE,
        STATUS,
        CREATE_TIME,
        PAY_STATUS,
        APPOINTMENT_TIME
        ;
    }
}
