package cn.taihealth.ih.service.impl.filter.bill;

import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;

public class OrderStartDateFilter implements SearchFilter<Bill> {
    private final Date date;

    public OrderStartDateFilter(Date date) {
        this.date = date;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<Bill> toSpecification() {
        return Specifications.ge("orderTime", date);
    }

    @Override
    public String toExpression() {
        String str = " date:" + date;
        return str;
    }

    @Override
    public boolean isValid() {
        return date != null;
    }
}
