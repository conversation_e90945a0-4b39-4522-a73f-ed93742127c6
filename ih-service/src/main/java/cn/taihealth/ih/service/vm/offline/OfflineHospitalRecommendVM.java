package cn.taihealth.ih.service.vm.offline;

import cn.taihealth.ih.domain.cloud.OfflineHospitalRecommend;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.dto.OfflineHospitalDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class OfflineHospitalRecommendVM extends AbstractEntityDTO {

    @ApiModelProperty("线下医院")
    private OfflineHospitalDTO offlineHospital;

    @ApiModelProperty("排序序号")
    private int sortNum;

    public OfflineHospitalRecommendVM(OfflineHospitalRecommend recommend) {
        super(recommend);
        this.offlineHospital = new OfflineHospitalDTO(recommend.getOfflineHospital());
        this.sortNum = recommend.getSortNum();
    }
}
