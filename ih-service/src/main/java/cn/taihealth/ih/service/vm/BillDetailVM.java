package cn.taihealth.ih.service.vm;

import cn.hutool.core.bean.BeanUtil;
import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import cn.taihealth.ih.domain.enums.BillChannelEnum;
import cn.taihealth.ih.domain.enums.BillPayStatusEnum;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.enums.BillSourceEnum;
import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import org.apache.commons.lang3.BooleanUtils;

/**
 * 交易明细
 */
@Data
public class BillDetailVM extends UpdatableDTO {


    @ApiModelProperty("第三方支付流水号")
    private String orderNo;

    @ApiModelProperty("商户订单号")
    private String merchantOrderNumber;

    @ApiModelProperty("业务订单号")
    private String productId;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("患者手机号")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String patientMobile;

    @ApiModelProperty("支付金额")
    private Integer paymentAmount;

    @ApiModelProperty("自付支付金额")
    private Integer selfPaymentAmount;

    @ApiModelProperty("医保支付金额")
    private Integer medicarePaymentAmount;

    @ApiModelProperty("退款金额")
    private String refundAmount;

    @ApiModelProperty("自付退款金额")
    private String selfRefundAmount;

    @ApiModelProperty("医保退款金额")
    private String medicareRefundAmount;

    @ApiModelProperty("自费/医保标识 1 自费 0 医保")
    private String selfFlag = "1";

    @ApiModelProperty("服务类型")
    private ProjectTypeEnum billServiceType;

    @ApiModelProperty("支付来源")
    private BillSourceEnum billSource;

    @ApiModelProperty("支付状态")
    private BillPayStatusEnum billPayStatus;

    @ApiModelProperty("支付渠道")
    private BillChannelEnum billChannel;

    @ApiModelProperty("下单时间")
    private Date orderTime;

    @ApiModelProperty("支付时间")
    private Date paymentTime;

    @ApiModelProperty("发起退款时间")
    private Date refundInitiationTime;

    public static BillDetailVM fromPo(Bill bill) {
        BillDetailVM billDetailVM = BeanUtil.copyProperties(bill, BillDetailVM.class);
        billDetailVM.setOrderNo(bill.getTransactionId());
        billDetailVM.setPatientName(bill.getPatientName());
        billDetailVM.setProductId(bill.getOrderNo());
        billDetailVM.setPatientMobile(bill.getPatientMobile());
        billDetailVM.setSelfFlag(BooleanUtils.isTrue(bill.getInsuranceFlag()) ? "0" : "1");
        return billDetailVM;
    }

}
