package cn.taihealth.ih.service.dto.hospital;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.dto.OfflineHospitalDTO;
import cn.taihealth.ih.service.vm.UploadVM;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 */
public class CreateHospitalDTO extends AbstractEntityDTO {

    @ApiParam(name = "标识")
    @NotBlank(message = "标识必填")
    private String code;

    @ApiParam(name = "名称, 必填")
    @NotBlank(message = "名称必填")
    private String name;

    @ApiParam(name = "用户名, 必填")
    @NotBlank(message = "用户名必填")
    private String username;

    @NotNull
    @ApiModelProperty("最短6字符,最长100字符")
    @Size(message = "密码的长度为6-100个字符",
        min = Constants.PASSWORD_MIN_LENGTH,
        max = Constants.PASSWORD_MAX_LENGTH)
    private String password;

    @ApiParam(name = "是否是测试医院")
    @NotNull(message = "是否为测试医院不能为空")
    private Boolean test;

    @ApiParam(name = "是否打开ca")
    //@NotNull(message = "ca是否打开不能为空")
    private Boolean ca = false;

    @ApiParam(name = "实体医院")
    private List<OfflineHospitalDTO> entityHospitals;

    @ApiParam(name = "证书文件(对象内传uploadId), 必填")
//    @NotNull(message = "证书文件必填")
    private UploadVM file;

    @ApiParam(name = "手机号, 必填")
    @NotBlank(message = "手机号必填")
    private String phone;

    @ApiParam(name = "邮箱, 必填")
    @NotBlank(message = "邮箱必填")
    private String email;

    public CreateHospitalDTO() {
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<OfflineHospitalDTO> getEntityHospitals() {
        return entityHospitals;
    }

    public void setEntityHospitals(List<OfflineHospitalDTO> entityHospitals) {
        this.entityHospitals = entityHospitals;
    }

    public UploadVM getFile() {
        return file;
    }

    public void setFile(UploadVM file) {
        this.file = file;
    }

    public Boolean getTest() {
        return test;
    }

    public void setTest(Boolean test) {
        this.test = test;
    }

    public Boolean getCa() {
        return ca;
    }

    public void setCa(Boolean ca) {
        this.ca = ca;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
