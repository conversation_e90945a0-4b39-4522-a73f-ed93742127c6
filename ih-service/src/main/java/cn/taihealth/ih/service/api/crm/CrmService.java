package cn.taihealth.ih.service.api.crm;

import cn.taihealth.ih.bean.CrmSearchBean;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.crm.CrmTaskDetailResult;
import cn.taihealth.ih.domain.enums.StatisticalMethods;
import cn.taihealth.ih.service.dto.crm.CrmTerminateReasonDTO;
import cn.taihealth.ih.service.vm.crm.*;
import cn.taihealth.ih.service.vm.statistics.supervision.NameCount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface CrmService {

    /**
     * 添加终止原因
     * @param hospital
     * @param dto
     * @return
     */
    CrmTerminateReasonDTO addReason(Hospital hospital, CrmTerminateReasonDTO dto);

    /**
     * 修改终止原因, 对于不可编译的原因, 直接描述不进行修改
     * @param hospital
     * @param dto
     * @return
     */
    CrmTerminateReasonDTO saveReason(Hospital hospital, CrmTerminateReasonDTO dto);

    /**
     * 删除终止原因, 对于不可编译的原因, 不进行删除
     * @param hospital
     * @param reasonId
     */
    void deleteReason(Hospital hospital, Long reasonId);

    /**
     * 问卷统计
     * @param searchBean
     * @param pageable
     * @return
     */
    Page<CrmQuestionnaireStatsVM> getQuestionnaireAnswersSourceCount(CrmSearchBean searchBean, Pageable pageable);

    /**
     * 问卷来源统计
     * @param isPlatform
     * @param hospital
     * @param searchBean
     * @return
     */
    List<NameCount> getQuestionnaireAnswersSourceCount(boolean isPlatform, Hospital hospital, CrmSearchBean searchBean);

    /**
     * 问卷详细统计
     * @param isPlatform
     * @param hospital
     * @param searchBean
     * @return
     */
    List<CrmQuestionOptionStatsVM> getQuestionnaireAnswersDetailCount(boolean isPlatform, Hospital hospital, CrmSearchBean searchBean);

    /**
     * 科室问题满意率统计
     * @param isPlatform
     * @param hospital
     * @param searchBean
     * @return
     */
    List<CrmOfflineDeptCountVM> getQuestionAnswersDetailCount(boolean isPlatform, Hospital hospital, CrmSearchBean searchBean, boolean needCount);

    List<CrmOfflineDeptRateRatioVM> getDeptRateRatio(boolean isPlatform, Hospital hospital, CrmSearchBean searchBean,
                                                     StatisticalMethods method, Date startAnswerTime);

    /**
     * 获取回答的问卷
     * @param crmTaskDetail
     * @return
     */
    CrmQuestionnaireVM getAnswerQuestion(CrmTaskDetailResult crmTaskDetail);
}
