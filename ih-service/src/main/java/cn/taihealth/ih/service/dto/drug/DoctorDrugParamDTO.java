package cn.taihealth.ih.service.dto.drug;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.HashSet;
import java.util.Set;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DoctorDrugParamDTO {

    @ApiModelProperty(value = "医生id", hidden = true)
    private Long doctorId;

    @ApiModelProperty("药品字典id list")
    @NotEmpty(message = "药品字典id必传")
    private Set<Long> dictIds = new HashSet<>();

}
