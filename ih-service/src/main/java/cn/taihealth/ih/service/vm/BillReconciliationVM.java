package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.UpdatableEntity;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.hospital.BillBalanceRecord;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.repo.BillBalanceRecordRepository;
import cn.taihealth.ih.repo.ElectronicMedicCardRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gitq.jedi.context.AppContext;
import io.swagger.annotations.ApiModelProperty;
import java.util.Comparator;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 交易对账明细
 */
@Data
public class BillReconciliationVM extends UpdatableDTO {


    @ApiModelProperty("第三方支付流水号")
    private String orderNo;

    @ApiModelProperty("医保结算订单号")
    private String payOrderId;

    @ApiModelProperty("业务订单编号")
    private String productId;

    @ApiModelProperty("商户订单号")
    private String merchantOrderNumber;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("患者手机号")
    @JsonDeserialize(using = PasswordDecodeDeserializer.class)
    @JsonSerialize(using = PasswordEncodeSerializer.class)
    private String patientMobile;

    @ApiModelProperty("商户平台总金额 单位分")
    private Integer merchantTotalAmount;

    @ApiModelProperty("自付商户平台金额 单位分")
    private Integer merchantSelfAmount;

    @ApiModelProperty("医保商户平台金额 单位分-不需要展示，测试对比使用")
    private Integer merchantMedicareAmount;

    @ApiModelProperty("his总金额")
    private Integer hisTotalAmount;

    @ApiModelProperty("自费his金额")
    private Integer hisSelfAmount;

    @ApiModelProperty("医保his金额-不需要展示，测试对比使用")
    private Integer hisMedicareAmount;


    @ApiModelProperty("服务类型")
    private ProjectTypeEnum billServiceType;

    @ApiModelProperty("支付来源")
    private BillSourceEnum billSource;

    @ApiModelProperty("支付状态")
    private BillPayStatusEnum billPayStatus;

    @ApiModelProperty("支付渠道")
    private BillChannelEnum billChannel;

    @ApiModelProperty("交易操作时间")
    private Date orderOperateTime;

    @ApiModelProperty("交易操作类型")
    private BillOperateTypeEnum billOperateType;

    @ApiModelProperty("对账结果")
    private ReconciliationResultEnum reconciliationResult;

    @ApiModelProperty("处理结果 (是否处理)")
    private Boolean solveFlag;

    @ApiModelProperty("是否医保支付")
    private Boolean insuranceFlag;

    @ApiModelProperty("冲正退款类型 MANUAL手动平账 REFUND 退款冲正平账")
    private BillBalanceTypeEnum refundTypeEnum;

    @ApiModelProperty("就诊卡号")
    private String cardNo;

    @ApiModelProperty("HIS院内患者编号")
    private String patId;


    public BillReconciliationVM(Bill po) {
        super(po);
        this.productId = po.getOrderNo();

        this.orderNo = po.getTransactionId();

        this.merchantOrderNumber = po.getMerchantOrderNumber();

        this.patientName = po.getPatientName();

        this.patientMobile = po.getPatientMobile();

        this.merchantTotalAmount = po.getBillOperateType() == BillOperateTypeEnum.PAYMENT ? po.getPaymentAmount() : po.getRefundAmount();
        this.merchantSelfAmount = po.getBillOperateType() == BillOperateTypeEnum.PAYMENT ? po.getSelfPaymentAmount() :
            po.getSelfRefundAmount();
        this.merchantMedicareAmount = po.getBillOperateType() == BillOperateTypeEnum.PAYMENT ? po.getMedicarePaymentAmount() :
            po.getMedicareRefundAmount();

        this.hisTotalAmount = po.getHisAmount();
        this.hisSelfAmount = po.getHisSelfAmount();
        this.hisMedicareAmount = po.getHisMedicareAmount();

        this.billServiceType = po.getBillServiceType();

        this.billSource = po.getBillSource();

        this.billPayStatus = po.getBillPayStatus();

        this.billChannel = po.getBillChannel();

        this.orderOperateTime = po.getOrderOperateTime();

        this.billOperateType = po.getBillOperateType();

        this.reconciliationResult = po.getReconciliationResult();

        this.solveFlag = po.getSolveFlag();

        this.insuranceFlag = BooleanUtils.isTrue(po.getInsuranceFlag());
        //2024年12月25日17:07:40 结算失败不退款  为不影响对账逻辑，凡是平过账的账单一律视为平账，无论重新对账后的结果如何
        List<BillBalanceRecord> billBalanceRecords = AppContext.getInstance(BillBalanceRecordRepository.class)
            .findAllByTransactionId(po.getTransactionId());
        if (CollectionUtils.isNotEmpty(billBalanceRecords)) {
            reconciliationResult = ReconciliationResultEnum.BALANCING;
            // 按 createdDate 降序排序
            billBalanceRecords.stream()
                .max(Comparator.comparing(BillBalanceRecord::getCreatedDate))
                .ifPresent(latestRecord -> this.refundTypeEnum = latestRecord.getBalanceType());
        }
        this.payOrderId = this.insuranceFlag ? po.getPayOrderId() : "-";

        setCardNoAndPatId(po.getBillServiceType(), po.getOrderNo(), po.getPatientId());
    }

    private void setCardNoAndPatId(ProjectTypeEnum type, String orderNo, Long patientId) {
        switch (type) {
            case HOSPITALIZATION_DEPOSIT:
                // 住院缴费
                Patient patient = new Patient();
                patient.setId(patientId);
                List<ElectronicMedicCard> electronicMedicCardList =  AppContext.getInstance(ElectronicMedicCardRepository.class)
                        .findAllByOnlineTypeAndPatient(ElectronicMedicCard.OnlineType.HIS, patient);
                Optional<ElectronicMedicCard> optional = electronicMedicCardList.stream().filter(u -> u.getCardType() == ElectronicMedicCard.CardType.SELF_PAY && u.isEnabled()).findFirst();
                if (optional.isPresent()) {
                    this.cardNo = optional.get().getNumber();
                    this.patId = optional.get().getHisPatid();
                }
                break;
            case OUTPATIENT_PAYMENT:
                // 门诊缴费
                if (StringUtils.isNotEmpty(orderNo)) {
                    List<HisOutpatientCharge> charges = AppContext.getInstance(HisOutpatientChargeRepository.class).findAllByHisOutpatientChargeGroupId(Long.parseLong(orderNo));
                    if (!charges.isEmpty()) {
                        Long electronicMedicCardId = charges.get(0).getElectronicMedicCardId();
                        if (electronicMedicCardId != null) {
                            ElectronicMedicCard medicCard = AppContext.getInstance(ElectronicMedicCardRepository.class).getById(electronicMedicCardId);
                            this.cardNo = medicCard.getNumber();
                            this.patId = medicCard.getHisPatid();
                        }
                    }
                }
                break;
            // 线下挂号
            case APPOINTMENT_REGISTRATION:
                if (StringUtils.isNotEmpty(orderNo)) {
                    Optional<OfflineOrder> offlineOrder = AppContext.getInstance(OfflineOrderRepository.class).findById(Long.parseLong(orderNo));
                    if (offlineOrder.isPresent() && offlineOrder.get().getElectronicMedicCard() != null) {
                        this.cardNo = offlineOrder.get().getElectronicMedicCard().getNumber();
                        this.patId = offlineOrder.get().getElectronicMedicCard().getHisPatid();
                    }
                }
                break;
            default:
        }

    }

}
