package cn.taihealth.ih.service.impl.filter.hisoutpatientchargegroup;

import cn.taihealth.ih.domain.his.HisOutpatientChargeGroup;
import cn.taihealth.ih.service.impl.filter.DateFilter;

/**
 */
public class CreatedDateFilter extends DateFilter<HisOutpatientChargeGroup> {

    public CreatedDateFilter(String param) {
        super("createdDate", param);
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public boolean equals(Object other) {
        if (!(other instanceof CreatedDateFilter)) {
            return false;
        }

        return super.equals(other);
    }

}
