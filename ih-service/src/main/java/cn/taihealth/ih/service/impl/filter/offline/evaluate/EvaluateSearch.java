package cn.taihealth.ih.service.impl.filter.offline.evaluate;

import cn.taihealth.ih.domain.Evaluate;
import cn.taihealth.ih.service.impl.filter.CreatedDateFilter;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import cn.taihealth.ih.service.impl.filter.order.hospitalorder.OrderWorkersPatientSearch;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class EvaluateSearch extends SearchCriteria<Evaluate> {


    public EvaluateSearch() {
        super();
    }

    public static EvaluateSearch of(String query) {
        EvaluateSearch evaluateSearch = new EvaluateSearch();
        evaluateSearch.parse(query);
        return evaluateSearch;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier each : Qualifier.values()) {
            list.add(each.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<Evaluate> createFilter(String input) {
        Matcher matcher = qualifierRegex.matcher(input);
        if (!matcher.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(matcher.group("qualifier").toUpperCase());
        String value = matcher.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }

        switch (qualifier) {
            case EVALUATEDATE:
                return new CreatedDateFilter<>(value);
            case DOCTOR_NAME:
                return new DoctorNameSearch(value);
            case PATIENT_NAME:
                return new PatientNameSearch(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<Evaluate> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        /**
         * 评论时间
         */
        EVALUATEDATE,
        /**
         * 医生名字
         */
        DOCTOR_NAME,
        /**
         * 患者名字
         */
        PATIENT_NAME,
    }
}
