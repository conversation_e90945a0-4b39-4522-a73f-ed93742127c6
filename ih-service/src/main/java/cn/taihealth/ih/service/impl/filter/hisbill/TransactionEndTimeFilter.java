package cn.taihealth.ih.service.impl.filter.hisbill;

import cn.taihealth.ih.domain.hospital.HisBill;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import java.util.Date;
import lombok.Getter;
import org.springframework.data.jpa.domain.Specification;

@Getter
public class TransactionEndTimeFilter implements SearchFilter<HisBill> {
    private final Date date;

    public TransactionEndTimeFilter(Date date) {
        this.date = date;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<HisBill> toSpecification() {
        return Specifications.lt("transaction_time", date);
    }

    @Override
    public String toExpression() {
        String str = " date:" + date;
        return str;
    }

    @Override
    public boolean isValid() {
        return date != null;
    }
}
