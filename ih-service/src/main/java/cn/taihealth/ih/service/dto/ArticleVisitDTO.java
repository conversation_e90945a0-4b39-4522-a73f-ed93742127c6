package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.hospital.ArticleVisit;

import java.util.Date;

/**
 */
public class ArticleVisitDTO extends UpdatableDTO {

    private ArticleDTO article;

    private boolean bookmarked;

    private Date bookmarkDate;

    private boolean liked;

    private Date likedDate;

    public ArticleVisitDTO() {}

    public ArticleVisitDTO(ArticleVisit visit) {
        super(visit);

//        this.user = new UserDTO(visit.getUser());
        this.article = new ArticleDTO(visit.getArticle());
        this.bookmarkDate = visit.getBookmarkDate();
        this.bookmarked = visit.isBookmarked();
        this.liked = visit.isLiked();
        this.likedDate = visit.getLikedDate();
    }

    public ArticleDTO getArticle() {
        return article;
    }

    public void setArticle(ArticleDTO article) {
        this.article = article;
    }

    public boolean isBookmarked() {
        return bookmarked;
    }

    public void setBookmarked(boolean bookmarked) {
        this.bookmarked = bookmarked;
    }

    public Date getBookmarkDate() {
        return bookmarkDate;
    }

    public void setBookmarkDate(Date bookmarkDate) {
        this.bookmarkDate = bookmarkDate;
    }

    public boolean isLiked() {
        return liked;
    }

    public void setLiked(boolean liked) {
        this.liked = liked;
    }

    public Date getLikedDate() {
        return likedDate;
    }

    public void setLikedDate(Date likedDate) {
        this.likedDate = likedDate;
    }
}
