package cn.taihealth.ih.service.database;

import cn.taihealth.ih.domain.crm.CrmQuestionnaireQuestion;
import cn.taihealth.ih.repo.crm.CrmQuestionnaireQuestionRepository;
import cn.taihealth.ih.repo.crm.CrmQuestionnaireRepository;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class Migrator97 {

    public void run() {
        log.info("将2024年03月8日之前的问卷全部改成必填开始");
        CrmQuestionnaireRepository questionnaireRepository = AppContext.getInstance(CrmQuestionnaireRepository.class);
        CrmQuestionnaireQuestionRepository questionnaireQuestionRepository = AppContext.getInstance(CrmQuestionnaireQuestionRepository.class);
        Date date38 = new Date(1709856000000L);
        questionnaireRepository.findAll(Specifications.lt("createdDate", date38))
                        .forEach(questionnaire -> {
                            List<CrmQuestionnaireQuestion> qq = questionnaire.getQuestionnaireQuestions();
                            qq.forEach(u -> u.setMust(true));
                            questionnaireQuestionRepository.saveAll(qq);
                        });

        log.info("将2024年03月8日之前的问卷全部改成必填结束");
    }

}
