package cn.taihealth.ih.service.vm.nodered.nodereddatacontent.appointment.vm;

import cn.taihealth.ih.service.dto.HospitalDictionaryDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class PushDictChangeMsgVM implements Serializable {
    // 渠道编码	Y
    private String request_id;
    // 渠道名称	Y
    private String message;

    public PushDictChangeMsgVM(String request_id) {
        this.request_id = request_id;
    }

}
