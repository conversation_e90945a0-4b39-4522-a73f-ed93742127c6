package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.dict.ExamDevice;
import cn.taihealth.ih.domain.enums.ExamDeviceStatus;
import cn.taihealth.ih.service.dto.hospital.offline.OfflineDeptDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: Moon
 * @Date: 2020/12/18 下午1:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExamDeviceDTO extends UpdatableDTO {

    @ApiModelProperty("设备名称")
    @NotBlank(message = "设备名称不能为空")
    @Size(max = 255, message = "设备名称超长")
    private String name;

    @ApiModelProperty("设备编号")
    @NotBlank(message = "设备编号不能为空")
    @Size(max = 255, message = "设备编号超长")
    private String code;

    @ApiModelProperty("设备地点")
    @NotBlank(message = "检查地点不能为空")
    @Size(max = 255, message = "设备地点超长")
    private String address;

    @ApiModelProperty("检查类别")
    @NotNull(message = "检查类别不能为空")
    private ExamCategoryDTO category;

    @ApiModelProperty("科室")
    @NotNull(message = "科室不能为空")
    private OfflineDeptDTO offlineDept;

    @ApiModelProperty("启动/停用")
    @NotNull(message = "启动/停用状态不能为空")
    private Boolean enabled;

    @ApiModelProperty("设备状态,仅在页面展示时是使用  ENABLED：启用 STOP：停用 MAINTENANCE：维护中")
    private ExamDeviceStatus status = ExamDeviceStatus.ENABLED;

    @ApiModelProperty("设备维护日")
    private List<ExamDeviceMaintenanceDTO> maintenanceList = Lists.newArrayList();

    public ExamDeviceDTO(ExamDevice device) {
        super(device);
        this.name = device.getName();
        this.code = device.getCode();
        this.address = device.getAddress();
        this.category = new ExamCategoryDTO(device.getCategory());
        if (device.getOfflineDept() != null) {
            this.offlineDept = new OfflineDeptDTO(device.getOfflineDept());
        }
        this.enabled = device.isEnabled();
    }
}
