package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.enums.ScheduleModel;
import cn.taihealth.ih.domain.hospital.ExamScheduleGroup;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import cn.taihealth.ih.service.dto.hospital.offline.OfflineDeptDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 检查预约-排班计划VM
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamScheduleGroupVM extends UpdatableDTO {

    @ApiModelProperty("计划名称")
    private String name;

    @ApiModelProperty("创建人")
    private UserVM creator;

    @ApiModelProperty("科室")
    private OfflineDeptDTO offlineDept;

    @ApiModelProperty("模式：循环/单天")
    private ScheduleModel model = ScheduleModel.REPEAT;

    @ApiModelProperty("开始日期")
    private Date startTime;

    @ApiModelProperty("结束日期")
    private Date endTime;

    @ApiModelProperty("循环日 1:周日 7:周一")
    private List<Integer> repeat = Lists.newArrayList();

    @ApiModelProperty("总可约数")
    private Integer count;

    @ApiModelProperty("总已预约数")
    private Integer countUse;

    @ApiModelProperty("当前设备 列表显示时使用")
    private List<ExamDeviceVM> devices = Lists.newArrayList();

    @ApiModelProperty("当前排班设备")
    private List<ExamScheduleVM> examSchedules = Lists.newArrayList();

    @ApiModelProperty("当前计划班次详情")
    private List<ExamScheduleGroupShiftVM> details = Lists.newArrayList();

    public ExamScheduleGroupVM(ExamScheduleGroup group) {
        super(group);
        this.name = group.getName();
        if (group.getCreator() != null) {
            this.creator = new UserVM(group.getCreator());
        }
        if (group.getOfflineDept() != null) {
            this.offlineDept = new OfflineDeptDTO(group.getOfflineDept());
        }
        this.model = group.getModel();
        this.startTime = group.getStartTime();
        this.endTime = group.getEndTime();
        this.repeat = group.getRepeat();
    }

    public ExamScheduleGroupVM(CreateExamScheduleGroupVM vm) {
        this.name = vm.getName();
        this.offlineDept = vm.getOfflineDept();
        this.model = vm.getModel();
        this.startTime = vm.getStartTime();
        this.endTime = vm.getEndTime();
        this.repeat = Arrays.stream(vm.getRepeat()).boxed().collect(Collectors.toList());
    }

}
