package cn.taihealth.ih.service.api.ai;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.dto.ai.*;
import cn.taihealth.ih.service.vm.UploadVM;

import java.util.List;

public interface HBOCService {

    /**
     * 保存家系图数据
     * @param user
     * @param hospital
     * @param conversationId
     * @param pedigreeList
     */
    void savePedigree(User user, Hospital hospital, String conversationId, List<PedigreeDTO> pedigreeList);

    /**
     * 查询家系图
     * @param current
     * @param hospital
     * @return
     */
    List<PedigreeDTO> listPedigree(User current, Hospital hospital);

    /**
     * 保存家系图数据 原始上下文数据(非结构化)
     * @param user
     * @param hospital
     * @param conversationId
     * @param pedigreeContext
     */
    void savePedigreeContext(User user, Hospital hospital, String conversationId, PedigreeContext pedigreeContext);

    /**
     * 保存CRF表单数据
     * @param user
     * @param hospital
     * @param conversationId
     * @param caseReportFormDTO
     */
    void saveCaseReportForm(User user, Hospital hospital, String conversationId, CaseReportFormDTO caseReportFormDTO);

    /**
     * 查询CRF表单列表
     * @param hospital
     * @param type
     * @return
     */
    List<CaseReportFormVM> listCaseReportForm(Hospital hospital, String type);

    /**
     * 导出CRF表单
     * @param hospital
     * @return
     */
    UploadVM exportCaseReportForm(Hospital hospital);
}
