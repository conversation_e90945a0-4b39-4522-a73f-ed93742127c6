package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.service.dto.OfflineHospitalDTO;
import cn.taihealth.ih.service.util.LngLat;
import cn.taihealth.ih.service.vm.offline.MiniAppOfflineHospitalVM;
import cn.taihealth.ih.service.vm.offline.OfflineHospitalAnnualInspectionVM;
import cn.taihealth.ih.service.vm.offline.OfflineHospitalRecommendVM;

import java.util.List;

/**
 */
public interface OfflineHospitalService {

    /**
     * 导入医院, 格式为gz文件, 数据类型为hospital的json格式数组
     * @param upload
     */
    void importHospitals(Upload upload);

    void save(OfflineHospital offlineHospital);

    void delete(OfflineHospital offlineHospital);

    void deleteById(Long id);

    /**
     * 获取有互联网医院的线下医院
     * @param query
     * @return
     */
    List<MiniAppOfflineHospitalVM> findUsedOfflineHospitals(String query, Double longitude, Double latitude);

    /**
     * 添加推荐的线下医院
     * @param vm
     */
    void addRecommendHospital(OfflineHospitalRecommendVM vm);

    void addOfflineHospitalAnnualInspection(OfflineHospitalAnnualInspectionVM vm);

    /**
     * 修改推荐的线下医院
     * @param recommendId
     * @param vm
     */
    void saveRecommendHospital(long recommendId, OfflineHospitalRecommendVM vm);

    /**
     * 删除推荐的线下医院
     * @param recommendId
     */
    void deleteRecommendHospital(long recommendId);

    /**
     * 获取线下医院
     * @param query
     * @param longitude
     * @param latitude
     * @return
     */
    List<OfflineHospitalDTO> getOfflineHospitalList(String query, Double longitude, Double latitude);

    /**
     * 根据经纬度计算距离医院的距离
     * @param hospital
     * @param lngLat
     * @return
     */
    int addressDistance(Hospital hospital, LngLat lngLat);
}
