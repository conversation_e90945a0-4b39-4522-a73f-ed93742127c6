package cn.taihealth.ih.service.vm.platform;

import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.service.vm.DoctorVM;
import cn.taihealth.ih.service.vm.hospital.HospitalVM;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class HospitalDoctorVM extends DoctorVM {

    @ApiModelProperty("医院")
    private HospitalVM hospital;

    public HospitalDoctorVM(MedicalWorker medicalWorker) {
        super(medicalWorker);
        this.hospital = new HospitalVM(medicalWorker.getHospital());
    }

}
