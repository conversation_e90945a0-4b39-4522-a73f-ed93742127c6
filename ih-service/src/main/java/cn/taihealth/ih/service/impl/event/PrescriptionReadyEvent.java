package cn.taihealth.ih.service.impl.event;

import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.domain.hospital.Order;

/**
 * 处方开具成功通知
 * @Author: Moon
 * @Date: 2020/11/4 8:43 下午
 */
public class PrescriptionReadyEvent extends AbstractEntityEvent<PrescriptionOrder> {

    private final Order order;

    public PrescriptionReadyEvent(PrescriptionOrder prescriptionOrder, Order order) {
        super(prescriptionOrder);
        this.order = order;
    }
    public Order getOrder(){
        return order;
    }

}
