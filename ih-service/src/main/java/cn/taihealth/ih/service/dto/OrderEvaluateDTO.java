package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.hospital.OrderWorker;
import cn.taihealth.ih.service.vm.DoctorVM;
import cn.taihealth.ih.service.vm.PatientVM;
import cn.taihealth.ih.service.vm.UserVM;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author: Moon
 * @Date: 2020/10/27 1:35 下午
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
@Accessors(chain = true)
public class OrderEvaluateDTO extends UpdatableDTO {

    public enum EvaluateFor {
        ORDER,
        DOCTOR,
        NURSE
    }

    @ApiModelProperty("评价星级")
    @NotNull(message = "评价不能为空")
    @Range(min = 0, max = 5, message = "评级不规范（1-5星）")
    private Integer rating;

    @ApiModelProperty("评价对象，DOCTOR 医生，NURSE 护士，ORDER 订单")
    private EvaluateFor evaluateFor = EvaluateFor.DOCTOR;

    @ApiModelProperty("评价内容")
    @Type(type = "org.hibernate.type.TextType")
    private String review;

    @ApiModelProperty("评价标签")
    private List<String> evaluateTag;

    @ApiModelProperty("评价日期")
    private Date evaluateDate;

    @ApiModelProperty("评价人")
    private UserVM user;

    @ApiModelProperty("就诊人信息")
    private PatientVM patient;

    @ApiModelProperty("被评价的医生")
    private DoctorVM doctor;

    @ApiModelProperty("用户端是否可见")
    private Boolean isShow = true;

    public OrderEvaluateDTO(OrderWorker orderWorker) {
        super(orderWorker);
        this.rating = orderWorker.getRating();
        this.review = orderWorker.getReview();
        this.evaluateDate = orderWorker.getEvaluateDate();
//        if (orderWorker.getUser().getAuthority() == Authority.DOCTOR) {
//            this.evaluateFor = EvaluateFor.DOCTOR;
//        }
        if (StringUtils.isNotBlank(orderWorker.getEvaluateTag())) {
            evaluateTag = JSONArray.parseArray(orderWorker.getEvaluateTag(), String.class);
        }
        doctor = new DoctorVM(orderWorker.getOrder().getDoctor(), false);
        user = new UserVM(orderWorker.getOrder().getUser());
        patient = new PatientVM(orderWorker.getOrder().getPatient(), null);
        isShow = orderWorker.isShowed();
    }
}
