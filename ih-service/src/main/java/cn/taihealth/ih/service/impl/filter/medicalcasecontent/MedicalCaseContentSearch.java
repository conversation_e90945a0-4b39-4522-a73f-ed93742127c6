package cn.taihealth.ih.service.impl.filter.medicalcasecontent;

import cn.taihealth.ih.domain.hospital.MedicalCaseContent;
import cn.taihealth.ih.service.impl.filter.SearchCriteria;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;

public class MedicalCaseContentSearch extends SearchCriteria<MedicalCaseContent> {

    public static MedicalCaseContentSearch of(String query) {
        MedicalCaseContentSearch search = new MedicalCaseContentSearch();
        search.parse(query);
        return search;
    }

    @Override
    protected List<String> getQualifiers() {
        List<String> list = Lists.newArrayList();
        for (Qualifier q : Qualifier.values()) {
            list.add(q.name().toLowerCase());
        }
        return list;
    }

    @Override
    protected SearchFilter<MedicalCaseContent> createFilter(String input) {
        Matcher m = qualifierRegex.matcher(input);
        if (!m.matches()) {
            return null;
        }

        Qualifier qualifier = Qualifier.valueOf(m.group("qualifier").toUpperCase());

        boolean not = input.startsWith("-");
        String value = m.group("value");
        if (Strings.isNullOrEmpty(value)) {
            return null;
        }

        switch (qualifier) {
            case CONTENT:
                return new ContentFilter(value);
            case SORT:
                return new SortFilter(value);
            default:
                return null;
        }
    }

    @Override
    protected SearchFilter<MedicalCaseContent> createContainFilter(Set<String> contains) {
        return null;
    }

    enum Qualifier {
        /**
         * 病历内容
         */
        CONTENT,
        /**
         * 排序
         */
        SORT
    }
}
