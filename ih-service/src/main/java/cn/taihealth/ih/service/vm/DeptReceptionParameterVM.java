package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.service.dto.DeptDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 */
@Data
@ToString(exclude = {"dept"})
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
@ApiModel
public class DeptReceptionParameterVM {

    private DeptDTO dept;

    @ApiModelProperty("平均接诊时长")
    @Min(value = 0, message = "平均接诊时长不能小于0")
    private int treatmentDuration;

    @ApiModelProperty("挂号费用(在线急诊 单位: 分, 默认25元)")
    @Min(value = 0, message = "费用不能小于0")
    private int emergencyFee = 2500;

    @ApiModelProperty("在线门诊挂号费用 单位：分")
    @Min(value = 0, message = "在线门诊挂号费用不能小于0")
    private int returnVisitFee = 2500;

    @ApiModelProperty("在线视频问诊费用 单位：分")
    @Min(value = 0, message = "在线视频问诊费用不能小于0")
    private int videoReturnVisitFee = 2500;

    @ApiModelProperty("电话问诊费用 单位：分")
    @Min(value = 0, message = "电话问诊费用不能小于0")
    private int phoneReturnVisitFee = 2500;

    @ApiModelProperty("在线视频咨询挂号费用 单位：分")
    @Min(value = 0, message = "在线视频咨询挂号费用不能小于0")
    private Integer videoConsultFee;

    @ApiModelProperty("在线图文咨询挂号费用 单位：分")
    @Min(value = 0, message = "在线图文咨询挂号费用不能小于0")
    private Integer graphicConsultFee;

    @ApiModelProperty("挂号费用(线下普通号 单位: 分, 默认25元)")
    @Min(value = 0, message = "费用不能小于0")
    private int offlineGeneralFee = 2500;


    @ApiModelProperty("是否开启在线视频咨询服务")
    private Boolean videoConsultEnabled = false;

    @ApiModelProperty("是否开启在线图文咨询服务")
    private Boolean graphicConsultEnabled = false;

    @ApiModelProperty("是否开启在线图文问诊服务")
    private Boolean returnVisitEnabled = false;

    @ApiModelProperty("是否开启在线视频问诊服务")
    private Boolean videoReturnVisitEnabled = false;

    @ApiModelProperty("是否开启电话问诊服务")
    private Boolean phoneReturnVisitEnabled = false;

    public DeptReceptionParameterVM(Dept dept) {
        this.dept = new DeptDTO(dept);
        this.treatmentDuration = dept.getTreatmentDuration() == null ? 0 : dept.getTreatmentDuration();
        this.emergencyFee = dept.getEmergencyFee();
        this.offlineGeneralFee = dept.getOfflineGeneralFee();

        this.returnVisitFee = dept.getReturnVisitFee();
        this.videoReturnVisitFee = dept.getVideoReturnVisitFee();
        this.phoneReturnVisitFee = dept.getPhoneReturnVisitFee();
        this.graphicConsultFee = dept.getGraphicConsultFee();
        this.videoConsultFee = dept.getVideoConsultFee();
        this.returnVisitEnabled = dept.getReturnVisitEnabled();
        this.videoReturnVisitEnabled = dept.getVideoReturnVisitEnabled();
        this.phoneReturnVisitEnabled = dept.getPhoneReturnVisitEnabled();
        this.graphicConsultEnabled = dept.getGraphicConsultEnabled();
        this.videoConsultEnabled = dept.getVideoConsultEnabled();

    }

}
