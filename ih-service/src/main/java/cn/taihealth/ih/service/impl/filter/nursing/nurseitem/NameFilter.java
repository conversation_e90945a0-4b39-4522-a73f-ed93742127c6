package cn.taihealth.ih.service.impl.filter.nursing.nurseitem;

import cn.taihealth.ih.domain.nursing.NurseItem;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.base.Strings;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;

/**
 *
 */
public class NameFilter implements SearchFilter<NurseItem> {

    private final String pattern;

    public NameFilter(String pattern) {
        this.pattern = pattern;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<NurseItem> toSpecification() {
        return Specifications.likeIgnoreCase("name", pattern);
    }

    @Override
    public String toExpression() {
        return "name:" + pattern;
    }

    @Override
    public boolean isValid() {
        return !Strings.isNullOrEmpty(pattern);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pattern);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof NameFilter)) {
            return false;
        }

        NameFilter rhs = (NameFilter) other;
        return Objects.equals(pattern, rhs.pattern);
    }
}
