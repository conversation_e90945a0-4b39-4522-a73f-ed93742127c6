package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.cloud.SupervisePlatform;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
@Accessors(chain = true) // 链式编程写法
public class SupervisePlatformDTO extends UpdatableDTO {

    @ApiModelProperty(value = "监管平台名称最长255个字符", required = true)
    @NotBlank(message = "监管平台名称不能为空")
    @Size(max = 255, message = "监管平台名称最长255个字符")
    private String name;

    @ApiModelProperty(value = "访问地址", required = true)
    @NotBlank(message = "访问地址不能为空")
    @Size(max = 2000, message = "访问地址最长2000个字符")
    private String webUrl;


    public SupervisePlatformDTO(SupervisePlatform supervisePlatform) {
        super(supervisePlatform);
        this.name = supervisePlatform.getName();
        this.webUrl = supervisePlatform.getWebUrl();
    }

}
