package cn.taihealth.ih.service.vm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@ApiModel(description = "小程序授权参数模型-新")
@Data
public class MiniAuthVM {


    @ApiModelProperty("appId")
    @NotBlank(message = "appId不可为空")
    private String appId;

    @ApiModelProperty("code")
    @NotBlank(message = "code不可为空")
    private String code;

    @ApiModelProperty("jsCode")
    @NotBlank(message = "jsCode不可为空")
    private String jsCode;

    @ApiModelProperty("phoneEncryptedData")
    @NotBlank(message = "phoneEncryptedData不可为空")
    private String phoneEncryptedData;

    @ApiModelProperty("phoneIv")
    @NotBlank(message = "phoneIv不可为空")
    private String phoneIv;

}
