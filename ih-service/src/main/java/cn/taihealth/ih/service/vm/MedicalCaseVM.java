package cn.taihealth.ih.service.vm;

import cn.taihealth.ih.domain.MedicalCase;
import cn.taihealth.ih.domain.MedicalCase.CreateType;
import cn.taihealth.ih.service.dto.AbstractEntityDTO;
import cn.taihealth.ih.service.dto.MedicalCaseDiseaseDTO;
import cn.taihealth.ih.service.dto.PatientDTO;
import cn.taihealth.ih.service.dto.UpdatableDTO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ToString(exclude = {"order", "patient"})
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class MedicalCaseVM extends UpdatableDTO {

    @ApiModelProperty("疾病，医生写病历使用diseaseId，举例diseases: [{id:diseaseId1}, {id:diseaseId2}]，"
        + "\t查询时会返回id(这里的id不是疾病id)、diseaseName和diseaseCode")
    @NotEmpty(message = "诊断不能为空")
    private List<MedicalCaseDiseaseDTO> diseases = Lists.newArrayList();

    @ApiModelProperty(value = "诊断日期")
    private Date startTime;

    @ApiModelProperty("病历内容")
    @Size(max = 500, message = " 病历内容最长500个字符")
    private String diagnosis;

    @ApiModelProperty("问诊小结")
    @Size(max = 500, message = " 问诊小结最长500个字符")
    private String summary;

    @ApiModelProperty("订单（仅含id）")
    private AbstractEntityDTO order = new AbstractEntityDTO();

    @ApiModelProperty("患者")
    private PatientDTO patient;

    @ApiModelProperty("医生")
    private DoctorVM doctor;

    @ApiModelProperty(value = "主诉")
    @Size(max = 255, message = "主诉最长255个字符")
    private String selfSpeak;
//
//    @ApiModelProperty("现病史")
//    @Size(max = 255, message = "现病史最长255个字符")
//    @NotBlank(message = "现病史不能为空")
//    private String nowMedicalHistory;
//
//    @ApiModelProperty("既往史")
//    @Size(max = 255, message = " 既往史最长255个字符")
//    @NotBlank(message = "既往史不能为空")
//    private String oldMedicalHistory;
//
//    @ApiModelProperty("过敏史")
//    @Size(max = 255, message = " 过敏史最长255个字符")
//    @NotBlank(message = "过敏史不能为空")
//    private String allergiesHistory;
//
//    @ApiModelProperty("体格检查")
//    @Size(max = 2000, message = " 体格检查最长2000个字符")
//    private String checking;


    @ApiModelProperty("添加类型，个人或者是医生")
    private CreateType createType = CreateType.DOCTOR;

    @ApiModelProperty("是否已发送给患者")
    private boolean sendUser;


    public MedicalCaseVM(MedicalCase medicalCase) {
        super(medicalCase);
//        selfSpeak = medicalCase.getSelfSpeak();
//        nowMedicalHistory = medicalCase.getNowMedicalHistory();
//        oldMedicalHistory = medicalCase.getOldMedicalHistory();
//        allergiesHistory = medicalCase.getAllergiesHistory();
//        checking = medicalCase.getChecking();
        diagnosis = medicalCase.getDiagnosis();
        startTime = medicalCase.getStartTime();
        order.setId(medicalCase.getOrder().getId());
        patient = new PatientDTO(medicalCase.getPatient(), null);
        createType = medicalCase.getCreateType();
        sendUser = medicalCase.isSendUser();
        doctor = new DoctorVM(medicalCase.getOrder().getDoctor());
        diseases = medicalCase.getDiseases()
            .stream()
            .map(MedicalCaseDiseaseDTO::new)
            .collect(Collectors.toList());
        summary = medicalCase.getSummary();
        order = new AbstractEntityDTO(medicalCase.getOrder());
    }

    public MedicalCase toEntityCase(MedicalCase mc) {
        mc.setSelfSpeak(selfSpeak);
//        mc.setNowMedicalHistory(nowMedicalHistory);
//        mc.setOldMedicalHistory(oldMedicalHistory);
//        mc.setAllergiesHistory(allergiesHistory);
//        mc.setChecking(checking);
        mc.setDiagnosis(diagnosis);
        mc.setStartTime(startTime);
        mc.setSendUser(sendUser);
        mc.setSummary(summary);
        // mc.setReturnVisit(this.returnVisit);
        return mc;
    }
}
