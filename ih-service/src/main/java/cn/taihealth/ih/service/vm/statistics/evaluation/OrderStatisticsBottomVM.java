package cn.taihealth.ih.service.vm.statistics.evaluation;

import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

@Data
public class OrderStatisticsBottomVM {

    private FormAnalysis formAnalysis;

    private List<OrderChart> orderChartList = Lists.newArrayList();
    private List<CompleteChart> completeCharts = Lists.newArrayList();
    private List<RefundedChart> refundedCharts = Lists.newArrayList();
    private List<PrescriptionChart> prescriptionCharts = Lists.newArrayList();
    private List<VideoRefundedStatus> videoRefundedStatusList = Lists.newArrayList();
    private List<GraphicRefundedStatus> graphicRefundedStatusList = Lists.newArrayList();
    private List<GraphicTop10> graphicTop10List = Lists.newArrayList();
    private List<VideoTop10> videoTop10List = Lists.newArrayList();

    @Data
    public static class FormAnalysis{
        //图文问诊订单总数
        @ApiModelProperty("图文问诊订单总数")
        private Integer graphicOrderCount;

        //视频问诊订单总数
        @ApiModelProperty("视频问诊订单总数")
        private Integer videoOrderCount;

        //图文退单总数
        @ApiModelProperty("图文退单总数")
        private Integer graphicRefundCount;

        //视频退单总数
        @ApiModelProperty("视频退单总数")
        private Integer videoRefundCount;

        //图文开具处方总数
        @ApiModelProperty("图文开具处方总数")
        private Integer graphicPrescriptionCount;

        //视频开具处方总数
        @ApiModelProperty("视频开具处方总数")
        private Integer videoPrescriptionCount;

        //图文处方支付总数
        @ApiModelProperty("图文处方支付总数")
        private Integer graphicPrescriptionPaymentCount;

        //视频处方支付总数
        @ApiModelProperty("视频处方支付总数")
        private Integer videoPrescriptionPaymentCount;

        //图文完成数订单
        @ApiModelProperty("图文完成数订单")
        private Integer graphicCompletedOrderCount;

        //视频完成数订单
        @ApiModelProperty("视频完成数订单")
        private Integer videoCompletedOrderCount;

        //图文接诊数
        @ApiModelProperty("图文接诊数")
        private Integer graphicDiagnosisCount;

        //视频接诊数
        @ApiModelProperty("视频接诊数")
        private Integer videoDiagnosisCount;

        //图文当日订单当日完成数
        @ApiModelProperty("图文当日订单当日完成数")
        private Integer graphicDailyCompletedOrderCount;

        //视频当日订单当日完成数
        @ApiModelProperty("视频当日订单当日完成数")
        private Integer videoDailyCompletedOrderCount;

        //图文当日订单当日接诊数
        @ApiModelProperty("图文当日订单当日接诊数")
        private Integer graphicDailyDiagnosisCount;

        //视频当日订单当日接诊数
        @ApiModelProperty("视频当日订单当日接诊数")
        private Integer videoDailyDiagnosisCount;
    }

    @Data
    public static class OrderChart{
        //统计时间
        @ApiModelProperty("统计时间")
        private LocalDate statisticsDate;

        //图文订单总数
        @ApiModelProperty("图文订单总数")
        private Integer graphicOrderCount;

        //视频订单总数
        @ApiModelProperty("视频订单总数")
        private Integer videoOrderCount;
    }

    @Data
    public static class CompleteChart{
        //统计时间
        @ApiModelProperty("统计时间")
        private LocalDate statisticsDate;

        //图文订单总数
        @ApiModelProperty("图文订单完成总数")
        private Integer graphicCompleteCount;

        //视频订单总数
        @ApiModelProperty("视频订单完成总数")
        private Integer videoCompleteCount;
    }

    @Data
    public static class RefundedChart{
        //统计时间
        @ApiModelProperty("统计时间")
        private LocalDate statisticsDate;

        //图文订单总数
        @ApiModelProperty("图文订单退款总数")
        private Integer graphicRefundedCount;

        //视频订单总数
        @ApiModelProperty("视频订单退款总数")
        private Integer videoRefundedCount;
    }



    @Data
    public static class PrescriptionChart{
        //统计时间
        @ApiModelProperty("统计时间")
        private LocalDate statisticsDate;

        //图文订单总数
        @ApiModelProperty("处方总数")
        private Integer prescriptionCount;

        //视频订单总数
        @ApiModelProperty("处方支付总数")
        private Integer prescriptionPaidCount;
    }

    @Data
    public static class VideoRefundedStatus{
        //统计时间
        @ApiModelProperty("退单状态")
        private OrderStatus name;

        //图文订单总数
        @ApiModelProperty("退单状态对应值")
        private Integer value;
    }

    @Data
    public static class GraphicRefundedStatus{
        //统计时间
        @ApiModelProperty("退单状态")
        private OrderStatus name;

        //图文订单总数
        @ApiModelProperty("退单状态对应值")
        private Integer value;
    }

    @Data
    public static class GraphicTop10{
        @ApiModelProperty("名字")
        private String doctorName;

        @ApiModelProperty("问诊量")
        private Integer count;
    }

    @Data
    public static class VideoTop10{
        @ApiModelProperty("名字")
        private String doctorName;

        @ApiModelProperty("问诊量")
        private Integer count;
    }

}
