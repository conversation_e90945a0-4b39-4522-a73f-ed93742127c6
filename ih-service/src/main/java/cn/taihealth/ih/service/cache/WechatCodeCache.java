package cn.taihealth.ih.service.cache;

import cn.taihealth.ih.wechat.service.vm.wechat.IhWxMpOAuth2AccessToken;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nullable;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

@Component
public class WechatCodeCache {

    private final RMapCache<String, IhWxMpOAuth2AccessToken> mapCache;

    public WechatCodeCache(RedissonClient redisson) {
        this.mapCache = redisson.getMapCache(WechatCodeCache.class.getName());
    }

    public void putToken(String code, IhWxMpOAuth2AccessToken token) {
        mapCache.fastPutIfAbsent(code, token, 10, TimeUnit.MINUTES);
    }

    public @Nullable IhWxMpOAuth2AccessToken getToken(String code) {
        return mapCache.get(code);
    }
}
