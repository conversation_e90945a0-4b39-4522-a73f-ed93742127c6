package cn.taihealth.ih.service.vm.ca;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class PdfTicketVM {

    @JsonIgnore
    @ApiModelProperty("订单id")
    private long orderId;

    @ApiModelProperty("处方单id, 不是处方的不需要填写")
    private long prescriptionOrderId;

    @ApiModelProperty("签署成功后ca返回的的PDF地址")
    private String url;

    @ApiModelProperty("pdf摘要值")
    private String digestValue;

}
