package cn.taihealth.ih.service.impl.sms;

import cn.taihealth.ih.commons.security.TokenValidator;
import cn.taihealth.ih.domain.SMSCode;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.SettingKey;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.service.cache.SMSCodeCache;
import cn.taihealth.ih.service.util.SettingsHelper;
import com.gitq.jedi.context.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

@Service("smsTokenValidator")
@Transactional
@Slf4j
public class SMSTokenValidator implements TokenValidator {

    private final SMSCodeCache smsCodeCache;

    public SMSTokenValidator(SMSCodeCache smsCodeCache) {
        this.smsCodeCache = smsCodeCache;
    }

    @Override
    public boolean validate(String mobile, String smsCode, String type) {
        SMSCode.CodeType tokenType = SMSCode.CodeType.valueOf(type.toUpperCase());
        switch (tokenType) {
            case SIGNUP:
            case RESET_PASSWORD:
            case LOGIN:
            case WECHAT:
                return validateBySender(false, AppContext.getInstance(UserService.class).getSystem(), mobile, smsCode, tokenType);
            case RESET_HEALTH_RECORD_PASSWORD:
            case RESET_MOBILE:
            case ADD_PATIENT:
                return validateBySender(false, CurrentUser.getOrThrow(), mobile, smsCode, tokenType);
            default:
                return false;
        }
    }

    @Override
    public boolean validateByHospital(boolean isTestHospital, String mobile, String smsCode, String type) {
        SMSCode.CodeType tokenType = SMSCode.CodeType.valueOf(type.toUpperCase());
        switch (tokenType) {
            case SIGNUP:
            case RESET_PASSWORD:
            case LOGIN:
            case WECHAT:
                return validateBySender(isTestHospital, AppContext.getInstance(UserService.class).getSystem(), mobile, smsCode, tokenType);
            case RESET_HEALTH_RECORD_PASSWORD:
            case RESET_MOBILE:
            case ADD_PATIENT:
                return validateBySender(isTestHospital, CurrentUser.getOrThrow(), mobile, smsCode, tokenType);
            default:
                return false;
        }
    }

    private boolean validateBySender(boolean isTestHospital, User sender, String mobile, String smsCode, SMSCode.CodeType type) {
        if (validateTestMobile(isTestHospital, mobile, smsCode)) {
            return true;
        }
        SMSCode smsToken = smsCodeCache.getToken(sender.getMobile(), mobile, type);
        if (smsToken != null) {
            boolean v = Objects.equals(smsCode, smsToken.getToken());
            if (!v) {
                log.info("验证码不正确: " + mobile + "," + type + "," + smsCode + ", 缓存中的验证码: " + smsToken.getToken());
            }
            return v;
        } else {
            log.info("验证码不存在: " + mobile + "," + type);
            return false;
        }
    }

    private boolean validateTestMobile(boolean isTestHospital, String mobile, String smsCode) {
        if (isTestHospital) {
            boolean validate = Objects.equals(SettingsHelper.getString(SettingKey.SMS_TEST_MOBILE_TOKEN), smsCode);
            if (Objects.equals(SettingsHelper.getString(SettingKey.SMS_TEST_MOBILE_TOKEN), smsCode)) {
                String senderMobile = AppContext.getInstance(UserService.class).getSystem().getMobile();
                SMSCode token = new SMSCode(senderMobile, mobile, SMSCode.CodeType.LOGIN, smsCode);
                smsCodeCache.putToken(senderMobile, mobile, SMSCode.CodeType.LOGIN, token);
            }
            return validate;
        } else {
            Optional<User> userOptional = AppContext.getInstance(UserCacheFindService.class).findOneByMobile(mobile);
            return userOptional.isPresent() && userOptional.get().getUserType() == User.UserType.TEST
                    && Objects.equals(SettingsHelper.getString(SettingKey.SMS_TEST_MOBILE_TOKEN), smsCode);
        }
    }
}
