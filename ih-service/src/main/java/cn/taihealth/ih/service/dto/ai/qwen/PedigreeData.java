package cn.taihealth.ih.service.dto.ai.qwen;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PedigreeData implements Serializable {

    @ApiModelProperty("成员虚拟ID")
    private Integer id;

    @ApiModelProperty("是否先证者")
    private boolean proband;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("疾病类型")
    private String cancer;

    @ApiModelProperty("发病年龄")
    private Integer age_at_diagnosis;

    @ApiModelProperty("是否死亡")
    private boolean deceased;

    @ApiModelProperty("死亡年龄")
    private Integer death_age_at;

    @ApiModelProperty("当前年龄")
    private Integer current_age;

    @ApiModelProperty("配偶")
    private Integer spouse_id;

    @ApiModelProperty("子女")
    private List<Integer> children_ids;

    @ApiModelProperty("父母")
    private List<Integer> parent_ids;

    @ApiModelProperty("兄弟姐妹")
    private List<Integer> siblings_ids;
}
