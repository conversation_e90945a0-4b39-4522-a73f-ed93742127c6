package cn.taihealth.ih.service.dto;

import cn.taihealth.ih.domain.OfflineMedicalRecords;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.constraints.Past;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 线下处方记录表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class OfflineMedicalRecordsDTO extends AbstractEntityDTO {

    @ApiModelProperty(value = "处方单药物", required = true)
    private String content;

    @ApiModelProperty(value = "处方单时间", required = true)
    @Past(message = "处方单时间不能超过当前时间")
    private Date prescriptionTime;

    @ApiModelProperty(value = "就诊人, 只有id", required = true)
    private AbstractEntityDTO patient;

    @ApiModelProperty(value = "上传处方单")
    private List<UploadDTO> upload = Lists.newArrayList();

    @ApiModelProperty(value = "备注")
    private String remarks;

    public OfflineMedicalRecordsDTO(OfflineMedicalRecords offline) {
        super(offline);
        content = offline.getContent();
        patient = new AbstractEntityDTO(offline.getPatient());
        prescriptionTime = offline.getPrescriptionTime();
        upload = offline.getUpload().stream().map(UploadDTO::new).collect(Collectors.toList());
        remarks = offline.getRemarks();
    }

    public OfflineMedicalRecords toEntity(OfflineMedicalRecords offlines) {
        if (null == offlines) {
            offlines = new OfflineMedicalRecords();
        }
        //offlines.setId(this.getId());
        offlines.setContent(content);
        offlines.setPrescriptionTime(prescriptionTime);
        // offlines.setUpload(this.upload());
        offlines.setRemarks(remarks);
        return offlines;
    }

}
