package cn.taihealth.ih.service.util;

import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.common.web.UrlUtils;
import com.gitq.jedi.context.AppContext;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

import java.io.IOException;

@Slf4j
public class ReportImageUtil {

    /**
     * 创建pdf
     * @param orderId
     * @param h5Url
     * @return
     */
    public static String createPDF(String hospitalCode, Long orderId, String h5Url) {
        ApplicationProperties.PdfReport pdfReport = AppContext.getInstance(ApplicationProperties.class).getPdfReport();
        String url = pdfReport.getUrl();
        String path = pdfReport.getPath();
        CreateReportRequest request = new CreateReportRequest();
        request.setFileName(orderId + "");
        String h5FullUrl = UrlUtils.concatSegments(h5Url, "hospcode-" + hospitalCode + "/out/visitimrecord", orderId + "");
        request.setUrl(h5FullUrl);

        try {
            log.info("pdf生成开始: orderId" + orderId);
            Response response = OkHttpUtils.post(url, StandardObjectMapper.stringify(request));
            String json = OkHttpUtils.getResponseBody(response).orElse(null);
            ReportResponse result = StandardObjectMapper.readValue(json, new TypeReference<>() {});
            log.info("pdf生成完毕: " + json);
            return UrlUtils.concatSegments(path, result.getPdfPath());
//            String imageUrl = "https://img2.baidu.com/it/u=98125250,1396568609&fm=253&fmt=auto&app=138&f=JPEG?w=420&h=420";
        } catch (IOException e) {
            throw new RuntimeException("生成pdf报告失败 orderId: " + orderId, e);
        }
    }

    @Getter
    @Setter
    public static class CreateReportRequest {

        private String url;
        private String fileName;

    }

    @Getter
    @Setter
    public static class ReportResponse {

        private String pdfPath;

    }
}
