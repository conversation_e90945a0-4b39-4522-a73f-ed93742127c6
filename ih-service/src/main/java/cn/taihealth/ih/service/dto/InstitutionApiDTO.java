package cn.taihealth.ih.service.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
@Accessors(chain = true) // 链式编程写法
public class InstitutionApiDTO {

    @ApiModelProperty(value = "邮箱", required = true)
    @NotBlank(message = "邮箱不能为空")
    public String emailOrMobile;

    @ApiModelProperty(value = "医院名称", required = true)
    @NotBlank(message = "医院名称不能为空")
    public String name;

    @ApiModelProperty(value = "证件类型：0多证,1多证合一", required = true)
    @NotNull
    public Integer identificationType;

    @ApiModelProperty(value = "组织类型 0企业,1事业单位", required = true)
    @NotNull
    public Integer organizationType;

    @ApiModelProperty(value = "营业执照号或事业单位事证号或统一社会信用代码", required = true)
    @NotNull
    public String organizationRegNo;

    @ApiModelProperty(value = "营业执照号扫描件路径", required = true)
    @NotNull
    public String pathname;

    @ApiModelProperty(value = "营业执照号扫描件名称，营业执照扫描件大小不能超过2MB", required = true)
    @NotNull
    public String filename;

    @ApiModelProperty(value = "法人姓名", required = true)
    @NotNull
    public String legalName;

    @ApiModelProperty(value = "法人身份证号", required = true)
    @NotNull
    public String legalIdentityCard;

    @ApiModelProperty(value = "法人电话", required = true)
    @NotNull
    public String legalMobile;


}
