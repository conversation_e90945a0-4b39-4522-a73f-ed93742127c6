package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.enums.MedicalWorkerSettingKey;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.List;

/**
 */
public interface MedicalWorkerSettingService {

    Object getValue(MedicalWorker medicalWorker, MedicalWorkerSettingKey key);

    <T> T getValue(MedicalWorker medicalWorker, MedicalWorkerSettingKey key, Class<T> klass);

    <T> T getValue(MedicalWorker medicalWorker, MedicalWorkerSettingKey key, TypeReference<T> type);

    String getString(MedicalWorker medicalWorker, MedicalWorkerSettingKey key);

    int getInt(MedicalWorker medicalWorker, MedicalWorkerSettingKey key);

    long getLong(MedicalWorker medicalWorker, MedicalWorkerSettingKey key);

    boolean getBoolean(MedicalWorker medicalWorker, MedicalWorkerSettingKey key);

    List<String> getListString(MedicalWorker medicalWorker, MedicalWorkerSettingKey key);

    <T> void save(MedicalWorker medicalWorker, MedicalWorkerSettingKey key, T value);

    void delete(MedicalWorker medicalWorker, MedicalWorkerSettingKey key);

}
