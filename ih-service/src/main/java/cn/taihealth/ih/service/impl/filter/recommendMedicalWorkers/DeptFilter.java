package cn.taihealth.ih.service.impl.filter.recommendMedicalWorkers;

import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.DeptMedicalWorker;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.RecommendMedicalWorker;
import cn.taihealth.ih.service.impl.filter.SearchFilter;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.ListJoin;
import java.util.Objects;

public class DeptFilter implements SearchFilter<RecommendMedicalWorker> {
    private final Long deptId;

    public DeptFilter(Long deptId) {
        this.deptId = deptId;
    }

    @Override
    public String getName() {
        return getClass().getName();
    }

    @Override
    public Specification<RecommendMedicalWorker> toSpecification() {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Join<RecommendMedicalWorker, MedicalWorker> rel = root.join("medicalWorker", JoinType.INNER);
            ListJoin<MedicalWorker, DeptMedicalWorker> med = rel.joinList("deptMedicalWorkers", JoinType.INNER);
//            Join<DeptMedicalWorker, Dept> deptWorker = med.join("dept", JoinType.INNER);
            return criteriaBuilder.equal(med.get("dept").get("id"), deptId);
        };
    }

    @Override
    public String toExpression() {
        return "deptId:" + deptId;
    }

    @Override
    public boolean isValid() {
        return deptId != null && deptId != 0;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (!(other instanceof DeptFilter)) {
            return false;
        }

        DeptFilter rhs = (DeptFilter) other;
        return Objects.equals(deptId, rhs.deptId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(deptId);
    }
}
