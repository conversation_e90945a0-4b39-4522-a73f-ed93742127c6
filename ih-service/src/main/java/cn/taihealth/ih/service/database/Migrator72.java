package cn.taihealth.ih.service.database;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.crm.CrmQuestion;
import cn.taihealth.ih.domain.enums.CrmType;
import cn.taihealth.ih.domain.enums.QuestionType;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.crm.CrmQuestionRepository;
import cn.taihealth.ih.service.api.UserService;
import com.gitq.jedi.context.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class Migrator72 {

    public void run() {
        log.info("Migrator72...增加一条默认问题");
        CrmQuestionRepository crmQuestionRepository = AppContext.getInstance(CrmQuestionRepository.class);
        HospitalRepository hospitalRepository = AppContext.getInstance(HospitalRepository.class);
        for (Hospital hospital : hospitalRepository.findAll()) {
            CrmQuestion crmQuestion = new CrmQuestion();
            crmQuestion.setContent("评价者姓名");
            crmQuestion.setCrmType(CrmType.OTHER);
            crmQuestion.setQuestionType(QuestionType.ANSWER);
            crmQuestion.setCreator(AppContext.getInstance(UserService.class).getSystem());
            crmQuestion.setHospital(hospital);
            crmQuestion.setDefaulted(true);
            crmQuestionRepository.save(crmQuestion);
        }
        log.info("Migrator72...增加一条默认问题 finish");
    }

}
