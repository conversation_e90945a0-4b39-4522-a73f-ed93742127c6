package cn.taihealth.ih.service.database;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Patient.Relationship;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.service.dto.UserDTO;
import com.gitq.jedi.context.AppContext;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 将本人关系就诊人的生日，性别同步到用户信息中
 */
@Component
@Slf4j
public class Migrator31 {

    public void run() {
        SpecialDiseasePlatformManagement();
    }

    @Transactional
    public void SpecialDiseasePlatformManagement() {
        log.info("migrator31....");

        PatientRepository patientRepository = AppContext.getInstance(PatientRepository.class);
        List<Patient> patients = patientRepository.findAllByRelationshipAndEnabled(Relationship.SELF,
                                                                                   true);
        patients.forEach(u -> {
            User user = u.getUser();
            UserDTO userDTO = new UserDTO(user);
            userDTO.setGender(u.getGender());
            userDTO.setBirthday(u.getBirthday());
            AppContext.getInstance(UserService.class).updateUser(user, userDTO);
        });
    }
}
