package cn.taihealth.ih.service.vm;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 线下处方记录表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor // 全参构造方法
@NoArgsConstructor // 无参构造方法
public class OfflineOnlineHistoryVM {

    private Long id;

    @ApiModelProperty(value = "处方单药物", required = true)
    private String content;

    @ApiModelProperty(value = "处方单时间", required = true)
    private Date prescriptionTime;

    @ApiModelProperty(value = "就诊人", required = true)
    private PatientVM patient;

    @ApiModelProperty(value = "上传处方单", required = true)
    private List<UploadVM> upload = Lists.newArrayList();

    @ApiModelProperty(value = "线上处方单或者线下处方单", required = true)
    private String type;

}
