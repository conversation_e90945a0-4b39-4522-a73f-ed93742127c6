package cn.taihealth.ih.service.api;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.enums.AggregatePayment;
import cn.taihealth.ih.domain.enums.InsurancePayMethod;
import cn.taihealth.ih.domain.enums.OrderRefundSource;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorker;
import cn.taihealth.ih.domain.hospital.offline.OfflineOrderQueue;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.dto.hospital.offline.OfflineMedicalWorkerDTO;
import cn.taihealth.ih.service.util.LngLat;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.PatientRegistInfoVM;
import cn.taihealth.ih.service.vm.offline.HisPayParam;
import cn.taihealth.ih.service.vm.offline.HisRefundParam;
import cn.taihealth.ih.wechat.service.vm.wechat.MedicalInsuranceParam;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

public interface OfflineOrderService {
    OfflineOrderDTO createOfflineOrder(Hospital hospital, CreateOfflineOrderDTO createOfflineOrderDTO);
//    OfflineOrderVM updateOfflineOrder(Hospital hospital, UpdateOfflineOrderVM offlineOrderVM);

    OfflineOrderDTO outPatientRreRegister(Hospital hospital, Long orderId, MedicalInsuranceParam vm);

    /**
     * 预约挂号签到
     * @param hospital
     * @param user
     * @param offlineOrderId
     * @param lngLat
     * @return
     */
    OfflineOrderQueue offlineOrderSignIn(Hospital hospital, User user, long offlineOrderId, LngLat lngLat);

    void orderPending(long offlineOrderId);

    /**
     * 门诊挂号付款成功
     * @param offlineOrderId
     * @param hisPayParam
     */
    void payOutpatientRegisterCharge(long offlineOrderId, HisPayParam hisPayParam);

    /**
     * 预约退款
     * @param offlineOrderId
     * @param insurancePayMethod
     * @param insuranceParam 医保代码
     */
    void refundingAppointmentRegisterCharge(long offlineOrderId, InsurancePayMethod insurancePayMethod, MedicalInsuranceParam insuranceParam,
                                            String cancelSettleId);

    /**
     * 停诊
     * @param offlineOrderId
     * @param orderRefundSource
     * @param insurancePayMethod
     * @return 是否有业务修改
     */
//    boolean stopAppointmentRegisterCharge(long offlineOrderId, OrderRefundSource orderRefundSource, InsurancePayMethod insurancePayMethod);

    void refundingAppointmentRegisterCharge(long offlineOrderId, OrderRefundSource orderRefundSource, InsurancePayMethod insurancePayMethod,
                                            MedicalInsuranceParam insuranceParam, String cancelSettleId);

    void refundingAppointmentRegisterCharge(long offlineOrderId, InsurancePayMethod insurancePayMethod);

    // 删除offlineOrder
    void deleteOfflineOrder(long offlineOrderId);

    OfflineOrder refundedAppointmentRegisterCharge(long offlineOrderId, HisRefundParam refund);

    /**
     * 创建核酸预约订单
     * @param hospital
     * @param createNucleicAcidOrderDTO
     * @return
     */
    NucleicAcidOrderDTO createNucleicAcidOrder(Hospital hospital, CreateNucleicAcidOrderDTO createNucleicAcidOrderDTO);

    void nucleicAcidOrderPending(long offlineOrderId);

    /**
     * 核酸预约订单付款成功
     * @param user
     * @param offlineOrderId
     * @param wechatOrder
     */
    void payNucleicAcidCharge(User user, long offlineOrderId, WechatOrder wechatOrder);


    /**
     * 根据订单类型查询不同的线下订单列表
     * @param hospital
     * @param patientId
     * @param status
     * @param orderId
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<OfflineOrder> findOfflineOrderListByType(Hospital hospital, String patientId, String status, String orderId, String type, String cardNo, Integer pageNum, Integer pageSize);
    UploadVM exportOfflineOrderListByType(Hospital hospital, String status, String orderId, String type, HttpServletResponse response);

    void updateMedicalAppointmentOrder(Hospital hospital, CreateMedicalAppointmentOrderDTO createMedicalAppointmentOrderDTO);

    OfflineOrderDTO getOfflineOrderDetail(long offlineOrderId);

    OfflineOrderDTO getOfflineOrderDetailWithInsurance(long offlineOrderId);

    /**
     * 获取医生的服务人数
     * @param hospital
     * @param offlineMedicalWorker
     * @return
     */
    int getPatientTotal(Hospital hospital, OfflineMedicalWorker offlineMedicalWorker);

    /**
     * 获取用户就诊过的线下医生
     * @param hospital
     * @param user
     * @param pageNo
     * @param size
     * @return
     */
    Page<OfflineMedicalWorkerDTO> getOfflineMedicalWorkerList(Hospital hospital, User user, Integer pageNo, Integer size);

    OfflineOrderDTO addOutPatientAppointmentRegister(Hospital orThrow, CreateOfflineOrderDTO createOfflineOrderDTO);

    /**
     * 取消预约登记
     * @param hospital
     * @param appointmentId
     */
    void cancelAppointment(Hospital hospital, long appointmentId);

    /**
     * 取消预约挂号
     * @param hospital
     * @param patient
     * @param appointmentId
     */
    void cancelRegister(Hospital hospital, Patient patient, long appointmentId);

    /**
     * 校验是否还能线下挂号
     * 如果超过线下挂号数量限制，返回false
     * @return 是否超过线下挂号限制
     */
    boolean checkRegistered(Hospital hospital, String hisPatid, Date time, String hisDeptId);

    /**
     * 预算，完整预算，附带查号源，锁号
     * @param hospital
     * @param order
     * @param insuranceParam
     * @return
     */
    OfflineOrderDTO preRegister(Hospital hospital, OfflineOrder order, MedicalInsuranceParam insuranceParam);

    List<PatientRegistInfoVM> getPatientRegistInfo(Patient patient, String orderStatusContains, Long cardId,
                                                   Hospital hospital);

    /**
     * 修改支付方式
     * @param order
     * @param aggregatePayment
     */
    void changeAggregatePayment(OfflineOrder order, AggregatePayment aggregatePayment);
}
