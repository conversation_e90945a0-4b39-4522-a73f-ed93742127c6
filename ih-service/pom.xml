<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ih-parent</artifactId>
        <groupId>cn.taihealth.ih</groupId>
        <version>2.0.4878</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ih-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-repo</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-realname</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-wechat-service</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-spring-security</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-ca</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-supervise</artifactId>
        </dependency>

        <!-- Spring Boot 邮件依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>-->

        <!--<dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>-->

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-codec</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-transcoder</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gitq.jedi</groupId>
            <artifactId>jedi-jinguist</artifactId>
        </dependency>

        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>problem</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.tencentyun</groupId>
            <artifactId>tls-sig-api-v2</artifactId>
            <version>2.0</version>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.bouncycastle</groupId>-->
<!--                    <artifactId>bcprov-jdk15on</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.bouncycastle</groupId>-->
<!--                    <artifactId>bcpkix-jdk15on</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>de.ruedigermoeller</groupId>
            <artifactId>fst</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
        </dependency>
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.github.ben-manes.caffeine</groupId>-->
<!--            <artifactId>caffeine</artifactId>-->
<!--            <version>3.0.6</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>ws.schild</groupId>-->
<!--            <artifactId>jave-core</artifactId>-->
<!--            <version>2.4.4</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>ws.schild</groupId>-->
<!--            <artifactId>jave-native-linux64</artifactId>-->
<!--            <version>2.4.4</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>ws.schild</groupId>-->
<!--            <artifactId>jave-native-osx64</artifactId>-->
<!--            <version>2.4.4</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-mts</artifactId>
            <version>2.6.2</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.4.6</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-sms</artifactId>
            <version>3.0.0-rc1</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>cn.com.riversoft</groupId>-->
        <!--            <artifactId>weixin-app</artifactId>-->
        <!--            <version>0.9.6</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>commons-logging</groupId>-->
        <!--                    <artifactId>commons-logging</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
        </dependency>


        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
            <version>1.7.2</version>
        </dependency>

        <!--        excel tool -->

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba/easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml-schemas</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>ocr_api20210707</artifactId>
            <version>1.1.12</version>
            <exclusions>
                <exclusion>
                    <groupId>org.dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun</groupId>
                    <artifactId>aliyun-java-sdk-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jacoco</groupId>
                    <artifactId>org.jacoco.agent</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun</groupId>
                    <artifactId>alibabacloud-gateway-spi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun</groupId>
                    <artifactId>tea-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun</groupId>
                    <artifactId>openapiutil</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-util</artifactId>
            <version>0.2.16</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>openapiutil</artifactId>
            <version>0.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.hitachivantara</groupId>
            <artifactId>hitachivantara-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hitachivantara</groupId>
            <artifactId>hitachivantara-java-sdk-hcp</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId> commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--   kuaidi100     -->
        <dependency>
            <groupId>com.github.kuaidi100-api</groupId>
            <artifactId>sdk</artifactId>
            <version>1.0.4</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.17.2</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun</groupId>
                    <artifactId>aliyun-java-sdk-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- jasypt -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>3.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.healthcard</groupId>
            <artifactId>healthcard-java-sdk-hbzzapi</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>lib</id>
            <url>file://${project.basedir}${file.separator}..${file.separator}lib</url>
        </repository>
    </repositories>

</project>
