package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.Doc;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.Optional;

/**
 */
public interface DocRepository extends EntityJpaRepository<Doc> {

    Optional<Doc> findOneByHospitalAndName(Hospital hospital, String name);

    Optional<Doc> findOneByHospitalAndTitle(Hospital hospital, String title);
}
