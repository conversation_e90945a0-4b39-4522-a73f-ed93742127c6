package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.cloud.OfflineHospitalAnnualInspection;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;

/**
 */
public interface OfflineHospitalAnnualInspectionRepository extends EntityJpaRepository<OfflineHospitalAnnualInspection> {
    List<OfflineHospitalAnnualInspection> findAllByOfflineHospitalOrderByYearDesc(OfflineHospital offlineHospital);
}
