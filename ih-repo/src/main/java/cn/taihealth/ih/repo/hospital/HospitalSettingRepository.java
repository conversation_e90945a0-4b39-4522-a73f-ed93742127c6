package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.hospital.HospitalSetting;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.cloud.Hospital;
import com.gitq.jedi.data.EntityJpaRepository;

import java.util.List;
import java.util.Optional;

public interface HospitalSettingRepository extends EntityJpaRepository<HospitalSetting> {

    Optional<HospitalSetting> findOneByHospitalAndKey(Hospital hospital, HospitalSettingKey key);
    List<HospitalSetting> findAllByHospital(Hospital hospital);

}
