package cn.taihealth.ih.repo.hospital.order;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.Checks;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 */
public interface CheckRepository extends EntityJpaRepository<Checks> {

    List<Checks> findByOrderId(long orderId);
    List<Checks> findByPatient(Patient patient);
    List<Checks> findByPatientAndHospital(Patient patient, Hospital hospital);
    List<Checks> findByHospitalAndUser(Hospital hospital, User user);

    Page<Checks> findByPatientAndExamOrderNotNull(Patient patient, Pageable page);
    Optional<Checks> findFirstByApplyNo(String applyNo);

}
