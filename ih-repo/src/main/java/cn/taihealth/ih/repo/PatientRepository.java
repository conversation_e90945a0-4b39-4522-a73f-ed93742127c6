package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Patient.Relationship;
import cn.taihealth.ih.domain.cloud.User;
import com.gitq.jedi.data.EntityJpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

/**
 *
 */
public interface PatientRepository extends EntityJpaRepository<Patient> {

    List<Patient> findAllByUser(User user);

    List<Patient> findAllByUserAndIdCardNumAndEnabled(User user, String idCardNum, boolean enabled);

    Optional<Patient> findFirstByNameAndIdCardNumAndEnabled(String name, String idCardNum, boolean enabled);

    List<Patient> findAllByUserAndIdCardNumAndEnabledAndHospital(User user, String idCardNum, boolean enabled, Hospital hospital);
    List<Patient> findAllByUserAndIdCardNumAndEnabledAndHospitalOrderByCreatedDateDesc(User user, String idCardNum, boolean enabled, Hospital hospital);

    @Query("FROM Patient c WHERE c.user=:user and c.isDefault=true")
    Optional<Patient> findDefaultPatient(@Param("user") User user);

    Optional<Patient> findByIdAndEnabled(Long id, boolean enabled);

    List<Patient> findAllByUserAndRelationshipAndEnabled(User user, Relationship relationship, boolean enabled);
    List<Patient> findAllByUserAndRelationshipAndEnabledAndHospital(User user, Relationship relationship, boolean enabled, Hospital hospital);

    List<Patient> findAllByRelationshipAndEnabled(Relationship relationship, boolean enabled);

    void deleteByUser(User user);

    Optional<Patient> findFirstByUserAndRelationshipAndEnabledAndMobile(User user, Relationship relationship,
                                                                        boolean enabled, String mobile);

    List<Patient> findAllByUserAndEnabled(User user, boolean enabled);
    List<Patient> findAllByUserAndHospital(User user, Hospital hospital);
    List<Patient> findAllByUserAndHospitalAndEnabled(User user, Hospital hospital, boolean enabled);
    Long countByUserAndEnabled(User user, boolean enabled);

    Optional<Patient> findFirstByUserAndRelationship(User user, Relationship self);
    // 根据hisPatid查询患者
    Optional<Patient> findFirstByElectronicMedicCardsHisPatidAndHospitalAndEnabled(String patId, Hospital hospital, boolean enabled);
    // 根据ids查询患者
    List<Patient> findAllByIdIn(List<Long> ids);

    // 根据hisPatid查询所有就诊人
    List<Patient> findAllByHisPatidAndHospitalAndEnabled(String patId, Hospital hospital, boolean enabled);
}
