package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.Article;
import cn.taihealth.ih.domain.hospital.ArticleAuthor;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;

/**
 *
 */
public interface ArticleAuthorRepository extends EntityJpaRepository<ArticleAuthor> {

    void deleteByArticle(Article article);

    ArticleAuthor findOneByArticleAndAuthor(Article article, User author);

    List<ArticleAuthor> findByArticle(Article article);

    List<ArticleAuthor> findByAuthor(User author);
}
