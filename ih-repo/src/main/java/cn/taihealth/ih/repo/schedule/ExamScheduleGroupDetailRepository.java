package cn.taihealth.ih.repo.schedule;

import cn.taihealth.ih.domain.hospital.ExamScheduleGroup;
import cn.taihealth.ih.domain.hospital.ExamScheduleGroupDetail;
import com.gitq.jedi.data.EntityJpaRepository;

/**
 * @Author: Moon
 * @Date: 2020/12/16 上午11:27
 */
public interface ExamScheduleGroupDetailRepository extends EntityJpaRepository<ExamScheduleGroupDetail> {

    void deleteByGroup(ExamScheduleGroup group);
}
