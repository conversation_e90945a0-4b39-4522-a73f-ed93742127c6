package cn.taihealth.ih.repo;


import cn.taihealth.ih.domain.cloud.WechatOrderRefund;
import com.gitq.jedi.data.EntityJpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface WechatOrderRefundRepository extends EntityJpaRepository<WechatOrderRefund> {

    Optional<WechatOrderRefund> findOneByOutRefundNo(String outRefundNo);

    List<WechatOrderRefund> findAllByOutTradeNo(String outTradeNo);

    List<WechatOrderRefund> findAllByTransactionId(String transactionId);

    @Query(value = "select r.* from ih_wechat_order_refunds r left join ih_wechat_orders o on r.transaction_id = o.transaction_id where r.refund_detail_no like %?1% and o.hospital_id = ?2",nativeQuery = true)
    List<WechatOrderRefund> findByRefundDetailNo(String settleId, Long hospitalCode);
}
