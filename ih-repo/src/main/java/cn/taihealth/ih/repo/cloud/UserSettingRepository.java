package cn.taihealth.ih.repo.cloud;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.UserSetting;
import cn.taihealth.ih.domain.enums.UserSettingKey;
import com.gitq.jedi.data.EntityJpaRepository;

import java.util.Optional;

public interface UserSettingRepository extends EntityJpaRepository<UserSetting> {

    Optional<UserSetting> findOneByHospitalAndUserAndKey(Hospital hospital, User user, UserSettingKey key);

}
