package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.enums.UploadType;
import cn.taihealth.ih.domain.cloud.User;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 */
public interface UploadRepository extends EntityJpaRepository<Upload> {

    Optional<Upload> findOneBySha(String sha);

    List<Upload> findAllByUser(User user);

    Page<Upload> findAllByUploadType(UploadType uploadType, Pageable pageable);

    void deleteByUser(User user);
}
