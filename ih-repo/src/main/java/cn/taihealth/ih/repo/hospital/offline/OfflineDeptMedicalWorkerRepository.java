package cn.taihealth.ih.repo.hospital.offline;

import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.domain.hospital.offline.OfflineDeptMedicalWorker;
import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorker;
import com.gitq.jedi.data.EntityJpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface OfflineDeptMedicalWorkerRepository extends EntityJpaRepository<OfflineDeptMedicalWorker> {

    List<OfflineDeptMedicalWorker> findAllByMedicalWorker(OfflineMedicalWorker medicalWorker);
    List<OfflineDeptMedicalWorker> findAllByDept(OfflineDept dept);
    Optional<OfflineDeptMedicalWorker> findOneByDeptAndMedicalWorker(OfflineDept dept,
                                                                     OfflineMedicalWorker medicalWorker);

    void deleteByMedicalWorker(OfflineMedicalWorker medicalWorker);

    @Transactional
    void deleteByMedicalWorkerId(Long workerId);
}
