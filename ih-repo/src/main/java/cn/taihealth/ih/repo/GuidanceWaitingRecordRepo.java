package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.GuidanceRecord;
import cn.taihealth.ih.domain.GuidanceWaitingRecord;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/9/27
 */
public interface GuidanceWaitingRecordRepo extends EntityJpaRepository<GuidanceWaitingRecord> {
  List<GuidanceWaitingRecord> findAllByChatIdAndOrderId(Long chatId, Long orderId);


  void deleteByOrderId(Long orderId);

  List<GuidanceWaitingRecord> findAllByOrderIdOrderBySequenceDesc(Long orderId);
}
