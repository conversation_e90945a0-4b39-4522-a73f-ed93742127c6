package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.dict.ExamCategory;
import cn.taihealth.ih.domain.dict.ExamItem;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;
import java.util.Optional;

/**
 * @Author: Moon
 * @Date: 2020/12/16 上午11:27
 */
public interface ExamItemRepository extends EntityJpaRepository<ExamItem> {

    Optional<ExamItem> findOneByHospitalAndCategoryAndCode(Hospital hospital, ExamCategory category, String code);

    List<ExamItem> findAllByHospitalAndCode(Hospital hospital, String code);
}
