package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.meddic.DicCategory;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;
import java.util.Optional;

public interface DicCategoryRepository extends EntityJpaRepository<DicCategory> {

    List<DicCategory> findAllByHospital(Hospital hospital);

    Optional<DicCategory> findOneByHospitalAndCategoryName(Hospital hospital, String categoryName);

}
