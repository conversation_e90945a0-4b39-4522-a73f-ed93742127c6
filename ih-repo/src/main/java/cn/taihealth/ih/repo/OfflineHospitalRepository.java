package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.cloud.OfflineHospital;
import com.gitq.jedi.data.EntityJpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 */
public interface OfflineHospitalRepository extends EntityJpaRepository<OfflineHospital> {

    Optional<OfflineHospital> findOneByName(String name);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "DELETE FROM ih_hospitals")
    void deleteAllBySQL();
}
