package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.UserMedicalWorkerRel;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import com.gitq.jedi.data.EntityJpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface UserMedicalWorkerRelRepository extends EntityJpaRepository<UserMedicalWorkerRel> {

    Optional<UserMedicalWorkerRel> findOneByUserAndMedicalWorker(User user, MedicalWorker medicalWorker);

    Optional<UserMedicalWorkerRel> findFirstByUserOrderByCreatedDateDesc(User user);

    Optional<UserMedicalWorkerRel> findFirstByUserAndCkd(User user, Boolean ckd);

    List<UserMedicalWorkerRel> findByMedicalWorkerAndCkd(MedicalWorker medicalWorker, Boolean ckd);

    List<UserMedicalWorkerRel> findByMedicalWorkerIn(Collection<MedicalWorker> medicalWorker);
    List<UserMedicalWorkerRel> findByUserIn(Collection<User> users);
    List<UserMedicalWorkerRel> findByUser(User users);

    List<UserMedicalWorkerRel> findByMedicalWorkerAndFt(MedicalWorker medicalWorker, Boolean ft);

    Optional<UserMedicalWorkerRel> findFirstByUserAndFt(User user, Boolean ft);

    List<UserMedicalWorkerRel> findByCkd(Boolean ckd);

    @Query(value = "select * from ih_user_medical_worker_rels "
        + "where medical_worker_id = ?1 and TO_DAYS(ft_bind_date) BETWEEN TO_DAYS(?2) AND TO_DAYS(?3)",
        nativeQuery = true)
    List<UserMedicalWorkerRel> getByMedicalWorkerIdBetweenDate(@Param("medicalWorkerId") Long medicalWorkerId, @Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate);
    
}
