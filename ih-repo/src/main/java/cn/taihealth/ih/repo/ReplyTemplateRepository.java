package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.ReplyTemplate;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;

public interface ReplyTemplateRepository extends EntityJpaRepository<ReplyTemplate> {

    List<ReplyTemplate> findByMedicalWorker(MedicalWorker medicalWorker);

    ReplyTemplate findOneByMedicalWorkerAndContent(MedicalWorker medicalWorker, String content);
}
