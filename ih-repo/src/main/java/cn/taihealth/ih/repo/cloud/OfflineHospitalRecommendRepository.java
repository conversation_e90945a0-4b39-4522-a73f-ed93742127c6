package cn.taihealth.ih.repo.cloud;

import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.cloud.OfflineHospitalRecommend;
import com.gitq.jedi.data.EntityJpaRepository;

import java.util.Optional;

/**
 */
public interface OfflineHospitalRecommendRepository extends EntityJpaRepository<OfflineHospitalRecommend> {

    Optional<OfflineHospitalRecommend> findOneByOfflineHospital(OfflineHospital offlineHospital);

}
