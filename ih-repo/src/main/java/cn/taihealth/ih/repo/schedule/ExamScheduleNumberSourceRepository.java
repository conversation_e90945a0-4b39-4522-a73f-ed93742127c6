package cn.taihealth.ih.repo.schedule;

import cn.taihealth.ih.domain.hospital.ExamSchedule;
import cn.taihealth.ih.domain.hospital.ExamScheduleNumberSource;
import cn.taihealth.ih.domain.hospital.HospitalDictionary;
import com.gitq.jedi.data.EntityJpaRepository;

import java.util.List;
import java.util.Optional;


public interface ExamScheduleNumberSourceRepository extends EntityJpaRepository<ExamScheduleNumberSource> {

    List<ExamScheduleNumberSource> findAllByExamSchedule(ExamSchedule examSchedule);

    Optional<ExamScheduleNumberSource> findByExamScheduleAndDictionary(ExamSchedule examSchedule, HospitalDictionary hospitalDictionary);
}
