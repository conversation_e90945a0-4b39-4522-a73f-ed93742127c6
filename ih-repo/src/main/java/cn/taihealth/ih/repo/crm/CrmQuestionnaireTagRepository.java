package cn.taihealth.ih.repo.crm;

import cn.taihealth.ih.domain.crm.CrmQuestionnaire;
import cn.taihealth.ih.domain.crm.CrmQuestionnaireTag;
import cn.taihealth.ih.domain.crm.CrmTag;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;

public interface CrmQuestionnaireTagRepository extends EntityJpaRepository<CrmQuestionnaireTag> {

    void deleteByQuestionnaireAndTag(CrmQuestionnaire questionnaire, CrmTag tag);

    void deleteByTag(CrmTag tag);

    List<CrmQuestionnaireTag> findAllByTag(CrmTag tag);

}
