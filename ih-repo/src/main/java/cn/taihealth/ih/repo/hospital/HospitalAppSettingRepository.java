package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.HospitalAppSetting;
import com.gitq.jedi.data.EntityJpaRepository;

import java.util.List;
import java.util.Optional;

public interface HospitalAppSettingRepository extends EntityJpaRepository<HospitalAppSetting> {

    List<HospitalAppSetting> findAllByHospital(Hospital hospital);

    Optional<HospitalAppSetting> findOneByHospitalAndCode(Hospital hospital, String code);

}
