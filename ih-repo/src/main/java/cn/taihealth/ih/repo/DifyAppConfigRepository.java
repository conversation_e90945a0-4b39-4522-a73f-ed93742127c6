package cn.taihealth.ih.repo;

import cn.taihealth.ih.domain.cloud.DifyAppConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DifyAppConfigRepository extends JpaRepository<DifyAppConfig, Long> {

    /**
     * 根据医院ID和应用类型查找配置
     * 
     * @param hospitalId 医院ID
     * @param appType 应用类型
     * @return 配置
     */
    Optional<DifyAppConfig> findByHospitalIdAndAppType(Long hospitalId, String appType);
    
    /**
     * 根据医院ID查找所有配置
     * 
     * @param hospitalId 医院ID
     * @return 配置列表
     */
    List<DifyAppConfig> findByHospitalId(Long hospitalId);
    
    /**
     * 根据医院ID和启用状态查找配置
     * 
     * @param hospitalId 医院ID
     * @param enabled 启用状态
     * @return 配置列表
     */
    List<DifyAppConfig> findByHospitalIdAndEnabled(Long hospitalId, Boolean enabled);
    
    /**
     * 根据appId查找配置
     * 
     * @param appId 应用ID
     * @return 配置
     */
    Optional<DifyAppConfig> findByAppId(String appId);
} 