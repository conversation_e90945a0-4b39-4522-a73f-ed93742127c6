package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.DoctorNoticeTime;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;

public interface DoctorNoticeTimeRepository extends EntityJpaRepository<DoctorNoticeTime> {

    List<DoctorNoticeTime> findByDoctor(MedicalWorker medicalWorker);

    List<DoctorNoticeTime> findByDoctorOrderByIdAsc(MedicalWorker medicalWorker);

    List<DoctorNoticeTime> findByNoticeTime(String noticeTime);
}
