package cn.taihealth.ih.repo.alipay;


import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.enums.ThirdOrderType;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;
import java.util.Optional;

public interface AliPayOrderRepository extends EntityJpaRepository<AliPayOrder> {

    Optional<AliPayOrder> findOneByOutTradeNo(String outTradeNo);

    List<AliPayOrder> findAllByOrderTypeAndBody(ThirdOrderType wechatOrderType, String body);
    List<AliPayOrder> findAllByBody(String body);



}
