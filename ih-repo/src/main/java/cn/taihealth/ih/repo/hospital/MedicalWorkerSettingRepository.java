package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.MedicalWorkerSetting;
import cn.taihealth.ih.domain.enums.MedicalWorkerSettingKey;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.Optional;

public interface MedicalWorkerSettingRepository extends EntityJpaRepository<MedicalWorkerSetting> {

    Optional<MedicalWorkerSetting> findOneByMedicalWorkerAndKey(MedicalWorker medicalWorker, MedicalWorkerSettingKey key);

}
