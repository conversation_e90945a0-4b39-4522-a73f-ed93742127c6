package cn.taihealth.ih.repo;


import cn.taihealth.ih.domain.cloud.WechatInsuranceOrderRefund;
import com.gitq.jedi.data.EntityJpaRepository;

import java.util.Optional;

public interface WechatInsuranceOrderRefundRepository extends EntityJpaRepository<WechatInsuranceOrderRefund> {

    Optional<WechatInsuranceOrderRefund> findOneByOutRefundNo(String outRefundNo);

    Optional<WechatInsuranceOrderRefund> findOneByOutTradeNo(String outTradeNo);

    Optional<WechatInsuranceOrderRefund> findOneByTransactionId(String transactionId);

    Optional<WechatInsuranceOrderRefund> findOneByPayOrdId(String payOrdId);

    Optional<WechatInsuranceOrderRefund> findOneByShBillNo(String shBillNo);

}
