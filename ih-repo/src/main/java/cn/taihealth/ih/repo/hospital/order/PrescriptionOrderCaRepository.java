package cn.taihealth.ih.repo.hospital.order;

import cn.taihealth.ih.domain.hospital.PrescriptionOrderCa;
import com.gitq.jedi.data.EntityJpaRepository;

import java.util.List;
import java.util.Optional;

public interface PrescriptionOrderCaRepository extends EntityJpaRepository<PrescriptionOrderCa> {

    Optional<PrescriptionOrderCa> findOneByOrderIdAndEnabled(long orderId, boolean enabled);
    Optional<PrescriptionOrderCa> findOneByPrescriptionOrderIdAndEnabled(long prescriptionOrderId, boolean enabled);
    List<PrescriptionOrderCa> findAllByPrescriptionOrderIdAndEnabled(long prescriptionOrderId, boolean enabled);

    Optional<PrescriptionOrderCa> findOneByDoctorPdfUid(String doctorPdfUid);
    Optional<PrescriptionOrderCa> findOneByDoctorReviewPdfUid(String doctorReviewPdfUid);

}
