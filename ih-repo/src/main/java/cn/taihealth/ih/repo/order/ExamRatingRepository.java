package cn.taihealth.ih.repo.order;

import cn.taihealth.ih.domain.hospital.ExamOrder;
import cn.taihealth.ih.domain.hospital.ExamRating;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.Optional;

/**
 * @Description
 * <AUTHOR>
 * @date  2020/12/21 16:10
 */
public interface ExamRatingRepository extends EntityJpaRepository<ExamRating> {

    Optional<ExamRating> findByExamOrder(ExamOrder examOrder);
}
