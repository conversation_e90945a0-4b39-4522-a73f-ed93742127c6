package cn.taihealth.ih.repo.cloud;

import cn.taihealth.ih.domain.cloud.UserUserRoleRel;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;

public interface UserUserRoleRelRepository extends EntityJpaRepository<UserUserRoleRel> {

    void deleteByUserRoleId(long userRoleId);

    List<UserUserRoleRel> findAllByUserId(long userId);

    List<UserUserRoleRel> findAllByUserRoleId(long userRoleId);

}
