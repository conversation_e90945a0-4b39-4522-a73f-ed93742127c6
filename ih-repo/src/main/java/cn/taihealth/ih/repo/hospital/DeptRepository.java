package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.Dept;
import cn.taihealth.ih.domain.hospital.Dept.DeptType;
import com.gitq.jedi.data.EntityJpaRepository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface DeptRepository extends EntityJpaRepository<Dept> {

    Optional<Dept> findOneByHospitalAndDeptCode(Hospital hospital, String deptCode);

    List<Dept> findAllByHospitalAndDeptName(Hospital hospital , String deptName);

    /**
     * 此方法用于查询某类型的科室
     * @param hospital 医院
     * @param type 类型
     * @return deptList
     */
    List<Dept> findAllByHospitalAndDeptType(Hospital hospital, DeptType type);

    /**
     * 此方法用于查询某类型下的某科室
     * @param hospital 医院
     * @param type 类型
     * @return dept
     */
    Optional<Dept> findOneByHospitalAndDeptTypeAndDeptName(Hospital hospital, DeptType type, String deptName);

    List<Dept> findAllByGuidanceNumber(int guidanceNumber);

    List<Dept> findByHospital(Hospital hospital);
    List<Dept> findByHospitalAndEnabled(Hospital hospital, Boolean enabled);
    List<Dept> findByHospitalAndDeptCodeIn(Hospital hospital, Collection<String> deptCode);

}
