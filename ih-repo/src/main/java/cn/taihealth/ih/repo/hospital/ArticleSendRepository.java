package cn.taihealth.ih.repo.hospital;

import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.hospital.Article;
import cn.taihealth.ih.domain.hospital.ArticleSend;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import com.gitq.jedi.data.EntityJpaRepository;
import java.util.List;
import java.util.Optional;

/**
 *
 */
public interface ArticleSendRepository extends EntityJpaRepository<ArticleSend> {

    List<ArticleSend> findAllByIsSend(boolean isSend);

    List<ArticleSend> findAllByIsSendAndPatient(boolean isSend, Patient patient);
    List<ArticleSend> findAllByIsSendAndMedicalWorker(boolean isSend, MedicalWorker medicalWorker);

    Optional<ArticleSend> findFirstByArticleAndMedicalWorkerAndPatientAndIsSend(Article article,
                                                                                MedicalWorker medicalWorker,
                                                                                Patient patient,
                                                                                boolean isSend);

}
