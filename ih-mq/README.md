### 环境搭建
ih-mq服务使用了RabbitMQ中间件，在本地或未安装环境的机器上调试前需要先搭建基础的环境   
RabbitMQ依赖于`Erlang`语言，所以在安装RabbitMQ之前需要先安装Erlang环境

#### 版本对照
不同的RabbitMQ版本依赖的Erlang版本也不一样   
***安装时一定要保证RabbitMQ和Erlang的版本兼容，RabbitMQ是基于Erlang开发的，再安装RabbitMQ之前必须先安装配置Erlang***   
具体可以参考 [RabbitMQ Erlang Version Requirements](https://www.rabbitmq.com/which-erlang.html)   

ih-mq服务使用的版本
- RabbitMQ: 3.8.2
- Erlang: 22.2.7

### Erlang安装
下载Erlang: [下载地址](https://erlang.org/download/otp_versions_tree.html)

### RabbitMQ安装
下载RabbitMQ: [下载地址](https://github.com/rabbitmq/rabbitmq-server/releases)

### RabbitMQ使用
#### 端口
- server:5672
- 管理控制台:15672

#### 管理控制台界面
- overview   
    可以看到当前虚拟主机下的所有消息队列状态  
    Ready:还没有消费的消息数 Unacked:未确认的消息数 Total:队列上的消息总数
- Connections   
    连接当前虚拟主机下的IP号和pid号
- Channels   
  信道，指连接上了队列的一些服务，正常来说一个连接对应一个Channels，但是如果你一个连接可以开启一个多线程的话就会去对应多个Channels
- Exchanges   
  连接上的所有的交换机
- Queues   
  连接上的所有的消息队列
- Admin   
  可以添加用户，删除用户，给用户配置

#### 配置
1. 配置交换机
   - Direct Exchange   
     有一个队列绑定到一个直连交换机上，同时赋予一个路由键 routing key 。
     然后当一个消息携带着路由值为X，这个消息通过生产者发送给交换机时，交换机就会根据这个路由值X去寻找绑定值也是X的队列
   - Fanout Exchange   
     扇型交换机，这个交换机没有路由键概念，就算你绑了路由键也是无视的。 这个交换机在接收到消息后，会直接转发到绑定到它上面的所有队列。
   - Topic Exchange   
     和Direct交换机类似，但他的routing key可以使用通配符，#：后续任意键，*：后续一个任意键   
     队列Q1 绑定键为 *.TT.*          队列Q2绑定键为  TT.#
     如果一条消息携带的路由键为 A.TT.B，那么队列Q1将会收到；
     如果一条消息携带的路由键为TT.AA.BB，那么队列Q2将会收到；
2. 配置队列
3. 队列和交换机的绑定   
以上配置可以参考`RabbitMQChannelConfig`配置类

#### 使用
- provider   
生产者投递消息时，只需要往相应的交换机投递即可，如果是Direct或Topic交换机，还需要加上相应的路由
- receiver   
消费者监听时，只需要监听相应的队列，等待消息发送即可
#### 消息可靠性
- 生产者可靠性传递
- server持久化
- 消费者可靠性消费