package cn.taihealth.ih.mq.config;

import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import lombok.NoArgsConstructor;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.*;
import org.springframework.amqp.rabbit.core.ChannelCallback;
import org.springframework.amqp.rabbit.core.CorrelationDataPostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.support.MessagePropertiesConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.expression.Expression;
import org.springframework.retry.RecoveryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.ErrorHandler;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Executor;

@NoArgsConstructor
public class MockAmqpTemplate extends RabbitTemplate {

    @Override
    protected void initDefaultStrategies() {
    }

    @Override
    public void convertAndSend(Object object) throws AmqpException {
    }

    @Override
    public void convertAndSend(String routingKey, Object object) throws AmqpException {
    }

    @Override
    public void convertAndSend(String routingKey, Object object, CorrelationData correlationData) throws AmqpException {
    }

    @Override
    public void convertAndSend(String exchange, String routingKey, Object object) throws AmqpException {
    }

    @Override
    public void convertAndSend(String exchange, String routingKey, Object object, CorrelationData correlationData) throws AmqpException {
    }

    @Override
    public void convertAndSend(Object message, MessagePostProcessor messagePostProcessor) throws AmqpException {
    }

    @Override
    public void convertAndSend(String routingKey, Object message, MessagePostProcessor messagePostProcessor) throws AmqpException {
    }

    @Override
    public void convertAndSend(Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData) throws AmqpException {
    }

    @Override
    public void convertAndSend(String routingKey, Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData) throws AmqpException {
    }

    @Override
    public void convertAndSend(String exchange, String routingKey, Object message, MessagePostProcessor messagePostProcessor) throws AmqpException {
    }

    @Override
    public void convertAndSend(String exchange, String routingKey, Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData) throws AmqpException {
    }

    public MockAmqpTemplate(ConnectionFactory connectionFactory) {
        super(connectionFactory);
    }

    @Override
    public void setExchange(String exchange) {
    }

    @Override
    public String getExchange() {
        return super.getExchange();
    }

    @Override
    public void setRoutingKey(String routingKey) {
    }

    @Override
    public String getRoutingKey() {
        return super.getRoutingKey();
    }

    @Override
    public void setDefaultReceiveQueue(String queue) {
    }

    @Override
    public void setEncoding(String encoding) {
    }

    @Override
    public String getEncoding() {
        return super.getEncoding();
    }

    @Override
    public synchronized void setReplyAddress(String replyAddress) {
    }

    @Override
    public void setReceiveTimeout(long receiveTimeout) {
    }

    @Override
    public void setReplyTimeout(long replyTimeout) {
    }

    @Override
    public void setMessageConverter(MessageConverter messageConverter) {
    }

    @Override
    public void setMessagePropertiesConverter(MessagePropertiesConverter messagePropertiesConverter) {
    }

    @Override
    protected MessagePropertiesConverter getMessagePropertiesConverter() {
        return super.getMessagePropertiesConverter();
    }

    @Override
    public MessageConverter getMessageConverter() {
        return super.getMessageConverter();
    }

    @Override
    public void setConfirmCallback(ConfirmCallback confirmCallback) {
    }

    @Override
    public void setReturnCallback(ReturnCallback returnCallback) {
    }

    @Override
    public void setMandatory(boolean mandatory) {
    }

    @Override
    public void setMandatoryExpression(Expression mandatoryExpression) {
    }

    @Override
    public void setMandatoryExpressionString(String mandatoryExpression) {
    }

    @Override
    public void setSendConnectionFactorySelectorExpression(Expression sendConnectionFactorySelectorExpression) {
    }

    @Override
    public void setReceiveConnectionFactorySelectorExpression(Expression receiveConnectionFactorySelectorExpression) {
    }

    @Override
    public void setCorrelationKey(String correlationKey) {
    }

    @Override
    public void setRetryTemplate(RetryTemplate retryTemplate) {
    }

    @Override
    public void setRecoveryCallback(RecoveryCallback<?> recoveryCallback) {
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
    }

    @Override
    public void setBeforePublishPostProcessors(MessagePostProcessor... beforePublishPostProcessors) {
    }

    @Override
    public void addBeforePublishPostProcessors(MessagePostProcessor... beforePublishPostProcessors) {
    }

    @Override
    public boolean removeBeforePublishPostProcessor(MessagePostProcessor beforePublishPostProcessor) {
        return super.removeBeforePublishPostProcessor(beforePublishPostProcessor);
    }

    @Override
    public void setAfterReceivePostProcessors(MessagePostProcessor... afterReceivePostProcessors) {
    }

    @Override
    public Collection<MessagePostProcessor> getAfterReceivePostProcessors() {
        return super.getAfterReceivePostProcessors();
    }

    @Override
    public void addAfterReceivePostProcessors(MessagePostProcessor... afterReceivePostProcessors) {
    }

    @Override
    public boolean removeAfterReceivePostProcessor(MessagePostProcessor afterReceivePostProcessor) {
        return super.removeAfterReceivePostProcessor(afterReceivePostProcessor);
    }

    @Override
    public void setCorrelationDataPostProcessor(CorrelationDataPostProcessor correlationDataPostProcessor) {
    }

    @Override
    public void setUseTemporaryReplyQueues(boolean value) {
    }

    @Override
    public void setUseDirectReplyToContainer(boolean useDirectReplyToContainer) {
    }

    @Override
    public void setUserIdExpression(Expression userIdExpression) {
    }

    @Override
    public void setUserIdExpressionString(String userIdExpression) {
    }

    @Override
    public void setBeanName(String name) {
    }

    @Override
    public void setTaskExecutor(Executor taskExecutor) {
    }

    @Override
    public void setUserCorrelationId(boolean userCorrelationId) {
    }

    @Override
    public boolean isUsePublisherConnection() {
        return super.isUsePublisherConnection();
    }

    @Override
    public void setUsePublisherConnection(boolean usePublisherConnection) {
    }

    @Override
    public void setNoLocalReplyConsumer(boolean noLocalReplyConsumer) {
    }

    @Override
    public void setReplyErrorHandler(ErrorHandler replyErrorHandler) {
    }

    @Override
    public Collection<String> expectedQueueNames() {
        return super.expectedQueueNames();
    }

    @Override
    public Collection<CorrelationData> getUnconfirmed(long age) {
        return super.getUnconfirmed(age);
    }

    @Override
    public int getUnconfirmedCount() {
        return super.getUnconfirmedCount();
    }

    @Override
    public void start() {
    }

    @Override
    protected void doStart() {
    }

    @Override
    public void stop() {
    }

    @Override
    protected void doStop() {
    }

    @Override
    public boolean isRunning() {
        return super.isRunning();
    }

    @Override
    public void destroy() {
        super.destroy();
    }

    @Override
    protected boolean useDirectReplyTo() {
        return super.useDirectReplyTo();
    }

    @Override
    public void send(Message message) throws AmqpException {
    }

    @Override
    public void send(String routingKey, Message message) throws AmqpException {
    }

    @Override
    public void send(String exchange, String routingKey, Message message) throws AmqpException {
    }

    @Override
    public void send(String exchange, String routingKey, Message message, CorrelationData correlationData) throws AmqpException {
    }

    @Override
    public void correlationConvertAndSend(Object object, CorrelationData correlationData) throws AmqpException {
    }

    @Override
    public Message receive() throws AmqpException {
        return super.receive();
    }

    @Override
    public Message receive(String queueName) {
        return super.receive(queueName);
    }

    @Override
    protected Message doReceiveNoWait(String queueName) {
        return super.doReceiveNoWait(queueName);
    }

    @Override
    public Message receive(long timeoutMillis) throws AmqpException {
        return super.receive(timeoutMillis);
    }

    @Override
    public Message receive(String queueName, long timeoutMillis) {
        return super.receive(queueName, timeoutMillis);
    }

    @Override
    public Object receiveAndConvert() throws AmqpException {
        return super.receiveAndConvert();
    }

    @Override
    public Object receiveAndConvert(String queueName) throws AmqpException {
        return super.receiveAndConvert(queueName);
    }

    @Override
    public Object receiveAndConvert(long timeoutMillis) throws AmqpException {
        return super.receiveAndConvert(timeoutMillis);
    }

    @Override
    public Object receiveAndConvert(String queueName, long timeoutMillis) throws AmqpException {
        return super.receiveAndConvert(queueName, timeoutMillis);
    }

    @Override
    public <T> T receiveAndConvert(ParameterizedTypeReference<T> type) throws AmqpException {
        return super.receiveAndConvert(type);
    }

    @Override
    public <T> T receiveAndConvert(String queueName, ParameterizedTypeReference<T> type) throws AmqpException {
        return super.receiveAndConvert(queueName, type);
    }

    @Override
    public <T> T receiveAndConvert(long timeoutMillis, ParameterizedTypeReference<T> type) throws AmqpException {
        return super.receiveAndConvert(timeoutMillis, type);
    }

    @Override
    public <T> T receiveAndConvert(String queueName, long timeoutMillis, ParameterizedTypeReference<T> type) throws AmqpException {
        return super.receiveAndConvert(queueName, timeoutMillis, type);
    }

    @Override
    public <R, S> boolean receiveAndReply(ReceiveAndReplyCallback<R, S> callback) throws AmqpException {
        return super.receiveAndReply(callback);
    }

    @Override
    public <R, S> boolean receiveAndReply(String queueName, ReceiveAndReplyCallback<R, S> callback) throws AmqpException {
        return super.receiveAndReply(queueName, callback);
    }

    @Override
    public <R, S> boolean receiveAndReply(ReceiveAndReplyCallback<R, S> callback, String exchange, String routingKey) throws AmqpException {
        return super.receiveAndReply(callback, exchange, routingKey);
    }

    @Override
    public <R, S> boolean receiveAndReply(String queueName, ReceiveAndReplyCallback<R, S> callback, String replyExchange, String replyRoutingKey) throws AmqpException {
        return super.receiveAndReply(queueName, callback, replyExchange, replyRoutingKey);
    }

    @Override
    public <R, S> boolean receiveAndReply(ReceiveAndReplyCallback<R, S> callback, ReplyToAddressCallback<S> replyToAddressCallback) throws AmqpException {
        return super.receiveAndReply(callback, replyToAddressCallback);
    }

    @Override
    public <R, S> boolean receiveAndReply(String queueName, ReceiveAndReplyCallback<R, S> callback, ReplyToAddressCallback<S> replyToAddressCallback) throws AmqpException {
        return super.receiveAndReply(queueName, callback, replyToAddressCallback);
    }

    @Override
    public Message sendAndReceive(Message message) throws AmqpException {
        return super.sendAndReceive(message);
    }

    @Override
    public Message sendAndReceive(Message message, CorrelationData correlationData) throws AmqpException {
        return super.sendAndReceive(message, correlationData);
    }

    @Override
    public Message sendAndReceive(String routingKey, Message message) throws AmqpException {
        return super.sendAndReceive(routingKey, message);
    }

    @Override
    public Message sendAndReceive(String routingKey, Message message, CorrelationData correlationData) throws AmqpException {
        return super.sendAndReceive(routingKey, message, correlationData);
    }

    @Override
    public Message sendAndReceive(String exchange, String routingKey, Message message) throws AmqpException {
        return super.sendAndReceive(exchange, routingKey, message);
    }

    @Override
    public Message sendAndReceive(String exchange, String routingKey, Message message, CorrelationData correlationData) throws AmqpException {
        return super.sendAndReceive(exchange, routingKey, message, correlationData);
    }

    @Override
    public Object convertSendAndReceive(Object message) throws AmqpException {
        return super.convertSendAndReceive(message);
    }

    @Override
    public Object convertSendAndReceive(Object message, CorrelationData correlationData) throws AmqpException {
        return super.convertSendAndReceive(message, correlationData);
    }

    @Override
    public Object convertSendAndReceive(String routingKey, Object message) throws AmqpException {
        return super.convertSendAndReceive(routingKey, message);
    }

    @Override
    public Object convertSendAndReceive(String routingKey, Object message, CorrelationData correlationData) throws AmqpException {
        return super.convertSendAndReceive(routingKey, message, correlationData);
    }

    @Override
    public Object convertSendAndReceive(String exchange, String routingKey, Object message) throws AmqpException {
        return super.convertSendAndReceive(exchange, routingKey, message);
    }

    @Override
    public Object convertSendAndReceive(String exchange, String routingKey, Object message, CorrelationData correlationData) throws AmqpException {
        return super.convertSendAndReceive(exchange, routingKey, message, correlationData);
    }

    @Override
    public Object convertSendAndReceive(Object message, MessagePostProcessor messagePostProcessor) throws AmqpException {
        return super.convertSendAndReceive(message, messagePostProcessor);
    }

    @Override
    public Object convertSendAndReceive(Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData) throws AmqpException {
        return super.convertSendAndReceive(message, messagePostProcessor, correlationData);
    }

    @Override
    public Object convertSendAndReceive(String routingKey, Object message, MessagePostProcessor messagePostProcessor) throws AmqpException {
        return super.convertSendAndReceive(routingKey, message, messagePostProcessor);
    }

    @Override
    public Object convertSendAndReceive(String routingKey, Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData) throws AmqpException {
        return super.convertSendAndReceive(routingKey, message, messagePostProcessor, correlationData);
    }

    @Override
    public Object convertSendAndReceive(String exchange, String routingKey, Object message, MessagePostProcessor messagePostProcessor) throws AmqpException {
        return super.convertSendAndReceive(exchange, routingKey, message, messagePostProcessor);
    }

    @Override
    public Object convertSendAndReceive(String exchange, String routingKey, Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData) throws AmqpException {
        return super.convertSendAndReceive(exchange, routingKey, message, messagePostProcessor, correlationData);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(Object message, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(message, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(Object message, CorrelationData correlationData, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(message, correlationData, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(String routingKey, Object message, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(routingKey, message, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(String routingKey, Object message, CorrelationData correlationData, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(routingKey, message, correlationData, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(String exchange, String routingKey, Object message, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(exchange, routingKey, message, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(Object message, MessagePostProcessor messagePostProcessor, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(message, messagePostProcessor, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(message, messagePostProcessor, correlationData, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(String routingKey, Object message, MessagePostProcessor messagePostProcessor, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(routingKey, message, messagePostProcessor, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(String routingKey, Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(routingKey, message, messagePostProcessor, correlationData, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(String exchange, String routingKey, Object message, MessagePostProcessor messagePostProcessor, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(exchange, routingKey, message, messagePostProcessor, responseType);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(String exchange, String routingKey, Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(exchange, routingKey, message, messagePostProcessor, correlationData, responseType);
    }

    @Override
    protected Message convertSendAndReceiveRaw(String exchange, String routingKey, Object message, MessagePostProcessor messagePostProcessor, CorrelationData correlationData) {
        return super.convertSendAndReceiveRaw(exchange, routingKey, message, messagePostProcessor, correlationData);
    }

    @Override
    protected Message convertMessageIfNecessary(Object object) {
        return super.convertMessageIfNecessary(object);
    }

    @Override
    protected Message doSendAndReceive(String exchange, String routingKey, Message message, CorrelationData correlationData) {
        return super.doSendAndReceive(exchange, routingKey, message, correlationData);
    }

    @Override
    protected Message doSendAndReceiveWithTemporary(String exchange, String routingKey, Message message, CorrelationData correlationData) {
        return super.doSendAndReceiveWithTemporary(exchange, routingKey, message, correlationData);
    }

    @Override
    protected Message doSendAndReceiveWithFixed(String exchange, String routingKey, Message message, CorrelationData correlationData) {
        return super.doSendAndReceiveWithFixed(exchange, routingKey, message, correlationData);
    }

    @Override
    protected void replyTimedOut(String correlationId) {
    }

    @Override
    public Boolean isMandatoryFor(Message message) {
        return super.isMandatoryFor(message);
    }

    @Override
    public <T> T execute(ChannelCallback<T> action) {
        return null;
    }

    @Override
    public <T> T invoke(OperationsCallback<T> action, com.rabbitmq.client.ConfirmCallback acks, com.rabbitmq.client.ConfirmCallback nacks) {
        return super.invoke(action, acks, nacks);
    }

    @Override
    public boolean waitForConfirms(long timeout) {
        return super.waitForConfirms(timeout);
    }

    @Override
    public void waitForConfirmsOrDie(long timeout) {
    }

    @Override
    public void doSend(Channel channel, String exchangeArg, String routingKeyArg, Message message, boolean mandatory, CorrelationData correlationData) throws IOException {
    }

    @Override
    protected void sendToRabbit(Channel channel, String exchange, String routingKey, boolean mandatory, Message message) throws IOException {
    }

    @Override
    protected boolean isChannelLocallyTransacted(Channel channel) {
        return super.isChannelLocallyTransacted(channel);
    }

    @Override
    public void addListener(Channel channel) {
    }

    @Override
    public void handleConfirm(PendingConfirm pendingConfirm, boolean ack) {
    }

    @Override
    public void handleReturn(int replyCode, String replyText, String exchange, String routingKey, AMQP.BasicProperties properties, byte[] body) {
    }

    @Override
    public boolean isConfirmListener() {
        return super.isConfirmListener();
    }

    @Override
    public boolean isReturnListener() {
        return super.isReturnListener();
    }

    @Override
    public void revoke(Channel channel) {
    }

    @Override
    public String getUUID() {
        return super.getUUID();
    }

    @Override
    public void onMessage(Message message) {
    }

    @Override
    public void containerAckMode(AcknowledgeMode mode) {
    }

    @Override
    public void onMessageBatch(List<Message> messages) {
    }

    @Override
    public boolean isChannelTransacted() {
        return super.isChannelTransacted();
    }

    @Override
    public void setChannelTransacted(boolean transactional) {
    }

    @Override
    public ConnectionFactory getConnectionFactory() {
        return super.getConnectionFactory();
    }

    @Override
    public void afterPropertiesSet() {
    }

    @Override
    protected Connection createConnection() {
        return super.createConnection();
    }

    @Override
    protected Connection getConnection(RabbitResourceHolder holder) {
        return super.getConnection(holder);
    }

    @Override
    protected Channel getChannel(RabbitResourceHolder holder) {
        return super.getChannel(holder);
    }

    @Override
    protected RabbitResourceHolder getTransactionalResourceHolder() {
        return super.getTransactionalResourceHolder();
    }

    @Override
    protected RuntimeException convertRabbitAccessException(Exception ex) {
        return super.convertRabbitAccessException(ex);
    }

    @Override
    public <T> T invoke(OperationsCallback<T> action) throws AmqpException {
        return super.invoke(action);
    }

    @Override
    public <T> T convertSendAndReceiveAsType(String exchange, String routingKey, Object message, CorrelationData correlationData, ParameterizedTypeReference<T> responseType) throws AmqpException {
        return super.convertSendAndReceiveAsType(exchange, routingKey, message, correlationData, responseType);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    @Override
    public String toString() {
        return super.toString();
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();
    }
}
