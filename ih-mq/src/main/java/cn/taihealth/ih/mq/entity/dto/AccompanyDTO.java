package cn.taihealth.ih.mq.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 慢病随访提醒通知业务数据实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccompanyDTO extends ValidDTO {

    /**
     * 随访日期
     */
    private String date;

    /**
     * 随访人
     */
    private String username;
    /**
     * 备注
     */
    private String remark;

    @Override
    public boolean valid() {
        return super.valid() && StringUtils.isNoneBlank(this.date, this.username, this.remark);
    }

}
