package cn.taihealth.ih.mq.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MessageTypeEnum {
    /**
     * 用户信息
     */
    USER_INFO("USER_INFO"),
    /**
     * 医患关系绑定
     */
    BINDING("BINDING"),
    /**
     * CKD通知
     */
    NOTIFY("NOTIFY"),
    /**
     * 医生待处理数量更新
     */
    DOC_TODO_NUM("DOC_TODO_NUM"),
    /**
     * 删除用户
     */
    USER_DELETE("USER_DELETE"),
    /**
     * 视频用户退出房间
     */
    VIDEO_END("VIDEO_END"),
    /**
     * 同步疾病
     */
    SYNC_HIS_DIC_DIAGNOSE("SYNC_HIS_DIC_DIAGNOSE"),
    SYNC_HIS_DRUG_INFO("SYNC_HIS_DRUG_INFO"),
    HIS_SYNC_DEPT_DICT("HIS_SYNC_DEPT_DICT"),

    HIS_SYNC_DOC_DICT("HIS_SYNC_DOC_DICT"),
    QUESTION_CHANGE("QUESTION_CHANGE"),
    QUESTIONNAIRES_CHANGE("QUESTIONNAIRES_CHANGE"),
    ALIPAY_REFUND_SUCCESS("ALIPAY_REFUND_SUCCESS"),

    /**
     * 4003消息
     */
    MSG_4003("MSG_4003"),

    /**
     * 取消结算确认
     */
    CONFIRM_REFUND("CONFIRM_REFUND"),

    /**
     * 查询退款状态
     */
    QUERY_REFUND_STATUS("QUERY_REFUND_STATUS")
    ;

    private final String name;
}
