package cn.taihealth.ih.mq.receiver;

import cn.taihealth.ih.mq.config.RabbitMQChannelConfig;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;


/**
 * MQ Receiver - NotifyReceiver
 */
@Slf4j
@Component
public class DeadLetterReceiver {


    @RabbitListener(queues = RabbitMQChannelConfig.DEAD_NOTIFY_QUEUE)
    public void processNotifyDeadLetter(JSONObject message, Channel channel,
                                        @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        try {
            log.info("Receiver [notify-dead-letter] message: {}", message);
            // TODO 针对死信队列处理的业务
            channel.basicAck(tag, false);
        } catch (Exception e) {
            //TODO 人工干预(发送邮件、消息记录转储)
            try {
                channel.basicAck(tag, false);
            } catch (IOException ignored) {
            }
            log.error(String.format("[死信队列]: %s 处理失败: %s", RabbitMQChannelConfig.DEAD_NOTIFY_QUEUE, e.getMessage()), e);
        }
    }
}
