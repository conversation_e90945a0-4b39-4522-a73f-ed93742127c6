package cn.taihealth.ih.mq.entity.dto.disease;

import cn.taihealth.ih.domain.PatientCategory;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
public class PatientCategoryDTO  {

    @ApiModelProperty("疾病标签")
    private CategoryVM categoryVM;

    @ApiModelProperty("疾病标签")
    private List<PatientCategoryDTO> patientCategoryDTOS;

    public PatientCategoryDTO(PatientCategory patientCategory) {
        //这里存在问题(懒加载、事务....)，暂时不使用这一套树状处理
        categoryVM = new CategoryVM(patientCategory.getCategory());
        if (patientCategory.getPatientCategories() != null) {
            patientCategoryDTOS = patientCategory.getPatientCategories().stream().map(PatientCategoryDTO::new)
                .collect(Collectors.toList());
        }

    }

    public CategoryVM getCategoryVMTree() {
        categoryVM.setCategories(patientCategoryDTOS.stream().map(PatientCategoryDTO::getCategoryVMTree).collect(
            Collectors.toList()));
        return categoryVM;
    }

    public CategoryVM getCategoryVM() {
        return categoryVM;
    }

    public void setCategoryVM(CategoryVM categoryVM) {
        this.categoryVM = categoryVM;
    }

    public List<PatientCategoryDTO> getPatientCategoryDTOS() {
        return patientCategoryDTOS;
    }

    public void setPatientCategoryDTOS(List<PatientCategoryDTO> patientCategoryDTOS) {
        this.patientCategoryDTOS = patientCategoryDTOS;
    }
}
