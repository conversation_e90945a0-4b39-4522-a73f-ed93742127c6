spring:
  datasource:
    url: **********************************************************************************************************************************************************
    username: root
    password: 123456
    dialect: org.hibernate.spatial.dialect.mysql.MySQLSpatialDialect
    driverclassname: com.mysql.cj.jdbc.Driver


  jackson:
    serialization:
      FAIL_ON_EMPTY_BEANS: false

  jpa:
    show-sql: true

  redis:
    host: 127.0.0.1
    port: 6379
    #    host: *************
    #    port: 6333
    database: 4


  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    publisher-confirm-type: correlated
    publisher-returns: on
    listener:
      simple:
        acknowledge-mode: manual

app:
  home: ../../data/home/
  uploadDir: ../../data/upload/
  fileStore: local
  baseUrl: http://test.ngrok.xifan.net.cn
  smsValiditySeconds: 60
  showStackTrace: true
  security:
    authentication:
      jwt:
  #        secret: fLx1CDyX4^RuG@R$
  #        base64Secret: d3clMjE0NnUlMjREJTIxejQlNUV1SGtrZkMxRCU1RU5JNypUQXE3NHI0VGVYb1hANFZSYmlaQmRzJTIzVnFLNjMlMjVNUXdVNXQlMjRpRk8=
  #        base64Secret: d0J9SlFta1hxQXFUQ29OV2VWYkdubmtrRWZvQXhrd052Q0JOWlFLVip4TDRDeDJqaWd2RnlkZXZueGh2b3NROAo=
  drug-store:
    host: http://localhost:8080
  weixinHost: https://api.taihealth.cn
  xxl:
    job:
      accessToken: 123456
      executor:
        appname: ih-backend
        logpath: ../../xxl-job/jobhandler
      admin:
        addresses: http://127.0.0.1:2222/xxl-job-admin

  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization"
    allow-credentials: false
    max-age: 1800

logging:
  level:
    cn.taihealth.ih: INFO
#    cn.taihealth.ih.dao: DEBUG

management:
  endpoint:
    enabled: false
    health:
      enabled: false
    info:
      enabled: false

# 调用nodeRed的baseUrl
nodeRedBaseUrl: http://**********:1880
