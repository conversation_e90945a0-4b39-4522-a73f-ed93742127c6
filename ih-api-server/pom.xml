<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ih-parent</artifactId>
        <groupId>cn.taihealth.ih</groupId>
        <version>2.0.4878</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ih-api-server</artifactId>
    <dependencies>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-service</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-wechat-service</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-maintenance</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-bootstrap</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gitq.jedi</groupId>
            <artifactId>jedi-context</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-properties-migrator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.tuckey</groupId>
            <artifactId>urlrewritefilter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-hikaricp</artifactId>
            <version>${hibernate.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.fizzed</groupId>
                <artifactId>stork-maven-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>stork-launcher</id>
                        <phase>package</phase>
                        <goals>
                            <goal>launcher</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>stork-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>assembly</goal>
                        </goals>
                        <configuration>
                            <finalName>ih-api-server-${project.version}</finalName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <format>properties</format>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
