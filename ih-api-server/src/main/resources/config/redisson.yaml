{"singleServerConfig": {"idleConnectionTimeout": 10000, "connectTimeout": 10000, "timeout": 3000, "retryAttempts": 3, "retryInterval": 1500, "password": null, "subscriptionsPerConnection": 5, "clientName": null, "address": "redis://127.0.0.1:6379", "subscriptionConnectionMinimumIdleSize": 1, "subscriptionConnectionPoolSize": 50, "connectionMinimumIdleSize": 32, "connectionPoolSize": 64, "database": 0, "dnsMonitoringInterval": 5000}, "transportMode": "NIO"}