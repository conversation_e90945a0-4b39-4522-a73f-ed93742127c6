package cn.taihealth.ih.maintenance.job;

import cn.hutool.core.util.StrUtil;
import cn.taihealth.ih.domain.ai.CaseReportForm;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.maintenance.conf.XxlRegister;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.UserRepository;
import cn.taihealth.ih.repo.ai.CaseReportFormRepository;
import cn.taihealth.ih.service.api.MessageService;
import com.gitq.jedi.data.specification.Specifications;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.time.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 智能随访推送定时任务
 */
@Component
@Slf4j
@AllArgsConstructor
public class SmartFollowUpJob {

    private final UserRepository userRepository;

    private final HospitalRepository hospitalRepository;

    private final MessageService shortMessageService;

    private final CaseReportFormRepository caseReportFormRepository;

    @XxlJob("SmartFollowUpJob")
    @XxlRegister(cron = "0 0 10 * * ?", author = "jiangzhenyu", jobDesc = "每天上午10天推送给N天前第一次做风险筛查的用户随访链接")
    public void run() {
        String params = XxlJobHelper.getJobParam();
        int days;
        if (StrUtil.isNotEmpty(params)) {
            days = Integer.parseInt(params);
        } else {
            days = 180;
        }
        List<Date> cycleDates = getCycleDates(5, days);

        // 红房子专属定时任务
        Optional<Hospital> hfz = hospitalRepository.findOneByCode("hfz");
        if (hfz.isPresent()) {
            Hospital hospital = hfz.get();
            List<CaseReportForm> all = caseReportFormRepository.findAll(Specifications.orderBy(Sort.Order.asc("createdDate")));
            Map<Long, List<CaseReportForm>> crfByUser = all.stream().collect(Collectors.groupingBy(CaseReportForm::getUserId));
            for (Map.Entry<Long, List<CaseReportForm>> entry : crfByUser.entrySet()) {
                // 该用户最早的数据
                CaseReportForm crf = entry.getValue().get(0);
                Date createdDate = crf.getCreatedDate();
                if (isInCycleDates(createdDate, cycleDates)) {
                    // 推送随访短信
                    User user = userRepository.findById(crf.getUserId()).get();
                    shortMessageService.sendSmartFollowUp(hospital, user.getMobile());
                }
            }
        }
    }

    // 获取周期性时间
    public static List<Date> getCycleDates(int numCycles, int cycleDays) {
        LocalDate today = LocalDate.now();
        ZoneId zone = ZoneId.systemDefault();

        return java.util.stream.IntStream.rangeClosed(1, numCycles)
                .mapToObj(i -> today.minusDays(i * cycleDays))
                .map(date -> Date.from(date.atStartOfDay(zone).toInstant()))
                .collect(Collectors.toList());
    }

    // 判断是否属于周期性时间
    public static boolean isInCycleDates(Date date, List<Date> cycleDates) {
        LocalDate localDate = toLocalDate(date);
        return cycleDates.stream()
                .map(item -> toLocalDate(item))
                .anyMatch(d -> d.equals(localDate));
    }

    public static LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
}
