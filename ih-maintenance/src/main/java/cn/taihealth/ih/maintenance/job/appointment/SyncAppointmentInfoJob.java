package cn.taihealth.ih.maintenance.job.appointment;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.service.api.LockService;
import cn.taihealth.ih.service.error.LockException;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Component
@Slf4j
@AllArgsConstructor
public class SyncAppointmentInfoJob {

    private final LockService lockService;
    private final HospitalRepository hospitalRepository;


    @XxlJob("SyncAppointmentDept")
    @Transactional
    public void syncAppointmentDept() {
        log.info("同步科室字典 start");
        for (Hospital hospital : hospitalRepository.findAll()) {
            try {
                if (!StringUtils.equals(hospital.getCode(),"default")) {
                    lockService.executeAutomaticRenewal(
                            getClass().getName() + ".syncAppointmentDept." + hospital.getCode(), () -> {
                                try {
                                    BusinessServiceStrategy.getInstance().getStrategy(true).syncDept(hospital);
                                } catch (Exception e) {
                                    log.error("医院：" + hospital.getCode() + "同步科室字典失败", e);
                                }
                                return 0;
                            });
                }
            } catch (LockException ignored){}
        }
        log.info("同步科室字典 end");
    }

    @XxlJob("SyncAppointmentDoctor")
    @Transactional
    public void syncAppointmentDoctor() {
        log.info("同步员工字典 start");
        for (Hospital hospital : hospitalRepository.findAll()) {
            try {
                if (!StringUtils.equals(hospital.getCode(),"default")) {
                    lockService.executeAutomaticRenewal(
                            getClass().getName() + ".syncAppointmentDoctor." + hospital.getCode(), () -> {
                                try {
                                    BusinessServiceStrategy.getInstance().getStrategy(true).syncDoctorOffline(hospital);
                                } catch (Exception e) {
                                    log.error("医院：" + hospital.getCode() + "同步员工字典失败", e);
                                }
                                return 0;
                            });
                }
            } catch (LockException ignored){}
        }
        log.info("同步员工字典 end");
    }

    @XxlJob("SyncAppointmentCheckCategory")
    @Transactional
    public void syncAppointmentCheckCategory() {
        log.info("同步检查类别 start");
        for (Hospital hospital : hospitalRepository.findAll()) {
            try {
                if (!StringUtils.equals(hospital.getCode(),"default")) {
                    lockService.executeAutomaticRenewal(
                            getClass().getName() + ".syncAppointmentCheckCategory." + hospital.getCode(), () -> {
                                try {
                                    BusinessServiceStrategy.getInstance().getStrategy(true).syncCheckCategory(hospital);
                                } catch (Exception e) {
                                    log.error("医院：" + hospital.getCode() + "同步检查类别字典失败", e);
                                }
                                return 0;
                            });
                }
            } catch (LockException ignored){}
        }
        log.info("同步检查类别 end");
    }

    @XxlJob("SyncAppointmentCheckItem")
    @Transactional
    public void syncAppointmentCheckItem() {
        log.info("同步检查项目字典 start");
        for (Hospital hospital : hospitalRepository.findAll()) {
            try {
                if (!StringUtils.equals(hospital.getCode(),"default")) {
                    lockService.executeAutomaticRenewal(
                            getClass().getName() + ".syncAppointmentCheckItem." + hospital.getCode(), () -> {
                                try {
                                    BusinessServiceStrategy.getInstance().getStrategy(true).syncCheckItem(hospital);
                                } catch (Exception e) {
                                    log.error("医院：" + hospital.getCode() + "同步检查项目字典失败", e);
                                }
                                return 0;
                            });
                }
            } catch (LockException ignored){}
        }
        log.info("同步检查项目字典 end");
    }

    @XxlJob("syncAppointmentCheckDevices")
    @Transactional
    public void syncAppointmentCheckDevices() {
        log.info("同步检查设备字典 start");
        for (Hospital hospital : hospitalRepository.findAll()) {
            try {
                if (!StringUtils.equals(hospital.getCode(),"default")) {
                    lockService.executeAutomaticRenewal(
                            getClass().getName() + ".syncAppointmentCheckDevices." + hospital.getCode(), () -> {
                                try {
                                    BusinessServiceStrategy.getInstance().getStrategy(true).syncCheckDevices(hospital);
                                } catch (Exception e) {
                                    log.error("医院：" + hospital.getCode() + "同步检查设备字典失败", e);
                                }
                                return 0;
                            });
                }
            } catch (LockException ignored){}
        }
        log.info("同步检查设备字典 end");
    }
}
