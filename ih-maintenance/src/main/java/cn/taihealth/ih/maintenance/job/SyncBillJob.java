package cn.taihealth.ih.maintenance.job;

import cn.hutool.core.util.StrUtil;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.cloud.WechatOrderRefund;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.domain.hospital.Bill;
import cn.taihealth.ih.repo.WechatOrderRefundRepository;
import cn.taihealth.ih.repo.WechatOrderRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRefundRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRepository;
import cn.taihealth.ih.repo.hospital.BillRepository;
import cn.taihealth.ih.repo.hospital.HisBillRepository;
import cn.taihealth.ih.service.api.job.BillJobService;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class SyncBillJob {

    private final BillRepository billRepository;

    private final WechatOrderRepository wechatOrderRepository;

    private final WechatOrderRefundRepository wechatOrderRefundRepository;

    private final HisBillRepository hisBillRepository;

    private final AliPayOrderRepository aliPayOrderRepository;

    private final AliPayOrderRefundRepository aliPayOrderRefundRepository;

    private final BillJobService billJobService;

    @XxlJob("SyncTodayBillHandler")
    public void runToday() {

        String params = XxlJobHelper.getJobParam();
        int days;
        if (StrUtil.isNotEmpty(params)) {
            days = Integer.parseInt(params);
        } else {
            days = 1;
        }
        Date now = new Date();
        // 查询昨天的微信订单
        List<WechatOrder> wechatOrderList = wechatOrderRepository.findAll(Specifications.between("updatedDate", getMinusDayZero(days), now));
        // 对product_id分组
        billJobService.groupGenerateBill(wechatOrderList);

        List<WechatOrderRefund> refunds = wechatOrderRefundRepository.findAll(Specifications.between("updatedDate", getMinusDayZero(days), now));
        billJobService.groupGenerateRefundBill(refunds);

        // 查询昨天的支付宝自费支付订单
        Specification<AliPayOrder> spec = (root, query, builder) -> builder.and(
                builder.between(root.get("updatedDate"), getMinusDayZero(days), now),
                builder.isFalse(root.get("isInsurance"))
        );
        List<AliPayOrder> aliPayOrderList = aliPayOrderRepository.findAll(spec);
        // 对body分组
        billJobService.groupGenerateAliPayBill(aliPayOrderList);

        // 查询昨天的支付宝自费退款订单
        Specification<AliPayOrderRefund> refundSpec = (root, query, builder) -> builder.and(
                builder.between(root.get("updatedDate"), getMinusDayZero(days), now),
                builder.isFalse(root.get("isInsurance"))
        );
        List<AliPayOrderRefund> aliPayOrderRefunds = aliPayOrderRefundRepository.findAll(refundSpec);
        billJobService.groupGenerateRefundAliPayBill(aliPayOrderRefunds);

        // 查询HIS账单-自费
        // TODO: his比ih多的账单, 逻辑错误, 具体的, 还需要分析
//        Specification<HisBill> hisSpecs = (root, query, builder) -> builder.and(
//                builder.between(root.get("updatedDate"), getMinusDayZero(days), now),
//                builder.or(
//                        builder.isFalse(root.get("insuranceFlag")),
//                        builder.isNull(root.get("insuranceFlag"))
//                )
//        );
//        List<HisBill> hisBills = hisBillRepository.findAll(hisSpecs);
//        log.info("bill-HIS自费订单总量 {}", hisBills.size());
//        billJobService.groupGenerateHisBill(hisBills);

        log.info("结束生成对帐数据-跟his账单比对汇总");

    }

    @XxlJob("SyncAllBillHandler")
//    @Transactional
    public void runAll() {
        Date now = new Date();
        // 删除原来的账单
        List<Specification<Bill>> specs = Lists.newArrayList();
        specs.add(Specifications.isFalse("insuranceFlag"));
        specs.add(Specifications.isNull("insuranceFlag"));
        billRepository.deleteAll(billRepository.findAll(Specifications.or(specs)));
        // 查询所有的微信订单
        List<WechatOrder> wechatOrderList = wechatOrderRepository.findAll(Specifications.lt("updatedDate", now));
        log.info("同步账单总量 {}", wechatOrderList.size());
        // 对product_id分组
        billJobService.groupGenerateBill(wechatOrderList);

        List<WechatOrderRefund> refunds = wechatOrderRefundRepository.findAll(Specifications.lt("updatedDate", now));
        billJobService.groupGenerateRefundBill(refunds);

        // 查询所有的支付宝自费支付订单
        Specification<AliPayOrder> paySpec = (root, query, builder) -> builder.and(
                builder.lessThan(root.get("updatedDate"), now),
                builder.isFalse(root.get("isInsurance"))
        );
        List<AliPayOrder> aliPayOrderList = aliPayOrderRepository.findAll(paySpec);
        // 对body分组
        billJobService.groupGenerateAliPayBill(aliPayOrderList);

        // 查询所有的支付宝自费退款订单
        Specification<AliPayOrderRefund> refundSpec = (root, query, builder) -> builder.and(
                builder.lessThan(root.get("updatedDate"), now),
                builder.isFalse(root.get("isInsurance"))
        );
        List<AliPayOrderRefund> aliPayOrderRefunds = aliPayOrderRefundRepository.findAll(refundSpec);
        billJobService.groupGenerateRefundAliPayBill(aliPayOrderRefunds);
    }

    private static Date getMinusDayZero(int days) {
        // 获取今天的日期
        Date today = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);

        // 将时间设为昨天的零点
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - days);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 获取n天前的零点时间
        return calendar.getTime();
    }
}
