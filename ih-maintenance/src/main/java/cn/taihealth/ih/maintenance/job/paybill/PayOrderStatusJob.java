package cn.taihealth.ih.maintenance.job.paybill;

import cn.taihealth.ih.maintenance.conf.XxlRegister;
import cn.taihealth.ih.maintenance.conf.XxlRegisterEnum;
import cn.taihealth.ih.maintenance.job.paybill.alipay.SyncAlipayOrderStatusJob;
import cn.taihealth.ih.maintenance.job.paybill.wanda.SyncWandaPayOrderStatusJob;
import cn.taihealth.ih.maintenance.job.paybill.wechatorder.SyncWechatInsuranceOrderStatusJob;
import cn.taihealth.ih.maintenance.job.paybill.wechatorder.SyncWechatOrderStatusJob;
import com.gitq.jedi.context.AppContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 这里需要一个定时任务（目前缺失未开发的）：微信对账，具体是：
 * 定时（比如每小时）扫描微信账单表和退款账单表里【未支付、退款中】的账单，
 * 去微信单独查询核对状态，对于我们状态不正确的给予修正。
 *
 * 如果业务操作中有取消订单的动作，还需要考虑：患者钱已付，但我们业务上没收到回调，
 * 业务订单还是支付中或未支付，患者取消订单（状态可能变为已取消），
 * 此时患者等待退款，每小时的定时轮询发现：微信已收款，我们没收款，并且患者已取消业务订单，此时我们的订单应该给患者退款，
 * 并且业务订单和微信支付账单状态此时需要变更为已退款，走退款的流程
 */
@Component
@Slf4j
@AllArgsConstructor
public class PayOrderStatusJob {

    /**
     * 修正非医保账单状态
     */
    @XxlJob("SyncWechatOrderStatusHandler")
    @XxlRegister(cron = "0 0 * * * ?", author = "薛春",
            jobDesc = "对账兜底（包含微信非医保/支付宝非医保），为支付中或退款中的订单单独查询更新。未退款成功的不重试退款")
    public void syncWechatOrderStatusHandler() {
        AppContext.getInstance(SyncWechatOrderStatusJob.class).syncWechatOrderStatusHandler();
        AppContext.getInstance(SyncAlipayOrderStatusJob.class).SyncAliPayOrderStatusHandler();
        // 万达链支付不使用，暂不执行定时任务
//        AppContext.getInstance(SyncWandaPayOrderStatusJob.class).SyncWandaPayOrderStatusHandler();
    }

    /**
     * 修正医保账单状态
     */
    @XxlJob("SyncWechatInsuranceOrderStatusHandler")
    @XxlRegister(cron = "0/30 * * * * ?", author = "薛春",
            jobDesc = "对账兜底（包含微信医保/支付宝医保），为支付中或退款中的订单单独查询更新。未退款成功的不重试退款")
    public void syncWechatInsuranceOrderStatus() {
        AppContext.getInstance(SyncWechatInsuranceOrderStatusJob.class).syncWechatInsuranceOrderStatus();
        AppContext.getInstance(SyncAlipayOrderStatusJob.class).SyncAliPayInsuranceOrderStatusHandler();
        // 万达链支付不使用，暂不执行定时任务
//        AppContext.getInstance(SyncWandaPayOrderStatusJob.class).SyncWandaPayInsuranceOrderStatusHandler();
    }

    /**
     * 重试退款
     */
    @XxlJob("RetryingRefundHandler")
    @XxlRegister(scheduleType = XxlRegisterEnum.ScheduleType.FIX_RATE, cron = "600", author = "薛春",
            jobDesc = "重试退款（包含微信非医保/微信医保/支付宝非医保/支付宝医保），为退款没有成功的微信非医保账单进行重试退款")
    public void retryingRefundHandler() {
        AppContext.getInstance(SyncWechatOrderStatusJob.class).retryingRefundHandler();
        AppContext.getInstance(SyncWechatInsuranceOrderStatusJob.class).retryingRefundHandler();
        AppContext.getInstance(SyncAlipayOrderStatusJob.class).retryingRefundHandler();
    }

}
