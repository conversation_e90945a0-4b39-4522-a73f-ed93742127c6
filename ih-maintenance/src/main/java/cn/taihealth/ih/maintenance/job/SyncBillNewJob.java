package cn.taihealth.ih.maintenance.job;

import cn.hutool.core.util.StrUtil;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.cloud.WechatOrderRefund;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.domain.hospital.BillNew;
import cn.taihealth.ih.maintenance.conf.XxlRegister;
import cn.taihealth.ih.repo.WechatOrderRefundRepository;
import cn.taihealth.ih.repo.WechatOrderRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRefundRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRepository;
import cn.taihealth.ih.repo.hospital.BillNewRepository;
import cn.taihealth.ih.repo.hospital.HisBillRepository;
import cn.taihealth.ih.service.api.job.BillNewJobService;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class SyncBillNewJob {

    // 新对账汇总表
    private final BillNewRepository billNewRepository;
    // 微信订单表
    private final WechatOrderRepository wechatOrderRepository;
    // 微信退款表
    private final WechatOrderRefundRepository wechatOrderRefundRepository;
    // his账单表
    private final HisBillRepository hisBillRepository;
    // 支付宝订单表
    private final AliPayOrderRepository aliPayOrderRepository;
    // 支付宝退款表
    private final AliPayOrderRefundRepository aliPayOrderRefundRepository;

    private final BillNewJobService billNewJobService;

    @XxlJob("SyncTodayBillNewHandler")
    @XxlRegister(cron = "0 0/10 * * * ?", author = "Norris", jobDesc = "新的对账汇总，每10分钟对线上业务数据汇总到ih_bill_new, 比如30,true，true删除所有数据")
    public void runTodayNewBill() throws ParseException {
        String params = XxlJobHelper.getJobParam();
        int days = 1;
        boolean isDeleteToRebuild = false; // 是否删除数据，true 表示删除最近days的数据，并做重建
        log.info("开始生成billNew新的对帐数据-跟his账单比对汇总, 参数{}", params);
        if (StrUtil.isNotEmpty(params)) {
            String[] paramArray = params.split(",");
            if (paramArray.length >= 1) {
                days = Integer.parseInt(paramArray[0]);
            }
            if (paramArray.length >= 2) {
                isDeleteToRebuild = Boolean.valueOf(paramArray[1]);
            }
        }

        Date beforeDaysDate = getMinusDayZero(days);
        Date now = new Date();
        if (isDeleteToRebuild) {
            // 删除原来的账单
            List<Specification<BillNew>> specs = Lists.newArrayList();
            specs.add(Specifications.isFalse("insuranceFlag"));
            specs.add(Specifications.isNull("insuranceFlag"));
            Specification<BillNew> spec = (root, query, builder) -> builder.and(
                 builder.between(root.get("orderOperateTime"), beforeDaysDate, now),
                 builder.or(
                     builder.isFalse(root.get("insuranceFlag")),
                     builder.isNull(root.get("insuranceFlag"))
                 )
             );

            List<BillNew> billNews = billNewRepository.findAll(spec);
            billNewRepository.deleteAll(billNews);
        }
        // 查询n天前0点至今的微信订单，比如days=1，查询昨天0点至今的微信订单
        List<WechatOrder> wechatOrderList = wechatOrderRepository.findAll(
            Specifications.between("payTime", beforeDaysDate, now)
            );
        log.info("billNew-微信支付订单总量 {}", wechatOrderList.size());
        billNewJobService.checkWeChatPayedBills(wechatOrderList);

        // 查询n天前0点至今的微信退款订单，比如days=1，查询昨天0点至今的微信退款订单
        List<WechatOrderRefund> wechatRefunds = wechatOrderRefundRepository.findAll(
            Specifications.between("updatedDate", beforeDaysDate, now)
            );
        log.info("billNew-微信退款订单总量 {}", wechatRefunds.size());
        billNewJobService.checkWeChatRefundBills(wechatRefunds);

        // 查询n天前0点至今的支付宝订单，比如days=1，查询昨天0点至今的支付宝订单
        Specification<AliPayOrder> spec = (root, query, builder) -> builder.and(
                builder.between(root.get("gmtPayment"), beforeDaysDate, now),
                builder.isFalse(root.get("isInsurance"))
        );
        List<AliPayOrder> aliPayOrderList = aliPayOrderRepository.findAll(spec);
        log.info("billNew-支付宝支付订单总量 {}", aliPayOrderList.size());
        billNewJobService.checkAliPayPayedBills(aliPayOrderList);

        // 查询n天前0点至今的支付宝退款订单，比如days=1，查询昨天0点至今的支付宝退款订单
        Specification<AliPayOrderRefund> refundSpec = (root, query, builder) -> builder.and(
                builder.between(root.get("updatedDate"), beforeDaysDate, now),
                builder.isFalse(root.get("isInsurance"))
        );
        List<AliPayOrderRefund> alipayRefunds = aliPayOrderRefundRepository.findAll(refundSpec);
        log.info("billNew-支付宝退款订单总量 {}", alipayRefunds.size());
        billNewJobService.checkAliPayRefundBills(alipayRefunds);

        // 查询HIS账单-自费
        // TODO: his比ih多的账单, 逻辑错误, 具体的, 还需要分析
//        Specification<HisBill> hisSpecs = (root, query, builder) -> builder.and(
//                builder.between(root.get("updatedDate"), beforeDaysDate, now),
//                builder.or(
//                        builder.isFalse(root.get("insuranceFlag")),
//                        builder.isNull(root.get("insuranceFlag"))
//                )
//        );
//        List<HisBill> hisBills = hisBillRepository.findAll(hisSpecs);
//        log.info("billNew-HIS自费订单总量 {}", hisBills.size());
//        billNewJobService.checkHisBill(hisBills);

        log.info("结束本次生成billNew对帐数据");
    }

    // 获取n天前的零点时间，比如传入1，就是昨天的零点时间
    private static Date getMinusDayZero(int days) {
        // 获取今天的日期
        Date today = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        // 获取n天前的日期
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - days);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 获取n天前的零点时间
        return calendar.getTime();
    }
}
