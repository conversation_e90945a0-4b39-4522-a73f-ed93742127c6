package cn.taihealth.ih.maintenance.job.his;

import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.cloud.*;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrderRefund;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.enums.ThirdOrderType;
import cn.taihealth.ih.domain.his.HisInpatientHospitalCharge;
import cn.taihealth.ih.domain.his.HisOutpatientCharge;
import cn.taihealth.ih.domain.his.HisOutpatientChargeGroup;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.OrderExtraInfo;
import cn.taihealth.ih.maintenance.conf.XxlRegister;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.alipay.AliPayOrderRefundRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRepository;
import cn.taihealth.ih.repo.his.HisInpatientHospitalChargeRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeGroupRepository;
import cn.taihealth.ih.repo.his.HisOutpatientChargeRepository;
import cn.taihealth.ih.repo.hospital.HospitalPublicPlatformRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.repo.order.OrderExtraInfoRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.TradeIHBillSummary;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.TradeIHOrderInfo;
import com.gitq.jedi.data.specification.Specifications;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 每天11点 上传前一天的互联网医院交易账单
 * @Author: jzs
 * @Date: 2023-09-06
 */
@Component
@Slf4j
@AllArgsConstructor
public class SyncHisTradeIHBillJob {

    private final HospitalRepository hospitalRepository;

    private final OrderRepository orderRepository;

    private final WechatOrderRepository wechatOrderRepository;

    private final WechatOrderRefundRepository wechatOrderRefundRepository;

    private final OfflineOrderRepository offlineOrderRepository;

    private final HisInpatientHospitalChargeRepository hisInpatientHospitalChargeRepository;

    private final HisOutpatientChargeGroupRepository hisOutpatientChargeGroupRepository;

    private final OrderExtraInfoRepository orderExtraInfoRepository;

    private final HisOutpatientChargeRepository hisOutpatientChargeRepository;

    private final HospitalPublicPlatformRepository hospitalPublicPlatformRepository;

    private final WechatInsuranceOrderRepository wechatInsuranceOrderRepository;

    private final WechatInsuranceOrderRefundRepository wechatInsuranceOrderRefundRepository;

    private final AliPayOrderRepository aliPayOrderRepository;

    private final AliPayOrderRefundRepository aliPayOrderRefundRepository;


    @XxlJob("SyncHisTradeIHBillHandler")
    @XxlRegister(cron = "0 0 1 * * ?", author = "jiangzhenyu", jobDesc = "每天1点 上传前一天的互联网医院交易账单(n表示n天前的0点到今天的0点，最多支持99天; yyyy-MM-dd表示只上传那一天的)")
    public void run() {
        log.info("上传互联网医院交易账单开始");
        String params = XxlJobHelper.getJobParam();
        Date endDate;
        Date startDate;
        if (StringUtils.isEmpty(params) || params.length() < 3) {
            Date now = new Date();
            int days = 1;
            if (StringUtils.isNotEmpty(params)) {
                days = Integer.parseInt(params);
            }
            endDate = TimeUtils.getStartOfDay(now);
            startDate = DateUtils.addDays(new Date(), -days);
        } else {
            Date date = TimeUtils.convert(params);
            startDate = TimeUtils.getStartOfDay(date);
            endDate = TimeUtils.getEndOfDay(date);
        }

        List<TradeIHOrderInfo> orderInfoList = Lists.newArrayList();

        // 查询昨天的微信支付订单
        List<WechatOrder> wechatOrders = wechatOrderRepository.findAll(
                Specifications.between("payTime", startDate, endDate)
        );
        wechatOrderToOrderInfo(wechatOrders, orderInfoList);

        // 查询昨天的微信退款订单
        List<WechatOrderRefund> wechatOrderRefunds = wechatOrderRefundRepository.findAll(
                Specifications.between("updatedDate", startDate, endDate)
        );
        wechatRefundToOrderInfo(wechatOrderRefunds, orderInfoList);

        // 查询昨天的微信医保支付订单
        List<WechatInsuranceOrder> wechatInsuranceOrders = wechatInsuranceOrderRepository.findAll(
                Specifications.between("payTime", startDate, endDate)
        );
        wechatInsuranceOrderToOrderInfo(wechatInsuranceOrders, orderInfoList);

        // 查询昨天的微信医保退款订单
        List<WechatInsuranceOrderRefund> wechatInsuranceOrderRefunds = wechatInsuranceOrderRefundRepository.findAll(
                Specifications.between("updatedDate", startDate, endDate)
        );
        wechatInsuranceRefundToOrderInfo(wechatInsuranceOrderRefunds, orderInfoList);

        // 查询支付宝支付订单（自费+医保）
        List<AliPayOrder> aliPayOrders = aliPayOrderRepository.findAll(
                Specifications.between("gmtPayment", startDate, endDate)
        );
        alipayOrderToOrderInfo(aliPayOrders, orderInfoList);

        // 查询支付宝退款订单（自费+医保）
        List<AliPayOrderRefund> alipayRefunds = aliPayOrderRefundRepository.findAll(Specifications.between(
                "updatedDate", startDate, endDate)
        );
        alipayRefundToOrderInfo(alipayRefunds, orderInfoList);

        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);

        // 根据医院分组
        Map<Long, List<TradeIHOrderInfo>> ordeInfoListByHospital = orderInfoList.stream().collect(Collectors.groupingBy(TradeIHOrderInfo::getHospitalId));

        for (Map.Entry<Long, List<TradeIHOrderInfo>> entry : ordeInfoListByHospital.entrySet()) {
            Hospital hospital = hospitalRepository.getById(entry.getKey());
            List<TradeIHOrderInfo> orders = entry.getValue();
            log.info("医院{}，上传 {} 条交易账单", hospital.getCode(), orders.size());
            TradeIHBillSummary billSummary = new TradeIHBillSummary();
            billSummary.setOrders(orders);
            billSummary.setStart_date(TimeUtils.dateToString(startDate, "yyyyMMddHHmmss"));
            billSummary.setEnd_date(TimeUtils.dateToString(endDate, "yyyyMMddHHmmss"));
            setAmountCount(billSummary);

            try {
                businessService.tradeIHBill(hospital, billSummary);
                log.info("医院{}，上传交易账单成功", hospital.getCode());
            } catch (Exception e) {
                log.error("医院{}，上传交易账单失败: {}", hospital.getCode(), e);
            }
        }
        log.info("上传互联网医院交易账单结束");
    }

    private void setAmountCount(TradeIHBillSummary billSummary) {
        List<TradeIHOrderInfo> orders = billSummary.getOrders();
        if (CollectionUtils.isEmpty(orders)) {
            billSummary.setOrder_count("0");
            billSummary.setSettlement_order_amount_count("0.00");
            billSummary.setRefund_amount_count("0.00");
            billSummary.setOrder_amount_count("0.00");
            billSummary.setRefund_application_amount_count("0.00");
            billSummary.setRecharge_voucher_refund_amount_count("0.00");
            billSummary.setHandling_fee_count("0.00");
            return;
        }
        // 应结订单总金额
        BigDecimal settlementOrderAmountCount = new BigDecimal("0.00");
        // 退款总金额
        BigDecimal refundAmountCount = new BigDecimal("0.00");
        // 订单总金额
        BigDecimal orderAmountCount = new BigDecimal("0.00");
        // 申请退款总金额
        BigDecimal refundApplicationAmountCount = new BigDecimal("0.00");
        //充值券退款总金额
        BigDecimal rechargeVoucherRefundAmountCount = new BigDecimal("0.00");
        // 手续费总金额
        BigDecimal handlingFeeCount = new BigDecimal("0.00");

        for (TradeIHOrderInfo order : orders) {
            settlementOrderAmountCount = settlementOrderAmountCount.add(new BigDecimal(order.getSettlement_order_amount()));
            refundAmountCount = refundAmountCount.add(new BigDecimal(order.getRefund_amount()));
            orderAmountCount = orderAmountCount.add(new BigDecimal(order.getOrder_amount()));
            refundApplicationAmountCount = refundApplicationAmountCount.add(new BigDecimal(order.getRefund_application_amount()));
        }

        billSummary.setOrder_count(orders.size() + "");
        billSummary.setSettlement_order_amount_count(settlementOrderAmountCount.toString());
        billSummary.setRefund_amount_count(refundAmountCount.toString());
        billSummary.setOrder_amount_count(orderAmountCount.toString());
        billSummary.setRefund_application_amount_count(refundApplicationAmountCount.toString());
        billSummary.setRecharge_voucher_refund_amount_count(rechargeVoucherRefundAmountCount.toString());
        billSummary.setHandling_fee_count(handlingFeeCount.toString());
    }

    private void wechatOrderToOrderInfo(List<WechatOrder> wechatOrders, List<TradeIHOrderInfo> orderInfoList) {
        for (WechatOrder order : wechatOrders) {
            TradeIHOrderInfo orderInfo = new TradeIHOrderInfo();
            // 医保/自费
            orderInfo.setOrder_type("0");
            // 支付方式
            orderInfo.setPay_type(getPayType(order.getAppId()));
            // 商品名称
            orderInfo.setProduct_name(getProductName(order.getWechatOrderType()));
            // 医保结算单据号
            orderInfo.setPay_order_id("");
            orderInfo.setTransaction_time(TimeUtils.dateToString(order.getPayTime(), "yyyyMMddHHmmss"));
            orderInfo.setTransaction_id(order.getTransactionId());
            orderInfo.setSerial_no(order.getProductId());
            String settleId = getSettleId(true, order.getProductId(), order.getWechatOrderType());
            orderInfo.setSettle_id(settleId);
            orderInfo.setOut_trade_no(order.getOutTradeNo());

            orderInfo.setTransaction_status("SUCCESS");
            // 支付相关
            orderInfo.setOrder_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setSettlement_order_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setVoucher_amount("0.00");
            orderInfo.setSelf_order_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setMedicare_order_amount("0.00");
            // 退款相关
            orderInfo.setMerchant_refund_number("");
            orderInfo.setRefund_id("");
            orderInfo.setRefund_amount("0.00");
            orderInfo.setSelf_refund_amount("0.00");
            orderInfo.setMedicare_refund_amount("0.00");
            orderInfo.setRecharge_voucher_refund_amount("0.00");
            orderInfo.setRefund_application_amount("0.00");
            orderInfo.setRefund_type("");
            orderInfo.setRefund_status("");

            orderInfo.setHospitalId(order.getHospitalId());

            orderInfoList.add(orderInfo);
        }
    }

    private void wechatRefundToOrderInfo(List<WechatOrderRefund> wechatOrderRefunds, List<TradeIHOrderInfo> orderInfoList) {
        for (WechatOrderRefund refund : wechatOrderRefunds) {
            WechatOrder order = wechatOrderRepository.findByTransactionId(refund.getTransactionId()).orElse(null);
            if (order == null) {
                continue;
            }
            TradeIHOrderInfo orderInfo = new TradeIHOrderInfo();
            // 医保/自费
            orderInfo.setOrder_type("0");
            // 支付方式
            orderInfo.setPay_type(getPayType(order.getAppId()));
            // 商品名称
            orderInfo.setProduct_name(getProductName(order.getWechatOrderType()));
            // 医保结算单据号
            orderInfo.setPay_order_id("");
            // 退款确认时间
            if (StringUtils.isNotEmpty(refund.getSuccessTime())) {
                Date successTime = TimeUtils.convert(refund.getSuccessTime());
                orderInfo.setTransaction_time(TimeUtils.dateToString(successTime, "yyyyMMddHHmmss"));
            }
            orderInfo.setTransaction_id(refund.getTransactionId());
            orderInfo.setSerial_no(order.getProductId());
            String settleId = getSettleId(false, order.getProductId(), order.getWechatOrderType());
            orderInfo.setSettle_id(settleId);
            orderInfo.setOut_trade_no(order.getOutTradeNo());

            orderInfo.setTransaction_status("REFUND");
            // 支付相关
            orderInfo.setOrder_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setSettlement_order_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setVoucher_amount("0.00");
            orderInfo.setSelf_order_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setMedicare_order_amount("0.00");
            // 退款相关
            orderInfo.setMerchant_refund_number(refund.getOutRefundNo());
            orderInfo.setRefund_id(refund.getRefundId());
            orderInfo.setRefund_application_amount(convertCentsToYuan(refund.getAmount()));
            orderInfo.setRefund_amount(convertCentsToYuan(refund.getAmount()));
            orderInfo.setSelf_refund_amount(convertCentsToYuan(refund.getAmount()));
            orderInfo.setMedicare_refund_amount("0.00");
            orderInfo.setRecharge_voucher_refund_amount("0.00");
            orderInfo.setRefund_type("ORIGINAL");
            orderInfo.setRefund_status(getRefundStatus(refund.getStatus()));

            orderInfo.setHospitalId(order.getHospitalId());

            orderInfoList.add(orderInfo);
        }
    }

    private void wechatInsuranceOrderToOrderInfo(List<WechatInsuranceOrder> wechatInsuranceOrders, List<TradeIHOrderInfo> orderInfoList) {
        for (WechatInsuranceOrder order : wechatInsuranceOrders) {
            TradeIHOrderInfo orderInfo = new TradeIHOrderInfo();
            // 医保/自费
            orderInfo.setOrder_type("1");
            // 支付方式
            orderInfo.setPay_type(getPayType(order.getAppId()));
            // 商品名称
            orderInfo.setProduct_name(getProductName(order.getWechatOrderType()));
            // 医保结算单据号
            orderInfo.setPay_order_id(order.getPayOrderId());
            orderInfo.setTransaction_time(TimeUtils.dateToString(order.getPayTime(), "yyyyMMddHHmmss"));
            orderInfo.setTransaction_id(order.getMedTransId());
            orderInfo.setSerial_no(order.getProductId());
            String settleId = getSettleId(true, order.getProductId(), order.getWechatOrderType());
            orderInfo.setSettle_id(settleId);
            orderInfo.setOut_trade_no(order.getHospOutTradeNo());

            orderInfo.setTransaction_status("SUCCESS");
            // 支付相关
            orderInfo.setOrder_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setSettlement_order_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setVoucher_amount("0.00");
            orderInfo.setSelf_order_amount(convertCentsToYuan(order.getCashFee()));
            orderInfo.setMedicare_order_amount(convertCentsToYuan(order.getInsuranceFee()));
            // 退款相关
            orderInfo.setMerchant_refund_number("");
            orderInfo.setRefund_id("");
            orderInfo.setRefund_amount("0.00");
            orderInfo.setSelf_refund_amount("0.00");
            orderInfo.setMedicare_refund_amount("0.00");
            orderInfo.setRecharge_voucher_refund_amount("0.00");
            orderInfo.setRefund_application_amount("0.00");
            orderInfo.setRefund_type("");
            orderInfo.setRefund_status("");

            orderInfo.setHospitalId(order.getHospitalId());

            orderInfoList.add(orderInfo);
        }
    }

    private void wechatInsuranceRefundToOrderInfo(List<WechatInsuranceOrderRefund> wechatInsuranceOrderRefunds, List<TradeIHOrderInfo> orderInfoList) {
        for (WechatInsuranceOrderRefund refund : wechatInsuranceOrderRefunds) {
            WechatInsuranceOrder order = wechatInsuranceOrderRepository.findOneByHospOutTradeNo(refund.getOutTradeNo()).orElse(null);
            if (order == null) {
                continue;
            }
            TradeIHOrderInfo orderInfo = new TradeIHOrderInfo();
            // 医保/自费
            orderInfo.setOrder_type("1");
            // 支付方式
            orderInfo.setPay_type(getPayType(order.getAppId()));
            // 商品名称
            orderInfo.setProduct_name(getProductName(order.getWechatOrderType()));
            // 医保结算单据号
            orderInfo.setPay_order_id(refund.getPayOrdId());
            // 退款确认时间
            if (StringUtils.isNotEmpty(refund.getSuccessTime())) {
                Date successTime = TimeUtils.convert(refund.getSuccessTime());
                orderInfo.setTransaction_time(TimeUtils.dateToString(successTime, "yyyyMMddHHmmss"));
            }
            orderInfo.setTransaction_id(refund.getTransactionId());
            orderInfo.setSerial_no(order.getProductId());
            String settleId = getSettleId(false, order.getProductId(), order.getWechatOrderType());
            orderInfo.setSettle_id(settleId);
            orderInfo.setOut_trade_no(order.getHospOutTradeNo());

            orderInfo.setTransaction_status("REFUND");
            // 支付相关
            orderInfo.setOrder_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setSettlement_order_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setVoucher_amount("0.00");
            orderInfo.setSelf_order_amount(convertCentsToYuan(order.getCashFee()));
            orderInfo.setMedicare_order_amount(convertCentsToYuan(order.getInsuranceFee()));
            // 退款相关
            orderInfo.setMerchant_refund_number(refund.getOutRefundNo());
            orderInfo.setRefund_id(refund.getRefundId());
            orderInfo.setRefund_application_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setRefund_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setSelf_refund_amount(convertCentsToYuan(order.getCashFee()));
            orderInfo.setMedicare_refund_amount(convertCentsToYuan(order.getInsuranceFee()));
            orderInfo.setRecharge_voucher_refund_amount("0.00");
            orderInfo.setRefund_type("ORIGINAL");
            orderInfo.setRefund_status(getRefundStatus(refund.getStatus()));

            orderInfo.setHospitalId(order.getHospitalId());

            orderInfoList.add(orderInfo);
        }
    }

    private void alipayOrderToOrderInfo(List<AliPayOrder> aliPayOrders, List<TradeIHOrderInfo> orderInfoList) {
        for (AliPayOrder order : aliPayOrders) {
            TradeIHOrderInfo orderInfo = new TradeIHOrderInfo();
            // 医保/自费
            orderInfo.setOrder_type(order.isInsurance() ? "1" : "0");
            // 支付方式
            orderInfo.setPay_type("2");
            // 商品名称
            orderInfo.setProduct_name(getProductName(order.getOrderType()));
            // 医保结算单据号
            orderInfo.setPay_order_id(order.isInsurance() ? order.getPayOrderId() : "");
            orderInfo.setTransaction_time(TimeUtils.dateToString(order.getGmtPayment(), "yyyyMMddHHmmss"));
            orderInfo.setTransaction_id(order.getTradeNo());
            orderInfo.setSerial_no(order.getBody());
            String settleId = getSettleId(true, order.getBody(), order.getOrderType());
            orderInfo.setSettle_id(settleId);
            orderInfo.setOut_trade_no(order.getOutTradeNo());

            orderInfo.setTransaction_status("SUCCESS");
            // 支付相关
            orderInfo.setOrder_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setSettlement_order_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setVoucher_amount("0.00");
            orderInfo.setSelf_order_amount(convertCentsToYuan(order.getCashFee()));
            orderInfo.setMedicare_order_amount(convertCentsToYuan(order.getInsuranceFee()));
            // 退款相关
            orderInfo.setMerchant_refund_number("");
            orderInfo.setRefund_id("");
            orderInfo.setRefund_amount("0.00");
            orderInfo.setSelf_refund_amount("0.00");
            orderInfo.setMedicare_refund_amount("0.00");
            orderInfo.setRecharge_voucher_refund_amount("0.00");
            orderInfo.setRefund_application_amount("0.00");
            orderInfo.setRefund_type("");
            orderInfo.setRefund_status("");

            orderInfo.setHospitalId(order.getHospitalId());

            orderInfoList.add(orderInfo);
        }
    }

    private void alipayRefundToOrderInfo(List<AliPayOrderRefund> aliPayOrderRefunds, List<TradeIHOrderInfo> orderInfoList) {
        for (AliPayOrderRefund refund : aliPayOrderRefunds) {
            AliPayOrder order = aliPayOrderRepository.findOneByOutTradeNo(refund.getOutTradeNo()).orElse(null);
            if (order == null) {
                continue;
            }
            TradeIHOrderInfo orderInfo = new TradeIHOrderInfo();
            // 医保/自费
            orderInfo.setOrder_type(refund.isInsurance() ? "1" : "0");
            // 支付方式
            orderInfo.setPay_type("2");
            // 商品名称
            orderInfo.setProduct_name(getProductName(order.getOrderType()));
            // 医保结算单据号
            orderInfo.setPay_order_id(refund.isInsurance() ? refund.getPayOrderId() : "");
            // 退款确认时间
            if (StringUtils.isNotEmpty(refund.getSuccessTime())) {
                Date successTime = TimeUtils.convert(refund.getSuccessTime());
                orderInfo.setTransaction_time(TimeUtils.dateToString(successTime, "yyyyMMddHHmmss"));
            }
            orderInfo.setTransaction_id(refund.getTradeNo());
            orderInfo.setSerial_no(order.getBody());
            String settleId = getSettleId(false, order.getBody(), order.getOrderType());
            orderInfo.setSettle_id(settleId);
            orderInfo.setOut_trade_no(order.getOutTradeNo());

            orderInfo.setTransaction_status("REFUND");
            // 支付相关
            orderInfo.setOrder_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setSettlement_order_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setVoucher_amount("0.00");
            orderInfo.setSelf_order_amount(convertCentsToYuan(order.getCashFee()));
            orderInfo.setMedicare_order_amount(convertCentsToYuan(order.getInsuranceFee()));
            // 退款相关
            orderInfo.setMerchant_refund_number(refund.getRefundNo());
            orderInfo.setRefund_id(refund.getRefundNo());
            orderInfo.setRefund_application_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setRefund_amount(convertCentsToYuan(order.getTotalFee()));
            orderInfo.setSelf_refund_amount(convertCentsToYuan(order.getCashFee()));
            orderInfo.setMedicare_refund_amount(convertCentsToYuan(order.getInsuranceFee()));
            orderInfo.setRecharge_voucher_refund_amount("0.00");
            orderInfo.setRefund_type("ORIGINAL");
            orderInfo.setRefund_status(getRefundStatus(refund.getStatus()));

            orderInfo.setHospitalId(order.getHospitalId());

            orderInfoList.add(orderInfo);
        }
    }

    /**
     * 获取结算单据号/取消结算单据号
     * @param isPayment 是否支付
     * @param businessId 业务表主键
     * @param orderType 业务类型
     */
    private String getSettleId(boolean isPayment, String businessId, ThirdOrderType orderType) {
        String settleId = "";
        switch (orderType) {
            // 线上挂号
            case REGISTER:
            // 线上处方
            case RECIPE:
                Order order = orderRepository.findById(Long.parseLong(businessId)).orElse(null);
                if (order != null) {
                    OrderExtraInfo extraInfo = orderExtraInfoRepository.findByOrderId(order.getId()).orElse(null);
                    if (extraInfo != null && isPayment) {
                        settleId = extraInfo.getSettleId();
                    }
                    if (extraInfo != null && !isPayment && CollectionUtils.isNotEmpty(extraInfo.getRefundDetailNo())) {
                        settleId = extraInfo.getRefundDetailNo().stream().collect(Collectors.joining(","));
                    }
                }
                break;
            case BEFORE_INPATIENT_FEE:
                // 住院缴费
                HisInpatientHospitalCharge charge = hisInpatientHospitalChargeRepository.findById(Long.parseLong(businessId)).orElse(null);
                if (charge != null && isPayment) {
                    settleId = charge.getOut_trade_no();
                }
                if (charge != null && !isPayment && CollectionUtils.isNotEmpty(charge.getSettleId())) {
                    settleId = charge.getSettleId().stream().collect(Collectors.joining(","));
                }
                break;
            case OUTPATIENT_FEE:
                // 门诊缴费
                HisOutpatientChargeGroup group = hisOutpatientChargeGroupRepository.findById(Long.parseLong(businessId)).orElse(null);
                if (group != null) {
                    List<HisOutpatientCharge> hisOutpatientChargeList = hisOutpatientChargeRepository.findAllByHisOutpatientChargeGroupId(group.getId());
                    settleId = hisOutpatientChargeList.stream().map(isPayment ? HisOutpatientCharge::getSettleId : HisOutpatientCharge::getCancelSettleId).collect(Collectors.joining(","));
                }
                break;
            // 线下挂号
            case OUTPATIENT_REGISTER_FEE:
            case OUTPATIENT_NUCLEIC_ACID_FEE:
            case SELF_BILLING:
            case MEDICAL_RECORD_COPY_APPOINTMENT:
            case MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:
                OfflineOrder offlineOrder = offlineOrderRepository.findById(Long.parseLong(businessId)).orElse(null);
                if (offlineOrder != null) {
                    settleId = isPayment ? offlineOrder.getSettleId() : offlineOrder.getCancelSettleId();
                }
                break;
            default:
        }
        return settleId;
    }

    /**
     * 获取微信支付类型 公众号/小程序
     *  默认微信支付方式为小程序
     */
    private String getPayType(String appId) {
        Optional<HospitalPublicPlatform> publicPlatform = hospitalPublicPlatformRepository.findOneByAppId(appId);
        if (publicPlatform.isPresent() && publicPlatform.get().getPlatformType() == PlatformTypeEnum.OFFICIAL_ACCOUNT) {
            return "3";
        }
        return "1";
    }

    /**
     * 获取产品名称
     */
    private String getProductName(ThirdOrderType orderType) {
        switch (orderType) {
            case REGISTER:
                return "挂号";
            case RECIPE:
              return "线上处方";
            case BEFORE_INPATIENT_FEE:
              return "住院缴费";
            case OUTPATIENT_FEE:
                return "门诊缴费";
            case OUTPATIENT_REGISTER_FEE:
                return "预约挂号";
            case OUTPATIENT_NUCLEIC_ACID_FEE:
                return "核酸预约";
            case SELF_BILLING:
                return "自助开单";
            case MEDICAL_RECORD_COPY_APPOINTMENT:
                return "病案复印预约";
            case MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:
                return "病案复印预约补缴";
            default:
                return "";
        }
    }

    /**
     * 获取退款状态
     */
    private String getRefundStatus(String status) {
        switch (status) {
            case "SUCCESS":
                return "SUCCESS";
            case "PROCESSING":
            case "REFUNDING":
                return "PROCESSING";
            case "FAIL":
                return "FAIL";
            case "ABNORMAL":
            case "CHANGE":
                return "CHANGE";
            default:
                return "";
        }
    }

    /**
     * 金额分转元，并转换成字符串
     */
    public static String convertCentsToYuan(Integer cents) {
        if (cents == null) {
            return "0.00";
        }
        double yuan = cents / 100.0;
        return String.format("%.2f", yuan);
    }

}
