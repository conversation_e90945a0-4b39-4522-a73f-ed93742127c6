package cn.taihealth.ih.maintenance.job;

import static org.apache.catalina.manager.Constants.CHARSET;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import cn.taihealth.ih.domain.TIMHistoryFile;
import cn.taihealth.ih.domain.VideoRoom;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.UploadType;
import cn.taihealth.ih.realname.MacUtil;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.TIMHistoryFileRepository;
import cn.taihealth.ih.repo.VideoRoomRepository;
import cn.taihealth.ih.service.api.HospitalSettingService;
import cn.taihealth.ih.service.api.LockService;
import cn.taihealth.ih.service.api.UploadResource;
import cn.taihealth.ih.service.api.UploadService;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.service.impl.im.IMException;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.regex.Matcher;
import javax.xml.bind.DatatypeConverter;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Fetch tencent Vod history and save the history to aliyun OSS
 */
@Component
public class FetchTencentVodHistory {
    private static final Logger log = LoggerFactory.getLogger(FetchTencentVodHistory.class);

    private final VideoRoomRepository videoRoomRepository;

    private final HospitalSettingService hospitalSettingService;

    private final TIMHistoryFileRepository timHistoryFileRepository;

    private final LockService lockService;

    public FetchTencentVodHistory(VideoRoomRepository videoRoomRepository,
                                  HospitalSettingService hospitalSettingService,
                                  TIMHistoryFileRepository timHistoryFileRepository,
                                  LockService lockService) {
        this.videoRoomRepository = videoRoomRepository;
        this.hospitalSettingService = hospitalSettingService;
        this.timHistoryFileRepository = timHistoryFileRepository;
        this.lockService = lockService;
    }

    //@Scheduled(cron = "0 0 3 * * ?")
    @Transactional
    public void run() {
        log.info("Started to download TIM vod ...");
        try {
            lockService.executeWithLock("FetchTencentVodHistory",() -> {
                upload();
                return 0;
            });

        } catch (Throwable e) {
            log.error("Failed downloading TIM vod file.", e);
        } finally {
            log.info("Finished downloading TIM vod file");
        }
    }

    public void upload() throws IOException {

        User system = AppContext.getInstance(UserService.class).getSystem();
        for (Hospital hospital : AppContext.getInstance(HospitalRepository.class).findAll()) {
            if ("default".equals(hospital.getCode())) {
                continue;
            }

            String timAppId = String.valueOf(HospitalSettingsHelper.getLong(hospital, HospitalSettingKey.TIM_APP_ID));
            if (StringUtils.isEmpty(timAppId)) {
                return;
            }
            Date lastTime = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.TIM_VOD_DATE, Date.class);
            if (lastTime == null) {
                lastTime = new Date(0);
//            lastTime = (Date) DataTypes.DATE.fromString("2018-09-19T00:00:00.000+0000", null);
            }
            try {
                int offset = 0;
                List<MediaInfoSet> list;
                do {
                    list = getVodList(hospital, lastTime, offset += 10, 10).MediaInfoSet;
                    list.forEach(u -> {
                        // 录制视频需要存储，其他视频不存储
                        // Record: 来自录制。如直播录制、直播时移录制等。
                        // Upload：来自上传。如拉取上传、服务端上传、客户端 UGC 上传等。
                        // VideoProcessing：来自视频处理。如视频拼接、视频剪辑等。
                        // Unknown：未知来源。1: 历史短视频服务，客户端 UGC 上传
                        if ("Record".equalsIgnoreCase(u.BasicInfo.SourceInfo.SourceType)) {
                            String roomId = "";
                            String SourceContext = u.BasicInfo.SourceInfo.SourceContext;
                            Matcher urlMatcher = Constants.URL_MATCH_PATTERN.matcher(SourceContext);
                            String appId = null;
                            if (urlMatcher.matches()) {
                                String paramStr = urlMatcher.group("param");
                                Matcher paramMatcher = Constants.URL_MATCH_PATTERN.matcher(paramStr);
                                while (paramMatcher.find()) {
                                    if ("groupid".equalsIgnoreCase(paramMatcher.group("attr"))) {
                                        roomId = paramMatcher.group("value");
                                    }
                                    if ("sdkappid".equalsIgnoreCase(paramMatcher.group("attr"))) {
                                        appId = paramMatcher.group("value");
                                    }
                                }
                            }
                            if (StringUtils.isEmpty(roomId) || !Objects.equals(timAppId, appId)) {
                                // 没有groupId或不是一个appId下的，不处理
                                return;
                            }
                            String groupId;
                            Optional<VideoRoom> videoRoomO = videoRoomRepository.findOneByRoomId(
                                roomId);
                            if (videoRoomO.isPresent()) {
                                groupId = videoRoomO.get().getOrder().getImGroupId();
                            } else {
                                // 数据不是在这个环境上产生的，不处理
                                return;
                            }
                            Date videoDate = (Date) DataTypes.DATE.fromString(u.BasicInfo.CreateTime, "yyyy-MM-dd'T'HH:mm:ss'Z'");
                            Upload upload = AppContext.getInstance(UploadService.class).upload(system,
                                UploadResource.of(u.BasicInfo.MediaUrl, UploadType.TIM_HISTORY, null));
                            TIMHistoryFile historyFile = new TIMHistoryFile();
                            historyFile.setGroupId(groupId);
                            historyFile.setUuid(u.FileId);
                            historyFile.setCreatedDate(videoDate);
                            historyFile.setUpload(upload);
                            timHistoryFileRepository.save(historyFile);
                            hospitalSettingService.save(hospital, HospitalSettingKey.TIM_VOD_DATE, videoDate);
                        }
                    });
                } while (list.size() == 10);
            } catch (Exception e) {
                log.error("同步实时音视频文件出错: hospitalCode - " + hospital.getCode(), e);
            }
        }
    }

    private SearchMediaResponse getVodList(Hospital hospital, Date start, int offset, int limit) throws Exception {
        String secretKey = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.TIM_SECRET_KEY);
        String service = "vod";
        String host = "vod.tencentcloudapi.com";
        String region = "ap-guangzhou";
        String action = "SearchMedia";
        String version = "2018-07-17";
        String algorithm = "TC3-HMAC-SHA256";
        Date now = new Date();
        String timestamp = String.valueOf(now.getTime() / 1000);
        String date = DataTypes.DATE.asString(now, "yyyy-MM-dd");

        SearchMedia searchMedia = new SearchMedia(offset, limit, start, new SortBy("CreateTime", "Asc"));
        String search = StandardObjectMapper.stringify(searchMedia);
        System.out.println(search);
        String httpRequestMethod = "POST";
        String canonicalUri = "/";
        String canonicalQueryString = "";
        String canonicalHeaders = "content-type:application/json; charset=utf-8\n" + "host:" + host + "\n";
        String signedHeaders = "content-type;host";
        String hashedRequestPayload = DigestUtils.sha256Hex(search);
        String canonicalRequest = httpRequestMethod + "\n" + canonicalUri + "\n" + canonicalQueryString + "\n"
            + canonicalHeaders + "\n" + signedHeaders + "\n" + hashedRequestPayload;

        // ************* 步骤 2：拼接待签名字符串 *************
        String credentialScope = date + "/" + service + "/" + "tc3_request";
        String hashedCanonicalRequest = DigestUtils.sha256Hex(canonicalRequest.getBytes(CHARSET));
        String stringToSign = algorithm + "\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;

        // ************* 步骤 3：计算签名 *************
        byte[] secretDate = MacUtil.sign256(("TC3" + secretKey).getBytes(CHARSET), date);
        byte[] secretService = MacUtil.sign256(secretDate, service);
        byte[] secretSigning = MacUtil.sign256(secretService, "tc3_request");
        String signature = DatatypeConverter.printHexBinary(MacUtil.sign256(secretSigning, stringToSign)).toLowerCase();

        // ************* 步骤 4：拼接 Authorization *************
        String authorization = algorithm + " " + "/" + credentialScope + ", "
            + "SignedHeaders=" + signedHeaders + ", " + "Signature=" + signature;

        Map<String, String> headers = new TreeMap<String, String>();
        headers.put("Authorization", authorization);
        headers.put("Host", host);
        headers.put("X-TC-Action", action);
        headers.put("X-TC-Timestamp", timestamp);
        headers.put("X-TC-Version", version);
        headers.put("X-TC-Region", region);
        String response = OkHttpUtils.post("https://cvm.tencentcloudapi.com/", search, headers)
            .body().string();
        SearchResponse r = StandardObjectMapper.readValue(response, new TypeReference<>() {});
        if (r.Response == null ||r.Response.MediaInfoSet == null) {
            throw new IMException("查询video列表失败: " + response);
        }
        return r.Response;
    }

    public static class SortBy {
        @JsonProperty
        private String Field;
        @JsonProperty
        private String Order;

        public SortBy(String field, String order) {
            Field = field;
            Order = order;
        }
    }

    public static class SearchMedia {
        @JsonProperty
        private int Offset;
        @JsonProperty
        private int Limit;
        @JsonProperty
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private Date StartTime;
        @JsonProperty
        private SortBy Sort;

        public SearchMedia(int offset, int limit, Date startTime, SortBy sort) {
            Limit = limit;
            Offset = offset;
            StartTime = startTime;
            Sort = sort;
        }
    }

    public static class SearchResponse {
        @JsonProperty
        private SearchMediaResponse Response;
    }

    public static class SearchMediaResponse {
        @JsonProperty
        private int TotalCount;
        @JsonProperty
        private String RequestId;
        @JsonProperty
        private List<MediaInfoSet> MediaInfoSet;
        @JsonProperty
        private Error Error;
    }

    public static class Error {
        @JsonProperty
        private String Code;
        @JsonProperty
        private String Message;
    }

    public static class MediaInfoSet {
        @JsonProperty
        private String FileId;
        @JsonProperty
        private BasicInfo BasicInfo;
    }

    public static class BasicInfo {
        @JsonProperty
        private String Name;
        @JsonProperty
        private String Description;
        @JsonProperty
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private String CreateTime;
        @JsonProperty
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private String UpdateTime;
        @JsonProperty
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private String ExpireTime;
        @JsonProperty
        private int ClassId;
        @JsonProperty
        private String ClassName;
        @JsonProperty
        private String ClassPath;
        @JsonProperty
        private String CoverUrl;
        @JsonProperty
        private String Type;
        @JsonProperty
        private String MediaUrl;
        @JsonProperty
        private String[] TagSet;
        @JsonProperty
        private String StorageRegion;
        @JsonProperty
        private String Vid;
        @JsonProperty
        private SourceInfo SourceInfo;
    }

    public static class SourceInfo {
        @JsonProperty
        private String SourceType;
        @JsonProperty
        private String SourceContext;
    }

}
