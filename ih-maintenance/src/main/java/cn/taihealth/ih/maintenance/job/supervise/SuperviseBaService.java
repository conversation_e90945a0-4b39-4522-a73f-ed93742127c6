package cn.taihealth.ih.maintenance.job.supervise;

import cn.hutool.json.JSONUtil;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.hospital.DeptRepository;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.service.util.JpaUtils;
import cn.taihealth.ih.service.util.SuperviseUtil;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.service.SuperviseFactory;
import cn.taihealth.ih.supervise.service.SuperviseService;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class SuperviseBaService {
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final DeptRepository deptRepository;

    private final HospitalRepository hospitalRepository;

    private final UserService userService;

    @XxlJob("SuperviseBaHandler")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void run() {
        log.info("Started to SuperviseBaHandler");
        try {
            String params = XxlJobHelper.getJobParam();
            baStart(params);
        } catch (Throwable e) {
            log.error("Failed SuperviseBaHandler", e);
        } finally {
            log.info("Finished SuperviseBaHandler");
        }
    }

    public void baStart(String hospitalCode) {
        JpaUtils.executeInTransaction(() -> {
            // 查询线上医院
            List<Hospital> hospitalList;
            if (StringUtils.isNotBlank(hospitalCode)) {
                hospitalList = Lists.newArrayList(hospitalRepository.findOneByCode(hospitalCode).get());
            } else {
                hospitalList = hospitalRepository.findAll();
            }
            for (Hospital hospital : hospitalList) {
                SuperviseDto dto = SuperviseUtil.getDto(hospital);

                SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
                if (superviseService != null && !"xxx".equals(dto.getCode())) {
                    // 医院备案
                    superviseService.organizationBa(hospital, dto);
                    // 科室备案
                    superviseService.departmentBa(hospital, dto);
                    // 复诊医生备案
                    List<MedicalWorker> hospitalMedicalWorkers = medicalWorkerRepository.findAllByHospital(hospital);
                    for (MedicalWorker hospitalMedicalWorker : hospitalMedicalWorkers) {
                        // 线上医生才需要备案
                        boolean doctor = userService.isDoctor(hospitalMedicalWorker.getUser(), hospital);
                        if (doctor && hospitalMedicalWorker.isEnabled() && !hospitalMedicalWorker.getDeptMedicalWorkers().isEmpty()){
                            superviseService.doctorBa(hospital, hospitalMedicalWorker, dto);
                        }
                        boolean isPharmacist = userService.isPharmacist(hospitalMedicalWorker.getUser(), hospital);
                        if (isPharmacist && hospitalMedicalWorker.isEnabled() && !hospitalMedicalWorker.getDeptMedicalWorkers().isEmpty()){
                            superviseService.pharmacistBa(hospital, hospitalMedicalWorker, dto);
                        }
                    }
                    // 年检信息备案
                    superviseService.annualInspectionInfoBa(hospital, dto);
                }
            }
            return 0;
        });

    }

}
