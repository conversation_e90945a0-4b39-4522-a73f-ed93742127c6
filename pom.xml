<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>2.7.18</version>
    </parent>
    <groupId>cn.taihealth.ih</groupId>
    <artifactId>ih-parent</artifactId>
    <version>2.0.4873</version>
    <packaging>pom</packaging>

    <properties>
        <spring.boot.version>2.7.18</spring.boot.version>
        <jedi.version>2.0.32</jedi.version>

        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

<!--        <spring-framework.version>5.3.31</spring-framework.version>-->
        <swagger.version>1.5.21</swagger.version>

        <redisson.version>3.23.3</redisson.version>

        <hikaricp.version>3.1.0</hikaricp.version>
        <jcache.version>1.0.0</jcache.version>
        <jjwt.version>0.7.0</jjwt.version>
        <liquibase-hibernate5.version>4.22.0</liquibase-hibernate5.version>
        <liquibase-slf4j.version>2.0.0</liquibase-slf4j.version>
        <logstash-logback-encoder.version>4.8</logstash-logback-encoder.version>

        <problem-spring-web.version>0.21.0</problem-spring-web.version>
        <jjwt.version>0.7.0</jjwt.version>

        <byte.buddy.version>1.9.7</byte.buddy.version>

        <commons-io.version>2.5</commons-io.version>
        <commons-lang.version>3.5</commons-lang.version>

        <binarywang.wexin.version>4.6.0</binarywang.wexin.version>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <liquibase.version>4.22.0</liquibase.version>
        <openfeign.version>11.9.1</openfeign.version>
        <commons-validator.version>1.7</commons-validator.version>
<!--        <tomcat.version>9.0.83</tomcat.version>-->
<!--        <spring-security.version>5.6.12</spring-security.version>-->
    </properties>

    <modules>
        <module>ih-commons</module>
        <module>ih-domain</module>
        <module>ih-service</module>
        <module>ih-repo</module>
        <module>ih-dao</module>
        <module>ih-rest</module>
        <module>ih-api-server</module>
        <module>ih-spring-security</module>
        <!--        <module>ih-protobuf</module>-->
        <module>ih-realname</module>
        <module>ih-maintenance</module>
        <module>ih-wechat-service</module>
        <module>ih-ca</module>
        <module>ih-mq</module>
        <module>ih-supervise</module>
        <!--        <module>ih-websocket-server</module>-->
    </modules>

    <dependencyManagement>
        <dependencies>

<!--            <dependency>-->
<!--                <groupId>org.springframework.security</groupId>-->
<!--                <artifactId>spring-security-bom</artifactId>-->
<!--                <version>${spring-security.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.springframework</groupId>-->
<!--                <artifactId>spring-framework-bom</artifactId>-->
<!--                <version>${spring-framework.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.gitq</groupId>
                <artifactId>gitq-app-parent</artifactId>
                <version>2.0.19</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.quartz-scheduler</groupId>
                        <artifactId>quartz</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-commons</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-mq</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-ca</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-supervise</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-codec</artifactId>
                <version>1.14</version>
            </dependency>

            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>8.1.3.140</version>
            </dependency>
            <dependency>
                <groupId>org.dm</groupId>
                <artifactId>DmDialect-for-hibernate</artifactId>
                <version>5.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-transcoder</artifactId>
                <version>1.14</version>
            </dependency>

            <dependency>
                <groupId>xml-apis</groupId>
                <artifactId>xml-apis</artifactId>
                <version>1.4.01</version>
            </dependency>

            <dependency>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-core</artifactId>
                <version>4.22.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.2.3</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.2.3</version>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-api-server</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>cn.taihealth.ih</groupId>-->
            <!--                <artifactId>ih-protobuf</artifactId>-->
            <!--                <version>${project.version}</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-maintenance</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.5</version>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.11</version>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-realname</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-repo</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>com.vividsolutions</groupId>
                <artifactId>jts</artifactId>
                <version>1.13</version>
            </dependency>
            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-rest</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-wechat-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.taihealth.ih</groupId>
                <artifactId>ih-spring-security</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.11</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>org.glassfish.jersey.media</groupId>-->
<!--                <artifactId>jersey-media-json-jackson</artifactId>-->
<!--                <version>2.26</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>org.glassfish.jersey.core</groupId>-->
<!--                <artifactId>jersey-client</artifactId>-->
<!--                <version>${jersey.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>org.glassfish.jersey.core</groupId>-->
<!--                <artifactId>jersey-common</artifactId>-->
<!--                <version>${jersey.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>org.glassfish.jersey.inject</groupId>-->
<!--                <artifactId>jersey-hk2</artifactId>-->
<!--                <version>2.26</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>org.glassfish.hk2</groupId>-->
<!--                <artifactId>hk2-locator</artifactId>-->
<!--                <version>2.5.0-b32</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>org.glassfish.hk2</groupId>-->
<!--                <artifactId>hk2-api</artifactId>-->
<!--                <version>2.5.0-b32</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte.buddy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.3-jre</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-hibernate-53</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>2.3.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>2.3.0.1</version>
            </dependency>

            <dependency>
                <groupId>de.ruedigermoeller</groupId>
                <artifactId>fst</artifactId>
                <version>3.0.3</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-core</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-jaxrs</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-servlet</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>

            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>20171018</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/javax.validation/validation-api -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>2021.0.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>2021.0.1.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
<!--            <dependency>-->
<!--                <groupId>com.mysql</groupId>-->
<!--                <artifactId>mysql-connector-j</artifactId>-->
<!--                <version>8.2.0</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>3.1.7</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.12.0</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>1.14.0</version>
            </dependency>

            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>2.5.1</version>
            </dependency>

            <dependency>
                <groupId>org.zalando</groupId>
                <artifactId>problem</artifactId>
                <version>${problem-spring-web.version}</version>
            </dependency>

            <dependency>
                <groupId>org.zalando</groupId>
                <artifactId>problem-spring-web</artifactId>
                <version>${problem-spring-web.version}</version>
            </dependency>

            <dependency>
                <groupId>org.tuckey</groupId>
                <artifactId>urlrewritefilter</artifactId>
                <version>4.0.4</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikaricp.version}</version>
            </dependency>

            <!-- itext -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>5.5.13.2</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-asian</artifactId>
                <version>5.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf.tool</groupId>
                <artifactId>xmlworker</artifactId>
                <version>5.5.8</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.11.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.lowagie</groupId>
                <artifactId>itext</artifactId>
                <version>2.1.7</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.google.protobuf</groupId>-->
            <!--                <artifactId>protobuf-java</artifactId>-->
            <!--                <version>${protobuf.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${binarywang.wexin.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jodd</groupId>
                        <artifactId>jodd-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-open</artifactId>
                <version>${binarywang.wexin.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.github.wxpay</groupId>-->
<!--                <artifactId>wxpay-sdk</artifactId>-->
<!--                <version>0.0.3</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.xikang</groupId>
                <artifactId>supervise-sdk-client</artifactId>
                <version>0.1.8</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <groupId>org.springframework.boot</groupId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>3.1.784</version>
            </dependency>
            <dependency>
                <groupId>com.hitachivantara</groupId>
                <artifactId>hitachivantara-java-sdk-core</artifactId>
                <version>0.4.475</version>
            </dependency>
            <dependency>
                <groupId>com.hitachivantara</groupId>
                <artifactId>hitachivantara-java-sdk-hcp</artifactId>
                <version>0.4.475</version>
            </dependency>

            <dependency>
                <groupId>com.gitq.jedi</groupId>
                <artifactId>jedi-commons</artifactId>
                <version>${jedi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.gitq.jedi</groupId>
                <artifactId>jedi-data</artifactId>
                <version>${jedi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.gitq.jedi</groupId>
                <artifactId>jedi-context</artifactId>
                <version>${jedi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.gitq.jedi</groupId>
                <artifactId>jedi-jinguist</artifactId>
                <version>${jedi.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.inject</groupId>
                <artifactId>javax.inject</artifactId>
                <version>1</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>3.0.2</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.12.5</version>
            </dependency>
            <dependency>
                <groupId>org.ocpsoft.prettytime</groupId>
                <artifactId>prettytime</artifactId>
                <version>5.0.7.Final</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.13.0</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>4.38.183.ALL</version>
            </dependency>
            <!--电子健康卡调用第三方接口所需要的jar包-->
            <dependency>
                <groupId>com.healthcard</groupId>
                <artifactId>healthcard-java-sdk-hbzzapi</artifactId>
                <version>1.0.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
        </pluginRepository>
        <pluginRepository>
            <id>atlassian-3rdparty</id>
            <url>https://packages.atlassian.com/maven-3rdparty</url>
        </pluginRepository>
    </pluginRepositories>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>atlassian-3rdparty</id>
            <url>https://packages.atlassian.com/maven-3rdparty</url>
            <releases>
                <enabled>true</enabled>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
        </repository>
        <repository>
            <id>lib</id>
            <name>lib</name>
            <url>file://${project.basedir}${file.separator}lib</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>superviseLib</id>
            <name>superviseLib</name>
            <url>file://${project.basedir}${file.separator}ih-supervise${file.separator}lib</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <findbugs.skip>true</findbugs.skip>
            </properties>
            <build>
                <plugins>
                    <!-- Run tests in parallel in the dev mode-->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>2</threadCount>
                            <perCoreThreadCount>true</perCoreThreadCount>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.19.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>1.4.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.1</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.shared</groupId>
                            <artifactId>maven-filtering</artifactId>
                            <version>3.1.1</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.5.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>3.0.4</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <groupId>org.eluder.coveralls</groupId>
                    <artifactId>coveralls-maven-plugin</artifactId>
                    <version>4.3.0</version>
                </plugin>
                <!--                <plugin>-->
                <!--                    <groupId>org.apache.maven.plugins</groupId>-->
                <!--                    <artifactId>maven-project-info-reports-plugin</artifactId>-->
                <!--                    <version>2.9</version>-->
                <!--                </plugin>-->
                <plugin>
                    <groupId>kr.motd.maven</groupId>
                    <artifactId>sphinx-maven-plugin</artifactId>
                    <version>1.5.3.Final</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <release>${java.version}</release>
                    <encoding>UTF-8</encoding>
                    <skip>true</skip>
                    <!--                    <source>${maven.compiler.source}</source>-->
                    <!--                    <target>${maven.compiler.target}</target>-->
                    <!--                    <optimize>true</optimize>-->
                    <!--                    <debug>true</debug>-->
                    <!--                    <forceJavacCompilerUse>true</forceJavacCompilerUse>-->
                    <!--                    <showWarnings>true</showWarnings>-->
                </configuration>
                <!--                <dependencies>-->
                <!--                    <dependency>-->
                <!--                        <groupId>org.codehaus.plexus</groupId>-->
                <!--                        <artifactId>plexus-compiler-javac-errorprone</artifactId>-->
                <!--                        <version>2.8.8</version>-->
                <!--                    </dependency>-->
                <!--                    <dependency>-->
                <!--                        <groupId>com.google.errorprone</groupId>-->
                <!--                        <artifactId>error_prone_core</artifactId>-->
                <!--                        <version>2.9.0</version>-->
                <!--                    </dependency>-->
                <!--                    <dependency>-->
                <!--                        <groupId>com.sun.jersey</groupId>-->
                <!--                        <artifactId>jersey-client</artifactId>-->
                <!--                        <version>1.19.4</version>-->
                <!--                    </dependency>-->
                <!--                </dependencies>-->
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>-Duser.language=en -Duser.region=US</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                    <preparationGoals>clean test</preparationGoals>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
