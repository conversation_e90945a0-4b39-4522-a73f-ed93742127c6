package cn.taihealth.ih.rest.controller.management;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.ReportApi;
import cn.taihealth.ih.repo.cloud.ReportApiRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.dto.ReportApiDTO;
import cn.taihealth.ih.service.impl.filter.reportapi.ReportApiSearch;
import cn.taihealth.ih.service.vm.IhPage;
import com.gitq.jedi.data.specification.Specifications;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

@RestController
@RequestMapping(value = {"/admin"})
@Api(tags = "m端-上报接口管理", description = "m端-上报接口管理")
public class SystemReportApiResource {

    private final ReportApiRepository reportApiRepository;

    public SystemReportApiResource(ReportApiRepository reportApiRepository) {
        this.reportApiRepository = reportApiRepository;
    }

    @GetMapping("/report_apis")
    @ApiOperation(value = "查询全部上报接口（分页）")
    public IhPage<ReportApiDTO> getAllReportApis(
        @ApiParam("查询条件 query=name:网络咨询服务 code:9527 report:true") @RequestParam(name = "query", required = false) String query,
        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "createdDate", "id");
        Hospital hospital = CurrentHospital.getOrDefault();
        Specification<ReportApi> specification = Specifications.and(Specifications.eq("hospital", hospital),
            ReportApiSearch.of(query).toSpecification());
        return new IhPage<>(reportApiRepository.findAll(specification, page).map(ReportApiDTO::new));
    }

    @PostMapping("/report_apis")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "添加上报接口")
    public ReportApiDTO createSupervisePlatform(
        @ApiParam("上报接口信息") @Valid @RequestBody ReportApiDTO dto) {
        Hospital hospital = CurrentHospital.getOrDefault();
        if (reportApiRepository.findOneByHospitalAndCode(hospital, dto.getCode())
            .isPresent()) {
            throw ErrorType.REPORT_API_CODE_ALREADY_USED.toProblem();
        }
        ReportApi reportApi = new ReportApi();
        reportApi.setHospital(hospital);
        reportApi.setReport(dto.getReport());
        reportApi.setCode(dto.getCode());
        reportApi.setServiceUrl(dto.getServiceUrl());
        reportApi.setName(dto.getName());
        return new ReportApiDTO(reportApiRepository.save(reportApi));
    }

    @PutMapping("/report_apis/{id}")
    @ApiOperation("修改指定监管平台信息")
    public void updateSupervisePlatform(@PathVariable Long id,
                                        @ApiParam("待修改的上报接口信息") @Valid @RequestBody ReportApiDTO dto) {
        Hospital hospital = CurrentHospital.getOrDefault();
        ReportApi reportApi = reportApiRepository.getById(id);
        if (!StringUtils.equals(reportApi.getCode(), dto.getCode()) &&
            reportApiRepository.findOneByHospitalAndCode(hospital, dto.getCode())
            .isPresent()) {
            throw ErrorType.REPORT_API_CODE_ALREADY_USED.toProblem();
        }
        if (!Objects.equals(reportApi.getHospital(), hospital)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem();
        }
        reportApi.setName(dto.getName());
        reportApi.setCode(dto.getCode());
        reportApi.setServiceUrl(dto.getServiceUrl());
        reportApi.setReport(dto.getReport());
        reportApiRepository.save(reportApi);
    }

    @DeleteMapping("/report_apis/{id}")
    @ApiOperation("删除指定上传接口信息")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteSupervisePlatform(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrDefault();
        ReportApi reportApi = reportApiRepository.getById(id);
        if (!Objects.equals(reportApi.getHospital(), hospital)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem();
        }
        reportApiRepository.delete(reportApi);
    }

}
