package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.MoreBeanUtils;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.HospitalSupervisePlatform;
import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.cloud.SupervisePlatform;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.OfflineHospitalRepository;
import cn.taihealth.ih.repo.cloud.SupervisePlatformRepository;
import cn.taihealth.ih.repo.hospital.HospitalPublicPlatformRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.HospitalService;
import cn.taihealth.ih.service.api.HospitalSupervisePlatformService;
import cn.taihealth.ih.service.cache.HospitalCache;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.impl.filter.hospitalpublicplatform.HospitalPublicPlatformSearch;
import cn.taihealth.ih.service.impl.filter.offlinehospital.OffHospitalSearch;
import cn.taihealth.ih.service.impl.filter.systemsuperviseplatform.SystemSupervisePlatformSearch;
import cn.taihealth.ih.service.vm.IhPage;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@RestController
@RequestMapping("/hospital")
@Api(tags = "d端-互联网医院管理", description = "d端-互联网医院管理")
public class HospitalResource {

    private final HospitalService hospitalService;
    private final HospitalRepository hospitalRepository;
    private final OfflineHospitalRepository offlineHospitalRepository;
    private final HospitalSupervisePlatformService hospitalSupervisePlatformService;
    private final SupervisePlatformRepository supervisePlatformRepository;

    private final HospitalPublicPlatformRepository hospitalPublicPlatformRepository;

    public HospitalResource(HospitalService hospitalService,
                            HospitalRepository hospitalRepository,
                            OfflineHospitalRepository offlineHospitalRepository,
                            HospitalSupervisePlatformService hospitalSupervisePlatformService,
                            SupervisePlatformRepository supervisePlatformRepository,
                            HospitalPublicPlatformRepository hospitalPublicPlatformRepository) {
        this.hospitalService = hospitalService;
        this.hospitalRepository = hospitalRepository;
        this.offlineHospitalRepository = offlineHospitalRepository;
        this.hospitalSupervisePlatformService = hospitalSupervisePlatformService;
        this.supervisePlatformRepository = supervisePlatformRepository;
        this.hospitalPublicPlatformRepository = hospitalPublicPlatformRepository;
    }

    @GetMapping("/hospitals")
    @ApiOperation("查询医院列表-当前医院")
    @ActionLog(action = "查询医院列表")
    public List<HospitalDTO> listHospitals() {
        Hospital hospital = CurrentHospital.getOrNull();
        HospitalDTO hospitalDTO = new HospitalDTO(hospital, true);
        HospitalSupervisePlatform hsp = hospitalSupervisePlatformService.findByHospital(hospital);
        if (null != hsp) {
            hospitalDTO.setHospitalSupervisePlatformDTO(new HospitalSupervisePlatformDTO(hsp));
        }
        return Lists.newArrayList(hospitalDTO);
    }


    @PutMapping("/hospitals/{id}")
    @ApiOperation("修改指定互联网医院信息(多字段验证)")
    @ActionLog(action = "修改指定互联网医院信息")
    public void updateHospitalNew(@ApiParam("互联网医院的ID号") @PathVariable("id") Long id,
                                  @ApiParam("待更新的医院属性") @Valid @RequestBody HospitalDTO vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Hospital hospitalDto = hospitalRepository.getById(id);
        if (!Objects.equals(hospital, hospitalDto)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem();
        }
        hospitalService.save(hospital, vm);
    }

    @GetMapping("/offline_hospitals")
    @ApiOperation("获取实体医院信息")
    @ActionLog(action = "获取实体医院信息")
    public IhPage<OfflineHospitalDTO> find(@RequestParam(name = "query", required = false) String query,
                                           @RequestParam(name = "page", required = false) Integer pageNo,
                                           @RequestParam(name = "size", required = false) Integer size) {
        if (pageNo == null) {
            pageNo = 0;
        }

        if (size == null) {
            size = Constants.BATCH_SIZE;
        }
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "createdDate", "id");
        OffHospitalSearch search = OffHospitalSearch.of(query);
        return new IhPage<>(offlineHospitalRepository.findAll(search.toSpecification(), page).map(
                OfflineHospitalDTO::new));
    }

    @PostMapping("/offline_hospitals")
    @ApiOperation("新增/修改实体医院")
    public OfflineHospitalDTO addOfflineHospital(@ApiParam("实体医院") @Valid @RequestBody OfflineHospitalDTO vm) {
        Hospital hospital = CurrentHospital.getOrNull();
        if (hospital == null) {
            throw ErrorType.NO_CURRENT_HOSPITAL_HOSPITAL_ERROR.toProblem();
        }
        if (vm.isNew()) {
            if (CollectionUtils.isNotEmpty(hospital.getOfflineHospitals())) {
                throw ErrorType.OFFLINE_HOSPITAL_EXISTS.toProblem();
            }
            if (offlineHospitalRepository.findOneByName(vm.getName()).isPresent()) {
                throw ErrorType.NAME_ALREADY_EXIST.toProblem();
            }
        } else {
            if (CollectionUtils.isEmpty(hospital.getOfflineHospitals())) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem();
            }
            OfflineHospital oldInfo = hospital.getOfflineHospitals().get(0);
            if (Objects.isNull(oldInfo)) {
                throw ErrorType.ILLEGAL_PARAMS.toProblem();
            }
            Optional<OfflineHospital> offlineHospitalOptional = offlineHospitalRepository.findOneByName(vm.getName());
            if (offlineHospitalOptional.isPresent()) {
                if (!Objects.equals(oldInfo.getId(), offlineHospitalOptional.get().getId())) {
                    throw ErrorType.NAME_ALREADY_EXIST.toProblem();
                }
            }
        }

        OfflineHospital offlineHospital = new OfflineHospital();
        MoreBeanUtils.copyPropertiesNonNull(offlineHospital, vm);
        offlineHospital.setCapabilities(StringUtils.join(vm.getCapabilities(), " "));
        hospitalService.save(hospital, offlineHospital);

        return new OfflineHospitalDTO(offlineHospital);
    }

    @PatchMapping("/bind-supervise-platform")
    @ApiOperation("绑定监管平台")
    @ActionLog(action = "绑定监管平台")
    public HospitalDTO bindSupervisePlatform(
            @Valid @RequestBody HospitalSupervisePlatformDTO hospitalSupervisePlatformDTO) {
        Hospital hospital = CurrentHospital.getOrThrow();
        hospitalSupervisePlatformService.save(hospital, hospitalSupervisePlatformDTO);
        AppContext.getInstance(HospitalCache.class).evictCode(hospital.getCode());
        HospitalDTO hospitalDTO = new HospitalDTO(hospital);
        HospitalSupervisePlatform hsp = hospitalSupervisePlatformService.findByHospital(hospital);
        if (null != hsp) {
            hospitalDTO.setHospitalSupervisePlatformDTO(new HospitalSupervisePlatformDTO(hsp));
        }
        return hospitalDTO;
    }

    @PatchMapping("/unbind-supervise-platform")
    @ApiOperation("解绑监管平台")
    @ActionLog(action = "解绑监管平台")
    public HospitalDTO bindSupervisePlatform() {
        Hospital hospital = CurrentHospital.getOrThrow();
        hospitalSupervisePlatformService.deleteByHospital(hospital);
        AppContext.getInstance(HospitalCache.class).evictCode(hospital.getCode());
        return new HospitalDTO(hospital);
    }

    @GetMapping("/supervise_platforms")
    @ApiOperation(value = "查询全部监管平台（分页）")
    @ActionLog(action = "查询全部监管平台")
    public IhPage<SupervisePlatformDTO> findDeptByHospital(
            @ApiParam("查询条件 query=name:四川互联网医院监管平台") @RequestParam(name = "query", required = false) String query,
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "createdDate", "id");
        Specification<SupervisePlatform> specification = SystemSupervisePlatformSearch.of(query).toSpecification();
        return new IhPage<>(supervisePlatformRepository.findAll(specification, page).map(SupervisePlatformDTO::new));
    }

    @GetMapping("/platforms")
    @ApiOperation(value = "查询医院所绑定的公众平台")
    @ActionLog(action = "查询医院所绑定的公众平台")
    public IhPage<HospitalPlatformDTO> getBoundPlatforms(
            @ApiParam("查询条件 query=platformFor:DOCTOR platformType:MINI") @RequestParam(name = "query", required = false) String query,
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable page = PageRequest.of(pageNo, size, Direction.ASC, "createdDate", "id");
        Specification<HospitalPublicPlatform> hospitalPublicPlatformAnd = Specifications
            .and(Specifications.eq("hospital", hospital),
                 HospitalPublicPlatformSearch.of(query).toSpecification());
        return new IhPage<>(hospitalPublicPlatformRepository.findAll(hospitalPublicPlatformAnd, page).map(HospitalPlatformDTO::new));
    }

}
