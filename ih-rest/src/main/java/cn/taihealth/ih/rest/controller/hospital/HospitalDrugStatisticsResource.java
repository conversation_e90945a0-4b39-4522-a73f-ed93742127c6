package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.repo.ExportTaskRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.DrugStoreUserService;
import cn.taihealth.ih.service.api.ExportTaskService;
import cn.taihealth.ih.service.dto.UploadDTO;
import cn.taihealth.ih.service.vm.IhPage;
import cn.taihealth.ih.service.vm.drugstore.DateSelectVM;
import cn.taihealth.ih.service.vm.drugstore.DrugStatisticsDetailVM;
import cn.taihealth.ih.service.vm.drugstore.DrugStatisticsVM;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: zihao
 */
@RestController
@RequestMapping("/hospital")
@Api(tags = "药品管理-药品销售统计", description = "药品管理-药品销售统计")
@Slf4j
@AllArgsConstructor
public class HospitalDrugStatisticsResource {

    private final DrugStoreUserService drugStoreUserService;
    private final ExportTaskRepository exportTaskRepository;
    private final ExportTaskService exportTaskService;


    @ApiOperation("查询药品累计种类，数量，销售额")
    @ActionLog(action = "查询药品累计种类，数量，销售额")
    @GetMapping("/drug/sales/statistics")
    public DrugStatisticsVM getDrugInstruction(@ApiParam("开始时间 格式2023-08-20") @RequestParam(value = "beginDate",
        required = false) String beginDate,
                                               @ApiParam("结束时间 格式2023-09-20") @RequestParam(value = "endDate", required = false) String endDate) {
        DateSelectVM dateSelectVM = new DateSelectVM();
        if (beginDate != null) {
            dateSelectVM.setBeginDate(TimeUtils.getStartOfDay(TimeUtils.convert(beginDate)));
        }
        if (endDate != null) {
            dateSelectVM.setEndDate(TimeUtils.getEndOfDay(TimeUtils.convert(endDate)));
        }
        dateSelectVM.setHospitalCode(CurrentHospital.getCode());
//        dateSelectVM.setEndDate(endDate);
        return drugStoreUserService.getDrugStatistics(dateSelectVM);
    }

    @ApiOperation("查询药品详情（  1. 药品ID、科室、药品名称、单价、数量、单位、总金额）")
    @ActionLog(action = "查询药品详情（  1. 药品ID、科室、药品名称、单价、数量、单位、总金额）")
    @GetMapping("/drug/sales/statistics/detail")
    public IhPage<DrugStatisticsDetailVM> getDrugDetails(
        @ApiParam("开始时间 格式2023-08-20") @RequestParam(value = "beginDate", required = false) String beginDate,
        @ApiParam("结束时间 格式2023-09-20") @RequestParam(value = "endDate", required = false) String endDate,
        @ApiParam("页数") @RequestParam(value = "page", defaultValue = "0", required = false) Integer page,
        @ApiParam("每页的项目数") @RequestParam(value = "size", defaultValue = "10", required = false) Integer size,
        @RequestParam(value = "drugId", required = false) Long drugId) {
        DateSelectVM dateSelectVM = new DateSelectVM();
        if (beginDate != null) {
            dateSelectVM.setBeginDate(TimeUtils.getStartOfDay(TimeUtils.convert(beginDate)));
        }
        if (endDate != null) {
            dateSelectVM.setEndDate(TimeUtils.getEndOfDay(TimeUtils.convert(endDate)));
        }
        dateSelectVM.setDrugId(drugId);
        dateSelectVM.setPage(page);
        dateSelectVM.setSize(size);
        dateSelectVM.setHospitalCode(CurrentHospital.getCode());
//        dateSelectVM.setEndDate(endDate);
        return new IhPage<>(drugStoreUserService.getDrugStatisticsDetailsPage(dateSelectVM));
    }

    @GetMapping("/drug/sales/statistics/details/export")
    @ApiOperation("药品销售统计-导出明细")
    @ActionLog(action = "药品销售统计-导出明细")
    public UploadDTO exportDetail(@ApiParam("开始时间 格式2023-08-20") @RequestParam(value = "beginDate",
        required = false) String beginDate,
                                  @ApiParam("结束时间 格式2023-09-20") @RequestParam(value = "endDate", required = false) String endDate,
                                  @RequestParam(value = "drugId", required = false) Long drugId) {
        DateSelectVM dateSelectVM = new DateSelectVM();
        if (beginDate != null) {
            dateSelectVM.setBeginDate(TimeUtils.getStartOfDay(TimeUtils.convert(beginDate)));
        }
        if (endDate != null) {
            dateSelectVM.setEndDate(TimeUtils.getEndOfDay(TimeUtils.convert(endDate)));
        }
        dateSelectVM.setDrugId(drugId);
        dateSelectVM.setHospitalCode(CurrentHospital.getCode());

//        dateSelectVM.setEndDate(endDate);

//        Hospital hospital = CurrentHospital.getOrThrow();
//        ExportTask exportTask = new ExportTask();
//        exportTask.setName(ExportTaskName.PHYSICIAN_STATISTICS_DETAILS);
//        exportTask.setStatus(ExportTaskStatus.PROCESSING);
//        exportTask.setHospital(hospital);
//        exportTask.setUser(CurrentUser.getOrThrow());
//        exportTask.setStartTime(new Date());
//        exportTaskRepository.save(exportTask);

        List<DrugStatisticsDetailVM> drugStatisticsDetails = drugStoreUserService.getDrugStatisticsDetails(
            dateSelectVM);
        return exportTaskService.exportDrugStatisticsDetails(drugStatisticsDetails, null, CurrentUser.getOrThrow());
    }


}
