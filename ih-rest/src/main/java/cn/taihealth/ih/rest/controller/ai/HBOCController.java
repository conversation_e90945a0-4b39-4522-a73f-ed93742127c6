package cn.taihealth.ih.rest.controller.ai;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.repo.UserRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.ai.HBOCService;
import cn.taihealth.ih.service.dto.ai.*;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/user/hboc")
@Api(tags = "遗传性妇科肿瘤相关服务")
@AllArgsConstructor
@Slf4j
public class HBOCController {

    private HBOCService hbocService;

    @PostMapping("/pedigree")
    @ApiOperation("保存家系图")
    @ActionLog(action = "保存家系图")
    public void savePedigree(@RequestHeader("USER_ID") String userId,
                             @RequestHeader("CONVERSATION_ID") String conversationId,
                             @RequestBody List<PedigreeDTO> pedigreeList) {
        User user = AppContext.getInstance(UserRepository.class).getById(Long.valueOf(userId));
        if (user == null) {
            log.error("根据UserId未获取到用户，无法保存家系图-UserId: ", userId);
            return;
        }
        Hospital hospital = CurrentHospital.getOrThrow();
        hbocService.savePedigree(user, hospital, conversationId, pedigreeList);
    }

    @PostMapping("/pedigree/context")
    @ApiOperation("保存家系图-上下文数据")
    @ActionLog(action = "保存家系图-上下文数据")
    public void savePedigree(@RequestHeader("USER_ID") String userId,
                             @RequestHeader("CONVERSATION_ID") String conversationId,
                             @RequestBody PedigreeContext pedigreeContext) {
        User user = AppContext.getInstance(UserRepository.class).getById(Long.valueOf(userId));
        if (user == null) {
            log.error("根据UserId未获取到用户，无法保存家系图-UserId: ", userId);
            return;
        }
        log.info("接收到生成家系图请求,原始上下文数据：" + pedigreeContext.getContext());
        Hospital hospital = CurrentHospital.getOrThrow();
        hbocService.savePedigreeContext(user, hospital, conversationId, pedigreeContext);
    }

    @GetMapping("/pedigree")
    @ApiOperation("查询家系图")
    @ActionLog(action = "查询家系图")
    public List<PedigreeDTO> listPedigree() {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return hbocService.listPedigree(current, hospital);
    }

    @PostMapping("/crf")
    @ApiOperation("保存CRF表单")
    @ActionLog(action = "保存CRF表单")
    public void saveCaseReportForm(@RequestHeader("USER_ID") String userId,
                                   @RequestHeader("CONVERSATION_ID") String conversationId,
                                   @RequestBody CaseReportFormDTO caseReportFormDTO) {
        User user = AppContext.getInstance(UserRepository.class).getById(Long.valueOf(userId));
        if (user == null) {
            log.error("根据UserId未获取到用户，无法保存CRF表单-UserId: ", userId);
            return;
        }
        log.info("接收到CRF表单数据：" + StandardObjectMapper.stringify(caseReportFormDTO));
        Hospital hospital = CurrentHospital.getOrThrow();
        hbocService.saveCaseReportForm(user, hospital, conversationId, caseReportFormDTO);
    }

    @GetMapping("/followup")
    @ApiOperation("查询随访问卷Code")
    @ActionLog(action = "查询随访问卷Code")
    public QuestionnaireVM getFollowUp() {
        Hospital hospital = CurrentHospital.getOrThrow();
        String questionnaireCode = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SMART_MEDICAL_FOLLOW_UP_CODE);
        QuestionnaireVM questionnaireVM = new QuestionnaireVM();
        questionnaireVM.setCode(questionnaireCode);
        return questionnaireVM;
    }
}
