package cn.taihealth.ih.rest.controller.cloud;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.security.TokenValidator;
import cn.taihealth.ih.domain.SMSCode.CodeType;
import cn.taihealth.ih.domain.UserAddress;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.TencentIM;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.SettingKey;
import cn.taihealth.ih.domain.enums.UploadType;
import cn.taihealth.ih.domain.service.LinkService;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.dto.HospitalDTO;
import cn.taihealth.ih.service.dto.TencentIMDTO;
import cn.taihealth.ih.service.dto.UserAddressDTO;
import cn.taihealth.ih.service.dto.UserDTO;
import cn.taihealth.ih.service.error.InvalidUploadException;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.SettingsHelper;
import cn.taihealth.ih.service.vm.TencentIMVideoVM;
import cn.taihealth.ih.service.vm.UploadVM;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.jinguist.MediaTypes;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 */
@RestController
@RequestMapping
@Api(tags = "当前用户的基本信息", description = "用户基本信息（USER以上权限）")
public class UserProfileResource {

    private static final Logger log = LoggerFactory.getLogger(UserProfileResource.class);

    private final UserService userService;
    private final UploadService uploadService;
    private final UserRepository userRepository;
    private final TokenValidator smsTokenValidator;
    private final UserAddressRepository userAddressRepository;
    private final UploadRepository uploadRepository;

    public UserProfileResource(UserRepository userRepository, UserService userService,
                               UploadService uploadService,
                               UserAddressRepository userAddressRepository,
                               TokenValidator smsTokenValidator,
                               UploadRepository uploadRepository) {
        this.userService = userService;
        this.uploadService = uploadService;
        this.userRepository = userRepository;
        this.smsTokenValidator = smsTokenValidator;
        this.userAddressRepository = userAddressRepository;
        this.uploadRepository = uploadRepository;
    }

    @GetMapping("/user")
    @ApiOperation("获取当前用户")
    @ActionLog(action = "获取当前用户")
    public UserDTO getUser(HttpServletRequest request) {
        String item = request.getHeader("ITEM");
        if (!"PC".equalsIgnoreCase(item)) {
            return new UserDTO(CurrentUser.getOrThrow());
        } else {
            String code = request.getHeader("IH_HOSPITAL");
            return new UserDTO(CurrentUser.getOrThrow(), code);
        }
    }

//    @PostMapping("/user/id_num_identity")
//    @ApiOperation("用户二要素验证")
//    public UserValid useriIdentity(@ApiParam("用户姓名") @RequestParam("name") String name,
//                                      @ApiParam("用户身份证号码") @RequestParam("identityCard") String identityCard) {
//        return Verification.userValid(name, identityCard);
//    }

    @GetMapping("/user/hospital")
    @ApiOperation("获取当前用户所在医院")
    @ActionLog(action = "获取当前用户所在医院")
    public HospitalDTO getHospital() {
        return new HospitalDTO(CurrentHospital.getOrThrow());
    }

    @GetMapping("/user/im")
    @ApiOperation("获取用户的云通讯信息")
    @ActionLog(action = "获取用户的云通讯信息")
    public TencentIMDTO getUserIM() {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        TencentIM tencentIM = userService.getUserIM(hospital, user);
        if (tencentIM == null) {
            return null;
        }
        return new TencentIMDTO(tencentIM);
    }

    @GetMapping("/user/im/video")
    @ApiOperation("获取用户的云通讯信息")
    @ActionLog(action = "获取用户的云通讯信息")
    public TencentIMVideoVM getUserIMVideo() {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        TencentIM tencentIM = userService.getUserIM(hospital, user);
        if (tencentIM == null) {
            return null;
        }
        TencentIMVideoVM vm = new TencentIMVideoVM(tencentIM);
        vm.setAppId(HospitalSettingsHelper.getLong(hospital, HospitalSettingKey.TIM_APP_ID));
        return vm;
    }

    @Timed
    @PutMapping("/user/im")
    @ApiOperation("更新用户的云通讯信息")
    @ActionLog(action = "更新用户的云通讯信息")
    public TencentIMDTO updateUserIM() {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        TencentIM im = userService.updateTencentIM(hospital, user);
        if (im == null) {
            return null;
        }
        return new TencentIMDTO(im);
    }

    @PatchMapping("/user")
    @ApiOperation("更新当前的用户信息")
    @ActionLog(action = "更新当前的用户信息")
    public void updateUser(@ApiParam("用户信息") @Valid @RequestBody UserDTO dto) {
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(current.getUsername(), dto.getUsername())) {
            throw ErrorType.USERNAME_NOT_MATCH.toProblem();
        }

        if (!current.getMobile().equals(dto.getMobile())) {
            String smsCode = dto.getSmsCode();
            if (!smsTokenValidator.validate(dto.getMobile(),
                smsCode, CodeType.RESET_MOBILE.name())) {
                throw ErrorType.INVALID_VALIDATION_TOKEN.toProblem();
            }
        }

        userService.updateUser(current, dto);
    }

    @PostMapping("/user/avatar")
    @ApiOperation("上传用户头像")
    @ActionLog(action = "上传用户头像")
    public ResponseEntity<String> upload(
        @ApiParam(name = "上传的头像文件，不大于5M") @RequestParam("file") MultipartFile file) {
        if (file.getSize() > SettingsHelper.getLong(SettingKey.MAX_ATTACHMENT_SIZE_KB) * 1024L) {
            throw ErrorType.FILE_IS_TOO_BIG.toProblem();
        }

        User user = CurrentUser.getOrThrow();
        log.info("原头像url:{},对应uploadAvatarId:{}",user.getAvatarUrl(),user.getUploadedAvatar().getId());

        Upload upload = uploadService
            .upload(user, UploadResource.of(file, UploadType.AVATAR, null));
        if (!MediaTypes.isImage(file.getOriginalFilename())) {
            throw new InvalidUploadException("File is not an image");
        }

        user.setAvatarType(User.AvatarType.UPLOAD);
        user.setUploadedAvatar(upload);
        user.setAvatarUrl(AppContext.getInstance(LinkService.class).urlOfUserAvatar(user));
        userService.saveUser(user, null);
        log.info("新头像url:{},对应uploadAvatarId:{}",user.getAvatarUrl(),upload.getId());
        return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(user.getAvatarUrl());
    }

    @PostMapping("/user/addresses")
    @ApiOperation(value = "增加/更新用户地址", notes = "如果地址中有id值则选择更新")
    @Transactional
    @ActionLog(action = "更新用户地址")
    public UserAddressDTO putUserAddress(@RequestBody @Valid UserAddressDTO dto) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();

        if (!dto.isNew()) {
            UserAddress address = userAddressRepository.getById(dto.getId());
            if (!Objects.equals(current, address.getUser())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
        }

        return new UserAddressDTO(userService.saveUserAddress(current, dto, hospital));
    }

    @GetMapping("/user/addresses/{id}")
    @ApiOperation(value = "获取用户详情地址")
    @ActionLog(action = "获取用户详情地址")
    public UserAddressDTO getUserAddress(@PathVariable("id") Long id) {
        User current = CurrentUser.getOrThrow();
        UserAddress userAddress = userAddressRepository.getById(id);
        if (!Objects.equals(current, userAddress.getUser())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return new UserAddressDTO(userAddress);
    }

    @DeleteMapping("/user/addresses/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation(value = "删除用户地址")
    @Transactional
    @ActionLog(action = "删除用户地址")
    public void deleteUserAddress(@PathVariable("id") Long id) {
        User current = CurrentUser.getOrThrow();
        UserAddress userAddress = userAddressRepository.getById(id);
        if (!Objects.equals(current, userAddress.getUser())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }

        userAddressRepository.deleteById(id);
    }

    @GetMapping("/user/addresses")
    @ApiOperation("获取用户所有的地址")
    @ActionLog(action = "获取用户所有的地址")
    public List<UserAddressDTO> getUserAddresses() {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return userAddressRepository.findByUserAndHospital(current, hospital).stream().sorted(
            Comparator.comparing(UserAddress::isDefaultAddress, Comparator.reverseOrder())
                .thenComparing(UserAddress::getUpdatedDate, Comparator.reverseOrder()))
            .map(UserAddressDTO::new).collect(Collectors.toList());
    }

    @PatchMapping("/user/eula_agreed")
    @ApiOperation("同意用户协议")
    @Transactional
    @ActionLog(action = "同意用户协议")
    public void saveEulaAgreed(@RequestParam(required = false, name = "agree") Boolean agree) {
        if (agree == null) {
            agree = true;
        }
        User current = CurrentUser.getOrThrow();
        User user = userRepository.getById(current.getId());
        user.setEulaAgreed(agree);
        userService.saveUser(user, null);
    }

    @PostMapping("/user/signature")
    @ApiOperation("上传用户签章")
    @ActionLog(action = "上传用户签章")
    public UploadVM uploadSignature(@RequestBody @Valid UploadVM uploadVM) {
        User user = CurrentUser.getOrThrow();
        //Hospital hospital = CurrentHospital.getOrThrow();
        Upload upload = null;
        if (null != uploadVM.getId()) {
            upload = uploadRepository.getById(uploadVM.getId());
        }
        userService.saveSignature(user, uploadVM);
        return null != upload ? new UploadVM(upload) : uploadVM;
    }

}
