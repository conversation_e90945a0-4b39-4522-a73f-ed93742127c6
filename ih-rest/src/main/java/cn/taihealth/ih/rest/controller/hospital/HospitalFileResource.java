package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.UploadType;
import cn.taihealth.ih.domain.hospital.HospitalFile;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.repo.hospital.HospitalFileRepository;
import cn.taihealth.ih.rest.controller.cloud.FilesResource;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.dto.UploadDTO;
import cn.taihealth.ih.service.vm.IhPage;
import com.gitq.jedi.context.AppContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Author: Moon
 * @Date: 2021/5/17 下午2:29
 */
@RestController
@RequestMapping(value = {"/hospital"})
@Api(tags = "d端-文件管理", description = "d端-文件管理")
public class HospitalFileResource {

    private final UploadRepository uploadRepository;
    private final HospitalFileRepository hospitalFileRepository;

    public HospitalFileResource(UploadRepository uploadRepository,
                                HospitalFileRepository hospitalFileRepository) {
        this.uploadRepository = uploadRepository;
        this.hospitalFileRepository = hospitalFileRepository;
    }

    @PostMapping("/files/upload")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "上传文件 (USER以上权限)", produces = org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
    @ActionLog(action = "上传文件")
    @Transactional
    public UploadDTO upload(@ApiParam(name = "file")
                            @RequestParam("file") MultipartFile file,
                            @ApiParam(name = "type")
                            @RequestParam(value = "type", required = false) String type) {
        Hospital hospital = CurrentHospital.getOrThrow();
        UploadType uploadType = "PUBLIC".equals(type) ? UploadType.PUBLIC : UploadType.HOSPITAL;
        UploadDTO upload = AppContext.getInstance(FilesResource.class).upload(file, uploadType, null);
        HospitalFile hospitalFile = new HospitalFile();
        hospitalFile.setHospital(hospital);
        hospitalFile.setFile(uploadRepository.getById(upload.getId()));
        hospitalFileRepository.save(hospitalFile);
        return upload;
    }

    @GetMapping("/files")
    @ApiOperation(value = "查询医院文件")
    @ActionLog(action = "查询医院文件")
    public IhPage<UploadDTO> listFiles(
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable pageable = PageRequest.of(pageNo, size, Sort.Direction.DESC, "id");
        return new IhPage<>(hospitalFileRepository.findByHospital(hospital, pageable)
                .map(u -> new UploadDTO(u.getFile())));
    }

}
