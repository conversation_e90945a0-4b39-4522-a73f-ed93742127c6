package cn.taihealth.ih.rest.controller.alipay;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.IpUtils;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform.PlatformForEnum;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.HospitalPublicPlatformService;
import cn.taihealth.ih.service.api.alipay.AliPayBusinessService;
import cn.taihealth.ih.service.vm.AliPayUnifiedOrder;
import cn.taihealth.ih.wechat.service.vm.CreateWechatOrderVM;
import cn.taihealth.ih.wechat.service.vm.wechat.MedicalInsuranceParamResult;
import com.alipay.api.AlipayApiException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/alipay")
@Api(tags = "支付宝相关接口")
@AllArgsConstructor
@Slf4j
public class AliPayController {

    private final AliPayBusinessService aliPayBusinessService;
    private final HospitalPublicPlatformService hospitalPublicPlatformService;

    @PostMapping("/order/unified_order")
    @ApiOperation("支付宝统一下单接口")
    @ActionLog(action = "支付宝统一下单接口")
    public AliPayUnifiedOrder getUnifiedOrder(
            @RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
            @RequestHeader(name = Constants.X_USER_PLATFORM, required = false) Long userPlatform,
            @Valid @RequestBody CreateWechatOrderVM vm,
            HttpServletRequest request) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        appId = checkAppId(hospital, appId);
        return aliPayBusinessService.getUnifiedOrder(current, appId, vm, IpUtils.realClientIp(request), hospital,
                userPlatform, false);
    }

    @PostMapping("/apps/{appId}/order/pay")
    @ApiOperation(value = "回调-未支付订单号支付成功")
//    @ThirdPartyLog(action = "回调-未支付订单号支付成功")
    public String payOrder(@PathVariable String appId,
                           HttpServletRequest request) {
        log.info("收到支付宝支付回调--------------------------------------------------------");
        return aliPayBusinessService.payOrderRequest(appId, request);
    }

//    @PostMapping("/alipay/apps/{appId}/order/pay/refund")
//    @ApiOperation(value = "回调-订单退款成功")
//    @ThirdPartyLog(action = "回调-订单退款成功")
//    public String refundOrder(
//        @PathVariable String appId,
//        @Valid @RequestBody String xml) {
//        log.info("订单退款回调--------------------------------------------------------");
//        log.info(xml);
//    }

    private String checkAppId(Hospital hospital, String appId) {
        HospitalPublicPlatform platform;
        if (StringUtils.isBlank(appId) || "null".equals(appId)) {
            platform = hospitalPublicPlatformService.getPlatform(hospital, PlatformTypeEnum.OFFICIAL_ACCOUNT,
                                                                 PlatformForEnum.PATIENT);
        } else {
            platform = hospitalPublicPlatformService.getPlatform(hospital, appId);
        }
        if (platform == null) {
            throw ErrorType.DATA_ACCESS_ERROR.toProblem("医院" + hospital.getName() + "未配置公众平台，请检查");
        }
        return platform.getAppId();
    }

    @GetMapping("/medical_insurance/param")
    @ApiOperation(value = "获取用户支付宝医保参数")
    @ActionLog(action = "获取用户支付宝医保参数")
    public MedicalInsuranceParamResult getMedicalInsuranceUserQuery(@RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
                                                                    @RequestParam String authCode,
                                                                    @RequestParam(required = false) String callUrl) throws AlipayApiException {
        Hospital hospital = CurrentHospital.getOrThrow();
        return aliPayBusinessService.getMedicalInsuranceUserQuery(hospital, appId, authCode, callUrl);
    }

    @PostMapping("/order/insurance/unified_order")
    @ApiOperation("支付宝统一下单接口")
    @ActionLog(action = "支付宝统一下单接口")
    public AliPayUnifiedOrder getInsuranceUnifiedOrder(
            @RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
            @RequestHeader(name = Constants.X_USER_PLATFORM, required = false) Long userPlatform,
            @Valid @RequestBody CreateWechatOrderVM vm,
            HttpServletRequest request) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        appId = checkAppId(hospital, appId);
        return aliPayBusinessService.getUnifiedOrder(current, appId, vm, IpUtils.realClientIp(request), hospital,
                userPlatform, true);
    }

}
