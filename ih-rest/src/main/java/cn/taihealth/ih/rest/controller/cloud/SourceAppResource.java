package cn.taihealth.ih.rest.controller.cloud;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.repo.cloud.SourceAppRepository;
import cn.taihealth.ih.service.api.SourceAppService;
import cn.taihealth.ih.service.dto.SourceAppDTO;
import cn.taihealth.ih.service.vm.IhPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

/**
 */
@RestController
@RequestMapping
@Api(tags = "来源应用", description = "来源应用")
public class SourceAppResource {

    private static final Logger log = LoggerFactory.getLogger(SourceAppResource.class);

    private final SourceAppRepository sourceAppRepository;
    private final SourceAppService sourceAppService;

    public SourceAppResource(SourceAppRepository sourceAppRepository,
                             SourceAppService sourceAppService) {
        this.sourceAppRepository = sourceAppRepository;
        this.sourceAppService = sourceAppService;
    }

    @GetMapping("/source/app_codes/{appCode}")
    @ApiOperation("获取来源应用")
    @ActionLog(action = "获取来源应用")
    public SourceAppDTO getSourceApp(@PathVariable String appCode) {
        return sourceAppService.getByCode(appCode);
    }

    @GetMapping("/source/apps")
    @ApiOperation("获取全部来源应用")
    @ActionLog(action = "获取全部来源应用")
    public IhPage<SourceAppDTO> getSigns(@ApiParam("页数") @RequestParam(value = "page", required = false) Integer page,
                                         @ApiParam("每页的项目数") @RequestParam(value = "size", required = false) Integer size) {
        if (page == null) {
            page = 0;
        }

        if (size == null) {
            size = Constants.BATCH_SIZE;
        }

        Pageable request = PageRequest.of(page, size == 0 ? Constants.BATCH_SIZE : size, Sort.by("id"));
        return new IhPage<>(sourceAppRepository.findAll(request).map(SourceAppDTO::new));
    }

}
