package cn.taihealth.ih.rest.controller.cloud;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.Ticket;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.repo.TicketRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.TicketService;
import cn.taihealth.ih.service.dto.TicketDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 意见反馈,留言咨询
 */
@RestController
@RequestMapping(value = {"/user"})
@Api(tags = "意见反馈,留言咨询", description = "意见反馈，留言咨询")
public class UserTicketResource {

    private final TicketService ticketService;
    private final TicketRepository ticketRepository;

    public UserTicketResource(TicketService ticketService,
                              TicketRepository ticketRepository) {
        this.ticketService = ticketService;
        this.ticketRepository = ticketRepository;
    }

    @ApiOperation("用户新建意见反馈/留言咨询")
    @ActionLog(action = "用户新建评价/留言咨询")
    @PostMapping("/tickets")
    public TicketDTO createTicket(@Valid @RequestBody TicketDTO dto) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrNull();
        Ticket ticket = ticketService.createTicket(current, hospital, dto);
        return new TicketDTO(ticket);
    }

    @ApiOperation("根据id查询某意见反馈/留言咨询")
    @ActionLog(action = "根据id查询某条意见反馈/留言咨询")
    @GetMapping("/tickets/{id}")
    public TicketDTO getTicket(@PathVariable("id") Long id) {
        User current = CurrentUser.getOrThrow();
        Ticket ticket = ticketRepository.getById(id);
        if (!Objects.equals(current, ticket.getUser())) {
            return null;
        }
        return new TicketDTO(ticket);
    }

    @GetMapping("/tickets")
    @ApiOperation("用户获取所有意见反馈/留言咨询")
    @ActionLog(action = "用户获取所有意见反馈/留言咨询")
    public List<TicketDTO> getUserTickets(@ApiParam("区分意见反馈和留言咨询 FEEDBACK:意见反馈 LEAVE_MESSAGE:留言咨询") @RequestParam(value = "type",required = false, defaultValue = "FEEDBACK") String type) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrNull();
        List<Ticket> list = ticketRepository.findAllByHospitalAndAndUserAndAndTypeOrderByCreatedDateDesc(hospital,
                user, Ticket.Type.valueOf(type.toUpperCase()));
        return list.stream().map(TicketDTO::new).collect(Collectors.toList());
    }


}
