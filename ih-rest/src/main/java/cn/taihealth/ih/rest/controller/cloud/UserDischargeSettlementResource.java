package cn.taihealth.ih.rest.controller.cloud;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.service.dto.*;
import com.gitq.jedi.context.AppContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 出院结算mock
 */
@RestController
@RequestMapping(value = {"/user"})
@Api(tags = "患者端-出院结算", description = "患者端-出院结算")
@Slf4j
@AllArgsConstructor
public class UserDischargeSettlementResource {

    @GetMapping("/discharge/information")
    @ApiOperation("出院结算-住院信息")
    @ActionLog(action = "出院结算-住院信息")
    public DischargeSettlementDTO getHospitalizationInformation(@ApiParam("患者id") @RequestParam(name = "patientId") long patientId) {
        DischargeSettlementDTO dischargeSettlementDTO = new DischargeSettlementDTO();
        dischargeSettlementDTO.setAdmissionNumber("12007");
        dischargeSettlementDTO.setStatus("在院");
        dischargeSettlementDTO.setDiagnosis("急性胰腺炎");
        dischargeSettlementDTO.setDeptName("消化营养科");
        dischargeSettlementDTO.setAdvanceAmount(5000);
        dischargeSettlementDTO.setBalance(1900);
        return dischargeSettlementDTO;
    }

    @GetMapping("/discharge/informationDetail")
    @ApiOperation("出院结算-住院清单")
    @ActionLog(action = "出院结算-住院清单")
    public DischargeSettlementDTO getHospitalizationInformationDetail(@ApiParam("患者id") @RequestParam(name = "patientId") long patientId,
    @ApiParam("住院号") @RequestParam("admissionNumber") String admissionNumber
    ) {
        PatientRepository patientRepository = AppContext.getInstance(PatientRepository.class);
        Patient patient = patientRepository.findById(patientId).orElseThrow();
        DischargeSettlementDTO dischargeSettlementDTO = new DischargeSettlementDTO();
        dischargeSettlementDTO.setCardNumber("23051500154");
        dischargeSettlementDTO.setAdmissionNumber("23050600666");
        dischargeSettlementDTO.setStatus("在院");
        dischargeSettlementDTO.setDoctorName("赵医生");
        dischargeSettlementDTO.setDiagnosis("急性胰腺炎");
        dischargeSettlementDTO.setDeptName("消化营养科");
        Date d = new Date();
        Date d1 = new Date(d.getTime() - 2 * 24 * 3600 * 1000L);
        dischargeSettlementDTO.setStartDate(d1);//当前日期前2日
        dischargeSettlementDTO.setAdvanceAmount(5000);
        dischargeSettlementDTO.setBalance(1900);
        PatientDTO patientDTO = new PatientDTO(patient, ElectronicMedicCard.OnlineType.HIS);
        dischargeSettlementDTO.setPatientDTO(patientDTO);
        return dischargeSettlementDTO;
    }

    @GetMapping("/discharge/fees")
    @ApiOperation("出院结算-费用分类")
    @ActionLog(action = "出院结算-费用分类")
    public DischargeFeeDTO getFees(@ApiParam("患者id") @RequestParam(name = "patientId") long patientId,
                                   @ApiParam("住院号") @RequestParam("admissionNumber") String admissionNumber
                                   ) {
        DischargeFeeDTO feeDTO = new DischargeFeeDTO();

        JSONArray array = JSONUtil.parseArray(feeJson);
        List<DischargeFeeDetailDTO> dischargeFeeDetailDTOS = JSONUtil.toList(array, DischargeFeeDetailDTO.class);
        feeDTO.setDetailList(dischargeFeeDetailDTOS);
        return feeDTO;
    }

    @GetMapping("/discharge/fees/{feeId}")
    @ApiOperation("出院结算-费用详情")
    @ActionLog(action = "出院结算-费用详情")
    public DischargeFeeDetailDTO getFeesDetail(@ApiParam("费用类型id") @PathVariable("feeId") long feeId) {
        JSONArray array = JSONUtil.parseArray(feeJson);
        List<DischargeFeeDetailDTO> dischargeFeeDetailDTOS = JSONUtil.toList(array, DischargeFeeDetailDTO.class);
        return dischargeFeeDetailDTOS.stream().filter(a -> a.getId().equals(feeId)).findAny().orElseThrow();
    }

    @GetMapping("/discharge/fees/preSettle")
    @ApiOperation("出院结算-预结算")
    @ActionLog(action = "出院结算-预结算")
    public DischargePreSettleDTO getPreSettle(@ApiParam("结算类型 1:现金支付 2:医保支付") @RequestParam("type") Integer type,
                                              @ApiParam("住院号") @RequestParam("admissionNumber") String admissionNumber
                                              ) {
        DischargePreSettleDTO settleDTO = new DischargePreSettleDTO();
        if (type == 2) {
            settleDTO.setYbBalance(110000);
            settleDTO.setYbBalanceCalcResult(-2419);
            settleDTO.setDepositBalanceCalcResult(4000);
            settleDTO.setFinalAmount(1581);
        } else {
            settleDTO.setYbBalance(1000);
            settleDTO.setYbBalanceCalcResult(4000);
            settleDTO.setDepositBalanceCalcResult(-111419);
            settleDTO.setFinalAmount(-107419);
        }

        return settleDTO;
    }

    @GetMapping("/discharge/fees/settle")
    @ApiOperation("出院结算-结算信息")
    @ActionLog(action = "出院结算-结算信息")
    public DischargeSettleDTO getSettle(@ApiParam("住院号") @RequestParam("admissionNumber") String admissionNumber) {
        DischargeSettleDTO settleDTO = new DischargeSettleDTO();

        return settleDTO;
    }


    private static String feeJson = "[\n" +
            "    {\n" +
            "        \"id\": 1,\n" +
            "        \"feeName\": \"西药费\",\n" +
            "        \"totalAmount\": 4131,\n" +
            "        \"items\": [\n" +
            "            {\n" +
            "                \"itemName\": \"硫酸阿托品片\",\n" +
            "                \"amount\": \"1670\"\n" +
            "                ,\"unit\": \"1.00/瓶\"\n" +
            "            },\n" +
            "            {\n" +
            "                \"itemName\": \"三磷酸腺苷二钠片\",\n" +
            "                \"amount\": \"2461\"\n" +
            "                ,\"unit\": \"1.00/盒\"\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 2,\n" +
            "        \"feeName\": \"中药费\",\n" +
            "        \"totalAmount\": 0,\n" +
            "        \"items\": []\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 3,\n" +
            "        \"feeName\": \"检查费\",\n" +
            "        \"totalAmount\": 252788,\n" +
            "        \"items\": [\n" +
            "            {\n" +
            "                \"itemName\": \"头部X线计算机体层（CT）增强扫描\",\n" +
            "                \"amount\": \"188000\"\n" +
            "                ,\"unit\": \"1次\"\n" +
            "            },\n" +
            "            {\n" +
            "                \"itemName\": \"头部磁共振扫描（1.7T）\",\n" +
            "                \"amount\": \"46700\"\n" +
            "                ,\"unit\": \"1次\"\n" +
            "            },\n" +
            "            {\n" +
            "                \"itemName\": \"人乳头瘤病毒（HPV）核酸检测\",\n" +
            "                \"amount\": \"14240\"\n" +
            "                ,\"unit\": \"1次\"\n" +
            "            },\n" +
            "            {\n" +
            "                \"itemName\": \"神经元特异性烯醇化酶测定（NSE）\",\n" +
            "                \"amount\": \"3848\"\n" +
            "                ,\"unit\": \"1次\"\n" +
            "            }\n" +
            "        ]\n" +
            "    }\n" +
            "    ,\n" +
            "    {\n" +
            "        \"id\": 4,\n" +
            "        \"feeName\": \"治疗费\",\n" +
            "        \"totalAmount\": 9400,\n" +
            "        \"items\": [\n" +
            "            {  \n" +
            "                \"itemName\": \"住院诊查费\",  \n" +
            "                \"amount\": \"9400\"  \n" +
            "                ,\"unit\": \"2天\"\n" +
            "              }\n" +
            "        ]\n" +
            "    }\n" +
            "    ,\n" +
            "    {\n" +
            "        \"id\": 5,\n" +
            "        \"feeName\": \"护理费\",\n" +
            "        \"totalAmount\": 4000,\n" +
            "        \"items\": [\n" +
            "            {  \n" +
            "                \"itemName\": \"III级护理\",  \n" +
            "                \"amount\": \"4000\"  \n" +
            "                ,\"unit\": \"2天\"\n" +
            "              }\n" +
            "        ]\n" +
            "    }\n" +
            "    ,\n" +
            "    {\n" +
            "        \"id\": 6,\n" +
            "        \"feeName\": \"住宿费\",\n" +
            "        \"totalAmount\": 6500,\n" +
            "        \"items\": [\n" +
            "            {  \n" +
            "                \"itemName\": \"普通病房床位费\",  \n" +
            "                \"amount\": \"6500\"  \n" +
            "                ,\"unit\": \"2天\"\n" +
            "              }\n" +
            "        ]\n" +
            "    }\n" +
            "    ,\n" +
            "    {\n" +
            "        \"id\": 7,\n" +
            "        \"feeName\": \"其他费用\",\n" +
            "        \"totalAmount\": 1600,\n" +
            "        \"items\": [\n" +
            "            {  \n" +
            "                \"itemName\": \"餐费\",  \n" +
            "                \"amount\": \"1600\"  \n" +
            "                ,\"unit\": \"2天\"\n" +
            "              }\n" +
            "        ]\n" +
            "    }\n" +
            "]\n";
}
