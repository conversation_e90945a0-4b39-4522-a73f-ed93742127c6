package cn.taihealth.ih.rest.controller.management;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.service.api.ChannelService;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.vm.hospital.ChannelKeyVM;
import cn.taihealth.ih.service.vm.hospital.ChannelVM;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/admin")
@Api(tags = "m端-互联网医院渠道管理", description = "admin")
@AllArgsConstructor
public class SystemHospitalChannelResource {

    private final ChannelService channelService;

    @GetMapping("/channels")
    @ApiOperation("获取可用渠道")
    public List<ChannelKeyVM> getChannels() {
        return channelService.getChannels();
    }

    @GetMapping("/hospital/channel")
    @ApiOperation("获取医院药品渠道")
    public ChannelVM getHospitalChannel() {
        Hospital hospital = CurrentHospital.getOrThrow();
        return channelService.getDrugChannel(hospital);
    }

    @DeleteMapping("/hospital/channel")
    @ApiOperation("删除医院药品渠道")
    public void deleteHospitalChannel() {
        Hospital hospital = CurrentHospital.getOrThrow();
        channelService.deleteDrugChannel(hospital);
    }

    @PutMapping("/hospital/channel")
    @ApiOperation("修改医院药品渠道")
    public ChannelVM saveHospitalChannel(@RequestBody ChannelVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return channelService.saveDrugChannel(hospital, vm);
    }

}

