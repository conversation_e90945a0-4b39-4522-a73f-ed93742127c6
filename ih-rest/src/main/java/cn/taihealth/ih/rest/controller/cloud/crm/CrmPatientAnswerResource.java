package cn.taihealth.ih.rest.controller.cloud.crm;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.crm.*;
import cn.taihealth.ih.domain.enums.CrmRecoveryStatusEnum;
import cn.taihealth.ih.repo.crm.*;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.crm.CrmQuestionnaireService;
import cn.taihealth.ih.service.api.crm.CrmService;
import cn.taihealth.ih.service.api.crm.CrmTaskService;
import cn.taihealth.ih.service.dto.crm.CrmQuestionnaireDTO;
import cn.taihealth.ih.service.impl.filter.crm.taskdetailresult.CrmTaskDetailResultSearch;
import cn.taihealth.ih.service.vm.crm.CrmPatientAnswerVM;
import cn.taihealth.ih.service.vm.crm.CrmQuestionVM;
import cn.taihealth.ih.service.vm.crm.CrmQuestionnaireVM;
import cn.taihealth.ih.service.vm.crm.CrmTaskResultVM;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@RestController
@RequestMapping(value = {"/user/questionnaires"})
@Api(tags = "就诊人 - 问卷", description = "就诊人 - 问卷")
@AllArgsConstructor
public class CrmPatientAnswerResource {

    private final CrmQuestionnaireRepository crmQuestionnaireRepository;
    private final CrmTaskService crmTaskService;
    private final CrmQuestionnaireQuestionRepository crmQuestionnaireQuestionRepository;
    private final CrmQuestionnaireService crmQuestionnaireService;
    private final CrmQuestionnairePutRepository crmQuestionnairePutRepository;
    private final CrmQuestionnairePushRepository crmQuestionnairePushRepository;
    private final CrmTaskDetailResultRepository crmTaskDetailResultRepository;
    private final CrmService crmService;

    private final CrmQuestionnaireChannelRepository crmQuestionnaireChannelRepository;


    // 奇怪的需求，重复答题后，需要覆盖前一次答案，会造成createDate不变，updateDate更新
    @PostMapping("/{questionnaireId}")
    @ApiOperation("用户提交问卷")
    @ActionLog(action = "用户提交问卷")
    public void commitQuestions(@ApiParam("问卷id") @PathVariable Long questionnaireId,
                                @RequestBody @Valid CrmPatientAnswerVM answerVM) {
        Hospital hospital = CurrentHospital.getOrNull();
        User user = CurrentUser.getOrNull();

        CrmQuestionnaire crmQuestionnaire = crmQuestionnaireRepository.findById(questionnaireId).orElse(null);
        if (crmQuestionnaire == null || !crmQuestionnaire.isEnabled()) {
            throw ErrorType.QUESTIONNAIRE_IS_OUT_TIME.toProblem("问卷已过期");
        }
        CrmQuestionnairePut put = crmQuestionnairePutRepository.findOneByHospitalAndCrmQuestionnaire(hospital, crmQuestionnaire).orElse(null);
        if (put == null || !put.isPut() || put.getRecoveryStatus() != CrmRecoveryStatusEnum.RECOVERY) {
            // 问卷未投放
            CrmQuestionnairePush push = crmQuestionnairePushRepository.findOneByHospitalAndCrmQuestionnaire(hospital, crmQuestionnaire).orElse(null);
            if (push == null) {
                throw ErrorType.QUESTIONNAIRE_IS_OUT_TIME.toProblem("问卷已过期");
            }
        }
        crmTaskService.saveAnswers(hospital, user, questionnaireId, answerVM);
    }

    @GetMapping("/{questionnaireId}")
    @ApiOperation("用户获取问卷信息")
    @ActionLog(action = "用户获取问卷信息")
    public CrmQuestionnaireDTO getQuestionnaire(@ApiParam("问卷id")@PathVariable Long questionnaireId) {
        Hospital hospital = CurrentHospital.getOrNull();
        CrmQuestionnaire crmQuestionnaire = crmQuestionnaireRepository.findById(questionnaireId).orElse(null);
        if (crmQuestionnaire == null || !crmQuestionnaire.isEnabled()) {
            throw ErrorType.QUESTIONNAIRE_IS_OUT_TIME.toProblem("问卷已过期");
        }
        CrmQuestionnairePut put = crmQuestionnairePutRepository.findOneByHospitalAndCrmQuestionnaire(hospital, crmQuestionnaire).orElse(null);
        if (put == null || !put.isPut() || put.getRecoveryStatus() != CrmRecoveryStatusEnum.RECOVERY) {
            // 问卷未投放
            CrmQuestionnairePush push = crmQuestionnairePushRepository.findOneByHospitalAndCrmQuestionnaire(hospital, crmQuestionnaire).orElse(null);
            if (push == null) {
                throw ErrorType.QUESTIONNAIRE_IS_OUT_TIME.toProblem("问卷已过期");
            }
        }
        List<CrmQuestionnaireQuestion> qqList = crmQuestionnaireQuestionRepository.findAllByQuestionnaireAndDeletedIsFalse(crmQuestionnaire)
                .stream().sorted(Comparator.comparing(CrmQuestionnaireQuestion::getQuestionOrder)).collect(Collectors.toList());
        List<CrmQuestionVM> list = Lists.newArrayList();
        for (CrmQuestionnaireQuestion crmQuestionnaireQuestion : qqList) {
            CrmQuestion question = crmQuestionnaireQuestion.getQuestion();
            if (question.isDeleted()) {
                continue;
            }
            CrmQuestionVM questionVM = new CrmQuestionVM(crmQuestionnaireQuestion);
            questionVM.setMust(crmQuestionnaireQuestion.isMust());
            questionVM.setCqqId(crmQuestionnaireQuestion.getId());
            list.add(questionVM);
        }
        Map<Long, List<Long>> linkedShows = crmQuestionnaireService.getQuestionnaireLinkedShows(crmQuestionnaire);
        return new CrmQuestionnaireDTO(crmQuestionnaire, null, null, null, list, linkedShows);
    }

    @GetMapping()
    @ApiOperation("用户获取问卷列表")
    @ActionLog(action = "用户获取问卷列表")
    public Page<CrmQuestionnaireDTO> getQuestionnaireList(
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrNull();
        Pageable page = PageRequest.of(pageNo, size);
        return crmQuestionnaireService.userGetHospitalPutQuestionnaire(hospital, page);
    }

    @GetMapping("/answer/results")
    @ApiOperation("查询用户回答的问卷列表")
    @ActionLog(action = "查询用户回答的问卷列表")
    public Page<CrmTaskResultVM> getAnswerQuestions(
            @ApiParam("query=QUESTIONNAIRE_ID:1234567890")
            @RequestParam(value = "query", required = false) String query,
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrNull();
        User user = CurrentUser.getOrThrow();
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "resultTime", "id");
        List<Specification<CrmTaskDetailResult>> specs = Lists.newArrayList();
        Specification<CrmTaskDetailResult> spec = CrmTaskDetailResultSearch.of(query).toSpecification();
        specs.add(spec);
        specs.add(Specifications.eq("answerer", user));
        if (hospital == null) {
            specs.add(Specifications.isNull("hospital"));
        } else {
            specs.add(Specifications.eq("hospital", hospital));
        }

        return crmTaskDetailResultRepository.findAll(Specifications.and(specs), page).map(CrmTaskResultVM::new);
    }

    @GetMapping("/answer/results/{resultId}/answer")
    @ApiOperation("查询回收问卷详细信息")
    @ActionLog(action = "查询回收问卷详细信息")
    public CrmQuestionnaireVM getAnswerQuestionById(@ApiParam("/crm/questionnaires/answer/results返回的id") @PathVariable Long resultId) {
        CrmTaskDetailResult crmTaskDetail = crmTaskDetailResultRepository.findById(resultId).orElseThrow();
        User user = CurrentUser.getOrThrow();
        if (!Objects.equals(user, crmTaskDetail.getAnswerer())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return crmService.getAnswerQuestion(crmTaskDetail);
    }

    @GetMapping("/channel/{channelId}")
    @ApiOperation("根据线下扫码渠道查询对应的问卷")
    @ActionLog(action = "根据线下扫码渠道查询对应的问卷")
    public CrmQuestionnaireDTO getPushQuestionnaireByChannel(@PathVariable long channelId) {
        return getQuestionnaire(crmQuestionnaireChannelRepository.getById(channelId).getQuestionnaire().getId());
    }

}
