package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.ExportTaskService;
import cn.taihealth.ih.service.dto.MedicalAppointmentOrderDTO;
import cn.taihealth.ih.service.dto.export.MedicalSkillAppointmentExportDTO;
import cn.taihealth.ih.service.impl.filter.offline.order.OfflineOrderSearch;
import cn.taihealth.ih.service.vm.UploadVM;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: jzs
 * @Date: 2023-10-09
 */
@RestController
@RequestMapping(value = {"/hospital/medical_appointment_orders"})
@Api(tags = "d端-医技预约管理", description = "d端-医技预约管理")
public class HospitalMedicalAppointmentOrderResource {

    private final OfflineOrderRepository offlineOrderRepository;
    private final ExportTaskService exportTaskService;


    public HospitalMedicalAppointmentOrderResource(OfflineOrderRepository offlineOrderRepository,
                                                   ExportTaskService exportTaskService) {
        this.offlineOrderRepository = offlineOrderRepository;
        this.exportTaskService = exportTaskService;
    }

    @GetMapping()
    @ApiOperation("查询医院所有的医技预约订单")
    @ActionLog(action = "查询医院所有的医技预约订单")
    public Page<MedicalAppointmentOrderDTO> findOfflineOrderListByHospital(
            @ApiParam(value = "查询页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNum,
            @ApiParam(value = "每页条数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer pageSize,
            @ApiParam("查询条件: DEPT_NAME:科室名字 ID:订单编号 PATIENT_NAME:患者姓名 MOBILE:患者手机号 STATUS:订单状态枚举" +
                    "下单时间CREATE_TIME:<=2023-01-01 10:00:00 CREATE_TIME:>=2023-01-01 10:00:00" +
                    "预约时间APPOINTMENT_TIME:<=2023-01-01 10:00:00 APPOINTMENT_TIME:>=2023-01-01 10:00:00")
            @RequestParam(required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.eq("type", ProjectTypeEnum.MEDICAL_APPOINTMENT));
        specs.add(OfflineOrderSearch.of(query).toSpecification());
        Pageable page = PageRequest.of(pageNum, pageSize, Sort.Direction.DESC, "createdDate");
        return offlineOrderRepository.findAll(Specifications.and(specs), page).map(MedicalAppointmentOrderDTO::new);
    }

    @GetMapping("/export")
    @ApiOperation("导出医院所有的医技预约订单")
    @ActionLog(action = "导出医院所有的医技预约订单")
    public UploadVM exportOfflineOrderListByHospital(
            @ApiParam("查询条件: DEPT_NAME:科室名字 ID:订单编号 PATIENT_NAME:患者姓名 MOBILE:患者手机号 STATUS:订单状态枚举" +
                    "下单时间CREATE_TIME:<=2023-01-01 10:00:00 CREATE_TIME:>=2023-01-01 10:00:00" +
                    "预约时间APPOINTMENT_TIME:<=2023-01-01 10:00:00 APPOINTMENT_TIME:>=2023-01-01 10:00:00")
            @RequestParam(required = false) String query, HttpServletResponse response) {
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<OfflineOrder>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.orderBy(Sort.Direction.DESC, "createdDate"));
        specs.add(Specifications.eq("type", ProjectTypeEnum.MEDICAL_APPOINTMENT));
        specs.add(OfflineOrderSearch.of(query).toSpecification());
        List<MedicalSkillAppointmentExportDTO> data = offlineOrderRepository.findAll(Specifications.and(specs))
                .stream().map(MedicalSkillAppointmentExportDTO::new).collect(Collectors.toList());
        return exportTaskService.exportMedicalSkillAppointment(data, response);
    }
}
