package cn.taihealth.ih.rest.controller.api;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.rest.controller.cloud.UserConstantResource.SystemConstants;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.cache.HospitalCache;
import cn.taihealth.ih.service.dto.drugorder.OrderInfoSmsDTO;
import cn.taihealth.ih.service.dto.express.ExpressData;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.PreChargeResult;
import cn.taihealth.ih.wechat.service.vm.RefundOrderVM;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * @Author: Moon
 * @Date: 2021/6/24 下午5:47
 */
@Slf4j
@RestController
@Api(tags = "外部系统使用特定账户(System角色用户mallAdmin)调用的api")
@AllArgsConstructor
public class ApiResource {

    private final HospitalRepository hospitalRepository;
    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final ExpressSenderInfoService expressSenderInfoService;
    private final HospitalPublicPlatformService hospitalPublicPlatformService;

//    @PostMapping("/api/token/{username}")
//    @ApiOperation("获取用户token-供外部系统Server调用")
//    public ResponseEntity<JWTAccessToken> getUserToken(@PathVariable String username) {
//        username = SM4Utils.urlDecoderCBC(username);
//        String finalUsername = username;
//        User user = userCacheFindService.findOneByUsername(username)
//            .orElseThrow(() -> new UsernameNotFoundException("Username is not found: " + finalUsername));
//        JWTAccessToken jwtAccessToken = getJwtAccessToken(user);
//        return ResponseEntity.ok(jwtAccessToken);
//    }
//
//    @PostMapping("/api/users")
//    @ApiOperation("获取token，用户不存在时创建用户-供外部系统Server调用（创建用户01）")
//    public ResponseEntity<JWTAccessToken> createUser(@Valid @RequestBody CreateUserVM vm) {
//        // 拿到医院信息
//        HospitalRepository hospitalRepository = AppContext.getInstance(HospitalRepository.class);
//        Hospital hospital = hospitalRepository.findOneByCode(vm.getHospital())
//            .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("医院不存在"));
//        User user = userService.checkOrCreateUser(vm.getNickName(), vm.getMobile(), vm.getIdNo(), vm.getSource());
//        // 创建就诊人
//        patientService.getOrAddDefaultPatientAndCheckIdentity(user, hospital);
//        JWTAccessToken jwtAccessToken = getJwtAccessToken(user);
//        return ResponseEntity.ok(jwtAccessToken);
//    }

//    private JWTAccessToken getJwtAccessToken(User user) {
//        AuthenticatedUserToken authedToken = new AuthenticatedUserToken(user.getUsername(),
//            user.getPassword(), user.getUserRoles().stream()
//            .map(a -> new SimpleGrantedAuthority("ROLE_" + a.getCode())).collect(Collectors.toSet()));
//
//        Authentication authentication = authenticationManager.authenticate(authedToken);
//        String accessToken = jwtAuthenticationProvider
//            .createAccessToken(authentication, validityInSeconds);
//        JWTAccessToken token = new JWTAccessToken(accessToken, validityInSeconds);
//        token.setUsername(user.getUsername());
//        return token;
//    }

//    @GetMapping("/api/open_page")
//    @ApiOperation("自动登录-用于鹊哥小程序图文问诊重定向跳转")
//    public ResponseEntity<?> openPage(@RequestParam(required = false) String name,
//                                      @RequestParam(required = false) String hospitalCode,
//                                      @RequestParam String mobile,
//                                      @RequestParam String appCode,
//                                      @RequestParam(required = false) String idNo,
//                                      @RequestParam String redirectUrl) {
//        if (sourceAppRepository.findOneByCode(appCode).isEmpty()) {
//            throw new RuntimeException("appCode不合法");
//        }
//        log.info("appCode:{}", appCode);
//        User user = userService.checkOrCreateUser(name, mobile, idNo, appCode);
//        log.info("检查或创建用户成功");
//        // 创建就诊人(不需要创建默认就诊人,只需要有就诊人就可以)
//        patientService.getOrAddPatient(user,
//                                       AppContext.getInstance(HospitalRepository.class).findOneByCode(hospitalCode)
//                                           .orElse(null));
//        log.info("检查或创建就诊人成功");
//        Map<String, String> params = new HashMap<>();
//        params.put("source", appCode);
//        JWTAccessToken token = getJwtAccessToken(user);
//        params.put("auth-token", token.getIdToken());
//        log.info("获取JWTAccessToken成功:JWTAccessToken:{}-{}",token,token.getIdToken());
//        return ResponseEntity.status(HttpStatus.TEMPORARY_REDIRECT)
//            .header(HttpHeaders.LOCATION, HttpUtil.addParams(redirectUrl, params)).build();
//
//    }

//    @GetMapping("/api/medical_workers")
//    @ApiOperation("查询某医院所有的医护人员信息(职业信息，基本信息)")
//    public IhPage<MedicalWorkerDTO> findAllByHospital(
//        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
//        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
//        @ApiParam("查询enabled不传表示查询全部 比如说：deptId:123 name:张三 enabled:true role:roleId")
//        @RequestParam(name = "query", required = false) String query) {
//
//        Hospital hospital = CurrentHospital.getOrThrow();
//        Specification<MedicalWorker> specification =
//            MedicalWorkersSearch.of(query).toSpecification();
//        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "createdDate");
//        specification = Specifications.and(specification,
//            Specifications.eq("hospital", hospital));
//        return new IhPage<>(medicalWorkerRepository.findAll(specification, page)
//            .map(me -> {
//                MedicalWorkerDTO m = new MedicalWorkerDTO(me, false);
//                m.setDepts(me.getDeptMedicalWorkers()
//                    .stream()
//                    .map(u -> new DeptVM(u.getDept()))
//                    .collect(Collectors.toList()));
//                return m;
//            }));
//    }
//
//    @GetMapping("/api/doctors/ids")
//    @ApiOperation("根据医生id数组查询医生列表-用于商城后台鹊哥护家套餐医生绑定")
//    public List<MedicalWorkerDTO> getDoctors(@RequestParam("ids") List<String> ids) {
//        if(ids == null || ids.isEmpty()) {
//            throw ErrorType.MEDICAL_QUERY_NOT_BANK.toProblem("查询条件不能为空");
//        }
//        List<Long> idsLong;
//        try {
//            idsLong = ids.stream().map(Long::valueOf).collect(Collectors.toList());
//        } catch (ClassCastException e) {
//            throw ErrorType.ILLEGAL_PARAMS.toProblem();
//        }
//        return medicalWorkerRepository.findAllById(idsLong).stream().map(MedicalWorkerDTO::new)
//            .collect(Collectors.toList());
//    }

    @GetMapping("/api/hospital/express/settings")
    @ApiOperation("查询互联网医院快递设置")
    public ExpressData getExpressSettings(@RequestParam ExpressScene scene) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return expressSenderInfoService.getExpressSettings(hospital, scene);
    }

    @GetMapping("/api/hospital/{code}/drug_order/settings")
    @ApiOperation("查询互联网医院药品订单设置")
    public SystemConstants getDrugOrderSetting(@PathVariable("code") String code) {
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(code)
            .orElseThrow(() -> ErrorType.BAD_REQUEST_ERROR.toProblem("医院不存在"));
        return new SystemConstants(hospital);
    }

    @PostMapping("/api/drug_order/sendSms")
    @ApiOperation("根据药品订单状态发送短信通知患者")
    public void sendSMSToPatient(@RequestBody @Valid OrderInfoSmsDTO order) {
        NoticeService noticeService = AppContext.getInstance(NoticeService.class);
        PatientRepository patientRepository = AppContext.getInstance(PatientRepository.class);
        Hospital hospital = AppContext.getInstance(HospitalCache.class).getHospital(order.getHospitalCode())
                .orElseThrow(ErrorType.NO_HOSPITAL_ERROR::toProblem);
        Patient patient = patientRepository.getById(order.getPatientId());
        log.info("=====药品订单状态: {}", order.getOrderStatus());
        HospitalSettingKey hospitalSettingKey = null;
        DrugOrderStatus orderStatus = order.getOrderStatus();
        Map<String, String> details = Maps.newHashMap();
        // 判断患者在小程序端是否打开了问诊开关
        if (noticeService.shouldSendUser(hospital, patient.getUser(), UserSettingKey.USER_CONSULT_NOTIFY)) {
            switch (orderStatus) {
                // 药品服务那里没有 WAIT_PAY 这个类型的调用
//                case WAIT_PAY: // 药品订单待支付
//                    hospitalSettingKey = HospitalSettingKey.NOTIFY_DRUG_ORDER_PENDING;
//                    break;
                case CLOSE: // 药品订单超时未支付
                    hospitalSettingKey = HospitalSettingKey.NOTIFY_DRUG_ORDER_CLOSED;
                    break;
                case CANCELLED: // 订单未支付患者主动取消
                    hospitalSettingKey = HospitalSettingKey.NOTIFY_ORDER_CANCELED;
                    break;
                case DELIVER: // 订单发货
                    hospitalSettingKey = HospitalSettingKey.NOTIFY_ORDER_DELIVERED;
                    break;
                case DELIVER_SIGN: // 订单收货
                    hospitalSettingKey = HospitalSettingKey.NOTIFY_ORDER_RECEIVED;
                    break;
                default:
                    break;
            }
            if (hospitalSettingKey != null) {
                details.put("patient", patient.getName());
                details.put("hospital", patient.getHospital().getName());
                details.put("dept", "-");
                AppContext.getInstance(NoticeService.class).notice(patient, order, details, hospitalSettingKey);
            }
        }
    }

    @GetMapping("/api/drug_order/preCharge")
    @ApiOperation("药品订单预算价格")
    public PreChargeResult getDurgPrice(@RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
                                        @RequestParam(name = "outPrescriptionId") String outPrescriptionId,
                                        @RequestParam(name = "hisRecipeNo") String hisRecipeNo) {
        PrescriptionOrder order = prescriptionOrderRepository.getById(Long.valueOf(outPrescriptionId));
        order.setHisRecipeNo(hisRecipeNo);
        PlatformTypeEnum platformType = hospitalPublicPlatformService.getByAppId(appId)
                .map(HospitalPublicPlatform::getPlatformType).orElse(null);
        // 调用his收费预算接口获取药品价格
        return BusinessServiceStrategy.getInstance().getStrategy(HospitalSettingsHelper.getBoolean(order.getHospital(), HospitalSettingKey.HIS_SYNC_SCHEDULE))
            .preCharge(order.getHospital(), order, platformType);
    }

    @PostMapping("/api/drug_order/preCharge/cancel")
    @ApiOperation("药品订单预算取消")
    public void drugOrderPreChargeCancelled(@RequestBody @Valid OrderInfoSmsDTO order) {
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(order.getId());
        BusinessServiceStrategy.getInstance().getStrategy(HospitalSettingsHelper.getBoolean(prescriptionOrder.getHospital(), HospitalSettingKey.HIS_SYNC_SCHEDULE))
            .cancelPreCharge(prescriptionOrder.getHospital(), prescriptionOrder);
    }

    @PostMapping("/api/prescription_order/refund")
    @ApiOperation(value = "订单申请退款-用于药品服务药品订单退款")
    public void refund(@RequestBody @Valid RefundOrderVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        AppContext.getInstance(PrescriptionService.class).refundPrescriptionOrder(hospital, vm, true, OrderRefundSource.IH);
    }

}
