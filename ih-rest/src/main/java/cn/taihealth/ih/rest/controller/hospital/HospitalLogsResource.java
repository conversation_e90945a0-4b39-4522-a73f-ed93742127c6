package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.LogService;
import cn.taihealth.ih.service.dto.SystemLogDTO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for view and managing Log Level at runtime.
 */
@RestController
@RequestMapping(value = { "/hospital" })
@Api(tags = "日志操作", description = "医院管理员权限")
public class HospitalLogsResource {

    private final LogService logService;

    public HospitalLogsResource(LogService logService) {
        this.logService = logService;
    }

    @Timed
    @GetMapping("/logs")
    @ApiOperation(value = "获取全部操作日志")
//    @ActionLog(action = "获取全部操作日志")
    public Page<SystemLogDTO> getList(@RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                        @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
                                        @RequestParam(name = "lastPageId", required = false) Long lastPageId,
                                        @RequestParam(name = "action", required = false, defaultValue = "next") String action) {
        Hospital hospital = CurrentHospital.getOrNull();
        return logService.getLogs(hospital, pageNo, size, lastPageId, action);
    }

}

