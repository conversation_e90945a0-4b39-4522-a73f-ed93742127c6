package cn.taihealth.ih.rest.controller.hospital.nursing;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.ClinicType;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.OrderOperation;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.domain.nursing.OrderNursingNurse;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.nursing.OrderNursingExtRepository;
import cn.taihealth.ih.repo.order.OrderOperationRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.service.api.nursing.NursingOrderService;
import cn.taihealth.ih.service.vm.EndOrderVM;
import cn.taihealth.ih.service.vm.OrderOperationVM;
import cn.taihealth.ih.service.vm.OrderVM;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.CopyAppointmentBillStatsVM;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.CopyAppointmentBillVM;
import cn.taihealth.ih.service.vm.nursing.*;
import cn.taihealth.ih.service.vm.order.CompleteOrderVM;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = {"/hospital/nursing_orders"})
@Api(tags = "护理订单", description = "护理订单")
@AllArgsConstructor
public class NursingOrderResource {

    private final OrderNursingExtRepository orderNursingExtRepository;
    private final OrderRepository orderRepository;
    private final NursingOrderService nursingOrderService;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final OrderOperationRepository orderOperationRepository;

    @GetMapping()
    @ApiOperation("查询医院所有的护理订单")
    @ActionLog(action = "查询医院所有的护理订单")
    public Page<OrderVM> findAllNursingOrderList(
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
            @ApiParam("带\"\"值的查询条件表示支持多值查询 orderStatusContains:\"PENDING COMPLETED REFUNDED\""
                    + "patientName:xxx 订单编号(orderId):123(精确搜索) mobile:123 (护士id)nurseId:123"
                    + "(项目分类id)itemClassifyId:123 (护理到家项目Id)homeItemId:123"
                    + "下单开始日期(create_time:>=2022-11-17)、下单结束日期(create_time:<=2022-11-17)"
                    + "预约开始日期(appointment_start_time:2022-11-17)、预约结束日期(appointment_end_time:2022-11-17)"
                    + "结算开始日期(settle_time:>=2022-11-17)、结算结束日期(settle_time:<=2022-11-17)"
                    + "服务视频上传情况(uploadFlag):true 仅报资损订单(LossFlag):true")
            @RequestParam(value = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable pageable = PageRequest.of(pageNo, size, Sort.Direction.DESC, "id");
        return nursingOrderService.getNursingHomeOrderList(hospital, null, null, query, pageable);
    }

    @GetMapping("/bills")
    @ApiOperation("查询护理到家对账单")
    @ActionLog(action = "查询护理到家对账单")
    public Page<CopyAppointmentBillVM> getBills(
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("query=copyAppointmentId:123 patientName:张三 copyIdCard:1234(加密) startDate:2024-02-02 " +
                    "endDate:2024-02-02 orderNo:4321(第三方支付流水号) NursingHomeFlag:true")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        PageRequest pageRequest = PageRequest.of(pageNo, size, Sort.Direction.DESC, "orderOperateTime");
        return nursingOrderService.getBills(hospital, query, pageRequest);
    }

    @GetMapping("/bills/stats")
    @ApiOperation("查询护理到家对账单统计")
    @ActionLog(action = "查询护理到家对账单统计")
    public CopyAppointmentBillStatsVM getBillsStats(
            @ApiParam("query=productId:123 patientName:张三 copyIdCard:1234 startDate:2024-02-02 endDate:2024-02-02 NursingHomeFlag:true")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return nursingOrderService.getBillStats(hospital, query);
    }

    @GetMapping("/bills/export")
    @ApiOperation("查询护理到家对账单统计")
    @ActionLog(action = "查询护理到家对账单统计")
    public UploadVM exportCopyAppointmentBills(
            @ApiParam("query=productId:123 patientName:张三 copyIdCard:1234 startDate:2024-02-02 endDate:2024-02-02 NursingHomeFlag:true")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return new UploadVM(nursingOrderService.exportNursingHomeBills(hospital, query));
    }

    @GetMapping("/my")
    @ApiOperation("医务人员查询自己负责的护理订单(咨询/上门)")
    @ActionLog(action = "医务人员查询自己负责的护理订单")
    public Page<OrderVM> findMyNursingOrderList(
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
            @ApiParam("带\"\"值的查询条件表示支持多值查询 orderStatusContains:\"PENDING COMPLETED REFUNDED\""
                    + "patientName:xxx OrderType:NURSING_HOME(护理到家) NURSING_CONSULT(护理咨询)"
                    + "homeItemId(到家项目):123 consulItemId(咨询项目):123")
            @RequestParam(value = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        MedicalWorker nurse = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        Pageable pageable = PageRequest.of(pageNo, size, Sort.Direction.DESC, "id");
        return nursingOrderService.getNursingHomeOrderList(hospital, null, nurse, query, pageable);
    }

    @GetMapping("/{orderId}")
    @ApiOperation("护理订单详情")
    @ActionLog(action = "护理订单详情")
    public OrderVM detail(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        OrderNursingExt ext = orderNursingExtRepository.findOneByBaseOrder(order).orElseThrow();
        OrderVM orderVM = new OrderVM(order);
        orderVM.setOrderNursingExt(new OrderNursingExtDTO(ext));
        return orderVM;
    }

    @GetMapping("/{orderId}/operations")
    @ApiOperation("护理订单操作日志")
    @ActionLog(action = "护理订单操作日志")
    public List<OrderOperationVM> findAllOrderOperationList(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        List<OrderOperation> orderOperationList = orderOperationRepository.findAllByOrder(order);
        return orderOperationList.stream().map(OrderOperationVM::new).collect(Collectors.toList());
    }

    @PostMapping("/{orderId}/assign")
    @ApiOperation("护理上门派单")
    @ActionLog(action = "护理上门派单")
    public void assign(@PathVariable long orderId,
                                     @Valid @RequestBody AssignNursingVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        List<MedicalWorker> nurses = Lists.newArrayList();
        for (Long nurseId : vm.getNurseIds()) {
            MedicalWorker nurse = medicalWorkerRepository.getById(nurseId);
            if (!Objects.equals(hospital, nurse.getHospital())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
            nurses.add(nurse);
        }
        MedicalWorker my = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        nursingOrderService.assign(order, my, nurses);
    }

    @PostMapping("/{orderId}/reject")
    @ApiOperation("审核不通过，全额退款")
    @ActionLog(action = "护理上门审核不通过，全额退款")
    public void reject(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        MedicalWorker my = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        List<MedicalWorker> nurses = order.getNurses().stream().map(OrderNursingNurse::getNurse).collect(Collectors.toList());
        if (!AppContext.getInstance(UserService.class).isHeadNurse(my.getUser(), order.getHospital())
                && nurses.stream().noneMatch(u -> Objects.equals(u, my))) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        nursingOrderService.reject(order, my);
    }

    @PostMapping("/{orderId}/start")
    @ApiOperation("护理咨询接诊")
    @ActionLog(action = "护理咨询接诊")
    public void start(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(order.getDoctor().getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (order.getStatus() != Order.OrderStatus.ONTIME_CONFIRMED) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确");
        }
        MedicalWorker my = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        nursingOrderService.startConsult(order, my);
    }

    @PutMapping("/{orderId}/complete_consultation")
    @ApiOperation("护理咨询结束会诊")
    @ActionLog(action = "护理咨询结束会诊")
    public void completeConsultation(@PathVariable(name = "orderId") long orderId,
                                        @RequestBody CompleteOrderVM completeOrderVM) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(order.getDoctor().getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(order.getStatus(), Order.OrderStatus.STARTED)) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确");
        }
        if (!Objects.equals(order.getOrderType(), ClinicType.NURSING_CONSULT)) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单类型不正确");
        }
        completeOrderVM.setReason("护士会诊结束操作");
        nursingOrderService.completeConsultation(order, user, completeOrderVM);
    }

    @GetMapping("/home_count")
    @ApiOperation("全部的护理上门数量（护士长）")
    @ActionLog(action = "查询全部的护理上门数量（护士长）")
    public NursingOrderCountVM getHomeCount() {
        Hospital hospital = CurrentHospital.getOrThrow();
        return nursingOrderService.getHomeCount(hospital, null);
    }

    @GetMapping("/home_count/my")
    @ApiOperation("自己的护理上门数量（护士）")
    @ActionLog(action = "查询自己的护理上门数量（护士）")
    public NursingOrderCountVM getMyHomeCount() {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        MedicalWorker medicalWorker = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user)
                .orElseThrow();
        return nursingOrderService.getHomeCount(hospital, medicalWorker);
    }

    @PostMapping("/{orderId}/start_home")
    @ApiOperation("医务人员开始上门")
    @ActionLog(action = "医务人员开始上门")
    public OrderVM startNursingOrder(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        MedicalWorker nurse = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        return nursingOrderService.startNursingHome(hospital, nurse, order);
    }

    @PostMapping("/{orderId}/end_home")
    @ApiOperation("医务人员结束护理到家服务")
    @ActionLog(action = "医务人员结束护理到家服务")
    public OrderVM endNursingOrder(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        MedicalWorker nurse = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        return nursingOrderService.endNursingHome(hospital, nurse, order);
    }


    @PutMapping("/{orderId}/info")
    @ApiOperation("医务人员修改护理订单上门时间/护理备注/预约时间")
    @ActionLog(action = "医务人员修改护理订单上门时间/护理备注/预约时间")
    public void editNursingHomeSimple(@PathVariable long orderId,
                                      @RequestBody NursingHomeSimpleVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        MedicalWorker nurse = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        nursingOrderService.editNursingHomeSimple(nurse, order, vm);
    }

    @PostMapping("/{orderId}/confirm_home")
    @ApiOperation("医务人员结算护理到家")
    @ActionLog(action = "医务人员结算护理到家")
    public OrderVM confirmNursingOrder(@PathVariable long orderId,
                                       @RequestBody NursingOrderCalculateDTO nursingOrderCalculateDTO) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        MedicalWorker nurse = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        return nursingOrderService.confirmNursingHome(hospital, nurse, order, nursingOrderCalculateDTO);
    }

    @PutMapping("/{orderId}/confirm_home/cancel")
    @ApiOperation("医务人员撤销结算")
    @ActionLog(action = "医务人员撤销结算")
    public OrderVM cancelConfirmNursingOrder(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(user, order.getDoctor().getUser())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        MedicalWorker nurse = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        return nursingOrderService.cancelConfirmNursingHome(nurse, order);
    }

    @PutMapping("/{orderId}/service_video")
    @ApiOperation("医务人员添加服务视频地址")
    @ActionLog(action = "医务人员添加服务视频地址")
    public void editNursingHomeServiceVideo(@PathVariable long orderId, @RequestBody NursingHomeSimpleVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        nursingOrderService.editNursingHomeServiceVideo(order, vm);
    }

    @PostMapping("/{orderId}/report_loss")
    @ApiOperation("医务人员对护理订单进行报损")
    @ActionLog(action = "医务人员对护理订单进行报损")
    public OrderVM reportLossNursing(@PathVariable long orderId, @RequestBody NursingReportLossVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        MedicalWorker nurse = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElseThrow();
        return nursingOrderService.reportLossNursing(order, nurse, vm);
    }

    @PutMapping("/{orderId}/end")
    @ApiOperation("护士退诊护理咨询")
    @ActionLog(action = "护士退诊护理咨询")
    public void stopOrder(@PathVariable("orderId") Long id, @Valid @RequestBody EndOrderVM vm) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(id);
        Hospital hospital = CurrentHospital.getOrThrow();
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (StringUtils.isEmpty(vm.getReason())) {
            vm.setReason("护士[" + user.getFullName() + "]退诊");
        }
        MedicalWorker medicalWorker = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElse(null);
        nursingOrderService.endOrder(user, medicalWorker, order, vm);
    }
}
