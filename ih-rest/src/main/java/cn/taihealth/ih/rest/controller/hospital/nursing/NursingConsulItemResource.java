package cn.taihealth.ih.rest.controller.hospital.nursing;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.nursing.NursingConsulItem;
import cn.taihealth.ih.repo.nursing.NursingConsulItemRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.nursing.NursingConsulItemService;
import cn.taihealth.ih.service.dto.nursing.NursingConsulItemDTO;
import cn.taihealth.ih.service.impl.filter.nursing.consulItem.NursingConsulItemSearch;
import com.gitq.jedi.data.specification.Specifications;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping(value = {"/hospital/nursing/consul_items"})
@Api(tags = "护理咨询项目", description = "护理咨询项目")
@AllArgsConstructor
public class NursingConsulItemResource {


    private final NursingConsulItemRepository nursingConsulItemRepository;
    private final NursingConsulItemService nursingConsulItemService;

    @PostMapping()
    @ApiOperation("添加护理咨询项目")
    @ActionLog(action = "添加护理咨询项目")
    public NursingConsulItemDTO add(@Valid @RequestBody NursingConsulItemDTO dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return new NursingConsulItemDTO(nursingConsulItemService.add(hospital, dto));
    }

    @PutMapping("/{id}")
    @ApiOperation("修改护理咨询项目")
    @ActionLog(action = "修改护理咨询项目")
    public NursingConsulItemDTO edit(@ApiParam("项目分类id") @PathVariable(name = "id") Long id,
                                     @Valid @RequestBody NursingConsulItemDTO dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        NursingConsulItem item = nursingConsulItemRepository.getById(id);
        if (!Objects.equals(hospital, item.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        dto.setId(id);
        return new NursingConsulItemDTO(nursingConsulItemService.edit(dto));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除护理咨询项目")
    @ActionLog(action = "删除护理咨询项目")
    public void delete(@ApiParam("项目分类id") @PathVariable(name = "id") Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        NursingConsulItem item = nursingConsulItemRepository.getById(id);
        if (!Objects.equals(hospital, item.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        nursingConsulItemRepository.delete(item);
    }

    @GetMapping()
    @ApiOperation("护理咨询项目列表")
    @ActionLog(action = "护理咨询项目列表")
    public Page<NursingConsulItemDTO> findPage(
            @ApiParam("query=enabled:true")
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(name = "page", required = false) Integer pageNo,
            @RequestParam(name = "size", required = false) Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable pageable = PageRequest.of(pageNo, size, Sort.by(Sort.Direction.DESC, "orderNo"));
        List<Specification<NursingConsulItem>> sps = new ArrayList<>();
        sps.add(Specifications.eq("hospital", hospital));
        sps.add(NursingConsulItemSearch.of(query).toSpecification());
        Page<NursingConsulItem> all = nursingConsulItemRepository.findAll(Specifications.and(sps), pageable);
        return all.map(NursingConsulItemDTO::new);
    }

    @PatchMapping("/{id}/enabled")
    @ApiOperation("启用/禁用护理咨询项目")
    @ActionLog(action = "启用/禁用护理咨询项目")
    public NursingConsulItemDTO showOrHide(
            @ApiParam("项目分类id") @PathVariable(name = "id") Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        NursingConsulItem item = nursingConsulItemRepository.getById(id);
        if (!Objects.equals(hospital, item.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        item.setEnabled(!item.isEnabled());
        return new NursingConsulItemDTO(nursingConsulItemRepository.save(item));
    }
}
