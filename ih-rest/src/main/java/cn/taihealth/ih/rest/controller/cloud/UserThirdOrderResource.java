package cn.taihealth.ih.rest.controller.cloud;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.cloud.WechatOrder;
import cn.taihealth.ih.domain.cloud.alipay.AliPayOrder;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.enums.ThirdOrderType;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.repo.WechatOrderRepository;
import cn.taihealth.ih.repo.alipay.AliPayOrderRepository;
import cn.taihealth.ih.repo.hospital.HospitalPublicPlatformRepository;
import cn.taihealth.ih.service.vm.business.BusinessOrderPayMethodVM;
import cn.taihealth.ih.service.vm.business.BusinessOrderPayMethodVM.PayTypeEnum;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 */
@RestController
@RequestMapping("/user/third_order")
@Api(tags = "用户-第三方订单接口")
@AllArgsConstructor
@Slf4j
public class UserThirdOrderResource {

    private final AliPayOrderRepository aliPayOrderRepository;
    private final WechatOrderRepository wechatOrderRepository;
    private final HospitalPublicPlatformRepository hospitalPublicPlatformRepository;

    @GetMapping("/pay_method")
    @ApiOperation(value = "业务订单支付渠道查询，若该订单无未支付订单，则返回为空")
    @ActionLog(action = "业务订单支付渠道查询")
    public BusinessOrderPayMethodVM preCharge(
        @ApiParam("业务id 根据业务类型不同取值不同 注意")  @RequestParam(value = "id") String id,
        @ApiParam("业务类型，(REGISTER: 挂号 RECIPE: 处方 EXAM_ITEM: 检查检验项目 OUTPATIENT_FEE: 门诊缴费 BEFORE_INPATIENT_FEE: 住院缴费 \" +\n"
            + "            \"OUTPATIENT_REGISTER_FEE: 患服-预约挂号 OUTPATIENT_NUCLEIC_ACID_FEE: 核酸预约  \" +\n"
            + "            \"MEDICAL_RECORD_COPY_APPOINTMENT: 病案复印预约  MEDICAL_RECORD_COPY_APPOINTMENT_APPEND:病案复印预约补缴)")  @RequestParam(value = "type", required = true)  String typeName) {
        List<AliPayOrder> aliPayOrders = aliPayOrderRepository.findAllByOrderTypeAndBody(ThirdOrderType.valueOf(typeName), id);
        AliPayOrder aliPayOrder = aliPayOrders.stream().filter(AliPayOrder::getPayed).findFirst()
                .orElse(aliPayOrders.stream().max(Comparator.comparing(AliPayOrder::getCreatedDate)).orElse(null));
        if (aliPayOrder != null) {

            BusinessOrderPayMethodVM businessOrderPayMethodVM = new BusinessOrderPayMethodVM(PayTypeEnum.ALI_PAY,
                                                                                             BusinessOrderPayMethodVM.PlatformTypeEnum.MINI_PROGRAM,
                                                                                             aliPayOrder.getAppId());
            log.info("交易渠道查询: id:{} type:{} 渠道{}", id, typeName, StandardObjectMapper.stringify(businessOrderPayMethodVM));
            return businessOrderPayMethodVM;
        }
        List<WechatOrder> wechatOrders = wechatOrderRepository.findAllByWechatOrderTypeAndProductId(ThirdOrderType.valueOf(typeName), id);
        WechatOrder wechatOrder = wechatOrders.stream().filter(WechatOrder::getPayed).findFirst()
                .orElse(wechatOrders.stream().max(Comparator.comparing(WechatOrder::getCreatedDate)).orElse(null));
        if (wechatOrder != null) {
            Optional<HospitalPublicPlatform> optionalHospitalPublicPlatform = hospitalPublicPlatformRepository.findOneByAppId(
                wechatOrder.getAppId());
            if (optionalHospitalPublicPlatform.isPresent()) {
                PlatformTypeEnum platformType = optionalHospitalPublicPlatform.get().getPlatformType();
                switch (platformType) {
                    case MINI:
                        BusinessOrderPayMethodVM businessOrderPayMethodVM = new BusinessOrderPayMethodVM(
                            PayTypeEnum.WECHAT,
                            BusinessOrderPayMethodVM.PlatformTypeEnum.MINI_PROGRAM,
                            wechatOrder.getAppId());
                        log.info("交易渠道查询: id:{} type:{} 渠道{}", id, typeName, StandardObjectMapper.stringify(businessOrderPayMethodVM));
                        return businessOrderPayMethodVM;
                    case OFFICIAL_ACCOUNT:
                        BusinessOrderPayMethodVM payMethodVM = new BusinessOrderPayMethodVM(
                            PayTypeEnum.WECHAT,
                            BusinessOrderPayMethodVM.PlatformTypeEnum.OFFICIAL_ACCOUNT, wechatOrder.getAppId());
                        log.info("交易渠道查询: id:{} type:{} 渠道{}", id, typeName, StandardObjectMapper.stringify(payMethodVM));
                        return payMethodVM;
                    default:
                        return null;
                }
            }
        }
        return null;
    }


}
