package cn.taihealth.ih.rest.controller.cloud;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.QRUtils;
import cn.taihealth.ih.commons.util.RedisUtil;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.TIMGroupMember;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import cn.taihealth.ih.domain.nursing.OrderNursingExt;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.nursing.OrderNursingExtRepository;
import cn.taihealth.ih.repo.order.OrderExtraInfoRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.repo.order.OrderWorkerRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.dto.OrderEvaluateDTO.EvaluateFor;
import cn.taihealth.ih.service.impl.filter.order.hospitalorder.HospitalOrderSearch;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.*;
import cn.taihealth.ih.service.vm.nursing.OrderNursingExtDTO;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.*;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> moon
 */
@RestController
@RequestMapping(value = {"/user"})
@Api(tags = "当前用户的就诊", description = "当前用户的就诊")
public class UserOrderResource {

    private static final Logger log = LoggerFactory.getLogger(UserOrderResource.class);

    private final OrderService orderService;
    private final OrderRepository orderRepository;
    private final OrderQueueService orderQueueService;
    private final PrescriptionService prescriptionService;
    private final MedicalCaseRepository medicalCaseRepository;
    private final PrescriptionOrderRepository prescriptionOrderRepository;

    private final MedicalWorkerRepository medicalWorkerRepository;
    private final TIMMessageService timMessageService;
    private final TIMGroupMemberRepository timGroupMemberRepository;
    private final RedisUtil redisUtil;
    private final ElectronicMedicCardRepository electronicMedicCardRepository;
    private final OrderExtraInfoRepository orderExtraInfoRepository;
    private final OrderWorkerRepository orderWorkerRepository;
    private final HospitalPublicPlatformService hospitalPublicPlatformService;
    private final OrderNursingExtRepository orderNursingExtRepository;

    public UserOrderResource(OrderService orderService,
                             OrderRepository orderRepository,
                             OrderQueueService orderQueueService,
                             PrescriptionService prescriptionService,
                             PrescriptionOrderRepository prescriptionOrderRepository,
                             MedicalCaseRepository medicalCaseRepository,
                             MedicalWorkerRepository medicalWorkerRepository, TIMMessageService timMessageService,
                             TIMGroupMemberRepository timGroupMemberRepository, RedisUtil redisUtil,
                             ElectronicMedicCardRepository electronicMedicCardRepository,
                             OrderExtraInfoRepository orderExtraInfoRepository,
                             OrderWorkerRepository orderWorkerRepository,
                             OrderNursingExtRepository orderNursingExtRepository,
                             HospitalPublicPlatformService hospitalPublicPlatformService) {
        this.orderService = orderService;
        this.orderRepository = orderRepository;
        this.orderQueueService = orderQueueService;
        this.prescriptionService = prescriptionService;
        this.medicalCaseRepository = medicalCaseRepository;
        this.prescriptionOrderRepository = prescriptionOrderRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.timMessageService = timMessageService;
        this.timGroupMemberRepository = timGroupMemberRepository;
        this.redisUtil = redisUtil;
        this.electronicMedicCardRepository = electronicMedicCardRepository;
        this.orderWorkerRepository = orderWorkerRepository;
        this.orderExtraInfoRepository = orderExtraInfoRepository;
        this.hospitalPublicPlatformService = hospitalPublicPlatformService;
        this.orderNursingExtRepository = orderNursingExtRepository;
    }

    @PostMapping("/orders")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation("用户发起分诊请求/创建复诊咨询/预约订单")
    @ActionLog(action = "用户发起分诊请求")
    public OrderVM startOrder(@RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
                              @Valid @RequestBody CreateOrderVM vm) {

        User user = CurrentUser.getOrThrow();
        // 2023年07月15日13:34:49 就诊卡修改，订单中的就诊卡信息必填
        ElectronicMedicCardDTO electronicMedicCard = vm.getElectronicMedicCard();
        Patient patient = electronicMedicCardRepository.getById(electronicMedicCard.getId()).getPatient();
        if (!Objects.equals(user, patient.getUser())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        Hospital hospital = CurrentHospital.getOrThrow();
        HospitalPublicPlatform publicPlatform = hospitalPublicPlatformService.getByAppId(appId).orElse(null);
        if (publicPlatform != null) {
            vm.setPlatformType(publicPlatform.getPlatformType());
        }
        Order order = orderService.createOrder(hospital, user, patient, vm);
        return new OrderVM(order);
    }

    @PutMapping("/orders/{orderId}/end")
    @ApiOperation("用户取消订单请求")
    @ActionLog(action = "用户取消订单请求")
    public void stopOrder(@PathVariable("orderId") Long id, @Valid @RequestBody EndOrderVM vm) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(id);
        if (!(order.getOrderType() == ClinicType.OUT || order.getOrderType() == ClinicType.CONSULT)) {
            // 这里只处理咨询复诊
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (StringUtils.isEmpty(vm.getReason())) {
            vm.setReason("用户取消");
        }
        vm.setDiagnosisCa(null);
        orderService.endOrder(user, null, order, vm, vm.getReason());
    }

    @PutMapping("/orders/{orderId}/register")
    @ApiOperation("急诊挂号/复诊挂号/预约")
    @ActionLog(action = "急诊挂号/复诊挂号/预约")
    public OrderVM registerOrder(@PathVariable(name = "orderId") long orderId,
                                 @RequestBody RegisterVM vm) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!(order.getOrderType() == ClinicType.OUT || order.getOrderType() == ClinicType.CONSULT)) {
            // 这里只处理咨询复诊
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (null != order.getPatient().getBirthday() && TimeUtils.age(order.getPatient().getBirthday()) < 14
            && (vm.getOrderType() == ClinicType.OUT || vm.getOrderType() == ClinicType.EMERGENCY)) {
            throw ErrorType.REGISTER_MIN_AGE.toProblem("低于14岁不能挂号复诊，仅可进行医疗咨询");
        }
        order = orderService.registerOrder(user, order, vm);
        return new OrderVM(order);
    }

    @GetMapping("/orders/{orderId}.png")
    @ApiOperation("预约二维码")
    @ActionLog(action = "预约二维码")
    public void registerOrder(HttpServletResponse response,
                              @PathVariable(name = "orderId") long orderId) throws Exception {
        Order order = orderRepository.getById(orderId);
        // 二维码中包含的信息
        String content = order.getVisitId() + "," + order.getAccessCode();
        QRUtils.createQRImageToResponse(response, content);
    }

    @PutMapping("/orders/{orderId}")
    @ApiOperation("修改订单-补充信息, 非图片数据")
    @ActionLog(action = "修改订单")
    public OrderVM updateOrder(@PathVariable(name = "orderId") long orderId,
                               @Valid @RequestBody OrderDTO dto) {
        User user = CurrentUser.getOrThrow();
        dto.setId(orderId);
        Order order = orderRepository.getById(orderId);
        if (!(order.getOrderType() == ClinicType.OUT || order.getOrderType() == ClinicType.CONSULT)) {
            // 这里只处理咨询复诊
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return new OrderVM(orderService.updateOrder(order, dto));
    }

    @PutMapping("/orders/{orderId}/images/{type}")
    @ApiOperation("修改订单-补充信息, 图片数据")
    @ActionLog(action = "修改订单")
    public List<UploadVM> updateOrderImages(@PathVariable(name = "orderId") long orderId,
                                            @PathVariable(name = "type") OrderImage.Type type,
                                            @Valid @RequestBody List<UploadVM> images) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!(order.getOrderType() == ClinicType.OUT || order.getOrderType() == ClinicType.CONSULT)) {
            // 这里只处理咨询复诊
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return orderService.updateImagesForOrder(user, order, type, images)
            .stream()
            .map(UploadVM::new)
            .collect(Collectors.toList());
    }

    @GetMapping("/orders/{id}")
    @ApiOperation("订单详情")
    @ActionLog(action = "订单详情")
    public OrderVM getDetailOrder(@PathVariable Long id) {
        Order order = orderRepository.getById(id);
        User user = CurrentUser.getOrThrow();
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        int estimateWaitTime = orderService.getEstimateWaitTime(order);
        OrderVM vm = new OrderVM(order, estimateWaitTime);
        if (order.getService() == null) {
            orderExtraInfoRepository.findByOrderId(order.getId()).ifPresent(extraInfo -> {
                ScheduleVM schedule = new ScheduleVM();
                schedule.setStartTime(TimeUtils.convert(extraInfo.getHisSourceNumberBeginTime()));
                vm.setService(schedule);
            });
        }
        if (order.getOrderType() == ClinicType.NURSING_CONSULT) {
            Optional<OrderNursingExt> orderNursingExtOptional = orderNursingExtRepository.findOneByBaseOrder(order);
            orderNursingExtOptional.ifPresent(orderNursingExt -> vm.setOrderNursingExt(new OrderNursingExtDTO(orderNursingExt)));
        }
        return vm;
    }

    @GetMapping("/prescription_order/{id}/orders/detail")
    @ApiOperation("通过处方id查订单详情")
    @ActionLog(action = "通过处方id查订单详情")
    public OrderVM getDetailOrderByPrescriptionOrderId(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = prescriptionOrderRepository.getById(id).getOrder();
        User user = CurrentUser.getOrThrow();
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem();
        }
        int estimateWaitTime = orderService.getEstimateWaitTime(order);
        return new OrderVM(order, estimateWaitTime);
    }

    @GetMapping("/orders")
    @ApiOperation("获取当前用户所有订单")
    @ActionLog(action = "获取当前用户所有订单")
    public Page<OrderVM> listPatientOrder(
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
        @ApiParam("'-'表示排除 query=orderType:OUT visitType:VIDEO onlineType:ONLINE realTimeStatus:REAL_TIME "
            + "patientid:12345676 patientname:张三"
            + "带\"\"值的查询条件表示支持多值查询"
            + " orderStatusContains:\"PENDING PENDING_CANCELLED REGISTERED ONTIME_CONFIRMED STARTED COMPLETED REFUNDED\""
            + " visitTypeContains:\"VIDEO GRAPHIC\""
            + " orderTypeContains:\"OUT IN EMERGENCY CONSULT NURSING_CONSULT NURSING_HOME\""
        )
        @RequestParam(value = "query", required = false) String query) {
        Pageable pageable = PageRequest.of(pageNo, size, Direction.DESC, "createdDate");
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<Order>> list = Lists.newArrayList();
        list.add(HospitalOrderSearch.of(query).toSpecification());
        list.add(Specifications.ne("status", OrderStatus.DRAFT));
        list.add(Specifications.eq("user", user));
        list.add(Specifications.eq("hospital", hospital));
        list.add(Specifications.isTrue("enabled"));
        Page<OrderVM> orders = orderRepository.findAll(Specifications.and(list), pageable)
            .map(order -> {
                OrderVM orderVM = new OrderVM(order);
                if (order.getOrderType() == ClinicType.NURSING_CONSULT) {
                    Optional<OrderNursingExt> orderNursingExtOptional = orderNursingExtRepository.findOneByBaseOrder(order);
                    orderNursingExtOptional.ifPresent(orderNursingExt -> orderVM.setOrderNursingExt(new OrderNursingExtDTO(orderNursingExt)));
                }
                return orderVM;
            });
        List<Long> orderIds = orders.getContent().stream().map(AbstractEntityDTO::getId).collect(Collectors.toList());
        List<OrderExtraInfo> infos = orderExtraInfoRepository.findAllByOrderIdIn(orderIds);
        Map<Long, OrderExtraInfo> infoMap = infos.stream().collect(Collectors.toMap(OrderExtraInfo::getOrderId, v -> v));
        orders.getContent().forEach(order -> {
            if (order.getService() == null) {
                OrderExtraInfo extraInfo = infoMap.get(order.getId());
                if (extraInfo != null){
                    ScheduleVM schedule = new ScheduleVM();
                    schedule.setStartTime(TimeUtils.convert(extraInfo.getHisSourceNumberBeginTime()));
                    order.setService(schedule);
                }
            }
        });
        return orders;
    }

    @GetMapping("/orders/uncompleted")
    @ApiOperation("查询用户未结束的订单（已完成 和 已取消 除外）")
    @ActionLog(action = "查询用户未结束的订单")
    public List<OrderVM> getOrders(@ApiParam("'-'表示排除 query=orderType:OUT")
                                   @RequestParam(value = "query", required = false) String query) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<Order>> status = Lists.newArrayList();
        status.add(Specifications.eq("status", OrderStatus.STARTED));
        status.add(Specifications.eq("status", OrderStatus.ONTIME_CONFIRMED));
        status.add(Specifications.eq("status", OrderStatus.REGISTERED));
        status.add(Specifications.eq("status", OrderStatus.PENDING
        ));
        status.add(Specifications.eq("status", OrderStatus.TRIAGED
        ));
        status.add(Specifications.eq("status", OrderStatus.TRIAGING
        ));
        status.add(Specifications.eq("status", OrderStatus.SUBMITTED
        ));
        List<Specification<Order>> list = Lists.newArrayList();
        list.add(HospitalOrderSearch.of(query).toSpecification());
        list.add(Specifications.or(status));
        list.add(Specifications.eq("user", current));
        list.add(Specifications.eq("hospital", hospital));
        list.add(Specifications.isTrue("enabled"));

        return orderRepository.findAll(Specifications.and(list))
            .stream()
            .filter(u -> {
                if (u.getRealTimeStatus() == RealTimeStatus.APPOINTMENT) {
                    return u.getService().getStartTime().getTime() - Constants.ONE_DAY_MILLIONS
                        < System.currentTimeMillis();
                }
                return true;
            })
            .sorted(Comparator.comparing(Order::getCreatedDate).reversed())
            .map(OrderVM::new).collect(Collectors.toList());
    }

    @GetMapping("/orders/{id}/queue_status")
    @ApiOperation("查询用户当前候诊的排队状态")
    @ActionLog(action = "查询用户当前候诊的排队状态")
    public OrderQueueStatusVM getOrderQueueStatus(@PathVariable Long id) {
        Order order = orderRepository.getById(id);
        User user = CurrentUser.getOrThrow();
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return orderQueueService.getQueueStatusByOrder(order);
    }

    @GetMapping("/orders/{id}/payment_status")
    @Deprecated
    @ApiOperation("查询订单支付状态-暂不可用")
    @ActionLog(action = "查询订单支付状态")
    public OrderVM getOrderPaymentStatus(@PathVariable Long id) {
        User current = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(id);
        User user = CurrentUser.getOrThrow();
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return new OrderVM(orderService.getOrderPaymentStatus(current, order));
    }

    @PostMapping("/orders/{id}/order_evaluate")
    @ApiOperation("用户给出评价")
    @ActionLog(action = "用户给出评价")
    public OrderEvaluateDTO addOrderEvaluate(@PathVariable Long id,
                                             @Valid @RequestBody OrderEvaluateDTO evaluate) {
        Order order = orderRepository.getById(id);
        User user = CurrentUser.getOrThrow();
        if (!user.equals(order.getUser())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return orderService.saveOrderWorkerEvaluate(order, evaluate, order.getDoctor());
    }

    @GetMapping("/orders/{id}/order_evaluate")
    @ApiOperation("用户查看自己的评价")
    @ActionLog(action = "用户查看自己的评价")
    public OrderWorkerDTO getOrderEvaluate(@PathVariable Long id,
                                           @RequestParam EvaluateFor evaluateFor) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = orderRepository.getById(id);
        if (!user.equals(order.getUser())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        List<OrderWorker> collect = Lists.newArrayList();
        if (evaluateFor == EvaluateFor.DOCTOR) {
            collect = order.getWorkers().stream()
                .filter(u -> u.getUser().equals(order.getDoctor().getUser()) && u.getRating() > 0)
                .collect(Collectors.toList());
        }
        if (collect.isEmpty()) {
            return null;
        }
        OrderWorker orderWorker = collect.get(0);
        return new OrderWorkerDTO(orderWorker);
    }

    @DeleteMapping("/orders/{id}/order_evaluate")
    @ApiOperation("用户删除自己的评价")
    @ActionLog(action = "用户删除自己的评价")
    public void deleteOrderEvaluate(@PathVariable Long id) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(id);
        if (!user.equals(order.getUser())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        order.getWorkers().stream()
            .filter(u -> u.getUser().equals(order.getDoctor().getUser()) && u.getRating() > 0)
            .findFirst().ifPresent(orderWorker -> {
                orderWorker.setReview("");
                orderWorker.setEvaluateDate(null);
                orderWorker.setEvaluateTag("");
                orderWorker.setRating(0);
                orderWorkerRepository.save(orderWorker);
            });
    }

    @GetMapping("/orders/order_evaluate/list")
    @ApiOperation("用户查看自己的线上问诊评价列表")
    @ActionLog(action = "用户查看自己的线上问诊评价列表")
    public IhPage<OrderVM> getOrderEvaluateList(
        @RequestParam EvaluateFor evaluateFor,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Order> orders = orderRepository.findAllByHospitalAndUser(hospital, user);
        List<OrderVM> list = Lists.newArrayList();
        if (evaluateFor == EvaluateFor.DOCTOR) {
            List<OrderWorker> orderWorkerList = Lists.newArrayList();
            orders.forEach(order -> order.getWorkers().stream()
                .filter(u -> u.getUser().equals(order.getDoctor().getUser()) && u.getRating() > 0)
                .findFirst().ifPresent(orderWorkerList::add));
            list = orderWorkerList.stream()
                .map(orderWorker -> new OrderVM(orderWorker.getOrder()))
                .sorted(Comparator.comparing(OrderVM::getRegisteredDate).reversed())
                .collect(Collectors.toList());
        }
        //分页
        int start = pageNo * size;
        int end = start + size;
        list = list.subList(Math.min(start, list.size()), Math.min(end, list.size()));
        return new IhPage<>(new PageImpl<>(list));
    }

    @GetMapping("/order_evaluate/{medicalWorkerId}/list")
    @ApiOperation("用户查看医生的评价列表")
    @ActionLog(action = "用户查看自己的评价列表")
    public Page<OrderWorkerDTO> getDoctorEvaluateList(
        @ApiParam("医生id") @PathVariable("medicalWorkerId") long medicalWorkerId,
        @ApiParam("tag") @RequestParam(name = "tag", required = false) String tag,
        @ApiParam("sort") @RequestParam(name = "sort", required = false, defaultValue = "desc") String sort,
        @ApiParam("page") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("size") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        MedicalWorker medicalWorker = medicalWorkerRepository.getById(medicalWorkerId);
        Hospital hospital = CurrentHospital.getOrThrow();
        Specification<OrderWorker> specification = (root, criteriaQuery, criteriaBuilder) -> {
            Join<OrderWorker, Order> orderJoin = root.join("order");
            Predicate docPre = criteriaBuilder.equal(root.get("user"), medicalWorker.getUser());
            Predicate ratingPre = criteriaBuilder.ge(root.get("rating"), 1);
            Predicate hospitalPre = criteriaBuilder.equal(orderJoin.get("hospital"), hospital);
            Predicate showPre = criteriaBuilder.isTrue(root.get("showed"));
            if (StringUtils.isNotBlank(tag)) {
                Predicate evaluateTag = criteriaBuilder.like(root.get("evaluateTag"), "%\""+ tag+"\"%");
                return criteriaBuilder.and(docPre, ratingPre, evaluateTag, hospitalPre, showPre);
            }
            return criteriaBuilder.and(docPre, ratingPre, hospitalPre, showPre);
        };
        Direction desc = "desc".equalsIgnoreCase(sort) ? Direction.DESC : Direction.ASC;
        PageRequest request = PageRequest.of(pageNo, size, desc, "evaluateDate");
        return orderWorkerRepository.findAll(specification, request).map(OrderWorkerDTO::new);
    }

    @GetMapping("/order_evaluate_tag/{medicalWorkerId}/list")
    @ApiOperation("用户查看医生的评价标签列表")
    @ActionLog(action = "用户查看医生的评价标签列表")
    public List<OrderEvaluateTagDTO> getDoctorEvaluateList(@PathVariable("medicalWorkerId") long medicalWorkerId) {
        MedicalWorker medicalWorker = medicalWorkerRepository.getById(medicalWorkerId);
        Hospital hospital = CurrentHospital.getOrThrow();
        Specification<OrderWorker> specification = (root, criteriaQuery, criteriaBuilder) -> {
            Join<OrderWorker, Order> orderJoin = root.join("order");
            Predicate docPre = criteriaBuilder.equal(root.get("user"), medicalWorker.getUser());
            Predicate ratingPre = criteriaBuilder.ge(root.get("rating"), 1);
            Predicate hospitalPre = criteriaBuilder.equal(orderJoin.get("hospital"), hospital);
            Predicate showPre = criteriaBuilder.isTrue(root.get("showed"));
            //Predicate evaluateTag = criteriaBuilder.isNotNull(root.get("evaluateTag"));
            return criteriaBuilder.and(docPre, ratingPre, hospitalPre, showPre);
        };
        List<List<String>> tagListList = orderWorkerRepository.findAll(specification).stream()
            .map(OrderWorker::getEvaluateTag).filter(StringUtils::isNotBlank)
            .map(tag -> JSONArray.parseArray(tag, String.class)).collect(Collectors.toList());
        Map<String, Integer> tagCounts = Maps.newHashMap();
        for (List<String> tagList : tagListList) {
            for (String tag : tagList) {
                tagCounts.put(tag, tagCounts.getOrDefault(tag, 0) + 1);
            }
        }
        List<OrderEvaluateTagDTO> list = Lists.newArrayList();
        tagCounts.forEach((k, v) -> list.add(new OrderEvaluateTagDTO(k, v)));
        return list;
    }

    @PutMapping("/orders/{orderId}/prescription/{id}/used")
    @ApiOperation("处方已使用")
    @ActionLog(action = "处方已使用")
    public void updatePrescriptionUsed(@PathVariable(name = "orderId") long orderId,
                                       @PathVariable(name = "id") long id) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(id);
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(id);
        if (!Objects.equals(prescriptionOrder.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        prescriptionService.updatePerscriptionUsed(user, orderId, id);
    }

    @GetMapping("/orders/{orderId}/medical_record")
    @ApiOperation("病例详情")
    @ActionLog(action = "病例详情")
    public MedicalCaseVM medicalCaseDetails(@PathVariable(name = "orderId") long orderId) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (!Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return medicalCaseRepository.findByOrderId(orderId)
            .map(MedicalCaseVM::new).orElse(null);
    }

    @GetMapping("/orders/{orderId}/prescription_order")
    @ApiOperation("获取就诊人的订单处方列表")
    @ActionLog(action = "获取就诊人的订单处方列表")
    public List<PrescriptionOrderVM> getMedicalCaseDetails(@PathVariable long orderId) {
        User current = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        shouldAccessOrderInfo(order, current);
        return prescriptionService.getMedicalCaseDetails(orderId);
    }

    /**
     * 是否拥有订单权限
     *
     * @param order
     * @param user
     */
    private void shouldAccessOrderInfo(Order order, User user) {
        if (Objects.equals(order.getUser(), user)) {
            return;
        }
        throw ErrorType.FORBIDDEN.toProblem();
    }

    @GetMapping("/orders/prescription_order/{prescriptionOrderId}")
    @ApiOperation("获取就诊人的订单处方详情")
    @ActionLog(action = "获取就诊人的订单处方详情")
    public List<PrescriptionOrderVM> getMedicalCaseDetailsByPrescriptionOrderId(
        @PathVariable long prescriptionOrderId) {
        User current = CurrentUser.getOrThrow();
        Order order = prescriptionOrderRepository.findById(prescriptionOrderId)
            .map(PrescriptionOrder::getOrder)
            .orElseThrow(ErrorType.ILLEGAL_PARAMS::toProblem);
//        shouldAccessOrderInfo(order, current);
        return prescriptionService.getMedicalCaseDetails(order.getId());
    }

    @GetMapping("/orders/timmessage/list")
    @ApiOperation("患者端-根据订单状态获取当前患者订单列表含消息-"
        + "(最近咨询列表：orderStatus:候诊中ONTIME_CONFIRMED+进行中STARTED+已完成COMPLETED+已退款REFUNDING+退款完成REFUNDED"
        + "+候诊中无医生接诊TIME_OUT_ONTIME_CONFIRMED_REFUNDED+医生接诊中退诊STARTED_REFUNDED+咨询完成后医生发起退款COMPLETED_REFUNDED"
        + "+候诊中患者手工取消订单ONTIME_CONFIRMED_REFUNDED+医生候诊中退诊ONTIME_CONFIRMED_DOCTOR_REFUNDED)"
        + "(已完成列表:orderStatus:已完成COMPLETED+超时已完成TIME_OUT_COMPLETED+退款完成REFUNDED"
        + "+候诊中无医生接诊TIME_OUT_ONTIME_CONFIRMED_REFUNDED+医生接诊中退诊STARTED_REFUNDED+咨询完成后医生发起退款COMPLETED_REFUNDED\"\n"
        + "+候诊中患者手工取消订单ONTIME_CONFIRMED_REFUNDED+医生候诊中退诊ONTIME_CONFIRMED_DOCTOR_REFUNDED)")
    @ActionLog(action = "患者端-订单列表含消息")
    public IhPage<OrderTIMMessageVM> orderList(
        @ApiParam("带\"\"值的查询条件表示支持多值查询,"
            + "orderStatusContains:\"ONTIME_CONFIRMED STARTED COMPLETED REFUNDING REFUNDED\""
            + "orderTypeContains:\"OUT EMERGENCY IN CONSULT UNKNOWN\""
            + "onlineTypeContains:\"ONLINE OFFLINE\""
            + "realtimeStatusContains:\"REAL_TIME APPOINTMENT\""
            + "visitTypeContains:\"VIDEO GRAPHIC\"") @RequestParam(name = "query", required
            = false) String query,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable pageable = PageRequest.of(pageNo, size, Sort.by(Sort.Direction.DESC, "createdDate"));
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<Order>> list = Lists.newArrayList();
        list.add(HospitalOrderSearch.of(query).toSpecification());
        list.add(Specifications.eq("user", user));
        list.add(Specifications.eq("hospital", hospital));
        list.add(Specifications.isTrue("patient.enabled"));
        Page<OrderTIMMessageVM> page = orderRepository.findAll(Specifications.and(list), pageable)
            .map(elem -> new OrderTIMMessageVM(elem, user, true));
        List<OrderTIMMessageVM> sortList = page.getContent().stream()
            .sorted((o1, o2) -> {
                if (o1.getTimMessageDTO() == null || o2.getTimMessageDTO() == null) {
                    return o2.getOrder().getCreatedDate().compareTo(o1.getOrder().getCreatedDate());
                } else {
                    return Long.compare(o2.getTimMessageDTO().getTime().getTime(), o1.getTimMessageDTO().getTime().getTime());
                }
            })
            .collect(Collectors.toList());
        return new IhPage<>(new PageImpl<>(sortList, pageable, page.getTotalElements()));
    }


    @PutMapping("/orders/im/group/{groupId}/message/{sequence}/read")
    @ApiOperation("更新当前用户的指定订单聊天室消息为已读")
    @ActionLog(action = "更新当前用户的指定订单聊天室消息为已读")
    public void readMessage(@PathVariable String groupId, @PathVariable int sequence) {
        User current = CurrentUser.getOrThrow();
        Optional<TIMGroupMember> byGroupIdAndUsername = timGroupMemberRepository.findByGroupIdAndUsername(groupId,
                                                                                                          current.getUsername());
        byGroupIdAndUsername.ifPresent(u -> {
            u.setSequence(sequence);
            timGroupMemberRepository.save(u);
            redisUtil.del("order_reply:" + groupId);
        });
    }

    @GetMapping("/orders/im/message/un_read_count")
    @ApiOperation("获取当前用户的指定订单聊天室未读消息数量")
    @ActionLog(action = "获取当前用户的指定聊天室未读消息数量")
    public int getUnReadCount(
        @ApiParam("'-'表示排除 query=orderType:OUT visitType:VIDEO onlineType:ONLINE realTimeStatus:REAL_TIME"
            + "带\"\"值的查询条件表示支持多值查询"
            + " orderStatusContains:\"PENDING PENDING_CANCELLED REGISTERED ONTIME_CONFIRMED STARTED COMPLETED REFUNDED\""
            + " visitTypeContains:\"VIDEO GRAPHIC\"")
        @RequestParam(value = "query", required = false) String query) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<Order>> list = Lists.newArrayList();
        list.add(HospitalOrderSearch.of(query).toSpecification());
        list.add(Specifications.eq("user", user));
        list.add(Specifications.eq("hospital", hospital));
        list.add(Specifications.isTrue("enabled"));
        list.add(Specifications.isTrue("patient.enabled"));
        return timMessageService.getUnReadCount(list, user);
    }

    // 2023年07月17日17:29:12 待支付、已完成、已取消、已退款订单，包含删除订单功能
    @DeleteMapping("/orders/{id}")
    @ApiOperation("用户删除订单-删除后订单仅对用户不可见")
    @ActionLog(action = "删除订单")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable Long id) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = orderRepository.getById(id);

        if (!order.getUser().equals(user)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("订单不属于当前用户, 不允许删除");
        }

        List<String> statuses = HospitalSettingsHelper.getValue(hospital,
                                                                HospitalSettingKey.ORDER_STATUS_ENABLE_TO_DELETE,
                                                                new TypeReference<>() {
                                                                });

        if (!statuses.contains(order.getStatus().name())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("订单状态不允许删除");
        }
        order.setEnabled(false);
        orderRepository.save(order);
    }

}
