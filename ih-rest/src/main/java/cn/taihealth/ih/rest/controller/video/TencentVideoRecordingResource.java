package cn.taihealth.ih.rest.controller.video;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.service.api.VideoChatService;
import cn.taihealth.ih.service.vm.video.TencentVideoRecordingVM;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: jzs
 * @Date: 2023-09-19
 */
@RestController
@RequestMapping("/user/tencent_video_recording")
@Api(tags = "视频通话录像", description = "视频通话录像")
@AllArgsConstructor
public class TencentVideoRecordingResource {

    private final VideoChatService videoChatService;

    @GetMapping("/{orderId}")
    @ApiOperation("获取用户的视频通话录像")
    @ActionLog(action = "获取用户的视频通话录像")
    public List<TencentVideoRecordingVM> getTencentVideoRecordingList(
            @ApiParam("当前订单Id") @PathVariable long orderId) {
        return videoChatService.getTencentVideoRecordingList(orderId);
    }
}
