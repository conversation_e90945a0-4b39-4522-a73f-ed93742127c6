package cn.taihealth.ih.rest.config;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.cache.HospitalCache;
import cn.taihealth.ih.spring.security.SecurityUtils;
import com.gitq.jedi.context.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 */
@Configuration
@Slf4j
public class CurrentConfiguration {

    @Autowired
    private HttpServletRequest request;
    private final UserCacheFindService userCacheFindService;

    public CurrentConfiguration(UserCacheFindService userCacheFindService) {
        this.userCacheFindService = userCacheFindService;
    }

    @Bean
    public CurrentUser currentUser() {
        return new CurrentUser() {
            @Override
            public Optional<User> get() {
                return userCacheFindService.findOneByUsername(SecurityUtils.getCurrentUserLogin());
            }
        };
    }

    @Bean
    public CurrentHospital currentHospital() {
        return new CurrentHospital() {
            @Override
            public Optional<Hospital> get() {
                String hospitalCode = getHospitalCode();
                if (StringUtils.isBlank(hospitalCode)) {
                    return Optional.empty();
                }
                HospitalCache hospitalCache = AppContext.getInstance(HospitalCache.class);
                return hospitalCache.getHospital(hospitalCode);
            }

            @Override
            public String getHospitalCode() {
                if (request == null) {
                    return null;
                }
                String hospitalCode = request.getHeader("IH_HOSPITAL");
                if (StringUtils.isBlank(hospitalCode)) {
                    hospitalCode = request.getParameter("hospital");
                }
                return hospitalCode;
            }

            @Override
            public String getRequestItem() {
                if (request == null) {
                    return null;
                }
                return request.getHeader("ITEM");
            }

        };
    }
}
