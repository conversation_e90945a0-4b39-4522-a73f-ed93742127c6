package cn.taihealth.ih.rest.controller.hospital.crm;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.dao.PatientDao;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.crm.CrmPlan;
import cn.taihealth.ih.domain.crm.CrmTaskDetail;
import cn.taihealth.ih.domain.crm.CrmTaskDetailResult;
import cn.taihealth.ih.domain.enums.CrmStatus;
import cn.taihealth.ih.repo.crm.CrmPlanRepository;
import cn.taihealth.ih.repo.crm.CrmTaskDetailRepository;
import cn.taihealth.ih.repo.crm.CrmTaskDetailResultRepository;
import cn.taihealth.ih.repo.crm.CrmTaskRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.crm.CrmPlanService;
import cn.taihealth.ih.service.api.crm.CrmStatisticsService;
import cn.taihealth.ih.service.vm.IhPage;
import cn.taihealth.ih.service.vm.crm.*;
import cn.taihealth.ih.service.vm.healthhistory.PatientDiseaseVM;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 */
@RestController
@RequestMapping(value = {"/hospital"})
@Api(tags = "随访统计", description = "随访统计相关API")
public class CrmStatisticsResource {

    private final CrmPlanService crmPlanService;
    private final CrmPlanRepository crmPlanRepository;
    private final CrmTaskRepository crmTaskRepository;
    private final CrmStatisticsService crmStatisticsService;
    private final CrmTaskDetailRepository crmTaskDetailRepository;
    private final CrmTaskDetailResultRepository crmTaskDetailResultRepository;

    public CrmStatisticsResource(CrmPlanService crmPlanService,
                                 CrmPlanRepository crmPlanRepository,
                                 CrmTaskRepository crmTaskRepository,
                                 CrmStatisticsService crmStatisticsService,
                                 CrmTaskDetailRepository crmTaskDetailRepository,
                                 CrmTaskDetailResultRepository crmTaskDetailResultRepository) {
        this.crmPlanService = crmPlanService;
        this.crmPlanRepository = crmPlanRepository;
        this.crmTaskRepository = crmTaskRepository;
        this.crmStatisticsService = crmStatisticsService;
        this.crmTaskDetailRepository = crmTaskDetailRepository;
        this.crmTaskDetailResultRepository = crmTaskDetailResultRepository;
    }

    @GetMapping("/crm/statistics/today")
    @ApiOperation("今日看板（今日随访数-待随访数-完成数-终止数）")
    @ActionLog(action = "今日看板")
    public CrmStatisticsTodayVM getPanel() {
        Hospital hospital = CurrentHospital.getOrThrow();
        Date now = new Date();
        Date startOfDay = TimeUtils.getStartOfDay(now);
        Date endOfDay = TimeUtils.getEndOfDay(now);
//        Date startOfDay = DateUtils.addDays(endOfDay, -20);
        return crmStatisticsService.getBoardData(hospital, startOfDay, endOfDay);
    }


    @GetMapping("/crm/statistics/today/lists")
    @ApiOperation("今日随访列表")
    @ActionLog(action = "今日随访列表")
    public IhPage<CrmTaskDetailStatisticsVM> getTasks(@ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                                      @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();

        Pageable page = PageRequest.of(pageNo, size, Direction.ASC, "planTime", "id");
        List<Specification<CrmTaskDetail>> specs = Lists.newArrayList();
        Date date = new Date();
        Date end = TimeUtils.getEndOfDay(date);
        Date start = TimeUtils.getStartOfDay(date);

        Specification<CrmTaskDetail> specification = Specifications.or(
            Specifications.or(Specifications.eq("status", CrmStatus.STARTED),
                Specifications.eq("status", CrmStatus.CREATED)),
            Specifications.and(
                Specifications.or(Specifications.eq("status", CrmStatus.FINISHED),
                    Specifications.eq("status", CrmStatus.TERMINATED)),
                Specifications.lt("endTime", end), Specifications.ge("endTime", start)
            )
        );

        specs.add(specification);
        specs.add(Specifications.lt("planTime", end));
        specs.add(Specifications.isTrue("plan.active"));
        specs.add(Specifications.eq("hospital", hospital));
        return new IhPage<>(crmTaskDetailRepository.findAll(Specifications.and(specs), page).map(u -> {
            CrmTaskDetailStatisticsVM vm = new CrmTaskDetailStatisticsVM(u);
            Patient patient = u.getPatient();
            PatientDiseaseVM patientDiseaseVM = new PatientDiseaseVM(patient);
            patientDiseaseVM.setDiseases(PatientDao.findAllPatientDiseaseByPatient(patient.getId())
                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            vm.setPatient(patientDiseaseVM);
            if (u.getStatus() == CrmStatus.FINISHED
                || u.getStatus() == CrmStatus.TERMINATED) {
                Optional<CrmTaskDetailResult> oneByTaskDetail = crmTaskDetailResultRepository
                    .findOneByTaskDetail(u);
                if (oneByTaskDetail.isPresent()) {
                    CrmTaskDetailResult result = oneByTaskDetail.get();
                    vm.setTerminateReason(result.getTerminateReason());
                    vm.setRemark(result.getRemark());
                }
            }
            return vm;
        }));
    }

    @GetMapping("/crm/statistics/today/diagram")
    @ApiOperation("今日随访分布图")
    @ActionLog(action = "今日随访分布图")
    public List<CrmStatisticsTodayGroupVM> getTu() {
        Hospital hospital = CurrentHospital.getOrThrow();
        Date now = new Date();
        Date startOfDay = TimeUtils.getStartOfDay(now);
        Date endOfDay = TimeUtils.getEndOfDay(now);
        return crmStatisticsService.getBoardDataGroup(hospital, startOfDay, endOfDay);
    }

    @GetMapping("/crm/statistics/satisfactions/plans/{id}/scores")
    @ApiOperation("满意度-得分详情")
    @ActionLog(action = "满意度-得分详情")
    public CrmStatisticsScoreVM getScores(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        CrmPlan plan = crmPlanRepository.getById(id);
        return crmStatisticsService.getScoreData(hospital, plan);
    }

    @GetMapping("/crm/statistics/satisfactions/plans/{id}/scores/charts")
    @ApiOperation("满意度-散点图数据")
    @ActionLog(action = "满意度-散点图数据")
    public List<CrmStatisticsScoreForChartVM> getScoreList(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        CrmPlan plan = crmPlanRepository.getById(id);
        return crmStatisticsService.getScoreList(hospital, plan);
    }

    @GetMapping("/crm/statistics/satisfactions/plans/{id}/scores/diagrams")
    @ApiOperation("满意度-分布图数据")
    @ActionLog(action = "满意度-分布图数据")
    public List<CrmStatisticsScoreForDiagramVM> getScoreDiagram(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        CrmPlan plan = crmPlanRepository.getById(id);
        return crmStatisticsService.getScoreDiagram(hospital, plan);
    }

    @GetMapping("/crm/statistics/plans")
    @ApiOperation("随访计划列表")
    @ActionLog(action = "随访计划列表")
    public IhPage<CrmStatisticsPlanVM> getScoreDiagram(@ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                                     @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();

        Pageable page = PageRequest.of(pageNo, size);

        return new IhPage<>(crmStatisticsService.getPlans(hospital, page));
    }


}
