package cn.taihealth.ih.rest.controller.hospital.crm;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.dao.PatientDao;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.crm.CrmPlan;
import cn.taihealth.ih.domain.crm.CrmTask;
import cn.taihealth.ih.domain.crm.CrmTaskDetailResult;
import cn.taihealth.ih.domain.enums.CrmStatus;
import cn.taihealth.ih.repo.crm.CrmPlanRepository;
import cn.taihealth.ih.repo.crm.CrmTaskDetailResultRepository;
import cn.taihealth.ih.repo.crm.CrmTaskRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.crm.CrmPlanService;
import cn.taihealth.ih.service.dto.crm.CrmPlanDTO;
import cn.taihealth.ih.service.impl.filter.crm.plan.CrmPlanSearch;
import cn.taihealth.ih.service.impl.filter.crm.task.CrmTaskSearch;
import cn.taihealth.ih.service.vm.IhPage;
import cn.taihealth.ih.service.vm.crm.CrmTaskDetailVM;
import cn.taihealth.ih.service.vm.crm.CrmTaskVM;
import cn.taihealth.ih.service.vm.healthhistory.PatientDiseaseVM;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 */
@RestController
@RequestMapping(value = {"/hospital"})
@Api(tags = "随访-计划管理", description = "随访-计划管理相关API")
public class CrmPlanResource {

    private final CrmPlanService crmPlanService;
    private final CrmPlanRepository crmPlanRepository;
    private final CrmTaskRepository crmTaskRepository;
    private final CrmTaskDetailResultRepository crmTaskDetailResultRepository;

    public CrmPlanResource(CrmPlanService crmPlanService,
                           CrmPlanRepository crmPlanRepository,
                           CrmTaskRepository crmTaskRepository,
                           CrmTaskDetailResultRepository crmTaskDetailResultRepository) {
        this.crmPlanService = crmPlanService;
        this.crmPlanRepository = crmPlanRepository;
        this.crmTaskRepository = crmTaskRepository;
        this.crmTaskDetailResultRepository = crmTaskDetailResultRepository;
    }

    @PostMapping("/crm/plans")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation("添加计划")
    @ActionLog(action = "添加计划")
    public CrmPlanDTO addPlans(@RequestBody @Valid CrmPlanDTO dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User creator = CurrentUser.getOrThrow();
        return crmPlanService.addPlan(hospital, creator, dto);
    }

    @PutMapping("/crm/plans/{id}")
    @ApiOperation("修改计划")
    @ActionLog(action = "修改计划")
    public CrmPlanDTO updatePlans(@PathVariable Long id,
                                  @RequestBody @Valid CrmPlanDTO dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User creator = CurrentUser.getOrThrow();
        CrmPlan plan = crmPlanRepository.getById(id);
        if(!Objects.equals(plan.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!plan.isEnabled()) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("该计划已被删除或不存在，无法修改");
        }
        return crmPlanService.updatePlan(hospital, creator, plan, dto);
    }

    @PatchMapping("/crm/plans/{id}/active/{active}")
    @ApiOperation("停止/运行计划")
    @ActionLog(action = "停止/运行计划")
    public void startOrStopPlan(@PathVariable Long id,
                                @PathVariable boolean active) {
        Hospital hospital = CurrentHospital.getOrThrow();
        CrmPlan plan = crmPlanRepository.getById(id);
        if(!Objects.equals(plan.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!plan.isEnabled()) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("该计划已被删除或不存在，无法修改");
        }
        plan.setActive(active);
        crmPlanRepository.save(plan);
    }

    @DeleteMapping("/crm/plans/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("删除计划")
    @ActionLog(action = "删除计划")
    public void deletePlans(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User creator = CurrentUser.getOrThrow();
        CrmPlan plan = crmPlanRepository.getById(id);
        if(!Objects.equals(plan.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!plan.isEnabled()) {
            return;
        }
        crmPlanService.deletePlan(creator, plan);
    }

    @GetMapping("/crm/plans")
    @ApiOperation("查询计划-列表")
    @ActionLog(action = "查询计划")
    public IhPage<CrmPlanDTO> getPlans(@ApiParam("query= ACTIVE:TRUE/FALSE NAME:xx计划 TAG_NAME:标签名 TAG_ID:id1 TAG_ID:id2") @RequestParam(value = "query", required = false) String query,
                                       @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                       @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();

        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate", "id");
        CrmPlanSearch search = CrmPlanSearch.of(query);
        List<Specification<CrmPlan>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.isTrue("enabled"));
        specs.add(search.toSpecification());
        return new IhPage<>(crmPlanRepository.findAll(Specifications.and(specs), page).map(CrmPlanDTO::new));
    }

    @GetMapping("/crm/plans/tasks")
    @ApiOperation("查询随访任务-列表")
    @ActionLog(action = "查询随访任务")
    public IhPage<CrmTaskVM> getTasks(@ApiParam("query= STATUS:NOW(当前随访)/TODAY(今日随访) NAME_PHONE_ID_PLAN:姓名/手机号/疾病名称/身份证号/随访计划名称  ") @RequestParam(value = "query", required = false) String query,
                                    @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                    @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();

        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "firstTime", "id");
        CrmTaskSearch search = CrmTaskSearch.of(query);
        List<Specification<CrmTask>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.isTrue("plan.active"));
        specs.add(search.toSpecification());
        return new IhPage<>(crmTaskRepository.findAll(Specifications.and(specs), page).map(u -> {
            CrmTaskVM vm = new CrmTaskVM(u);
            List<CrmTaskDetailVM> detailVMS = u.getTaskDetails().stream().map(t -> {
                CrmTaskDetailVM crmTaskDetailVM = new CrmTaskDetailVM(t);
                if (crmTaskDetailVM.getStatus() == CrmStatus.FINISHED
                    || crmTaskDetailVM.getStatus() == CrmStatus.TERMINATED) {
                    Optional<CrmTaskDetailResult> oneByTaskDetail = crmTaskDetailResultRepository
                        .findOneByTaskDetail(t);
                    if (oneByTaskDetail.isPresent()) {
                        CrmTaskDetailResult result = oneByTaskDetail.get();
                        crmTaskDetailVM.setTerminateReason(result.getTerminateReason());
                        crmTaskDetailVM.setTerminateReasonId(result.getTerminateReasonId());
                    }
                    if (t.getStatus() == CrmStatus.TERMINATED) {
                        crmTaskDetailVM.setTerminateDate(t.getEndTime());
                    }
                }
                return crmTaskDetailVM;
            }).collect(Collectors.toList());
            vm.setTaskDetails(detailVMS);

            Patient patient = u.getPatient();
            PatientDiseaseVM patientDiseaseVM = new PatientDiseaseVM(patient);
            patientDiseaseVM.setDiseases(PatientDao.findAllPatientDiseaseByPatient(patient.getId())
                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            vm.setPatient(patientDiseaseVM);
            return vm;
        }));
    }


}
