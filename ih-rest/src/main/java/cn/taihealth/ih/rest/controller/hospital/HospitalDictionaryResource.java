package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.HospitalDictionary;
import cn.taihealth.ih.repo.HospitalDictionaryRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.dictionary.DictionaryService;
import cn.taihealth.ih.service.dto.HospitalDictionaryDTO;
import cn.taihealth.ih.service.vm.IhPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RestController
@RequestMapping(value = {"/hospital/dictionary"})
@Api(tags = "d端-字典管理", description = "字典管理")
public class HospitalDictionaryResource {

    private final DictionaryService dictionaryService;

    private final HospitalDictionaryRepository hospitalDictionaryRepository;

    public HospitalDictionaryResource(DictionaryService dictionaryService,
                                      HospitalDictionaryRepository hospitalDictionaryRepository) {
        this.dictionaryService = dictionaryService;
        this.hospitalDictionaryRepository = hospitalDictionaryRepository;
    }

    @PostMapping
    @ApiOperation("添加字典")
    @ActionLog(action = "添加字典")
    public HospitalDictionaryDTO addDictionary(
        @ApiParam("传入的格式应该是需要新加的字典为主体，如果添加的是二级字典，则在parent属性中还应该带上对应的一级字典"
            + "例如：想添加一个一级字典，传入的对象应该包括name和code"
            + "；想添加二级字典，传入的对象应该包括name、code和parent，parent代表的是需要添加的字典对应的一级字典对象")
        @Valid @RequestBody HospitalDictionaryDTO dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        return dictionaryService.addDictionary(hospital, user, dto);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("根据code删除字典")
    @ActionLog(action = "根据code删除字典")
    public void removeDictionary(@ApiParam("字典id") @PathVariable long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        HospitalDictionary hospitalDictionary = hospitalDictionaryRepository.findById(id)
            .orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);
        if (!Objects.equals(hospital, hospitalDictionary.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        dictionaryService.removeDictionary(hospital, hospitalDictionary);
    }

    @PutMapping
    @ApiOperation("修改字典")
    @ActionLog(action = "修改字典")
    public HospitalDictionaryDTO updateDictionary(
        @ApiParam("修改字典只需要传入对应的字典对象即可，不需要传入对应的一级字典，只允许修改name和code，需要设置是否启用有专门的接口")
        @Valid @RequestBody HospitalDictionaryDTO dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        HospitalDictionary hospitalDictionary = hospitalDictionaryRepository.getById(dto.getId());
        if (!Objects.equals(hospital, hospitalDictionary.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return dictionaryService.updateDictionary(hospital, user, dto);
    }

    @GetMapping
    @ApiOperation("查询所有一级字典")
    @ActionLog(action = "查询所有一级字典")
    public Page<HospitalDictionaryDTO> findFirstDictionary(
        @ApiParam("字典名称") @RequestParam(name = "name", required = false) String name,
        @ApiParam("是否启用") @RequestParam(name = "enabled", required = false) Boolean enabled,
        @ApiParam(name = "page") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam(name = "size") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return dictionaryService.findFirstDictionary(hospital, name, enabled, pageNo, size);
    }

    @GetMapping("/enabled/{code}")
    @ApiOperation("用户根据code查询对应启用的字典")
    @ActionLog(action = "用户根据code查询对应启用的字典")
    public List<HospitalDictionaryDTO> findDictionaryByParentAndEnabled(@ApiParam("要查询的一级code") @PathVariable String code) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Optional<HospitalDictionary> optional = hospitalDictionaryRepository
            .findFirstByHospitalAndParentAndCode(hospital, null, code);
        if (optional.isEmpty() || !optional.get().getEnabled()) {
            return Lists.newArrayList();
        }
        return dictionaryService.findDictionaryByParentAndEnabled(hospital, optional.get());
    }

    @GetMapping("/{code}")
    @ApiOperation("根据code查询对应字典")
    @ActionLog(action = "根据code查询对应字典")
    public IhPage<HospitalDictionaryDTO> findDictionaryByParent(
        @ApiParam("父类code") @PathVariable String code,
        @ApiParam(name = "page") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam(name = "size") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return new IhPage<>(dictionaryService.findDictionaryByParent(hospital, code, pageNo, size));
    }

    @PutMapping("/{id}")
    @ApiOperation("根据code禁用/启用字典")
    @ActionLog(action = "根据code禁用字典")
    public HospitalDictionaryDTO updateDictEnable(@ApiParam("字典id") @PathVariable long id) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        HospitalDictionary hospitalDictionary = hospitalDictionaryRepository.findById(id)
            .orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);
        if (!Objects.equals(hospital, hospitalDictionary.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return dictionaryService.updateDictEnable(hospital, user, hospitalDictionary);
    }



}
