package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.Inspection;
import cn.taihealth.ih.domain.MedicalCase;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.*;
import cn.taihealth.ih.domain.hospital.*;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder.Status;
import cn.taihealth.ih.repo.MedicalCaseRepository;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.MedicalWorkerScoreRepository;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.hospital.order.CheckRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOperationRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionRepository;
import cn.taihealth.ih.repo.nursing.OrderNursingExtRepository;
import cn.taihealth.ih.repo.order.InspectionRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.dto.InspectionDTO;
import cn.taihealth.ih.service.dto.MedicalCaseDiseaseDTO;
import cn.taihealth.ih.service.dto.PrescriptionOrderDTO;
import cn.taihealth.ih.service.dto.historyrecord.OrderHistoryRecordDTO;
import cn.taihealth.ih.service.impl.filter.order.OrderRegisteredNotEndFilter;
import cn.taihealth.ih.service.impl.filter.order.hospitalorder.HospitalOrderSearch;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.util.SuperviseUtil;
import cn.taihealth.ih.service.vm.*;
import cn.taihealth.ih.service.vm.nursing.OrderNursingExtDTO;
import cn.taihealth.ih.service.vm.order.CompleteOrderVM;
import cn.taihealth.ih.service.vm.statistics.doctor.DoctorWorkbench;
import cn.taihealth.ih.supervise.dto.SuperviseDto;
import cn.taihealth.ih.supervise.service.SuperviseFactory;
import cn.taihealth.ih.supervise.service.SuperviseService;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Maps;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.*;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.persistence.criteria.Predicate;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> moon
 */
@RestController
@RequestMapping(value = {"/hospital"})
@Api(tags = "d端-诊疗-医生/护士操作", description = "d端-诊疗-医生/护士操作")
public class HospitalOrderResource {

    private static final Logger log = LoggerFactory.getLogger(HospitalOrderResource.class);

    private final HospitalService hospitalService;
    private final OrderService orderService;
    private final OrderRepository orderRepository;
    private final MedicalCaseRepository medicalCaseRepository;
    private final CheckRepository checkRepository;
    private final PrescriptionRepository prescriptionRepository;
    private final InspectionRepository inspectionRepository;
    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final PrescriptionService prescriptionService;
    private final PatientRepository patientRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final PrescriptionOperationRepository prescriptionOperationRepository;

    private final TIMMessageService timMessageService;

    private final MedicalWorkerScoreRepository medicalWorkerScoreRepository;
    private final OrderNursingExtRepository orderNursingExtRepository;

    public HospitalOrderResource(HospitalService hospitalService,
                                 OrderService orderService,
                                 OrderRepository orderRepository,
                                 MedicalCaseRepository medicalCaseRepository,
                                 CheckRepository checkRepository,
                                 PrescriptionRepository prescriptionRepository,
                                 InspectionRepository inspectionRepository,
                                 PrescriptionOrderRepository prescriptionorderrepository,
                                 PrescriptionService prescriptionService,
                                 MedicalWorkerRepository medicalWorkerRepository,
                                 PatientRepository patientRepository,
                                 PrescriptionOperationRepository prescriptionOperationRepository,
                                 TIMMessageService timMessageService,
                                 MedicalWorkerScoreRepository medicalWorkerScoreRepository,
                                 OrderNursingExtRepository orderNursingExtRepository) {
        this.hospitalService = hospitalService;
        this.orderService = orderService;
        this.orderRepository = orderRepository;
        this.medicalCaseRepository = medicalCaseRepository;
        this.checkRepository = checkRepository;
        this.prescriptionRepository = prescriptionRepository;
        this.inspectionRepository = inspectionRepository;
        this.prescriptionOrderRepository = prescriptionorderrepository;
        this.prescriptionService = prescriptionService;
        this.patientRepository = patientRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.prescriptionOperationRepository = prescriptionOperationRepository;
        this.timMessageService = timMessageService;
        this.medicalWorkerScoreRepository = medicalWorkerScoreRepository;
        this.orderNursingExtRepository = orderNursingExtRepository;
    }

    @GetMapping("/orders/need_triage")
    @ApiOperation("分诊列表/所有待分诊")
    @ActionLog(action = "查询分诊列表/所有待分诊")
    public List<OrderVM> findWaitingOrderList(
        @ApiParam(name = "查询", value = "查询格式  query = createTime:>=2020-08-01 sort:\"recommendLevel-asc createdDate-asc\" ") @RequestParam(value = "query", required = false) String query) {
        //throw ErrorType.UNAUTHORIZED.toProblem();`
        /* 智能等级排序，对应推荐等级 */
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return orderService.findAllOrderWaiting(query, user, hospital).stream().map(OrderVM::new)
            .collect(Collectors.toList());
    }

    @GetMapping("/orders/triaged")
    @ApiOperation("分诊列表/所有已分诊")
    @ActionLog(action = "查询分诊列表/所有已分诊")
    public IhPage<OrderVM> findOrderedList(
        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @ApiParam(name = "查询", value = "查询格式  query = time:>=2020-08-01") @RequestParam(value = "query", required = false) String query) {
        //人工等级排序，对应紧急等级
        Pageable request = PageRequest
            .of(pageNo, size, Sort.Direction.DESC, "triagedDate");
        User user = CurrentUser.getOrNull();
        Hospital hospital = CurrentHospital.getOrThrow();
        return new IhPage<>(orderService.findAllOrdered(query, user, request, hospital).map(OrderVM::new));
    }

    @PutMapping("/orders/{orderId}/triage/start")
    @ApiOperation("护士开始分诊")
    @ActionLog(action = "护士开始分诊")
    public OrderVM startTriage(@PathVariable(name = "orderId") long orderId) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        order = orderService.startTriageOrder(user, order);
        return new OrderVM(order);
    }


    @PutMapping("/orders/{orderId}/triage/completed")
    @ApiOperation("护士完成分诊")
    @ActionLog(action = "护士完成分诊")
    public OrderVM triage(@PathVariable(name = "orderId") long orderId,
                          @Valid @RequestBody TriageOrderVM vm) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        order = orderService.triageOrder(user, order, vm);
        return new OrderVM(order);
    }

    @GetMapping("/orders/list")
    @Timed
    @ApiOperation("医生查询自己的问诊订单")
    @ActionLog(action = "医生查询自己的问诊订单")
    public IhPage<OrderVM> findOrderList(
        @ApiParam(value = "参数订单类型,为null查询所有  OUT(复诊,咨询,线上门诊咨询预约)  EMERGENCY(急诊) 接诊类型:receptionType (待接诊:wait) "
            + "(接诊中:reception) (候诊中:waiting) (计划中:plan) 待接诊排序(emergencyLevel, createdDate) 接诊中排序(emergencyLevel, registeredDate) 计划中排序(startTime-asc)"
            + "同时支持 onlineType:ONLINE+ORDERSTATUSCONTAINS:COMPLETED TIME_OUT_COMPLETED+ORDERTYPECONTAINS:OUT CONSULT"
            + "查询条件：开始日期(create_time:>=2022-11-17)、结束日期(create_time:<=2022-11-17)、服务类型（visit_type:VIDEO）、"
            + "患者姓名（patient_name:赵）、就诊卡号（patient_card:123）、订单ID(id:123)、"
            + "状态（order_status_contains:\"TERMINATED COMPLETED\"）根据orderStatus的枚举和需求上需要的传入就行可多传）、"
            + "问诊类别（order_type_contains:CONSULT）"
            + " query=orderType:OUT receptionType:wait sort:\"emergencyLevel-asc createdDate-asc\" ")
        @RequestParam(name = "query", required = false) String query,
        @ApiParam(value = "查询页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNum,
        @ApiParam(value = "每页条数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer pageSize) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return new IhPage<>(orderService.findOrderList(user, query, hospital, pageNum, pageSize));
    }

    @GetMapping("/orders")
    @Timed
    @ApiOperation("查询医院所有的就诊订单")
    @ActionLog(action = "查询医院所有的就诊订单")
    public IhPage<ListOrderVM> findOrderListByHospital(
        @ApiParam(value = "{参数订单类型: \n"
            + "订单分类: onlineType - [(ONLINE: 在线订单)|(OFFLINE: 线下订单)]\n"
            + "问诊类型：visitType:VIDEO（视频问诊）| GRAPHIC（图文问诊）"
            + "订单类型: orderType - [(OUT: 门诊)|(EMERGENCY: 急诊)|(IN: 住院)|(CONSULT: 咨询)]\n"
            + "\n订单状态: status - [(UNKNOWN:默认状态，无业务意义) | (DRAFT:草稿) | (SUBMITTED:待分配) | (TRIAGING:分诊中) | "
            + "(TRIAGING_CANCELLED:分诊中时取消) | (TRIAGED:已分诊) | (PENDING:待付款) | (PENDING_CANCELLED:支付时取消) | "
            + "(REGISTERED:已挂号) | (ONTIME_CONFIRMED:候诊中(到时间)) | (NOTONTIME_CONFIRMED:候诊中(没到时间:不是一个状态)) | "
            + "(REFUNDING:退款中) | (REFUNDED:已退款) | (STARTED:进行中) | (TERMINATED:已终止) | (COMPLETED:已完成) | "
            + "(CLOSED;已关闭)]\n"
            + "\n模糊查询: [患者姓名/患者手机号/医生姓名: userInfo]\n"
            + "\n模糊查询: [订单编号: id]\n"
            + "范围查询: [接诊时间: admissionDate]\n"
            + "范围查询: [下单时间: registeredDate]\n"
            + "排序: [接诊时间: admissionDate] | [订单创建时间: createDate]\n"
            + "带\"\"值的查询条件表示支持多值查询,orderStatusContains:"
            + "\"ONTIME_CONFIRMED STARTED COMPLETED REFUNDING REFUNDED...\"\n"
            + "示例: query=sort:\"createdDate-asc registeredDate-desc\"+onlineType:ONLINE+orderType:OUT+status:COMPLETED+userInfo:郑+doctor_name:郑+admissionDate:<=2022-11-17+admissionDate:>=2021-11-17}"
        )
        @RequestParam(name = "query", required = false) String query,
        @ApiParam(value = "查询页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNum,
        @ApiParam(value = "每页条数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer pageSize) {
        Page<ListOrderVM> orderVMPage = orderService.findOrderListByHospital(query, pageNum, pageSize,
                CurrentHospital.getOrThrow()).map(order -> {
            ListOrderVM listOrderVM = new ListOrderVM(order);
            orderNursingExtRepository.findOneByBaseOrder(order).ifPresent(orderNursingExt
                            -> listOrderVM.setOrderNursingExt(new OrderNursingExtDTO(orderNursingExt)));
                    return listOrderVM;
        });
        List<Long> orderIds = orderVMPage.getContent().stream().map(ListOrderVM::getId).collect(Collectors.toList());
        List<MedicalCase> medicalCases = medicalCaseRepository.findAllByOrderIdIn(orderIds);
        Map<Long, MedicalCase> medicalCaseMap = medicalCases.stream().collect(Collectors.toMap(k -> k.getOrder().getId(), v -> v));
        orderVMPage.stream().forEach(o -> {
            MedicalCase medicalCase = medicalCaseMap.get(o.getId());
            if (medicalCase != null) {
                o.setMedicalCase(new MedicalCaseVM(medicalCase));
            }
            if (null != o.getService()) {
                long time = new Date().getTime() - o.getService().getStartTime().getTime();
                o.setWaitTime((int) (time > 0 ? time / 1000 : 0));
            }
        });
        return new IhPage<>(orderVMPage);
    }

    @PutMapping("/orders/{orderId}/end")
    @ApiOperation("医生退诊")
    @ActionLog(action = "医生退诊")
    public void stopOrder(@PathVariable("orderId") Long id, @Valid @RequestBody EndOrderVM vm) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(id);
        Hospital hospital = CurrentHospital.getOrThrow();
        currentHospitalValid(order.getHospital());
        if (StringUtils.isEmpty(vm.getReason())) {
            vm.setReason("医生[" + user.getFullName() + "]退诊");
        }
        MedicalWorker medicalWorker = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user).orElse(null);
        orderService.endOrder(user, medicalWorker, order, vm, vm.getReason());
    }

    @GetMapping("/orders/uploadDataG")
    public void getUploadData(
        @RequestParam(name = "type") Integer type,
        @RequestParam(name = "page", required = false) Integer page,
        @RequestParam(name = "size", required = false) Integer size
    ) {
        Hospital hospital = CurrentHospital.getOrThrow();
        SuperviseDto dto = SuperviseUtil.getDto(hospital);
        SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
        if (superviseService != null){
            superviseService.selectUploadData(hospital, type, page, size, dto);
        }
    }

    /**
     * 监管上报
     * @param
     */
    @PutMapping("/orders/{step}/uploadData")
    public void uploadData(@RequestBody Map<String, String> maps, @PathVariable Integer step) {
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<Order>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.isNotNull("settleDate"));
        specs.add(Specifications.between("createdDate", TimeUtils.convert(maps.get("start")),
                                         TimeUtils.convert(maps.get("end"))));
//        specs.add(Specifications.isNotNull("settleDate"));
        List<Order> orders = orderRepository.findAll(Specifications.and(specs), Sort.by(Direction.ASC, "id"));
        SuperviseDto dto = SuperviseUtil.getDto(hospital);
        SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
        if (superviseService != null){
            if (step == 1) {
                for (Order order : orders) {
                    // 重传预约信息
                    superviseService.appointmentInfoUpload(hospital, order.getId(), dto);
                }
            }
            if (step == 2) {
                // 筛选出状态为结束后医生退款的 传status = 3
                orders.stream().filter(u -> u.getStatus() == OrderStatus.COMPLETED_REFUNDED).forEach(u -> {
                    superviseService.createVisitRecord(hospital, u.getId(), dto);
                });
            }
            if (step == 3) {
                // 筛选出状态为未接诊退款的 传status = 4
                orders.stream().filter(u -> u.getStatus() == OrderStatus.ONTIME_CONFIRMED_DOCTOR_REFUNDED
                    || u.getStatus() == OrderStatus.ONTIME_CONFIRMED_REFUNDED).forEach(u -> {
                    superviseService.rejectRegistration(hospital, u.getId(), dto);
                });
            }
        }
    }

    /**
     * 监管上报
     * @param
     */
    @PutMapping("/orders/{step}/uploadDataP")
    public void uploadCF(@RequestBody Map<String, String> maps, @PathVariable Integer step) {
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<Order>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(Specifications.isNotNull("settleDate"));
        specs.add(Specifications.between("createdDate", TimeUtils.convert(maps.get("start")),
                                         TimeUtils.convert(maps.get("end"))));
        List<Order> orders = orderRepository.findAll(Specifications.and(specs), Sort.by(Direction.ASC, "id"));
        SuperviseDto dto = SuperviseUtil.getDto(hospital);
        SuperviseService superviseService = SuperviseFactory.getSuperviseService(dto.getSuperviseEnum());
        PrescriptionOrderCaRepository orderCaRepository = AppContext.getInstance((PrescriptionOrderCaRepository.class));
        AtomicInteger i = new AtomicInteger();
        if (superviseService != null){
//            if (step == 1) {
//                for (Order order : orders) {
//                    // 重传预约信息
//                    superviseService.appointmentInfoUpload(hospital, order.getId(), dto);
//                }
//            }
            if (step == 2) {
                // 筛选出状态为结束后医生退款的 传status = 3
                orders.stream().filter(u -> u.getStatus() == OrderStatus.COMPLETED_REFUNDED).forEach(u -> {
                    List<PrescriptionOrder> prescriptionOrders = prescriptionOrderRepository.findByOrderIdAndEnabled(
                        u.getId(), true);
                    for (PrescriptionOrder prescriptionOrder : prescriptionOrders) {
                        Optional<PrescriptionOrderCa> orderCaOptional =
                            orderCaRepository.findOneByPrescriptionOrderIdAndEnabled(
                            prescriptionOrder.getId(), true);
                        if (orderCaOptional.isPresent()) {
                            i.getAndIncrement();
                            superviseService.auditPrescription(hospital, prescriptionOrder.getId(), dto, true,
                                                               orderCaOptional.get());
                        }
                    }

                });
            }
            log.info("共上传了{}个处方", i.get());
//            if (step == 3) {
//                // 筛选出状态为未接诊退款的 传status = 4
//                orders.stream().filter(u -> u.getStatus() == OrderStatus.ONTIME_CONFIRMED_DOCTOR_REFUNDED
//                    || u.getStatus() == OrderStatus.ONTIME_CONFIRMED_REFUNDED).forEach(u -> {
//                    superviseService.createVisitRecord(hospital, u.getId(), dto);
//                });
//            }
        }
    }

    @GetMapping("/orders/reception/total")
    @Timed
    @ApiOperation("接诊队列/ 总数量")
    @ActionLog(action = "接诊队列/ 总数量")
    public long getOrderReceptionTotal(
        @ApiParam(value = "参数订单类型,为null查询所有, OUT(复诊,咨询,线上门诊咨询预约)  EMERGENCY(急诊) 接诊类型:receptionType (待接诊:wait) "
            + "(接诊中:reception) (候诊中:waiting) 待接诊排序(emergencyLevel, createdDate) 接诊中(emergencyLevel, registeredDate) "
            + " query=orderType:OUT receptionType:wait sort:\"emergencyLevel-asc createdDate-asc\" ")
        @RequestParam(name = "query", required = false) String query) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return orderService.getOrderReceptionTotal(user, query, hospital);
    }

    @GetMapping("/doctors/{medicalWorkerId}/orders/opened")
    @ApiOperation("医生的受影响清单,包括已挂号,等待中已开始,和退款中的")
    @ActionLog(action = "医生的受影响清单")
    public IhPage<OrderVM> findDoctorsOpenedOrders(
        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @PathVariable long medicalWorkerId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        MedicalWorker medicalWorker = medicalWorkerRepository.getById(medicalWorkerId);
        if (!Objects.equals(hospital, medicalWorker.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        List<Specification<Order>> sp = Lists.newArrayList();
        sp.add(new OrderRegisteredNotEndFilter().toSpecification());
        sp.add(Specifications.eq("doctor", medicalWorker));
        Pageable request = PageRequest.of(pageNo, size, Direction.ASC, "registeredDate");
        sp.add(Specifications.eq("hospital", hospital));
        return new IhPage<>(orderRepository.findAll(Specifications.and(sp), request)
            .map(OrderVM::new));
    }

    @GetMapping("/orders/reception_history")
    @Timed
    @ApiOperation("接诊队列/ 接诊历史 急诊/复诊")
    @ActionLog(action = "查询接诊队列")
    public IhPage<OrderVM> findOrderReceptionHistoryList(
        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @ApiParam(value =
            "查询格式 (EMERGENCY(急诊) OUT(复诊,咨询)必填) 状态说明(COMPLETED=已完成,TERMINATED=已终止,REFUNDING=已退款，REFUNDED=退款中), rating 评分 "
                + "query = orderType:OUT admissionDate:>=2020-08-11 admissionDate:<=2020-08-15 userName:张三 status:COMPLETED diseaseName:糖尿病 level:ONE rating:1")
        @RequestParam(name = "query", required = false) String query) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable request = PageRequest.of(pageNo, size, Direction.DESC, "admissionDate");
        return new IhPage<>(orderService.findAllOrderReceptionHistory(user, query, request, hospital).map(u -> {
            u.setWorkers(u.getWorkers().stream()
                             .filter(t -> Objects.equals(u.getDoctor().getUser(), t.getUser()))
                             .collect(Collectors.toList()));
            return new OrderVM(u);
        }));
    }

    @PutMapping("/orders/{orderId}/rush_order")
    @ApiOperation("医生抢单")
    @ActionLog(action = "医生抢单")
    public OrderVM grabAnOrder(@PathVariable(name = "orderId") long orderId) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        if (null != order.getUser()) {
            if (order.getUser().getId().longValue() == user.getId().longValue()) {
                throw ErrorType.CAN_NOT_TALK_TO_YOURSELF.toProblem();
            }
        }
        currentHospitalValid(order.getHospital());
        if (!hospitalService.userInHospital(user, order.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!Objects.equals(order.getStatus(), OrderStatus.REGISTERED)) {
            throw ErrorType.GRAB_ORDER_TIP.toProblem();
        }
        return new OrderVM(orderService.grabAnOrder(user, order));
    }

    @PutMapping("/orders/{orderId}/confirmed")
    @ApiOperation("医生接待候诊中的用户-接诊")
    @ActionLog(action = "医生接待候诊中的用户-接诊")
    public OrderVM waitingOrder(@PathVariable(name = "orderId") long orderId) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);

        currentHospitalValid(order.getHospital());
        if (!Objects.equals(order.getDoctor().getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (Objects.equals(order.getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem("医生不能接自己诊");
        }
        if (order.getStatus() != OrderStatus.ONTIME_CONFIRMED) {
            throw ErrorType.GRAB_ORDER_TIP.toProblem();
        }

        if (order.getService() != null && !HospitalSettingsHelper.getBoolean(order.getHospital(),
                                                                             HospitalSettingKey.APPOINTMENT_OUT_DAY)
            && !TimeUtils.isInDays(order.getService().getStartTime(), new Date())) {
            throw ErrorType.TIME_IS_OUT.toProblem();
        }

        return orderService.waitingOrder(user, order);
    }

    @PutMapping("/orders/{orderId}/complete_consultation")
    @ApiOperation("医生结束会诊")
    @ActionLog(action = "医生结束会诊")
    public OrderVM completeConsultation(@PathVariable(name = "orderId") long orderId,
                                        @RequestBody CompleteOrderVM completeOrderVM) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        List<PrescriptionOrder> preOrderList = prescriptionOrderRepository.findByOrderIdAndEnabled(orderId, true);
        MedicalCase medicalCase = medicalCaseRepository.findByOrderId(orderId).orElse(null);
        if (!Objects.equals(order.getStatus(), OrderStatus.STARTED)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem();
        } else if (order.getOrderType() != ClinicType.CONSULT) {
            if (medicalCase == null || medicalCase.getDiseases().isEmpty() || StringUtils.isEmpty(medicalCase.getSummary())) {
                throw ErrorType.MEDICAL_CASE_BLANK.toProblem("请填写病历，不能为空");
            }

            if (!preOrderList.isEmpty()) {
                preOrderList.forEach(preOrder -> {
                    if (preOrder.getStatus().equals(PrescriptionOrder.Status.UNSENT)) {
                        throw ErrorType.MEDICAL_PRESCRIPTION_SENDUSER.toProblem("处方未发送给患者，不能结束会诊");
                    } else if (!preOrder.getStatus().equals(PrescriptionOrder.Status.SENT) && !preOrder
                        .getStatus().equals(Status.USED)) {
                        throw ErrorType.MEDICAL_PRESCRIPTION_STATUS.toProblem("处方未审核通过，不能结束会诊");
                    }
                });
            } else if (!medicalCase.isSendUser()) {
                throw ErrorType.MEDICAL_CASE_NOT_SEND_USER.toProblem("病历未发送给患者");
            }
        }
        completeOrderVM.setReason("医生会诊结束操作");
        return new OrderVM(orderService.completeConsultation(user, order, getMedicalHtml(orderId),
                "医生会诊结束操作", completeOrderVM));
    }

    @PostMapping("/orders/{orderId}/medical_record")
    @ApiOperation("增加/修改用户病历")
    @ActionLog(action = "增加/修改用户病历")
    public MedicalCaseVM addMedicalCase(@Valid @RequestBody MedicalCaseVM medicalCaseVM,
                                        @PathVariable(name = "orderId") long orderId) {
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        return new MedicalCaseVM(prescriptionService.addOrUpdateMedicalCase(medicalCaseVM, orderId));
    }

    @PostMapping("/orders/{orderId}/checks")
    @ApiOperation("增加/修改用户检查单")
    @ActionLog(action = "增加/修改用户检查单")
    public List<ChecksVM> addDoctorTellCheck(
        @Valid @RequestBody List<CreateChecksVM> doctorTellList,
        @PathVariable(name = "orderId") long orderId) {
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        User user = CurrentUser.getOrThrow();
        return prescriptionService.addDoctorTellCheck(doctorTellList, user, orderId);
    }

    @DeleteMapping("/checks/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("检查单删除")
    @ActionLog(action = "检查单删除")
    public void delDoctorCheck(@PathVariable(name = "id") long id) {
        Checks checks = checkRepository.getById(id);
        currentHospitalValid(checks.getHospital());
        try {
            prescriptionService.delDoctorCheck(id);
        } catch (Exception e) {
            log.error("该检查单已预约，不能删除", e);
            throw ErrorType.CHECKS_NOT_DELETE.toProblem();
        }
    }

    @GetMapping("/orders/{orderId}/checks")
    @ApiOperation("检查单详情")
    @ActionLog(action = "查询检查单详情")
    public List<ChecksVM> doctorCheckDetails(@PathVariable(name = "orderId") long orderId) {
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        List<Checks> doctorTellList = checkRepository.findByOrderId(orderId);
        return doctorTellList.stream().map(ChecksVM::new).collect(Collectors.toList());
    }

    @PostMapping("/orders/{orderId}/inspections")
    @ApiOperation("增加/修改用户检验单")
    @ActionLog(action = "增加/修改用户检验单")
    public List<InspectionDTO> addInspections(
        @Valid @RequestBody List<InspectionDTO> inspectionDTOList,
        @PathVariable(name = "orderId") long ordersId) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(ordersId);
        currentHospitalValid(order.getHospital());
        return prescriptionService.addInspections(inspectionDTOList, ordersId);
    }

    @DeleteMapping("/inspections/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("检验单删除")
    @ActionLog(action = "检验单删除")
    public void delInspections(@PathVariable(name = "id") long id) {
        prescriptionService.delInspections(id);
    }

    @GetMapping("/orders/{orderId}/inspections")
    @ApiOperation("检验单详情")
    @ActionLog(action = "查询检验单详情")
    public List<InspectionDTO> inspectionsDetails(@PathVariable(name = "orderId") long ordersId) {
        List<Inspection> inspectionList = inspectionRepository.findByOrderId(ordersId);
        return inspectionList.stream().map(InspectionDTO::new).collect(Collectors.toList());
    }


    @PostMapping("/orders/{orderId}/prescription_orders")
    @ApiOperation("增加用户处方单")
    @ActionLog(action = "增加用户处方单")
    public PrescriptionOrderVM addDoctorPrescriptionOrder(
        @Valid @RequestBody PrescriptionOrderVM prescriptionOrderDTO,
        @PathVariable(name = "orderId") long ordersId) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = orderRepository.getById(ordersId);
        currentHospitalValid(order.getHospital());
        MedicalWorker doctor = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user)
            .orElseThrow(ErrorType.FORBIDDEN::toProblem);
        List<PrescriptionOrder> list = prescriptionOrderRepository.findByOrderIdAndEnabled(ordersId, true);
        PrescriptionOrder prescriptionOrder;
        if (CollectionUtils.isNotEmpty(list)) {
            prescriptionOrder = prescriptionService
                    .updateDoctorPrescriptionOrder(user, doctor, hospital, prescriptionOrderDTO, ordersId, list.get(0).getId());
        } else {
            prescriptionOrder = prescriptionService
                    .addDoctorPrescriptionOrder(user, doctor, hospital, prescriptionOrderDTO, ordersId);
        }
        return new PrescriptionOrderVM(prescriptionOrder);
    }


    @PutMapping("/orders/{orderId}/prescription_orders/{id}")
    @ApiOperation("修改用户处方单")
    @ActionLog(action = "修改用户处方单")
    public PrescriptionOrderVM updateDoctorPrescriptionOrder(
        @Valid @RequestBody PrescriptionOrderVM prescriptionOrderVM,
        @PathVariable(name = "orderId") long ordersId, @PathVariable(name = "id") long id) {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = orderRepository.getById(ordersId);
        currentHospitalValid(order.getHospital());
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(id);
        currentHospitalValid(prescriptionOrder.getHospital());
        MedicalWorker doctor = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user)
            .orElseThrow(ErrorType.FORBIDDEN::toProblem);
        prescriptionOrder = prescriptionService
            .updateDoctorPrescriptionOrder(user, doctor, hospital, prescriptionOrderVM, ordersId, id);
        prescriptionOrder = prescriptionOrderRepository.getById(prescriptionOrder.getId());
        return new PrescriptionOrderVM(prescriptionOrder);
    }

    @DeleteMapping("/prescription_orders/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("处方单删除")
    @ActionLog(action = "处方单删除")
    public void delDoctorPrescriptionOrder(@PathVariable(name = "id") long id) {
        User user = CurrentUser.getOrThrow();
        PrescriptionOrder prescriptionOrder = prescriptionOrderRepository.getById(id);
        currentHospitalValid(prescriptionOrder.getHospital());
        prescriptionService.delDoctorPrescriptionOrder(user, id);
    }

    @DeleteMapping("/prescription_drugs/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("删除处方单里面的药品列")
    @ActionLog(action = "删除处方单里面的药品列")
    public void delDoctorPrescription(@PathVariable(name = "id") long id) {
        User user = CurrentUser.getOrThrow();
        Prescription result = prescriptionRepository.getById(id);
        currentHospitalValid(result.getOrder().getHospital());
        prescriptionService.delDoctorPrescription(user, id);
    }

    @GetMapping("/orders/{orderId}/prescriptions")
    @ApiOperation("处方单详情")
    @ActionLog(action = "处方单详情")
    public List<PrescriptionOrderVM> doctorPrescriptionDetails(
        @PathVariable(name = "orderId") long ordersId) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(ordersId);
        currentHospitalValid(order.getHospital());
        return orderService.getDoctorPrescriptionOrderDetails(user, ordersId);
    }

    @PutMapping("/orders/{orderId}/prescription_orders/confirm")
    @ApiOperation("医生提交处方单，在提交审核之前")
    @ActionLog(action = "医生提交处方单，在提交审核之前")
    public void doctorConfirmReview(@PathVariable(name = "orderId") long orderId,
                                   @Valid @RequestBody List<PrescriptionOrderVM> prescriptionOrderVMS) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        shouldAccessPharmacistInfo(user);
        prescriptionService.doctorConfirmPrescription(prescriptionOrderVMS, user, order.getHospital(), orderId);
    }

    @PutMapping("/orders/{orderId}/prescription_orders/commit")
    @ApiOperation("医生提交审核处方单")
    @ActionLog(action = "医生提交审核处方单")
    public void doctorCommitReview(@PathVariable(name = "orderId") long orderId,
                                   @Valid @RequestBody List<PrescriptionOrderVM> prescriptionOrderVMS) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        shouldAccessPharmacistInfo(user);
        prescriptionService.doctorCommitReview(prescriptionOrderVMS, user, order.getHospital(), orderId);
    }

//    @PutMapping("/orders/{orderId}/prescription_orders/manual_send")
//    @ApiOperation("医生手动提交发送处方给用户")
//    @ActionLog(action = "医生手动提交发送处方给用户")
//    public void doctorCommitManualSend(@PathVariable(name = "orderId") long orderId,
//                                       @Valid @RequestBody List<PrescriptionOrderVM> prescriptionOrderVMS) {
//        User user = CurrentUser.getOrThrow();
//        shouldAccessPharmacistInfo(user);
//        Order order = orderRepository.getById(orderId);
//        currentHospitalValid(order.getHospital());
//        prescriptionService.doctorCommitManualSend(prescriptionOrderVMS, user, orderId);
//    }

    @PutMapping("/orders/{orderId}/prescription_orders/{id}/reject")
    @ApiOperation("药师审核处方未通过")
    @ActionLog(action = "药师审核处方未通过")
    public PrescriptionOrderVM doctorReviewReject(@PathVariable(name = "orderId") long orderId,
                                                  @PathVariable(name = "id") long id,
                                                  @Valid @RequestBody PrescriptionOrder4RejectVM prescriptionOrderVM) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        shouldAccessPharmacistInfo(user);
        return prescriptionService.doctorReviewReject(user, orderId, id, prescriptionOrderVM);
    }

    @PutMapping("/orders/{orderId}/prescription_orders")
    @ApiOperation("药师审核处方通过")
    @ActionLog(action = "药师审核处方通过")
    public List<PrescriptionOrderVM> doctorReview(@PathVariable(name = "orderId") long orderId,
                                                  @Valid @RequestBody List<PrescriptionOrderVM> prescriptionOrderVMS) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        //回调方法 Function
        List<PrescriptionOrder> prescriptionOrders = prescriptionService
            .doctorReviewPrescription(user, orderId, prescriptionOrderVMS);
        return prescriptionOrders.stream().map(PrescriptionOrderVM::new)
            .collect(Collectors.toList());
    }

//    @ApiOperation("药师自动审核处方通过")
//    @ActionLog(action = "药师自动审核处方通过")
//    public void doctorReviewAuto(long orderId, long prescriptionId) {
//        List<PrescriptionOrderVM> list = Lists.newArrayList();
//        PrescriptionOrderVM vm = new PrescriptionOrderVM();
//        vm.setId(prescriptionId);
//        list.add(vm);
//        Order order = orderRepository.getById(orderId);
//        String userName = HospitalSettingsHelper.getString(order.getHospital(), HospitalSettingKey.PHARMACIST_USERNAME);
//        Optional<User> user = userCacheFindService.findOneByUsername(userName);
//        //回调方法 Function
//        user.ifPresent(value -> prescriptionService.doctorReviewPrescription(value, orderId, list, this::drawImage));
//    }

    // 病历正文生成HTML
    //  @GetMapping("/medicalcasehtml/test")
    public String getMedicalHtml(long orderId) {
        MedicalCase medicalcase = medicalCaseRepository.findByOrderId(orderId).orElse(new MedicalCase());
        Map<String, Object> data = Maps.newHashMap();
        data.put("startTime", medicalcase.getStartTime());
        data.put("selfSpeak", medicalcase.getSelfSpeak());
        data.put("nowMedicalHistory", medicalcase.getNowMedicalHistory());
        data.put("oldMedicalHistory", medicalcase.getOldMedicalHistory());
        data.put("allergiesHistory", medicalcase.getAllergiesHistory());
        data.put("checking", medicalcase.getChecking());
        data.put("diagnosis", medicalcase.getDiagnosis());
        Context context = new Context();
        context.setVariables(data);
        return AppContext.getInstance(TemplateEngine.class).process("medicalcase.html", context);
    }

    @GetMapping("/order_history/{orderId}")
    @ApiOperation("查看生成病历记录详情,归档记录")
    @ActionLog(action = "查看生成病历记录详情")
    public OrderHistoryRecordDTO orderHistoryRecordDetails(
        @PathVariable(name = "orderId") long orderId) {
        User user = CurrentUser.getOrThrow();
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        return orderService.orderHistoryRecordDetail(user, orderId);
    }

    @GetMapping("/order_history/prescription_orders")
    @ApiOperation("药师查看需要审核列表")
    @ActionLog(action = "药师查看需要审核列表")
    public List<PrescriptionOrderVM> listPharmacist() {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        shouldAccessPharmacistInfo(user);
        return orderService.findAllPrescriptionOrder(user, hospital).stream().map(PrescriptionOrderVM::new)
            .peek(p -> {
                MedicalCase medicalCase = medicalCaseRepository
                    .findByOrderId(p.getOrder().getId()).orElse(null);
                p
                    .setDiagnosis(null != medicalCase ? medicalCase.getDiagnosis() : (""));
                if (null != medicalCase && !medicalCase.getDiseases().isEmpty()) {
                    p.setDiseases(
                        medicalCase.getDiseases().stream().map(MedicalCaseDiseaseDTO::new)
                            .collect(Collectors.toList()));
                }
            }).sorted(Comparator.comparing(PrescriptionOrderVM::getCommitedDate, Comparator.naturalOrder()))
            .collect(Collectors.toList());
    }

    @GetMapping("/order_history/prescription_orders/review")
    @ApiOperation("药师查询，今日已审核列表")
    @ActionLog(action = "药师查询，今日已审核列表")
    public List<PrescriptionOrderVM> listTodayPharmacist() {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();

        shouldAccessPharmacistInfo(user);
        return orderService.listTodayPharmacist(user, hospital).stream()
            .filter(prescriptionOrderDTO -> null != prescriptionOrderDTO.getReviewTime())
            .sorted(Comparator.comparing(PrescriptionOrderVM::getReviewTime).reversed())
            .collect(Collectors.toList());
    }

    @GetMapping("/order_history/prescription_orders/total")
    @ApiOperation("处方审核 总数量")
    @ActionLog(action = "查询处方审核 总数量")
    public long prescriptionOrderTotal() {
        User user = CurrentUser.getOrThrow();
        shouldAccessPharmacistInfo(user);
        long dayReview = listTodayPharmacist().size();
        long notReview = listPharmacist().size();
        return dayReview + notReview;
    }

    @GetMapping("/orders/{orderId}/prescription_orders/total")
    @ApiOperation("本订单处方总数量")
    @ActionLog(action = "查询本订单处方总数量")
    public long getPrescriptionOrderTotal(@PathVariable(name = "orderId") long orderId) {
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        return prescriptionService.getPrescriptionOrderTotal(orderId);
    }

    @GetMapping("/orders/{orderId}/prescription_orders/revien/count")
    @ApiOperation("本订单处方已审核数量")
    @ActionLog(action = "查询本订单处方已审核数量")
    public long getPrescriptionOrderRevienCount(@PathVariable(name = "orderId") long orderId) {
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        return prescriptionService.getPrescriptionOrderRevienCount(orderId);
    }

    @GetMapping("/orders/{orderId}/prescription_orders/sent/count")
    @ApiOperation("本订单处方已发送给用户的数量")
    @ActionLog(action = "本订单处方已发送给用户的数量")
    public long getPrescriptionOrderSentCount(@PathVariable(name = "orderId") long orderId) {
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        return prescriptionService.getPrescriptionOrderSentCount(orderId);
    }

    @PutMapping("/orders/{orderId}/medical_case/sent")
    @ApiOperation("本订单病历是否发送给用户")
    @ActionLog(action = "本订单病历是否发送给用户")
    public void updateMedicalCaseSent(@PathVariable(name = "orderId") long orderId) {
        User user = CurrentUser.getOrThrow();
        shouldAccessPharmacistInfo(user);
        Order order = orderRepository.getById(orderId);
        currentHospitalValid(order.getHospital());
        prescriptionService.updateMedicalCaseSent(orderId);
    }

    @GetMapping("/prescription_order_history/review")
    @ApiOperation("药师查询，已审核历史列表")
    @ActionLog(action = "药师查询，已审核历史列表")
    public IhPage<PrescriptionOrderVM> listPharmacistHistory(
        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @ApiParam(value = "查询格式 状态说明(PASS=(未发送,已发送) REFUSE=已拒绝) "
            + "status = PASS") @RequestParam(name = "status", required = false) String status) {
        User user = CurrentUser.getOrThrow();
        shouldAccessPharmacistInfo(user);
        Pageable request = PageRequest.of(pageNo, size, Direction.DESC, "createdDate");
        String statusParam = "";
        if (StringUtils.isNotBlank(status)) {
            statusParam = status.trim();
        }
        Hospital hospital = CurrentHospital.getOrThrow();

        return new IhPage<>(prescriptionService.listPharmacistHistory(user, request, statusParam, hospital)
            .map(u -> {
                PrescriptionOrderVM prescriptionOrderVM = new PrescriptionOrderVM(u);
                PrescriptionOrder po = prescriptionOrderRepository.getById(u.getId());
                if (po.getOrder().getDept() != null) {
                    prescriptionOrderVM.setDept(new DeptVM(po.getOrder().getDept()));
                }
                if (null != po.getUpload()) {
                    prescriptionOrderVM.setUpload(new UploadVM(po.getUpload()));
                }
                return prescriptionOrderVM;
            }));
    }

    @GetMapping("/prescription_order_history/operations/{operationsId}")
    @ApiOperation("药师查看审核历史记录的处方详情,操作日志记录的详情")
    @ActionLog(action = "药师查看审核历史记录的处方详情")
    public PrescriptionOrderVM getPrescriptionOrderHistoryDetails(
        @PathVariable(name = "operationsId") long operationsId) {
        User user = CurrentUser.getOrThrow();
        shouldAccessPharmacistInfo(user);
        PrescriptionOrderOperation operation = prescriptionOperationRepository.getById(operationsId);
        currentHospitalValid(operation.getHospital());
        PrescriptionOrderDTO prescriptionOrderDTO = prescriptionService
            .getPrescriptionOrderHistoryDetails(operation);
        if (null != prescriptionOrderDTO) {
            PrescriptionOrderVM prescriptionOrderVM = new PrescriptionOrderVM(prescriptionOrderDTO);
            Upload upload = operation.getPrescriptionOrder().getUpload();
            if (null != upload) {
                prescriptionOrderVM.setUpload(new UploadVM(upload));
            }
            return prescriptionOrderVM;
        }
        return new PrescriptionOrderVM();
    }

    @GetMapping("/patients/{patientId}/checks")
    @ApiOperation("获取就诊人待预约检查项目(医生)")
    @ActionLog(action = "获取就诊人待预约检查项目")
    public List<ChecksVM> getCheckReservation(@PathVariable(name = "patientId") long patientId) {
        Patient patient = patientRepository.getById(patientId);
        Hospital hospital = CurrentHospital.getOrThrow();
        return AppContext.getInstance(UserCheckService.class).findReservationChecks(patient, hospital).stream()
            .map(u -> new ChecksVM(u, false))
            .collect(Collectors.toList());
    }

    @GetMapping("/prescription_order_history/count")
    @ApiOperation("药师查看审核历史记录所有总数")
    @ActionLog(action = "药师查看审核历史记录所有总数")
    public long getPrescriptionOrderHistoryCount() {
        User user = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        shouldAccessPharmacistInfo(user);
        return prescriptionService.getPrescriptionOrderHistoryCount(user, hospital);
    }
    
    @GetMapping("/orders/{id}/detail")
    @ApiOperation("根据id查订单详情")
    @ActionLog(action = "查询订单详情")
    public OrderVM getDetailOrder(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = orderRepository.getById(id);
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem();
        }
        int estimateWaitTime = orderService.getEstimateWaitTime(order);
        OrderVM orderVM = new OrderVM(order, estimateWaitTime);
        orderNursingExtRepository.findOneByBaseOrder(order)
                .ifPresent(orderNursingExt -> orderVM.setOrderNursingExt(new OrderNursingExtDTO(orderNursingExt)));
        return orderVM;
    }

    @GetMapping("/prescription_order/{id}/orders/detail")
    @ApiOperation("通过处方id查订单详情")
    @ActionLog(action = "通过处方id查订单详情")
    public OrderVM getDetailOrderByPrescriptionOrderId(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Order order = prescriptionOrderRepository.getById(id).getOrder();
        if (!Objects.equals(hospital, order.getHospital())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem();
        }
        int estimateWaitTime = orderService.getEstimateWaitTime(order);
        return new OrderVM(order, estimateWaitTime);
    }

    /**
     * 是否有病历权限
     *
     * @param preOrder
     * @param user
     */
    private void shouldAccessPrescriptionOrderInfo(Order preOrder, User user) {
        return;
//        if (Objects.equals(preOrder.getUser(), user)) {
//            return;
//        }
//        if (user.getAuthority().inHospital()) {
//            return;
//        }
//        throw ErrorType.FORBIDDEN.toProblem();
    }


    /**
     * 是否有药师权限
     *
     * @param user
     * @param user
     */
    private void shouldAccessPharmacistInfo(User user) {
        return;
//        if (user.getAuthority().inHospital()) {
//            return;
//        }
//        if (user.getAuthority().isPharmacist()) {
//            return;
//        }
//        throw ErrorType.FORBIDDEN.toProblem();
    }

    /**
     * 验证医院是否为当前医院
     *
     * @param hospital1
     */
    private void currentHospitalValid(Hospital hospital1) {
        Hospital hospital = CurrentHospital.getOrThrow();
        if (!Objects.equals(hospital, hospital1)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
    }

    @GetMapping("/orders/timmessage/list/{medicalWorkerId}")
    @ApiOperation("医生端-根据订单状态获取医生的订单列表含消息"
        + "(候诊列表：orderStatus:候诊中ONTIME_CONFIRMED)"
        + "(正在咨询列表：orderStatus:进行中STARTED)+"
        + "已完成列表：orderStatus:已完成COMPLETED+超时已完成TIME_OUT_COMPLETED+退款完成REFUNDED+医生接诊中退诊STARTED_REFUNDED+"
        + "咨询完成后医生发起退款COMPLETED_REFUNDED+医生候诊中退诊ONTIME_CONFIRMED_DOCTOR_REFUNDED+候诊中患者手工取消订单ONTIME_CONFIRMED_REFUNDED")
    @ActionLog(action = "医生端-咨询列表")
    public IhPage<OrderTIMMessageVM> orderList(
        @PathVariable(name = "medicalWorkerId") Long medicalWorkerId,
        @ApiParam("带\"\"值的查询条件表示支持多值查询,"
            + "orderStatusContains:\"ONTIME_CONFIRMED STARTED COMPLETED REFUNDING REFUNDED\""
            + "orderTypeContains:\"OUT EMERGENCY IN CONSULT UNKNOWN\""
            + "onlineTypeContains:\"ONLINE OFFLINE\""
            + "realtimeStatusContains:\"REAL_TIME APPOINTMENT\""
            + "visitTypeContains:\"VIDEO GRAPHIC\"") @RequestParam(name = "query", required
            = false) String query,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        MedicalWorker medicalWorker = medicalWorkerRepository.findById(medicalWorkerId)
            .orElseThrow(ErrorType.NOT_FOUND_ERROR::toProblem);
        Pageable pageable = PageRequest.of(pageNo, size, Direction.DESC, "createdDate");
        List<Specification<Order>> list = com.google.common.collect.Lists.newArrayList();
        //list.add(Specifications.eq("orderType", ClinicType.CONSULT));
        List<Specification<Order>> visitTypeSpecList = com.google.common.collect.Lists.newArrayList();
        //visitTypeSpecList.add(Specifications.eq("visitType", VisitType.GRAPHIC));
        //visitTypeSpecList.add(Specifications.eq("visitType", VisitType.VIDEO));
        list.add(Specifications.or(visitTypeSpecList));
        //list.add(Specifications.eq("onlineType", OnlineType.ONLINE));
        //list.add(Specifications.eq("realTimeStatus", RealTimeStatus.REAL_TIME));
        list.add(Specifications.eq("doctor", medicalWorker));
        list.add(Specifications.eq("hospital", hospital));
        list.add(HospitalOrderSearch.of(query).toSpecification());
        Page<OrderTIMMessageVM> page = orderRepository.findAll(Specifications.and(list), pageable)
            .map(elem -> new OrderTIMMessageVM(elem, user, false));
        List<OrderTIMMessageVM> sortList = page.getContent().stream()
            .sorted((o1, o2) -> {
                if (o1.getTimMessageDTO() == null || o2.getTimMessageDTO() == null) {
                    return o2.getOrder().getCreatedDate().compareTo(o1.getOrder().getCreatedDate());
                } else {
                    return Long.compare(o2.getTimMessageDTO().getTime().getTime(), o1.getTimMessageDTO().getTime().getTime());
                }
            })
            .collect(Collectors.toList());
        return new IhPage<>(new PageImpl<>(sortList, pageable, page.getTotalElements()));
    }

    @GetMapping("/orders/medical_workers/{id}/im/message/un_read_count")
    @ApiOperation("获取当前用户的指定订单聊天室未读消息数量 + 属于自己的候诊中订单数")
    @ActionLog(action = "获取当前用户的指定聊天室未读消息数量")
    public int getUnReadCount(
        @ApiParam("'-'表示排除 query=orderType:OUT visitType:VIDEO onlineType:ONLINE realTimeStatus:REAL_TIME"
            + "带\"\"值的查询条件表示支持多值查询"
            + " orderStatusContains:\"PENDING PENDING_CANCELLED REGISTERED ONTIME_CONFIRMED STARTED COMPLETED REFUNDED\""
            + " visitTypeContains:\"VIDEO GRAPHIC\"")
        @RequestParam(value = "query", required = false) String query,
        @PathVariable("id") long id) {
        User user = CurrentUser.getOrThrow();
        MedicalWorker doctor = medicalWorkerRepository.getById(id);
        currentHospitalValid(doctor.getHospital());
        List<Specification<Order>> list = com.google.common.collect.Lists.newArrayList();
        list.add(HospitalOrderSearch.of(query).toSpecification());
        list.add(Specifications.eq("doctor", doctor));
        int sum = timMessageService.getUnReadCount(list, user);
//        int sum = orderRepository.findAll(Specifications.and(list)).stream().
//            mapToInt(u -> timMessageService.getOrderMessageUnReadCount(u, user)).sum();

        List<Specification<Order>> list1 = com.google.common.collect.Lists.newArrayList();
        list1.add(Specifications.eq("orderType", ClinicType.CONSULT));
        List<Specification<Order>> visitTypeSpecList = com.google.common.collect.Lists.newArrayList();
        visitTypeSpecList.add(Specifications.eq("visitType", VisitType.GRAPHIC));
        visitTypeSpecList.add(Specifications.eq("visitType", VisitType.VIDEO));
        list1.add(Specifications.or(visitTypeSpecList));
        list1.add(Specifications.eq("onlineType", OnlineType.ONLINE));
        list1.add(Specifications.eq("realTimeStatus", RealTimeStatus.REAL_TIME));
        list1.add(Specifications.eq("doctor", doctor));
        list1.add(Specifications.eq("status", OrderStatus.ONTIME_CONFIRMED));
        int sum1 = orderRepository.findAll(Specifications.and(list1)).size();
        return sum + sum1;
    }

    @GetMapping("/orders/medical_workers/{id}/orders_count")
    @ApiOperation("获取当前用户的指定订单数")
    @ActionLog(action = "获取当前用户的指定订单数")
    public int get0rderCount(
        @ApiParam("'-'表示排除 query=orderType:OUT visitType:VIDEO onlineType:ONLINE realTimeStatus:REAL_TIME"
            + "带\"\"值的查询条件表示支持多值查询"
            + " orderStatusContains:\"PENDING PENDING_CANCELLED REGISTERED ONTIME_CONFIRMED STARTED COMPLETED REFUNDED\""
            + " visitTypeContains:\"VIDEO GRAPHIC\"")
        @RequestParam(value = "query", required = false) String query,
        @PathVariable("id") long id) {
        MedicalWorker doctor = medicalWorkerRepository.getById(id);
        currentHospitalValid(doctor.getHospital());
        List<Specification<Order>> list = com.google.common.collect.Lists.newArrayList();
        list.add(HospitalOrderSearch.of(query).toSpecification());
        list.add(Specifications.eq("doctor", doctor));
        return (int) orderRepository.findAll(Specifications.and(list)).stream().count();
    }

    @GetMapping("/doctorWorkbench/{medical_worker_id}")
    @ApiOperation("获取当前医生的线上问诊情况和处方情况")
    @ActionLog(action = "获取当前医生的线上问诊情况和处方情况")
    public DoctorWorkbench getDoctorWorkbench(@ApiParam("医生id") @PathVariable("medical_worker_id") long id) {
        MedicalWorker doctor = medicalWorkerRepository.getById(id);
        currentHospitalValid(doctor.getHospital());
        List<Specification<Order>> appointmentSpecs = Lists.newArrayList();
        appointmentSpecs.add(Specifications.eq("doctor", doctor));
        appointmentSpecs.add(Specifications.eq("realTimeStatus", RealTimeStatus.APPOINTMENT));
        Long appointmentCount = orderRepository.count(Specifications.and(appointmentSpecs));
//        Specification<Order> receivedSpec = (root, criteriaQuery, criteriaBuilder) -> {
//            Predicate docPre = criteriaBuilder.equal(root.get("doctor"), doctor);
//            Predicate statusPre = root.get("status").in(OrderStatus.getReceivedOrders());
//            return criteriaBuilder.and(docPre, statusPre);
//        };
//        Long receivedCount = orderRepository.count(receivedSpec);
        Specification<Order> rejectSpec = (root, criteriaQuery, criteriaBuilder) -> {
            Predicate docPre = criteriaBuilder.equal(root.get("doctor"), doctor);
            Predicate statusPre = root.get("status").in(OrderStatus.getRejectOrders());
            return criteriaBuilder.and(docPre, statusPre);
        };
        Long rejectCount = orderRepository.count(rejectSpec);
        Specification<PrescriptionOrder> doctorSpec = Specifications.eq("doctor", doctor);
        Long prescriptionCount = prescriptionOrderRepository.count(doctorSpec);
        Specification<PrescriptionOrder> reviewSpec = doctorSpec.and(Specifications.isNotNull("doctorReview"));
        Long reviewCount = prescriptionOrderRepository.count(reviewSpec);
        Specification<PrescriptionOrder> paidSpec = doctorSpec.and(Specifications.isTrue("paid"));
        Long paidCount = prescriptionOrderRepository.count(paidSpec);
        DoctorWorkbench doctorWorkbench = new DoctorWorkbench();
        doctorWorkbench.setAppointmentTotal(appointmentCount.intValue());
        medicalWorkerScoreRepository.findFirstByMedicalWorker(doctor).ifPresent(score ->
                doctorWorkbench.setReceivedTotal(score.getPatientTotalScore()));
        doctorWorkbench.setRejectTotal(rejectCount.intValue());
        doctorWorkbench.setPrescriptionTotal(prescriptionCount.intValue());
        doctorWorkbench.setReviewTotal(reviewCount.intValue());
        doctorWorkbench.setSentTotal(paidCount.intValue());
        return doctorWorkbench;
    }
}
