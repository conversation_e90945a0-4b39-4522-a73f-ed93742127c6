package cn.taihealth.ih.rest.controller.nodered;

import cn.taihealth.ih.service.vm.nodered.NodeRedResponse;
import cn.taihealth.ih.service.vm.nodered.NodeRedResponseData;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.ChargeItemInfo;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController()
@RequestMapping("/his/message")
@Api(tags = "his通过node-red调用的api")
public class HisMessageController {

    @PostMapping("/paitentInfo")
    public NodeRedResponse Q_GetDicDiagnose(@RequestBody JSONObject postBody, HttpServletRequest request)
        throws Exception {

        String hospcode = request.getHeader("hospcode");

        String requestId = postBody.getString("request_id");
        String msg_type = postBody.getString("msg_type");
        if ("3001".equals(msg_type)) {
            return new NodeRedResponse("200", "药品发货成功");
        }
        return new NodeRedResponse("0", msg_type + "暂未实现");
    }


    @PostMapping("/businessInfo")
    public NodeRedResponse chargeItem(@RequestBody JSONObject postBody, HttpServletRequest request) throws Exception {

        String hospcode = request.getHeader("hospcode");
        String query_scope = (String) postBody.get("query_scope");
        if (!"0".equals(query_scope)) {
            return new NodeRedResponse("1", "失败");
        }

        List<ChargeItemInfo> chargeItemInfoList = new ArrayList<>();

        // 创建第一个元素
        ChargeItemInfo item1 = new ChargeItemInfo();
        item1.setItem_name("项目1");
        item1.setItem_code("CODE1");
        item1.setPrice("10.00");
        item1.setExpense_price("8.00");
        item1.setNon_expense_price("12.00");
        item1.setSet_item_name("套餐1");
        item1.setUnit("个");
        item1.setItem_spec("规格1");
        chargeItemInfoList.add(item1);

        // 创建第二个元素
        ChargeItemInfo item2 = new ChargeItemInfo();
        item2.setItem_name("项目2");
        item2.setItem_code("CODE2");
        item2.setPrice("15.00");
        item2.setExpense_price("12.00");
        item2.setNon_expense_price("18.00");
        item2.setSet_item_name("套餐2");
        item2.setUnit("个");
        item2.setItem_spec("规格2");
        chargeItemInfoList.add(item2);

        // 创建第三个元素
        ChargeItemInfo item3 = new ChargeItemInfo();
        item3.setItem_name("项目3");
        item3.setItem_code("CODE3");
        item3.setPrice("20.00");
        item3.setExpense_price("16.00");
        item3.setNon_expense_price("24.00");
        item3.setSet_item_name("套餐3");
        item3.setUnit("个");
        item3.setItem_spec("规格3");
        chargeItemInfoList.add(item3);

        NodeRedResponse nodeRedResponse = new NodeRedResponse();
        nodeRedResponse.setCode("0");
        nodeRedResponse.setMsg("成功");
        NodeRedResponseData data = new NodeRedResponseData();
        data.setContent(chargeItemInfoList);
        nodeRedResponse.setData(data);
        return nodeRedResponse;
    }

}
