package cn.taihealth.ih.rest.controller.management.crm;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.crm.CrmQuestionnaireClassify;
import cn.taihealth.ih.repo.crm.CrmQuestionnaireClassifyRepository;
import cn.taihealth.ih.repo.crm.CrmQuestionnaireQuestionnaireClassifyRepository;
import cn.taihealth.ih.service.dto.crm.CrmQuestionnaireClassifyDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(value = {"/admin/crm/questionnaires/classify"})
@Api(tags = "admin问卷分类管理", description = "admin问卷分类管理")
@AllArgsConstructor
public class SystemCrmQuestionnaireClassifyResource {

    private final CrmQuestionnaireClassifyRepository crmQuestionnaireClassifyRepository;
    private final CrmQuestionnaireQuestionnaireClassifyRepository crmQuestionnaireQuestionnaireClassifyRepository;


    @PostMapping()
    @ApiOperation("添加问卷分类")
    @ActionLog(action = "添加问卷分类")
    public CrmQuestionnaireClassifyDTO addQuestionnaireClassify(@RequestBody @Valid CrmQuestionnaireClassifyDTO dto) {
        return new CrmQuestionnaireClassifyDTO(crmQuestionnaireClassifyRepository.save(dto.toEntity(null)));
    }

    @PutMapping("/{id}")
    @ApiOperation("修改问卷分类")
    @ActionLog(action = "修改问卷分类")
    public CrmQuestionnaireClassifyDTO updateQuestionnaireClassify(@PathVariable Long id,
                                                       @RequestBody @Valid CrmQuestionnaireClassifyDTO dto) {
        CrmQuestionnaireClassify crmQuestionnaireClassify = crmQuestionnaireClassifyRepository.getById(id);
        crmQuestionnaireClassify.setCode(dto.getCode());
        crmQuestionnaireClassify.setName(dto.getName());
        return new CrmQuestionnaireClassifyDTO(crmQuestionnaireClassifyRepository.save(crmQuestionnaireClassify));
    }


    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("删除问卷分类")
    @ActionLog(action = "删除问卷分类")
    @Transactional
    public void deleteQuestionnaireClassify(@PathVariable Long id) {
        CrmQuestionnaireClassify classify = crmQuestionnaireClassifyRepository.getById(id);
        crmQuestionnaireQuestionnaireClassifyRepository.deleteAllByCrmQuestionnaireClassify(classify);
        crmQuestionnaireClassifyRepository.delete(classify);
    }

    @GetMapping()
    @ApiOperation("查询问卷")
    @ActionLog(action = "查询问卷")
    public Page<CrmQuestionnaireClassifyDTO> getQuestionnaireClassify(
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate");
        return crmQuestionnaireClassifyRepository.findAll(page).map(CrmQuestionnaireClassifyDTO::new);
    }
}
