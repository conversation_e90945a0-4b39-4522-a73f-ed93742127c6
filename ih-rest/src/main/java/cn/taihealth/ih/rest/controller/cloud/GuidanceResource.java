package cn.taihealth.ih.rest.controller.cloud;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.GuidanceQuestion;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.GuidenceQuestionTypeEnum;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.GuidanceService;
import cn.taihealth.ih.service.dto.GuidanceQuestionDTO;
import cn.taihealth.ih.service.dto.GuidanceRecordCombinedDTO;
import cn.taihealth.ih.service.dto.GuidanceRecordDTO;
import cn.taihealth.ih.service.dto.GuidanceSuggestDTO;
import cn.taihealth.ih.service.dto.GuidanceWaitRecordCombinedDTO;
import cn.taihealth.ih.service.dto.GuidanceWaitRecordDTO;
import cn.taihealth.ih.service.util.IHBeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2020/9/27
 */
@RestController
@Api(tags = "导诊,候诊对话", description = "根据问答来给用户推荐就诊的科室,以及在候诊的时候让用户修改就诊记录(病历处方等)")
@RequestMapping(value = {"/guidance"})
public class GuidanceResource {

    @Autowired
    private GuidanceService guidanceService;
        
    @PostMapping("/question_and_answer/{questionType}")
    @ApiOperation("导诊阶段根据根据问答来收集必须的就诊信息")
    @ActionLog(action = "导诊阶段根据根据问答来收集必须的就诊信息")
    public GuidanceRecordDTO nextQuestion(
        @ApiParam("会话id, 开始的时候不填") @RequestBody GuidanceRecordDTO guidanceRecordDTO,
        @ApiParam("问题类别") @PathVariable Integer questionType) throws Exception {
        User current = CurrentUser.getOrThrow();
        GuidenceQuestionTypeEnum guidenceQuestionTypeEnum = GuidenceQuestionTypeEnum.lookupByIdUtil(questionType);
        if (guidenceQuestionTypeEnum.getId() != GuidenceQuestionTypeEnum.EMERGENCY.getId()
                && guidenceQuestionTypeEnum.getId() != GuidenceQuestionTypeEnum.REVISIT.getId()&&guidenceQuestionTypeEnum.getId()!=GuidenceQuestionTypeEnum.INTERROGATION.getId()) {
            throw ErrorType.ILLEGAL_PARAMS
                    .toProblem(String.format("导诊阶段的问题类型有误：%s", guidenceQuestionTypeEnum.name()));
        }
        return guidanceService.getQuestion(current.getId(), guidenceQuestionTypeEnum, guidanceRecordDTO);
    }

    @GetMapping("/question_and_answer/{questionType}")
    @ApiOperation("导诊阶段的会话记录")
    @ActionLog(action = "查询导诊阶段的会话记录")
    public GuidanceRecordCombinedDTO getGuideQuestionRecords(
        @ApiParam("急诊导诊阶段的 OrderId") @RequestParam Long orderId,
        @ApiParam("问题类别") @PathVariable Integer questionType) throws Exception {
        User current = CurrentUser.getOrThrow();
        GuidenceQuestionTypeEnum guidenceQuestionTypeEnum = GuidenceQuestionTypeEnum.lookupByIdUtil(questionType);
        return guidanceService.getGuidanceQuestionRecord(current.getId(), orderId);
    }

    @PostMapping("/question_and_answer/done/{questionType}")
    @ApiOperation("结束导诊对话收集导诊的科室")
    @ActionLog(action = "结束导诊阶段的会话记录")
    public void questionDone(
            @ApiParam("会话id, 开始的时候不填") @RequestBody GuidanceSuggestDTO guidanceSuggestDTO,
            @ApiParam("问题类别") @PathVariable Integer questionType) throws Exception {
        User current = CurrentUser.getOrThrow();
        GuidenceQuestionTypeEnum guidenceQuestionTypeEnum = GuidenceQuestionTypeEnum.lookupByIdUtil(questionType);
        guidanceService.questionDone(guidanceSuggestDTO, guidenceQuestionTypeEnum);
    }

    @PostMapping("/wait_question_and_answer/{questionType}")
    @ApiOperation("候诊阶段根据根据问答来让用户修改就诊记录(病历处方等)")
    @ActionLog(action = "候诊阶段根据根据问答来让用户修改就诊记录(病历处方等)")
    public GuidanceWaitRecordDTO nextWaitingQuestion(
            @ApiParam("会话id, 开始的时候不填") @Valid @RequestBody GuidanceWaitRecordDTO guidanceWaitRecordDTO,
            @ApiParam("问题类别") @PathVariable Integer questionType) throws Exception {
        User current = CurrentUser.getOrThrow();
        GuidenceQuestionTypeEnum guidenceQuestionTypeEnum = GuidenceQuestionTypeEnum.lookupByIdUtil(questionType);
        return guidanceService
                .getWaitingQuestion(current.getId(), guidenceQuestionTypeEnum, guidanceWaitRecordDTO);
    }


    @GetMapping("/wait_question_and_answer/{questionType}")
    @ApiOperation("候诊阶段的会话记录")
    @ActionLog(action = "查询候诊阶段的会话记录")
    public GuidanceWaitRecordCombinedDTO getWaitingQuestionRecord(
            @ApiParam("orderId 挂号订单号") @RequestParam(required = true) Long orderId,
            @ApiParam("问题类别") @PathVariable Integer questionType) throws Exception {
        User current = CurrentUser.getOrThrow();
        GuidenceQuestionTypeEnum guidenceQuestionTypeEnum = GuidenceQuestionTypeEnum.lookupByIdUtil(questionType);
        return guidanceService.getWaitingQuestionRecord(current.getId(), orderId);
    }

    @PostMapping("/init_q_and_a")
    @ApiOperation("根据需求直接将问答信息初始化")
    @ActionLog(action = "根据需求直接将问答信息初始化")
    public void initQAndA() {

        guidanceService.initQAndA();
    }

//    @PostMapping("/add_question")
//    @ApiOperation("添加问题")
//    public GuidanceQuestionDTO createQuestion(
//            @RequestBody GuidanceQuestionDTO guidanceRecordDTO) throws Exception {
//        User current = CurrentUser.getOrThrow();
//        GuidanceQuestion guidanceQuestion = new GuidanceQuestion();
//        IHBeanUtils.copyProperties(guidanceRecordDTO, guidanceQuestion);
//        return new GuidanceQuestionDTO(guidanceService.createGuidanceQuestion(guidanceQuestion));
//    }

//    @PostMapping("/update_question")
//    @ApiOperation("修改问题")
//    public GuidanceQuestionDTO updateQuestion(
//            @RequestBody GuidanceQuestionDTO guidanceRecordDTO) throws Exception {
//        User current = CurrentUser.getOrThrow();
//        GuidanceQuestion guidanceQuestion = new GuidanceQuestion();
//        IHBeanUtils.copyProperties(guidanceRecordDTO, guidanceQuestion);
//        return new GuidanceQuestionDTO(guidanceService.updateGuidanceQuestion(guidanceQuestion));
//    }

//    @PostMapping("/del_question")
//    @ApiOperation("删除问题")
//    public void delQuestion(
//            @RequestBody GuidanceQuestionDTO guidanceRecordDTO) throws Exception {
//        User current = CurrentUser.getOrThrow();
//        if (guidanceRecordDTO.getId() == null) {
//            throw new Exception("必填字段为空");
//        }
//        guidanceService.delGuidanceQuestionById(guidanceRecordDTO.getId());
//    }

//    @GetMapping("/get_questions")
//    @ApiOperation("获取问题列表")
//    public IhPage<GuidanceQuestionDTO> getQuestions(
//            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
//            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size)
//            throws Exception {
//        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "questionType");
//
//        Page<GuidanceQuestion> guidanceQuestions = guidanceService.getGuidanceQuestion(page);
//        return guidanceQuestions.map(this::convert);
//    }

    private GuidanceQuestionDTO convert(GuidanceQuestion guidanceQuestion) {
        GuidanceQuestionDTO guidanceQuestionDTO = new GuidanceQuestionDTO();
        IHBeanUtils.copyProperties(guidanceQuestion, guidanceQuestionDTO);
        return guidanceQuestionDTO;
    }
}
