package cn.taihealth.ih.rest.controller.tim;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.aop.logging.ThirdPartyLog;
import cn.taihealth.ih.commons.util.HttpUtil;
import cn.taihealth.ih.domain.TIMHistoryFile;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.service.LinkService;
import cn.taihealth.ih.mq.config.RabbitMQChannelConfig;
import cn.taihealth.ih.mq.entity.message.VideoEndMsg;
import cn.taihealth.ih.mq.enums.MessageTypeEnum;
import cn.taihealth.ih.mq.provider.MessageProvider;
import cn.taihealth.ih.repo.TIMHistoryFileRepository;
import cn.taihealth.ih.service.api.TIMGroupMemberService;
import cn.taihealth.ih.service.api.TIMMessageService;
import cn.taihealth.ih.service.dto.UploadDTO;
import cn.taihealth.ih.service.dto.live.TencentRoomUserExitDTO;
import cn.taihealth.ih.service.vm.IhPage;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 */
@RestController
@Api(tags = "腾讯IM", description = "腾讯IM (不需要权限)")
public class TIMResource {

    private final TIMMessageService timMessageService;
    private final TIMHistoryFileRepository timHistoryFileRepository;
    private final TIMGroupMemberService timGroupMemberService;
    private final MessageProvider messageProvider;

    public TIMResource(TIMHistoryFileRepository timHistoryFileRepository, TIMMessageService timMessageService,
                       TIMGroupMemberService timGroupMemberService, MessageProvider messageProvider) {
        this.timMessageService = timMessageService;
        this.timHistoryFileRepository = timHistoryFileRepository;
        this.timGroupMemberService = timGroupMemberService;
        this.messageProvider = messageProvider;
    }

    public static class CallbackResult {

        @JsonProperty("ActionStatus")
        private String actionStatus = "OK";

        @JsonProperty("ErrorInfo")
        private String errorInfo = "";

        @JsonProperty("ErrorCode")
        private int errorCode = 0;
    }

    private static final Logger log = LoggerFactory.getLogger(TIMResource.class);

    @PostMapping("/tim/callback")
    @ApiOperation("腾讯云IM回调接口, (不需要权限)")
    @ThirdPartyLog(action = "腾讯云IM回调接口, (不需要权限)")
    public CallbackResult callbackAfterVideoChatClosed(HttpServletRequest request,
                                                       @RequestParam("SdkAppid") String sdkAppId,
                                                       @RequestParam("CallbackCommand") String callbackCommand,
                                                       @RequestBody String json) {
        log.info("------------------腾讯云IM回调接口----------------- params："
                + HttpUtil.toListHttpParams(request.getParameterMap()) + ", body: " + json);

        switch (callbackCommand) {
            case "Group.CallbackAfterGroupDestroyed":
//                群解散
                break;
            case "Group.CallbackAfterMemberExit":
//                成员退群
                break;
            case "Group.CallbackAfterCreateGroup":
                // 新建群组
                timGroupMemberService.saveGroupMemberWhenGroupCreate(json);
                break;
            case "Group.CallbackAfterNewMemberJoin":
                // 新成员加入
                timGroupMemberService.saveGroupMemberWhenGroupMemberAdd(json);
                break;
            case "Group.CallbackAfterSendMsg":
                // 发送消息
                timMessageService.saveMessage(json);
                break;
            default:
        }
        return new CallbackResult();
    }

    public static class TIMHistoryFileVM {

        private Long id;
        private Date createdDate;
        private String fileName;
        private String mimeType;
        private String groupId;
        private String url;

        public TIMHistoryFileVM(TIMHistoryFile timHistoryFile) {
            id = timHistoryFile.getId();
            createdDate = timHistoryFile.getCreatedDate();
            groupId = timHistoryFile.getGroupId();
            fileName = timHistoryFile.getUuid();
            Upload upload = timHistoryFile.getUpload();
            if (upload != null) {
                mimeType = upload.getMimeType();
                url = AppContext.getInstance(LinkService.class).urlOfUpload(upload);
            }
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public Date getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(Date createdDate) {
            this.createdDate = createdDate;
        }

        public String getMimeType() {
            return mimeType;
        }

        public void setMimeType(String mimeType) {
            this.mimeType = mimeType;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    @GetMapping("/tim/history_files")
    @ApiOperation("获取腾讯备份文件 （需要HOTLINE权限）")
    public IhPage<TIMHistoryFileVM> getTimHistoryFiles(
        @ApiParam(name = "since", value = "从...日期开始，格式采用ISO8601")
        @RequestParam(required = true) String since,
        @ApiParam(name = "page", value = "页数,从0开始")
        @RequestParam(name = "page", required = false) Integer pageNo,
        @ApiParam(name = "size", value = "最大返回数量,默认100")
        @RequestParam(required = false) Integer size) {
        if (pageNo == null) {
            pageNo = 0;
        }
        if (size == null) {
            size = Constants.BATCH_SIZE;
        }
        Date date = (Date) DataTypes.DATE.fromString(since, Constants.ISO8601);
        Specification<TIMHistoryFile> spec = Specifications.ge("createdDate", date);
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.ASC, "createdDate");
        return new IhPage<>(timHistoryFileRepository.findAll(spec, page)
            .map(TIMHistoryFileVM::new));
    }

    public static class AudioMessage {

        @JsonProperty
        private UploadDTO audio;
        @JsonProperty
        private String info;
    }

    @PostMapping("/tim/user_exit_room")
    @ApiOperation("用户退出房间回调")
    @ThirdPartyLog(action = "用户退出房间回调")
    public void sendGroupAudioMessage(@RequestBody String body, HttpServletRequest request) {


        log.info("用户退出房间回调, body: {}", body);
        JSONObject info = JSONObject.parseObject(body);
        String eventInfoJson = info.getJSONObject("EventInfo").toJSONString();
        TencentRoomUserExitDTO eventInfo = StandardObjectMapper.readValue(eventInfoJson, new TypeReference<>() {
        });
        String appid = request.getHeader("SdkAppId");
        Integer eventType = info.getInteger("EventType");
        if (eventType != null && eventType.equals(104)) {
            String roomId = eventInfo.getRoomId();
            String username = eventInfo.getUsername();
            messageProvider.sendMessage("", RabbitMQChannelConfig.DELAYED_IM_QUEUE_NAME,
                    new VideoEndMsg(roomId, appid, username), MessageTypeEnum.VIDEO_END);
        }
    }
}
