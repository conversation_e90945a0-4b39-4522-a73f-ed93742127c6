package cn.taihealth.ih.rest.error;


import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.config.ApplicationProperties;
import cn.taihealth.ih.commons.error.ErrorConstants;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.service.api.InvalidIdCardImageException;
import cn.taihealth.ih.service.error.InvalidDiagnosisDataException;
import cn.taihealth.ih.service.error.LockException;
import cn.taihealth.ih.service.error.UserNotFoundException;
import cn.taihealth.ih.service.impl.im.IMException;
import cn.taihealth.ih.service.impl.sms.SMSException;
import cn.taihealth.ih.service.impl.sms.SMSValiditySecondException;
import cn.taihealth.ih.service.impl.wechat.WechatException;
import cn.taihealth.ih.service.util.GeoException;
import cn.taihealth.ih.spring.security.jwt.DoctorDisabledException;
import cn.taihealth.ih.spring.security.jwt.UnauthorizedException;
import cn.taihealth.ih.spring.security.sms.BadTokenException;
import com.alipay.api.AlipayApiException;
import com.gitq.jedi.common.exception.Exceptions;
import com.google.common.base.Strings;
import io.jsonwebtoken.ExpiredJwtException;
import org.hibernate.ObjectNotFoundException;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.*;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.ServletWebRequest;
import org.zalando.problem.DefaultProblem;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.ThrowableProblem;
import org.zalando.problem.spring.web.advice.validation.ConstraintViolationProblem;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.EntityNotFoundException;
import javax.servlet.http.HttpServletRequest;
import java.sql.BatchUpdateException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller advice to translate the server side exceptions to client-friendly json structures.
 * The error response follows RFC7807 - Problem Details for HTTP APIs (https://tools.ietf.org/html/rfc7807)
 */
@ControllerAdvice
public class ExceptionTranslator implements LoggableProblemHandling {

    private static final Logger log = LoggerFactory.getLogger(ExceptionTranslator.class);

    private final boolean showStackTrace;

    public ExceptionTranslator(ApplicationProperties properties) {
        this.showStackTrace = properties.isShowStackTrace();
    }

    /**
     * Post-process Problem payload to add the message key for front-end if needed
     */
    @Override
    public ResponseEntity<Problem> process(@Nullable ResponseEntity<Problem> entity, NativeWebRequest request) {
        if (entity == null || entity.getBody() == null) {
            return entity;
        }
        String path = ((ServletWebRequest) request).getRequest().getServletPath();
        String params = ((ServletWebRequest) request).getRequest().getQueryString();
        String urlParam = ((ServletWebRequest) request).getRequest().getMethod() + " " + (Strings.isNullOrEmpty(params) ? path : path + "?" + params);
        log.info(urlParam);
        Problem problem = entity.getBody();
        if (!(problem instanceof ConstraintViolationProblem || problem instanceof DefaultProblem)) {
            return entity;
        }

        ProblemBuilder builder = Problem.builder()
            .withType(Problem.DEFAULT_TYPE.equals(problem.getType()) ? ErrorConstants.DEFAULT_TYPE : problem.getType())
            .withStatus(problem.getStatus())
            .withTitle(problem.getTitle())
            .with("path", path);

        if (problem instanceof ConstraintViolationProblem) {
            builder
                .with("code", ErrorType.CONSTRAINT_VIOLATION.getCode())
                .with("violations", ((ConstraintViolationProblem) problem).getViolations())
                .withTitle(ErrorType.CONSTRAINT_VIOLATION.getTitle())
                .withDetail(ErrorType.CONSTRAINT_VIOLATION.getDetail())
                .withStatus(ErrorType.CONSTRAINT_VIOLATION.getStatus());
        } else {
            problem.getParameters().forEach(builder::with);
            builder
                .withCause(((DefaultProblem) problem).getCause())
                .withDetail(problem.getDetail())
                .withInstance(problem.getInstance());
            if (showStackTrace) {
                builder.with("stacktrace", ((DefaultProblem) problem).getStackTrace());
            }

            if (!problem.getParameters().containsKey("path")
                || Strings.isNullOrEmpty((String) problem.getParameters().get("path"))) {
                builder.with("path", path);
            }

            if (!problem.getParameters().containsKey("code") && problem.getStatus() != null) {
                builder.with("code", "error.http." + problem.getStatus().getStatusCode());
            }
        }
        ThrowableProblem rp = builder.build();
        return new ResponseEntity<>(rp, entity.getHeaders(), entity.getStatusCode());
    }

    @Override
    public ResponseEntity<Problem> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, @Nonnull NativeWebRequest request) {
        BindingResult result = ex.getBindingResult();
        List<FieldErrorVM> fieldErrors = result.getFieldErrors().stream()
                .map(f -> new FieldErrorVM(f.getObjectName(), f.getField(), f.getDefaultMessage()))
                .collect(Collectors.toList());

        ProblemBuilder builder = ProblemUtils.toBuilder(ErrorType.CONSTRAINT_VIOLATION.toProblem());
        builder.with("fieldErrors", fieldErrors);
        builder.with("path", getRequestPath(request));
        if (!fieldErrors.isEmpty()) {
            FieldErrorVM first = fieldErrors.get(0);
            builder.withTitle(first.getMessage());
            builder.withDetail(first.getMessage());
        }

        return create(ex, builder.build(), request);
    }

    @ExceptionHandler(ConcurrencyFailureException.class)
    public ResponseEntity<Problem> handleConcurrencyFailure(ConcurrencyFailureException ex, NativeWebRequest request) {
        log.info("系统同步错误", ex);
        Problem problem = ErrorType.CONCURRENCY_FAILURE.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(InvalidIdCardImageException.class)
    public ResponseEntity<Problem> handleInvalidIdCardImageException(InvalidIdCardImageException ex, NativeWebRequest request) {
        Problem problem = ErrorType.INVALID_ID_CARD_IMAGE.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler({SQLIntegrityConstraintViolationException.class, DuplicateKeyException.class,
        ConstraintViolationException.class, BatchUpdateException.class})
    public ResponseEntity<Problem> handleDuplicateDateException(Exception ex, NativeWebRequest request) {
        Problem problem = ErrorType.DATA_ACCESS_ERROR.toProblem(null,ex.getMessage(),getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(IMException.class)
    public ResponseEntity<Problem> handleIMException(IMException ex, NativeWebRequest request) {
        Problem problem = ErrorType.IM_COMMUNICATION_FAILED.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(WechatException.class)
    public ResponseEntity<Problem> handleWechatException(WechatException ex, NativeWebRequest request) {
        Problem problem = ErrorType.WECHAT_COMMUNICATION_FAILED.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(UnauthorizedException.class)
    public ResponseEntity<Problem> handleUnauthoirzedException(UnauthorizedException ex, NativeWebRequest request) {
        Problem problem = ErrorType.UNAUTHORIZED.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler( { DataAccessException.class })
    public ResponseEntity<Problem> handleDataAccessException(DataAccessException ex, NativeWebRequest request) {
        Problem problem;
        if (Exceptions.isCausedBy(ex, EntityNotFoundException.class, ObjectNotFoundException.class)) {
            log.error("请求的资源不存在:", ex);
            problem = ErrorType.NOT_FOUND_ERROR.toProblem("请求的资源不存在");
        } else if (Exceptions.isCausedBy(ex, EmptyResultDataAccessException.class)) {
            log.error("数据不存在:", ex);
            problem = ErrorType.DATA_ACCESS_ERROR.toProblem("数据不存在");
        } else if (Exceptions.isCausedBy(ex, DataIntegrityViolationException.class)) {
            if (ex.getRootCause() instanceof SQLIntegrityConstraintViolationException) {
                log.error("数据库语句执行错误:", ex);
                problem = ErrorType.DATA_ACCESS_ERROR.toProblem("数据库语句执行错误");
            } else {
                log.error("数据访问错误:", ex);
                problem = ErrorType.DATA_ACCESS_ERROR.toProblem("数据访问错误");
            }
        } else {
            log.error("数据访问错误:", ex);
            problem = ErrorType.DATA_ACCESS_ERROR.toProblem("数据访问错误");
        }

        return create(ex, problem, request);
    }

    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<Problem> handleUserNotFoundException(UserNotFoundException ex, NativeWebRequest request) {
        Problem problem = ErrorType.USER_NOT_FOUND.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(InvalidDiagnosisDataException.class)
    public ResponseEntity<Problem> handleInvalidDiagnosisDataException(InvalidDiagnosisDataException ex, NativeWebRequest request) {
        Problem problem = ErrorType.INVALID_DIAGNOSIS_DATA.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(SMSException.class)
    public ResponseEntity<Problem> handleSMSException(SMSException ex, NativeWebRequest request) {
        Problem problem = ErrorType.SMS_FAILURE.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(ExpiredJwtException.class)
    public ResponseEntity<Problem> handleExpiredJwtException(ExpiredJwtException ex, NativeWebRequest request) {
        Problem problem = ErrorType.JWT_TOKEN_EXPIRED.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(BadCredentialsException.class)
    @ActionLog(action = "登录")
    public ResponseEntity<Problem> handleBadCredentialsException(BadCredentialsException ex, NativeWebRequest request) {
        Problem problem = ErrorType.BAD_CREDENTIALS.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(BadTokenException.class)
    @ActionLog(action = "登录")
    public ResponseEntity<Problem> handleBadTokenException(BadTokenException ex, NativeWebRequest request) {
        Problem problem = ErrorType.INVALID_VALIDATION_TOKEN.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(LockException.class)
    public ResponseEntity<Problem> handleLockException(LockException ex, NativeWebRequest request) {
        Throwable cause = ex.getCause();
        return create(cause, request);
    }

    @ExceptionHandler(AlipayApiException.class)
    public ResponseEntity<Problem> handleLockException(AlipayApiException ex, NativeWebRequest request) {
        Problem problem = ErrorType.INTERNAL_SERVER_ERROR.toProblem("支付宝调用失败", ex.getMessage(), getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(GeoException.class)
    public ResponseEntity<Problem> handleGeoException(GeoException ex, NativeWebRequest request) {
        Problem problem = ErrorType.INVALID_GEO_INFO.toProblemWithPath(getRequestPath(request));
        return create(ex, problem, request);
    }

    @ExceptionHandler(SMSValiditySecondException.class)
    public ResponseEntity<Problem> handleSMSValiditySecondException(SMSValiditySecondException ex, NativeWebRequest request) {
        Problem problem = ErrorType.SMS_FAILURE.toProblem(ex.getMessage());
        return create(ex, problem, request);
    }

    @ExceptionHandler(DoctorDisabledException.class)
    public ResponseEntity<Problem> handleDoctorForbiddenException(DoctorDisabledException ex, NativeWebRequest request) {
        Problem problem = ErrorType.DOCTOR_IS_DISABLED.toProblem(ex.getMessage());
        return create(ex, problem, request);
    }

    private static String getRequestPath(NativeWebRequest request) {
        return request.getNativeRequest(HttpServletRequest.class).getRequestURI();
    }
}

