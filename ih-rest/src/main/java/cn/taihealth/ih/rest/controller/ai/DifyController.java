package cn.taihealth.ih.rest.controller.ai;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.ai.DifyService;
import cn.taihealth.ih.service.dto.ai.dify.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/user/dify")
@Api(tags = "Dify应用接口")
@AllArgsConstructor
@Slf4j
public class DifyController {

    private DifyService difyService;

    @GetMapping("/site")
    @ApiOperation("获取应用信息")
    @ActionLog(action = "获取应用信息")
    public SiteResponse site() {
        Hospital hospital = CurrentHospital.getOrThrow();
        return difyService.site(hospital);
    }

    @GetMapping("/parameters")
    @ApiOperation("获取应用配置信息")
    @ActionLog(action = "获取应用配置信息")
    public ParametersResponse parameters() {
        Hospital hospital = CurrentHospital.getOrThrow();
        return difyService.parameters(hospital);
    }

    @GetMapping("/meta")
    @ApiOperation("获取应用配置信息")
    @ActionLog(action = "获取应用配置信息")
    public MetaResponse meta() {
        Hospital hospital = CurrentHospital.getOrThrow();
        return difyService.meta(hospital);
    }

    @PostMapping("/files/upload")
    @ApiOperation("上传文件")
    @ActionLog(action = "上传文件")
    public UploadResponse upload(@RequestParam("file") MultipartFile file,
                                 @RequestParam(value = "user",required = false) String user) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        if (StringUtils.isEmpty(user)) {
            user = current.getId() + "";
        }
        return difyService.upload(user, hospital, file);
    }

    @PostMapping("/chat-messages")
    @ApiOperation("发送对话消息(流式)")
    @ActionLog(action = "发送对话消息(流式)")
    public ResponseEntity<SseEmitter> chatStreaming(@RequestBody MessageRequest message) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        SseEmitter emitter = difyService.chatStreaming(current, hospital, message);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(new org.springframework.http.MediaType("text", "event-stream", StandardCharsets.UTF_8));
        return new ResponseEntity<>(emitter, headers, HttpStatus.OK);
    }

    @PostMapping("/chat-messages/{taskId}/stop")
    @ApiOperation("停止响应")
    @ActionLog(action = "停止响应")
    public StopResponse stop(@PathVariable("taskId") String taskId, @RequestBody StopRequest request) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        String user = current.getId() + "";
        if (StringUtils.isNotEmpty(request.getUser())) {
            user = request.getUser();
        }
        return difyService.stop(user, hospital, taskId);
    }

    @PostMapping("/chat-messages/blocking")
    @ApiOperation("发送对话消息")
    @ActionLog(action = "发送对话消息")
    public MessageResponse chatBlocking(@RequestBody MessageRequest message) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return difyService.chat(current, hospital, message);
    }

    @PostMapping("/messages/{message_id}/feedbacks")
    @ApiOperation("消息反馈")
    @ActionLog(action = "消息反馈")
    public FeedbackResponse feedbacks(@PathVariable("message_id") String messageId, @RequestBody FeedbackRequest request) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        String user = current.getId() + "";
        if (StringUtils.isNotEmpty(request.getUser())) {
            user = request.getUser();
        }
        return difyService.feedbacks(user, hospital, messageId, request);
    }

    @GetMapping("/messages/{message_id}/suggested-questions")
    @ApiOperation("获取下一轮建议问题列表")
    @ActionLog(action = "获取下一轮建议问题列表")
    public SuggestedResponse suggested(@PathVariable("message_id") String messageId,
                                       @RequestParam(value = "user", required = false) String user) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        if (StringUtils.isEmpty(user)) {
            user = current.getId() + "";
        }
        return difyService.suggested(user, hospital, messageId);
    }

    @GetMapping("/messages")
    @ApiOperation("获取会话历史消息")
    @ActionLog(action = "获取会话历史消息")
    public HistoryMessages messages(@RequestParam(value = "user", required = false) String user,
                                    @RequestParam(value = "conversation_id") String conversationId,
                                    @RequestParam(value = "limit", required = false) String limit,
                                    @RequestParam(value = "first_id", required = false) String firstId) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        if (StringUtils.isEmpty(user)) {
            user = current.getId() + "";
        }
        return difyService.messages(user, hospital, conversationId, limit, firstId);
    }

    @GetMapping("/conversations")
    @ApiOperation("获取会话列表")
    @ActionLog(action = "获取会话列表")
    public HistoryConversations conversations(@RequestParam(value = "user", required = false) String user,
                                              @RequestParam(value = "last_id", required = false) String lastId,
                                              @RequestParam(value = "limit", required = false) String limit,
                                              @RequestParam(value = "pinned", required = false) String pinned,
                                              @RequestParam(value = "sort_by", required = false) String sortBy) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        if (StringUtils.isEmpty(user)) {
            user = current.getId() + "";
        }
        return difyService.conversations(user, hospital, lastId, limit, pinned, sortBy);
    }

    @PostMapping("/conversations/{conversationId}/name")
    @ApiOperation("会话重命名")
    @ActionLog(action = "会话重命名")
    public String renameConversations(@PathVariable("conversationId") String conversationId, @RequestBody RenameRequest renameRequest) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return difyService.renameConversations(current, hospital, conversationId, renameRequest);
    }

    @GetMapping("/conversations/{conversationId}/variables")
    @ApiOperation("获取对话变量")
    @ActionLog(action = "获取对话变量")
    public VariablesResponse variables(@PathVariable("conversationId") String conversationId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return difyService.variables(hospital, conversationId);
    }


    @DeleteMapping("/conversations/{conversationId}")
    @ApiOperation("删除会话")
    @ActionLog(action = "删除会话")
    public String deleteConversations(@PathVariable("conversationId") String conversationId) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return difyService.deleteConversations(current, hospital, conversationId);
    }

    @PostMapping("/audio-to-text ")
    @ApiOperation("语音转文字")
    @ActionLog(action = "语音转文字")
    public AudioToTextResponse audioToText(@RequestParam("file") MultipartFile file) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        return difyService.audioToText(current, hospital, file);
    }

    @PostMapping("/text-to-audio")
    @ApiOperation("文字转语音")
    @ActionLog(action = "文字转语音")
    public byte[] textToAudio(@Valid @RequestBody TextToAudioRequest textToAudioRequest) {
        User current = CurrentUser.getOrThrow();
        Hospital hospital = CurrentHospital.getOrThrow();
        if (StringUtils.isEmpty(textToAudioRequest.getUser())) {
            textToAudioRequest.setUser(current.getId() + "");
        }
        return difyService.textToAudio(current, hospital, textToAudioRequest);
    }
}
