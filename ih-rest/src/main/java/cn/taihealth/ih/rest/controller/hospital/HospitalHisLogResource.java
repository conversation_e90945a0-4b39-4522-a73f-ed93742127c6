package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.HisMessageService;
import cn.taihealth.ih.service.dto.FromHisMessageDTO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for view and managing Log Level at runtime.
 */
@RestController
@RequestMapping(value = {"/hospital"})
@Api(tags = "his推送记录", description = "his推送过来的记录")
public class HospitalHisLogResource {

    private final HisMessageService hisMessageService;

    public HospitalHisLogResource(HisMessageService service) {
        this.hisMessageService = service;
    }

    @Timed
    @GetMapping("/his/push_messages")
    @ApiOperation(value = "获取全部his推送日志")
    public Page<FromHisMessageDTO> getList(@RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                           @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
                                           @ApiParam("query="
                                          + "push_date::<=2022-11-17+push_date:>=2021-11-17"
                                          + "msg_type:1201/1301/接口文档规定的类型"
                                          + "operation_sn:20231107889"
                                          + "patient_id:xxxx "
                                          + "patient_name:xxxx ")
                                      @RequestParam(value = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrNull();
        return hisMessageService.getHisMessages(hospital, pageNo, size, query);
    }

}

