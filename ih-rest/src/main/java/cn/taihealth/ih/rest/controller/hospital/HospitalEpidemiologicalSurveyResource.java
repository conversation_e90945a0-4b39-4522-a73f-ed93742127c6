package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.domain.EpidemiologicalSurvey;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.FollowupRecord;
import cn.taihealth.ih.repo.EpidemiologicalSurveyRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.dto.EpidemiologicalSurveyDTO;
import cn.taihealth.ih.service.dto.FollowupRecordDTO;
import cn.taihealth.ih.service.impl.filter.epidemiologicalsurvey.EpidemiologicalSurveySearch;
import cn.taihealth.ih.service.vm.IhPage;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 */
@RestController
@RequestMapping(value = {"/hospital/epidemiological_survey"})
@Api(tags = "医院-流调筛选管理", description = "医院-流调筛选管理")
@Slf4j
@AllArgsConstructor
public class HospitalEpidemiologicalSurveyResource {

    private final EpidemiologicalSurveyRepository epidemiologicalSurveyRepository;

    @GetMapping
    @ApiOperation("查询流掉筛选结果")
    public Page<EpidemiologicalSurveyDTO> getSurveyByHospital(
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("查询条件: NAME:名字 MOBILE:111 CARD_NUM:222 NORMAL(筛选结果):true " +
                    "CREATE_TIME:(提交时间)<=2023-01-01 10:00:00 CREATE_TIME:>=2023-01-01 10:00:00")
            @RequestParam(required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<EpidemiologicalSurvey>> specs = Lists.newArrayList();
        specs.add(Specifications.eq("hospital", hospital));
        specs.add(EpidemiologicalSurveySearch.of(query).toSpecification());
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate");
        return epidemiologicalSurveyRepository.findAll(Specifications.and(specs), page).map(EpidemiologicalSurveyDTO::new);
    }

}
