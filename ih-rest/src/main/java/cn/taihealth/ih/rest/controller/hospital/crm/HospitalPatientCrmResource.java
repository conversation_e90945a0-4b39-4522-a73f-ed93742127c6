package cn.taihealth.ih.rest.controller.hospital.crm;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.crm.*;
import cn.taihealth.ih.domain.enums.CrmStatus;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.crm.*;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.crm.CrmTaskService;
import cn.taihealth.ih.service.dto.crm.CrmOptionDTO;
import cn.taihealth.ih.service.dto.crm.CrmTaskDetailAnswerDTO;
import cn.taihealth.ih.service.dto.crm.CrmTaskDetailDTO;
import cn.taihealth.ih.service.vm.crm.CrmAnswerHistoryVM;
import cn.taihealth.ih.service.vm.crm.UpdatePlanTimeVM;
import cn.taihealth.ih.service.vm.crm.addPatientToPlansVM;
import com.gitq.jedi.common.datatype.DataTypes;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 */
@RestController
@RequestMapping(value = {"/hospital"})
@Api(tags = "随访-对就诊人的随访", description = "随访-对就诊人的随访相关API")
public class HospitalPatientCrmResource {

    private final CrmPlanRepository crmPlanRepository;
    private final PatientRepository patientRepository;
    private final CrmTaskService crmTaskService;
    private final CrmTaskDetailRepository crmTaskDetailRepository;
    private final CrmTaskDetailAnswerRepository crmTaskDetailAnswerRepository;
    private final OrderRepository orderRepository;
    private final CrmQuestionnaireQuestionRepository crmQuestionnaireQuestionRepository;
    private final CrmOptionRepository crmOptionRepository;
    private final CrmTaskRepository crmTaskRepository;

    public HospitalPatientCrmResource(CrmPlanRepository crmPlanRepository,
                                      PatientRepository patientRepository,
                                      CrmTaskService crmTaskService,
                                      CrmTaskDetailRepository crmTaskDetailRepository,
                                      CrmTaskDetailAnswerRepository crmTaskDetailAnswerRepository,
                                      OrderRepository orderRepository,
                                      CrmQuestionnaireQuestionRepository crmQuestionnaireQuestionRepository,
                                      CrmOptionRepository crmOptionRepository,
                                      CrmTaskRepository crmTaskRepository) {

        this.crmPlanRepository = crmPlanRepository;
        this.patientRepository = patientRepository;
        this.crmTaskService = crmTaskService;
        this.crmTaskDetailRepository = crmTaskDetailRepository;
        this.crmTaskDetailAnswerRepository = crmTaskDetailAnswerRepository;
        this.orderRepository = orderRepository;
        this.crmQuestionnaireQuestionRepository = crmQuestionnaireQuestionRepository;
        this.crmOptionRepository = crmOptionRepository;
        this.crmTaskRepository = crmTaskRepository;
    }

    @PostMapping("/patient/{patientId}/crm/plans/tasks")
    @ApiOperation("将就诊人加入随访计划")
    @ActionLog(action = "将就诊人加入随访计划")
    public void addPatientToPlan(@PathVariable Long patientId,
                                 @RequestBody @Valid addPatientToPlansVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Patient patient = patientRepository.getById(patientId);
        List<CrmPlan> plans = vm.getPlanIds().stream().map(crmPlanRepository::getById).collect(Collectors.toList());
        Order order = null;
        if (vm.getOrder() != null) {
            order = orderRepository.getById(vm.getOrder().getId());
        }
        crmTaskService.addOrUpdateTaskForPatient(hospital, patient, plans, order, user);
    }

    @GetMapping("/patient/crm/plans/tasks/details/{id}")
    @ApiOperation("开始进行本次随访-返回满意度问卷表单")
    @ActionLog(action = "开始进行本次随访")
    public CrmTaskDetailDTO startCrm(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        CrmTaskDetail detail = crmTaskDetailRepository.getById(id);
        if(!Objects.equals(detail.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (detail.getFollowUpUser() != null && !user.equals(detail.getFollowUpUser())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("该随访正由其他人执行");
        }

        return crmTaskService.startTask(hospital, detail, user);
    }

    @PatchMapping("/patient/crm/plans/tasks/details/{id}")
    @ApiOperation("将随访任务状态修改为CREATED状态")
    @ActionLog(action = "将随访任务状态修改为CREATED状态")
    public void updateCrmStatus(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        CrmTaskDetail detail = crmTaskDetailRepository.getById(id);
        if(!Objects.equals(detail.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (detail.getFollowUpUser() != null && !user.equals(detail.getFollowUpUser())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("该随访正由其他人执行");
        }
        detail.setStatus(CrmStatus.CREATED);
        crmTaskDetailRepository.save(detail);
    }

    @GetMapping("/patient/crm/plans/tasks/details/{id}/answers")
    @ApiOperation("查询问卷问题答案")
    @ActionLog(action = "查询问卷问题答案")
    public List<CrmTaskDetailAnswerDTO> updateQuestions(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        CrmTaskDetail detail = crmTaskDetailRepository.getById(id);
        if(!Objects.equals(detail.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return crmTaskDetailAnswerRepository.findAllByTaskDetail(detail).stream().map(u -> {
            CrmTaskDetailAnswerDTO answerDTO = new CrmTaskDetailAnswerDTO(u.getCrmQuestionnaireQuestion());
            String[] split = StringUtils.split(u.getOptions(), ",");
            if (split != null) {
                answerDTO.setOptions(Arrays.stream(split).map(Long::parseLong)
                    .collect(Collectors.toList()));
            }
            answerDTO.setResult(u.getResult());
            answerDTO.setScore(u.getScore());
            return answerDTO;
        }).collect(Collectors.toList());
    }

//    @PutMapping("/patient/crm/plans/tasks/details/{id}")
//    @ApiOperation("完成/暂存随访任务")
//    @ActionLog(action = "暂存随访任务")
//    public CrmTaskDetailDTO updateTask(@PathVariable Long id,
//                                       @RequestBody @Valid CrmTaskDetailUpdateVM vm) {
//        Hospital hospital = CurrentHospital.getOrThrow();
//        User user = CurrentUser.getOrThrow();
//        CrmTaskDetail detail = crmTaskDetailRepository.getById(id);
//        if(!Objects.equals(detail.getHospital(), hospital)) {
//            throw ErrorType.FORBIDDEN.toProblem();
//        }
//        if (!user.equals(detail.getFollowUpUser())) {
//            throw ErrorType.ILLEGAL_PARAMS.toProblem("该随访正由其他人执行或途径非法");
//        }
//
//        return crmTaskService.updateTask(hospital, detail, vm.getTaskDetail(), vm.getAnswers(), user);
//    }

    @PutMapping("/patient/crm/plans/tasks/details/{id}/plan_time")
    @ApiOperation("修改计划时间 非CREATED 和 STARTED状态的返回200但不会修改成功")
    @Transactional
    @ActionLog(action = "修改计划时间")
    public void updateTaskPlanTime(@PathVariable Long id,
                                   @RequestBody @Valid UpdatePlanTimeVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Date planTime = (Date) DataTypes.DATE.fromString(vm.getPlanTimeStr(), "yyyy-MM-dd");
        CrmTaskDetail detail = crmTaskDetailRepository.getById(id);
        if(!Objects.equals(detail.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        CrmTask task = detail.getTask();
        List<CrmTaskDetail> taskDetails = task.getTaskDetails();
        Optional<CrmTaskDetail> first = taskDetails.stream().findFirst();
        if (first.isPresent() && first.get().getId().equals(id)) {
            task.setFirstTime(planTime);
            crmTaskRepository.save(task);
        }
        if ((detail.getStatus() == CrmStatus.CREATED || detail.getStatus() == CrmStatus.STARTED) && planTime != null) {
            taskDetails.forEach(u -> {
                if (u.getDetailOrder() > detail.getDetailOrder() && planTime.getTime() > u
                    .getPlanTime().getTime()) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("计划时间应早于下次随访的计划时间");
                }
                if (u.getDetailOrder() < detail.getDetailOrder() && planTime.getTime() < u
                    .getPlanTime().getTime()) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("计划时间应晚于上次随访的计划时间");
                }
            });
            detail.setPlanTime(planTime);
            detail.setFollowUpUser(null);
        }
        crmTaskDetailRepository.save(detail);
    }

    @PutMapping("/patient/crm/plans/tasks/{id}/first/plan_time")
    @ApiOperation("修改第一次随访时间 需要taskId 仅供测试使用")
    @ActionLog(action = "修改第一次随访时间")
    public void updateTaskFirstTime(@PathVariable Long id,
                                    @RequestBody @Valid UpdatePlanTimeVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Date firstTime = (Date) DataTypes.DATE.fromString(vm.getPlanTimeStr(), "yyyy-MM-dd");
        CrmTask task = crmTaskRepository.getById(id);
        if(!Objects.equals(task.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        task.setFirstTime(firstTime);
        crmTaskRepository.save(task);
    }

    @GetMapping("/patient/crm/plans/tasks/details/{id}/answers/history")
    @ApiOperation("查询问卷问题历史记录")
    @ActionLog(action = "查询问卷问题历史记录")
    public List<CrmAnswerHistoryVM> updateTaskPlanTime(@RequestParam Long cqqId,
                                                       @PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        CrmTaskDetail detail = crmTaskDetailRepository.getById(id);
        if(!Objects.equals(detail.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        CrmQuestionnaireQuestion cqq = crmQuestionnaireQuestionRepository.getById(cqqId);
        List<Specification<CrmTaskDetailAnswer>> specs = Lists.newArrayList();
        CrmTask task = detail.getTask();
        specs.add(Specifications.eq("taskDetail.task", task));
        specs.add(Specifications.ne("taskDetail", detail));
        specs.add(Specifications.eq("crmQuestionnaireQuestion", cqq));

        return crmTaskDetailAnswerRepository.findAll(Specifications.and(specs), Sort.by(Direction.ASC, "updatedDate"))
            .stream().map(u -> {
                CrmAnswerHistoryVM vm = new CrmAnswerHistoryVM(u);
                List<CrmOptionDTO> optionDTOS = Lists.newArrayList();
                if (u.getOptions() != null) {
                    String[] split = u.getOptions().split(",");
                    List<String> strings = Arrays.asList(split);
                    optionDTOS = strings.stream()
                        .map(s -> new CrmOptionDTO(crmOptionRepository.getById(Long.parseLong(s))))
                        .collect(Collectors.toList());
                }
                vm.setOptions(optionDTOS);
                return vm;
            }).collect(Collectors.toList());
    }

}
