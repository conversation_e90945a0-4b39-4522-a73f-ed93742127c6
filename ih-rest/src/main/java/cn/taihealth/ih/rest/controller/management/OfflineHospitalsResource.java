package cn.taihealth.ih.rest.controller.management;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.MoreBeanUtils;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.OfflineHospital;
import cn.taihealth.ih.domain.cloud.OfflineHospitalAnnualInspection;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorker;
import cn.taihealth.ih.domain.hospital.offline.OfflineMedicalWorkerRecommend;
import cn.taihealth.ih.repo.OfflineHospitalAnnualInspectionRepository;
import cn.taihealth.ih.repo.OfflineHospitalRepository;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.repo.cloud.OfflineHospitalRecommendRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineMedicalWorkerRecommendRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineMedicalWorkerRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.OfflineHospitalService;
import cn.taihealth.ih.service.api.offline.OfflineMedicalWorkerService;
import cn.taihealth.ih.service.dto.OfflineHospitalDTO;
import cn.taihealth.ih.service.dto.hospital.offline.OfflineMedicalWorkerDTO;
import cn.taihealth.ih.service.impl.filter.offline.medicalworker.OfflineMedicalWorkerSearch;
import cn.taihealth.ih.service.impl.filter.offlinehospital.OffHospitalSearch;
import cn.taihealth.ih.service.vm.offline.OfflineHospitalAnnualInspectionVM;
import cn.taihealth.ih.service.vm.offline.OfflineHospitalRecommendVM;
import cn.taihealth.ih.service.vm.offline.OfflineMedicalWorkerRecommendVM;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = {"/admin"})
@Api(tags = "线下实体医院管理")
public class OfflineHospitalsResource {

    private final UploadRepository uploadRepository;
    private final OfflineHospitalRepository offlineHospitalRepository;
    private final OfflineHospitalService offlineHospitalService;
    private final OfflineHospitalRecommendRepository offlineHospitalRecommendRepository;
    private final OfflineMedicalWorkerRecommendRepository offlineMedicalWorkerRecommendRepository;
    private final OfflineMedicalWorkerService offlineMedicalWorkerService;
    private final OfflineHospitalAnnualInspectionRepository offlineHospitalAnnualInspectionRepository;

    public OfflineHospitalsResource(OfflineHospitalRepository offlineHospitalRepository,
                                    UploadRepository uploadRepository,
                                    OfflineHospitalRecommendRepository offlineHospitalRecommendRepository,
                                    OfflineMedicalWorkerRecommendRepository offlineMedicalWorkerRecommendRepository,
                                    OfflineMedicalWorkerService offlineMedicalWorkerService,
                                    OfflineHospitalService offlineHospitalService,
                                    OfflineHospitalAnnualInspectionRepository offlineHospitalAnnualInspectionRepository) {
        this.offlineHospitalRepository = offlineHospitalRepository;
        this.offlineHospitalService = offlineHospitalService;
        this.uploadRepository = uploadRepository;
        this.offlineHospitalRecommendRepository = offlineHospitalRecommendRepository;
        this.offlineMedicalWorkerRecommendRepository = offlineMedicalWorkerRecommendRepository;
        this.offlineMedicalWorkerService = offlineMedicalWorkerService;
        this.offlineHospitalAnnualInspectionRepository = offlineHospitalAnnualInspectionRepository;
    }

    @GetMapping("/offline_hospitals")
    @ApiOperation("获取医院信息")
    public Page<OfflineHospitalDTO> find(
        @ApiParam("query: enabled:true/false") @RequestParam(name = "query", required = false) String query,
                                         @RequestParam(name = "page", required = false) Integer pageNo,
                                         @RequestParam(name = "size", required = false) Integer size) {
        if (pageNo == null) {
            pageNo = 0;
        }

        if (size == null) {
            size = Constants.BATCH_SIZE;
        }

        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "createdDate", "id");
        OffHospitalSearch search = OffHospitalSearch.of(query);
        return offlineHospitalRepository.findAll(search.toSpecification(), page).map(
            OfflineHospitalDTO::new);
    }

    @GetMapping("/offline_hospitals/{id}")
    @ApiOperation("获取指定的医院的信息")
    public OfflineHospitalDTO getHospital(@ApiParam("医院的ID号") @PathVariable("id") Long id) {
        return new OfflineHospitalDTO(offlineHospitalRepository.getById(id));
    }

    @PatchMapping("/offline_hospitals/{id}")
    @ApiOperation("更新医院的信息")
    public OfflineHospitalDTO updateHospital(@ApiParam("医院的ID号") @PathVariable("id") Long id,
                                             @ApiParam("待更新的医院属性") @Valid @RequestBody OfflineHospitalDTO vm) {
        OfflineHospital offlineHospital = offlineHospitalRepository.getById(id);
        MoreBeanUtils.copyPropertiesNonNull(offlineHospital, vm);
        offlineHospital.setCapabilities(StringUtils.join(vm.getCapabilities(), " "));
        offlineHospitalService.save(offlineHospital);
        return new OfflineHospitalDTO(offlineHospital);
    }

    @PostMapping("/offline_hospitals")
    @ApiOperation("新增医院的信息")
    @ResponseStatus(HttpStatus.CREATED)
    public OfflineHospitalDTO addHospital(@ApiParam("医院") @Valid @RequestBody OfflineHospitalDTO vm) {
        if (offlineHospitalRepository.findOneByName(vm.getName()).isPresent()) {
            throw ErrorType.NAME_ALREADY_EXIST.toProblem();
        }

        OfflineHospital offlineHospital = new OfflineHospital();
        MoreBeanUtils.copyPropertiesNonNull(offlineHospital, vm);
        offlineHospital.setCapabilities(StringUtils.join(vm.getCapabilities(), " "));
        offlineHospitalService.save(offlineHospital);
        return new OfflineHospitalDTO(offlineHospital);
    }

    @PostMapping("/offline_hospitals/import/{uploadId}")
    @ApiOperation("根据上传的医院数据，将数据导入到系统中, 格式为gz文件, 数据类型为hospital的json格式数组")
    public void importHospital(@PathVariable("uploadId") long uploadId) {
        Upload upload = uploadRepository.getById(uploadId);
        offlineHospitalService.importHospitals(upload);
    }

    @DeleteMapping("/offline_hospitals/{id}")
    @ApiOperation("删除指定的医院")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteHospital(@PathVariable("id") Long id) {
        offlineHospitalService.delete(offlineHospitalRepository.getById(id));
    }

    @GetMapping("/offline_hospitals/recommends")
    @ApiOperation("查询推荐的线下医院")
    public Page<OfflineHospitalRecommendVM> getRecommendHospitals(@RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                      @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "sortNum", "id");
        return offlineHospitalRecommendRepository.findAll(page).map(OfflineHospitalRecommendVM::new);
    }

    @PostMapping("/offline_hospitals/recommends")
    @ApiOperation("添加推荐的线下医院")
    public void addRecommendHospitals(@RequestBody OfflineHospitalRecommendVM vm) {
        offlineHospitalService.addRecommendHospital(vm);
    }

    @PutMapping("/offline_hospitals/recommends/{recommendId}")
    @ApiOperation("修改推荐的线下医院")
    public void saveRecommendHospitals(@PathVariable long recommendId, @RequestBody OfflineHospitalRecommendVM vm) {
        offlineHospitalService.saveRecommendHospital(recommendId, vm);
    }

    @DeleteMapping("/offline_hospitals/recommends/{recommendId}")
    @ApiOperation("删除推荐的线下医院")
    public void saveRecommendHospitals(@PathVariable long recommendId) {
        offlineHospitalService.deleteRecommendHospital(recommendId);
    }

    @GetMapping("/offline_doctors")
    @ApiOperation(value = "查询医院的全部线下医务人员（分页）")
    @ActionLog(action = "查询医院的全部线下医务人员")
    public Page<OfflineMedicalWorkerDTO> getAllMedicalWorker(@ApiParam("查询条件 query=name:张 deptId:100000123 title:YIS") @RequestParam(name = "query", required = false) String query,
                                                             @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                                             @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "orderValue", "createdDate", "id");
        Hospital hospital = CurrentHospital.getOrNull();
        List<Specification<OfflineMedicalWorker>> sps = Lists.newArrayList();
        sps.add(OfflineMedicalWorkerSearch.of(query).toSpecification());
        if (hospital != null) {
            sps.add(Specifications.eq("hospital", hospital));
        }
        return AppContext.getInstance(OfflineMedicalWorkerRepository.class).findAll(Specifications.and(sps), page).
                map(OfflineMedicalWorkerDTO::new);
    }

    @GetMapping("/offline_doctor/recommends")
    @ApiOperation("查询推荐的线下医生")
    public Page<OfflineMedicalWorkerRecommendVM> getRecommendDoctor(@RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                                                    @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "sortNum", "id");
        Specification<OfflineMedicalWorkerRecommend> sp = Specifications.isNull("hospital");
        return offlineMedicalWorkerRecommendRepository.findAll(sp, page)
                .map(OfflineMedicalWorkerRecommendVM::new);
    }

    @PostMapping("/offline_doctor/recommends")
    @ApiOperation("添加推荐的线下医生")
    public void addRecommendDoctor(@RequestBody OfflineMedicalWorkerRecommendVM vm) {
        offlineMedicalWorkerService.addRecommendDoctor(null, vm);
    }

    @PutMapping("/offline_doctor/recommends/{recommendId}")
    @ApiOperation("修改推荐的线下医生")
    public void saveRecommendDoctor(@PathVariable long recommendId, @RequestBody OfflineMedicalWorkerRecommendVM vm) {
        offlineMedicalWorkerService.saveRecommendDoctor(recommendId, null, vm);
    }

    @DeleteMapping("/offline_doctor/recommends/{recommendId}")
    @ApiOperation("删除推荐的线下医生")
    public void saveRecommendDoctor(@PathVariable long recommendId) {
        offlineMedicalWorkerService.deleteRecommendDoctor(recommendId);
    }

    @GetMapping("/offline_hospitals/{id}/annual_inspections")
    @ApiOperation("查询线下医院年检信息")
    public Page<OfflineHospitalAnnualInspectionVM> getHospitalAIs(@PathVariable(name = "id") Long offlineHospitalId,
                                                                  @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                                                  @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC,  "id");
        Specification<OfflineHospitalAnnualInspection> sp = Specifications.eq("offlineHospital.id",
                                                                                offlineHospitalId);
        return offlineHospitalAnnualInspectionRepository.findAll(sp, page).map(OfflineHospitalAnnualInspectionVM::new);
    }

    @PostMapping("/offline_hospitals/annual_inspections")
    @ApiOperation("添加线下医院年检信息")
    public void addHospitalAIs(@RequestBody OfflineHospitalAnnualInspectionVM vm) {
        offlineHospitalService.addOfflineHospitalAnnualInspection(vm);
    }

    @PutMapping("/offline_hospitals/annual_inspections/{inspectionId}")
    @ApiOperation("修改线下医院年检信息")
    public void saveHospitalAIs(@PathVariable long inspectionId, @RequestBody OfflineHospitalAnnualInspectionVM vm) {
        vm.setId(inspectionId);
        offlineHospitalService.addOfflineHospitalAnnualInspection(vm);
    }

    @DeleteMapping("/offline_hospitals/annual_inspections/{inspectionId}")
    @ApiOperation("删除线下医院年检信息")
    public void removeHospitalAI(@PathVariable long inspectionId) {
        offlineHospitalAnnualInspectionRepository.deleteById(inspectionId);
    }

}
