package cn.taihealth.ih.rest.controller.hospital;

import cn.hutool.json.JSONUtil;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.DrugInstruction;
import cn.taihealth.ih.domain.VisitorPass;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.repo.DrugInstructionRepository;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.repo.VisitorPassRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.dto.DrugInstructionDTO;
import cn.taihealth.ih.service.dto.VisitorPassDTO;
import cn.taihealth.ih.service.impl.filter.ticket.TicketSearch;
import cn.taihealth.ih.service.impl.filter.visitorpass.VisitorPassSearch;
import cn.taihealth.ih.service.vm.DrugInstructionVM;
import cn.taihealth.ih.service.vm.IhPage;
import cn.taihealth.ih.service.vm.UploadVM;
import com.gitq.jedi.data.specification.Specifications;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

/**
 * @Author: zihao
 */
@RestController
@RequestMapping("/hospital")
@Api(tags = "住院管理-电子陪护证", description = "住院管理-电子陪护证")
@Slf4j
@AllArgsConstructor
public class HospitalVisitorPassResource {


    private final VisitorPassRepository visitorPassRepository;


    // 查询
    @GetMapping("/visitorPasses")
    @ApiOperation("住院管理-电子陪护证/查询(分页)")
    @ActionLog(action = "住院管理-电子陪护证/查询(分页)")
    public IhPage<VisitorPassDTO> list(@ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                       @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
                                       @ApiParam(value = "查询条件，query=startDate:2023-10-10 regno:23021" +
                                               " patname:张三 pat_certificate_no:310000199912121234 attendant_name:李四 " +
                                               "attendant_certificate_no:310000199912121234 status:UN_AUDIT") @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Specification<VisitorPass> specs = Specifications.and(Specifications.eq("hospital", hospital)
                , Specifications.eq("deleted", false),
                VisitorPassSearch.of(query).toSpecification()
                );
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate");
        log.info("电子陪护证（分页）查询 参数{}", JSONUtil.toJsonStr(specs));
        return new IhPage<>(visitorPassRepository.findAll(specs, page).map(VisitorPassDTO::new));
    }


    // 明细
    @ApiOperation("住院管理-电子陪护证明细查询")
    @ActionLog(action = "住院管理-电子陪护证明细查询")
    @GetMapping("/visitorPasses/{id}")
    public VisitorPassDTO getDrugInstruction(@PathVariable("id") Long id) {
        return new VisitorPassDTO(visitorPassRepository.getById(id));
    }

}
