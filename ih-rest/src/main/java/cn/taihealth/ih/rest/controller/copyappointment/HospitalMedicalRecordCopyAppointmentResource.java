package cn.taihealth.ih.rest.controller.copyappointment;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.medicalrecordcooyappointment.MedicalRecordCopyAppointmentService;
import cn.taihealth.ih.service.dto.express.LogisticsRouteInfoDTO;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.UserMedicalRecordCopyAppointmentDTO;
import cn.taihealth.ih.service.vm.UploadVM;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.CopyAppointmentAccountCheckVM;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.CopyAppointmentBillStatsVM;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.CopyAppointmentBillVM;
import cn.taihealth.ih.service.vm.hospital.medicalrecordcopyappointment.StatisticsOrdersVM;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: jzs
 * @Date: 2023-10-12
 */
@RestController
@RequestMapping("/hospital/medical_record_copy_appointment")
@Api(tags = "d端-病案复印预约管理")
@AllArgsConstructor
public class HospitalMedicalRecordCopyAppointmentResource {

    private final MedicalRecordCopyAppointmentService medicalRecordCopyAppointmentService;

    @GetMapping("/orders/statistics")
    @ApiOperation("看板统计")
    @ActionLog(action = "看板统计")
    public StatisticsOrdersVM getStatisticsOrders() {
        Hospital hospital = CurrentHospital.getOrThrow();
        return medicalRecordCopyAppointmentService.getStatisticsOrders(hospital);
    }

    @GetMapping("/orders")
    @ApiOperation("查询病案复印预约订单列表")
    @ActionLog(action = "查询病案复印预约订单列表")
    public Page<UserMedicalRecordCopyAppointmentDTO> getMedicalRecordCopyAppointmentOrders(
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("查询条件: 带\"\"值的查询条件表示支持多值查询 " +
                    "ORDER_STATUS:\"WAITING_REVIEW SURPLUS_REFUNDING SURPLUS_REFUNDED\"(待审核) \"WAITING_COPY SURPLUS_REFUNDING SURPLUS_REFUNDED\"(待复印) WAITING_ACCOUNTING(待核算) " +
                    "WAITING_APPEND(待补缴) WAIT_SEND(待邮寄) WAIT_SIGN(待签收) DELIVER_SIGN(已签收) WAIT_PICK_UP(待自提) " +
                    "PICK_UP(已自提) APPLY_REFUNDING(待退款) REFUNDED(已退款) " +
                    "DELIVERY_METHOD:邮寄方式 POSTAL_DELIVERY(邮寄)  SELF_PICKUP(自取) " +
                    "\nORDER_ID:订单ID PATIENT_NAME:患者名字 CARD_NUM:身份证号码 REG_NUM:住院号" +
                    "\nREG_TIME(出院时间)<=2023-01-01 10:00:00 REG_TIME(出院时间):>=2023-01-01 10:00:00" +
                    "\nCREATE_TIME:(申请时间)<2023-01-01 10:00:00 CREATE_TIME:>=2023-01-01 10:00:00" +
                    "\nSTEP(用来查询当前是查什么场景下的订单):APPLY_REFUND(表示查询病案退款申请下的订单) REFUND_REJECT(拒绝退款) REVIEW_REJECT(审核驳回)")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate");
        return medicalRecordCopyAppointmentService.queryAppointment(hospital, null, query, page);
    }

    @GetMapping("/orders/id/{copyAppointmentId}")
    @ApiOperation("获取病案复印预约订单")
    @ActionLog(action = "获取病案复印预约订单")
    public UserMedicalRecordCopyAppointmentDTO getMedicalRecordCopyAppointmentOrder(@PathVariable Long copyAppointmentId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return medicalRecordCopyAppointmentService.getAppointmentInfo(copyAppointmentId);
    }

    @GetMapping("/orders/account_check")
    @ApiOperation("查询病案复印对账管理")
    @ActionLog(action = "查询病案复印对账管理")
    public CopyAppointmentAccountCheckVM getCopyAppointmentAccountCheck (
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("查询条件:COPY_APPOINTMENT_ID:订单ID COPY_APPOINTMENT_PATIENT_NAME:患者名字 " +
                    "COPY_APPOINTMENT_ID_CARD_NUM:身份证号码" +
                    "CREATE_TIME:(申请时间)<2023-01-01 10:00:00 CREATE_TIME:>=2023-01-01 10:00:00" +
                    "PAY_TIME:(支付时间)<2023-01-01 10:00:00 PAY_TIME:>=2023-01-01 10:00:00" +
                    "PAY_STATUS: REGISTERED(已支付) REFUND(已退款) PENDING(待支付)")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate");
        return medicalRecordCopyAppointmentService.getCopyAppointmentAccountCheck(hospital, query, page);
    }

    @PutMapping("/orders/order_status_change")
    @ApiOperation("医院操作当前操作订单的步骤")
    @ActionLog(action = "医院操作当前操作订单的步骤")
    public UserMedicalRecordCopyAppointmentDTO operateMedicalRecordAppointmentOrder(@RequestBody UserMedicalRecordCopyAppointmentDTO userMedicalRecordCopyAppointmentDTO) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        return medicalRecordCopyAppointmentService.operateMedicalRecordAppointmentOrder(hospital, user, userMedicalRecordCopyAppointmentDTO);
    }

    @GetMapping("/orders/{id}/logistics/info")
    @ApiOperation("查询订单的物流信息")
    @ActionLog(action = "查询订单的物流信息")
    public List<LogisticsRouteInfoDTO> getLogisticsInfo(@ApiParam("病案复印预约订单id") @PathVariable long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return medicalRecordCopyAppointmentService.getLogisticsInfo(hospital, id);
    }

    @GetMapping("/bills")
    @ApiOperation("查询病案复印对账单")
    @ActionLog(action = "查询病案复印对账单")
    public Page<CopyAppointmentBillVM> getBills(
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("query=COPY_APPOINTMENT_ID:123 patientName:张三 idCard:1234(加密) startDate:2024-02-02 endDate:2024-02-02 orderNo:4321(第三方支付流水号)")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        PageRequest pageRequest = PageRequest.of(pageNo, size, Sort.Direction.DESC, "orderOperateTime");
        return medicalRecordCopyAppointmentService.getBills(hospital, query, pageRequest);
    }

    @GetMapping("/bills/stats")
    @ApiOperation("查询病案复印对账单统计")
    @ActionLog(action = "查询病案复印对账单统计")
    public CopyAppointmentBillStatsVM getBillsStats(
            @ApiParam("query=productId:123 patientName:张三 idCard:1234 startDate:2024-02-02 endDate:2024-02-02")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return medicalRecordCopyAppointmentService.getBillStats(hospital, query);
    }

    @GetMapping("/bills/export")
    @ApiOperation("查询病案复印对账单统计")
    @ActionLog(action = "查询病案复印对账单统计")
    public UploadVM exportCopyAppointmentBills(
            @ApiParam("query=productId:123 patientName:张三 idCard:1234 startDate:2024-02-02 endDate:2024-02-02")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return new UploadVM(medicalRecordCopyAppointmentService.exportCopyAppointmentBills(hospital, query));
    }

}
