package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.ExpressScene;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.DrugStoreService;
import cn.taihealth.ih.service.api.ExpressSenderInfoService;
import cn.taihealth.ih.service.dto.drugorder.LogisticsRouteInfo;
import cn.taihealth.ih.service.dto.express.ExpressData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/hospital/logistics")
@RequiredArgsConstructor
@Api(tags = "d端-物流相关信息", description = "d端-物流相关信息")
public class HospitalLogisticsResource {

    private final DrugStoreService drugStoreService;
    private final ExpressSenderInfoService expressSenderInfoService;

    @GetMapping("/info")
    @ApiOperation("根据物流公司和单号查询物流信息")
    public List<LogisticsRouteInfo> savePageSettings(
            @RequestParam(value = "trackingType", required = false) String trackingType,
            @RequestParam(value = "trackingNumber") String trackingNumber,
            @RequestParam(required = false) String phone) {
        Hospital hospital = CurrentHospital.getOrThrow();
        ExpressData expressData = expressSenderInfoService.getExpressSettings(hospital, ExpressScene.ONLINE_MEDICINE_ORDER);
        return drugStoreService.getLogisticsInfo(hospital.getCode(), trackingType, trackingNumber, phone, expressData);
    }


}
