package cn.taihealth.ih.rest.controller.copyappointment;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.repo.medicalrecordcopyappointment.MedicalRecordCopyPurposeScopeRelRepository;
import cn.taihealth.ih.repo.medicalrecordcopyappointment.MedicalRecordCopyScopeRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.medicalrecordcooyappointment.MedicalRecordCopyPurposeService;
import cn.taihealth.ih.service.api.medicalrecordcooyappointment.MedicalRecordCopyScopeService;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.MedicalRecordCopyPurposeDTO;
import cn.taihealth.ih.service.dto.medicalrecordcopyappointment.MedicalRecordCopyScopeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: jzs
 * @Date: 2023-10-18
 */
@RestController
@RequestMapping("/hospital/medical_record_copy_appointment/setting")
@Api(tags = "d端-病案设置管理")
@AllArgsConstructor
public class HospitalMedicalRecordSettingResource {

    private final MedicalRecordCopyScopeService medicalRecordCopyScopeService;
    private final MedicalRecordCopyPurposeService medicalRecordCopyPurposeService;
    private final MedicalRecordCopyScopeRepository medicalRecordCopyScopeRepository;
    private final MedicalRecordCopyPurposeScopeRelRepository medicalRecordCopyPurposeScopeRelRepository;

    @PostMapping("/copy_scope")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation("新建病案分类")
    @ActionLog(action = "新建病案分类")
    public MedicalRecordCopyScopeDTO createMedicalRecordCopyScope (@Valid @RequestBody MedicalRecordCopyScopeDTO medicalRecordCopyScopeDTO) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return medicalRecordCopyScopeService.createMedicalRecordCopyScope(hospital, medicalRecordCopyScopeDTO);
    }

    @PutMapping("/copy_scope")
    @ApiOperation("修改病案分类")
    @ActionLog(action = "修改病案分类")
    public MedicalRecordCopyScopeDTO updateMedicalRecordCopyScope (@RequestBody MedicalRecordCopyScopeDTO medicalRecordCopyScopeDTO) {
        return medicalRecordCopyScopeService.updateMedicalRecordCopyScope(medicalRecordCopyScopeDTO);
    }

    @GetMapping("/copy_scope_list")
    @ApiOperation("查询病案分类列表")
    @ActionLog(action = "查询病案分类列表")
    public Page<MedicalRecordCopyScopeDTO> getMedicalRecordCopyScopeList (
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("查询条件:NAME:分类名称 CODE:分类编码 CREATE_TIME:(创建日期)<=2023-01-01 10:00:00 CREATE_TIME:>=2023-01-01 10:00:00")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate");
        return medicalRecordCopyScopeService.queryMedicalRecordCopyScopeList(hospital, query, page);
    }

    @GetMapping("/enable/copy_scope_list")
    @ApiOperation("查询启用的病案分类列表")
    @ActionLog(action = "查询启用的病案分类列表")
    public List<MedicalRecordCopyScopeDTO> getMedicalRecordCopyScopeList() {
        Hospital hospital = CurrentHospital.getOrThrow();
        return medicalRecordCopyScopeRepository.findAllByHospitalAndEnabledIsTrueOrderByCreatedDateDesc(hospital)
                .stream().map(MedicalRecordCopyScopeDTO::new).collect(Collectors.toList());
    }

    @DeleteMapping("/copy_scope/{id}")
    @ApiOperation("删除病案分类")
    @ActionLog(action = "删除病案分类")
    public void deleteMedicalRecordCopyScope (@PathVariable("id") long id) {
        medicalRecordCopyPurposeScopeRelRepository.findFirstByCopyScopeId(id).ifPresent(medicalRecordCopyPurposeScopeRel -> {
            throw ErrorType.CAN_NOT_DELETE.toProblem("当前的病案分类正在被使用，无法删除");
        });
        medicalRecordCopyScopeRepository.delete(medicalRecordCopyScopeRepository.getById(id));
    }

    @PostMapping("/copy_purpose")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation("新建病案用途")
    @ActionLog(action = "新建病案用途")
    public MedicalRecordCopyPurposeDTO createMedicalRecordCopyPurpose (@Valid @RequestBody MedicalRecordCopyPurposeDTO medicalRecordCopyPurposeDTO) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return medicalRecordCopyPurposeService.createMedicalRecordCopyPurpose(hospital, medicalRecordCopyPurposeDTO);
    }

    @PutMapping("/copy_purpose")
    @ApiOperation("修改病案用途")
    @ActionLog(action = "修改病案用途")
    public MedicalRecordCopyPurposeDTO updateMedicalRecordCopyPurpose (@RequestBody MedicalRecordCopyPurposeDTO medicalRecordCopyPurposeDTO) {
        return medicalRecordCopyPurposeService.updateMedicalRecordCopyPurpose(medicalRecordCopyPurposeDTO);
    }

    @GetMapping("/copy_purpose_list")
    @ApiOperation("查询病案复印用途列表")
    @ActionLog(action = "查询病案复印用途列表")
    public Page<MedicalRecordCopyPurposeDTO> getMedicalRecordCopyPurposeList (
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("查询条件:NAME:用途名称 CREATE_TIME:(创建日期)<=2023-01-01 10:00:00 CREATE_TIME:>=2023-01-01 10:00:00")
            @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate");
        return medicalRecordCopyPurposeService.queryMedicalRecordCopyPurposeList(hospital, query, page);
    }

    @DeleteMapping("/copy_purpose/{id}")
    @ApiOperation("删除病案用途")
    @ActionLog(action = "删除病案用途")
    public void deleteMedicalRecordCopyPurpose (@PathVariable("id") long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        medicalRecordCopyPurposeService.deleteMedicalRecordCopy(hospital, id);
    }
}
