package cn.taihealth.ih.rest.controller.management;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.cloud.PlatformRole;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.repo.UserRepository;
import cn.taihealth.ih.repo.cloud.PlatformRoleRepository;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.SystemRoleAuthService;
import cn.taihealth.ih.service.api.UserService;
import cn.taihealth.ih.service.cache.RoleAuthCache;
import cn.taihealth.ih.service.dto.PlatformRoleDTO;
import cn.taihealth.ih.service.impl.filter.platformrole.PlatformRoleSearch;
import cn.taihealth.ih.service.vm.IhPage;
import cn.taihealth.ih.service.vm.RolePageVM;
import cn.taihealth.ih.service.vm.RolePageVM.Menu;
import cn.taihealth.ih.spring.security.UserRoleCodeName;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 */
@Slf4j
@RestController
@RequestMapping(value = "/admin/platform")
@Api(tags = "m端-角色权限", description = "m端-角色权限")
public class SystemPlatformRoleAuthResource {

    private final PlatformRoleRepository platformRoleRepository;
    private final SystemRoleAuthService systemRoleAuthService;
    private final UserService userService;
    private final UserRepository userRepository;

    public SystemPlatformRoleAuthResource(
        PlatformRoleRepository platformRoleRepository,
        UserRepository userRepository,
        SystemRoleAuthService systemRoleAuthService, UserService userService) {
        this.platformRoleRepository = platformRoleRepository;
        this.systemRoleAuthService = systemRoleAuthService;
        this.userService = userService;
        this.userRepository = userRepository;
    }

    @PostMapping("/roles")
    @ApiOperation("添加平台角色")
    @ActionLog(action = "添加平台角色")
    @ResponseStatus(HttpStatus.CREATED)
    public PlatformRoleDTO addRole(@RequestBody @Valid PlatformRoleDTO dto) {
        return systemRoleAuthService.addRole(dto);
    }

    @DeleteMapping("/roles/{roleId}")
    @ApiOperation("删除平台角色")
    @ActionLog(action = "删除平台角色")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteRole(@PathVariable long roleId) {
        systemRoleAuthService.deleteRole(roleId);
    }

    @PatchMapping("/roles/{roleId}")
    @ApiOperation("修改角色")
    @ActionLog(action = "修改角色")
    public PlatformRoleDTO editRole(@RequestBody PlatformRoleDTO dto) {
        return systemRoleAuthService.editRole(dto);
    }

    @GetMapping("/roles")
    @ApiOperation("查询角色")
    @ActionLog(action = "查询角色")
    public IhPage<PlatformRoleDTO> editRole(
        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @ApiParam("查询条件name:xxx") @RequestParam(required = false) String query) {
        List<Specification<PlatformRole>> sp = Lists.newArrayList();
        sp.add(PlatformRoleSearch.of(query).toSpecification());
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "createdDate", "id");
        return new IhPage<>(platformRoleRepository.findAll(Specifications.and(sp), page)
            .map(PlatformRoleDTO::new));
    }

    @GetMapping("/roles/{roleId}")
    @ApiOperation("获取角色权限菜单")
    @ActionLog(action = "获取角色权限菜单")
    public RolePageVM getRoleAuths(@PathVariable long roleId) {
        return systemRoleAuthService.getRoleAuths(roleId);
    }

    @PutMapping("/roles/{roleId}")
    @ApiOperation("修改角色权限菜单")
    @ActionLog(action = "修改角色权限菜单")
    public RolePageVM getRoleAuths(@PathVariable long roleId,
                                   @RequestBody RolePageVM vm) {
        vm.setId(roleId);
        systemRoleAuthService.saveRoleAuths(vm);
        return systemRoleAuthService.getRoleAuths(roleId);
    }

    @GetMapping("/menus")
    @ApiOperation("查询OMC用户菜单")
    @ActionLog(action = "查询OMC用户菜单")
    public Menu getMenus() {
//        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//        UserDetail userDetail = (UserDetail) authentication.getPrincipal();
//        if (userDetail.getCloudType() == 0) {
//            throw ErrorType.FORBIDDEN.toProblem();
//        }
        User user = CurrentUser.getOrThrow();
        List<UserRoleCodeName> roles = AppContext.getInstance(RoleAuthCache.class).getRoles(user.getUsername());
        if (roles.stream().anyMatch(u -> Objects.equals("ADMIN", u.getCode().toUpperCase()))) {
            log.info("超级管理查询菜单 ----- ");
            return systemRoleAuthService.getAllMenus();
        }
        if (CollectionUtils.isNotEmpty(user.getPlatformRoles())) {
            return systemRoleAuthService.getMenus(user.getPlatformRoles().stream().map(PlatformRole::getCode).collect(
                Collectors.toList()));
        }
        return new Menu();
    }

}
