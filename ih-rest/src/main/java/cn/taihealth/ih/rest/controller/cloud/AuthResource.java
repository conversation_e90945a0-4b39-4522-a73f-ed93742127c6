package cn.taihealth.ih.rest.controller.cloud;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.security.TokenValidator;
import cn.taihealth.ih.commons.util.IpUtils;
import cn.taihealth.ih.domain.SMSCode;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.PlatformTypeEnum;
import cn.taihealth.ih.domain.enums.SignupSource;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform.PlatformForEnum;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.rest.controller.RestPaths;
import cn.taihealth.ih.rest.error.Check;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.vm.PasswordNewVM;
import cn.taihealth.ih.service.vm.PasswordVM;
import cn.taihealth.ih.service.vm.SignupVM;
import cn.taihealth.ih.spring.security.authenticated.AuthenticatedUserToken;
import cn.taihealth.ih.spring.security.jwt.JWTAuthenticationProvider;
import cn.taihealth.ih.spring.security.jwt.JWTConfigurer;
import cn.taihealth.ih.spring.security.jwt.JWTToken;
import com.gitq.jedi.context.AppContext;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Objects;

/**
 *
 */
@RestController
@RequestMapping
@Api(tags = "用户的登录与注册", description = "用户的登录与注册")
public class AuthResource {

    private final AuthenticationManager authenticationManager;
    private final JWTAuthenticationProvider jwtAuthenticationProvider;
    private final UserService userService;
    private final TokenValidator smsTokenValidator;
    private final PasswordManager passwordEncoder;
    private final UserCacheFindService userCacheFindService;
    private final UserPlatformInfoService userPlatformInfoService;

    public AuthResource(AuthenticationManager authenticationManager,
                        JWTAuthenticationProvider jwtAuthenticationProvider,
                        UserService userService,
                        UserCacheFindService userCacheFindService,
                        TokenValidator smsTokenValidator,
                        PasswordManager passwordEncoder,
                        UserPlatformInfoService userPlatformInfoService) {
        this.authenticationManager = authenticationManager;
        this.jwtAuthenticationProvider = jwtAuthenticationProvider;
        this.userService = userService;
        this.userCacheFindService = userCacheFindService;
        this.smsTokenValidator = smsTokenValidator;
        this.passwordEncoder = passwordEncoder;
        this.userPlatformInfoService = userPlatformInfoService;
    }

    @PostMapping(RestPaths.SIGNUP)
    @Timed
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation("注册用户，不需要权限（创建用户05）")
    @Transactional
    public ResponseEntity<JWTToken> signup(HttpServletRequest request,
                                           @RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
                                           @ApiParam("注册信息") @Valid @RequestBody SignupVM vm) {

        User current = CurrentUser.getOrNull();
        Hospital hospital = CurrentHospital.getOrThrow();
        appId = checkAppId(hospital, appId);
        if (current != null) {
            throw ErrorType.ILLEGAL_STATE.toProblem("用户已登录", "用户已登录，请先登出后再注册");
        }
        // 微信登录 不需要密码
        if (StringUtils.isBlank(vm.getWechatCode())) {
            // 验证密码规则
            Check.passwordCheck(vm.getPassword());
        }
        boolean valid = smsTokenValidator
            .validate(vm.getMobile(), vm.getSmsCode(), vm.getCodeType().name());
        if (!valid) {
            throw ErrorType.INVALID_VALIDATION_TOKEN.toProblem();
        }

        String clientIp = IpUtils.realClientIp(request);

        String finalAppId = appId;
        userCacheFindService.findOneByUsername(vm.getUsername()).ifPresent(u -> {
            if (!CollectionUtils.isEmpty(userPlatformInfoService.getByUserAndAppId(u, finalAppId))) {
                throw ErrorType.USERNAME_ALREADY_USED.toProblem();
            }
        });
        userCacheFindService.findOneByMobile(vm.getMobile()).ifPresent(u -> {
            if (!CollectionUtils.isEmpty(userPlatformInfoService.getByUserAndAppId(u, finalAppId))) {
                throw ErrorType.MOBILE_ALREADY_USED.toProblem();
            }
        });
        // 该接口仅H5调用，如注册来源只会是H5
        vm.setSignupSource(SignupSource.H5_PATIENT);
        User newUser = userService.signup(appId, vm, clientIp, hospital);

        AuthenticatedUserToken internalUserToken = new AuthenticatedUserToken(
            newUser.getUsername(),
            newUser.getPassword());
        Authentication authentication = authenticationManager.authenticate(internalUserToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        JWTToken jwt = jwtAuthenticationProvider.createToken(authentication, true);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, "Bearer " + jwt.getIdToken());

        return new ResponseEntity<>(jwt, httpHeaders, HttpStatus.OK);
    }

    @PatchMapping("/reset-password")
    @Timed
    @ApiOperation("更改用户密码,通过手机呈更改，不需要权限")
    @Transactional
    public void resetPassword(@Valid @RequestBody PasswordVM vm) {
        User user = CurrentUser.getOrNull();
        Check.passwordCheck(vm.getNewPassword());
        if (user == null) {
            String smsCode = vm.getSmsCode();
            if (!smsTokenValidator
                .validate(vm.getMobile(), smsCode, SMSCode.CodeType.RESET_PASSWORD.name())) {
                throw ErrorType.INVALID_VALIDATION_TOKEN.toProblem();
            }

            user = userCacheFindService.findOneByMobile(vm.getMobile())
                .orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        } else {
            if (!Objects.equals(user.getMobile(), vm.getMobile())) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
        }

        userService.updatePassword(user, vm.getNewPassword());
    }

    @PatchMapping("/reset-new-password")
    @Timed
    @ApiOperation("更改用户密码，通过旧密码修改")
    @Transactional
    public void resetNewPassword(@Valid @RequestBody PasswordNewVM newvm) {
        User user = CurrentUser.getOrThrow();
        Check.passwordCheck(newvm.getNewPassword());
        if (!passwordEncoder.matches(newvm.getOldPassword(), user.getPassword())) {
            throw ErrorType.PASSWORD_NOT_MATCH.toProblem();
        }
        userService.updatePassword(user, newvm.getNewPassword());
    }

    private String checkAppId(Hospital hospital, String appId) {
        HospitalPublicPlatformService hospitalPublicPlatformService = AppContext.getInstance(
            HospitalPublicPlatformService.class);
        HospitalPublicPlatform platform;
        if (StringUtils.isBlank(appId)) {
            platform = hospitalPublicPlatformService.getPlatform(hospital, PlatformTypeEnum.OFFICIAL_ACCOUNT,
                                                                 PlatformForEnum.PATIENT);
        } else {
            platform = hospitalPublicPlatformService.getPlatform(hospital, appId);
        }
        if (platform == null) {
            throw ErrorType.DATA_ACCESS_ERROR.toProblem("医院" + hospital.getName() + "未配置公众平台，请检查");
        }
        return platform.getAppId();
    }

}
