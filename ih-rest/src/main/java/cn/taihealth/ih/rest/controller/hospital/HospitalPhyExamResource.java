package cn.taihealth.ih.rest.controller.hospital;

import cn.hutool.json.JSONUtil;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.DrugInstruction;
import cn.taihealth.ih.domain.PhyExam;
import cn.taihealth.ih.domain.VisitorPass;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Upload;
import cn.taihealth.ih.repo.DrugInstructionRepository;
import cn.taihealth.ih.repo.PhyExamRepository;
import cn.taihealth.ih.repo.UploadRepository;
import cn.taihealth.ih.repo.VisitorPassRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.ExportTaskService;
import cn.taihealth.ih.service.dto.DrugInstructionDTO;
import cn.taihealth.ih.service.dto.PhyExamDTO;
import cn.taihealth.ih.service.dto.VisitorPassDTO;
import cn.taihealth.ih.service.dto.export.PhyExamExportDTO;
import cn.taihealth.ih.service.impl.filter.visitorpass.VisitorPassSearch;
import cn.taihealth.ih.service.vm.DrugInstructionVM;
import cn.taihealth.ih.service.vm.IhPage;
import cn.taihealth.ih.service.vm.UploadVM;
import com.gitq.jedi.data.specification.Specifications;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: zihao
 */
@RestController
@RequestMapping("/hospital")
@Api(tags = "订单管理-体检订单", description = "订单管理-体检订单")
@Slf4j
@AllArgsConstructor
public class HospitalPhyExamResource {


    private final PhyExamRepository phyExamRepository;
    private final ExportTaskService exportTaskService;


    // 查询
    @GetMapping("/phyExams")
    @ApiOperation("订单管理-体检订单/查询(分页)")
    @ActionLog(action = "订单管理-体检订单/查询(分页)")
    public IhPage<PhyExamDTO> list(@ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                   @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
                                   @ApiParam("体检类别 PERSONAL 个人 ENTERPRISE 团体") @RequestParam(name = "attribute") PhyExam.Attribute attribute,
                                   @ApiParam(value = "查询条件，query=status:WAIT_EXAM examNo:23021" +
                                               " appointmentDate:2023-10-10 createdDate:2023-10-10 patName:李四 " +
                                               "patMobile:17605104078 体检套餐:mock数据 不传") @RequestParam(name = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Specification<PhyExam> specs = Specifications.and(Specifications.eq("hospital", hospital),
                Specifications.eq("attribute", attribute)
        );
        Pageable page = PageRequest.of(pageNo, size, Sort.Direction.DESC, "createdDate");
        log.info("体检订单（分页）查询 参数{}", JSONUtil.toJsonStr(specs));
        return new IhPage<>(phyExamRepository.findAll(specs, page).map(PhyExamDTO::new));
    }

    @GetMapping("/phyExams/export")
    @ApiOperation("订单管理-体检订单/查询(分页)")
    @ActionLog(action = "订单管理-体检订单/查询(分页)")
    public UploadVM export(
            @ApiParam("体检类别 PERSONAL 个人 ENTERPRISE 团体") @RequestParam(name = "attribute") PhyExam.Attribute attribute,
            @ApiParam(value = "查询条件，query=status:WAIT_EXAM examNo:23021" +
                    " appointmentDate:2023-10-10 createdDate:2023-10-10 patName:李四 " +
                    "patMobile:17605104078 体检套餐:mock数据 不传") @RequestParam(name = "query", required = false) String query,
            HttpServletResponse response) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Specification<PhyExam> specs = Specifications.and(Specifications.eq("hospital", hospital),
                Specifications.orderBy(Sort.Direction.DESC, "createdDate"), Specifications.eq("attribute", attribute));
        List<PhyExamExportDTO> data = phyExamRepository.findAll(specs).stream().map(PhyExamExportDTO::new).collect(Collectors.toList());
        return exportTaskService.exportPhyExam(data, response, attribute);
    }

}
