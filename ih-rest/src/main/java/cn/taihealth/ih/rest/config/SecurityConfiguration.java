package cn.taihealth.ih.rest.config;

import cn.taihealth.ih.service.api.PasswordManager;
import cn.taihealth.ih.service.config.IHFilterSecurityMetadataSource;
import cn.taihealth.ih.spring.security.Interceptor.DynamicallyUrlAccessDecisionManager;
import cn.taihealth.ih.spring.security.Interceptor.IHFilterSecurityInterceptor;
import cn.taihealth.ih.spring.security.authenticated.AuthenticatedUserProvider;
import cn.taihealth.ih.spring.security.jwt.JWTAuthenticationProvider;
import cn.taihealth.ih.spring.security.jwt.JWTConfigurer;
import cn.taihealth.ih.spring.security.sms.SMSAuthenticationProvider;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.vote.RoleVoter;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.data.repository.query.SecurityEvaluationContextExtension;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.firewall.StrictHttpFirewall;
import org.springframework.web.filter.CorsFilter;
import org.zalando.problem.spring.web.advice.security.SecurityProblemSupport;

import javax.annotation.PostConstruct;
import java.util.List;


/**
 */
@Configuration
@Import(SecurityProblemSupport.class)
public class SecurityConfiguration {

    private final AuthenticationManagerBuilder authenticationManagerBuilder;
    private final UserDetailsService domainUserDetailsService;
    private final JWTAuthenticationProvider jwtAuthenticationProvider;
    private final CorsFilter corsFilter;
    private final SecurityProblemSupport problemSupport;
    private final PasswordEncoder passwordEncoder;
    private final SMSAuthenticationProvider smsAuthenticationProvider;

    public SecurityConfiguration(AuthenticationManagerBuilder authenticationManagerBuilder,
                                 UserDetailsService domainUserDetailsService,
                                 PasswordEncoder passwordEncoder,
                                 JWTAuthenticationProvider jwtAuthenticationProvider,
                                 CorsFilter corsFilter, SecurityProblemSupport problemSupport,
                                 SMSAuthenticationProvider smsAuthenticationProvider) {
        this.authenticationManagerBuilder = authenticationManagerBuilder;
        this.domainUserDetailsService = domainUserDetailsService;
        this.passwordEncoder = passwordEncoder;
        this.jwtAuthenticationProvider = jwtAuthenticationProvider;
        this.corsFilter = corsFilter;
        this.problemSupport = problemSupport;
        this.smsAuthenticationProvider = smsAuthenticationProvider;
    }

    @PostConstruct
    public void init() {
        try {
            authenticationManagerBuilder.authenticationProvider(daoAuthenticationProvider())
                    .authenticationProvider(authenticatedUserProvider())
                    .authenticationProvider(smsAuthenticationProvider);
        } catch (Exception e) {
            throw new BeanInitializationException("Security configuration failed", e);
        }
    }

    /**
     * 配置WebSecurity：注册WebSecurityCustomizer的一个实例
     */
//    @Bean
//    public WebSecurityCustomizer webSecurityCustomizer() {
//        return web -> web.ignoring()
//                .antMatchers(HttpMethod.OPTIONS, "/**")
//                .antMatchers("/avatar/**");
//                .antMatchers("/i18n/**")
//                .antMatchers("/content/**")
//                .antMatchers("/h2-console/**");
//    }

    @Bean
    SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.logout().disable()
                .addFilterBefore(corsFilter, UsernamePasswordAuthenticationFilter.class)
                .exceptionHandling()
                .authenticationEntryPoint(problemSupport)
                .accessDeniedHandler(problemSupport)
                .and().csrf()
                .disable()
                .headers()
                .frameOptions()
                .disable()
                .and()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .apply(securityConfigurerAdapter());
        http.addFilterAfter(dynamicallyUrlInterceptor(), FilterSecurityInterceptor.class);
        return http.build();
    }

    @Bean
    public IHFilterSecurityInterceptor dynamicallyUrlInterceptor(){
        IHFilterSecurityInterceptor interceptor = new IHFilterSecurityInterceptor();
        interceptor.setSecurityMetadataSource(new IHFilterSecurityMetadataSource());

        //配置RoleVoter决策
        List<AccessDecisionVoter<?>> decisionVoters = Lists.newArrayList();
        decisionVoters.add(new RoleVoter());
        //设置认证决策管理器
        interceptor.setAccessDecisionManager(new DynamicallyUrlAccessDecisionManager(decisionVoters));
        return interceptor;
    }

    private JWTConfigurer securityConfigurerAdapter() {
        return new JWTConfigurer(jwtAuthenticationProvider);
    }

    @Bean
    public SecurityEvaluationContextExtension securityEvaluationContextExtension() {
        return new SecurityEvaluationContextExtension();
    }

    /**
     * 获取AuthenticationManager（认证管理器），登录时认证使用
     * @param authenticationConfiguration
     * @return
     * @throws Exception
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    //    @Bean
    public AuthenticatedUserProvider authenticatedUserProvider() {
        return new AuthenticatedUserProvider(domainUserDetailsService);
    }

    //    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setPasswordEncoder(passwordEncoder);
        daoAuthenticationProvider.setUserDetailsService(domainUserDetailsService);
        return daoAuthenticationProvider;
    }


    @Bean
    public PasswordManager passwordManager(PasswordEncoder encoder) {
        return new PasswordManager() {

            @Override
            public String encode(CharSequence rawPassword) {
                return encoder.encode(rawPassword);
            }

            @Override
            public boolean matches(CharSequence rawPassword, String encodedPassword) {
                return encoder.matches(rawPassword, encodedPassword);
            }
        };
    }

    @Bean
    public HttpFirewall httpFirewall() {
        StrictHttpFirewall firewall = new StrictHttpFirewall();
        firewall.setAllowUrlEncodedSlash(true);
        firewall.setAllowUrlEncodedDoubleSlash(true);
        firewall.setAllowBackSlash(true);
        firewall.setAllowSemicolon(true);
        firewall.setAllowUrlEncodedPercent(true);
        firewall.setAllowUrlEncodedPeriod(true);
        return firewall;
    }

}
