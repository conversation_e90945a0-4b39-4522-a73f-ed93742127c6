package cn.taihealth.ih.rest.controller.nodered;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.aop.logging.ElectronicHealthCardData;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.OkHttpUtils;
import cn.taihealth.ih.commons.util.TimeUtils;
import cn.taihealth.ih.domain.ElectronicMedicCard;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.InsurancePayMethod;
import cn.taihealth.ih.domain.enums.PayStatus;
import cn.taihealth.ih.domain.enums.ProjectTypeEnum;
import cn.taihealth.ih.domain.hospital.HospitalPublicPlatform;
import cn.taihealth.ih.domain.hospital.OfflineOrder;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.OrderExtraInfo;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.repo.ElectronicMedicCardRepository;
import cn.taihealth.ih.repo.PatientRepository;
import cn.taihealth.ih.repo.hospital.offline.OfflineDeptRepository;
import cn.taihealth.ih.repo.order.OfflineOrderRepository;
import cn.taihealth.ih.repo.order.OrderExtraInfoRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.api.offline.InpatientService;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.impl.order.BusinessOrderManager;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.InPatientApplyVM;
import cn.taihealth.ih.service.vm.OrderCheckItemVM;
import cn.taihealth.ih.service.vm.OrderVM;
import cn.taihealth.ih.service.vm.OutpatientChargeRefundedReq;
import cn.taihealth.ih.service.vm.business.HisInpatientChargeVM;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.*;
import cn.taihealth.ih.service.vm.nodered.nodereddatacontent.request.*;
import cn.taihealth.ih.wechat.service.vm.wechat.MedicalInsuranceParam;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jzs
 * @Date: 2023-09-06
 *
 */
@RestController
@RequestMapping(value = {"/user/pat_service/patients"})
@Api(tags = "His院内患者服务管理-患者端", description = "His院内患者服务管理-患者端")
@Slf4j
public class HisPatientController {

    private final PatientService patientService;
    private final PatientRepository patientRepository;
    private final ElectronicMedicCardRepository electronicMedicCardRepository;
    private final OrderRepository orderRepository;
    private final OrderExtraInfoRepository orderExtraInfoRepository;

    private final InpatientService inpatientService;
    private final OfflineOrderService offlineOrderService;
    private final OfflineOrderRepository offlineOrderRepository;
    private final OfflineDeptRepository offlineDeptRepository;
    private final LockService lockService;
    private final HospitalPublicPlatformService hospitalPublicPlatformService;

    private final String BEGINDATE = "19700101";

    public HisPatientController(PatientRepository patientRepository, OrderRepository orderRepository,
                                PatientService patientService,
                                OrderExtraInfoRepository orderExtraInfoRepository,
                                InpatientService inpatientService, OfflineOrderService offlineOrderService,
                                OfflineOrderRepository offlineOrderRepository, OfflineDeptRepository offlineDeptRepository,
                                ElectronicMedicCardRepository electronicMedicCardRepository,
                                HospitalPublicPlatformService hospitalPublicPlatformService,
                                LockService lockService) {
        this.patientRepository = patientRepository;
        this.orderRepository = orderRepository;
        this.orderExtraInfoRepository = orderExtraInfoRepository;
        this.inpatientService = inpatientService;
        this.offlineOrderService = offlineOrderService;
        this.offlineOrderRepository = offlineOrderRepository;
        this.offlineDeptRepository = offlineDeptRepository;
        this.electronicMedicCardRepository = electronicMedicCardRepository;
        this.lockService = lockService;
        this.hospitalPublicPlatformService = hospitalPublicPlatformService;
        this.patientService = patientService;
    }

    //4
    @GetMapping("/source_details")
    @ApiOperation("查询全院预约号源信息（含科室号源和医生号源）Q_GetSourceDetails")
    @ActionLog(action = "查询全院预约号源信息（含科室号源和医生号源）")
    public List<AppointmentInfo> getSourceDetails(
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate,
            @ApiParam("就诊渠道 0线下就诊 1线上就诊 -1全部 默认值为0，为空默认为0")
            @RequestParam(name="channelType", required = false, defaultValue = "0") String channelType,
            @RequestParam(name = "deptId", required = false) String deptId,
            @RequestParam(name = "doctorName", required = false) String doctorName) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getSourceDetails(hospital, beginDate, endDate, channelType, deptId, doctorName);
    }

    //5
    @GetMapping("/scheduling_list/{schedulingId}")
    @ApiOperation("查询指定排班序号的号源信息 Q_GetSchedulingListById")
    @ActionLog(action = "查询指定排班序号的号源信息（含预约科室号源、预约医生号源、当班科室号源、当班医生号源）")
    public List<SchedulingInfo> getSchedulingListById(
            @ApiParam("排序序号") @PathVariable("schedulingId") String schedulingId,
            @ApiParam("排班类型 1科室号源 2医生号源") @RequestParam(value = "sourceType") String sourceType,
            @ApiParam("就诊渠道 0线下就 1线上就诊 -1全部 默认值为0，为空默认为0")
            @RequestParam(name = "channelType", required = false, defaultValue = "0") String channelType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getSchedulingListById(hospital, schedulingId, sourceType, channelType);
    }

    //6
    @GetMapping("/scheduling_dept_list")
    @ApiOperation("查询预约出诊科室信息 Q_GetSchedulingDeptList")
    @ActionLog(action = "查询时间区间内的有排班的科室信息（包含专家出诊的科室）-根据条件可筛选出仅含有普通号号源的的科室")
    public List<SchedulingDeptInfo> getSchedulingDeptList(
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate,
            @ApiParam("就诊渠道 0线下就 1线上就诊 -1全部 默认值为0，为空默认为0")
            @RequestParam(name = "channelType", required = false, defaultValue = "0") String channelType,
            @ApiParam("排班类型 0普通 1专家 2特需 3远程门诊 4专病专家 5专病 6整合门诊 7中医膏方门诊 8普通医生门诊 9线上门诊")
            @RequestParam(name = "scheduleType", required = false) String scheduleType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        List<SchedulingDeptInfo> schedulingDeptInfoList = businessService
                .getSchedulingDeptList(hospital, beginDate, endDate, channelType);
        if (StringUtils.isNotBlank(scheduleType)) {
            schedulingDeptInfoList = schedulingDeptInfoList.stream()
                    .filter(u -> StringUtils.contains(u.getScheduling_type(), scheduleType)).collect(Collectors.toList());
        }
        // 获取已经手动关闭线下挂号的科室
        List<OfflineDept> offlineDeptList = offlineDeptRepository.findAllByHospitalAndEnabled(hospital, false);
        List<String> offlineDeptCodeList = offlineDeptList.stream().map(OfflineDept::getDeptCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(offlineDeptCodeList)) {
            // 小程序端左边一级科室用的是second_dept_id 右边的二级科室用的是dept_id,所以手动关闭科室的时候两级科室都要过滤
            schedulingDeptInfoList = schedulingDeptInfoList.stream()
                    .filter(schedulingDeptInfo -> StringUtils.isNotBlank(schedulingDeptInfo.getDept_id())
                            && !offlineDeptCodeList.contains(schedulingDeptInfo.getDept_id())
                            && StringUtils.isNotBlank(schedulingDeptInfo.getSecond_dept_id())
                            && !offlineDeptCodeList.contains(schedulingDeptInfo.getSecond_dept_id()))
                    .collect(Collectors.toList());
        }
        // 设置排序序号并排序
        List<OfflineDept> allDeptList = offlineDeptRepository.findAllByHospitalAndEnabled(hospital, true);
        if (CollectionUtils.isNotEmpty(allDeptList)) {
            Map<String, List<OfflineDept>> allDeptMap = allDeptList.stream().collect(Collectors.groupingBy(OfflineDept::getDeptCode));
            for (SchedulingDeptInfo schedulingDeptInfo : schedulingDeptInfoList) {
                schedulingDeptInfo.setFirstLevelOrder(0);
                schedulingDeptInfo.setSecondLevelOrder(0);
                schedulingDeptInfo.setLastLevelOrder(0);
                if (allDeptMap.containsKey(schedulingDeptInfo.getFirst_dept_id())) {
                    int orderValue = allDeptMap.get(schedulingDeptInfo.getFirst_dept_id()).get(0).getOrderValue();
                    schedulingDeptInfo.setFirstLevelOrder(orderValue);
                }
                if (allDeptMap.containsKey(schedulingDeptInfo.getSecond_dept_id())) {
                    int orderValue = allDeptMap.get(schedulingDeptInfo.getSecond_dept_id()).get(0).getOrderValue();
                    schedulingDeptInfo.setSecondLevelOrder(orderValue);
                }
                if (allDeptMap.containsKey(schedulingDeptInfo.getDept_id())) {
                    int orderValue = allDeptMap.get(schedulingDeptInfo.getDept_id()).get(0).getOrderValue();
                    schedulingDeptInfo.setLastLevelOrder(orderValue);
                }
            }
            schedulingDeptInfoList.sort(Comparator
                    .comparing(SchedulingDeptInfo::getFirstLevelOrder)
                    .thenComparing(SchedulingDeptInfo::getSecondLevelOrder)
                    .thenComparing(SchedulingDeptInfo::getLastLevelOrder).reversed());
        }
        return schedulingDeptInfoList;
    }

    @GetMapping("/scheduling_dept_list_test")
    @ApiOperation("查询预约出诊科室信息 Q_GetSchedulingDeptListTest")
    @ActionLog(action = "查询时间区间内的有排班的科室信息（包含专家出诊的科室）-根据条件可筛选出仅含有普通号号源的的科室")
    public List<SchedulingDeptInfo> getSchedulingDeptListTest(
        @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
        @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate,
        @ApiParam("就诊渠道 0线下就 1线上就诊 -1全部 默认值为0，为空默认为0")
        @RequestParam(name = "channelType", required = false, defaultValue = "0") String channelType,
        @ApiParam("排班类型 0普通 1专家 2特需 3远程门诊 4专病专家 5专病 6整合门诊 7中医膏方门诊 8普通医生门诊 9线上门诊")
        @RequestParam(name = "scheduleType", required = false) String scheduleType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        List<SchedulingDeptInfo> schedulingDeptInfoList = businessService
            .getSchedulingDeptListTest(hospital, beginDate, endDate, channelType);
        if (StringUtils.isNotBlank(scheduleType)) {
            schedulingDeptInfoList = schedulingDeptInfoList.stream()
                .filter(u -> StringUtils.contains(u.getScheduling_type(), scheduleType)).collect(Collectors.toList());
        }
        // 获取已经手动关闭线下挂号的科室
        List<OfflineDept> offlineDeptList = offlineDeptRepository.findAllByHospitalAndEnabled(hospital, false);
        List<String> offlineDeptCodeList = offlineDeptList.stream().map(OfflineDept::getDeptCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(offlineDeptCodeList)) {
            // 小程序端左边一级科室用的是second_dept_id 右边的二级科室用的是dept_id,所以手动关闭科室的时候两级科室都要过滤
            schedulingDeptInfoList = schedulingDeptInfoList.stream()
                .filter(schedulingDeptInfo -> StringUtils.isNotBlank(schedulingDeptInfo.getDept_id())
                    && !offlineDeptCodeList.contains(schedulingDeptInfo.getDept_id())
                    && StringUtils.isNotBlank(schedulingDeptInfo.getSecond_dept_id())
                    && !offlineDeptCodeList.contains(schedulingDeptInfo.getSecond_dept_id()))
                .collect(Collectors.toList());
        }
        // 设置排序序号并排序
        List<OfflineDept> allDeptList = offlineDeptRepository.findAllByHospitalAndEnabled(hospital, true);
        if (CollectionUtils.isNotEmpty(allDeptList)) {
            Map<String, List<OfflineDept>> allDeptMap = allDeptList.stream().collect(Collectors.groupingBy(OfflineDept::getDeptCode));
            for (SchedulingDeptInfo schedulingDeptInfo : schedulingDeptInfoList) {
                schedulingDeptInfo.setFirstLevelOrder(0);
                schedulingDeptInfo.setSecondLevelOrder(0);
                schedulingDeptInfo.setLastLevelOrder(0);
                if (allDeptMap.containsKey(schedulingDeptInfo.getFirst_dept_id())) {
                    int orderValue = allDeptMap.get(schedulingDeptInfo.getFirst_dept_id()).get(0).getOrderValue();
                    schedulingDeptInfo.setFirstLevelOrder(orderValue);
                }
                if (allDeptMap.containsKey(schedulingDeptInfo.getSecond_dept_id())) {
                    int orderValue = allDeptMap.get(schedulingDeptInfo.getSecond_dept_id()).get(0).getOrderValue();
                    schedulingDeptInfo.setSecondLevelOrder(orderValue);
                }
                if (allDeptMap.containsKey(schedulingDeptInfo.getDept_id())) {
                    int orderValue = allDeptMap.get(schedulingDeptInfo.getDept_id()).get(0).getOrderValue();
                    schedulingDeptInfo.setLastLevelOrder(orderValue);
                }
            }
            schedulingDeptInfoList.sort(Comparator
                    .comparing(SchedulingDeptInfo::getFirstLevelOrder)
                    .thenComparing(SchedulingDeptInfo::getSecondLevelOrder)
                    .thenComparing(SchedulingDeptInfo::getLastLevelOrder).reversed());
        }
        return schedulingDeptInfoList;
    }

    //8
    @GetMapping("/scheduling_dept_source_details/{deptId}")
    @ApiOperation("查询指定科室预约号源信息(普通号) Q_GetSchedulingDeptSourceDetails")
    @ActionLog(action = "查询指定科室预约号源信息(普通号)")
    public List<SchedulingDeptSourceInfo> getSchedulingDeptSourceDetails(
            @ApiParam("科室代码") @PathVariable("deptId") String deptId,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd 时间跨度最长1个月") @RequestParam("endDate") String endDate,
            @ApiParam("就诊渠道 0线下就 1线上就诊 -1全部 默认值为0，为空默认为0")
            @RequestParam(name = "channelType", required = false, defaultValue = "0") String channelType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getSchedulingDeptSourceDetails(hospital, beginDate, endDate, deptId, channelType);
    }

    //7
    @GetMapping("/scheduling_doctor_list/{deptId}")
    @ApiOperation("查询预约出诊医生信息 Q_GetSchedulingDoctorList")
    @ActionLog(action = "查询时间区间内的有排班的医生信息")
    public List<SchedulingDoctorInfo> getSchedulingDoctorList(
            @ApiParam("科室代码") @PathVariable("deptId") String deptId,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate,
            @ApiParam("就诊渠道 0线下就诊 1线上就诊 -1全部 默认值为0，为空默认为0")
            @RequestParam(name = "channelType", required = false, defaultValue = "0") String channelType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getSchedulingDoctorList(hospital, deptId, beginDate, endDate, channelType);
    }

    //9
    @GetMapping("/scheduling_doctor_source_details/{doctorId}")
    @ApiOperation("查询预约医生号源信息(专家号) Q_GetSchedulingDoctorSourceDetails")
    @ActionLog(action = "查询预约医生号源信息")
    public SchedulingDoctorSourceDetails getSchedulingDoctorSourceDetails(
            @ApiParam("医生代码") @PathVariable("doctorId") String doctorId,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd 时间跨度最长3个月") @RequestParam("endDate") String endDate,
            @ApiParam("就诊渠道 0线下就诊 1线上就诊 -1全部 默认值为0，为空默认为0")
            @RequestParam(name = "channelType", required = false, defaultValue = "0") String channelType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getSchedulingDoctorSourceInfo(hospital, beginDate, endDate, doctorId, channelType);
    }

    // 额外的接口
    @PostMapping("/appointment/register")
    @ApiOperation("患者预约挂号-1.查号源，2.锁号，3.预算。 挂号费为0时返回的状态是REGISTERED(待取号)或REFUND(已退款),挂号费不为0时返回的状态是WAIT_PAY(待支付)")
    @ActionLog(action = "创建预约挂号订单")
    @ElectronicHealthCardData(scene = "0101011", inputKey = {})
    public OfflineOrderDTO addAppointmentRegister(@RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
                                                  @RequestBody CreateOfflineOrderDTO createDTO) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        log.info("患者预约挂号, createDTO: {}", StandardObjectMapper.stringify(createDTO));
        ElectronicMedicCard card = electronicMedicCardRepository.getById(createDTO.getElectronicMedicCard().getId());
        Patient patient = patientService.getById(createDTO.getPatient().getId());
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current) || !Objects.equals(card.getPatient(), patient)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        String si = createDTO.getSchedulingDeptSourceInfo() == null ? null : createDTO.getSchedulingDeptSourceInfo().getScheduling_id();
        if (si == null) {
            si = createDTO.getSchedulingDoctorSourceInfo() == null ? null : createDTO.getSchedulingDoctorSourceInfo().getScheduling_id();
        }
        if (si != null) {
            String key = "lock.paymentIntervalLimit.user:" + user.getId() + ".outpatientRegisterFee." + si;
            if (!lockService.getLockWithoutUnlock(key, HospitalSettingsHelper.getLong(hospital, HospitalSettingKey.PAYMENT_INTERVAL_LIMIT))) {
                throw ErrorType.FREQUENTLY_OPENS.toProblem("操作太频繁，请稍后再试");
            }
        }
        MedicalInsuranceParam insuranceParam = createDTO.getInsuranceParam();
        if (insuranceParam == null) {
            insuranceParam = new MedicalInsuranceParam();
        }
        HospitalPublicPlatform publicPlatform = hospitalPublicPlatformService.getByAppId(appId).orElse(null);
        if (publicPlatform != null) {
            insuranceParam.setPlatformType(publicPlatform.getPlatformType());
        }
        return offlineOrderService.createOfflineOrder(hospital, createDTO);
    }

    /**
     * <pre>
     *     现在每次进入挂号医保支付页面没有每次都预算，
     *     为了解决如下问题：his到医保6201明细上传返回的payToken有效期2个小时，
     *     如果2小时后患者支付成功了，his医保查询支付结果无法调用问题，
     *     单独给医保挂号，加一个ih的挂号的预算接口，每次进入蓝色医保的预约挂号页面都要做挂号预算，
     *     保证每次进来后端都去his做预算，支付完成后可以及时查到结果
     * </pre>
     * @param appId
     * @param offlineOrderId
     * @param insuranceParam
     * @return
     */
    @PostMapping("/appointment/pre_register/{offlineOrderId}")
    @ApiOperation("门诊挂号预算。理论上挂号费为0的订单不会调用此接口")
    @ActionLog(action = "门诊挂号预算")
    public OfflineOrderDTO appointmentPreRegister(@RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
                                                  @PathVariable(name = "offlineOrderId") Long offlineOrderId,
                                                  @RequestBody MedicalInsuranceParam insuranceParam) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        OfflineOrder offlineOrder = offlineOrderRepository.getById(offlineOrderId);
        if (!Objects.equals(offlineOrder.getElectronicMedicCard().getPatient().getUser(), user)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (offlineOrder.getRegistrationFee() == 0) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("挂号费不能为0");
        }
        if (offlineOrder.getSelfFlag() == 1) {
            // 自费的不重新预算，直接返回
            return new OfflineOrderDTO(offlineOrder);
        }
        String key = "lock.paymentIntervalLimit.user:" + user.getId() + ".outpatientRegisterFee." + offlineOrder.getScheduleId();
        if (!lockService.getLockWithoutUnlock(key, HospitalSettingsHelper.getLong(hospital, HospitalSettingKey.PAYMENT_INTERVAL_LIMIT))) {
            throw ErrorType.FREQUENTLY_OPENS.toProblem("操作太频繁，请稍后再试");
        }
        if (insuranceParam == null) {
            insuranceParam = new MedicalInsuranceParam();
        }
        HospitalPublicPlatform publicPlatform = hospitalPublicPlatformService.getByAppId(appId).orElse(null);
        if (publicPlatform != null) {
            insuranceParam.setPlatformType(publicPlatform.getPlatformType());
        }
        return offlineOrderService.preRegister(hospital, offlineOrder, insuranceParam);
    }

    @PostMapping("/outpatient/appointment/register")
    @ApiOperation("门诊预约登记-1.查号源，2.锁号，3.预约登记")
    @ActionLog(action = "门诊预约登记")
    public OfflineOrderDTO addOutPatientAppointmentRegister(
            @RequestBody CreateOfflineOrderDTO createOfflineOrderDTO) {
        ElectronicMedicCard card = electronicMedicCardRepository.getById(createOfflineOrderDTO.getElectronicMedicCard().getId());
        Patient patient = patientService.getById(createOfflineOrderDTO.getPatient().getId());
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current) || !Objects.equals(card.getPatient(), patient)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return offlineOrderService.addOutPatientAppointmentRegister(CurrentHospital.getOrThrow(), createOfflineOrderDTO);
    }

    @PostMapping("/outpatient/pre_register/{orderId}")
    @ApiOperation("预约登记转预算 挂号费为0时返回的状态是REGISTERED(待取号)或REFUND(已退款),挂号费不为0时返回的状态是WAIT_PAY(待支付)")
    @ActionLog(action = "预约登记转预算")
    public OfflineOrderDTO outPatientRreRegister(@RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
                                                 @ApiParam("订单id") @PathVariable("orderId") Long orderId,
                                                 @RequestBody(required = false) MedicalInsuranceParam vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Patient patient = offlineOrderRepository.findById(orderId)
                .map(u -> patientService.getById(Long.parseLong(u.getPatientId())))
                .orElseThrow(() -> ErrorType.ILLEGAL_PARAMS.toProblem("订单不存在"));
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        String key = "lock.paymentIntervalLimit.user:" + user.getId() + ".outpatientRegisterFee." + orderId;
        if (!lockService.getLockWithoutUnlock(key, HospitalSettingsHelper.getLong(hospital, HospitalSettingKey.PAYMENT_INTERVAL_LIMIT))) {
            throw ErrorType.FREQUENTLY_OPENS.toProblem("操作太频繁，请稍后再试");
        }
        if (vm == null) {
            vm = new MedicalInsuranceParam();
        }
        HospitalPublicPlatform publicPlatform = hospitalPublicPlatformService.getByAppId(appId).orElse(null);
        if (publicPlatform != null) {
            vm.setPlatformType(publicPlatform.getPlatformType());
        }
        return offlineOrderService.outPatientRreRegister(hospital, orderId, vm);
    }

//    @PutMapping("/appointment/register/offline_order")
//    @ApiOperation("更新预约挂号订单")
//    @ActionLog(action = "更新预约挂号订单")
//    public OfflineOrderVM addAppointmentRegister(
//        @RequestBody UpdateOfflineOrderVM updateOfflineOrderVM) {
//        return offlineOrderService.updateOfflineOrder(CurrentHospital.getOrThrow(), updateOfflineOrderVM);
//    }

    @PostMapping("/appointment/register/refund")
    @ApiOperation("患者预约挂号退款")
    @ActionLog(action = "患者预约挂号退款")
    @ElectronicHealthCardData(scene = "0101057", inputKey = {"createOfflineOrderDTO"})
    public void appointmentRegisterRefund(
            @RequestHeader(name = Constants.X_IH_APPID, required = false) String appId,
            @RequestBody CreateOfflineOrderDTO createOfflineOrderDTO) {
        log.info("患者预约挂号退款, createOfflineOrderDTO: {}", StandardObjectMapper.stringify(createOfflineOrderDTO));

        OfflineOrder offlineOrder= offlineOrderRepository.getById(createOfflineOrderDTO.getId());
        if (OfflineOrder.OutPatientStatus.REGISTERED != offlineOrder.getStatus()
                && OfflineOrder.OutPatientStatus.WAITTREAT != offlineOrder.getStatus()) {
            if (!(offlineOrder.getInternetPayStatus() == PayStatus.SUCCESS && offlineOrder.getHisPayStatus() == PayStatus.FAIL)) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确");
            }
        }

        Patient patient = patientService.getById(Long.parseLong(offlineOrder.getPatientId()));
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        MedicalInsuranceParam insuranceParam = createOfflineOrderDTO.getInsuranceParam();
        if (insuranceParam == null) {
            insuranceParam = new MedicalInsuranceParam();
        }
        HospitalPublicPlatform publicPlatform = hospitalPublicPlatformService.getByAppId(appId).orElse(null);
        if (publicPlatform != null) {
            insuranceParam.setPlatformType(publicPlatform.getPlatformType());
        }
        offlineOrderService.refundingAppointmentRegisterCharge(createOfflineOrderDTO.getId(), InsurancePayMethod.CASH_ONLY, insuranceParam, null);
    }

    // 这个接口要加的话，放到admin权限中去，不要放在user权限里
//    @PostMapping("/appointment/register/refund_without_check")
//    @ApiOperation("患者预约挂号退款-不做状态检查")
//    @ActionLog(action = "患者预约挂号退款-不做状态检查")
//    public void  appointmentRegisterRefundUncheck(
//        @RequestBody CreateOfflineOrderDTO createOfflineOrderDTO) {
//        offlineOrderService.refundingAppointmentRegisterChargeUnCheck(createOfflineOrderDTO.getId());
//    }

    @GetMapping("/appointment/offline_order/{orderId}/detail")
    @ApiOperation("患者挂号订单详情查询")
    @ActionLog(action = "患者挂号订单详情查询")
    public OfflineOrderDTO getOfflineOrderDetail(
        @PathVariable("orderId") Long orderId) {
        OfflineOrder offlineOrder = offlineOrderRepository.getById(orderId);
        Patient patient = patientService.getById(Long.parseLong(offlineOrder.getPatientId()));
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (System.currentTimeMillis() - offlineOrder.getUpdatedDate().getTime() < 120_000) {
            Hospital hospital = CurrentHospital.getOrThrow();
            offlineOrder = AppContext.getInstance(BusinessOrderManager.class).checkAndPay(hospital, offlineOrder);
        }
        return new OfflineOrderDTO(offlineOrder);
    }

    @GetMapping("/appointment/offline_order/{orderId}/insurance/detail")
    @ApiOperation("患者挂号订单详情查询-医保专用，仅用于查询支付是否成功（根据status判断）")
    @ActionLog(action = "患者挂号订单详情查询-医保专用")
    public OfflineOrderDTO getOfflineOrderDetailWithInsurance(
        @PathVariable("orderId") Long orderId) {
        return offlineOrderService.getOfflineOrderDetailWithInsurance(orderId);
    }

    //10
//    @GetMapping("/Q_GetPatientAppointment")
//    @ApiOperation("查询患者预约记录")
//    @ActionLog(action = "查询患者预约记录")
//    public List<PatientAppointmentInfo> getPatientAppointment(
//            @ApiParam("患者姓名") @RequestParam("patientName") String patientName,
//            @ApiParam("门诊patid 通过患者信息查询接口获取") @RequestParam("patientId") String patientId,
//            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("begin_date") String beginDate,
//            @ApiParam("结束日期 格式yyyyMMdd 时间跨度最长3个月") @RequestParam("end_date") String endDate,
//            @ApiParam("当前操作员标志 0仅查询当前预约途径预约记录，1不关联预约途径(含其他预约途径)。默认0")
//            @RequestParam(name = "routeFlag", required = false, defaultValue = "0") String routeFlag,
//            @ApiParam("日期类型 开始结束日期的类型 0以预约操作日期查询，1以预约就诊日期查询（默认为0，传空则默认0）为空则代表预约操作日期")
//            @RequestParam(name = "dateType", required = false, defaultValue = "0") String dateType) {
//        Hospital hospital = CurrentHospital.getOrThrow();
////        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getPatientAppointment(hospital, patientName, patientId, beginDate, endDate, routeFlag, dateType);
//    }

    //12
    @PostMapping("/{patientId}/cancel/appointment/{appointmentId}")
    @ApiOperation(value = "门诊取消预约 B_CancelAppointment")
    @ActionLog(action = "通过门诊患者唯一号和预约序号取消预约 B_CancelAppointment")
    public void cancelAppointment(
            @ApiParam("预约序号 标记唯一一条预约记录的唯一号，由《门诊预约登记》反馈此字段") @PathVariable("appointmentId") long appointmentId,
            @ApiParam("就诊人id") @PathVariable("patientId") long patientId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.getById(patientId);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        OfflineOrder offlineOrder = offlineOrderRepository.getById(appointmentId);
        if (!Objects.equals(patientId + "", offlineOrder.getPatientId())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        offlineOrderService.cancelRegister(hospital, patient, appointmentId);
    }

    @PostMapping("/cancel/appointment/{offlineOrderId}")
    @ApiOperation(value = "预约登记患者取消门诊预约(订单未支付时) B_CancelAppointment")
    @ActionLog(action = "预约登记患者取消门诊预约(订单未支付时) B_CancelAppointment")
    public void cancelOutPatientAppointment(
            @ApiParam("订单Id") @PathVariable("offlineOrderId") long offlineOrderId) {
        // TODO 如果是预约登记，需要调用his的B_CancelAppointment接口，来取消his的接口，等测试测反向流程时需要实现
        Hospital hospital = CurrentHospital.getOrThrow();
        OfflineOrder offlineOrder = offlineOrderRepository.findById(offlineOrderId)
                .orElseThrow(() -> ErrorType.BAD_REQUEST_ERROR.toProblem("订单不存在"));
        if (offlineOrder.getType() != ProjectTypeEnum.OUTPATIENT_APPOINTMENT_REGISTRATION) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单类型不正确");
        }
        if (offlineOrder.getStatus() != OfflineOrder.OutPatientStatus.WAIT_PAY) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("订单状态不正确");
        }
        Patient patient = patientRepository.getById(Long.parseLong(offlineOrder.getPatientId()));
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        offlineOrderService.cancelAppointment(hospital, offlineOrderId);
    }

    //13
    @GetMapping("/{id}/orders")
    @ApiOperation("查询患者挂号记录  Q_GetRegistListByPatId")
    @ActionLog(action = "查询患者挂号记录")
    @ElectronicHealthCardData(scene = "0101013", inputKey = {"id"})
    public List<PatientRegistInfoVM> getRegisterListByPatId(
            @ApiParam("当前患者id") @PathVariable("id") Long id,
            @ApiParam("订单状态（orderStatusContains:\" 待支付:WAIT_PAY 已取消:CANCELLED 已关闭:CLOSED 已完成:COMPLETED 待取号:REGISTERED 退款中:REFUNDING 已退款:REFUND\"）" +
                    "根据orderStatus的枚举和需求上需要的传入就行可多传）") @RequestParam(value = "orderStatusContains", required = false) String orderStatusContains,
            @ApiParam("就诊卡号") @RequestParam(value = "cardId", required = false) Long cardId) {
        // 2023年10月21日10:08:50 改造该查询，逻辑上查询我侧数据，再将匹配的his数据覆盖至我侧数据返回
        // 2023年10月21日11:14:08 分配任务干业务开发（D端管理）去了，现在不改这个。
        // 2023年10月24日10:10:00 要来的总要来，继续改造
        // 依据：当用户进行支付的时候，存在两种情况
        //1. 用户点击微信关闭按钮，放弃支付
        //2. 用户输入支付密码进行支付，因为这是一个分布式事务，第一步，微信支付成功。 第二步，数据传送到HIS，HIS进行处理，但是处理有可能失败，目前的处理方式是自动回滚，这没有问题。
        //但是在我的挂号，”待支付“ 状态中需要显示，我侧存储的订单，因为只有这样，才可以兼容上述两种情况
        // 结论：现在挂号订单以我侧为主，HIS为辅，HIS的数据只是用来覆盖我侧的数据，不会影响我侧的数据
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.getById(id);
        // log记录患者name  id patid
        log.info("患者姓名：{}，患者id：{}，患者patid：{}", patient.getName(), patient.getId(), patient.getHisPatid());
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        List<PatientRegistInfoVM> orders = offlineOrderService.getPatientRegistInfo(patient, orderStatusContains, cardId, hospital);
        for (int i = 0; i < orders.size(); i++) {
            PatientRegistInfoVM order = orders.get(i);
            if (order.getId() == null) {
                order.setId((long) (i + 1));
            }
        }
        return orders;
    }

    //14
    @GetMapping("/order/{id}")
    @ApiOperation("查询患者预约挂号信息 Q_GetRegistListByRegno")
    @ActionLog(action = "查询患者预约挂号信息")
    public PatientRegistInfo getRegistListByRegno(@ApiParam("订单id") @PathVariable("id") Long id) {
        OfflineOrder order = offlineOrderRepository.getById(id);
        ElectronicMedicCard card = order.getElectronicMedicCard();
        Patient patient = card.getPatient();
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        // 患者姓名
        String patientName = patient.getName();
        // 门诊patid 门诊患者唯一号
        String hisPatid = card.getHisPatid();
        // 门诊挂号序号 与预约序号 不能同步为空
        String regno = order.getRegNo();
        // 预约序号 门诊挂号序号 不能同步为空
        String appointmentId = order.getAppointmentId();
        // 当前操作员标志 0仅查询当前挂号途径挂号记录，1不关联挂号途径(含其他挂号途径)。默认0
        String routeFlag = "1";
        if (StringUtils.isBlank(regno) && StringUtils.isBlank(appointmentId)) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("门诊挂号序号与预约序号不能同步为空");
        }
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getRegistListByRegno(hospital, patientName, regno, appointmentId, hisPatid, routeFlag);
    }

    // 前端所需要的额外接口
    @DeleteMapping("/order/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("删除患者挂号信息")
    public void deleteOrderById(@ApiParam("订单id") @PathVariable("id") Long id) {
        OfflineOrder order = offlineOrderRepository.getById(id);
        Patient patient = patientRepository.getById(Long.parseLong(order.getPatientId()));
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        offlineOrderService.deleteOfflineOrder(id);
    }

    //15
    @GetMapping("/{patientId}/patient_register_waiting_list")
    @ApiOperation("查询患者当前候诊信息 Q_GetPatientRegistWaitingListByPatId")
    @ActionLog(action = "通过门诊患者唯一号查询患者当前候诊信息")
    public List<PatientRegistWaitingInfo> getPatientRegisterWaitingListByPatId(@ApiParam("患者Id") @PathVariable("patientId") Long patientId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getPatientRegistWaitingListByPatId(hospital, patient.getName(), patient.getHisPatid());
    }

    //16
    @GetMapping("/Q_GetPatientDrugWaitingList")
    @ApiOperation("查询患者取药排队信息")
    @ActionLog(action = "查询患者取药排队信息")
    public List<PatientDrugWaitingInfo> getPatientDrugWaitingList(
            @ApiParam("患者姓名") @RequestParam("patientName") String patientName,
            @ApiParam("门诊patid 门诊患者唯一号") @RequestParam("patientId") String patientId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User current = CurrentUser.getOrThrow();
        if (!havePidAuth(patientId, current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
//        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getPatientDrugWaitingList(hospital, patientName, patientId);
    }

    //17
    @GetMapping("/Q_GetOutpatientItemHint")
    @ApiOperation("查询门诊项目提示信息")
    @ActionLog(action = "查询门诊项目提示信息")
    public List<OutpatientItemHintInfo> getOutpatientItemHint(
            @ApiParam("门诊patid 门诊患者唯一号") @RequestParam("patientId") String patientId,
            @ApiParam("结算收据号") @RequestParam("settleId") String settleId) {
        Hospital hospital = CurrentHospital.getOrThrow();
//        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getOutpatientItemHint(hospital, patientId, settleId);
    }

    //18
    @GetMapping("/Q_GetPatientSignInfo")
    @ApiOperation("查询患者到诊信息 返回该预约渠道预约的患者到诊记录，仅支持查询一天。")
    @ActionLog(action = "查询患者到诊信息 返回该预约渠道预约的患者到诊记录，仅支持查询一天。")
    public List<PatientSignInfo> getPatientSignList(
            @ApiParam("查询日期 仅支持查询一天 格式yyyyMMdd") @RequestParam("date") String date) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getPatientSignInfo(hospital, date);
    }

    //19
    @GetMapping("/outpatient_recipes/{patientId}")
    @ApiOperation("查询门诊患者处方列表 Q_GetOutpatientRecipes")
    @ActionLog(action = "查询门诊患者处方列表")
    public List<OutpatientRecipeInfo> getOutpatientRecipeList(
            @ApiParam("就诊人id") @PathVariable("patientId") Long patientId,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate,
            @ApiParam("医保入参 医保端所需参数，自费病人为空，此参数仅限支持医保脱卡支付医院(统一采用BASE64编码转换)，具体参数格式根据各地医保参照附件说明")
            @RequestParam(value = "insurance_param", required = false) String insuranceParam) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getOutpatientRecipeList(hospital, patient.getName(), patient.getHisPatid(), beginDate, endDate, insuranceParam);
    }

    @GetMapping("/recipe_list_renewable/{patient_id}")
    @ApiOperation("查询患者可续方处方信息 Q_GetPatientRecipeListRenewable")
    @ActionLog(action = "查询患者可续方处方信息")
    public List<RecipeInfo> getPatientRecipeListRenewable(
            @ApiParam("就诊人id") @PathVariable("patient_id") Long patient_id,
            @ApiParam("开始日期 格式yyyyMMdd 以开方日期为条件") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd 以开方日期为条件") @RequestParam("endDate") String endDate,
            @ApiParam("医保入参 医保端所需参数，自费病人为空，此参数仅限支持医保脱卡支付医院(统一采用BASE64编码转换)，具体参数格式根据各地医保参照附件说明")
            @RequestParam(value = "insurance_param", required = false) String insuranceParam) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getPatientRecipeListRenewable(hospital, patient, beginDate, endDate, insuranceParam);
    }

    //20
    @GetMapping("/inpatient_records")
    @ApiOperation("查询患者住院就诊记录 Q_GetInpatientRecord")
    @ActionLog(action = "查询患者住院就诊记录")
    @ElectronicHealthCardData(scene = "0101085", inputKey = {"patientId"})
    public List<InpatientRecord> getInpatientRecordList(
            @ApiParam("就诊人id") @RequestParam(value = "patientId", required = false) Long patientId,
            @ApiParam("就诊卡号") @RequestParam(value = "cardNo", required = false) String cardNo,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam(name = "beginDate", required = false) String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam(name = "endDate", required = false) String endDate,
            @ApiParam("在院状态 0全部 1仅在院") @RequestParam("status") String status) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = null;
        if (patientId != null) {
            patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        }
        if (StringUtils.isNotBlank(cardNo)) {
            ElectronicMedicCard card = AppContext.getInstance(ElectronicMedicCardRepository.class)
                    .findByNumberAndCardTypeAndPatientHospitalAndOnlineTypeAndPatient(cardNo, ElectronicMedicCard.CardType.SELF_PAY,
                            hospital, ElectronicMedicCard.OnlineType.HIS, patient).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
            patient = card.getPatient();
        }
        if (patient == null) {
            throw ErrorType.PATIENT_NOT_EXISTED.toProblem();
        }
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        String patientName = patient.getName();
        String hisPatientId = patient.getHisPatid();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getInpatientRecordList(hospital, patientName, hisPatientId, beginDate, endDate, status);
    }

    @GetMapping("/inpatient_records/hospno/{hospno}")
    @ApiOperation("根据住院号查询患者住院就诊记录-扫码缴费 Q_GetInpatientRecord")
    @ActionLog(action = "根据住院号查询患者住院就诊记录-扫码缴费")
    public List<InpatientRecord> getInpatientRecordList(
            @ApiParam("住院号") @PathVariable("hospno") String hospNo,
            @ApiParam("患者姓名") @RequestParam(value = "patientName") String patientName,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam(name = "beginDate", required = false) String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam(name = "endDate", required = false) String endDate,
            @ApiParam("在院状态 0全部 1仅在院") @RequestParam("status") String status) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getInpatientRecordList(hospital, patientName, null, hospNo, beginDate, endDate, status);
    }

    //21
    @GetMapping("/pre_payment_info/{patient_id}/{reg_no}")
    @ApiOperation("查询住院患者预交金汇总信息")
    @ActionLog(action = "查询住院患者预交金汇总信息")
    public InpatientAdvanceCharge getInpatientAdvanceCharge(
            @ApiParam("就诊人id") @PathVariable("patient_id") Long patient_id,
            @ApiParam("住院号") @PathVariable("reg_no") String reg_no) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        String patientName = patient.getName();
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getInpatientAdvanceCharge(hospital, patientName, reg_no);
    }

    //22
    @GetMapping("/pre_payment_history/{patient_id}/{reg_no}")
    @ApiOperation("查询住院患者预交金明细")
    @ActionLog(action = "查询住院患者预交金明细")
    @ElectronicHealthCardData(scene = "0101054", inputKey = {"patient_id"})
    public List<InpatientAdvanceChargeDetail> getInpatientAdvanceChargeDetailList(
            @ApiParam("就诊人id") @PathVariable("patient_id") Long patient_id,
            @ApiParam("住院号") @PathVariable("reg_no") String reg_no,
            @ApiParam("患者姓名") @RequestParam(name = "patientName", required = false) String patientName) {
        Hospital hospital = CurrentHospital.getOrThrow();
        if (patient_id != 0L) {
            Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
            patientName = patient.getName();
            User current = CurrentUser.getOrThrow();
            if (!Objects.equals(patient.getUser(), current)) {
                throw ErrorType.FORBIDDEN.toProblem();
            }
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getInpatientAdvanceChargeDetailList(hospital, patientName, reg_no);
    }

    //23
    @PostMapping("/inpatient_hospital_pre_charge")
    @ApiOperation(value = "住院预交金预充值")
    @ActionLog(action = "住院预交金预充值")
    @ElectronicHealthCardData(scene = "0101056", inputKey = {})
    public HisInpatientChargeVM inpatientHospCardPreCharge(
            @Valid @RequestBody InpatientHospitalPreChargeReq req) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User current = CurrentUser.getOrThrow();
        Patient patient;
        if (req.getPatientId() != null) {
            patient = patientRepository.findById(req.getPatientId()).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        } else {
            // 扫码充值 获取扫码缴费专用就诊人
            patient = inpatientService.getQrCodePatient(hospital);
        }
        return inpatientService.inpatientPreCharge(current, hospital, patient, req);
    }

    @GetMapping("/inpatient_hospital_pre_charge/{id}")
    @ApiOperation("查询住院患者预交金订单状态（包含支付状态）")
    @ActionLog(action = "查询住院患者预交金汇总信息")
    public HisInpatientChargeVM getInpatientAdvanceCharge(@ApiParam("订单id") @PathVariable("id") Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User current = CurrentUser.getOrThrow();
        return inpatientService.getInpatientPreChargeInfo(current, hospital, id);
    }

    //25
    @PostMapping("/hospital_admission_info/{card_id}")
    @ApiOperation("住院病人入院单信息填写")
    @ActionLog(action = "患者入院信息登记，用于患者自行填写的信息回写医院系统")
    public SupplyHospitalAdmissionCertificateResult saveSupplyHospitalAdmissionCertificate(
            @ApiParam("就诊卡id") @PathVariable("card_id") long card_id,
            @Valid @RequestBody SupplyHospitalAdmissionCertificateReq supplyHospitalAdmissionCertificate) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.saveSupplyHospitalAdmissionCertificate(hospital, card_id,supplyHospitalAdmissionCertificate);
    }

    //26
    @PutMapping("/B_UpdateHospitalAdmissionCertificate")
    @ApiOperation("住院病人入院单信息更新")
    @ActionLog(action = "患者入院信息登记，用于患者自行填写的信息更新至医院系统")
    public ReponseResult updateHospitalAdmissionCertificate(
            @Valid @RequestBody HospitalAdmissionCertificateReq param) {
        if (StringUtils.isBlank(param.getAdmission_no()) && StringUtils.isBlank(param.getRegno())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("入院单号与住院号不可同时为空");
        }
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.updateHospitalAdmissionCertificate(hospital, param);
    }

    //27
    @GetMapping("/B_CheckHospitalAdmissionOnline")
    @ApiOperation("线上入院判断")
    @ActionLog(action = "患者线上入院登记确认，用于判断患者是否可以在线上进行登记，如果患者必须去窗口进行人工审核，则返回对应错误提示信息\n" +
            "限医院中有部分入院患者无法在线入院的情况时使用，如果医院完全不支持线上入院，统一返回false，并给出对应提示信息。")
    public ReponseResult checkHospitalAdmissionOnline(
            @ApiParam("患者姓名") @RequestParam("patientName") String patientName,
            @ApiParam("住院号") @RequestParam(value = "regno", required = false) String regNo,
            @ApiParam("入院单号") @RequestParam("admission_no") String admission_no,
            @ApiParam("患者唯一号") @RequestParam(value = "patid", required = false) String patientId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.checkHospitalAdmissionOnline(hospital, patientName, regNo, admission_no, patientId);
    }

    //28
    // B_SaveHospitalAdmission
    @PutMapping("/hospital_admission")
    @ApiOperation("入院登记提交")
    @ActionLog(action = "患者提交入院信息，如自费患者此时HIS将患者填写的入院单扩展信息中的个人信息保存至住院病人首页库，" +
            "执行登记操作。如医保患者等需要线下确认的患者，此接口应报错并给与相关提示")
    public HospitalAdmission addHospitalAdmission(@RequestBody HospitalAdmissionReq param) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.addHospitalAdmission(hospital, param);
    }

    //28
    @GetMapping("/{patientId}/inpatient_fee_detail")
    @ApiOperation("查询住院患者费用明细信息 Q_GetInpatientFeeDetail")
    @ActionLog(action = "查询日期区间内住院患者费用清单明细信息")
    public List<InpatientFeeDetail> getInpatientFeeDetails(
            @ApiParam("患者id") @PathVariable("patientId") long patientId,
            @ApiParam("住院号") @RequestParam(value = "regno") String regNo,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getInpatientFeeDetails(hospital, patient.getName(), regNo, beginDate, endDate);
    }

    //30
    @GetMapping("/inpatient_daily_list/{patient_id}/{reg_no}")
    @ApiOperation("查询住院患者一日清单 Q_GetInpatientDailyList")
    @ActionLog(action = "查询住院患者一日清单")
    public List<InpatientDaily> getInpatientDaily(
            @ApiParam("就诊人id") @PathVariable("patient_id") long patient_id,
            @ApiParam("住院号") @PathVariable("reg_no") String reg_no,
            @ApiParam("查询日期 格式yyyyMMdd") @RequestParam("date") String date,
            @ApiParam("是否按数量汇总，0不汇总1数量汇总")
            @RequestParam(value = "summary_flag", required = false, defaultValue = "0") String summary_flag) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getInpatientDaily(hospital, patient.getName(), reg_no, date, summary_flag);
    }

    //31
    @GetMapping("/{patientId}/inpatient_settle")
    @ApiOperation("查询住院患者结算记录 Q_GetInpatientSettleInfo")
    @ActionLog(action = "查询日期区间内住院患者费用结算信息")
    public List<InpatientSettleInfo> getInpatientSettlelist(
            @ApiParam("患者id") @PathVariable("patientId") long patientId,
            @ApiParam("住院号") @RequestParam(value = "regno") String regNo) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getInpatientSettleList(hospital, patient.getName(), regNo);
    }

    //32
    @GetMapping("/hospital_admission_certificate/{id}")
    @ApiOperation("查询患者入院证信息 Q_GetHospitalAdmissionCertificateByCardNo")
    @ActionLog(action = "查询患者入院证信息")
    public List<HospitalAdmissionCertificate> getHospitalAdmissionCertificateByCardNo(
            @ApiParam("就诊人Id") @PathVariable("id") Long id,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam(name = "beginDate", required = false) String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam(name = "endDate", required = false) String endDate) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        Patient patient = patientRepository.findById(id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        return businessService.getHospitalAdmissionCertificateByCardNo(hospital, patient.getName(),
                "", patient.getHisPatid(), patient.getIdCardNum(), "", beginDate, endDate);
    }

    //33
    @GetMapping("/discharge_medication/{patient_id}/{reg_no}")
    @ApiOperation("查询住院患者出院带药信息 Q_GetDischargeMedication")
    @ActionLog(action = "查询住院患者出院带药信息")
    public List<DischargeMedication> getDischargeMedicationList(
            @ApiParam("就诊人id") @PathVariable("patient_id") Long patient_id,
            @ApiParam("住院号") @PathVariable("reg_no") String reg_no) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getDischargeMedicationList(hospital, patient.getName(), reg_no);
    }

    //34
    @GetMapping("/{patientId}/operation_hints")
    @ApiOperation("查询患者手术提示告知信息 Q_GetOperationHints")
    @ActionLog(action = "如果患者需进行手术治疗，开具手术医嘱后可查询手术对应的告知说明")
    public List<OperationHints> getOperationHints(
            @ApiParam("患者id") @PathVariable("patientId") long patientId,
            @ApiParam("住院号") @RequestParam(value = "regno") String regNo) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        return businessService.getOperationHints(hospital, patient.getName(), regNo);
    }

    //35
    @GetMapping("/operation_schedule/{patient_id}/{reg_no}")
    @ApiOperation("查询患者手术进展信息 Q_GetOperationSchedule")
    @ActionLog(action = "查询患者手术进展信息")
    public List<OperationSchedule> getOperationSchedule(
            @ApiParam("就诊人id") @PathVariable("patient_id") Long patient_id,
            @ApiParam("就诊类别:1门诊 2住院") @RequestParam("user_source") String user_source,
            @ApiParam("就诊流水号 门诊传门诊序号，住院传住院号") @PathVariable("reg_no") String reg_no) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        return businessService.getOperationSchedule(hospital, patient.getName(), user_source, reg_no);
    }

    //36
    @GetMapping("/{patientId}/inpatient_medical_Record_text")
    @ApiOperation("查询患者住院病历(文本段) Q_GetInpatientMedicalRecordText")
    @ActionLog(action = "查询患者单次住院就诊的入院病历和出院小结")
    public List<InpatientMedicalRecordText> getInpatientMedicalRecordText(
            @ApiParam("患者id") @PathVariable("patientId") long patientId,
            @ApiParam("住院号 标记唯一 一次住院") @RequestParam(value = "regno") String regNo,
            @ApiParam("病历类型 1入院病历 2出院小结") @RequestParam(value = "rec_type") String recType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        return businessService.getInpatientMedicalRecordText(hospital, patient.getName(), regNo, recType);
    }

    //37
    @PostMapping("/B_InpatientPreCharge")
    @ApiOperation(value = "病人出院预算 -----需要处理入库逻辑暂时无法对接")
    @ActionLog(action = "病人出院预算")
    // 出院病人预结算，计算患者住院期间全部费用与押金之间的差额，如需补缴押金，反馈应补押金金额，后续调用住院押金充值相关接口补缴押金。
    // 如需退还部分押金，需患者去窗口办理退费
    public InpatientPreChargeResult InpatientPreCharge() {
        // TODO 需要入库，参数不明确
        Order order = new Order();
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        InpatientPreChargeResult inpatientPreChargeResult = businessService.inpatientPreCharge(hospital, order);
        if (Double.parseDouble(inpatientPreChargeResult.getAdvance_amount()) < 0) {
          // 患者最终需要补交押金的金额。如为负数代表患者需去窗口办理退费
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("请您去窗口办理退费");
        }
        return inpatientPreChargeResult;
    }

    //38
    @PostMapping("/B_InpatientConfirmCharge")
    @ApiOperation(value = "病人出院结算 -----需要处理入库逻辑暂时无法对接")
    @ActionLog(action = "病人出院结算")
    // 出院病人结算，如补缴完押金，调用此接口HIS系统扣除患者押金进行结算
    public InpatientConfirmChargeResult inpatientConfirmCharge() {
        // TODO 需要入库，参数不明确
        Order order = new Order();
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.inpatientConfirmCharge(hospital, order);
    }

    //39
    @GetMapping("/{patientId}/lis_report_list")
    @ApiOperation("查询患者所有检验报告列表，包括未出结果和已出结果 Q_GetLisReportsByPatId")
    @ActionLog(action = "查询患者所有检验报告列表，包括未出结果和已出结果")
    @ElectronicHealthCardData(scene = "0101082", inputKey = {"patientId"})
    public List<InspectionReport> getInspectionReportListByPatId(
            @ApiParam("患者Id") @PathVariable("patientId") Long patientId,
            @ApiParam("就诊卡id") @RequestParam(value = "electronicMedicCardId", required = false) Long electronicMedicCardId,
            @ApiParam("就诊类别 1门诊 2住院 不传显示全部") @RequestParam(value = "userSource", required = false) String userSource,
            @ApiParam("日期范围 query=select_time:<=2023-07-25 select_time:>=2023-07-25") @RequestParam("query") String query) {
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        Hospital hospital = CurrentHospital.getOrThrow();
        String[] extractedDates = TimeUtils.extractDatesToString(query);
        String beginDate = (extractedDates[1] == null) ? BEGINDATE : extractedDates[1];
        String endDate = (extractedDates[1] == null) ? TimeUtils.currentDateToString() : extractedDates[0];
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        ElectronicMedicCard card;
        if (electronicMedicCardId != null) {
            card = electronicMedicCardRepository.getById(electronicMedicCardId);
        } else {
            card = patient.getElectronicMedicCards().stream().filter(ElectronicMedicCard::isEnabled).findFirst().get();
        }
        return businessService.getInspectionReportListByPatId(hospital, card, userSource, beginDate, endDate);
    }

    @GetMapping("/{patientId}/lis_report/{reportNo}")
    @ApiOperation("查询实验室检验报告结果 Q_GetLisReportResult")
    @ActionLog(action = "查询实验室检验报告结果")
    public List<LaboratoryReport> getLaboratoryReportList(
            @ApiParam("患者Id") @PathVariable("patientId") Long patientId,
            @ApiParam("报告单号") @PathVariable("reportNo") String reportNo,
            @ApiParam("报告类别代码") @RequestParam(name = "reportTypeCode", required = false) String reportTypeCode) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User current = CurrentUser.getOrThrow();
        Patient patient = patientRepository.getById(patientId);
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        List<LaboratoryReport> reports = businessService.getLaboratoryReportList(hospital, reportNo, reportTypeCode);
        return reports;
    }

    // Q_GetRisReportsByPatId
    //41
    @GetMapping("/{patientId}/ris_reports_list")
    @ApiOperation("查询患者所有检查报告列表，包括未出结果和已出结果")
    @ActionLog(action = "查询患者所有检查报告列表，包括未出结果和已出结果")
    @ElectronicHealthCardData(scene = "0101081", inputKey = {"patientId"})
    public List<RisReport> getRisReportsByPatId(
            @ApiParam("患者Id") @PathVariable("patientId") Long patientId,
            @ApiParam("就诊卡id") @RequestParam(value = "electronicMedicCardId", required = false) Long electronicMedicCardId,
            @ApiParam("就诊类别 1门诊 2住院 不传显示全部") @RequestParam(value = "userSource", required = false) String userSource,
            @ApiParam("日期范围 query=select_time:<=2023-07-25 select_time:>=2023-07-25") @RequestParam("query") String query) {
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        Hospital hospital = CurrentHospital.getOrThrow();
        String[] extractedDates = TimeUtils.extractDatesToString(query);
        String beginDate = (extractedDates[1] == null) ? BEGINDATE : extractedDates[1];
        String endDate = (extractedDates[1] == null) ? TimeUtils.currentDateToString() : extractedDates[0];
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        ElectronicMedicCard card;
        if (electronicMedicCardId != null) {
            card = electronicMedicCardRepository.getById(electronicMedicCardId);
        } else {
            card = patient.getElectronicMedicCards().stream().filter(ElectronicMedicCard::isEnabled).findFirst().get();
        }
        return businessService.getRisReportsByPatId(hospital, card, userSource, beginDate, endDate);
    }

    @GetMapping("/{patientId}/ris_reports/{reportNo}")
    @ApiOperation("查询检查报告结果 Q_GetRisReportResult")
    @ActionLog(action = "查询检查报告结果")
    public List<RisReportResult> getRisReportResult(
            @ApiParam("患者Id") @PathVariable("patientId") Long patientId,
            @ApiParam("报告单号") @PathVariable("reportNo") String reportNo,
            @ApiParam("报告类别代码") @RequestParam(name = "reportTypeCode", required = false) String reportTypeCode) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User current = CurrentUser.getOrThrow();
        Patient patient = patientRepository.getById(patientId);
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getRisReportResult(hospital, reportNo, reportTypeCode);
    }

    //43
    @GetMapping("/price_publicity_list")
    @ApiOperation("查询价格公示列表 Q_GetPricePublicityList")
    @ActionLog(action = "查询价格公示列表")
    public Page<PricePublicityInfo> getPricePublicityList(
            @ApiParam("价格公示数据类型 1.药品2.项目 2个都需要实现") @RequestParam(name = "pricePublicityType", required = false)
            String pricePublicityType,
            @ApiParam("关键字 搜索查询，不传查全部") @RequestParam(name = "keyword", required = false) String keyword,
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getPricePublicityPage(hospital, pricePublicityType, keyword, pageNo, size);
    }

    //44
    @GetMapping("/doctor_schedules")
    @ApiOperation("查询专家特需出诊公示列表 Q_GetDoctorSchedules")
    @ActionLog(action = "查询专家特需出诊公示列表")
    public Page<DoctorSchedules> getDoctorSchedules(
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("科室代码") @RequestParam(name = "deptId", required = false) String deptId,
            @ApiParam("专家名称") @RequestParam(name = "doctorName", required = false) String doctorName,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getDoctorSchedulesByPage(hospital, deptId, doctorName, beginDate ,endDate, pageNo, size);
    }

    //45
    @GetMapping("/Q_GetSpecialClinicList")
    @ApiOperation("查询专病门诊公示列表")
    @ActionLog(action = "查询专病门诊公示列表")
    public List<SpecialClinicInfo> getSpecialClinicList(
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getSpecialClinicList(hospital, beginDate, endDate);
    }

    //46
    @GetMapping("/Q_GetGeneralClinicList")
    @ApiOperation("查询普通门诊公示列表")
    @ActionLog(action = "查询普通门诊公示列表")
    public List<GeneralClinicInfo> getGeneralClinicList(
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getGeneralClinicList(hospital, beginDate, endDate);
    }

    //47
    @PostMapping("/apply_electronic_invoice/{patient_id}")
    @ApiOperation("申请电子发票（异步）")
    @ActionLog(action = "申请电子发票（异步）")
    public ApplyElectronicInvoice applyElectronicInvoice(
            @ApiParam("就诊人Id") @PathVariable("patient_id") Long patient_id,
            @RequestBody ApplyElectronicInvoiceReq param) {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        return businessService.applyElectronicInvoice(hospital, patient, param);
    }

    //48
    @GetMapping("/electronic_invoice_file/{patient_id}")
    @ApiOperation("电子发票文件查询")
    @ActionLog(action = "电子发票文件查询")
    public ElectronicInvoiceFile getElectronicInvoiceFile(
            @ApiParam("就诊人Id") @PathVariable("patient_id") Long patient_id,
            @ApiParam("电子收据号") @RequestParam("elec_invoice_no") String eleInvoiceNo) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getElectronicInvoiceFile(hospital, patient, eleInvoiceNo);
    }

    @GetMapping("/electronic_invoice_list/{patient_id}")
    @ApiOperation("电子发票列表查询")
    @ActionLog(action = "电子发票列表查询")
    public Page<ElectronicInvoiceItem> getElectronicInvoiceFile(
            @ApiParam("就诊人Id") @PathVariable("patient_id") Long patient_id,
            @ApiParam("在线类型 -1全部 0线上 1线下") @RequestParam("online_type") String online_type,
            @ApiParam("page") @RequestParam(value = "page", required = false, defaultValue = "0") int page,
            @ApiParam("size") @RequestParam(value = "size", required = false, defaultValue = "10") int size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patient_id).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        // 我们系统中page从0开始，his接口文档定义从1开始
        return businessService.getElectronicInvoiceList(hospital, patient.getHisPatid(), online_type, (page + 1) + "", size + "");
    }

    // Q_GetOutpatientUnChargeRecipe
    // 一期的接口
    @GetMapping("/{patientId}/outpatient_uncharge_recipe")
    @ApiOperation("查询门诊患者待缴费项目信息")
    @ActionLog(action = "查询门诊患者待缴费项目信息")
    public List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(
            @ApiParam("门诊patid 门诊患者唯一号") @PathVariable("patientId") Long patientId,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate,
            @ApiParam("医保入参 占位，具体参数格式内容需要根据当地医保确定")
            @RequestParam(name = "insuranceParam", required = false) String insuranceParam,
            @ApiParam("就诊卡id") @RequestParam(name = "cardId", required = false) Long cardId,
            @ApiParam("就诊渠道 0线下就 1线上就诊 -1全部")
            @RequestParam(name="channelType", required = false, defaultValue = "0") String channelType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        ElectronicMedicCard card = cardId == null ? null : electronicMedicCardRepository.getById(cardId);
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        // 处理一下时间格式问题
        List<OutpatientUnChargeRecipeInfo> recipeList = businessService.getOutpatientUnChargeRecipeList(
            hospital, patient, card, beginDate, endDate, insuranceParam, channelType);
        recipeList.forEach(u -> {
            Date convert = TimeUtils.convert(u.getRecipe_time());
            u.setRecipe_time(TimeUtils.dateToString(convert));
        });
        return recipeList;
    }

    @GetMapping("/qrCode/outpatient_uncharge_recipes")
    @ApiOperation("查询门诊患者待缴费项目信息")
    @ActionLog(action = "查询门诊患者待缴费项目信息")
    public List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(
        @ApiParam("his patId") @RequestParam("patId") String patId,
        @ApiParam("his patName") @RequestParam("patName") String patName,
        @ApiParam("his regno") @RequestParam("regno") String regno,
        @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
        @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate,
        @ApiParam("医保入参 占位，具体参数格式内容需要根据当地医保确定")
        @RequestParam(name = "insuranceParam", required = false) String insuranceParam,
        @ApiParam("就诊卡id") @RequestParam(name = "cardId", required = false) Long cardId,
        @ApiParam("就诊渠道 0线下就 1线上就诊 -1全部")
        @RequestParam(name="channelType", required = false, defaultValue = "0") String channelType) {
        Hospital hospital = CurrentHospital.getOrThrow();
        ElectronicMedicCard card = cardId == null ? null : electronicMedicCardRepository.getById(cardId);
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        // 处理一下时间格式问题
        List<OutpatientUnChargeRecipeInfo> recipeList = businessService.getOutpatientUnChargeRecipeList(
            hospital, patId,patName, card, beginDate, endDate, insuranceParam, channelType);
        recipeList.forEach(u -> {
            Date convert = TimeUtils.convert(u.getRecipe_time());
            u.setRecipe_time(TimeUtils.dateToString(convert));
        });
        return recipeList;
    }

    // 一期的接口
    @GetMapping("/{patientId}/outpatient_charge")
    @ApiOperation("查询门诊患者缴费记录 Q_GetOutpatientChargeListByPatId")
    @ActionLog(action = "通过门诊患者唯一号查询患者缴费记录")
    @ElectronicHealthCardData(scene = "0101052", inputKey = {"patientId"})
    public List<OutpatientCharge> getOutpatientChargeListByPatId(
            @ApiParam("患者Id") @PathVariable("patientId") Long patientId,
            @ApiParam("就诊渠道 0线下就 1线上就诊 -1全部")
            @RequestParam(name="channelType", required = false, defaultValue = "-1") String channelType,
            @ApiParam("日期范围 query=charge_time:<=2023-07-25 charge_time:>=2023-07-25") @RequestParam("query") String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        String[] extractedDates = TimeUtils.extractDatesToString(query);
        String beginDate = (extractedDates[1] == null) ? TimeUtils.dateToString(DateUtils.addMonths(new Date(), -6),
         "yyyyMMdd")  :
            extractedDates[1];
        String endDate = (extractedDates[1] == null) ? TimeUtils.currentDateToString() : extractedDates[0];
        return businessService.getOutpatientChargeList(hospital, patient, channelType, beginDate, endDate);
    }

    // 一期的接口
    @GetMapping("/{patientId}/outpatient_charge_detail/{settleId}")
    @ApiOperation("查询门诊患者缴费详细信息 Q_GetOutpatientChargeDetails")
    @ActionLog(action = "查询门诊患者缴费详细信息")
    public OutpatientChargeDetail getOutpatientChargeDetails(
            @ApiParam("患者Id") @PathVariable("patientId") Long patientId,
            @ApiParam("收据号") @PathVariable("settleId") String settleId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User current = CurrentUser.getOrThrow();
        Patient patient = patientRepository.getById(patientId);
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getOutpatientChargeDetails(hospital, settleId);
    }

    // B_SaveApplication 目前还没有对接his
    // 一，二期都不存在的接口
    @PostMapping("/appointment/nucleic_acid_order")
    @ApiOperation("核酸预约")
    @ActionLog(action = "核酸预约-自助开单")
    public NucleicAcidOrderDTO saveApplication(@RequestBody CreateNucleicAcidOrderDTO createNucleicAcidOrderDTO) {
        return offlineOrderService.createNucleicAcidOrder(CurrentHospital.getOrThrow(), createNucleicAcidOrderDTO);
    }

    @GetMapping("/{patientId}/appointment/nucleic_acid_orders")
    @ApiOperation("患者查询核酸预约订单列表")
    @ActionLog(action = "患者查询核酸预约订单列表")
    public Page<NucleicAcidOrderDTO> getNucleicAcidOrdersByPatientId(
            @ApiParam("患者Id") @PathVariable("patientId") String patientId,
            @ApiParam("订单状态  待支付:WAIT_PAY PENDING  已完成:COMPLETED")
            @RequestParam(value = "orderStatusContains", required = false, defaultValue = "WAIT_PAY PENDING") String orderStatusContains,
            @ApiParam("订单类型") @RequestParam(value = "type", required = false, defaultValue = "APPOINTMENT_NUCLEIC_ACID") String type,
            @ApiParam(value = "查询页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNum,
            @ApiParam(value = "每页条数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer pageSize
    ) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return offlineOrderService.findOfflineOrderListByType(hospital, patientId,
                orderStatusContains, "", type, "", pageNum, pageSize).map(NucleicAcidOrderDTO::new);
    }

    @GetMapping("/appointment/nucleic_acid_order/{orderId}/detail")
    @ApiOperation("患者查询核酸预约订单详情")
    @ActionLog(action = "患者查询核酸预约订单详情")
    public NucleicAcidOrderDTO getNucleicAcidDetail(@PathVariable("orderId") Long orderId) {
        return new NucleicAcidOrderDTO(offlineOrderRepository.getById(orderId));
    }

    @GetMapping("/{patientId}/appointment/medical_appointment_orders")
    @ApiOperation("患者查询医技预约订单列表")
    @ActionLog(action = "患者查询医技预约订单列表")
    public Page<MedicalAppointmentOrderDTO> getMedicalAppointmentOrderList(
            @ApiParam("患者Id") @PathVariable("patientId") String patientId,
            @ApiParam("订单状态  待预约:APPOINTMENT 已预约:RESERVED  已完成:COMPLETED 已过期:EXPIRED  已完成tab传: COMPLETED EXPIRED")
            @RequestParam(value = "orderStatusContains", required = false, defaultValue = "APPOINTMENT") String orderStatusContains,
            @ApiParam("订单类型") @RequestParam(value = "type", required = false, defaultValue = "MEDICAL_APPOINTMENT") String type,
            @ApiParam("就诊卡号") @RequestParam(value = "cardNo") String cardNo,
            @ApiParam(value = "查询页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNum,
            @ApiParam(value = "每页条数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer pageSize) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return offlineOrderService.findOfflineOrderListByType(hospital, patientId,
                orderStatusContains, "", type, cardNo, pageNum, pageSize).map(MedicalAppointmentOrderDTO::new);
    }

    @PutMapping("/appointment/medical_appointment_orders")
    @ApiOperation("患者去预约")
    @ActionLog(action = "患者去预约")
    public void updateMedicalAppointmentOrder(@RequestBody CreateMedicalAppointmentOrderDTO createMedicalAppointmentOrderDTO) {
        offlineOrderService.updateMedicalAppointmentOrder(CurrentHospital.getOrThrow(), createMedicalAppointmentOrderDTO);
    }

    @GetMapping("/order/{orderId}/check_items")
    @ApiOperation("患者查看检查检验详情")
    @ActionLog(action = "患者查看检查检验详情")
    public OrderCheckItemVM getCheckItems(
        @PathVariable("orderId") Long orderId) {
        OrderExtraInfo orderExtraInfo = orderExtraInfoRepository.findByOrderId(orderId)
            .orElseThrow(() -> ErrorType.BAD_REQUEST_ERROR.toProblem("订单不存在"));
        List<CheckItem> checkItems = Lists.newArrayList();
        String checkItemStr = orderExtraInfo.getCheckItems();
        if (StringUtils.isNotBlank(checkItemStr)) {
            checkItems = StandardObjectMapper.readValue(checkItemStr, new TypeReference<>() {
            });
        }
        OrderCheckItemVM orderCheckItemVM = new OrderCheckItemVM();
        orderCheckItemVM.setOrder(new OrderVM(orderRepository.getById(orderId)));
        orderCheckItemVM.setCheckItems(checkItems);
        return orderCheckItemVM;
    }

    @GetMapping("/order/{orderId}/in_patient_apply")
    @ApiOperation("患者查询入院证信息")
    @ActionLog(action = "患者查询入院证信息")
    public InPatientApplyVM getInPatientApply(
        @PathVariable("orderId") Long orderId) {
        OrderExtraInfo orderExtraInfo = orderExtraInfoRepository.findByOrderId(orderId)
            .orElseThrow(ErrorType.ORDER_NOT_FOUND::toProblem);
        String inPatientApply = orderExtraInfo.getInPatientApply();
        if (StringUtils.isBlank(inPatientApply)) {
            return null;
        } else {
            return StandardObjectMapper.readValue(inPatientApply, new TypeReference<>() {
            });
        }
    }

    @GetMapping("/{patientId}/physical_examination_record")
    @ApiOperation("查询患者体检记录 Q_GetPhysicalExaminationRecord")
    @ActionLog(action = "根据身份证号码或联系电话查询时间区间内的体检记录信息")
    public List<PhysicalExaminationRecord> getPhysicalExaminationRecord(
            @ApiParam("患者Id") @PathVariable("patientId") long patientId,
            @ApiParam("就诊卡id") @RequestParam(value = "electronicMedicCardId", required = false) Long electronicMedicCardId,
            @ApiParam("日期范围 query=select_time:<=2023-07-25 select_time:>=2023-07-25") @RequestParam("query") String query) {
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        Hospital hospital = CurrentHospital.getOrThrow();
        String[] extractedDates = TimeUtils.extractDatesToString(query);
        String beginDate = (extractedDates[1] == null) ? BEGINDATE : extractedDates[1];
        String endDate = (extractedDates[1] == null) ? TimeUtils.currentDateToString() : extractedDates[0];
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        ElectronicMedicCard card;
        if (electronicMedicCardId != null) {
            card = electronicMedicCardRepository.getById(electronicMedicCardId);
        } else {
            card = patient.getElectronicMedicCards().stream().filter(ElectronicMedicCard::isEnabled).findFirst().get();
        }

        return businessService.getPhysicalExaminationRecord(hospital, card, beginDate, endDate);
    }

    @GetMapping("/{patientId}/physical_examination_reportFile")
    @ApiOperation("查询患者体检报告 Q_GetPhysicalExaminationReportFile")
    @ActionLog(action = "查询患者体检报告")
    public List<PhysicalExaminationReportFile> getPhysicalExaminationReportFile(
            @ApiParam("患者Id") @PathVariable("patientId") long patientId,
            @ApiParam("体检编号") @RequestParam(value = "tjbh") String tjbh) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.getPhysicalExaminationReportFile(hospital, patient.getName(), tjbh);
    }

    @PostMapping("/outpatient/charge/return")
    @ApiOperation("用户门诊缴费退费")
    @ActionLog(action = "用户门诊缴费退费")
    public String returnFee(
        @RequestBody OutpatientChargeRefundedReq req) throws IOException {
        req.setHospitalCode("99");
        String stringify = StandardObjectMapper.stringify(req);
        String bodyStr;
        // 切换到现场  要改his接口
//        String url = "http://10.0.0.217:7010/hisapi/outpatient/charge/return";
        String url = "https://hlwyy.ycrh.com/test/hisapi/outpatient/charge/return";
        try (okhttp3.ResponseBody body = OkHttpUtils.post(url,
                                                          stringify).body()) {
            bodyStr = body.string();
            log.info("用户手动门诊缴费退费body: {}  result: {}", stringify, bodyStr);
            if (!"success".equals(bodyStr)) {
                throw ErrorType.BAD_REQUEST_ERROR.toProblem("退费失败：" + bodyStr);
            }
        }
        return bodyStr;
    }

    @GetMapping("/Q_GetPatientMedicalRecord")
    @ApiOperation("查询门诊患者文本病历")
    @ActionLog(action = "查询门诊患者文本病历")
    public List<PatientMedicalRecord> patientMedicalRecords(
        @ApiParam("就诊人id") @RequestParam("patid") Long patientId,
            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate
    ) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
        User current = CurrentUser.getOrThrow();
        if (!Objects.equals(patient.getUser(), current)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        return businessService.patientMedicalRecords(hospital, patient.getHisPatid(), beginDate, endDate);
    }

    // TODO 以下是第一次his二期接口对接文档整理的内容
//    @GetMapping("/Q_GetPatientWaitingListByDoctor/id")
//    @ApiOperation("当前门诊候诊队列查询")
//    @ActionLog(action = "当前门诊候诊队列查询")
//    public List<PatientWaitingInfo> getPatientWaiting(
//            @ApiParam("医生id") @PathVariable("id") Long id) {
//        Hospital hospital = CurrentHospital.getOrThrow();
//        MedicalWorker medicalWorker = medicalWorkerRepository.findById(id).orElseThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getPatientWaiting(hospital, medicalWorker);
//    }
//
//    @GetMapping("/Q_GetOutpatientUnChargeRecipe/{patientId}")
//    @ApiOperation("查询门诊患者待缴费处方信息(未收费处方)")
//    @ActionLog(action = "查询门诊患者待缴费处方信息(未收费处方)")
//    public List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipeList(
//            @ApiParam("门诊patid 门诊患者唯一号") @PathVariable("patientId") Long patientId,
//            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
//            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate,
//            @ApiParam("医保入参 医保端所需参数，自费病人为空，此参数仅限支持医保脱卡支付医院(统一采用BASE64编码转换)，具体参数格式根据各地医保参照附件说明")
//            @RequestParam(name = "insuranceParam", required = false) String insuranceParam) {
//        Hospital hospital = CurrentHospital.getOrThrow();
//        Patient patient = patientRepository.findById(patientId).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem);
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getOutpatientUnChargeRecipeList(hospital, patient, beginDate, endDate, insuranceParam, "-1");
//    }

//    @GetMapping("/Q_GetLisReportsByCardNo")
//    @ApiOperation("查询检验报告列表（通过患者院内卡号）")
//    @ActionLog(action = "查询检验报告列表（通过患者院内卡号）")
//    public List<InspectionReport> getInspectionReportListByCardNo(
//            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
//            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate) {
//        // TODO 患者院内卡号怎么拿的？
//        String patientName = null;
//        String cardNo = null;
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getInspectionReportListByCardNo(hospital, patientName, cardNo, beginDate, endDate);
//    }
//
//    @GetMapping("/Q_GetMicroorganismReportsByCardNo")
//    @ApiOperation("查询微生物报告列表（通过患者院内卡号）")
//    @ActionLog(action = "查询微生物报告列表（通过患者院内卡号）")
//    public List<MicroorganismReport> getMicroorganismReportsByCardNo(
//            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
//            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate) {
//        // TODO 患者院内卡号怎么拿的？
//        String patientName = null;
//        String cardNo = null;
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getMicroorganismReportsByCardNo(hospital, patientName, cardNo, beginDate, endDate);
//    }
//
//    @GetMapping("/Q_GetMicroorganismReportsByPatId/{id}")
//    @ApiOperation("查询微生物报告列表（通过门诊患者唯一号）")
//    @ActionLog(action = "查询微生物报告列表（通过门诊患者唯一号）")
//    public List<MicroorganismReport> getMicroorganismReportsByPatId(
//            @ApiParam("患者标识号 门诊传patientId，住院传住院号(regno)") @PathVariable("id") Long id,
//            @ApiParam("就诊类别 1门诊 2住院") @RequestParam("userSource") Integer userSource,
//            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
//            @ApiParam("结束日期 格式yyyyMMdd 默认周期1个月") @RequestParam("endDate") String endDate) {
//        String patientName = null;
//        if (userSource == 1) {
//            patientName = patientRepository.findById(id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem).getName();
//        } else if (userSource == 2) {
//            // TODO 住院号regno 怎么查？
//        }
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getMicroorganismReportsByPatId(hospital, patientName, id, userSource, beginDate, endDate);
//    }
//
//    @GetMapping("/Q_GetMicroorganismReportResult")
//    @ApiOperation("查询实验室微生物报告结果")
//    @ActionLog(action = "查询实验室微生物报告结果")
//    public List<LaboratoryMicroorganismsReportResult> getLaboratoryMicroorganismsReportResult() {
//        // TODO 如何查报告单号 report_no, 报告类别代码 report_type_code ?
//        String reportNo = null;
//        String reportTypeCode = null;
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getLaboratoryMicroorganismsReportResult(hospital, reportNo, reportTypeCode);
//    }
//
//    @GetMapping("/Q_GetRisReportsByCardNo")
//    @ApiOperation("查询微生物报告列表（通过患者院内卡号）")
//    @ActionLog(action = "查询微生物报告列表（通过患者院内卡号）")
//    public List<RisReport> getRisReportsByCardNo(
//            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
//            @ApiParam("结束日期 格式yyyyMMdd") @RequestParam("endDate") String endDate) {
//        // TODO 患者院内卡号怎么拿的？
//        String patientName = null;
//        String cardNo = null;
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getRisReportsByCardNo(hospital, patientName, cardNo, beginDate, endDate);
//    }
//

//
//    @GetMapping("/Q_GetLisCriticalValueReport/{id}")
//    @ApiOperation("查询检验危急值信息")
//    @ActionLog(action = "查询检验危急值信息")
//    public List<CriticalValueReport> getCriticalValueReportByPatId(
//            @ApiParam("患者标识号 门诊传patientId，住院传住院号(regno)") @PathVariable("id") Long id,
//            @ApiParam("就诊类别 1门诊 2住院") @RequestParam("userSource") Integer userSource,
//            @ApiParam("开始日期 格式yyyyMMdd") @RequestParam("beginDate") String beginDate,
//            @ApiParam("结束日期 格式yyyyMMdd 默认周期1个月") @RequestParam("endDate") String endDate) {
//        // TODO 这块的处理逻辑是否跟  查询微生物报告列表（通过门诊患者唯一号） 一样 ？ 文档中没有说明
//        String patientName = null;
//        if (userSource == 1) {
//            patientName = patientRepository.findById(id).orElseThrow(ErrorType.PATIENT_NOT_EXISTED::toProblem).getName();
//        } else if (userSource == 2) {
//            // TODO 住院号regno 怎么查？
//        }
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getCriticalValueReportByPatId(hospital, patientName, id, userSource, beginDate, endDate);
//    }
//
//    @GetMapping("/Q_GetInpatientByHiscardno/{hisCardNo}")
//    @ApiOperation("查询住院患者基本信息（通过病历号和患者联系电话）- 如果his不涉及病历号，可不实现这个接口")
//    @ActionLog(action = "查询住院患者基本信息（通过病历号和患者联系电话）- 如果his不涉及病历号，可不实现这个接口")
//    public List<InpatientInfo> getInpatientListByHisCardNo(
//            @ApiParam("病历号") @PathVariable("hisCardNo") String hisCardNo) {
//        // TODO 输入参数如何取值的？
////        Patient patient = patientRepository.findById(id).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
//        String patientName = null;
//        String telephone = null;
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getInpatientListByHisCardNo(hospital, patientName, hisCardNo, telephone);
//    }
//
//    @GetMapping("/Q_GetInpatientByCardno/{cardNo}")
//    @ApiOperation("查询住院患者基本信息（通过患者院内卡号）")
//    @ActionLog(action = "查询住院患者基本信息（通过患者院内卡号）")
//    public List<InpatientInfo> getInpatientListByCardNo(
//            @ApiParam("患者院内卡号") @PathVariable("cardNo") String cardNo) {
//        // TODO 输入参数如何取值的？
////        Patient patient = patientRepository.findById(id).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
//        String patientName = null;
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getInpatientListByCardNo(hospital, patientName, cardNo);
//    }
//
//    @GetMapping("/Q_GetInpatientByCertNo/{certNo}")
//    @ApiOperation("查询住院患者基本信息（通过证件号）")
//    @ActionLog(action = "查询住院患者基本信息（通过证件号）")
//    public List<InpatientInfo> getInpatientListByCertNo(
//            @ApiParam("证件号") @PathVariable("certNo") String certNo) {
//        // TODO 输入参数如何取值的？
////        Patient patient = patientRepository.findB(id).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
//        String patientName = null;
//        Hospital hospital = CurrentHospital.getOrThrow();
//        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
//        return businessService.getInpatientListByCertNo(hospital, patientName, certNo);
//    }

    private boolean havePidAuth(String hispid, User user) {
        boolean haveAuth = false;
        List<ElectronicMedicCard> cards = electronicMedicCardRepository.findByHisPatidAndEnabled(hispid, true);
        for (ElectronicMedicCard c : cards) {
            if (Objects.equals(c.getPatient().getUser(), user)) {
                haveAuth = true;
                break;
            }
        }
        return haveAuth;
    }

}
