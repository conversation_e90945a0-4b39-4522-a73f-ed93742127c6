package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.hospital.UserPlatformInfo;
import cn.taihealth.ih.repo.HospitalRepository;
import cn.taihealth.ih.repo.UserPlatformInfoRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.dto.UserPlatformInfoDTO;
import cn.taihealth.ih.service.impl.filter.UserPlatformInfo.UserPlatformInfoSearch;
import cn.taihealth.ih.service.vm.IhPage;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.criteria.Join;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = {"/hospital/userPlatformInfo"})
@Api(tags = "d端-患者管理", description = "患者管理")
public class HospitalUserPlatformInfoResource {


    private final HospitalRepository hospitalRepository;
    private final UserPlatformInfoRepository userPlatformInfoRepository;

    public HospitalUserPlatformInfoResource(HospitalRepository hospitalRepository,
                                            UserPlatformInfoRepository userPlatformInfoRepository) {
        this.hospitalRepository = hospitalRepository;
        this.userPlatformInfoRepository = userPlatformInfoRepository;
    }

    @GetMapping()
    @ApiOperation("获取患者列表")
    @ActionLog(action = "获取患者列表")
    public IhPage<UserPlatformInfoDTO> getCategoryByParent(
        @ApiParam("查询条件 query=name:张三/zhangsan/15011223344") @RequestParam(value = "query", required = false) String query,
        @ApiParam(name = "page") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam(name = "size") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        List<Specification<UserPlatformInfo>> specs = Lists.newArrayList();
        UserPlatformInfoSearch search = UserPlatformInfoSearch.of(query);
        specs.add(search.toSpecification());
        Pageable request = PageRequest
            .of(pageNo, size);
        Specification<UserPlatformInfo> sp = (root, q, criteriaBuilder) -> {
            Join<UserPlatformInfo, User> userJoin = root.join("user");
            q.orderBy(criteriaBuilder.desc(userJoin.get("createdDate")));
            q.groupBy(root.get("user"));
            return criteriaBuilder.equal(root.get("hospital").get("id"), hospital.getId());
        };
        specs.add(sp);
        return new IhPage<>(userPlatformInfoRepository.findAll(Specifications.and(specs), request).map(UserPlatformInfoDTO::new));
    }

}
