package cn.taihealth.ih.rest.controller.management;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.SupervisePlatform;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.repo.UserRepository;
import cn.taihealth.ih.repo.cloud.SupervisePlatformRepository;
import cn.taihealth.ih.service.cache.UserCache;
import cn.taihealth.ih.service.dto.SupervisePlatformDTO;
import cn.taihealth.ih.service.impl.filter.systemsuperviseplatform.SystemSupervisePlatformSearch;
import cn.taihealth.ih.service.vm.IhPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * @Author: Moon
 * @Date: 2021/5/17 下午2:29
 */
@RestController
@RequestMapping(value = {"/admin"})
@Api(tags = "m端-监管平台管理", description = "m端-监管平台管理")
public class SystemSupervisePlatformResource {

    private final SupervisePlatformRepository supervisePlatformRepository;
    private final UserRepository userRepository;
    private final UserCache userCache;
    private final UserCacheFindService userCacheFindService;

    public SystemSupervisePlatformResource(SupervisePlatformRepository supervisePlatformRepository,
                                           UserRepository userRepository,
                                           UserCacheFindService userCacheFindService,
                                           UserCache userCache) {
        this.supervisePlatformRepository = supervisePlatformRepository;
        this.userRepository = userRepository;
        this.userCache = userCache;
        this.userCacheFindService = userCacheFindService;
    }

    @GetMapping("/supervise_platforms")
    @ApiOperation(value = "查询全部监管平台（分页）")
    public IhPage<SupervisePlatformDTO> findDeptByHospital(
        @ApiParam("查询条件 query=name:四川互联网医院监管平台") @RequestParam(name = "query", required = false) String query,
        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "createdDate", "id");
        Specification<SupervisePlatform> specification = SystemSupervisePlatformSearch.of(query).toSpecification();
        return new IhPage<>(supervisePlatformRepository.findAll(specification, page).map(SupervisePlatformDTO::new));
    }

    @PostMapping("/supervise_platforms")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value = "添加监管平台")
    public SupervisePlatformDTO createSupervisePlatform(
        @ApiParam("监管平台信息") @Valid @RequestBody SupervisePlatformDTO dto) {
        String name = dto.getName();
        Optional<SupervisePlatform> optional = supervisePlatformRepository.findByName(name);
        optional.ifPresent(u -> {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("该平台已存在，请勿重复添加");
        });
        SupervisePlatform supervisePlatform = new SupervisePlatform();
        supervisePlatform.setName(name);
        supervisePlatform.setWebUrl(dto.getWebUrl());
        return new SupervisePlatformDTO(supervisePlatformRepository.save(supervisePlatform));
    }

    @PutMapping("/supervise_platforms/{id}")
    @ApiOperation("修改指定监管平台信息")
    public void updateSupervisePlatform(@PathVariable Long id,
                                        @ApiParam("待修改的平台信息") @Valid @RequestBody SupervisePlatformDTO dto) {
        SupervisePlatform supervisePlatform = supervisePlatformRepository.getById(id);
        String name = dto.getName();
        if (!StringUtils.equals(supervisePlatform.getName(), name)) {
            Optional<SupervisePlatform> optional = supervisePlatformRepository.findByName(name);
            optional.ifPresent(u -> {
                throw ErrorType.ILLEGAL_PARAMS.toProblem("该名称已存在，请勿重复添加");
            });
        }
        supervisePlatform.setName(name);
        supervisePlatform.setWebUrl(dto.getWebUrl());
        supervisePlatformRepository.save(supervisePlatform);
    }

    @DeleteMapping("/supervise_platforms/{id}")
    @ApiOperation("删除指定监管平台信息,当与医院有关联时会报错")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteSupervisePlatform(@PathVariable Long id) {
        try {
            SupervisePlatform supervisePlatform = supervisePlatformRepository.getById(id);
            supervisePlatformRepository.delete(supervisePlatform);
        } catch (Exception e) {
            throw ErrorType.SUPERVISE_PLATFORM_CAN_NOT_DELETE.toProblem();
        }
    }


}
