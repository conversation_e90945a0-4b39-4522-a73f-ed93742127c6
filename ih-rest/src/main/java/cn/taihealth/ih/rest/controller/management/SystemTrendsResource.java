package cn.taihealth.ih.rest.controller.management;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.service.api.TrendsService;
import cn.taihealth.ih.service.dto.HospitalTrendsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/admin")
@RequiredArgsConstructor
@Api(tags = "m端-总部动态管理", description = "m端-总部动态管理")
public class SystemTrendsResource {

    private final TrendsService hospitalTrendsService;

    @PostMapping("/trends")
    @ApiOperation("添加/修改动态")
    public HospitalTrendsDTO savePageSettings(@ApiParam("动态入参") @Valid @RequestBody HospitalTrendsDTO dto) {
        return hospitalTrendsService.addTrends(dto, null);
    }

    @DeleteMapping("/trends/{trendsId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("删除动态")
    public void getPageSettings(@PathVariable Long trendsId) {
        hospitalTrendsService.removeTrends(null, trendsId);
    }

    @GetMapping("/trends/list")
    @ApiOperation("查询动态列表")
    @ActionLog(action = "查询动态列表")
    public Page<HospitalTrendsDTO> getTemplateList(
            @ApiParam("查询条件：query=title:动态标题+show:是否显示[SHOW | HIDDEN]+sort:[createdDate-asc | updatedDate-desc]") @RequestParam(value = "query", required = false) String query,
            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        return hospitalTrendsService.getTrendsList(query, PageRequest.of(pageNo, size), null);
    }

    @GetMapping("/trends/{trendsId}/content")
    @ApiOperation("查询动态具体数据")
    public HospitalTrendsDTO getTrends(@PathVariable Long trendsId) {
        return hospitalTrendsService.getTrendsById(trendsId);
    }

}
