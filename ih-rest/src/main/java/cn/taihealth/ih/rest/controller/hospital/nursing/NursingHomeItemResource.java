package cn.taihealth.ih.rest.controller.hospital.nursing;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.nursing.NursingHomeItem;
import cn.taihealth.ih.domain.nursing.NursingHomeItemNurse;
import cn.taihealth.ih.repo.nursing.NursingHomeItemNurseRepository;
import cn.taihealth.ih.repo.nursing.NursingHomeItemRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.nursing.NursingHomeItemService;
import cn.taihealth.ih.service.impl.filter.nursing.homeitem.NursingHomeItemSearch;
import cn.taihealth.ih.service.vm.nursing.NurseVM;
import cn.taihealth.ih.service.vm.nursing.NursingHomeItemVM;
import com.gitq.jedi.data.specification.Specifications;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = {"/hospital/nursing/home_items"})
@Api(tags = "护理到家项目管理", description = "护理到家项目管理")
@AllArgsConstructor
public class NursingHomeItemResource {


    private final NursingHomeItemRepository nursingHomeItemRepository;
    private final NursingHomeItemService nursingHomeItemService;
    private final NursingHomeItemNurseRepository nursingHomeItemNurseRepository;

    @PostMapping()
    @ApiOperation("添加护理到家项目")
    @ActionLog(action = "添加护理到家项目")
    public NursingHomeItemVM add(@Valid @RequestBody NursingHomeItemVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        return new NursingHomeItemVM(nursingHomeItemService.add(hospital, vm));
    }

    @PutMapping("/{id}")
    @ApiOperation("修改护理到家项目")
    @ActionLog(action = "修改护理到家项目")
    public NursingHomeItemVM edit(@ApiParam("项目分类id") @PathVariable(name = "id") Long id,
                                     @Valid @RequestBody NursingHomeItemVM dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        NursingHomeItem item = nursingHomeItemRepository.getById(id);
        if (!Objects.equals(hospital, item.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        dto.setId(id);
        return new NursingHomeItemVM(nursingHomeItemService.edit(dto));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除护理到家项目")
    @ActionLog(action = "删除护理到家项目")
    public void delete(@ApiParam("项目分类id") @PathVariable(name = "id") Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        NursingHomeItem item = nursingHomeItemRepository.getById(id);
        if (!Objects.equals(hospital, item.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        nursingHomeItemService.delete(item);
    }

    @GetMapping()
    @ApiOperation("护理到家项目列表")
    @ActionLog(action = "护理到家项目列表")
    public Page<NursingHomeItemVM> findPage(
            @ApiParam("query=NAME:true ITEM_CLASSIFY_ID(项目分类ID):123456 HOME_ITEM_ID(到家项目ID):123213")
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(name = "page", required = false) Integer pageNo,
            @RequestParam(name = "size", required = false) Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable pageable = PageRequest.of(pageNo, size, Sort.by(Sort.Direction.DESC, "id"));
        List<Specification<NursingHomeItem>> sps = new ArrayList<>();
        sps.add(Specifications.eq("hospital", hospital));
        sps.add(Specifications.isFalse("deleted"));
        sps.add(NursingHomeItemSearch.of(query).toSpecification());
        Page<NursingHomeItem> all = nursingHomeItemRepository.findAll(Specifications.and(sps), pageable);
        return all.map(NursingHomeItemVM::new);
    }

    @GetMapping("/{id}/nurses")
    @ApiOperation("获取护理到家项目下的护士列表")
    @ActionLog(action = "获取护理到家项目下的护士列表")
    public List<NurseVM> findAllNuserList(
            @PathVariable(name = "id") Long id,
            @RequestParam(value = "name", required = false) String name) {
        Hospital hospital = CurrentHospital.getOrThrow();
        NursingHomeItem nursingHomeItem = nursingHomeItemRepository.getById(id);
        if (!Objects.equals(hospital, nursingHomeItem.getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        List<Specification<NursingHomeItemNurse>> specs = new ArrayList<>();
        specs.add(Specifications.eq("homeItem", nursingHomeItem));
        if (StringUtils.isNotBlank(name)) {
            specs.add(Specifications.like("nurse.user.fullName", name));
        }
        return nursingHomeItemNurseRepository.findAll(Specifications.and(specs),
                Sort.by(Sort.Direction.DESC, "id"))
                .stream().map(nursingHomeItemNurse -> new NurseVM(nursingHomeItemNurse.getNurse()))
                .collect(Collectors.toList());
    }

}
