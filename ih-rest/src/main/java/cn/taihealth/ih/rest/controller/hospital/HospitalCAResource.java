package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.ca.been.CaVerifySignParam;
import cn.taihealth.ih.ca.been.Qrcode;
import cn.taihealth.ih.ca.been.shukey.UKeySignedCallBack;
import cn.taihealth.ih.ca.enums.SignatureType;
import cn.taihealth.ih.ca.service.CaService;
import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.HospitalCAEnum;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.Order;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.domain.hospital.PrescriptionOrderCa;
import cn.taihealth.ih.repo.MedicalWorkerRepository;
import cn.taihealth.ih.repo.hospital.CaActionLogRepository;
import cn.taihealth.ih.repo.hospital.order.DiagnosisCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderCaRepository;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.repo.order.OrderRepository;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.cache.CaCache;
import cn.taihealth.ih.service.dto.hospital.CaActionLogDTO;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.IhPage;
import cn.taihealth.ih.service.vm.ca.*;
import cn.taihealth.ih.spring.security.authenticated.AuthenticatedUserToken;
import cn.taihealth.ih.spring.security.jwt.JWTAuthenticationProvider;
import cn.taihealth.ih.spring.security.jwt.JWTToken;
import com.gitq.jedi.common.jackson.StandardObjectMapper;
import com.gitq.jedi.context.AppContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/hospital/ca")
@Api(tags = "d端-互联网医院CA接口", description = "d端-互联网医院CA接口")
@AllArgsConstructor
public class HospitalCAResource {

    private final CaCache caCache;
    private final CaCertificateService caCertificateService;
    private final CaActionLogService caActionLogService;
    private final CaActionLogRepository caActionLogRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final AuthenticationManager authenticationManager;
    private final JWTAuthenticationProvider jwtAuthenticationProvider;
    private final DiagnosisCaRepository diagnosisCaRepository;
    private final PrescriptionOrderCaRepository prescriptionOrderCaRepository;

    @GetMapping("/params")
    @ApiOperation("获取ca参数")
    @ActionLog(action = "获取ca参数")
    public HospitalCaParam getParams() {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        // 判断医院是否禁用
        if (!HospitalSettingsHelper.getBoolean(hospital, HospitalSettingKey.CA_ALL_ENABLED)) {
            return new HospitalCaParam(false, false);
        }

        boolean caLogin = AppContext.getInstance(HospitalService.class).loginWithCa(hospital);

        if (!caLogin) {
            return new HospitalCaParam(false, false);
        } else {
            boolean signatureUseCa = AppContext.getInstance(HospitalService.class).signatureWithCa(hospital);
            HospitalCAEnum caEnum = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);
            if (caEnum == HospitalCAEnum.XINAN) {
                // 信安 username规则: 用户id_hospitalCode_PC_随机数
                XinanCaParam xinanCaParam = new XinanCaParam();
                xinanCaParam.setAppId(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_APP_ID));
                xinanCaParam.setSecretKey(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_SECRET_KEY));
                xinanCaParam.setRealName(user.getFullName());
                xinanCaParam.setMobile(user.getMobile());
                xinanCaParam.setHost(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_FRONT_URL));
                return new HospitalCaParam(caEnum, caLogin, signatureUseCa, xinanCaParam);
            } if (caEnum == HospitalCAEnum.SH_UKEY) {
                XinanCaParam caParam = new XinanCaParam();
                caParam.setAppId(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_APP_ID));
                caParam.setSecretKey(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_SECRET_KEY));
                caParam.setRealName(user.getFullName());
                caParam.setMobile(user.getMobile());
                caParam.setHost(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_FRONT_URL));
//                caParam.setAppCode(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_APP_CODE));
//                caParam.setAppPwd(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_APP_PWD));
                return new HospitalCaParam(caEnum, caLogin, signatureUseCa, caParam);
            } else {
                return new HospitalCaParam(false, false);
            }
        }
    }

    @GetMapping("/qrcode")
    @ApiOperation("获取CA登录二维码信息")
    @ActionLog(action = "获取CA登录二维码信息")
    public Qrcode qrcode() {
        Hospital hospital = CurrentHospital.getOrThrow();
        boolean loginWithCa = AppContext.getInstance(HospitalService.class).loginWithCa(hospital);
        if (!loginWithCa) {
            return new Qrcode(false);
        }

        HospitalCAEnum caEnum = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);
        CaService caService = AppContext.getInstance(caEnum.getServiceName(), CaService.class);
        String url = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.XINAN_API_BACKEND_URL);
        Qrcode qrcode = caService.getQrcode(url);
        caCache.putToken(qrcode.getRandom(), qrcode);
        return qrcode;
    }


    @PostMapping("/verifySignData")
    @ApiOperation("验证CA签名")
    @ActionLog(action = "验证CA签名")
    public void verifySignData(@RequestBody @Valid VerifySignDataVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        boolean loginWithCa = AppContext.getInstance(HospitalService.class).loginWithCa(hospital);
        if (!loginWithCa) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("ca登录未开启，请联系管理员");
        }
        HospitalCAEnum caEnum = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);
        String url;
        CaService caService = AppContext.getInstance(caEnum.getServiceName(), CaService.class);
        boolean verify = false;
        switch (caEnum) {
            case SH_UKEY:
                url = HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_API_WEBSERVICE_URL);
                CaVerifySignParam caSignParam = new CaVerifySignParam();
                caSignParam.setCert(vm.getCert());
                caSignParam.setAppCode(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_APP_CODE));
                caSignParam.setAppPwd(HospitalSettingsHelper.getString(hospital, HospitalSettingKey.SH_UKEY_APP_PWD));
                verify = caService.verifySignData(url, caSignParam, vm.getInData(), vm.getSignData());
                break;
        }
        if (!verify) {
            throw ErrorType.BAD_REQUEST_ERROR.toProblem("CA签名验证失败");
        }
    }

    @PostMapping("/login/qrcode")
    @ApiOperation("校验CA二维码登录是否成功")
//    @ActionLog(action = "校验CA二维码登录是否成功")
    public QrcodeTokenVM qrcodeLogin(@RequestBody @Valid QrcodeLoginVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        boolean loginWithCa = AppContext.getInstance(HospitalService.class).loginWithCa(hospital);
        if (!loginWithCa) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("ca登录未开启，请联系管理员");
        }

        String random = vm.getRandom();
        Qrcode qrcode = caCache.getToken(random);
        if (qrcode == null) {
            return new QrcodeTokenVM(false, true);
        }

        MedicalWorkerCaQrcodeVM medicalWorkerCaQrcode = caCertificateService.loginWithQrcode(hospital, vm.getRandom());
        if (medicalWorkerCaQrcode.isNeedRefresh()) {
            return new QrcodeTokenVM(false, true);
        }
        if (!medicalWorkerCaQrcode.isLogin()) {
            return new QrcodeTokenVM(false);
        }

        User user = medicalWorkerCaQrcode.getUser();

        AuthenticatedUserToken authenticatedUserToken =
                new AuthenticatedUserToken(user.getUsername(), user.getPassword());

        Authentication authentication = authenticationManager
                .authenticate(authenticatedUserToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        JWTToken jwt = jwtAuthenticationProvider.createToken(authentication, false);
        return new QrcodeTokenVM(jwt);
    }


    @GetMapping("/orders/{orderId}/prescriptions/unsigned")
    @ApiOperation("获取未签章的处方单")
    @ActionLog(action = "获取未签章的处方单")
    public CaUnsignedDiagnosisVM doctorUnsignedPrescriptions(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = AppContext.getInstance(OrderRepository.class).getById(orderId);
        if (!Objects.equals(order.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        boolean signatureWithCa = AppContext.getInstance(HospitalService.class).signatureWithCa(hospital);
        if (!signatureWithCa) {
            return new CaUnsignedDiagnosisVM(false);
        }
        CaUnsignedDiagnosisVM vm = caCertificateService.prescriptionsToPdf(orderId);
        vm.setSignatureUseCa(true);
        return vm;
    }

    @PostMapping("/doctor/orders/{orderId}/prescriptions")
    @ApiOperation("医生开立处方后处方签章")
    @ActionLog(action = "医生开立处方后处方签章")
    public CaPrescriptionVM doctorPrescription(@PathVariable long orderId, @RequestBody @Valid CaPrescriptionParam param) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = AppContext.getInstance(OrderRepository.class).getById(orderId);
        if (!Objects.equals(order.getHospital(), hospital) || (!Objects.equals(order.getDoctor().getUser(), user))) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        boolean signatureWithCa = AppContext.getInstance(HospitalService.class).signatureWithCa(hospital);
        if (!signatureWithCa) {
            return new CaPrescriptionVM(false);
        }
        HospitalCAEnum hospitalCA = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);
        param.setHospitalCA(hospitalCA);
        List<PrescriptionOrder> prescriptionOrders = AppContext.getInstance(PrescriptionOrderRepository.class).findByOrderIdAndEnabled(orderId, true);
        List<SignaturePdfVM> signaturePdfs;
        CaPrescriptionVM vm;
        switch (hospitalCA) {
            case XINAN:
                if (StringUtils.isBlank(param.getUsername())) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("ca用户名不能为空");
                }
                signaturePdfs = prescriptionOrders.stream().map(u ->
                                caCertificateService.prescriptionOrderSignatureXinan(hospital, user, u.getId(), SignatureType.DOCTOR, param.getUsername()))
                        .collect(Collectors.toList());
                return new CaPrescriptionVM(signaturePdfs);
            case SH_UKEY:
                signaturePdfs = prescriptionOrders.stream().map(u ->
                        caCertificateService.prescriptionOrderSignatureSH(hospital, user, u.getId(), SignatureType.DOCTOR, param))
                        .collect(Collectors.toList());
                vm = new CaPrescriptionVM(false);
                vm.setSignaturePdfs(signaturePdfs);
                return vm;
            default:
                return new CaPrescriptionVM(false);
        }
    }

//    @PostMapping("/doctor/prescriptions/check")
//    @ApiOperation("校验处方签章是否成功")
//    @ActionLog(action = "校验处方签章是否成功")
//    public boolean checkSignature(@RequestBody @Valid List<String> params) {
//        Hospital hospital = CurrentHospital.getOrThrow();
//        List<Boolean> booleans = params.stream().map(u ->
//                !caCertificateService.checkSignatureSuccessXinan(hospital, u)).collect(Collectors.toList());
//        return booleans.stream().allMatch(u -> u);
//    }

    @PostMapping("/doctor/prescriptions/upload")
    @ApiOperation("上传医生处方签章")
    @ActionLog(action = "上传医生处方签章")
    public void doctorPrescriptionUpload(@RequestBody @Valid List<PdfTicketVM> params) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        params.forEach(u ->
                caCertificateService.uploadDoctorSignaturePdfXinan(hospital, user, u));
    }

    @PostMapping("/pharmacist/prescriptions/upload")
    @ApiOperation("上传药师处方签章")
    @ActionLog(action = "上传药师处方签章")
    public void pharmacistPrescriptionUpload(@RequestBody @Valid List<PdfTicketVM> param) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        param.forEach(u ->
                caCertificateService.uploadDoctorReviewSignaturePdfXinan(hospital, user, u));
    }

    @PostMapping("/pharmacist/orders/{orderId}/prescriptions")
    @ApiOperation("药师审核完成后处方签章")
    @ActionLog(action = "药师审核完成后处方签章")
    public CaPrescriptionVM pharmacistPrescription(@PathVariable long orderId, @RequestBody @Valid CaPrescriptionParam param) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = AppContext.getInstance(OrderRepository.class).getById(orderId);
        if (!Objects.equals(order.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }

        boolean signatureWithCa = AppContext.getInstance(HospitalService.class).signatureWithCa(hospital);
        if (!signatureWithCa) {
            return new CaPrescriptionVM(false);
        }
        HospitalCAEnum hospitalCA = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);
        List<PrescriptionOrder> prescriptionOrders = null;
        if (hospitalCA != HospitalCAEnum.UNKNOWN) {
            prescriptionOrders = AppContext.getInstance(PrescriptionOrderRepository.class).findByOrderIdAndEnabled(orderId, true);
        }
        param.setHospitalCA(hospitalCA);
        List<SignaturePdfVM> signaturePdfs;
        CaPrescriptionVM vm;
        switch (hospitalCA) {
            case XINAN:
                if (StringUtils.isBlank(param.getUsername())) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("ca用户名不能为空");
                }
                signaturePdfs = prescriptionOrders.stream().map(u ->
                                caCertificateService.prescriptionOrderSignatureXinan(hospital, user, u.getId(), SignatureType.DOCTOR_REVIEW, param.getUsername()))
                        .collect(Collectors.toList());
                return new CaPrescriptionVM(signaturePdfs);
            case SH_UKEY:
                signaturePdfs = prescriptionOrders.stream().map(u ->
                                caCertificateService.prescriptionOrderSignatureSH(hospital, user, u.getId(), SignatureType.DOCTOR, param))
                        .collect(Collectors.toList());
                vm = new CaPrescriptionVM(false);
                vm.setSignaturePdfs(signaturePdfs);
                return vm;
            default:
                return new CaPrescriptionVM(false);
        }
    }

    @PostMapping("/action/log")
    @ApiOperation("保存ca操作日志")
    @ActionLog(action = "保存ca操作日志")
    public void saveActionLog(@RequestBody @Valid CaActionLogParam param) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        caActionLogService.save(hospital, user, param);
    }

    @GetMapping("/action/logs")
    @ApiOperation("查询ca操作日志")
    @ActionLog(action = "查询ca操作日志")
    public Page<CaActionLogDTO> getActionLogs(
            @ApiParam(name = "page") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam(name = "size") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable pageable = PageRequest.of(pageNo, size, Sort.Direction.DESC, "operationTime");
        return caActionLogRepository.findAllByHospital(hospital, pageable)
                .map(CaActionLogDTO::new);
    }

    @GetMapping("/action/logs/self")
    @ApiOperation("查询ca操作日志")
    @ActionLog(action = "查询ca操作日志")
    public IhPage<CaActionLogDTO> getActionSelfLogs(
            @ApiParam(name = "page") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
            @ApiParam(name = "size") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        MedicalWorker medicalWorker = medicalWorkerRepository.findOneByHospitalAndUser(hospital, user)
                .orElseThrow(ErrorType.FORBIDDEN::toProblem);
        Pageable pageable = PageRequest.of(pageNo, size, Sort.Direction.DESC, "operationTime");
        return new IhPage<>(caActionLogRepository.findAllByMedicalWorker(medicalWorker, pageable)
                .map(CaActionLogDTO::new));
    }

    @GetMapping("/orders/{orderId}/diagnosis/unsigned")
    @ApiOperation("获取未签章的诊断单")
    @ActionLog(action = "获取未签章的诊断单")
    public CaUnsignedDiagnosisVM doctorUnsignedDiagnosis(@PathVariable long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = AppContext.getInstance(OrderRepository.class).getById(orderId);
        if (!Objects.equals(order.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        boolean signatureWithCa = AppContext.getInstance(HospitalService.class).signatureWithCa(hospital);
        if (!signatureWithCa) {
            return new CaUnsignedDiagnosisVM(false);
        }
        CaUnsignedDiagnosisVM vm = caCertificateService.diagnosisToPdf(hospital, user, orderId);
        vm.setSignatureUseCa(true);
        return vm;
    }

    @PostMapping("/orders/{orderId}/diagnosis")
    @ApiOperation("医生诊断单签章")
    @ActionLog(action = "医生开立诊断后诊断单签章")
    public CaDiagnosisVM doctorDiagnosis(@PathVariable long orderId, @RequestBody @Valid CaPrescriptionParam param) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = AppContext.getInstance(OrderRepository.class).getById(orderId);
        if (!Objects.equals(order.getHospital(), hospital) || (!Objects.equals(order.getDoctor().getUser(), user))) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        boolean signatureWithCa = AppContext.getInstance(HospitalService.class).signatureWithCa(hospital);
        if (!signatureWithCa) {
            return new CaDiagnosisVM(false);
        }
        HospitalCAEnum hospitalCA = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.CA_TYPE, HospitalCAEnum.class);
        param.setHospitalCA(hospitalCA);
        SignaturePdfVM signaturePdfs;
        CaDiagnosisVM caDiagnosis;
        switch (hospitalCA) {
            case XINAN:
                if (StringUtils.isBlank(param.getUsername())) {
                    throw ErrorType.ILLEGAL_PARAMS.toProblem("ca用户名不能为空");
                }
                signaturePdfs = caCertificateService.diagnosisSignatureXinan(hospital, user, orderId, param.getUsername());
                return new CaDiagnosisVM(signaturePdfs);
            case SH_UKEY:
                signaturePdfs = caCertificateService.diagnosisSignatureSH(hospital, user, orderId, param);
                caDiagnosis = new CaDiagnosisVM(signaturePdfs);
                caDiagnosis.setSignatureUseCa(false);
                return caDiagnosis;
            default:
                return new CaDiagnosisVM(false);
        }
    }

    @PostMapping("/orders/{orderId}/diagnosis/upload")
    @ApiOperation("上传医生诊断单签章")
    @ActionLog(action = "上传医生诊断单签章")
    public void doctorDiagnosisUpload(@PathVariable long orderId, @RequestBody @Valid PdfTicketVM params) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        params.setOrderId(orderId);
        caCertificateService.uploadDiagnosisSignaturePdfXinan(hospital, user, params);
    }

    @PostMapping("/orders/{orderId}/{type}/signed")
    @ApiOperation("签章完成回调")
    @ActionLog(action = "签章完成回调")
    public void signedCallBack(@PathVariable long orderId,
                               @ApiParam(name = "type", value = "diagnosis|prescriptions|pharmacist")
                               @PathVariable String type,
                               @RequestBody UKeySignedCallBack params) {
        log.info("接收到ca签章完成回调 orderId: " + orderId + ", type: " + type + ", {}", StandardObjectMapper.stringify(params));
        switch (type) {
            case "diagnosis":
                caCertificateService.diagnosisSignatureSHSavePdf(orderId, params);
                break;
            case "prescriptions":
                caCertificateService.prescriptionSignatureSHSavePdf(orderId, params);
                break;
            case "pharmacist":
                caCertificateService.prescriptionSignaturePassedSHSavePdf(orderId, params);
                break;
            default:
        }
    }


    @GetMapping("/doctor/orders/{orderId}/{type}/signed")
    @ApiOperation("查询是否完成签章")
    @ActionLog(action = "查询是否完成签章")
    public SignVM caSigned(@PathVariable long orderId,
                           @ApiParam(name = "type", value = "diagnosis|prescriptions|pharmacist")
                           @PathVariable String type) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Order order = AppContext.getInstance(OrderRepository.class).getById(orderId);
        if (!Objects.equals(order.getHospital(), hospital) || (!Objects.equals(order.getDoctor().getUser(), user))) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if ("diagnosis".equalsIgnoreCase(type)) {
            return diagnosisCaRepository.findOneByOrderId(orderId).map(u ->
                    new SignVM(u.getDiagnosisPdfId() != null)
            ).orElseGet(SignVM::new);
        } else {
            PrescriptionOrderCa prescriptionOrderCa = prescriptionOrderCaRepository.findOneByOrderIdAndEnabled(orderId, true)
                    .orElse(null);
            if (prescriptionOrderCa == null) {
                return new SignVM();
            }
            if ("prescriptions".equalsIgnoreCase(type)) {
                return new SignVM(prescriptionOrderCa.getDoctorPdfId() != null);
            } else {
                return new SignVM(prescriptionOrderCa.getFinalPdfId() != null);
            }
        }
    }

}
