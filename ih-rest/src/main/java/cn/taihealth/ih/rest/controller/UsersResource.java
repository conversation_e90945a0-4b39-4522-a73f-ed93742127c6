package cn.taihealth.ih.rest.controller;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.util.IpUtils;
import cn.taihealth.ih.commons.util.MoreBeanUtils;
import cn.taihealth.ih.commons.util.RandomUtils;
import cn.taihealth.ih.domain.HealthRecord;
import cn.taihealth.ih.domain.UserPlatformTemporaryInfo;
import cn.taihealth.ih.domain.UserRemoveLog;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.Patient;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.cloud.UserSource;
import cn.taihealth.ih.domain.enums.HealthRecordField;
import cn.taihealth.ih.domain.enums.HospitalSettingKey;
import cn.taihealth.ih.domain.enums.IdCardNameUnitEnum;
import cn.taihealth.ih.domain.enums.SignupSource;
import cn.taihealth.ih.domain.hospital.MedicalWorker;
import cn.taihealth.ih.domain.hospital.UserPlatformInfo;
import cn.taihealth.ih.domain.service.UserCacheFindService;
import cn.taihealth.ih.realname.SM4Utils;
import cn.taihealth.ih.realname.serializer.PasswordDecodeDeserializer;
import cn.taihealth.ih.realname.serializer.PasswordEncodeSerializer;
import cn.taihealth.ih.repo.*;
import cn.taihealth.ih.rest.error.Check;
import cn.taihealth.ih.service.api.*;
import cn.taihealth.ih.service.dto.*;
import cn.taihealth.ih.service.impl.UserManager;
import cn.taihealth.ih.service.impl.ca.CertificateService;
import cn.taihealth.ih.service.impl.event.UserAddEvent;
import cn.taihealth.ih.service.impl.filter.Logic;
import cn.taihealth.ih.service.impl.filter.patient.PatientSearch;
import cn.taihealth.ih.service.impl.filter.user.UserSearch;
import cn.taihealth.ih.service.util.HospitalSettingsHelper;
import cn.taihealth.ih.service.vm.HealthRecordImageVM;
import cn.taihealth.ih.service.vm.HealthRecordVM;
import cn.taihealth.ih.service.vm.PasswordNewVM;
import cn.taihealth.ih.spring.security.UserRoleCodeName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gitq.jedi.context.AppContext;
import com.gitq.jedi.data.specification.Specifications;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@RestController
@Api(tags = "用户管理", description = "用户管理")
public class UsersResource {

    private static final Logger logger = LoggerFactory.getLogger(UsersResource.class);

    private final UserRepository userRepository;
    private final UserCertificationService userCertificationService;
    private final UserService userService;
    private final PatientRepository patientRepository;
    private final PatientService patientService;
    private final HealthRecordRepository healthRecordRepository;
    private final HealthRecordService healthRecordService;
    private final UserCacheFindService userCacheFindService;
    private final ApplicationEventPublisher eventPublisher;
    private final UserPlatformInfoRepository userPlatformInfoRepository;
    private final UserPlatformTemporaryInfoRepository userPlatformTemporaryInfoRepository;
    private final MedicalWorkerRepository medicalWorkerRepository;
    private final UserRemoveLogRepository userRemoveLogRepository;
    private final MedicalWorkerAssistantRelRepository medicalWorkerAssistantRelRepository;


    public UsersResource(UserRepository userRepository,
                         UserCertificationService userCertificationService,
                         PatientRepository patientRepository,
                         HealthRecordRepository healthRecordRepository,
                         UserService userService,
                         PatientService patientService,
                         HealthRecordService healthRecordService,
                         UserCacheFindService userCacheFindService,
                         ApplicationEventPublisher eventPublisher,
                         UserPlatformInfoRepository userPlatformInfoRepository,
                         UserPlatformTemporaryInfoRepository userPlatformTemporaryInfoRepository,
                         MedicalWorkerRepository medicalWorkerRepository,
                         UserRemoveLogRepository userRemoveLogRepository,
                         MedicalWorkerAssistantRelRepository medicalWorkerAssistantRelRepository,
                         HospitalRepository hospitalRepository) {
        this.userRepository = userRepository;
        this.userCertificationService = userCertificationService;
        this.patientRepository = patientRepository;
        this.healthRecordRepository = healthRecordRepository;
        this.userService = userService;
        this.patientService = patientService;
        this.healthRecordService = healthRecordService;
        this.userCacheFindService = userCacheFindService;
        this.eventPublisher = eventPublisher;
        this.userPlatformInfoRepository = userPlatformInfoRepository;
        this.userPlatformTemporaryInfoRepository = userPlatformTemporaryInfoRepository;
        this.medicalWorkerRepository = medicalWorkerRepository;
        this.userRemoveLogRepository = userRemoveLogRepository;
        this.medicalWorkerAssistantRelRepository = medicalWorkerAssistantRelRepository;
    }

    @GetMapping("/users/{username}")
    @ApiOperation("根据用户名查找用户")
    public UserDTO findUser(
        @ApiParam("用户名") @PathVariable("username") String username) {
        username = SM4Utils.urlDecoderCBC(username);
        return userCacheFindService.findOneByUsername(username)
            .map(UserDTO::new)
            .orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
    }

    @PatchMapping("/users/{username}")
    @ApiOperation("更新指定的用户")
    public UserDTO updateUser(
        @ApiParam("用户名") @PathVariable("username") String username,
        @ApiParam("用户信息") @Valid @RequestBody UserDTO dto) {
        username = SM4Utils.urlDecoderCBC(username);
        logger.info("Updating user info {}", username);
        dto.setUsername(username);
//        if (!Objects.equals(dto.getUsername(), username)) {
//            throw ErrorType.USERNAME_NOT_MATCH.toProblem();
//        }

        User current = CurrentUser.getOrThrow();
//        if (current.getAuthority().lt(dto.getAuthority())) {
//            throw new IllegalArgumentException("Authority param is illegal");
//        }

        String finalUsername = username;
        userCacheFindService.findOneByMobile(dto.getMobile()).ifPresent(u -> {
            if (!Objects.equals(u.getUsername(), finalUsername)) {
                throw ErrorType.MOBILE_ALREADY_USED.toProblem();
            }
        });
        User updated = userService.updateUser(current, dto);
        return new UserDTO(updated);
    }

    @PatchMapping("/users/{username}/password")
    @ApiOperation("更新指定用户的密码")
    public void updatePassword(
        @ApiParam("用户名") @PathVariable("username") String username,
        @ApiParam("密码") @Valid @RequestBody PasswdVM newPassword) {
        username = SM4Utils.urlDecoderCBC(username);
        logger.info("Updating user password {}", username);
        User user = userCacheFindService.findOneByUsername(username)
            .orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);

        Check.matcherPassord(newPassword.newPassword);
//        Check.passwordCheck(newPassword.newPassword);
//        if (current.getAuthority().ge(user.getAuthority())) {
        userService.updatePassword(user, newPassword.newPassword);
//        } else {
//            throw Problem.builder().withStatus(Status.UNAUTHORIZED)
//                .withTitle("你无权为此用户更改密码")
//                .withDetail("你无权为此用户更改密码")
//                .build();
//        }
    }

    @PatchMapping("/users/reset-password")
    @Timed
    @ApiOperation("更改用户密码,通过手机呈更改，不需要权限")
    @Transactional
    public void resetPassword(@Valid @RequestBody PasswordNewVM vm) {
        logger.info("Updating user password {}", vm.getUsername());
        User user = userCacheFindService.findOneByUsername(vm.getUsername())
            .orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);

        Check.matcherPassord(vm.getNewPassword());
        userService.updatePassword(user, vm.getNewPassword());
    }

    public static class PasswdVM {

        @JsonProperty
        @JsonDeserialize(using = PasswordDecodeDeserializer.class)
        @JsonSerialize(using = PasswordEncodeSerializer.class)
        private String newPassword;
    }

    @PostMapping("/users")
    @ApiOperation("创建用户06")
    @ResponseStatus(HttpStatus.CREATED)
    public UserDTO createUser(@Valid @RequestBody ManagedUserDTO dto,
                              HttpServletRequest request) {
        Check.passwordCheck(dto.getPassword());

        userCacheFindService.findOneByUsername(dto.getUsername()).ifPresent(u -> {
            throw ErrorType.USERNAME_ALREADY_USED.toProblem();
        });

        userCacheFindService.findOneByMobile(dto.getMobile()).ifPresent(u -> {
            throw ErrorType.MOBILE_ALREADY_USED.toProblem();
        });

        User created = userService.createUser(dto);
        // 添加用户来源04 DASHBOARD
        UserSource userSource = new UserSource();
        userSource.setSignupIp(IpUtils.realClientIp(request));
        userSource.setUserId(created.getId());
        userSource.setHospital(CurrentHospital.getOrNull());
        userSource.setSignupSource(SignupSource.DASHBOARD);
        eventPublisher.publishEvent(new UserAddEvent(userSource));
        return new UserDTO(created);
    }

    @PatchMapping("/users/{username}/roles")
    @ApiOperation("更新用户非医院角色")
    public void updateUserRoles(@PathVariable("username") String username,
                                @ApiParam("非医院角色编码，\"USER\"可以不传，都会有")
                                @Valid @RequestBody List<UserRoleCodeName> roleCodes) {
        username = SM4Utils.urlDecoderCBC(username);
        User user = userCacheFindService.findOneByUsername(username).orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        userService.updateUserRoles(user, roleCodes);
    }

    @PutMapping("/admin/platform/users")
    @ApiOperation("更新OMC平台用户")
    public void updatePlatformUser(@ApiParam("平台角色") @Valid @RequestBody UpdatePlatformRolesVM vm) {
        userService.updatePlatformUser(vm);
    }

    @PostMapping("/admin/platform/users")
    @ApiOperation("创建平台用户")
    @Transactional
    public UserDTO createPlatformUser(@Valid @RequestBody ManagedUserDTO dto,
                                      HttpServletRequest request) {
        if (CollectionUtils.isEmpty(dto.getPlatformRoles())) {
            throw ErrorType.ILLEGAL_PARAMS.toProblem("请选择一个平台角色");
        }

        userCacheFindService.findOneByUsername(dto.getUsername()).ifPresent(u -> {
            throw ErrorType.USERNAME_ALREADY_USED.toProblem();
        });

        userCacheFindService.findOneByMobile(dto.getMobile()).ifPresent(u -> {
            throw ErrorType.MOBILE_ALREADY_USED.toProblem();
        });

        dto.setRoles(Lists.newArrayList(new UserRoleCodeName("SUPERVISOR", "监管员")));
        User created = userService.createUser(dto);
        userService.updatePlatformRoles(created, dto.getPlatformRoles());
        // 添加用户来源04 DASHBOARD
        UserSource userSource = new UserSource();
        userSource.setSignupIp(IpUtils.realClientIp(request));
        userSource.setUserId(created.getId());
        userSource.setSignupSource(SignupSource.DASHBOARD);
        eventPublisher.publishEvent(new UserAddEvent(userSource));
        return new UserDTO(created);
    }

    @GetMapping("/users")
    @ApiOperation("查找所有的用户")
    public Page<UserDTO> findUsers(
        @ApiParam("查询条件: 如username:1234 lock:false full_name:1234 authority:USER platform:true/false name:xxx (默认按照username,full_name,mobile查询)")
        @RequestParam(value = "query", required = false) String query,
        @ApiParam("逻辑运算符: AND, OR") @RequestParam(value = "logic", required = false) Logic logic,
        @ApiParam("页数") @RequestParam(value = "page", required = false) Integer page,
        @ApiParam("每页的项目数") @RequestParam(value = "size", required = false) Integer size) {
        if (page == null) {
            page = 0;
        }

        if (size == null) {
            size = Constants.BATCH_SIZE;
        }

        if (logic == null) {
            logic = Logic.AND;
        }
        Pageable request = PageRequest
            .of(page, size == 0 ? Constants.BATCH_SIZE : size, Sort.by("id"));
        return userRepository.findAll(UserSearch.of(query, logic).toSpecification(), request)
            .map(UserDTO::new);
    }

    @GetMapping("/users/patients")
    @ApiOperation("获取所有用户就诊人")
    public Page<UserPatientDTO> getAllUserPatients(
        @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
        @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @ApiParam("query=idCardNum:encodeURIComponent(AesEncode(123456789012345678)) name:张三 patid:123 mobile:encodeURIComponent(AesEncode(131xxxxxxx)")
        @RequestParam(value = "query", required = false) String query) {
        Hospital hospital = CurrentHospital.getOrThrow();
        Pageable request = PageRequest
            .of(pageNo, size, Sort.Direction.DESC, "createdDate");
        List<Specification<Patient>> sp = Lists.newArrayList();
        sp.add(PatientSearch.of(query).toSpecification());
        sp.add(Specifications.eq("hospital", hospital));
        sp.add(Specifications.isTrue("enabled"));
        return patientRepository.findAll(Specifications.and(sp), request)
            .map(UserPatientDTO::new);
    }

    @PatchMapping("/users/{username}/patients/{id}")
    @ApiOperation("更新紧急联系人的信息")
    public void updateUserPatient(@PathVariable("username") String username,
                                  @PathVariable("id") Long id,
                                  @Valid @RequestBody PatientDTO vm) {
        username = SM4Utils.urlDecoderCBC(username);
        vm.setId(id);
        User user = userCacheFindService.findOneByUsername(username)
            .orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        patientService.updatePatient(user, vm);
    }

    @DeleteMapping("/users/{username}/patients/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("删除用户的紧急联系人")
    public void deleteUserPatient(
        @ApiParam("用户的username") @PathVariable("username") String username,
        @ApiParam("紧急联系人的ID") @PathVariable("id") Long id) {
        username = SM4Utils.urlDecoderCBC(username);
        Patient patient = patientRepository.getById(id);
        if (!patient.getUser().getUsername().equals(username)) {
            throw new IllegalArgumentException("Username not match");
        }
        patientService.deletePatient(patient);
    }

    @GetMapping("/users/{username}/id_card")
    @ApiOperation("获取指定用户的身份证信息")
    @Transactional(readOnly = true)
    public UserIdCardDTO getIdCardInfo(@PathVariable("username") String username) {
        username = SM4Utils.urlDecoderCBC(username);
        User user = userCacheFindService.findOneByUsername(username)
            .orElseThrow(ErrorType.USER_NOT_FOUND::toProblem);
        User currentUser = CurrentUser.getOrThrow();

//        if (currentUser.getAuthority().implies(user.getAuthority()) && currentUser.getAuthority().implies(
//            Authority.HOSPITAL_OPS)) {
        return userCertificationService.getIdCert(user, null).map(UserIdCardDTO::new)
            .orElse(null);
//        } else {
//            throw ErrorType.FORBIDDEN.toProblem();
//        }

    }

    @GetMapping("/users/patient/{patientId}/health_record")
    @ApiOperation("获取用户的健康档案")
    public HealthRecordImageVM getUserHealthRecord(@PathVariable("patientId") long patientId) {
        Patient patient = patientRepository.getById(patientId);
        return healthRecordService.getHealthRecordImageVM(patient);
    }

    @GetMapping("/users/patient/{patientId}/health_records/personal_info/{field}")
    @ApiOperation("获取就诊人的健康信息")
    public HealthRecordVM getHealthRecordByField(@PathVariable Long patientId,
                                                 @ApiParam("类型") @PathVariable HealthRecordField field) {
        Patient patient = patientRepository.getById(patientId);
        return healthRecordService.getHealthRecordByField(patient, field);
    }

    @PutMapping("/users/patient/{patientId}/health_record")
    @ApiOperation("添加／更新用户的健康档案")
    public HealthRecordDTO updateUserHealthRecord(@PathVariable("patientId") long patientId,
                                                  @Valid @RequestBody HealthRecordDTO vm) {
        Patient patient = patientRepository.getById(patientId);
        HealthRecord record = healthRecordRepository.findOneByPatient(patient)
            .orElse(new HealthRecord(patient.getUser(), patient));
        MoreBeanUtils.copyPropertiesNonNull(record, vm, ImmutableSet.of("user", "id"));
        healthRecordRepository.save(record);
        return new HealthRecordDTO(record);
    }

    @DeleteMapping("/users/patient/{patientId}/health_record")
    @ApiOperation("删除用户的健康档案")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteUserHealthRecord(@PathVariable("patientId") long patientId) {
        Patient patient = patientRepository.getById(patientId);
        Optional<HealthRecord> record = healthRecordRepository.findOneByPatient(patient);
        record.ifPresent(healthRecordRepository::delete);
    }

    @DeleteMapping("/users/remove/{userId}")
    @ApiOperation("清除指定用户信息")
    @Transactional
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeUser(@ApiParam("用户id") @PathVariable("userId") Long userId, HttpServletRequest request) {
        // 生产打开删除用户功能
        /*if (!env.acceptsProfiles(Profiles.of("dev"))) {
            throw ErrorType.FORBIDDEN.toProblem("当前操作仅可在dev环境");
        }*/
        User operator = CurrentUser.getOrThrow();
        User user = userRepository.findById(userId).orElseThrow();
        //用户权限中code=='ADMIN'||admin=true||code=='SYSTEM‘ 的用户不可清除
        UserDTO userDTO = new UserDTO(user);
        boolean flag = userDTO.getRoles().stream()
            .anyMatch(elem -> elem.isAdmin() || elem.getCode().equals("ADMIN") || elem.getCode().equals("SYSTEM"));
        if (flag) {
            throw ErrorType.FORBIDDEN.toProblem("系统管理员、系统用户、医院管理员用户数据不可清除");
        }
        List<UserPlatformInfo> userPlatformInfos = userPlatformInfoRepository.findAllByUser(user);
        List<String> unionIds = userPlatformInfos.stream()
            .map(UserPlatformInfo::getUnionId)
            .distinct()
            .collect(Collectors.toList());
        Specification<UserPlatformTemporaryInfo> spec = (root, query, cb) -> {
            Path<Object> appId = root.get("unionId");
            List<Predicate> list = new ArrayList<>();
            CriteriaBuilder.In<Object> in = cb.in(appId);
            in.value(unionIds);
            list.add(in);
            Predicate[] array = new Predicate[list.size()];
            return cb.and(list.toArray(array));
        };
        List<UserPlatformTemporaryInfo> userPlatformTemporaryInfos = userPlatformTemporaryInfoRepository.findAll(spec);
        userPlatformTemporaryInfoRepository.deleteAll(userPlatformTemporaryInfos);
        userPlatformTemporaryInfoRepository.flush();

        userPlatformInfoRepository.deleteAll(userPlatformInfos);
        userPlatformInfoRepository.flush();

        //如果当前用户为医生，医生数据处理(替换手机号、身份证)
        List<MedicalWorker> medicalWorkerList = medicalWorkerRepository.findAllByUser(user);
        String newValue = String.valueOf(System.currentTimeMillis());
        if (!CollectionUtils.isEmpty(medicalWorkerList)) {
            List<MedicalWorker> medicalWorkers = medicalWorkerList.stream()
                .peek(elem -> {
                    elem.setPhone(newValue);
                    elem.setIdentity(newValue);
                    elem.setIdentity(RandomUtils.generateRandomNumbers(18));
                    //2023-04-04 删除用户同时把医生设置禁用状态
                    elem.setEnabled(false);
                    medicalWorkerAssistantRelRepository.deleteByDoctorOrAssistant(elem.getId());
                }).collect(Collectors.toList());
            medicalWorkerRepository.saveAll(medicalWorkers);
        }

        //用户手机号+username替换
        String oldUserName = user.getUsername();
        String oldMobile = user.getMobile();
        user.setMobile(newValue);
        user.setIdentity(newValue);
        user.setUsername(UUID.randomUUID().toString().replace("-", ""));
        userService.alterUser(user, oldUserName, oldMobile);
        //记录日志
        UserRemoveLog removeLog = new UserRemoveLog();
        removeLog.setOldMobile(oldMobile);
        removeLog.setOldUsername(oldUserName);
        removeLog.setUser(user);
        removeLog.setOperator(operator);
        removeLog.setIp(IpUtils.realClientIp(request));
        removeLog.setOperateDate(new Date());
        userRemoveLogRepository.save(removeLog);

    }

    @PostMapping("/users/individualVerify")
    @ApiOperation("用户实名认证(二要素)，仅验证二要素是否正确，不会修改用户的实名信息，非必要尽量不调此接口")
    public void userIndividual(@Valid @RequestBody UserIndividualDTO userIndividualDTO) {
        Hospital hospital = CurrentHospital.getOrThrow();
        if (AppContext.getInstance(UserManager.class).checkUserShouldIdentity(hospital)) {
            String idCardNum = userIndividualDTO.getIdCardNum();
            String username = userIndividualDTO.getUsername();
            IdCardNameUnitEnum type = HospitalSettingsHelper.getValue(hospital, HospitalSettingKey.ID_CARD_NAME_UNIT,
                                                                      IdCardNameUnitEnum.class);
            CertificateService certificateService = CertificateService.getInstance(type.getServiceName());
            if (certificateService.individualVerify(hospital, idCardNum, username)) {
                logger.info("用户: {} 二要素实名认证成功", username);
            }
        }
    }

}
