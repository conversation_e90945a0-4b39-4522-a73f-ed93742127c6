package cn.taihealth.ih.rest.controller.cloud;

import cn.taihealth.ih.commons.Constants;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.cloud.User;
import cn.taihealth.ih.domain.enums.UserSettingKey;
import cn.taihealth.ih.realname.SM4Utils;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.CurrentUser;
import cn.taihealth.ih.service.api.UserSettingService;
import cn.taihealth.ih.service.vm.UserSettingsVM;
import com.gitq.jedi.common.datatype.DataTypes;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@RestController
@RequestMapping(value = { "/user" })
@Api(tags = "用户配置", description = "USER权限")
public class UserSettingsResource {

    private final UserSettingService userSettingService;

    public UserSettingsResource(UserSettingService userSettingService) {
        this.userSettingService = userSettingService;
    }


    @GetMapping("/settings")
    @ApiOperation(value = "获取全部用户配置")
    public List<UserSettingsVM> getUserSettings() {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        List<UserSettingKey> keys = Lists.newArrayList(UserSettingKey.values());
        return keys.stream().map(u -> {
            UserSettingsVM vm = new UserSettingsVM(u);
            vm.setKeyValue(userSettingService.getValue(hospital, user, u));
            return vm;
        }).collect(Collectors.toList());
    }

    @GetMapping("/settings/{key}")
    @ApiOperation(value = "获取用户配置")
    public UserSettingsVM getUserSetting(@PathVariable("key") UserSettingKey key) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        UserSettingsVM vm = new UserSettingsVM(key);
        vm.setKeyValue(userSettingService.getValue(hospital, user, key));
        return vm;
    }

    @PutMapping("/settings/{key}")
    @ApiOperation(value = "修改用户配置")
    public void updateUserSetting(@PathVariable("key") UserSettingKey key,
                                    @RequestBody UserSettingsVM vm) {
        Hospital hospital = CurrentHospital.getOrThrow();
        User user = CurrentUser.getOrThrow();
        Object value = vm.getValue();
        if (key.getDataType() == DataTypes.PASSWORD && value instanceof String) {
            String str = value.toString();
            try {
                str = SM4Utils.decrypt_CBC_Padding(Constants.SM3_BASE_KEY, str);
            } catch (Exception e) {
                str = null;
            }
            vm.setValue(str);
        }
        userSettingService.save(hospital, user, key, vm.getValue());
    }

}
