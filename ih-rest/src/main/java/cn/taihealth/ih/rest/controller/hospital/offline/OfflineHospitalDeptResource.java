package cn.taihealth.ih.rest.controller.hospital.offline;

import cn.taihealth.ih.commons.aop.logging.ActionLog;
import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.hospital.offline.OfflineDept;
import cn.taihealth.ih.repo.hospital.offline.OfflineDeptRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.offline.OfflineDeptService;
import cn.taihealth.ih.service.dto.hospital.offline.OfflineDeptDTO;
import cn.taihealth.ih.service.impl.filter.offline.dept.OfflineDeptSearch;
import cn.taihealth.ih.service.impl.strategy.BusinessService;
import cn.taihealth.ih.service.impl.strategy.BusinessServiceStrategy;
import com.gitq.jedi.data.specification.Specifications;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

/**
 * @Author: Moon
 * @Date: 2021/5/17 下午2:29
 */
@RestController
@RequestMapping(value = {"/hospital/offline"})
@Api(tags = "d端-线下科室管理", description = "d端-线下科室管理")
public class OfflineHospitalDeptResource {

    private final OfflineDeptService offlineDeptService;
    private final OfflineDeptRepository offlineDeptRepository;

    public OfflineHospitalDeptResource(OfflineDeptRepository offlineDeptRepository,
                                       OfflineDeptService offlineDeptService) {
        this.offlineDeptRepository = offlineDeptRepository;
        this.offlineDeptService = offlineDeptService;
    }

    @GetMapping("/depts")
    @ApiOperation(value = "查询医院的全部线下科室（分页）")
    @ActionLog(action = "查询医院的全部线下科室")
    public Page<OfflineDeptDTO> getAllDepts(@ApiParam("查询条件 query=deptName:急 introduce:true/false medical_skill:3"
        + "(传科室对应的科室分类编码)") @RequestParam(name = "query", required = false) String query,
                                            @ApiParam("页数") @RequestParam(name = "page", required = false, defaultValue = "0") Integer pageNo,
                                            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Pageable page = PageRequest.of(pageNo, size, Direction.DESC, "orderValue", "createdDate", "id");
        Hospital hospital = CurrentHospital.getOrThrow();
        Specification<OfflineDept> deptSpecificationAnd = Specifications
            .and(Specifications.eq("hospital", hospital),
                    OfflineDeptSearch.of(query).toSpecification());
        return offlineDeptRepository.findAll(deptSpecificationAnd, page).map(OfflineDeptDTO::new);
    }

    @PostMapping("/depts")
    @ApiOperation(value = "添加线下科室")
    @ActionLog(action = "添加线下科室")
    public OfflineDeptDTO createDept(@ApiParam("科室信息") @Valid @RequestBody OfflineDeptDTO dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        if (offlineDeptRepository.findOneByHospitalAndDeptCode(hospital, dto.getDeptCode()).isPresent()) {
            throw ErrorType.DEPT_CODE_ALREADY_USED.toProblem();
        }
        return new OfflineDeptDTO(offlineDeptService.addOfflineDept(hospital, dto));
    }

    @GetMapping("/depts/{id}")
    @ApiOperation("获取指定线下科室信息")
    @ActionLog(action = "获取指定线下科室信息")
    public OfflineDeptDTO getDept(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        OfflineDept dept = offlineDeptRepository.getById(id);
        if (!Objects.equals(dept.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return new OfflineDeptDTO(dept);
    }

    @PutMapping("/depts/{id}")
    @ApiOperation("修改指定线下科室信息")
    @ActionLog(action = "修改指定线下科室信息")
    public void updateDept(@PathVariable Long id,
                           @ApiParam("待修改的线下科室信息") @Valid @RequestBody OfflineDeptDTO dto) {
        Hospital hospital = CurrentHospital.getOrThrow();
        OfflineDept dept = offlineDeptRepository.getById(id);
        if (!Objects.equals(dept.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        if (!dto.getDeptCode().equals(dept.getDeptCode()) && offlineDeptRepository
            .findOneByHospitalAndDeptCode(hospital, dto.getDeptCode()).isPresent()) {
            throw ErrorType.DEPT_CODE_ALREADY_USED.toProblem();
        }
        dto.setId(id);
        offlineDeptService.updateOfflineDept(hospital, dto);
    }

    @DeleteMapping("/depts/{id}")
    @ApiOperation("删除指定线下科室信息,关联时会报错")
    @ActionLog(action = "删除指定线下科室信息")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteDept(@PathVariable Long id) {
        Hospital hospital = CurrentHospital.getOrThrow();
        OfflineDept dept = offlineDeptRepository.getById(id);
        if (!Objects.equals(dept.getHospital(), hospital)) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        try {
            offlineDeptRepository.delete(dept);
        } catch (Exception e) {
            throw ErrorType.DEPT_CAN_NOT_DELETE.toProblem();
        }
    }

    @PostMapping("/depts/syncHisDeptList")
    @ApiOperation(value = "同步HIS科室列表")
    @ActionLog(action = "同步HIS科室列表")
    public void syncHisDeptList() {
        Hospital hospital = CurrentHospital.getOrThrow();
        BusinessService businessService = BusinessServiceStrategy.getInstance().getStrategy(true);
        businessService.syncDept(hospital);
    }
}
