package cn.taihealth.ih.rest.controller.hospital;

import cn.taihealth.ih.commons.error.ErrorType;
import cn.taihealth.ih.commons.vm.PageHelperBean;
import cn.taihealth.ih.domain.cloud.Hospital;
import cn.taihealth.ih.domain.enums.ExpressScene;
import cn.taihealth.ih.domain.hospital.PrescriptionOrder;
import cn.taihealth.ih.repo.hospital.order.PrescriptionOrderRepository;
import cn.taihealth.ih.service.api.CurrentHospital;
import cn.taihealth.ih.service.api.DrugStoreService;
import cn.taihealth.ih.service.api.ExpressSenderInfoService;
import cn.taihealth.ih.service.dto.drugorder.DrugPrescriptionDTO;
import cn.taihealth.ih.service.dto.drugorder.OrderInfoAdminDTO;
import cn.taihealth.ih.service.dto.drugorder.OrderStatusChangeDTO;
import cn.taihealth.ih.service.dto.express.ExpressData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.Optional;

/**
 * @Author: zihao
 */
@RestController
@RequestMapping("/hospital/drugstore")
@Api(tags = "药品服务接口", description = "药品服务接口")
@Slf4j
@AllArgsConstructor
public class HospitalDrugStoreResource {

    private final DrugStoreService drugStoreService;
    private final PrescriptionOrderRepository prescriptionOrderRepository;
    private final ExpressSenderInfoService expressSenderInfoService;

    @ApiOperation(value = "分页查询订单")
    @GetMapping("/orders")
    public PageHelperBean<OrderInfoAdminDTO> getALLPrescriptions(
            @ApiParam("页数，这里用的pageHelper的页码，从1开始") @RequestParam(name = "page", required = false, defaultValue = "1") Integer pageNo,
            @ApiParam("每页条目数") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @ApiParam("query=createdDateGt:1654790931 createdDateLt:1654790931 "
                    + "orderStatusList:CREATE,PAY,DELIVER "
                    + "orderEndType:DRUG_REJECT "
                    + "payStatus:NO_PAY/PAY_SUCCESS/PAY_FAIL"
                    + "orderNo:998（注意此项是LONG类型 并且不是模糊匹配）"
                    + "mobile:18739522122 "
                    + "trackingNo:xxx(物流单号暂未生效)")
            @RequestParam(value = "query", required = false) String query) {
        String hospitalCode = CurrentHospital.getCode();
        return drugStoreService.getDrugOrderPage(pageNo - 1, size, hospitalCode, query);
    }

    @ApiOperation(value = "分页查询订单")
    @GetMapping("/orders/{drugOrderId}")
    public OrderInfoAdminDTO getPrescriptions(@PathVariable long drugOrderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        OrderInfoAdminDTO orderInfo = drugStoreService.getDrugOrderById(drugOrderId);
        Optional<PrescriptionOrder> prescriptionOrder = prescriptionOrderRepository.findById(orderInfo.getOutPrescriptionId());
        if (prescriptionOrder.isEmpty() || !Objects.equals(hospital, prescriptionOrder.get().getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return orderInfo;
    }

    @ApiOperation(value = "查询订单处方详情")
    @GetMapping("/orders/{orderId}/recipe")
    public DrugPrescriptionDTO getALLPrescriptions(@PathVariable("orderId") Long orderId) {
        Hospital hospital = CurrentHospital.getOrThrow();
        DrugPrescriptionDTO prescription = drugStoreService.getDrugRecipeByOrderId(orderId);
        Optional<PrescriptionOrder> prescriptionOrder = prescriptionOrderRepository.findById(prescription.getOutPrescriptionId());
        if (prescriptionOrder.isEmpty() || !Objects.equals(hospital, prescriptionOrder.get().getHospital())) {
            throw ErrorType.FORBIDDEN.toProblem();
        }
        return prescription;
    }

    @ApiOperation(value = "订单状态变更")
    @PostMapping("/order/statusChange")
    public void statusChange(@RequestBody OrderStatusChangeDTO change) {
        Hospital hospital = CurrentHospital.getOrThrow();
        ExpressData expressData = expressSenderInfoService.getExpressSettings(hospital, ExpressScene.ONLINE_MEDICINE_ORDER);
        change.setExpressData(expressData);
        drugStoreService.statusChange(change);
    }

}
