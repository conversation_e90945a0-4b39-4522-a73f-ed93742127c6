<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ih-parent</artifactId>
        <groupId>cn.taihealth.ih</groupId>
        <version>2.0.4873</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ih-rest</artifactId>

    <properties>
        <java.version>11</java.version>
        <swagger2markup.version>1.2.0</swagger2markup.version>
        <asciidoctor.input.directory>${project.basedir}/src/docs/asciidoc</asciidoctor.input.directory>

        <swagger.output.dir>${project.build.directory}/swagger</swagger.output.dir>
        <swagger.snippetOutput.dir>${project.build.directory}/asciidoc/snippets</swagger.snippetOutput.dir>
        <generated.asciidoc.directory>${project.build.directory}/asciidoc/generated</generated.asciidoc.directory>
        <asciidoctor.html.output.directory>${project.build.directory}/asciidoc/html</asciidoctor.html.output.directory>
        <asciidoctor.pdf.output.directory>${project.build.directory}/asciidoc/pdf</asciidoctor.pdf.output.directory>

        <swagger.input>${swagger.output.dir}/swagger.json</swagger.input>
        <springfox.version>2.9.2</springfox.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-csv</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-spring-security</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-repo</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.tuckey</groupId>
            <artifactId>urlrewritefilter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>problem-spring-web</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.gitq.jedi</groupId>-->
<!--            <artifactId>jedi-jinguist</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-parser</artifactId>
            <version>1.0.40</version>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taihealth.ih</groupId>
            <artifactId>ih-ca</artifactId>
            <scope>compile</scope>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.github.kongchen</groupId>
                <artifactId>swagger-maven-plugin</artifactId>
                <version>3.1.7</version>
                <configuration>
                    <apiSources>
                        <apiSource>
                            <springmvc>true</springmvc>
                            <locations>
                                <location>cn.taihealth.ih.rest</location>
                            </locations>
                            <schemes>http,https</schemes>
                            <host>api.taihealth.cn</host>
                            <info>
                                <title>互联网医院API</title>
                                <version>version-${project.version}</version>
                                <description><![CDATA[
                                医院参数</br>
                                d/n端调用时，加在httpheader中，IH_HOSPITAL=code, ITEM=PC</br>
                                u端调用时，加在httpheader中，IH_HOSPITAL=code</br>
                                </br>
                                m端调用时，加在params中，?hospital=code</br>
                                </br>
                                如果m端使用数据模板的医院，不需要传医院参数
                                ]]></description>
                            </info>
                            <outputFormats>json</outputFormats>
                            <swaggerDirectory>${basedir}/target/classes/cn/taihealth/ih/rest/web/</swaggerDirectory>
                        </apiSource>
                    </apiSources>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
