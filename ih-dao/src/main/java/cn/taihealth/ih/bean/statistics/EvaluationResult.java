package cn.taihealth.ih.bean.statistics;

import cn.taihealth.ih.domain.enums.ClinicType;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * 查询收费详情
 * @Author: Moon
 * @Date: 2020/11/20 上午10:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class EvaluationResult implements RowMapper<EvaluationResult>, Serializable {

    private Date dateLabel;
    private ClinicType type;
    private Long deptId;
    private Long doctorId;
    private int orderCount;
    private int evaluationCount;
    private int ratingSum;
    private String ratingAvg = "0.0";

    @Override
    public EvaluationResult mapRow(ResultSet rs, int rowNum) throws SQLException {
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        List<String> columns = Lists.newArrayList();
        for (int i = 1; i <= columnCount; i++) {
            columns.add(metaData.getColumnName(i));
        }
        Date dateLabel = null;
        if (columns.contains("dateLabel")) {
            dateLabel = rs.getDate("dateLabel");
        }
        ClinicType type = null;
        if (columns.contains("order_type")) {
            String code = rs.getString("order_type");
            type = ClinicType.valueOf(code);
        }
        Long deptId = null;
        if (columns.contains("dept_id")) {
            deptId = rs.getLong("dept_id");
        }

        Long doctorId = null;
        if (columns.contains("doctor_id")) {
            doctorId = rs.getLong("doctor_id");
        }
        int orderCount = 0;
        if (columns.contains("orderCount")) {
            orderCount = (int) rs.getLong("orderCount");
        }

        int evaluationCount = 0;
        if (columns.contains("evaluationCount")) {
            evaluationCount = (int) rs.getLong("evaluationCount");
        }

        int ratingSum = 0;
        if (columns.contains("ratingSum")) {
            ratingSum = (int) rs.getLong("ratingSum");
        }

        String ratingAvg = "0.0";
        if (columns.contains("ratingAvg")) {
            ratingAvg = rs.getString("ratingAvg");
        }
        return new EvaluationResult(dateLabel, type, deptId, doctorId, orderCount, evaluationCount, ratingSum,
            ratingAvg);
    }
}
