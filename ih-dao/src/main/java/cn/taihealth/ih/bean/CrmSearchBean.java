package cn.taihealth.ih.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("CrmSearchBean")
public class CrmSearchBean implements Serializable {

    @ApiModelProperty("医院编码")
    private String hospital;

    @ApiModelProperty("线下科室id")
    private Long offlineDeptId;

    @ApiModelProperty("问卷id")
    private Long questionnaireId;

    @ApiModelProperty("问题id")
    private Long questionId;

    @ApiModelProperty("投放开始时间")
    private Date startPutTime;

    @ApiModelProperty("投放结束时间")
    private Date endPutTime;

    @ApiModelProperty("回答开始时间")
    private Date startAnswerTime;

    @ApiModelProperty("回答结束时间")
    private Date endAnswerTime;

    @ApiModelProperty("渠道id")
    private Long channelId;

    @ApiModelProperty("线下就诊医院id")
    private Long visitHospitalId;

}

