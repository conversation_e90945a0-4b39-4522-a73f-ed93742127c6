package cn.taihealth.ih.bean;

import java.io.Serializable;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.jdbc.core.RowMapper;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrmTaskCountBean implements RowMapper<CrmTaskCountBean>, Serializable {

    /**
     * 日历时间
     */
    private Date date;
    /**
     * 任务数量
     */
    private int count;

    @Override
    public CrmTaskCountBean mapRow(ResultSet rs, int rowNum) throws SQLException {
        Date date = rs.getDate("date");
        int count = rs.getInt("count");

        return new CrmTaskCountBean(date, count);
    }
}

