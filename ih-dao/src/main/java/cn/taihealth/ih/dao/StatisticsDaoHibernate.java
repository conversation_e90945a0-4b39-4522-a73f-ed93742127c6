package cn.taihealth.ih.dao;

import cn.taihealth.ih.bean.statistics.BusinessResult;
import cn.taihealth.ih.bean.statistics.EvaluationResult;
import cn.taihealth.ih.bean.statistics.FeeResult;
import cn.taihealth.ih.domain.hospital.Order.OrderStatus;
import com.gitq.jedi.context.AppContext;
import java.util.Date;
import java.util.List;
import javax.sql.DataSource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
public class StatisticsDaoHibernate {

    private final DataSource dataSource = AppContext.getInstance(DataSource.class);

    private JdbcTemplate getJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    /**
     * 查询接诊数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @param deptId 科室
     * @param doctorId 医生
     * @param hospitalId 医院
     * @return 数据集
     */

    public long getOrderCount(Date startTime,
                              Date endTime,
                              List<OrderStatus> orderStatus,
                              Long hospitalId,
                              Long doctorId,
                              Long deptId,
                              String disease) {

        String diseaseArg = disease != null ? " AND o.disease like '%" + disease + "%' " : "";
        String sql = StringUtils.join(
            "SELECT ",
            " COUNT( o.id ) AS count ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            getQueryStrByStatus(orderStatus),
            getQueryStrByArgLong("o.dept_id", deptId),
            getQueryStrByArgLong("o.doctor_id", doctorId),
            diseaseArg);
        return getJdbcTemplate().queryForObject(sql, Long.class, startTime, endTime, hospitalId.toString());
    }

    /**
     * 统计-服务质量-今日数据
     * 条件：实时订单：创建时间在今天范围内
     *      预约订单：预约时间在今天范围内
     *      订单状态在提供范围内
     *      订单所属医院
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @return 数据集
     */

    public EvaluationResult getEvaluationToday(Date startTime,
                                               Date endTime,
                                               List<OrderStatus> orderStatus,
                                               Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " IFNULL(SUM( CASE WHEN w.rating > 0 THEN 1 ELSE 0 END ), 0) AS evaluationCount, ",
            " IFNULL(SUM( w.rating ), 0) AS ratingSum, ",
            " IFNULL(convert(SUM( w.rating )/SUM( CASE WHEN w.rating > 0 THEN 1 ELSE 0 END ),decimal(15,1)), 0) AS ratingAvg ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_order_workers w ON o.id = w.order_id ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            getQueryStrByStatus(orderStatus)
        );
        return getJdbcTemplate().queryForObject(sql, new EvaluationResult(), startTime, endTime, hospitalId.toString());
    }


    /**
     * 服务质量-科室排名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @return 数据集
     */
    public List<EvaluationResult> getEvaluationsGroupByDept(Long hospitalId,
                                                            Date startTime,
                                                            Date endTime,
                                                            List<OrderStatus> orderStatus) {
        String sql = StringUtils.join(
            "SELECT ",
            " o.dept_id, ",
            " IFNULL(SUM( CASE WHEN w.rating > 0 THEN 1 ELSE 0 END ), 0) AS evaluationCount, ",
            " IFNULL(SUM( w.rating ), 0) AS ratingSum, ",
            " IFNULL(convert(SUM( w.rating )/SUM( CASE WHEN w.rating > 0 THEN 1 ELSE 0 END ),decimal(15,1)), 0) AS ratingAvg ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_order_workers w ON o.id = w.order_id ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            " AND o.dept_id is not null ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY o.dept_id ",
            " ORDER BY ratingAvg DESC ");
        return getJdbcTemplate().query(sql, new EvaluationResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 服务质量-医生排名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @return 数据集
     */

    public List<EvaluationResult> getEvaluationsGroupByDoctor(Long hospitalId,
                                                              Date startTime,
                                                              Date endTime,
                                                              List<OrderStatus> orderStatus) {
        String sql = StringUtils.join(
            "SELECT ",
            " o.doctor_id , ",
            " IFNULL(SUM( CASE WHEN w.rating > 0 THEN 1 ELSE 0 END ), 0) AS evaluationCount, ",
            " IFNULL(SUM( w.rating ), 0) AS ratingSum, ",
            " IFNULL(convert(SUM( w.rating )/SUM( CASE WHEN w.rating > 0 THEN 1 ELSE 0 END ),decimal(15,1)), 0) AS ratingAvg ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_order_workers w ON o.id = w.order_id ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            " AND o.doctor_id is not null ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY o.doctor_id ",
            " ORDER BY ratingAvg DESC ");
        return getJdbcTemplate().query(sql, new EvaluationResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 服务质量-类型趋势
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @return 数据集
     */
    public List<EvaluationResult> getEvaluationsGroupByTypeAndDate(Long hospitalId,
                                                                   Date startTime,
                                                                   Date endTime,
                                                                   List<OrderStatus> orderStatus,
                                                                   String dateFormat) {
        String sql = StringUtils.join(
            "SELECT ",
            " DATE_FORMAT(s.start_time, ?) AS dateLabel, ",
            " o.order_type, ",
            " CONVERT (AVG( w.rating ),DECIMAL ( 15, 1 )) AS ratingAvg ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_order_workers w ON o.id = w.order_id ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            " AND w.rating > 0 ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY ",
            " dateLabel, ",
            " o.order_type ",
            " order by dateLabel ");
        return getJdbcTemplate()
            .query(sql, new EvaluationResult(), dateFormat, startTime, endTime, hospitalId.toString());
    }

    /**
     * 服务质量-类型趋势-总均分
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @return 数据集
     */
    public List<EvaluationResult> getEvaluationsTotalGroupByDate(Long hospitalId,
                                                                 Date startTime,
                                                                 Date endTime,
                                                                 List<OrderStatus> orderStatus,
                                                                 String dateFormat) {
        String sql = StringUtils.join(
            "SELECT ",
            " DATE_FORMAT(s.start_time, ?) AS dateLabel, ",
            " IFNULL(CONVERT (AVG( w.rating ),DECIMAL ( 15, 1 )), 0) AS ratingAvg ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_order_workers w ON o.id = w.order_id ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            " AND w.rating > 0 ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY ",
            " dateLabel ",
            " order by dateLabel ");
        return getJdbcTemplate()
            .query(sql, new EvaluationResult(), dateFormat, startTime, endTime, hospitalId.toString());
    }

    /**
     * 业务量-科室排名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @param hospitalId 医院
     * @return 数据集
     */

    public List<BusinessResult> getBusinessGroupByDept(Date startTime,
                                                       Date endTime,
                                                       List<OrderStatus> orderStatus,
                                                       Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " o.dept_id, ",
            " COUNT( o.id ) AS orderCount, ",
            " IFNULL(SUM( CASE WHEN o.order_type = 'OUT' THEN 1 ELSE 0 END ), 0) AS outPatient, ",
            " IFNULL(SUM( CASE WHEN o.order_type = 'CONSULT' THEN 1 ELSE 0 END ), 0) AS consult, ",
            " IFNULL(SUM( CASE WHEN o.order_type = 'EMERGENCY' THEN 1 ELSE 0 END ), 0) AS emergency, ",
            " IFNULL(SUM( CASE WHEN o.order_type = 'EMERGENCY' THEN 1 ELSE 0 END ), 0) AS triage ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            " AND o.dept_id is not null ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY o.dept_id ",
            " ORDER BY orderCount DESC");
        return getJdbcTemplate().query(sql, new BusinessResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 业务量-服务类型分布
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @param hospitalId 医院
     * @return 数据集
     */

    public BusinessResult getBusiness(Date startTime,
                                      Date endTime,
                                      List<OrderStatus> orderStatus,
                                      Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " IFNULL(SUM( CASE WHEN o.order_type = 'OUT' THEN 1 ELSE 0 END ), 0) AS outPatient, ",
            " IFNULL(SUM( CASE WHEN o.order_type = 'CONSULT' THEN 1 ELSE 0 END ), 0) AS consult, ",
            " IFNULL(SUM( CASE WHEN o.order_type = 'EMERGENCY' THEN 1 ELSE 0 END ), 0) AS emergency ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            " AND o.dept_id is not null ",
            getQueryStrByStatus(orderStatus)
        );
        return getJdbcTemplate().queryForObject(sql, new BusinessResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 业务量-药品排名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 数据集
     */
    public List<BusinessResult> getBusinessGroupByDrug(Date startTime,
                                                       Date endTime,
                                                       Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " ps.drug_name AS drugName, ",
            " ps.unit AS drugUnit, ",
            " COUNT(p.id) AS prescriptionCount, ",
            " IFNULL(SUM(ps.quantity), 0) AS drugCount ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_prescription_orders p ON p.order_id = o.id ",
            " LEFT JOIN ih_prescriptions ps ON ps.prescription_order_id = p.id ",
            " Where  ",
            " p.send_user_date >= ? AND p.send_user_date < ? ",
            " AND o.hospital_id = ? ",
            " GROUP BY ps.drug_name, ps.unit ",
            " ORDER BY drugCount DESC ");
        return getJdbcTemplate().query(sql, new BusinessResult(), startTime, endTime, hospitalId.toString());

    }

    /**
     * 业务量-医生排名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 数据集
     */

    public List<BusinessResult> getBusinessGroupByDoctor(Date startTime,
                                                         Date endTime,
                                                         Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " o.doctor_id, ",
            " IFNULL(sum(CASE WHEN p.send_user_date >= ? AND p.send_user_date < ? THEN 1 ELSE 0 END ), 0) AS prescriptionCount, ",
            " IFNULL(sum(CASE WHEN c.pay_time >= ? AND c.pay_time < ? THEN 1 ELSE 0 END ), 0) AS checksCount ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_prescription_orders p ON p.order_id = o.id ",
            " LEFT JOIN ih_check_groups c ON c.order_id = o.id ",
            " Where  ",
            " (p.send_user_date >= ? AND p.send_user_date < ? ) ",
            " OR (c.pay_time >= ? AND c.pay_time < ?) ",
            " AND o.hospital_id = ? ",
            " AND o.doctor_id is not null ",
            " GROUP BY o.doctor_id ");
        return getJdbcTemplate()
            .query(sql, new BusinessResult(), startTime, endTime, startTime, endTime, startTime, endTime, startTime,
                endTime, hospitalId.toString());
    }

    /**
     * 业务量-疾病排名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 数据集
     */
    public List<BusinessResult> getBusinessGroupByDisease(Date startTime,
                                                          Date endTime,
                                                          Long hospitalId) {
        String sql = StringUtils.join("SELECT ",
            " o.disease AS disease, ",
            " IFNULL(sum(CASE WHEN p.send_user_date >= ? AND p.send_user_date < ? THEN 1 ELSE 0 END ), 0) AS prescriptionCount ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_prescription_orders p ON p.order_id = o.id ",
            " Where  ",
            " (p.send_user_date >= ? AND p.send_user_date < ?) ",
            " AND o.hospital_id = ? ",
            " AND o.disease is not null ",
            " GROUP BY o.disease ");
        return getJdbcTemplate()
            .query(sql, new BusinessResult(), startTime, endTime, startTime, endTime, hospitalId.toString());
    }

    /**
     * 业务量-类型时间趋势
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @return 数据集
     */
    public List<BusinessResult> getBusinessGroupByTypeAndDate(Long hospitalId,
                                                              Date startTime,
                                                              Date endTime,
                                                              List<OrderStatus> orderStatus,
                                                              String dateFormat) {
        String sql = StringUtils.join("SELECT ",
            " DATE_FORMAT(s.start_time, ?) AS dateLabel, ",
            " o.order_type, ",
            " count(o.id) AS orderCount ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " and (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY ",
            " dateLabel, ",
            " o.order_type ");
        return getJdbcTemplate()
            .query(sql, new BusinessResult(), dateFormat, startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-就诊收入
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @param hospitalId 医院
     * @return 数据集
     */
    public Long getClinicFee(Date startTime,
                             Date endTime,
                             List<OrderStatus> orderStatus,
                             Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " IFNULL(SUM(CASE WHEN o.registration_fee >= 0 THEN o.registration_fee ELSE 0 END ), 0 ) AS clinicFee ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            getQueryStrByStatus(orderStatus));
        return getJdbcTemplate().queryForObject(sql, Long.class, startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-药品收入
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 收入
     */

    public Long getDrugFee(Date startTime, Date endTime,
                           Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " IFNULL(SUM(ps.quantity * ps.price), 0) AS drugFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_prescription_orders p ON p.order_id = o.id ",
            " LEFT JOIN ih_prescriptions ps ON ps.prescription_order_id = p.id ",
            " Where  ",
            " p.send_user_date >= ? AND p.send_user_date < ? ",
            " AND o.hospital_id = ? ");
        return getJdbcTemplate().queryForObject(sql, Long.class, startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-检查收入
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 收入
     */

    public Long getChecksFee(Date startTime, Date endTime,
                             Long hospitalId) {
        String sql = StringUtils.join("SELECT ",
            " IFNULL(sum( cs.price ), 0) as checksFee",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_check_groups c ON c.order_id = o.id ",
            " LEFT JOIN ih_checks cs ON cs.checks_group_id = c.id ",
            " Where  ",
            " c.pay_time >= ? AND c.pay_time < ? ",
            " AND o.hospital_id = ? ");
        return getJdbcTemplate().queryForObject(sql, Long.class, startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-就诊收入排行-医生
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @param hospitalId 医院
     * @return 数据集
     */
    public List<FeeResult> getClinicFeeDoctor(Date startTime,
                                              Date endTime,
                                              List<OrderStatus> orderStatus,
                                              Long hospitalId) {
        String sql = StringUtils.join("SELECT ",
            " o.doctor_id, ",
            " IFNULL(sum(CASE WHEN o.registration_fee >= 0 THEN o.registration_fee ELSE 0 END ), 0) AS clinicFee ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND o.doctor_id is not null ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY o.doctor_id ",
            " ORDER BY clinicFee DESC ");
        return getJdbcTemplate().query(sql, new FeeResult(), startTime, endTime, hospitalId.toString());

    }

    /**
     * 收费-就诊收入排行-科室
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @param hospitalId 医院
     * @return 数据集
     */

    public List<FeeResult> getClinicFeeDept(Date startTime,
                                            Date endTime,
                                            List<OrderStatus> orderStatus,
                                            Long hospitalId) {
        String sql = StringUtils.join("SELECT ",
            " o.dept_id, ",
            " IFNULL(SUM(CASE WHEN o.registration_fee >= 0 THEN o.registration_fee ELSE 0 END ), 0) AS clinicFee ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND o.dept_id is not null ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY o.dept_id ",
            " ORDER BY clinicFee DESC ");
        return getJdbcTemplate().query(sql, new FeeResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-药品收入排行-医生
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 收入
     */
    public List<FeeResult> getDrugFeeDoctor(Date startTime,
                                            Date endTime,
                                            Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " o.doctor_id, ",
            " IFNULL(SUM(ps.quantity * ps.price), 0) AS drugFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_prescription_orders p ON p.order_id = o.id ",
            " LEFT JOIN ih_prescriptions ps ON ps.prescription_order_id = p.id ",
            " Where  ",
            " p.send_user_date >= ? AND p.send_user_date < ? ",
            " AND o.hospital_id = ? ",
            " AND o.doctor_id is not null ",
            " GROUP BY o.doctor_id ",
            " ORDER BY drugFee DESC ");
        return getJdbcTemplate().query(sql, new FeeResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-药品收入排行-科室
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 收入
     */
    public List<FeeResult> getDrugFeeDept(Date startTime,
                                          Date endTime,
                                          Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " o.dept_id, ",
            " IFNULL(SUM(ps.quantity * ps.price), 0) AS drugFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_prescription_orders p ON p.order_id = o.id ",
            " LEFT JOIN ih_prescriptions ps ON ps.prescription_order_id = p.id ",
            " Where  ",
            " p.send_user_date >= ? AND p.send_user_date < ? ",
            " AND o.hospital_id = ? ",
            " AND o.doctor_id is not null ",
            " GROUP BY o.dept_id ",
            " ORDER BY drugFee DESC ");
        return getJdbcTemplate().query(sql, new FeeResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-检查收入排行-医生
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 收入
     */
    public List<FeeResult> getChecksFeeDoctor(Date startTime,
                                              Date endTime,
                                              Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " o.doctor_id, ",
            " IFNULL(SUM( cs.price ),0) AS checksFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_check_groups c ON c.order_id = o.id ",
            " LEFT JOIN ih_checks cs ON cs.checks_group_id = c.id ",
            " Where  ",
            " c.pay_time >= ? AND c.pay_time < ? ",
            " AND o.hospital_id = ? ",
            " AND o.doctor_id is not null ",
            " GROUP BY o.doctor_id ",
            " ORDER BY checksFee DESC");
        return getJdbcTemplate().query(sql, new FeeResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-检查收入排行-科室
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 收入
     */
    public List<FeeResult> getChecksFeeDept(Date startTime,
                                            Date endTime,
                                            Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " o.dept_id, ",
            " IFNULL(SUM( cs.price ), 0) AS checksFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_check_groups c ON c.order_id = o.id ",
            " LEFT JOIN ih_checks cs ON cs.checks_group_id = c.id ",
            " Where  ",
            " c.pay_time >= ? AND c.pay_time < ? ",
            " AND o.hospital_id = ? ",
            " AND o.dept_id is not null ",
            " GROUP BY o.dept_id ",
            " ORDER BY checksFee DESC ");
        return getJdbcTemplate().query(sql, new FeeResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-用药排名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 数据集
     */
    public List<FeeResult> getFeeGroupByDrug(Date startTime,
                                             Date endTime,
                                             Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " ps.drug_name AS drugName, ",
            " COUNT(p.id) AS prescriptionCount, ",
            " IFNULL(SUM(ps.quantity * ps.price), 0) AS drugFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_prescription_orders p ON p.order_id = o.id ",
            " LEFT JOIN ih_prescriptions ps ON ps.prescription_order_id = p.id ",
            " Where  ",
            " p.send_user_date >= ? AND p.send_user_date < ? ",
            " AND o.hospital_id = ? ",
            " GROUP BY drugName ",
            " ORDER BY drugFee DESC ");
        return getJdbcTemplate().query(sql, new FeeResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-检查排名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 数据集
     */

    public List<FeeResult> getFeeGroupByChecks(Date startTime,
                                               Date endTime,
                                               Long hospitalId) {
        String sql = StringUtils.join(
            "SELECT ",
            " i.`name` AS checksName, ",
            " COUNT(cs.id) AS checksTimes, ",
            " IFNULL(SUM(cs.price),0) AS checksFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_check_groups c ON c.order_id = o.id ",
            " LEFT JOIN ih_checks cs ON cs.checks_group_id = c.id ",
            " INNER JOIN ih_exam_items i ON i.id = cs.exam_item_id ",
            " Where  ",
            " c.pay_time >= ? AND c.pay_time < ? ",
            " AND o.hospital_id = ? ",
            " GROUP BY checksName ",
            " ORDER BY checksFee DESC ");
        return getJdbcTemplate().query(sql, new FeeResult(), startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-就诊收入-时间趋势
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @param hospitalId 医院
     * @return 数据集
     */
    public List<FeeResult> getClinicFeeDate(Date startTime,
                                            Date endTime,
                                            List<OrderStatus> orderStatus,
                                            Long hospitalId,
                                            String dateFormat) {
        String sql = StringUtils.join("SELECT ",
            " DATE_FORMAT(s.start_time, ?) AS dateLabel, ",
            " IFNULL(SUM(CASE WHEN o.registration_fee >= 0 THEN o.registration_fee ELSE 0 END ), 0) AS clinicFee ",
            " FROM ",
            " ih_orders o ",
            " INNER JOIN ih_schedules s ON s.id = o.service_id ",
            " Where ",
            " s.start_time >= ? AND s.start_time < ? ",
            " AND o.online_type = 'ONLINE' ",
            " AND o.hospital_id = ? ",
            " AND (o.order_type='OUT' or o.order_type='CONSULT' or o.order_type='EMERGENCY') ",
            getQueryStrByStatus(orderStatus),
            " GROUP BY dateLabel ");

        return getJdbcTemplate()
            .query(sql, new FeeResult(), dateFormat, startTime, endTime, hospitalId.toString());
    }

    /**
     * 收费-药品收入-时间趋势
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 收入
     */
    public List<FeeResult> getDrugFeeDate(Date startTime,
                                          Date endTime,
                                          Long hospitalId,
                                          String dateFormat) {
        String sql = StringUtils.join("SELECT ",
            " DATE_FORMAT(p.send_user_date, ?) AS dateLabel, ",
            " IFNULL(SUM(ps.quantity * ps.price), 0) AS drugFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_prescription_orders p ON p.order_id = o.id ",
            " LEFT JOIN ih_prescriptions ps ON ps.prescription_order_id = p.id ",
            " Where  ",
            " p.send_user_date >= ? AND p.send_user_date < ? ",
            " AND o.hospital_id = ? ",
            " GROUP BY dateLabel ");

        return getJdbcTemplate()
            .query(sql, new FeeResult(), dateFormat, startTime, endTime, hospitalId.toString());
    }


    /**
     * 收费-检查收入-时间趋势
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalId 医院
     * @return 收入
     */
    public List<FeeResult> getChecksFeeDate(Date startTime,
                                            Date endTime,
                                            Long hospitalId,
                                            String dateFormat) {
        String sql = StringUtils.join("SELECT ",
            " DATE_FORMAT(c.pay_time, ?) AS dateLabel, ",
            " IFNULL(SUM( cs.price ), 0) AS checksFee ",
            " FROM ",
            " ih_orders o ",
            " LEFT JOIN ih_check_groups c ON c.order_id = o.id ",
            " LEFT JOIN ih_checks cs ON cs.checks_group_id = c.id ",
            " Where  ",
            " c.pay_time >= ? AND c.pay_time < ? ",
            " AND o.hospital_id = ? ",
            "GROUP BY dateLabel ");

        return getJdbcTemplate().query(sql, new FeeResult(), dateFormat, startTime, endTime, hospitalId.toString());
    }

    private String getQueryStrByArgLong(String columnStr, Long arg) {
        return arg != null ? " AND " + columnStr + " = " + arg.toString() + " " : "";
    }

    private StringBuilder getQueryStrByStatus(List<OrderStatus> orderStatus) {
        StringBuilder statusArgs = new StringBuilder();
        if (CollectionUtils.isNotEmpty(orderStatus)) {
            statusArgs = new StringBuilder(" AND o.status in ( ");
            for (OrderStatus status : orderStatus) {
                statusArgs.append("'");
                statusArgs.append(status.toString());
                statusArgs.append("'");
                statusArgs.append(",");
            }
            statusArgs.deleteCharAt(statusArgs.length() - 1);
            statusArgs.append(" ) ");
        }
        return statusArgs;
    }
}
