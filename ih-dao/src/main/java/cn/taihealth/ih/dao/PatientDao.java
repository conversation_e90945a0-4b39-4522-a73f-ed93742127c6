package cn.taihealth.ih.dao;

import cn.taihealth.ih.bean.MedicalCaseHistoryBean;
import cn.taihealth.ih.bean.PatientSignLogsBean;
import cn.taihealth.ih.rowmapper.MedicalCaseHistoryRowMapper;
import cn.taihealth.ih.rowmapper.PatientSignLogsRowMapper;
import com.beust.jcommander.internal.Lists;
import com.gitq.jedi.context.AppContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.Date;
import java.util.List;

public class PatientDao {

    private final static MedicalCaseHistoryRowMapper MEDICAL_CASE_HISTORY_ROW_MAPPER = new MedicalCaseHistoryRowMapper();
    private final static PatientSignLogsRowMapper PATIENT_SIGN_LOGS_ROW_MAPPER = new PatientSignLogsRowMapper();

    /**
     * 查询就诊人历史就诊记录
     * @param patientId
     * @param limit
     * @param offset
     * @return
     */
    public static List<MedicalCaseHistoryBean> getMedicalCaseHistory(long patientId, int limit, long offset) {
        String sql = StringUtils.join(" SELECT dd.id AS id, dd.visitType AS type, ",
                " dd.time AS time FROM ( SELECT cc.id, ",
                " 'visit' AS visitType, ",
                " cc.visit_date AS time ",
                " FROM ih_patient_clinics cc ",
                " LEFT JOIN ih_order_history_record ohr ON cc.order_history_record_id = ohr.id ",
                " WHERE cc.type != 'CONSULT' AND cc.patient_id = " + patientId,
                " UNION SELECT omr.id, ",
                " 'prescription' AS visitType, omr.prescription_time AS time ",
                " FROM ",
                " ih_prescription_records pr ",
                " LEFT JOIN ih_offline_medical_records omr ON pr.offlinemedical_records_id = omr.id ",
                " WHERE pr.type = 'OFFLINE' AND pr.patient_id = " + patientId,
                " ) dd ORDER BY time DESC limit " + offset + ", " + limit);
        DataSource dataSource = AppContext.getInstance(DataSource.class);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return jdbcTemplate.query(sql, MEDICAL_CASE_HISTORY_ROW_MAPPER);
    }

    /**
     * 查询就诊人历史就诊数量
     * @param patientId
     * @return
     */
    public static Long getMedicalCaseHistoryCount(long patientId) {
        String sql = StringUtils.join(" SELECT count(*) AS count ",
                " FROM ( SELECT cc.id, ",
                " 'visit' AS visitType, ",
                " cc.visit_date AS time ",
                " FROM ih_patient_clinics cc ",
                " LEFT JOIN ih_order_history_record ohr ON cc.order_history_record_id = ohr.id ",
                " WHERE cc.type != 'CONSULT' AND cc.patient_id = " + patientId,
                " UNION SELECT omr.id, ",
                " 'prescription' AS visitType, omr.prescription_time AS time ",
                " FROM ",
                " ih_prescription_records pr ",
                " LEFT JOIN ih_offline_medical_records omr ON pr.offlinemedical_records_id = omr.id ",
                " WHERE pr.type = 'OFFLINE' AND pr.patient_id = " + patientId,
                " ) dd");
        DataSource dataSource = AppContext.getInstance(DataSource.class);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        List<Long> list = jdbcTemplate.queryForList(sql, Long.class);
        return list.stream().findFirst().orElse(0L);
    }

    /**
     * 查询就诊人历史体征数据
     * @param patientId
     * @param startDate
     * @param endDate
     * @param signs
     * @param interval
     * @return
     */
    public static List<PatientSignLogsBean> getPatientSignsStats(long patientId, Date startDate,
                                                                 Date endDate, List<Long> signs, String interval) {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> params = Lists.newArrayList();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(" l.sign_id AS sign_id, ");
        if ("group".equals(interval)) {
            sqlBuilder.append(" DATE_FORMAT(l.record_date, '%Y-%m-%d') date, ");
        }
        sqlBuilder.append(" AVG(l.num_value) AS value ");
        sqlBuilder.append(" FROM ih_user_sign_logs l WHERE ");
        sqlBuilder.append(" l.patient_id = ? ");
        params.add(patientId);
        if (CollectionUtils.isNotEmpty(signs)) {
            sqlBuilder.append(" AND l.sign_id IN ");
            sqlBuilder.append("(");
            sqlBuilder.append(StringUtils.join(signs, ","));
            sqlBuilder.append(")");
        }
        sqlBuilder.append(" AND record_date >= ? AND record_date < ? ");
        params.add(startDate);
        params.add(endDate);
        sqlBuilder.append(" GROUP BY l.sign_id ");
        if ("group".equals(interval)) {
            sqlBuilder.append(" , date ");
        }

        DataSource dataSource = AppContext.getInstance(DataSource.class);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return jdbcTemplate.query(sqlBuilder.toString(), PATIENT_SIGN_LOGS_ROW_MAPPER, params.toArray());
    }

    /**
     * 查询就诊人全部疾病
     * @param patientId
     * @return
     */
    public static List<String> findAllPatientDiseaseByPatient(Long patientId) {
        List<Object> params = Lists.newArrayList();
        String sql = PatientProvider.findAllPatientDiseaseByPatient(patientId, params);

        DataSource dataSource = AppContext.getInstance(DataSource.class);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return jdbcTemplate.queryForList(sql, String.class, params.toArray());
    }

}
