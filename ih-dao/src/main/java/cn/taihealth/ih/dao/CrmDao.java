package cn.taihealth.ih.dao;

import cn.taihealth.ih.bean.CrmSearchBean;
import cn.taihealth.ih.bean.statistics.CrmAnswerCountResult;
import com.beust.jcommander.internal.Lists;
import com.gitq.jedi.context.AppContext;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

public class CrmDao {

    public static List<CrmAnswerCountResult> getQuestionnaireAnswersList(CrmSearchBean searchBean, Pageable pageable) {
        List<Object> params = Lists.newArrayList();
        String sql = CrmProvider.getQuestionnaireAnswers(searchBean, params);
        sql += (" limit " + pageable.getOffset() + ", " + pageable.getPageSize());
        DataSource dataSource = AppContext.getInstance(DataSource.class);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return jdbcTemplate.query(sql, new CrmAnswerCountResult(), params.toArray());
    }

    public static Long getQuestionnaireAnswersCount(CrmSearchBean searchBean) {
        List<Object> params = Lists.newArrayList();
        String sql = CrmProvider.getQuestionnaireAnswers(searchBean, params);
        sql = "SELECT count(*) AS count FROM (" + sql + ") table" + System.currentTimeMillis();
        DataSource dataSource = AppContext.getInstance(DataSource.class);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return jdbcTemplate.queryForList(sql, Long.class, params.toArray())
                .stream().findFirst().orElse(0L);
    }

    public static List<Map<String, Object>> getHospitalQuestionsList(CrmSearchBean searchBean, Pageable pageable) {
        List<Object> params = Lists.newArrayList();
        String sql = CrmProvider.getHospitalQuestions(searchBean, params);
        sql += (" limit " + pageable.getOffset() + ", " + pageable.getPageSize());
        DataSource dataSource = AppContext.getInstance(DataSource.class);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return jdbcTemplate.queryForList(sql, params.toArray());
    }

    public static Long getHospitalQuestionsCount(CrmSearchBean searchBean) {
        List<Object> params = Lists.newArrayList();
        String sql = CrmProvider.getHospitalQuestions(searchBean, params);
        sql = "SELECT count(*) AS count FROM (" + sql + ") table" + System.currentTimeMillis();
        DataSource dataSource = AppContext.getInstance(DataSource.class);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return jdbcTemplate.queryForList(sql, Long.class, params.toArray())
                .stream().findFirst().orElse(0L);
    }
}
